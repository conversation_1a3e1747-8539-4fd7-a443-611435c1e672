<?php

namespace plugins\shop\controller;

use core\lib\redisCached;
use Exception;
use plugins\shop\form\messagesFrom;
use plugins\shop\models\configModel;
use plugins\shop\models\creditCardModel;
use plugins\shop\models\creditCardVirtualModel;
use plugins\shop\models\creditCardFeeModel;
use plugins\shop\models\userModel;
use Rap2hpoutre\FastExcel\FastExcel;
use core\lib\db\dbShopMysql;
use Throwable;

class creditCardController extends baseController
{
    // 根据类型获取对应的Model
    protected function getModelByType($type = 1)
    {
        return $type == 2 ? new creditCardVirtualModel() : new creditCardModel();
    }

    // 列表
    public function getList()
    {
        $type = isset($_GET['type']) ? (int)$_GET['type'] : 1;
        $model = $this->getModelByType($type);

        if ($type == 1) {
            $paras_list = [
                'card_number', 'bank', 'storage_location', 'usage_group', 'fee_group', 'shop_number',
                'cardholder_name', 'credit_card_status',
                'card_type', 'activation_status', 'shop_status', 'dep_id',
                'job_status', 'is_use_self', 'legal_person_shop', 'billing_date',
                'is_annual_fee', 'validity_date', 'application_date',
                'update_date', 'bind_shop_date', 'annual_fee_deduction_date',
                'page', 'page_size'
            ];
        } else {
            $paras_list = [
                'card_number', 'activation_platform', 'main_account', 'shop_number', 'service_provider',
                'credit_card_status', 'use_platform', 'currency', 'shop_status',
                'validity_date', 'activation_date', 'use_status',
                'bind_shop_date', 'update_date',
                'page', 'page_size'
            ];
        }

        $param = array_intersect_key($_GET, array_flip($paras_list));
        $param['page'] = $param['page'] ?? 1;
        $param['page_size'] = $param['page_size'] ?? 10;

        $list = $model->getList($param);

        // 查询所有的未完成费用申请
        $feeList = [];
        $ids = array_column($list['list'], 'id');
        if (!empty($ids)) {
            $ids = array_unique(array_filter($ids));
            $feeModel = new creditCardFeeModel();
            $feeList = $feeModel->getList(['credit_card_id' => $ids, 'status' => 0]);
            $feeList = array_column($feeList, null, 'credit_card_id');
        }

        foreach ($list['list'] as &$item) {
            empty($item['fee']) && $item['fee'] = $feeList[$item['id']] ?? null;
        }
        returnSuccess($list);
    }

    // 新增
    public static function add()
    {
        $type = isset($_POST['type']) ? (int)$_POST['type'] : 1;
        $model = (new self())->getModelByType($type);

        $param = $_POST;
        $id = $_POST['id'] ?? 0;

        try {
            $model->dataValidCheck($param);
        } catch (Exception $e) {
            returnError($e->getMessage());
        }

        // 检查卡号唯一性
        $detail = $model->getByCardNumber($param['card_number'], $id);
        if ($detail) {
            returnError('信用卡号已存在');
        }

        if ($id) {
            // 验证数据正确性
            $detail = $model->getById($id);
            if (!$detail) {
                returnError('数据不存在');
            }
            $model->edit($param, $id, $detail);
            returnSuccess([], '编辑成功');
        } else {
            $model->add($param);
            returnSuccess([], '添加成功');
        }
    }

    // 详情
    public static function detail()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $type = isset($_GET['type']) ? (int)$_GET['type'] : 1;
        $model = (new self())->getModelByType($type);

        $detail = $model->getById($id);
        $detail = $model->formatItem($detail);
        if (!$detail) {
            returnError('数据不存在');
        }

        returnSuccess($detail);
    }

    // 获取日志
    public static function getLog()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }
        $type = isset($_GET['type']) ? (int)$_GET['type'] : 1;
        $model = (new self())->getModelByType($type);

        $db = dbShopMysql::getInstance();
        $list = $db->table('operation_log')
            ->where('table_name = :table_name and table_id = :table_id',
                ['table_name' => 'credit_card', 'table_id' => $id])
            ->order('id desc')
            ->list();

        $maps = $model::getMaps();
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        foreach ($list as &$item) {
            $item['operator_name'] = $users[$item['operator']] ?? '';
            $attach = json_decode($item['attach'], true);
            $item['before'] = $attach['before'] ?? null;
            $item['before'] = $model->formatItem($item['before'], $maps);
            $item['after'] = $attach['after'] ?? null;
            $item['after'] = $model->formatItem($item['after'], $maps);
            $item['notice_user'] = array_map(function ($u) use ($users) {
                return $users[$u] ?? '';
            }, $attach['notice_user'] ?? []);
            unset($item['attach']);
        }
        returnSuccess($list);
    }

    // 获取费用申请记录
    public static function getFeeList()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $feeModel = new creditCardFeeModel();
        $list = $feeModel->getList(['credit_card_id' => [$id]]);
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        foreach ($list as &$item) {
            $item['pay_file'] = json_decode($item['pay_file'], true);
            $item['operator_name'] = $users[$item['operator']] ?? '';
        }
        returnSuccess($list);
    }

    // 批量导入
    public function import()
    {
        $type = isset($_POST['type']) ? (int)$_POST['type'] : 1;
        $model = (new self())->getModelByType($type);
        $paras_list = $model::$paras_list;

        if (empty($_POST['excel_src'])) {
            returnError('表格链接不能为空');
        }

        $excel_url = SELF_FK . $_POST['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格文件不存在');
        }

        $data = (new FastExcel())->import($excel_url, function ($row) {
            // 将每一项 trim 去空格，再判断整行是否为空
            $cleaned = collect($row)->map(function ($value) {
                if (is_string($value)) {
                    return trim((string)$value);
                }
                return $value;
            });
            // 全部为空则跳过
            if ($cleaned->filter()->isEmpty()) {
                return null;
            }
            return $cleaned->toArray(); // 返回处理后的数据
        });
        $first = $data[0];

        $receive_account = redisCached::getReceiveAccountAll();
        $receive_account = array_column($receive_account, null, 'account_name');
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_id', 'user_name');
        $deps = redisCached::getDepartment();
        $deps = array_column($deps, 'id', 'name');


        $import_data = [];
        $error_data = [];
        if ($type == 1) {
            if (empty($first['卡号']) || empty($first['开卡银行']) || empty($first['有效开始日期']) ||
                empty($first['有效结束日期']) || empty($first['类型']) || empty($first['激活情况']) ||
                !isset($first['安全码']) || empty($first['还款日/每月']) || empty($first['信用卡登记负责人']) ||
                empty($first['开户人姓名']) || !isset($first['城市']) || !isset($first['部门']) ||
                !isset($first['工作情况']) || empty($first['提供日期']) || empty($first['存储位置']) ||
                !isset($first['对接群']) || empty($first['是否自用']) || empty($first['年费金额']) ||
//                empty($first['币种']) ||
                empty($first['扣年费日期']) || !isset($first['年费达标标准']) ||
                empty($first['使用费标准']) || empty($first['持卡人收款账户']) || !isset($first['费用对接群']) ||
                !isset($first['备注'])) {
                returnError('表头错误');
            }

            // 批量导入不校验的字段
            unset($paras_list['credit_card_status']);

            foreach ($data as $row) {
                $error_msg = [];
                empty($row['卡号']) && $error_msg[] = '卡号不能为空';
                // 唯一性检验
                $detail = $model->getByCardNumber($row['卡号']);
                if ($detail) {
                    $error_msg[] = '信用卡号已存在';
                }
                empty($row['开卡银行']) && $error_msg[] = '开卡银行不能为空';
                empty($row['有效开始日期']) && $error_msg[] = '有效开始日期不能为空';
                $validity_period_start = null;
                try {
                    $validity_period_start = $row['有效开始日期']->format('Y-m-d');
                    if (empty($row['有效开始日期']) || strtotime($validity_period_start) === false) {
                        $error_msg[] = '有效开始日期格式错误';
                    }
                } catch (Throwable $e) {
                    $error_msg[] = '有效开始日期格式错误';
                }
                empty($row['有效结束日期']) && $error_msg[] = '有效结束日期不能为空';
                $validity_period_end = null;
                try {
                    $validity_period_end = $row['有效结束日期']->format('Y-m-d');
                    if (empty($row['有效结束日期']) || strtotime($validity_period_end) === false) {
                        $error_msg[] = '有效结束日期格式错误';
                    }
                } catch (Throwable $e) {
                    $error_msg[] = '有效结束日期格式错误';
                }
                empty($row['类型']) && $error_msg[] = '类型不能为空';
                empty($row['激活情况']) && $error_msg[] = '激活情况不能为空';
                empty($row['还款日/每月']) && $error_msg[] = '还款日/每月不能为空';
                empty($row['信用卡登记负责人']) && $error_msg[] = '信用卡登记负责人不能为空';
                if (!isset($users[$row['信用卡登记负责人']])) {
                    $error_msg[] = '信用卡登记负责人不存在';
                } else {
                    $record_keeper = $users[$row['信用卡登记负责人']];
                }
                empty($row['开户人姓名']) && $error_msg[] = '开户人姓名不能为空';
                $dep_id = null;
                if (!empty($row['部门'])) {
                    if (!isset($deps[$row['部门']])) {
                        $error_msg[] = '部门不存在';
                    } else {
                        $dep_id = $deps[$row['部门']];
                    }
                }

                empty($row['提供日期']) && $error_msg[] = '提供日期不能为空';
                $application_date = null;
                try {
                    $application_date = $row['提供日期']->format('Y-m-d');
                    if (empty($row['提供日期']) || strtotime($application_date) === false) {
                        $error_msg[] = '提供日期格式错误';
                    }
                } catch (Throwable $e) {
                    $error_msg[] = '提供日期格式错误';
                }
                empty($row['存储位置']) && $error_msg[] = '存储位置不能为空';
                empty($row['是否自用']) && $error_msg[] = '是否自用不能为空';
                empty($row['年费金额']) && $error_msg[] = '年费金额不能为空';
//                empty($row['币种']) && $error_msg[] = '币种不能为空';
                empty($row['扣年费日期']) && $error_msg[] = '扣年费日期不能为空';
                empty($row['使用费标准']) && $error_msg[] = '使用费标准不能为空';
                empty($row['持卡人收款账户']) && $error_msg[] = '持卡人收款账户不能为空';


                $item_data = [
                    "card_number"                => $row["卡号"],
                    "activation_bank"            => $row["开卡银行"],
                    "validity_period"            => ($validity_period_start && $validity_period_end) ? [$validity_period_start, $validity_period_end] : null,
                    "card_type"                  => $row["类型"],
                    "activation_status"          => $row["激活情况"],
                    "security_code"              => $row["安全码"] ?? '',
                    "billing_date"               => $row["还款日/每月"],
                    "record_keeper"              => $record_keeper ?? null,
                    "cardholder_name"            => $row["开户人姓名"],
                    "city"                       => $row["城市"] ?? '',
                    "dep_id"                     => $dep_id ?? null,
                    "job_status"                 => $row["工作情况"] ?? '',
                    "application_date"           => $application_date ?? null,
                    "storage_location"           => $row["存储位置"],
                    "usage_group"                => $row["对接群"],
                    "is_use_self"                => $row["是否自用"],
                    "annual_fee_amount"          => $row["年费金额"],
                    "annual_fee_deduction_date"  => $row["扣年费日期"],
                    "annual_fee_standard"        => $row["年费达标标准"] ?? '',
                    "usage_fee_standard"         => $row["使用费标准"] ?? '',
                    "cardholder_receive_account" => $row["持卡人收款账户"],
                    "fee_group"                  => $row["费用对接群"] ?? '',
                    "remark"                     => $row["备注"],
                ];
                $item_data['credit_card_status'] = $model::STATUS_USING;
                $check_row = $model->dataValidCheck($item_data, $paras_list, false);
                if (!empty($check_row['error'])) {
                    $error_msg = array_merge($error_msg, $check_row['error']);
                }

                if (!empty($error_msg)) {
                    $row['失败原因'] = implode(';', $error_msg);
                    $error_data[] = $row;
                    continue;
                }

                $import_data[] = $item_data;
                $id = $model->add($item_data, '导入');
            }
        } else {
            if (empty($first['开卡日期']) || empty($first['开卡平台']) || empty($first['主账户']) ||
                empty($first['使用平台']) || empty($first['信用卡号']) || empty($first['有效开始日期']) ||
                empty($first['有效结束日期']) || empty($first['安全码（CVC）']) || empty($first['币种']) ||
                empty($first['对接服务商']) || !isset($first['备注'])) {
                returnError('表头错误');
            }

            // 批量导入不校验的字段
            unset($paras_list['use_status']);

            foreach ($data as $row) {
                $error_msg = [];
                empty($row['开卡日期']) && $error_msg[] = '开卡日期不能为空';
                try {
                    $activation_date = $row['开卡日期']->format('Y-m-d');
                    if (empty($row['开卡日期']) || strtotime($activation_date) === false) {
                        $error_msg[] = '开卡日期格式错误';
                    }
                } catch (Throwable $e) {
                    $error_msg[] = '开卡日期格式错误';
                }
                empty($row['主账户']) && $error_msg[] = '主账户不能为空';
                if (!isset($receive_account[$row['主账户']])) {
                    $error_msg[] = '主账户不存在';
                } else {
                    if ($receive_account[$row['主账户']]['pid'] != 0) {
                        $error_msg[] = '收款账户不是主账户';
                    }
                    $receive_account_id = $receive_account[$row['主账户']]['id'];
                }
                empty($row['信用卡号']) && $error_msg[] = '信用卡号不能为空';
                // 唯一性检验
                $detail = $model->getByCardNumber($row['信用卡号']);
                if ($detail) {
                    $error_msg[] = '信用卡号已存在';
                }
                empty($row['有效开始日期']) && $error_msg[] = '有效开始日期不能为空';
                try {
                    $validity_period_start = $row['有效开始日期']->format('Y-m-d');
                    if (empty($row['有效开始日期']) || strtotime($validity_period_start) === false) {
                        $error_msg[] = '有效开始日期格式错误';
                    }
                } catch (Throwable $e) {
                    $error_msg[] = '有效开始日期格式错误';
                }
                empty($row['有效结束日期']) && $error_msg[] = '有效结束日期不能为空';
                try {
                    $validity_period_end = $row['有效结束日期']->format('Y-m-d');
                    if (empty($row['有效结束日期']) || strtotime($validity_period_end) === false) {
                        $error_msg[] = '有效结束日期格式错误';
                    }
                } catch (Throwable $e) {
                    $error_msg[] = '有效结束日期格式错误';
                }
                empty($row['安全码（CVC）']) && $error_msg[] = '安全码（CVC）不能为空';
                empty($row['币种']) && $error_msg[] = '币种不能为空';
                empty($row['对接服务商']) && $error_msg[] = '对接服务商不能为空';

                $item_data = [
                    "activation_date"     => $activation_date ?? null,
                    "activation_platform" => $row["开卡平台"],
                    "receive_account_id"  => $receive_account_id ?? null,
                    "use_platform"        => $row["使用平台"],
                    "card_number"         => $row['信用卡号'],
                    "validity_period"     => [$validity_period_start, $validity_period_end],
                    "security_code"       => $row['安全码（CVC）'],
                    "currency"            => $row["币种"],
                    "service_provider"    => $row["对接服务商"],
                    "remark"              => $row["备注"],
                ];
                $check_row = $model->dataValidCheck($item_data, $paras_list, false);
                if (!empty($check_row['error'])) {
                    $error_msg = array_merge($error_msg, $check_row['error']);
                }

                if (!empty($error_msg)) {
                    $row['失败原因'] = implode(';', $error_msg);
                    $error_data[] = $row;
                    continue;
                }

                $import_data[] = $item_data;
                $id = $model->add($item_data, '导入');
            }

        }


        if (count($error_data) > 0) {
            // 使用项目根目录动态构建导出文件路径
            $projectRoot = SELF_FK;
            $src = '/public/shop/temp/error/user_import_errors' . time() . rand(100, 1000) . '.xlsx';
            $exportPath = $projectRoot . $src;

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径

            returnSuccess(['error_file' => $src, 'error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $error_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $import_data], '导入成功');
        }
    }

    // 导出
    public function export()
    {
        $type = isset($_POST['type']) ? (int)$_POST['type'] : 1;
        $model = $this->getModelByType($type);

        if ($type == 1) {
            $paras_list = [
                'card_number', 'bank', 'storage_location', 'usage_group', 'fee_group', 'shop_number',
                'cardholder_name', 'credit_card_status',
                'card_type', 'activation_status', 'shop_status', 'dep_id',
                'job_status', 'is_use_self', 'legal_person_shop', 'billing_date',
                'is_annual_fee', 'validity_date', 'application_date',
                'update_date', 'bind_shop_date', 'annual_fee_deduction_date',
                'keys', 'ids'
            ];
        } else {
            $paras_list = [
                'card_number', 'activation_platform', 'main_account', 'shop_number', 'service_provider',
                'credit_card_status', 'use_platform', 'currency', 'shop_status',
                'validity_date', 'activation_date', 'use_status',
                'bind_shop_date', 'update_date',
                'keys', 'ids'
            ];
        }


        $param = array_intersect_key($_POST, array_flip($paras_list));

        $data = $model->getList($param, 'id desc', true);
        if (empty($data)) {
            returnError('没有数据可导出');
        }
        $export_data = [];
        foreach ($data as $item) {
            if ($type == 1) {
                $keys = [
                    '卡号', '开卡银行', '开始有效期', '结束有效期', '类型', '激活情况',
                    '安全码', '还款日', '登记负责人', '开户人姓名', '城市', '部门',
                    '工作情况', '提供日期', '存储位置', '使用对接群', '是否自用', '年费金额',
                    '扣除年费日期', '年费达标标准', '使用费标准', '持卡人收款账户',
                    '费用对接群', '备注'
                ];
            } else {
                $keys = [
                    '开卡日期', '开卡平台', '主账户', '使用平台', '信用卡号', '开始有效期',
                    '结束有效期', '安全码(CVC)', '币种', '对接服务商', '备注'
                ];
            }

            $sortedItem = [];
            foreach ($keys as $key) {
                if (!empty($param['keys']) && !in_array($key, $param['keys'])) continue;
                if ($key == '开始有效期') {
                    $sortedItem[$key] = $item['有效期'][0];
                } elseif ($key == '结束有效期') {
                    $sortedItem[$key] = $item['有效期'][1];
                } elseif ($key == '使用平台') {
                    $sortedItem[$key] = implode(';', $item['使用平台']);
                } else {
                    $sortedItem[$key] = $item[$key] ?? '';
                }
            }
            returnSuccess($sortedItem);
            $export_data[] = $sortedItem;
        }

        $filePath = '/public/shop/temp/credit_card_' . $type . date('YmdHis') . rand(1, 1000) . '.xlsx';
        (new FastExcel($export_data))->export(SELF_FK . $filePath);

        returnSuccess(['src' => $filePath], '导出成功');
    }


    // 批量编辑
    public static function editBatch()
    {
        $paras_list = array('data');
        $request_list = ['data' => '编辑数据'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

        if (count($param['data']) > 500) {
            returnError('批量编辑数据不能超过500条');
        }

        $phone_numbers = array_column($param['data'], 'card_number');
        $phone_numbers = array_unique($phone_numbers);
        if (count($phone_numbers) != count($param['data'])) {
            returnError('批量编辑数据不能有重复的卡号');
        }

        $type = isset($_POST['type']) ? (int)$_POST['type'] : 1;
        $model = (new self())->getModelByType($type);
        $result = $model->editBatch($param['data']);

        returnSuccess($result);
    }

    // 实体卡挂失
    public static function reportLoss()
    {
        $paras_list = array('id', 'is_reissued', 'notifyer', 'remark',);


        $param = array_intersect_key($_POST, array_flip($paras_list));
        if (empty($_POST['id'])) {
            returnError('ID不能为空');
        }
        $id = $_POST['id'];

        $model = new creditCardModel();
        $detail = $model->getById($id);

        if (!$detail) {
            returnError('数据不存在');
        }

        if ($detail['credit_card_status'] != creditCardModel::STATUS_USING) {
            returnError('该卡状态不允许挂失');
        }

        try {
            $other_attach = [];
            if (isset($param['notifyer']) && !empty($param['notifyer'])) {
                $other_attach['notice_user'] = $param['notifyer'];
            }

            $model->edit([
                'credit_card_status' => $param['is_reissued'] ? creditCardModel::STATUS_PENDING_REISSUE : creditCardModel::STATUS_WAIT_LOST,
                'is_in_process'      => 1,
            ], $_POST['id'], $detail, '挂失', $param['remark'], '', $other_attach);

            // 通知
            $user_name = userModel::$wname;
            $users = configModel::noticeUser('credit_card', 'loss', [
                'credit_card_record_keeper' => $detail['record_keeper'],
                'users'                     => $param['notifyer'] ?? []
            ]);
            if (!empty($users)) {
                $msg = '';
                if ($param['is_reissued']) $msg = '并补办';
                messagesFrom::senMeg($users, 1, "【{$user_name}】请求挂失{$msg}【{$detail['card_number']}】，请及时处理", $id, '', '信用卡挂失');
            }

            returnSuccess([], '挂失成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    // 实体卡申请补办
    public static function applyReissue()
    {
        if (empty($_POST['id'])) {
            returnError('ID不能为空');
        }

        $id = $_POST['id'];
        $model = new creditCardModel();
        $detail = $model->getById($id);

        if (!$detail) {
            returnError('数据不存在');
        }

        if ($detail['credit_card_status'] != creditCardModel::STATUS_WAIT_LOST) {
            returnError('该卡不能申请补办');
        }

        try {
            $model->edit([
                'credit_card_status' => creditCardModel::STATUS_PENDING_REISSUE,
                'is_in_process'      => 1,
            ], $id, $detail);

            // 通知
            $user_name = userModel::$wname;
            $users = configModel::noticeUser('credit_card', 'reissue', [
                'credit_card_record_keeper' => $detail['record_keeper'],
            ]);
            if (!empty($users)) {
                messagesFrom::senMeg($users, 1, "【{$user_name}】请求补办【{$detail['card_number']}】，请及时处理", $id, '', '信用卡补办');
            }

            returnSuccess([], '申请补办成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    // 实体卡注销
    public static function cancel()
    {
        $paras_list = array('id', 'notifyer', 'remark', 'type');

        $param = array_intersect_key($_POST, array_flip($paras_list));
        if (empty($_POST['id'])) {
            returnError('ID不能为空');
        }

        $id = $_POST['id'];
        $type = isset($param['type']) ? (int)$param['type'] : 1;
        $model = (new self())->getModelByType($type);
        $detail = $model->getById($id);

        if (!$detail) {
            returnError('数据不存在');
        }

        $other_attach = [];
        if (isset($param['notifyer']) && !empty($param['notifyer'])) {
            $other_attach['notice_user'] = $param['notifyer'];
        }
        // 实体卡注销
        if ($type == 1) {
            if ($detail['credit_card_status'] != creditCardModel::STATUS_USING) {
                returnError('该卡状态不允许注销');
            }

            try {

                $model->edit([
                    'credit_card_status' => creditCardModel::STATUS_PENDING_CANCEL,
                    'is_in_process'      => 1,
                ], $id, $detail, '注销', $param['remark'], '', $other_attach);

                // 通知
                $user_name = userModel::$wname;
                $users = configModel::noticeUser('credit_card', 'close', [
                    'credit_card_record_keeper' => $detail['record_keeper'],
                    'users'                     => $param['notifyer'] ?? []
                ]);
                if (!empty($users)) {
                    messagesFrom::senMeg($users, 1, "【{$user_name}】请求注销【{$detail['card_number']}】，请及时处理", $id, '', '信用卡注销');
                }

                returnSuccess([], '注销成功');
            } catch (Exception $e) {
                returnError($e->getMessage());
            }
        } // 虚拟卡注销
        else {
            if ($detail['use_status'] == '已注销') {
                returnError('该卡状态不允许注销');
            }

            try {
                $model->edit([
                    'use_status' => '已注销',
                ], $id, $detail, '注销', $param['remark'], '', $other_attach);

                // 通知
                $user_name = userModel::$wname;
                $users = configModel::noticeUser('credit_card', 'close', [
                    'users' => $param['notifyer'] ?? []
                ]);
                if (!empty($users)) {
                    messagesFrom::senMeg($users, 1, "【{$user_name}】注销了【{$detail['card_number']}】", $id, '', '信用卡注销');
                }

                returnSuccess([], '注销成功');
            } catch (Exception $e) {
                returnError($e->getMessage());
            }
        }


    }

    // 实体卡跟进记录
    public static function addFollowUp()
    {
        $paras_list = array('id', 'is_finished', 'credit_card_status', 'notifyer', 'remark');

        $param = array_intersect_key($_POST, array_flip($paras_list));
        if (empty($_POST['id'])) {
            returnError('ID不能为空');
        }

        $model = new creditCardModel();
        $detail = $model->getById($_POST['id']);

        if (!$detail) {
            returnError('数据不存在');
        }

        $other_attach = [];
        if (isset($param['notifyer']) && !empty($param['notifyer'])) {
            $other_attach['notice_user'] = $param['notifyer'];
        }

        if ($param['is_finished'] == 1) {
            $model->edit([
                'is_in_process'      => 0,
                'credit_card_status' => $param['credit_card_status'],
            ], $_POST['id'], $detail, "跟进", $param['remark'], "", $other_attach);
        } else {
            // 仅记录日志
            $model->saveDataLog($model->table, $param['id'], [], [], "跟进", $param['remark'], "", $other_attach);
        }

        returnSuccess([], '添加跟进记录成功');
    }

    // 实体卡申请费用
    public static function applyFee()
    {
        $paras_list = array('id', 'fee_year', 'amount', 'remark');

        $param = array_intersect_key($_POST, array_flip($paras_list));
        if (empty($_POST['id'])) {
            returnError('ID不能为空');
        }

        $model = new creditCardModel();
        $detail = $model->getById($_POST['id']);

        if (!$detail) {
            returnError('数据不存在');
        }

        if ($detail['fee_id']) {
            returnError("当前信用卡已存在费用申请");
        }

        $fee_year = json_decode($detail['fee_year'], true) ?? [];
        if (in_array($param['fee_year'], $fee_year)) {
            returnError("{$param['fee_year']}年费用已申请");
        }

        // 创建费用记录
        $feeModel = new creditCardFeeModel();
        $feeData = [
            'credit_card_id' => $param['id'],
            'fee_year'       => $param['fee_year'],
            'amount'         => $detail['annual_fee_amount'],
            'type'           => creditCardFeeModel::TYPE_APPLY,
            'remark'         => $param['remark'],
        ];

        $fee_id = $feeModel->add($feeData);

        // 写回信用卡表
        dbShopMysql::getInstance()->table($model->table)
            ->where('id = :id', ['id' => $param['id']])
            ->update([
                'is_in_process'      => 1,
                'credit_card_status' => creditCardModel::STATUS_FEE,
                'fee_id'             => $fee_id,
            ]);

        // 通知
        $user_name = userModel::$wname;
        $users = configModel::noticeUser('credit_card', 'fee', [
            'credit_card_record_keeper' => $detail['record_keeper'],
        ]);
        if (!empty($users)) {
            messagesFrom::senMeg($users, 1, "【{$user_name}】提交了【{$detail['card_number']}】{$param['fee_year']}的信用卡费用，请进行费用确认", $param['id'], '', '申请信用卡费');
        }

        returnSuccess(['id' => $fee_id], '费用申请成功');
    }

    // 实体卡费用确认
    public static function confirmFee()
    {
        $paras_list = array('id', 'fee_id', 'pay_file', 'pay_date', 'remark');

        $param = array_intersect_key($_POST, array_flip($paras_list));
        if (empty($_POST['id'])) {
            returnError('ID不能为空');
        }

        $model = new creditCardModel();
        $detail = $model->getById($_POST['id']);

        if (!$detail) {
            returnError('数据不存在');
        }

        // 创建费用记录
        $feeModel = new creditCardFeeModel();
        $apply_fee = $feeModel->getById($param['fee_id']);
        if (!$apply_fee) {
            returnError('费用记录不存在');
        }

        $feeData = [
            'credit_card_id' => $param['id'],
            'pid'            => $param['fee_id'],
            'fee_year'       => $apply_fee['fee_year'],
            'amount'         => $apply_fee['amount'],
            'type'           => creditCardFeeModel::TYPE_CONFIRM,
            'pay_date'       => $param['pay_date'],
            'pay_file'       => json_encode($param['pay_file'], JSON_UNESCAPED_UNICODE),
            'status'         => 1,
            'remark'         => $param['remark'],
        ];

        $fee_id = $feeModel->add($feeData);

        // 申请费用状态更新
        $feeModel->edit([
            'status' => 1,
        ], $param['fee_id']);

        $fee_year = json_decode($detail['fee_year'], true);
        $fee_year[] = $apply_fee['fee_year'];

        // 写回信用卡表
        dbShopMysql::getInstance()->table($model->table)
            ->where('id = :id', ['id' => $param['id']])
            ->update([
                'is_in_process'      => 0,
                'fee_id'             => 0,
                'fee_year'           => json_encode($fee_year),
                'credit_card_status' => creditCardModel::STATUS_USING,
            ]);

        returnSuccess(['id' => $fee_id], '费用确认成功');
    }

    // 撤销流程
    public static function cancelProcess()
    {
        $paras_list = array('id', 'remark');

        $param = array_intersect_key($_POST, array_flip($paras_list));
        if (empty($_POST['id'])) {
            returnError('ID不能为空');
        }

        $model = new creditCardModel();
        $detail = $model->getById($_POST['id']);

        if (!$detail) {
            returnError('数据不存在');
        }

        if (!in_array($detail['credit_card_status'], [
            creditCardModel::STATUS_PENDING_CANCEL,  // 注销流程
            creditCardModel::STATUS_WAIT_LOST, // 挂失不补办
            creditCardModel::STATUS_PENDING_REISSUE, // 挂失不补办
            creditCardModel::STATUS_FEE, // 挂失不补办
        ])) {
            returnError('不允许取消当前流程');
        }

        $feeModel = new creditCardFeeModel();
        if ($detail['credit_card_status'] == creditCardModel::STATUS_FEE) {
            // 申请费用
            $apply_fee = $feeModel->getById($detail['fee_id']);
            if (!$apply_fee) {
                returnError('费用记录不存在');
            }
        }

        $id = $_POST['id'];
        $updateData = [
            'is_in_process'      => 0,
            'credit_card_status' => $model::STATUS_USING,
        ];

        $type = '';
        if ($detail['credit_card_status'] == creditCardModel::STATUS_PENDING_CANCEL) {
            // 注销流程
            $users = configModel::noticeUser('credit_card', 'close', [
                'credit_card_record_keeper' => $detail['record_keeper'],
            ]);
            $type = '注销';
        } elseif ($detail['credit_card_status'] == creditCardModel::STATUS_WAIT_LOST) {
            // 挂失不补办
            $users = configModel::noticeUser('credit_card', 'loss', [
                'credit_card_record_keeper' => $detail['record_keeper'],
            ]);
            $type = '挂失';
        } elseif ($detail['credit_card_status'] == creditCardModel::STATUS_PENDING_REISSUE) {
            // 挂失补办
            $users = configModel::noticeUser('credit_card', 'reissue', [
                'credit_card_record_keeper' => $detail['record_keeper'],
            ]);
            $type = '补办';
        } elseif ($detail['credit_card_status'] == creditCardModel::STATUS_FEE) {
            // 申请费用
            $users = configModel::noticeUser('credit_card', 'fee', [
                'credit_card_record_keeper' => $detail['record_keeper'],
            ]);
            $type = '费用';

            // 创建费用记录
            $feeData = [
                'credit_card_id' => $param['id'],
                'pid'            => $detail['fee_id'],
                'fee_year'       => $apply_fee['fee_year'],
                'amount'         => $apply_fee['amount'],
                'type'           => creditCardFeeModel::TYPE_CANCEL,
                'status'         => 1,
                'remark'         => $param['remark'],
            ];

            $fee_id = $feeModel->add($feeData);

            // 申请费用状态更新
            $feeModel->edit([
                'status' => 1,
            ], $detail['fee_id']);

            $updateData['fee_id'] = null;
        }


        $model->edit($updateData, $_POST['id'], $detail, '撤销流程', $param['remark']);

        if (!empty($users)) {
            $user_name = userModel::$wname;
            messagesFrom::senMeg($users, 1, "【{$user_name}】已撤销【{$detail['card_number']}】的【{$type}】申请", $id, '', '撤销申请');
        }

        returnSuccess([], '撤销流程成功');
    }

    // 将中文键名转为英文键名
    protected static function changeToEnKey($data, $paras_list)
    {
        $result = [];
        $flip_list = array_flip($paras_list);

        foreach ($data as $key => $value) {
            if (isset($flip_list[$key])) {
                $result[$flip_list[$key]] = $value;
            }
        }

        return $result;
    }
}
