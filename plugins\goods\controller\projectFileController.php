<?php
/**
 * @author: zhangguoming
 * @Time: 2024/1/2 9:23
 */

namespace  plugins\goods\controller;

use plugins\goods\form\downLoadFrom;
use core\lib\config;
use core\lib\db\dbMysql;
use core\lib\log;

class projectFileController
{
    //获取文件列表
    public function getList()
    {
        $paras_list = array('flow_path_id', 'filename', 'goods_file_type', 'goods_id', 'goods_name','order_by', 'page', 'page_size');
        $param = arrangeParam($_POST, $paras_list);
        $flow_path_id = (int)$param['flow_path_id'];
        $goods_id = (int)$param['goods_id'];
        $db = dbMysql::getInstance();
        $db->table('goods_project_file','a')
            ->leftJoin('goods_new','b','b.id = a.goods_id')
            ->leftJoin('qwuser','c','c.id = a.user_id')
            ->where('where a.is_delete = 0');
        if ($flow_path_id > 0) {
            $db->andWhere('and a.flow_path_id=:flow_path_id',['flow_path_id'=>$flow_path_id]);
        }
        if ($goods_id > 0) {
            $db->andWhere('and a.goods_id=:goods_id',['goods_id'=>$goods_id]);
        }
        if (!empty($param['goods_name'])) {
            $db->andWhere('and b.goods_name like :goods_name',['goods_name'=>"%{$param['goods_name']}%"]);
        }
        if (!empty($param['filename'])) {
            $db->andWhere('and a.filename like :filename',['filename'=>"%{$param['filename']}%"]);
        }
        if (!empty($param['goods_name'])) {
            $db->andWhere('and b.goods_name like :goods_name',['goods_name'=>"%{$param['goods_name']}%"]);
        }
        if (!empty($param['order_by'])) {
            $order_by = json_decode($param['order_by'],true);
            $order_str = '';
            foreach ($order_by as $k=>$ord){
                $order_str .= 'a.'.$k.' '.$ord.',';
            }
        }
        if(empty($order_str)) {
            $db->order('a.id desc');
        } else {
            $db->order(trim($order_str,','));
        }
        if (!empty($param['goods_file_type'])) {
            $file_type = json_decode($param['goods_file_type']);
            if (count($file_type)) {
                $string = '';
                foreach ($file_type as $v) {
                    switch ($v) {
                        case 1;
                            $string.=' or a.source_type = 1';break;
                        case 2;
                            $string.=' or a.source_type = 2';break;
                        case 3;
                            $string.=' or (a.source_type = 0 and event_type = 4)';break;
                        case 4;
                            $string.=' or (a.source_type = 0 and event_type = 5)';break;
                        case 5;
                            $string.=' or (a.source_type = 0 and event_type = 7)';break;
                    }
                }
                $string = trim($string,' or');
                if ($string) {
                    $db->andWhere('and ('.$string.')');
                }
            }
        }

        $db->field('a.id,a.event_type,a.source_type,a.extension,a.filename,a.created_at,a.src,a.thumb_src,b.goods_name,c.wname,a.flow_path_id');
        $list = $db->pages($param['page'],$param['page_size']);
        foreach ($list['list'] as &$v) {
            $v['flow_path_name'] = config::getDataName('flow_path',$v['flow_path_id']);
        }
        returnSuccess($list);
    }

    //文件下载
    public function downLoadFile(){
        ini_set('memory_limit', '1024M');
        $id = (int)$_GET['id'];
        $source_type = (int)$_GET['source_type'];
        if (!$id || !$source_type) {
            SetReturn(-1,'参数有误');
        }
        $db = dbMysql::getInstance();
        if ($source_type == 1) {
            $db->table('goods_project_file');
            $db->where('where id = '.$id);
            $db->field('filename,src,goods_id');
            $file_data = $db->one();
            if (!$file_data) {
                SetReturn(-1,'未查询到图片信息');
            }
        } elseif ($source_type == 2) {
            $db->table('goods_attachment');
            $db->where('where id = '.$id);
            $db->field('file_name as filename,url as src,goods_id');
            $file_data = $db->one();
            if (!$file_data) {
                SetReturn(-1,'未查到图片信息');
            }
        } else {
            SetReturn(-1,'参数来源类型不对');
        }
        $base64Data = downLoadFrom::getdownLoadBase64Encode($file_data['src'],$file_data['goods_id']);
        $res = [
            'filename'=>$file_data['filename'],
//            'url'=>$file_data['src'],
            'base64Data'=>$base64Data,
        ];
        //记录下载日志
        log::downLoadLog($id, $source_type);
        returnSuccess($res);
    }

}