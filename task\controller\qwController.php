<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/11 13:32
 */

namespace task\controller;

use core\jobs\qwUserSynJobs;
use core\lib\config;
use core\lib\db\dbCMysql;
use core\lib\db\dbMysql;
use core\lib\log;
use core\lib\predisV;

class qwController
{
    private array $qw_config = [];

    //更新企业部门信息
    public function getPartmentList()
    {
        $this->qw_config = config::all('qw');
        $queue_key = config::get('delay_queue_key', 'app');
        $api_token = $_POST['token'] ?? '';
        if ($api_token != '576d9908a51f11ee97f19c2dcd695fd0') {
            echo 1111;
            die;
        }
        $redis = (new predisV())::$client;
        $token = $this->getToken();
        $url = $this->qw_config['partment_url'] . '?access_token=' . $token;
        $list = requestHttp($url);
        $department = isset($list['department']) ? $list['department'] : [];
        if (count($department) > 0) {
            $db = dbMysql::getInstance();
            $qw_partments = [];
            $time = microtime(true);
            foreach ($department as $item) {
                $db->table('qwdepartment');
                $partment = $db->where('where wp_id = ' . $item['id'])->one();
                $data_ = [
                    'name'              => $item['name'],
                    'department_leader' => json_encode($item['department_leader']),
                    'qw_parentid'       => $item['parentid'],
                    'sort'              => $item['order'],
                    'wp_id'             => $item['id'],
                ];
                if ($partment) {
                    $data_['updated_at'] = date('Y-m-d H:i:s');
                    $db->update($data_);
                } else {
                    $data_['created_at'] = date('Y-m-d H:i:s');
                    $db->insert($data_);
                }
                //TODO 跟新部门成员  使用异步队列（防止超时）
                $qw_partments[] = ['id' => $item['id'], 'name' => $item['name']];
                if (count($qw_partments) == 10) {
                    $time += 0.1 * 60;
                    $task = new qwUserSynJobs($qw_partments); // 创建任务类实例
                    $redis->zAdd($queue_key, [], $time, serialize($task));
                    $qw_partments = [];
                }
            }
            if (count($qw_partments)) {
                //$time += 10*60;
                $time += 0.1 * 60;
                $task = new qwUserSynJobs($qw_partments); // 创建任务类实例
                $redis->zAdd($queue_key, [], $time, serialize($task));
            }
            log::taskLog()->info('企微：获取部门更新成功');
        } else {
            log::taskLog()->error('企微：获取部门事变');
        }
    }

    protected function getToken()
    {
        $redis = (new predisV())::$client;
        $token = $redis->get('qy_token_');
        if ($token) {
            return $token;
        } else {
            $qw_config = $this->qw_config;
            $token_url = $qw_config['token_url_nei'];
            $data = requestHttp($token_url, 'get');
            if ($data && isset($data['data'])) {
                $redis->set('qy_token', $data['data']);
                $redis->expire('qy_token', 5 * 60);
                return $data['data'];
            } else {
                log::taskLog()->error('企微：获取token失败');
                die;
            }
//            if ($data && $data['errcode']==0) {
//                $redis->set('qy_token_', $data['access_token']);
//                $redis->expire('qy_token_',5*60);
//                return $data['access_token'];
//            } else {
//                log::taskLog()->error('企微：获取token失败');die;
//            }
        }
    }

    //发送应用消息
    function sendMessage()
    {
        global $qw_config;
        global $log;
        $token = $this->getToken();
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=' . $token;
        //获取跳转地址
        $msg = [
            'type'      => 'textcard',
            'qw_userid' => 'ZhangGuoMing',
            'msg'       => 'sad',
            'title'     => '6262',
            'data'      => json_encode(['sda' => 1616])
        ];
        $msg_url = 'https://www.baidu.com/';
        //获取数据
        $data = [
            'touser'  => $msg['qw_userid'],
            'msgtype' => $msg['type'] ?? 'text',
            'agentid' => 1000007,//企业应用的id
        ];
        if ($data['msgtype'] == 'text') {
            $data['text'] = ['content' => $msg['msg']];
        } elseif ($data['msgtype'] == 'textcard') {
            $data['textcard'] = [
                'title'       => $msg['title'],
                'description' => $msg['msg'],
                'url'         => $msg_url,
            ];
        }
        //发送消息
        $res = requestHttp($url, 'post', json_encode($data));
        if (!empty($res)) {
            //日志记录(不管成功与否)
            dd($res);
        } else {
            return false;
        }

    }


    public
    function addTxtToPic()
    {
        $text = '88888';
//        $paddingChar = '-';
//        $paddedString = mb_str_pad($text, 15, $paddingChar, STR_PAD_RIGHT);
        $img = 'D:\project\oaAdmin\oa-adme-api\public\temp\add_txt_imgs\ZhangGuoMing\无标题.jpg';
        $img_txt = 'D:\project\oaAdmin\oa-adme-api\public\temp\add_txt_imgs\ZhangGuoMing\888.jpg';


        $newExifData = '-Make=YourCameraMake -Model=YourCameraModel'; // 设置新的 EXIF 数据

// 调用 exiftool 修改 EXIF 数据
        $command = "perl-exiftool $newExifData $img";
        exec($command, $output, $returnVar);

        if ($returnVar === 0) {
            echo "EXIF data updated successfully.";
        } else {
            echo "Error updating EXIF data: " . implode("\n", $output);
        }
        dd(55);


        addWatermarkAsExif($img, $img_txt, '88484');
        $dddd = getWatermarkFromExif($img_txt, '88484');
        dd($dddd);
////
//        embedTextInImage($img,$img_txt,$text);
////        $img_txt = 'D:\project\oaAdmin\oa-adme-api/public/temp/add_txt_imgs/ZhangGuoMing/e6660003ee8c11.png';
////        $img_txt = 'D:\project\oaAdmin\oa-adme-api/public/temp/add_txt_imgs/ZhangGuoMing/e666004006e2e7.jpeg';
//        $dddd = extractTextFromImage($img,50);
        dd($dddd);
    }

    //批量删除消息
    public
    function delMessgae()
    {
        dd(11);
        $redis = (new predisV())::$client;

        $streams = ['qw_message' => 0]; // 从流的开始读取，0 表示最小的可能ID
        $result = $redis->xread($streams, '100');

        // 遍历读取到的数据
        $messageId = [];
        foreach ($result as $stream => $entries) {
            foreach ($entries as $id => $entry) {
                $messageId[] = $id; // 获取消息ID
            }
        }
//        dd($messageId);
        $redis->xDel('qw_message', $messageId);
    }
}