<?php

namespace plugins\shop\models;

use core\lib\redisCached;
use Exception;

class shopRegisterModel extends baseModel
{
    public string $table = 'shop_register';

    // 任务状态常量
    const STATUS_PROCESSING = 1;  // 处理中
    const STATUS_SUCCESS = 2;    // 注册成功
    const STATUS_FAILED = 3;     // 注册失败
    const STATUS_PAUSED = 4;     // 已暂停
    const STATUS_FAILED_COMPLETED = 5; // 注册失败-彻底

    public function __construct()
    {
        self::$paras_list = shopModel::$paras_list;
        parent::__construct();
    }

    public static array $json_keys = [
        'receive_card_id'
    ];


    /**
     * 格式化输出项
     */
    public function formatItem($item, $maps = [])
    {
        $users = $maps['users'] ?? [];
        if (empty($maps['users'])) {
            $users = redisCached::getUserInfo();
            $users = array_column($users, 'user_name', 'user_id');
        }
        $deps = $maps['deps'] ?? [];
        if (empty($deps)) {
            $deps = redisCached::getDepartment();
            $deps = array_column($deps, 'name', 'id');
        }
        $company = $maps['company'] ?? [];
        if (empty($company)) {
            $company = redisCached::getCompany();
        }
        $phone_card = $maps['phone_card'] ?? [];
        if (empty($phone_card)) {
            $phone_card = redisCached::getPhoneCard();
        }
        $email = $maps['email'] ?? [];
        if (empty($email)) {
            $email = redisCached::getEmail();
        }
        $credit_card = $maps['credit_card'] ?? [];
        if (empty($credit_card)) {
            $credit_card = redisCached::getCreditCard();
        }
        $receive_card = $maps['receive_card'] ?? [];
        if (empty($receive_card)) {
            $receive_card = redisCached::getReceiveCard();
        }
        $trademark = $maps['trademark'] ?? [];
        if (empty($trademark)) {
            $trademark = redisCached::getTrademark();
        }

        $maps = [
            ['name' => 'operator_name', 'maps' => $users, 'key' => 'operator'],
            ['name' => 'dep_name', 'maps' => $deps, 'key' => 'dep_id'],

            // 公司相关
            ['name' => 'company_name', 'maps' => array_column($company, 'company_name', 'id'), 'key' => 'company_id'],
            ['name' => 'company_status', 'maps' => array_column($company, 'company_status', 'id'), 'key' => 'company_id'],
            ['name' => 'company_register_country', 'maps' => array_column($company, 'register_country', 'id'), 'key' => 'company_id'],

            // 注册手机/注册邮箱
            ['name' => 'phone_number', 'maps' => array_column($phone_card, 'phone_number', 'id'), 'key' => 'phone_card_id'],
            ['name' => 'phone_manager', 'maps' => array_column($phone_card, 'phone_manager', 'id'), 'key' => 'phone_card_id'],
            ['name' => 'email_account', 'maps' => array_column($email, 'email_account', 'id'), 'key' => 'email_id'],
            ['name' => 'email_safe_phone_number', 'maps' => array_column($email, 'email_safe_phone_number', 'id'), 'key' => 'email_id'],
            ['name' => 'email_assistant_email', 'maps' => array_column($email, 'email_assistant_email', 'id'), 'key' => 'email_id'],

            // 信用卡
            ['name' => 'credit_card_number', 'maps' => array_column($credit_card, 'card_number', 'id'), 'key' => 'credit_card_id'],
            ['name' => 'credit_card_validity_period', 'maps' => array_column($credit_card, 'validity_period', 'id'), 'key' => 'credit_card_id'],

            // 收款卡
            ['name' => 'receive_card', 'maps' => $receive_card, 'key' => 'receive_card_id', 'is_array' => 1, 'keys' => ['card_number', 'receive_platform']],

            // 公司法人
            ['name' => 'legal_person_id', 'maps' => array_column($company, 'legal_person_id', 'id'), 'key' => 'company_id'],

            // 品牌
            ['name' => 'trademark', 'maps' => $trademark, 'key' => 'trademark_id', 'is_array' => 1, 'keys' => ['brand_name']],
        ];

        return parent::formatItem($item, $maps);
    }

    // 数据校验
    public function dataValidCheck(array $data, array $param_list = [], $is_throw = true, array $error = []): array
    {
        if (empty($param_list)) $param_list = shopModel::$paras_list;
        // 邮箱和电话卡只需要一个即可
        if (!isset($data['email_id']) && !isset($data['phone_card_id'])) {
            if (!$is_throw) {
                $error[] = '邮箱和电话卡不能同时为空';
            } else {
                throw new Exception('邮箱和电话卡不能同时为空');
            }
        }
        unset($param_list['email_id']);
        unset($param_list['phone_card_id']);

        if ($data['register_type'] == "自注册") {
            unset($param_list['price']);
            unset($param_list['currency']);
        } elseif ($data['register_type'] == "定制" || $data['register_type'] == '现号') {
            unset($param_list['company_id']);
            $legal_person = $data['legal_person'];
            $legal_person_model = new legalPersonModel();
            $legal_person_model->dataValidCheck($legal_person);
            $company = $data['company'];
            $company_model = new companyModel();
            $company_model->dataValidCheck($company);
        }

        // 注册日期不校验
        unset($param_list['register_date']);

        return parent::dataValidCheck($data, $param_list, $is_throw, $error);
    }

    /**
     * 新增注册任务
     */
    public function add($data, $type = '新增')
    {
        $data['register_status'] = self::STATUS_PROCESSING;
        if ($data['register_type'] == "自注册") {
            unset($data['price']);
            unset($data['currency']);
        } elseif ($data['register_type'] == "定制" || $data['register_type'] == '现号') {
            unset($data['company_id']);
            $legal_person = $data['legal_person'];
            $legal_person_model = new legalPersonModel();
            $legal_person['type'] = '定制';
            // 检查身份证号唯一性
            $detail = $legal_person_model->getByIdCard($legal_person['id_card'] ?? '');
            if ($detail) {
                returnError('法人身份证号已存在');
            }
            $legal_person_id = $legal_person_model->add($legal_person);
            $company = $data['company'];
            $company['legal_person_id'] = $legal_person_id;
            $company['register_status'] = companyModel::REGISTER_STATUS_SUCCESS;
            $company_model = new companyModel();
            $company_id = $company_model->add($company);
            $data['company_id'] = $company_id;
            unset($data['company']);
            unset($data['legal_person']);
        }

        return parent::add($data, $type);
    }

    /**
     * 编辑注册任务
     */
    public function edit($data, $id, $old_data = [], $type = '编辑', $remark = '', $result = '', $other_attach = []) {
        // 处理数据
        if ($data['register_type'] == "自注册") {
            unset($data['price']);
            unset($data['currency']);
        } elseif ($data['register_type'] == "定制" || $data['register_type'] == '现号') {
            unset($data['company_id']);
            $legal_person = $data['legal_person'];
            $legal_person_model = new legalPersonModel();
            $legal_person_model->dataValidCheck($legal_person);
            $company = $data['company'];
            $company_model = new companyModel();
            $company_model->dataValidCheck($company);
        }

        parent::edit($data, $id, $old_data, $type, $remark, $result, $other_attach);

    }

    /**
     * 跟进注册任务
     */
    public function follow($id, $data)
    {
        $old_data = $this->getById($id);
        $old_data = $this->formatItem($old_data);
        if (!$old_data) {
            throw new Exception('数据不存在');
        }

        if ($old_data['register_status'] != self::STATUS_PROCESSING) {
            throw new Exception('注册任务不能再跟进');
        }

        $updateData = [];

        if ($data['shop_apply_id']) {
            // 需要校验是否已经被绑定过
            $exist = $this->db->table('shop_register')
                ->where('shop_apply_id = :shop_apply_id and id != :id', [
                    'shop_apply_id' => $data['shop_apply_id'],
                    'id' => $id
                ])
                ->one();
            if ($exist) {
                throw new Exception('店铺申请已被绑定');
            }

            $updateData['shop_apply_id'] = $data['shop_apply_id'];
        }

        if ($data['is_end'] == 1) {
            $updateData['register_status'] = $data['register_result'] ? self::STATUS_SUCCESS : self::STATUS_FAILED;
            $updateData['register_date'] = $data['register_date'];
            $updateData['activation_date'] = $data['activation_date'];
            $updateData['finished_at'] = date('Y-m-d H:i:s');

            if ($data['register_result'] == 1) {
                // 写入店铺表
                $shopModel = new shopModel();
                $old_data['register_date'] = $data['register_date'];
                $old_data['activation_date'] = $data['activation_date'];
                $shopModel->dataValidCheck($old_data, shopModel::$paras_list);
                unset($old_data['id']);
                $shop_id = $shopModel->add($old_data);
                $updateData['shop_id'] = $shop_id;
            }

        }

        if (!empty($updateData)) {
            parent::edit($updateData, $id, $old_data, '跟进', $data['remark'] ?? '', $data['result'] ?? '');
        } else {
            // 仅记录日志
            $this->saveDataLog($this->table, $id, [], [], "跟进",$data['remark'] ?? '', $data['register_result'] ?? '');
        }
    }

    /**
     * 重新注册
     */
    public function reRegister($id, $data)
    {
        $old_data = $this->getById($id);
        if (!$old_data) {
            throw new Exception('数据不存在');
        }

        // 原任务关闭
        // todo 记录原有的法人、公司信息
        parent::edit(['status' => self::STATUS_FAILED_COMPLETED], $id, $old_data, '重新注册');

        // 唯一性校验
        $detail = $this->getByShopNumber($data['shop_number']);
        if ($detail) {
            throw new Exception('店铺编号已存在');
        }

        unset($data['id']);
        $data['status'] = self::STATUS_PROCESSING;
        return parent::add($data, '新增');
    }

    /**
     * 暂停注册任务
     */
    public function pause($id)
    {
        $old_data = $this->getById($id);
        if (!$old_data) {
            throw new Exception('数据不存在');
        }

        if ($old_data['register_status'] != self::STATUS_PROCESSING) {
            throw new Exception('注册任务不能暂停');
        }
        $updateData = [
            'register_status' => self::STATUS_PAUSED,
        ];

        parent::edit($updateData, $id, $old_data, '暂停');
    }

    /**
     * 取消暂停
     */
    public function cancelPause($id)
    {
        $old_data = $this->getById($id);
        if (!$old_data) {
            throw new Exception('数据不存在');
        }

        if ($old_data['register_status'] != self::STATUS_PAUSED) {
            throw new Exception('注册任务不能取消暂停');
        }
        $updateData = [
            'register_status' => self::STATUS_PROCESSING,
        ];

        parent::edit($updateData, $id, $old_data, '取消暂停');
    }

    /**
     * 催办
     */
    public function urge($id)
    {
        $detail = $this->detail($id);
        if (!$detail) {
            return false;
        }

        return $this->db->table($this->table)
            ->where('id', $id)
            ->update([
                'last_urge_at' => date('Y-m-d H:i:s'),
                'urge_count'   => intval($detail['urge_count']) + 1,
                'urge_by'      => $_SESSION['user_id'] ?? 0
            ]);
    }

    /**
     * 获取详情
     */
    public function detail($id)
    {
        return $this->db->table($this->table)
            ->where('id', $id)
            ->one();
    }

    public static function getMaps () {
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');
        $deps = redisCached::getDepartment();
        $deps = array_column($deps, 'name', 'id');
        $company = redisCached::getCompany();
        $phone_card = redisCached::getPhoneCard();
        $email = redisCached::getEmail();
        $credit_card = redisCached::getCreditCard();
        $receive_card = redisCached::getReceiveCard();
        $trademark = redisCached::getTrademark();
        return [
            'users'        => $users,
            'deps'         => $deps,
            'company'      => $company,
            'phone_card'   => $phone_card,
            'email'        => $email,
            'credit_card'  => $credit_card,
            'receive_card' => $receive_card,
            'trademark'    => $trademark
        ];
    }

    /**
     * 获取列表
     */
    public function getList($param, $order = 'id desc', $is_export = false)
    {
        $db = $this->db->table($this->table, 'sr')
            ->field('sr.*')
            ->leftJoin('company', 'c', 'c.id = sr.company_id')
            ->where('1=1');

        // 店铺编号
        if (!empty($param['shop_number'])) {
            $this->db->andWhere('sr.shop_number = :shop_number', ['shop_number' => $param['shop_number']]);
        }
        // 注册类型
        if (!empty($param['register_type'])) {
            $db->andWhere('sr.register_type = :register_type', ['register_type' => $param['register_type']]);
        }
        // 注册状态
        if (isset($param['register_status'])) {
            $db->andWhere('sr.register_status = :register_status', ['register_status' => $param['register_status']]);
        }
        // 公司名称
        if (!empty($param['company_name'])) {
            $db->andWhere('c.company_name like :company_name', ['company_name' => '%' . $param['company_name'] . '%']);
        }
        // 公司状态
        if (isset($param['company_status'])) {
            $db->andWhere('c.company_status = :company_status', ['company_status' => $param['company_status']]);
        }
        // 站点
        if (!empty($param['country_site'])) {
            $db->andWhere('sr.shop_site = :shop_site', ['shop_site' => $param['country_site']]);
        }
        // 开始时间
        if (!empty($param['created_at']) && is_array($param['created_at'])) {
            $db->andWhere('sr.created_at >= :create_start_time and sr.created_at <= :create_end_time', [
                'create_start_time' => $param['created_at'][0],
                'create_end_time'   => $param['created_at'][1] . ' 23:59:59'
            ]);
        }
        // 结束时间
        if (!empty($param['finished_at']) && is_array($param['finished_at'])) {
            $db->andWhere('sr.finished_at >= :end_time_start and sr.finished_at <= :end_time_end', [
                'end_time_start' => $param['finished_at'][0],
                'end_time_end'   => $param['finished_at'][1] . ' 23:59:59'
            ]);
        }

        $db->order($order);

        if (!$is_export && !empty($param['page']) && !empty($param['page_size'])) {
            $list = $db->pages($param['page'], $param['page_size']);
            $maps = self::getMaps();

            foreach ($list['list'] as &$item) {
                $item = $this->formatItem($item, $maps);
            }
            return $list;
        } else {
            $list = $db->list();

            $result = [];
            $maps = self::getMaps();
            foreach ($list as $item) {
                $formatted = $this->formatItem($item, $maps);
                if ($is_export) {
                    // 导出使用中文键名
                    $result[] = self::changeToCnKey($formatted);
                } else {
                    $result[] = $formatted;
                }
            }

            return $result;
        }
    }

    // 通过店铺编号获取信息
    public function getByShopNumber($shop_number, $id = null)
    {
        $this->db->table($this->table)->where('where shop_number = :shop_number', ['shop_number' => $shop_number]);
        if ($id) {
            $this->db->andWhere('id != :id', ['id' => $id]);
        }
        return $this->db->one();
    }
}
