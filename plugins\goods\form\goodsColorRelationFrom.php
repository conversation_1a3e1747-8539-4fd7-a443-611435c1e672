<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/1 16:55
 */

namespace  plugins\goods\form;

use core\lib\db\dbMysql;

class goodsColorRelationFrom
{
    public static mixed $color_relation_list = false;

    public static function getGoodsColor(int $goods_id,array $color_relation_ids = []) {
        $db = dbMysql::getInstance();
        $db->table('goods_color_relation','a')
            ->leftJoin('goods_color','b','b.id=a.color_id')
            ->where('where a.goods_id=:goods_id and a.is_delete = 0',['goods_id'=>$goods_id]);
        if (count($color_relation_ids)) {
            $db->whereIn('a.id',$color_relation_ids);
        }
        $data = $db->field('a.id,a.has_handset,a.color_id,a.sku,b.color_name,b.color_name_en')->list();
        return $data;
    }
    //图片需求列表获取产品分类
    public static function getImgsRequestListColor(array $list) {
        if (!count($list)) {
            return [];
        }
        $goods_ids = array_column($list,'goods_id');
        if (!self::$color_relation_list) {
            $db = dbMysql::getInstance();
            $color_list = $db->table('goods_color_relation','a')
                ->leftJoin('goods_color','b','b.id = a.color_id')
                ->where('where a.is_delete = 0')
                ->whereIn('goods_id',$goods_ids)
                ->field('a.id as color_id,a.has_handset,a.goods_id,b.color_name,b.color_name_en')
                ->list();
            self::$color_relation_list = $color_list;
        } else {
            $color_list = self::$color_relation_list;
        }
        if (count($color_list)) {
            foreach ($list as $k=>$v) {
                $color_item = [];
                foreach ($color_list as $v1) {
                    if ($v['goods_id'] == $v1['goods_id']) {
                        $color_item[] = $v1;
                    }
                }
                $list[$k]['color_list'] = $color_item;
            }
        }
        return $list;

    }
    //商品列表获取颜色信息
    public static function getImgsGoodsListColor(array $list) {
        if (!count($list)) {
            return [];
        }
        $goods_ids = array_column($list,'id');
        $db = dbMysql::getInstance();
        $color_list = $db->table('goods_color_relation','a')
            ->leftJoin('goods_color','b','b.id = a.color_id')
            ->where('where a.is_delete = 0')
            ->whereIn('goods_id',$goods_ids)
            ->field("a.id,a.color_id,a.has_handset,a.goods_id,b.color_name,b.color_name_en,a.sku,'' as goods_img")
            ->list();
        if (count($color_list)) {
            foreach ($list as $k=>$v) {
                $color_item = [];
                foreach ($color_list as $v1) {
                    if ($v['id'] == $v1['goods_id']) {
                        $color_item[] = $v1;
                    }
                }
                $list[$k]['color_relation'] = $color_item;
            }
        }
        return $list;
    }
}