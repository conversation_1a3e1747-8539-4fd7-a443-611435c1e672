<?php

namespace financial\form;

use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use Rap2hpoutre\FastExcel\FastExcel;

/**
 * 项目表单类
 */
class projectForm
{
    public static array $export_project_key = [
        'id' => 'ID',
        'p_id' => '父ID',
        'level' => '级别',
        'project_name' => '项目名称',
        'user_id' => '负责人ID',
        'user_ids' => '项目成员ID集合',
        'created_time' => '创建时间',
        'updated_time' => '更新时间',
        'status_lx' => '领星状态',
        'is_delete_lx' => '领星删除状态',
        'listing_id' => '同步的Listing ID',
        'syn_time' => '同步时间',
        'is_delete' => 'OA删除状态',
        'seller_sku' => 'SKU',
        'Leader' => '组长',
        'Project_Code' => '项目编码'
    ];
    public static function importExcelData($data)
    {
        $errors = [];
        $failed_data = [];
        $successful_data = [];

        // 获取数据库实例
        $dby = dbMysql::getInstance();
        $dbf = dbFMysql::getInstance();

        // 验证数据
        foreach ($data as $index => $row) {
            $level1 = isset($row['一级']) && !empty($row['一级']);
            $level2 = isset($row['二级']) && !empty($row['二级']);
            $projectName = isset($row['项目名称']) && !empty($row['项目名称']);
            $responsible = isset($row['负责人']) && !empty($row['负责人']);

            // 验证规则：
            // 1. 一级和项目名称必须存在，否则记录错误
            if (!$level1 || !$projectName) {
                $errors[$index + 1] = "一级和项目名称是必填项。";
            }

            // 2. 只存在一级的数据且负责人和运营不存在数据，正常
            if ($level1 && !$level2 && !$responsible) {
                $successful_data[] = $row;
                continue;
            }

            // 3. 只存在一级和二级的数据且负责人和运营不存在数据，正常
            if ($level1 && $level2 && !$responsible) {
                $successful_data[] = $row;
                continue;
            }

            // 4. 如果存在项目名称，但没有一级或二级，记录错误
            if ($projectName && (!$level1 || !$level2)) {
                $errors[$index + 1] = "项目名称存在时，一级和二级必须同时存在。";
            }

            // 5. 其它情况下，记录缺失的数据
            if (!$level1 && $level2) {
                $errors[$index + 1] = "缺少一级数据。";
            }

            if ($level1 && $level2 && !$projectName) {
                $errors[$index + 1] = "缺少项目名称。";
            }

            // 6. 检查负责人是否存在于用户表中
            if ($responsible) {
                $user = $dby->table('qwuser')
                    ->where('where wname = :wname', ['wname' => $row['负责人']])
                    ->one();
                if (!$user) {
                    $errors[$index + 1] = "没有相关用户: " . $responsible;
                }
            }

            // 7. 检查三级项目是否有重复名字
            if ($projectName) {
                $pname = $dbf->table('project')
                    ->where('where project_name = :project_name AND level = :level', ['project_name' => $row['项目名称'], 'level' => 3])
                    ->one();

                if ($pname) {
                    $errors[$index + 1] = "已有同名项目: " . $row['项目名称'];
                }
            }

            // 记录成功的数据
            if (!isset($errors[$index + 1])) {
                $successful_data[] = $row;
            }
        }

        // 处理数据并生成错误记录
        foreach ($data as $index => $row) {
            if (isset($errors[$index + 1])) {
                $failed_data[] = [
                    '行号' => $index + 1,
                    '失败原因' => $errors[$index + 1]
                ];
            }
        }

        // 如果有导入失败的数据，生成失败记录的 Excel 文件
        if (!empty($failed_data)) {
            // 使用项目根目录动态构建失败记录文件路径
            $projectRoot = dirname(__DIR__, 2); // 假设控制器位于 'project_root/financial/controller' 目录下
            $failed_file_path = $projectRoot . '/public_financial/temp/project/error_data/failed_imports.xlsx';
            $failed_file_pathqd = '/public_financial/temp/project/error_data/failed_imports.xlsx';
            $exportDir = dirname($failed_file_path);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            (new FastExcel($failed_data))->export($failed_file_path);

            // 限制返回的失败数据记录条数为10条
            $failed_data_for_display = array_slice($failed_data, 0, 10);
            $res = [
                'failed_data' => $failed_data_for_display,
                'failed_file_download_path' => $failed_file_pathqd,
                'successful_data' => $successful_data
            ];
        } else {
            $res = [
                'failed_data' => [],
                'failed_file_download_path' => null,
                'successful_data' => $successful_data
            ];
        }

        return $res;
    }
    public static function exportproject($list)
    {
        //表头
        $new_data = [];
        //数据
        $xuhao = 1;
        foreach ($list as $v) {
            $item['序号'] = $xuhao;
            foreach ($v as $k => $v1) {
                if (!isset(self::$export_project_key[$k])) {
                    continue;
                }
                $value = $v1;
                //时间转化
                if ($k == 'status') {
                    if ($v1 == 0) {
                        $value = '禁用';
                    } else {
                        $value = '启用';
                    }
                }
                $item[self::$export_project_key[$k]] = $value;
            }
            $new_data[] = $item;
            $xuhao++;
        }
        //保存
        $save_path = "/public_financial/temp/role/user";
        $url = SELF_FK . $save_path;
        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path . "/" . date('YmdHis') . uniqid() . '.xlsx';
        $url = SELF_FK . $path;
        if (!file_exists($url)) {
            touch($url);
        }
        (new FastExcel($new_data))->export($url);
        return $path;
    }
    //清除组长和组员关系
    public static function cleanLeader($ids) {
        $db = dbFMysql::getInstance();
        $db->table('project')
            ->where('is_delete = 0')
            ->whereIn('id',$ids)
            ->update(['Leader'=>'{}']);
    }
    //获取项目组组长和组员
}
