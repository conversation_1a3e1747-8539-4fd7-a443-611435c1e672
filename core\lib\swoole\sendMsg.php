<?php
/**
 * @author: zhangguoming
 * @Time: 2024/1/9 11:54
 */

use Swoole\Coroutine;
use Swoole\Coroutine\Http\Client;
use function Swoole\Coroutine\run;

run(function () {
    $client = new Client('127.0.0.1', 9502);
    $id = urldecode($_SERVER['argv'][1]);
    $msg = urldecode($_SERVER['argv'][2]);
    $ret = $client->upgrade('/');
    if ($ret) {
        $data = ['id'=>$id,'msg'=>$msg];
        $client->push(json_encode($data));
    }
});