<?xml version="1.0" encoding="UTF-8"?>
<project>
    <meta>
        <id>fba_inbound_shipment_detail</id>
        <name>FBA货件明细功能</name>
        <description>实现FBA货件明细的完整管理功能，包括数据同步、业务处理和Web管理界面</description>
        <status>completed</status>
        <priority>high</priority>
        <creation_date>2025-07-01</creation_date>
        <completion_date>2025-07-01</completion_date>
    </meta>

    <requirements>
        <business_goal>建立FBA货件明细的完整管理系统，支持数据同步、查询筛选、编辑和导入导出</business_goal>
        <technical_requirements>
            <requirement>从领星API拉取FBA货件数据</requirement>
            <requirement>两阶段数据存储：ERP原始数据 + Logistics业务数据</requirement>
            <requirement>24个字段的详细列表展示</requirement>
            <requirement>10个筛选条件支持</requirement>
            <requirement>5个可编辑字段</requirement>
            <requirement>导入导出功能（仅修改模式）</requirement>
        </technical_requirements>
        <data_requirements>
            <source_api>/erp/sc/routing/storage/shipment/getInboundShipmentList</source_api>
            <source_database>dbErpMysql.lingxing_fba_inbound_shipment</source_database>
            <target_database>dbLMysql.fba_inbound_shipment_detail</target_database>
        </data_requirements>
    </requirements>

    <technical_architecture>
        <database_design>
            <erp_table>
                <name>lingxing_fba_inbound_shipment</name>
                <purpose>存储领星API原始数据，只读不改</purpose>
                <key_fields>shipment_sn, shipment_data, relate_list, logistics_data</key_fields>
            </erp_table>
            <logistics_table>
                <name>fba_inbound_shipment_detail</name>
                <purpose>存储业务数据，支持编辑</purpose>
                <key_fields>24个展示字段 + 筛选字段 + 可编辑字段</key_fields>
            </logistics_table>
        </database_design>

        <controller_architecture>
            <api_sync>lingXingApiController::synFbaInboundShipment</api_sync>
            <data_transform>logisticsController::transformFbaInboundShipmentData</data_transform>
            <business_management>fbaInboundShipmentController</business_management>
        </controller_architecture>

        <model_architecture>
            <raw_model>fbaInboundShipmentRawModel - 处理ERP原始数据</raw_model>
            <detail_model>fbaInboundShipmentDetailModel - 处理Logistics业务数据</detail_model>
        </model_architecture>

        <data_flow>
            <stage1>领星API → lingXingApiController → ERP数据库</stage1>
            <stage2>ERP数据库 → logisticsController → Logistics数据库</stage2>
            <stage3>Logistics数据库 → fbaInboundShipmentController → Web界面</stage3>
        </data_flow>
    </technical_architecture>

    <implementation_plan>
        <phase name="database" status="completed">
            <task name="create_erp_table" status="completed">
                <description>创建ERP数据库原始表</description>
                <file>plugins/logistics/sql/lingxing_fba_inbound_shipment.sql</file>
            </task>
            <task name="create_logistics_table" status="completed">
                <description>创建Logistics数据库业务表</description>
                <file>plugins/logistics/sql/fba_inbound_shipment_detail.sql</file>
            </task>
        </phase>

        <phase name="models" status="completed">
            <task name="raw_model" status="completed">
                <description>实现ERP原始数据模型</description>
                <file>plugins/logistics/models/fbaInboundShipmentRawModel.php</file>
            </task>
            <task name="detail_model" status="completed">
                <description>实现Logistics业务数据模型</description>
                <file>plugins/logistics/models/fbaInboundShipmentDetailModel.php</file>
            </task>
        </phase>

        <phase name="controllers" status="completed">
            <task name="api_controller" status="completed">
                <description>实现领星API同步方法</description>
                <file>task/controller/lingXingApiController.php</file>
                <method>synFbaInboundShipment</method>
            </task>
            <task name="logistics_controller" status="completed">
                <description>实现数据转换方法</description>
                <file>task/controller/logisticsController.php</file>
                <method>transformFbaInboundShipmentData</method>
            </task>
            <task name="business_controller" status="completed">
                <description>实现业务管理控制器</description>
                <file>plugins/logistics/controller/fbaInboundShipmentController.php</file>
                <methods>getList, update, export, import</methods>
            </task>
        </phase>

        <phase name="automation" status="completed">
            <task name="shell_script" status="completed">
                <description>创建两阶段数据同步脚本</description>
                <file>task/shell/lingxing_fba_inbound_shipment.sh</file>
            </task>
        </phase>

        <phase name="testing" status="completed">
            <task name="unit_tests" status="completed">
                <description>编写完整的单元测试</description>
                <file>plugins/logistics/tests/fbaInboundShipmentTest.php</file>
            </task>
        </phase>
    </implementation_plan>

    <features_completed>
        <data_sync>
            <feature>领星API数据同步到ERP数据库</feature>
            <feature>ERP数据库到Logistics数据库的数据转换</feature>
            <feature>基于shipment_sn的插入/更新判断逻辑</feature>
            <feature>可编辑字段保护机制</feature>
        </data_sync>

        <business_management>
            <feature>24个字段的完整列表展示</feature>
            <feature>10个筛选条件支持</feature>
            <feature>5个可编辑字段的更新功能</feature>
            <feature>分页查询支持</feature>
        </business_management>

        <import_export>
            <feature>Excel/CSV格式数据导出</feature>
            <feature>仅修改模式的数据导入</feature>
            <feature>基于货件编码的数据匹配</feature>
            <feature>导入数据验证和错误处理</feature>
        </import_export>

        <automation>
            <feature>两阶段自动化数据同步</feature>
            <feature>企微通知集成</feature>
            <feature>详细的处理日志记录</feature>
            <feature>错误处理和恢复机制</feature>
        </automation>
    </features_completed>

    <field_mappings>
        <display_fields count="24">
            <field source="wname" target="warehouse_name">发货仓库</field>
            <field source="create_time" target="plan_date">计划日期</field>
            <field source="create_user" target="responsible_person">负责人</field>
            <field source="sname" target="site">站点</field>
            <field source="method_name" target="transport_method">运输方式</field>
            <field source="product_name" target="product_name">品名</field>
            <field source="asin" target="asin">ASIN</field>
            <field source="fnsku" target="fnsku">FNSKU</field>
            <field source="num" target="plan_quantity">计划数量</field>
            <field source="shipment_id" target="shipment_code">货件编码</field>
            <field source="logistics.tracking_no" target="tracking_number">跟踪单号</field>
            <field source="shipment_time" target="ship_date">发货日期</field>
            <field source="shipment_status" target="shipment_status">货件状态</field>
            <field source="quantity_shipped" target="received_quantity">签收数量</field>
            <field source="diff_num" target="difference">差异</field>
        </display_fields>

        <editable_fields count="5">
            <field name="tracking_number">跟踪单号</field>
            <field name="transparent_label">透明标签</field>
            <field name="is_label_changed">是否换标</field>
            <field name="remark">备注</field>
            <field name="remark2">备注2</field>
        </editable_fields>

        <filter_fields count="10">
            <field name="country">国家</field>
            <field name="plan_date_start">计划开始时间</field>
            <field name="plan_date_end">计划结束时间</field>
            <field name="warehouse_name">仓库</field>
            <field name="transport_method">运输方式</field>
            <field name="product_name">产品名称</field>
            <field name="shop_code">店铺代码</field>
            <field name="fnsku">FNSKU</field>
            <field name="warehouse_code">入仓编号</field>
            <field name="remark">备注</field>
        </filter_fields>
    </field_mappings>

    <deployment_info>
        <database_scripts>
            <script>plugins/logistics/sql/lingxing_fba_inbound_shipment.sql</script>
            <script>plugins/logistics/sql/fba_inbound_shipment_detail.sql</script>
        </database_scripts>

        <shell_script>
            <file>task/shell/lingxing_fba_inbound_shipment.sh</file>
            <schedule>建议每小时执行一次</schedule>
        </shell_script>

        <api_endpoints>
            <endpoint>POST /task/lingXingApi/synFbaInboundShipment</endpoint>
            <endpoint>POST /task/logistics/transformFbaInboundShipmentData</endpoint>
            <endpoint>POST /plugins/logistics/controller/fbaInboundShipmentController/getList</endpoint>
            <endpoint>POST /plugins/logistics/controller/fbaInboundShipmentController/update</endpoint>
            <endpoint>POST /plugins/logistics/controller/fbaInboundShipmentController/export</endpoint>
            <endpoint>POST /plugins/logistics/controller/fbaInboundShipmentController/import</endpoint>
        </api_endpoints>
    </deployment_info>

    <context_recovery>
        <key_concepts>
            <concept>两阶段数据架构：原始数据保护 + 业务数据灵活</concept>
            <concept>基于shipment_sn的数据同步和更新判断</concept>
            <concept>可编辑字段保护：更新时不覆盖用户编辑内容</concept>
            <concept>分离的控制器职责：API同步、数据转换、业务管理</concept>
        </key_concepts>

        <understanding_checkpoints>
            <checkpoint>理解领星API数据结构和字段映射</checkpoint>
            <checkpoint>掌握两阶段数据流和处理逻辑</checkpoint>
            <checkpoint>了解可编辑字段的保护机制</checkpoint>
            <checkpoint>熟悉导入导出的业务规则</checkpoint>
        </understanding_checkpoints>
    </context_recovery>

    <project_completion>
        <completion_summary>
            FBA货件明细功能已完全开发完成，包括：
            - 完整的数据同步架构（领星API → ERP → Logistics）
            - 24个字段的详细列表展示
            - 10个筛选条件支持
            - 5个可编辑字段的更新功能
            - 导入导出功能（仅修改模式）
            - 自动化Shell脚本和企微通知
            - 完整的单元测试覆盖
        </completion_summary>

        <next_steps>
            <step>生产环境数据库表创建</step>
            <step>Shell脚本部署和定时任务配置</step>
            <step>Web界面集成和用户培训</step>
        </next_steps>
    </project_completion>
</project>
