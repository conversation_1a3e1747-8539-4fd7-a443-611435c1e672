<?php
/**
 * @author: zhangguoming
 * @Time: 2024/2/26 17:21
 */

namespace plugins\logistics\form;

use core\lib\db\dbLMysql;
use core\lib\predisV;
use plugins\logistics\models\userModel;

class messagesFrom
{
    /**
     * @param array $w_userids
     * @param string $text
     * @return void
     * @throws \core\lib\ExceptionError
     */
    public static function senMeg(array $w_userids, int $msg_type, string $text, int $model_id, $remarks = '', $title = '') {
        $w_userids = array_unique($w_userids);
        if (empty($text)) {
            SetReturn(-1,'消息不能为空');
        }

        $ldb = dbLMysql::getInstance();

        $insert_data = [
            'user_id'=>userModel::$qwuser_id??0,
            'title'=>$title ?: self::getQwMsgTitle($msg_type),
            'text'=>$text,
            'type'=>$msg_type,
            'model_id'=>$model_id,
            'remarks'=>$remarks
        ];
        $msg_data = [
            'system_type'=>7,//绩效系统
            'type'=>'textcard',
            'msg'=>$text,
            'title'=>$title ?: self::getQwMsgTitle($msg_type),
        ];
        new predisV();
        $ldb->table('messages');

        $w_userids = ['WeiXueYun'];

        foreach ($w_userids as $user_id) {
            $insert_data['qw_userid'] = $user_id; // 接收人
            $msg_id = $ldb->insert($insert_data);
            $msg_data['qw_userid'] = $user_id;
            $msg_data['data'] = json_encode(['id'=>$msg_id]);
            predisV::redisQueue($msg_data);
        }
        //websocket推送消息
        $swool_data = [
            'qw_userid'=>$w_userids,
            'msg'=>$text
        ];
        //webSocket消息聊天通知
        \core\lib\swoole\webSocketSwoole::sendMsg(json_encode($swool_data, JSON_UNESCAPED_UNICODE));
    }
    public static function getQwMsgTitle($type) {
        $title = '';
        switch ($type) {
            case 1;
                $title = "考勤确认";
                break;
        }
        return $title;
    }




















}