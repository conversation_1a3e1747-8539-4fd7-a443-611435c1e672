<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/24 18:52
 */

namespace  plugins\goods\form;

use plugins\goods\models\userModel;
use core\lib\db\dbMysql;

class goodsHardwareApprovalFrom
{
    /**
     * @param $project
     * @param $param
     * @param $wids
     * @return void
     * @throws \core\lib\ExceptionError  事件审核未通过给负责人发起审核
     */
    public static function hardeareTestNotPass($project,$param,$wids_,$node_index){
        $db = dbMysql::getInstance();
        $hardware_approval = $db->table('goods_hardware_approval')
            ->where('where project_id=:project_id and node_index=:node_index',['project_id'=>$project['id'],'node_index'=>$node_index])
            ->one();
        if ($hardware_approval) {
            SetReturn(-1,'审批已生成，切勿重复提交');
        } else {
            //硬件检测审批人
            $hardware_approval_user = configFrom::getConfigByName('hardware_approval_user');
            if (!$hardware_approval_user) {
                SetReturn(-1,'审批人未配置，请联系管理员');
            }
            //生成审批数据
            $hardware_approval_user = json_decode($hardware_approval_user,true);
            $db->table('goods_hardware_approval')
                ->insert([
                    'user_id'=>userModel::$qwuser_id,
                    'qwuser_id'=>$hardware_approval_user[0]['id'],
                    'goods_id'=>$project['goods_id'],
                    'project_id'=>$project['id'],
                    'reason_ids'=>$param['reason_ids'],
                    'remarks'=>$param['remarks'],
                    'created_time'=>date('Y-m-d H:i:s'),
                    'node_index'=>$node_index
                ]);
            $hardware_check_not_pass = configFrom::getConfigByName('hardware_check_not_pass');
            //生成审批待办数据+发送审批消息
            goodsMattersFrom::addApprovalMatter($project,$hardware_approval_user[0]['id'],'产品硬件检测',$param['node_index']);
            $other_data = [
                'user_id'=>userModel::$qwuser_id,
                'model_id'=>$project['id'],
                'node_index'=>$param['node_index'],
                'msg_type'=>8
            ];
            $wids = [$hardware_approval_user[0]['wid']];
            $msg = messagesFrom::getMsgTxt(10,$project['matter_name'],'产品硬件检测','');
            //通知：审核失败信息到对应人员
            $other_data1 = [
                'user_id'=>userModel::$qwuser_id,
                'model_id'=>$project['id'],
                'node_index'=>$param['node_index'],
                'msg_type'=>7
            ];
            $wids1 = array_column(json_decode($hardware_check_not_pass,true),'wid');
            $msg1 = messagesFrom::getMsgTxt(7,$project['matter_name'],'产品硬件检测','产品硬件检测');
            $wids1 = array_unique(array_merge($wids_,$wids1));
            messagesFrom::senMeg($wids,$msg,$other_data,$param['remarks']);
            messagesFrom::senMeg($wids1,$msg1,$other_data1,$param['remarks']);
        }

    }

    //获取异常特批审核人
    public static function getHardwareApproval($is_pass,$project_id){
        $key = $is_pass?'hardware_approval_pass':'hardware_approval_not_pass';
        $default_user = configFrom::getConfigByName($key);
        $wids = array_column(json_decode($default_user,true),'wid');
        if ($is_pass) {
            //测试人员
            $db = dbMysql::getInstance();
            $hardware_test = $db->table('goods_hardware_test','a')
                ->leftJoin('qwuser','b','b.id=a.qwuser_id')
                ->where('where a.project_id=:project_id',['project_id'=>$project_id])
                ->field('b.wid')
                ->list();
            $wids = array_merge($wids,array_column($hardware_test,'wid'));
        }
        return $wids;
    }
}