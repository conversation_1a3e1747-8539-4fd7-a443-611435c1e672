<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/30 10:14
 */

namespace  plugins\goods\controller;

use plugins\goods\form\downLoadFrom;
use plugins\goods\form\goodsCateFrom;
use plugins\goods\form\imgsRequestCollectionForm;
use plugins\goods\form\imgsRequestFrom;
use core\lib\config;
use core\lib\db\dbMysql;
use core\lib\log;
use Rap2hpoutre\FastExcel\FastExcel;

class imgsRequestCollectionController
{
    public function getList() {
        $paras_list = array(
            'page','page_size',
            'goods_name',
            'request_name',
            'goods_ids',//产品查询
            'type',//需求类型
            'second_types',//废弃
            'platform_id',
            'user_ids',//申请人id
            'file_types',//文件类型
            'order_by',// 排序，
            'language_ids',
            'color_ids',
            'user_ids',
            'cate_ids'
            );
        $param = arrangeParam($_POST, $paras_list);
        $list = imgsRequestCollectionForm::getList($param);
        returnSuccess($list);
    }

    //单个下载
    public function downLoad() {
        $id = $_POST['id'];
        if (!$id) {
            SetReturn(-1,'参数有误');
        }
        $db = dbMysql::getInstance();
        $img = $db->table('imgs_request_collection')
            ->field('id,url,goods_id')
            ->where('where id=:id',['id'=>$id])
            ->one();
        if (!$img) {
            SetReturn(-1,'未找到图片');
        }
        $base64Data = downLoadFrom::getdownLoadBase64Encode($img['url'],$img['goods_id'],1);
        log::downLoadLog($id, 5);
        returnSuccess(['data'=>$base64Data]);
    }

    //导入共享盘文件
    public function importShareField() {
        $excel_url = $_GET['excel_url']??'';
        if (empty($excel_url)) {
            returnError('请上传导入链接');
        }
        try {
            $excelData = (new FastExcel)->import($excel_url);
            $data = $excelData->toArray();
            if (empty($data)) {
                returnError('Excel文件内容为空');
            }
            //写入数据
            $res = imgsRequestCollectionForm::importShareField($data);
            returnSuccess($res);
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }
    }

}