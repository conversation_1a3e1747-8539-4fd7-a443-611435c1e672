<?php
namespace plugins\logistics\controller;

use core\lib\ExceptionError;
use plugins\logistics\form\PrepareAuditForm;

class PrepareAuditController
{
    /**
     * 提交备货审核
     */
    public function submit()
    {
        $paras_list = array('title', 'content', 'amount');
        $param = arrangeParam($_POST, $paras_list);
        
        try {
            PrepareAuditForm::submitAudit($param);
            returnSuccess('', '提交成功');
        } catch (ExceptionError $e) {
            returnError($e->getMessage());
        }
    }
    
    /**
     * 获取备货审核列表
     */
    public function getList()
    {
        $paras_list = array('page', 'page_size', 'status', 'creator_id', 'department_id', 'start_date', 'end_date');
        $param = arrangeParam($_GET, $paras_list);
        
        try {
            $result = PrepareAuditForm::getAuditList($param);
            returnSuccess($result, '获取成功');
        } catch (ExceptionError $e) {
            returnError($e->getMessage());
        }
    }
    
    /**
     * 获取备货审核详情
     */
    public function getDetail()
    {
        $paras_list = array('id');
        $param = arrangeParam($_GET, $paras_list);
        
        if (empty($param['id'])) {
            returnError('缺少ID参数');
        }
        
        try {
            $result = PrepareAuditForm::getAuditDetail($param['id']);
            returnSuccess($result, '获取成功');
        } catch (ExceptionError $e) {
            returnError($e->getMessage());
        }
    }
    
    /**
     * 处理审批
     */
    public function processAudit()
    {
        $paras_list = array('flow_id', 'is_pass', 'comment');
        $param = arrangeParam($_POST, $paras_list);
        
        try {
            PrepareAuditForm::processAudit($param);
            returnSuccess('', '处理成功');
        } catch (ExceptionError $e) {
            returnError($e->getMessage());
        }
    }
    
    /**
     * 撤销审批
     */
    public function cancelAudit()
    {
        $paras_list = array('instance_id');
        $param = arrangeParam($_POST, $paras_list);
        
        if (empty($param['instance_id'])) {
            returnError('缺少实例ID参数');
        }
        
        try {
            PrepareAuditForm::cancelAudit($param['instance_id']);
            returnSuccess('', '撤销成功');
        } catch (ExceptionError $e) {
            returnError($e->getMessage());
        }
    }
}