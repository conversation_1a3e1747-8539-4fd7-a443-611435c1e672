<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/20 14:44
 */

namespace financial\controller;

use admin\common\publicMethod;
use core\jobs\exportMskuReportDataJobs;
use core\jobs\exportMskuReportImportErrorDataJobs;
use core\jobs\importMskuReportDataJobs;
use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\rediskeys;
use financial\form\checkoutForm;
use financial\form\customColumnForm;
use financial\form\mskuReportForm;
use financial\form\runShellTaskForm;
use financial\models\checkoutModel;
use financial\models\mskuReportModel;
use financial\models\userModel;
use Rap2hpoutre\FastExcel\FastExcel;

class mskuReportController
{
    //获取报告数据列表
    public function getList(){
        $paras_list = array('date_time','sid','project_ids','search_type','search_value','import_time','page_size','page');
        $request_list = ['date_time'=>'时间','project_ids'=>'项目ID'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $form = new mskuReportForm($param['date_time']);
        $data = $form->getList($param);
        $data['export_key_list'] = mskuReportModel::$export_key_list;
        returnSuccess($data);
    }
    //总计查询
    public function getColumnCount() {
        $paras_list = array('date_time','sid','project_ids','search_type','search_value','import_time');
        $request_list = ['date_time'=>'时间','project_ids'=>'项目ID'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $form = new mskuReportForm($param['date_time']);
        $data = $form->getCount($param);
        returnSuccess($data);
    }
    //根据id删除数据
    public function del() {
        $paras_list = array('date_time','sid','project_ids','search_type','search_value','import_time','type','ids');
        $request_list = ['date_time'=>'时间','ids'=>'要操作的列','type'=>'删除方式'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $m_date = date('Y-m',strtotime($param['date_time']));
        //验证数据是否可以删除
        checkoutModel::verifyLock([$m_date]);
        $type = (int)$param['type'];
        if (!in_array($type,[1,2,3])) {
            returnError('删除方式错误');
        }
        $form = new mskuReportForm($m_date);
        //验证其他操作是否都已经完成
        $redis = (new \core\lib\predisV())::$client;
        checkoutForm::verifyQueue($redis,$m_date);
        if ($type == 1) {
            //删除勾选数据
            $ids = json_decode($param['ids']);
            if (!count($ids)) {
                returnError('请勾选要删除的数据');
            }
            $form->delById($ids,$param['date_time']);
        } elseif ($type == 2) {
            //删除查询数据
            mskuReportForm::$list_type = 1;
            $ids = array_column($form->getList($param),'id');
            if (!count($ids)) {
                returnError('未能查询到可删除的数据');
            }
            $form->delById($ids,$param['date_time']);
        } else {
            //再删除当月数据
            $form->delByMdata($param['date_time']);
        }

        returnSuccess('删除成功');
    }
    //导入报表数据
    public function import() {
        ini_set('memory_limit', '500M');
        set_time_limit(60);
        $paras_list = array('excel_src', 'date', 'filename');
        $request_list = ['excel_src'=>'表格链接','date'=>'时间','filename'=>'表格名称'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $excel_url = SELF_FK.$param['excel_src'];
        $excel_name = $param['filename'];
        if (!file_exists($excel_url)) {
            returnError('表格不存在');
        }
        $date = date('Y-m',strtotime($param['date']));
        $year = date('Y',strtotime($date));
        //验证数据是否可以上传
        checkoutModel::verifyLock([$date]);
        $excel_data = (new FastExcel)->import($excel_url);
        $data = $excel_data->toArray();
        $count_num = count($data);
        if (!$count_num) {
            returnError('表格内不存在数据');
        }
        if ($count_num > 20000) {
            returnError('表格单次限制导入数据20000行');
        }
        //数据导入
        $redis = (new \core\lib\predisV())::$client;
        //其他操作验证
        checkoutForm::verifyImportMskuData($redis,$date);
        //表头判断
        mskuReportForm::verifyExcelHead($data[0]);
        //导入记录
        $db = dbFMysql::getInstance();
        mskuReportModel::creatMskuReportTable($year);
        $import_id = $db->table('msku_report_import')
            ->insert([
                'user_id'=>userModel::$qwuser_id,
                'excel_name'=>$excel_name,
                'excel_path'=>$excel_url,
                'total'=>$count_num,
                'success_count'=>0,
                'fail_count'=>0,
                'report_date'=>$date,
                'created_time'=>date('Y-m-d H:i:s'),
            ]);
        $key = rediskeys::$export_msku_import.$date;
        $data = [
            'success_count'=>0, //采购数
            'error_count'=>0,  //失败数
            'total'=>$count_num, //总数
            'excel_src'=>$excel_url, //上传文件地址
            'date'=>$date,
            'import_id'=>$import_id,
            'offset'=>0
        ];
        $redis->set($key,json_encode($data));
        $redis->expire($key,60*60);
        $queue_key = config::get('delay_queue_key', 'app');
        $task = new importMskuReportDataJobs($key); // 创建任务类实例
        $redis->zAdd($queue_key, [], 0, serialize($task));

        returnSuccess(['progress_key'=>$key,'import_id'=>$import_id],'导入中');
    }
    //导出报表数据
    public function exportData() {
        $paras_list = array('date_time','sid','project_ids','search_type','search_value','import_time','type','ids','export_list');
        $request_list = ['date_time'=>'时间','ids'=>'要操作的列','type'=>'导出类型','export_list'=>'要导出的字段'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        if (empty($param['date_time'])) {
            returnError('请选择查询的时间');
        }
        if (empty($param['export_list']) || $param['export_list']=='[]') {
            returnError('请选择要导出的字段');
        }
        $export_list = json_decode($param['export_list']);
        $date_time = $param['date_time'];
        $form = new mskuReportForm($param['date_time']);
        $db = dbFMysql::getInstance();
        $db->table($form->table_name)
            ->where('where is_delete = 0 and reportDateMonth=:date_time and import_id > 0',['date_time'=>$date_time]);
        if (!empty($param['sid'])) {
            $db->andWhere('sid=:sid',['sid'=>$param['sid']]);
        }
        if ($param['type'] == 0) {
            $ids = json_decode($param['ids']);
            if (!count($ids)) {
                returnError('请选择要导出得数据');
            }
            $db->whereIn('id',$ids);
        }
        $count = $db->count();
        if (!$count) {
            returnSuccess('未查询到任何数据');
        }
        $redis = (new \core\lib\predisV())::$client;
        $key = uniqid('export_msku_report');
        $data = [
            'success_count'=>0,
            'total'=>$count,
            'excel_url'=>[],
            'zip_url'=>'',
        ];
        $redis->set($key,json_encode($data));
        $redis->expire($key,60*60);
        $queue_key = config::get('delay_queue_key', 'app');
        unset($param['export_list']);
        $task = new exportMskuReportDataJobs($key,$param,$export_list,1,userModel::$qwuser_id); // 创建任务类实例
        $redis->zAdd($queue_key, [], 0, serialize($task));
//        $task->task();
        returnSuccess(['progress_key'=>$key],'导出中');
    }
    //查看导入记录
    public function importLogList() {
        $paras_list = array('date_time','page_size','page');
        $param = arrangeParam($_POST, $paras_list);
        $db = dbFMysql::getInstance();
        $db->table('msku_report_import','a')
            ->leftJoinOut('db','qwuser','b','b.id = a.user_id');
        if (!empty($param['date_time'])) {
            $begin_time = $param['date_time'].'-01 00:00:00';
            $end_time = date('Y-m-d H:i:s',strtotime($begin_time.'+1 month'));
            $db->where('where a.created_time>=:begin_time and a.created_time<=:end_time',['begin_time'=>$begin_time,'end_time'=>$end_time]);
        }
        $data = $db->order('a.id desc')
            ->field('a.*,b.wname as user_name')
            ->pages($param['page'],$param['page_size']);
        if (count($data['list'])) {
            //获取异常标签
            $data['list'] = mskuReportForm::getLogErrorList($data['list']);
        }
        returnSuccess($data);
    }
    //导出-导入错误历史
    public function exportErrorList() {
        $import_id = $_POST['import_id'];
        if (!$import_id) {
            returnError('缺少必传参数');
        }
        $db = dbFMysql::getInstance();
        $count = $db->table('msku_report_import_error_log')
            ->where('where import_id=:import_id',['import_id'=>$import_id])
            ->count();
        if (!$count) {
            returnError('未查询到该记录有错误数据');
        }
        $redis = (new \core\lib\predisV())::$client;
        $key = uniqid('export_msku_import_error');
        $data = [
            'success_count'=>0,
            'total'=>$count,
            'excel_url'=>[],
            'zip_url'=>'',
        ];
        $redis->set($key,json_encode($data));
        $redis->expire($key,60*60);
        $queue_key = config::get('delay_queue_key', 'app');
        $task = new exportMskuReportImportErrorDataJobs($key,$import_id,1); // 创建任务类实例
        $redis->zAdd($queue_key, [], 0, serialize($task));
        //$task->task();
        returnSuccess(['progress_key'=>$key],'导出中');
    }
    //查看导入异常列表
    public function errorListLog() {
        $paras_list = array('import_id','page','page_size');
        $request_list = ['import_id'=>'导入记录ID'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $import_id = (int)$param['import_id'];
        $db = dbFMysql::getInstance();
        $data = $db->table('msku_report_import_error_log')
            ->where('where import_id=:import_id',['import_id'=>$import_id])
            ->field('row_num,error_reason')
            ->pages($param['page'],$param['page_size']);
        returnSuccess($data);
    }

}