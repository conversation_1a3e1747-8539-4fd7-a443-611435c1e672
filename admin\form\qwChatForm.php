<?php
namespace admin\form;

use core\sdk\qw_app\qwApiRequest;

class qwChatForm
{
    /**
     * 保存或更新企业微信群聊
     *
     * @param \PDO $db 数据库实例
     * @param int $model_type 模块类型（1=产品需求）
     * @param int $model_id 模块ID
     * @param string $owner 群主ID
     * @param string $chat_name 群名称
     * @param array $user_list 新成员列表（数组）
     * @return void
     */
    public static function saveChat($db, int $model_type, int $model_id, string $owner, string $chat_name, array $user_list): void
    {
        // 查询现有群信息
        $qw_chat = $db->table('qw_chat')
            ->where('model_type = :mt AND model_id = :mid', [
                'mt'  => $model_type,
                'mid' => $model_id,
            ])
            ->one();

        // 如果存在且已有 chatid，则编辑群成员
        if ($qw_chat && !empty($qw_chat['chatid'])) {
            // 原成员列表解码为数组
            $user_list_old = json_decode($qw_chat['user_list'], true) ?: [];

            // 计算新增和删除成员
            $add_user = array_diff($user_list, $user_list_old);
            $del_user = array_diff($user_list_old, $user_list);

            if ($add_user || $del_user) {
                $res = qwApiRequest::editAppchat(
                    $qw_chat['chatid'],
                    $chat_name,
                    $owner,
                    array_values($add_user),
                    array_values($del_user)
                );
                $failed_reason = qwApiRequest::$error_msg;
                $status = $res ? 3 : 4; // 3=编辑成功,4=编辑失败

                // 更新数据库
                $db->table('qw_chat')
                    ->where('id = :id', ['id' => $qw_chat['id']])
                    ->update([
                        'status'         => $status,
                        'failed_reason'  => $failed_reason,
                        'user_list'      => json_encode($user_list),
                        'chat_name'      => $chat_name,
                        'owner'          => $owner,
                    ]);
            }
        } else {
            // 不存在或未创建，创建新群
            $chatid = qwApiRequest::createAppchat($chat_name, $owner, $user_list);
            $failed_reason = qwApiRequest::$error_msg;
            $status = $chatid ? 1 : 0; // 1=创建成功,0=创建失败

            if ($qw_chat) {
                // 更新已有记录
                $db->table('qw_chat')
                    ->where('id = :id', ['id' => $qw_chat['id']])
                    ->update([
                        'status'         => $status,
                        'chat_name'      => $chat_name,
                        'owner'          => $owner,
                        'chatid'         => $chatid,
                        'failed_reason'  => $failed_reason,
                        'user_list'      => json_encode($user_list),
                    ]);
            } else {
                // 插入新记录
                $db->table('qw_chat')
                    ->insert([
                        'model_type'     => $model_type,
                        'model_id'       => $model_id,
                        'status'         => $status,
                        'chat_name'      => $chat_name,
                        'owner'          => $owner,
                        'chatid'         => $chatid,
                        'failed_reason'  => $failed_reason,
                        'user_list'      => json_encode($user_list),
                    ]);
            }
        }
    }
}
