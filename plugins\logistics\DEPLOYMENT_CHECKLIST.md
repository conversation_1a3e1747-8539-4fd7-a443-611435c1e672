# 海外仓备货单SKU拆分项目部署检查清单

## 📋 部署前检查

### 数据库准备
- [ ] **创建明细表**
  ```sql
  -- 执行建表语句
  source plugins/logistics/sql/overseas_inbound_detail.sql
  ```
- [ ] **验证表结构**
  ```sql
  DESC overseas_inbound_detail;
  ```
- [ ] **检查索引**
  ```sql
  SHOW INDEX FROM overseas_inbound_detail;
  ```

### 代码文件检查
- [ ] **模型文件** - `plugins/logistics/models/overseasInboundDetailModel.php`
- [ ] **控制器方法** - `task/controller/logisticsController.php::processOverseasInboundSplit`
- [ ] **Shell脚本** - `task/shell/lingxing_overseas_inbound.sh`
- [ ] **测试文件** - `plugins/logistics/tests/`

### 权限检查
- [ ] **文件权限** - 确保PHP可以读取所有相关文件
- [ ] **数据库权限** - 确保数据库用户有INSERT、UPDATE、SELECT权限
- [ ] **日志权限** - 确保可以写入错误日志

## 🧪 功能测试

### 单元测试
```bash
# 运行所有测试
php plugins/logistics/tests/runAllTests.php

# 单独运行模型测试
php plugins/logistics/tests/overseasInboundDetailTest.php

# 单独运行集成测试
php plugins/logistics/tests/integrationTest.php
```

### 接口测试
```bash
# 测试SKU拆分接口
curl -X POST -d "token=01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0&batch_size=10" \
'http://oa.ywx.com/task/logistics/processOverseasInboundSplit'

# 测试导出接口
curl -X POST -d "token=01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0&sync_date=2025-07-01" \
'http://oa.ywx.com/task/logistics/exportOverseasInboundDetail'
```

### 数据验证
- [ ] **拆分数据完整性** - 检查拆分后的SKU数量是否正确
- [ ] **字段映射准确性** - 验证关键字段映射是否正确
- [ ] **数据类型正确性** - 确保数值字段类型正确

## 🚀 部署步骤

### 1. 数据库部署
```sql
-- 1. 备份现有数据（如果有）
-- 2. 创建新表
CREATE TABLE `overseas_inbound_detail` (...);

-- 3. 验证表创建成功
SELECT COUNT(*) FROM overseas_inbound_detail;
```

### 2. 代码部署
```bash
# 1. 备份现有文件
cp task/controller/logisticsController.php task/controller/logisticsController.php.bak
cp task/shell/lingxing_overseas_inbound.sh task/shell/lingxing_overseas_inbound.sh.bak

# 2. 部署新代码（已完成）

# 3. 验证文件完整性
ls -la plugins/logistics/models/overseasInboundDetailModel.php
ls -la plugins/logistics/tests/
```

### 3. 定时任务配置
```bash
# 检查现有定时任务
crontab -l

# 海外仓备货单同步任务应该已经存在
# 新的SKU拆分逻辑已集成到现有Shell脚本中
```

## 📊 监控配置

### 日志监控
- [ ] **错误日志** - 监控PHP错误日志中的拆分相关错误
- [ ] **处理日志** - 监控拆分处理的进度和结果
- [ ] **性能日志** - 监控处理时间和内存使用

### 数据监控
```sql
-- 每日数据量监控
SELECT sync_date, COUNT(*) as detail_count, 
       COUNT(DISTINCT overseas_order_no) as order_count,
       COUNT(DISTINCT sku) as sku_count
FROM overseas_inbound_detail 
WHERE sync_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
GROUP BY sync_date
ORDER BY sync_date DESC;

-- 错误数据监控
SELECT COUNT(*) as error_count
FROM overseas_inbound_detail 
WHERE (overseas_order_no = '' OR sku = '' OR quantity <= 0)
AND sync_date = CURDATE();
```

### 性能监控
- [ ] **处理时间** - 监控每次拆分处理的耗时
- [ ] **内存使用** - 监控PHP进程内存使用情况
- [ ] **数据库性能** - 监控相关SQL查询性能

## ⚠️ 风险控制

### 数据安全
- [ ] **备份策略** - 确保有完整的数据备份
- [ ] **回滚方案** - 准备快速回滚方案
- [ ] **权限控制** - 限制对敏感数据的访问

### 异常处理
- [ ] **网络异常** - 处理网络连接失败的情况
- [ ] **数据异常** - 处理无效JSON或缺失字段的情况
- [ ] **系统异常** - 处理内存不足或超时的情况

### 监控告警
- [ ] **处理失败告警** - 当拆分处理失败时发送告警
- [ ] **数据异常告警** - 当发现异常数据时发送告警
- [ ] **性能告警** - 当处理时间过长时发送告警

## 📈 性能优化

### 数据库优化
```sql
-- 检查索引使用情况
EXPLAIN SELECT * FROM overseas_inbound_detail 
WHERE overseas_order_no = 'TEST001' AND sku = 'TEST_SKU';

-- 检查表统计信息
ANALYZE TABLE overseas_inbound_detail;
```

### 代码优化
- [ ] **批量大小调优** - 根据实际情况调整批量处理大小
- [ ] **内存管理** - 确保及时释放不需要的变量
- [ ] **查询优化** - 优化数据库查询语句

## ✅ 部署验证

### 功能验证
- [ ] **自动拆分** - 验证定时任务能正常执行SKU拆分
- [ ] **数据准确性** - 抽查拆分结果的准确性
- [ ] **导入导出** - 验证导入导出功能正常工作

### 性能验证
- [ ] **处理速度** - 验证处理速度满足要求
- [ ] **系统稳定性** - 验证长时间运行的稳定性
- [ ] **资源使用** - 验证CPU和内存使用在合理范围

### 业务验证
- [ ] **数据完整性** - 业务人员验证数据完整性
- [ ] **功能可用性** - 业务人员验证功能可用性
- [ ] **报表准确性** - 验证基于新数据的报表准确性

## 📞 应急联系

### 技术支持
- **开发人员**: [联系方式]
- **运维人员**: [联系方式]
- **数据库管理员**: [联系方式]

### 回滚步骤
1. **停止定时任务**
2. **恢复原始Shell脚本**
3. **清理异常数据**
4. **恢复数据库备份**（如需要）

## 📝 部署记录

- **部署日期**: ___________
- **部署人员**: ___________
- **测试结果**: ___________
- **验收人员**: ___________
- **备注**: ___________

---

**注意**: 请在生产环境部署前完成所有检查项目，确保系统稳定可靠。
