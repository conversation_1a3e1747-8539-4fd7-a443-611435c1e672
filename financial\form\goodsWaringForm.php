<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/31 9:48
 */

namespace financial\form;

use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use financial\models\goodsCategoryModel;
use financial\models\goodsWaringModel;
use financial\models\mskuReportModel;
use financial\models\userModel;
use Rap2hpoutre\FastExcel\FastExcel;

//用于产品预警的的更新
class goodsWaringForm
{
    public static array $export_list = [
            'asin' => 'ASIN',
            'country'=>'国家',
            'p_asin' => '父ASIN',
            'sku' => 'SKU',
            'product_name' => '品名',
            'category_name'=> '产品分类',
            'yunying' => '运营人员',
            'waring_name'=>'预警名称',
            'created_time'=>'预警时间',
            'status'=>'预警状态',
            'rules'=>'预警监控条件',
            'reason_txt'=>'预警值',
        ];
    //预警处理的权限验证
    public static function verifySolveAuth($goods_waring_id,$is_check = 0) {
        $db = dbFMysql::getInstance();
        $db->table('goods_waring_check')
            ->where('where id=:id',['id'=>$goods_waring_id])
            ->field('id,weidu_key,user_id,status,waring_name,column_name,dimension,rules,reason_txt,receive_type,yunying_ids,zuzhang_ids');
        $goods_waring = $db->one();
        $goods_waring['zuzhang_ids'] = $zuzhang_ids = json_decode($goods_waring['zuzhang_ids']);
        $goods_waring['yunying_ids'] = $yunying_ids = json_decode($goods_waring['yunying_ids']);
        $goods_waring['receive_type'] = json_decode($goods_waring['receive_type']);
        $goods_waring['rules'] = json_decode($goods_waring['rules']);
        $goods_waring['reason_txt'] = json_decode($goods_waring['reason_txt']);
        if ($is_check == 0) {
            if (!userModel::$is_super) {
                $user_ids = array_merge($zuzhang_ids,$yunying_ids);
                if (in_array(userModel::$qwuser_id,$user_ids)) {
                    returnError('非运营或项目负责人无权处理预警');
                }
            }
        } else {
            if (!userModel::$is_super) {
                $user_ids = $zuzhang_ids;
                if (in_array(userModel::$qwuser_id,$user_ids)) {
                    returnError('非项目负责人无权审核');
                }
            }
        }
        return $goods_waring;
    }
    //预警处理后发送消息给处理人
    public static function sendMsgToChecker($waring_name,$asin,array $checker_ids,$goods_waring_id) {
        $db = dbMysql::getInstance();
        $user_ = $db->table('qwuser')
            ->whereIn('id',$checker_ids)
            ->field('wid')
            ->one();
        if ($user_) {
            messagesFrom::senMeg([$user_['wid']],10,"预警".$waring_name."的品【asin:{$asin}】预警已处理，请审核",$goods_waring_id);
        }
    }
    //预警审核验证
    public static function verifyCheckAuth($goods_waring_id) {
        $db = dbFMysql::getInstance();
        $goods_waring = $db->table('goods_waring')
            ->where('where is_delete = 0 and id=waring_id',['waring_id'=>$goods_waring_id])
            ->field('id,status,waring_name,column_name,sku,dimension,rules,reason_txt,yunying_id,receive_type,zuzhang_id')
            ->one();
        $yunying_ids = json_decode($goods_waring['yunying_ids']);
        $zuzhang_ids = json_decode($goods_waring['zuzhang_id']);
        $receive_type = json_decode($goods_waring['receive_type']);
        if (count($receive_type) == 2) {
            if (!in_array(userModel::$qwuser_id,$yunying_ids) && !in_array(userModel::$qwuser_id,$zuzhang_ids)) {
                returnError('非负责人不可查看/处理商品预警');
            }
        } else {
            if ($receive_type[0] == 1) {
                if (!in_array(userModel::$qwuser_id,$yunying_ids)) {
                    returnError('非负责人不可查看/处理商品预警');
                }
            } else {
                if (!in_array(userModel::$qwuser_id,$zuzhang_ids)) {
                    returnError('非负责人不可查看/处理商品预警');
                }
            }
        }
        return $goods_waring;
    }
    //导出报表
    public static function exportTableData($param) {
        $export_keys = json_decode($param['export_list']);
        if (!count($export_keys)) {
            returnError('请选择要导出的字段');
        }
        //数据获取
        $form = new waringTableForm('waring_goods');
        $form::$list_type = 2;
        $data = $form::waringGoodsTotal();
        if (!count($data)) {
            returnError('无数据可导出');
        }
        //需要导出的字段
        $export_keys = array_intersect_key(self::$export_list,array_flip($export_keys));
        $new_data = [];
        foreach ($data as $v) {
            $item = [];
            foreach ($export_keys as $key_=>$key_name) {
                $val = $v[$key_];
                if (in_array($key_,['p_asin','sku','product_name','category_name'])) {
                    $val = implode(',',$val);
                }
                if ($key_ == 'yunying') {
                    $yunyings = array_column($val,'wname');
                    $val = implode(',',$yunyings);
                }
                if ($key_ == 'status') {
                    switch ($val) {
                        case 1:
                            $val = '待处理';break;
                        case 2:
                            $val = '待审核';break;
                        case 3:
                            $val = '审核失败';break;
                        case 4:
                            $val = '审核通过';break;
                        default:
                            $val = '未知状态';
                    }

                }
                $item[$key_name] = $val;
            }
            $new_data[] = $item;
        }
        $chunkedArray = array_chunk($new_data, 10000);
        // 导出文件路径
        $save_path = '/public_financial/temp/export/table/waring';
        $url = SELF_FK . $save_path;
        if (!is_dir($url)) {
            if (!mkdir($url, 0777, true)) {
                returnError('Unable to create directory：' . $url);
            }
        }
        $excel_path = [];
        foreach ($chunkedArray as $v) {
            $file_name_ = '/'.date('YmdHis') . uniqid() . '.xlsx';
            $path = $url.$file_name_;
            (new FastExcel($v))->export($path);
            $excel_path[] = $save_path.$file_name_;
        }
        //保存数据到zip
        $zip_path = $save_path.'/'.date('YmdHis'). '.zip';
        setZipByUrl($excel_path,$zip_path);
        returnSuccess($zip_path,'导出成功');
    }
}