<?php
namespace plugins\shop\form;

use Rap2hpoutre\FastExcel\FastExcel;

/**
 * @author: zhangguoming
 * @Time: 2024/6/12 10:48
 */
class rolesForm
{
    public static array $export_user_key = [
        'wname'=>'姓名',
        'role_name'=>'角色名称',
        'status'=>'状态',
        'created_time'=>'创建时间',
    ];
    public static function exportUser($list) {
        //表头
        $new_data = [];
        //数据
        $xuhao = 1;
        foreach ($list as $v) {
            $item['序号'] = $xuhao;
            foreach ($v as $k=>$v1) {
                if (!isset(self::$export_user_key[$k])) {
                    continue;
                }
                $value = $v1;
                //时间转化
                if ($k == 'status') {
                    if ($v1 == 0) {
                        $value = '禁用';
                    } else {
                        $value = '启用';
                    }
                }
                $item[self::$export_user_key[$k]] = $value;
            }
            $new_data[] = $item;
            $xuhao++;
        }
        //保存
        $save_path = "/public_salary/temp/role/user";
        $url = SELF_FK.$save_path;
        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path."/".date('YmdHis').uniqid().'.xlsx';
        $url = SELF_FK.$path;
        if (!file_exists($url)) {
            touch($url);
        }
        (new FastExcel($new_data))->export($url);
        return $path;
    }
}