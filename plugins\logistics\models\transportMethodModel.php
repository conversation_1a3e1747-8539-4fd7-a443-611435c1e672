<?php
/**
 * 运输方式模型
 * @purpose 运输方式数据处理
 * @Author: System
 * @Time: 2025/06/24
 */

namespace plugins\logistics\models;

use core\lib\db\dbErpMysql;

class transportMethodModel
{
    private $db;
    
    public function __construct()
    {
        $this->db = dbErpMysql::getInstance();
    }

    /**
     * 获取运输方式列表
     * @param array $params 查询参数
     * @return array
     */
    public function getTransportMethodList($params = [])
    {
        $where = '1=1';
        $whereData = [];
        
        // 启用状态筛选
        if (isset($params['enabled']) && $params['enabled'] !== '') {
            $where .= ' AND enabled = :enabled';
            $whereData['enabled'] = $params['enabled'];
        }
        
        // 是否系统运输方式筛选
        if (isset($params['is_system']) && $params['is_system'] !== '') {
            $where .= ' AND is_system = :is_system';
            $whereData['is_system'] = $params['is_system'];
        }
        
        // 运输方式名称筛选
        if (!empty($params['name'])) {
            $where .= ' AND name LIKE :name';
            $whereData['name'] = '%' . $params['name'] . '%';
        }
        
        // 运输方式ID筛选
        if (!empty($params['method_id'])) {
            $where .= ' AND method_id = :method_id';
            $whereData['method_id'] = $params['method_id'];
        }
        
        // 序号筛选
        if (!empty($params['code'])) {
            $where .= ' AND code = :code';
            $whereData['code'] = $params['code'];
        }
        
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 20;
        $pageSize = min($pageSize, 100); // 最大100条
        
        return $this->db->table('lingxing_transport_method')
            ->field('*')
            ->where($where, $whereData)
            ->order('code ASC, id DESC')
            ->pages($page, $pageSize);
    }

    /**
     * 获取单条运输方式详情
     * @param int $id
     * @return array|false
     */
    public function getTransportMethodDetail($id)
    {
        return $this->db->table('lingxing_transport_method')
            ->where('id = :id', ['id' => $id])
            ->one();
    }

    /**
     * 根据运输方式ID获取详情
     * @param string $methodId
     * @return array|false
     */
    public function getTransportMethodByMethodId($methodId)
    {
        return $this->db->table('lingxing_transport_method')
            ->where('method_id = :method_id', ['method_id' => $methodId])
            ->one();
    }

    /**
     * 保存运输方式数据（全量同步）
     * @param array $data
     * @return int
     */
    public function saveTransportMethodData($data)
    {
        try {
            $this->db->beginTransaction();
            
            $successCount = 0;
            $syncDate = date('Y-m-d');
            
            // 清空当前数据（全量同步）
            $this->db->table('lingxing_transport_method')->where('1=1')->delete();
            
            foreach ($data as $item) {
                if ($this->saveTransportMethodItem($item, $syncDate)) {
                    $successCount++;
                }
            }
            
            $this->db->commit();
            return $successCount;
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * 保存单个运输方式项
     * @param array $item
     * @param string $syncDate
     * @return bool
     */
    private function saveTransportMethodItem($item, $syncDate)
    {
        // 准备数据
        $saveData = [
            'method_id' => $item['method_id'] ?? '',
            'code' => $item['code'] ?? '',
            'name' => $item['name'] ?? '',
            'is_system' => $item['is_system'] ? 1 : 0,
            'enabled' => $item['enabled'] ?? 1,
            'remark' => $item['remark'] ?? '',
            'creator_id' => $item['creator_id'] ?? 0,
            'creator_name' => $item['creator_name'] ?? '',
            'updater_id' => $item['updater_id'] ?? 0,
            'updater_name' => $item['updater_name'] ?? '',
            'created_at' => $item['created_at'] ?? 0,
            'updated_at' => $item['updated_at'] ?? 0,
            'sync_date' => $syncDate,
            'created_time' => date('Y-m-d H:i:s'),
            'updated_time' => date('Y-m-d H:i:s')
        ];

        return $this->db->table('lingxing_transport_method')->insert($saveData);
    }

    /**
     * 更新运输方式状态
     * @param string $methodId
     * @param int $enabled
     * @return int
     */
    public function updateTransportMethodStatus($methodId, $enabled)
    {
        return $this->db->table('lingxing_transport_method')
            ->where('method_id = :method_id', ['method_id' => $methodId])
            ->update([
                'enabled' => $enabled,
                'updated_time' => date('Y-m-d H:i:s')
            ]);
    }

    /**
     * 删除运输方式
     * @param string $methodId
     * @return int
     */
    public function deleteTransportMethod($methodId)
    {
        return $this->db->table('lingxing_transport_method')
            ->where('method_id = :method_id', ['method_id' => $methodId])
            ->delete();
    }

    /**
     * 获取启用状态映射
     * @return array
     */
    public function getEnabledMap()
    {
        return [
            0 => '停用',
            1 => '启用'
        ];
    }

    /**
     * 获取是否系统运输方式映射
     * @return array
     */
    public function getIsSystemMap()
    {
        return [
            0 => '否',
            1 => '是'
        ];
    }

    /**
     * 获取运输方式统计信息
     * @return array
     */
    public function getTransportMethodStats()
    {
        $stats = [
            'total' => 0,
            'enabled' => 0,
            'disabled' => 0,
            'system' => 0,
            'custom' => 0
        ];

        // 总数
        $stats['total'] = $this->db->table('lingxing_transport_method')
            ->count();

        // 启用数量
        $stats['enabled'] = $this->db->table('lingxing_transport_method')
            ->where('enabled = 1')
            ->count();

        // 停用数量
        $stats['disabled'] = $this->db->table('lingxing_transport_method')
            ->where('enabled = 0')
            ->count();

        // 系统运输方式数量
        $stats['system'] = $this->db->table('lingxing_transport_method')
            ->where('is_system = 1')
            ->count();

        // 自定义运输方式数量
        $stats['custom'] = $this->db->table('lingxing_transport_method')
            ->where('is_system = 0')
            ->count();

        return $stats;
    }

    /**
     * 获取所有启用的运输方式（用于下拉选择）
     * @return array
     */
    public function getEnabledTransportMethods()
    {
        return $this->db->table('lingxing_transport_method')
            ->field('method_id, code, name')
            ->where('enabled = 1')
            ->order('code ASC')
            ->list();
    }

    /**
     * 验证参数
     * @param array $params
     * @return array
     */
    public function validateParams($params)
    {
        $errors = [];
        
        // 验证启用状态
        if (isset($params['enabled']) && $params['enabled'] !== '') {
            $validEnabled = [0, 1];
            if (!in_array((int)$params['enabled'], $validEnabled)) {
                $errors[] = '无效的启用状态值';
            }
        }
        
        // 验证是否系统运输方式
        if (isset($params['is_system']) && $params['is_system'] !== '') {
            $validIsSystem = [0, 1];
            if (!in_array((int)$params['is_system'], $validIsSystem)) {
                $errors[] = '无效的系统运输方式标识';
            }
        }
        
        // 验证分页参数
        if (isset($params['page_size']) && $params['page_size'] > 100) {
            $errors[] = '分页数量不能超过100';
        }
        
        return $errors;
    }

    /**
     * 搜索运输方式
     * @param string $keyword
     * @param int $limit
     * @return array
     */
    public function searchTransportMethods($keyword, $limit = 20)
    {
        $where = '(name LIKE :keyword OR code LIKE :keyword2)';
        $whereData = [
            'keyword' => '%' . $keyword . '%',
            'keyword2' => '%' . $keyword . '%'
        ];
        
        return $this->db->table('lingxing_transport_method')
            ->field('method_id, code, name, enabled, is_system')
            ->where($where, $whereData)
            ->order('enabled DESC, code ASC')
            ->limit($limit)
            ->list();
    }

    /**
     * 批量更新状态
     * @param array $methodIds
     * @param int $enabled
     * @return int
     */
    public function batchUpdateStatus($methodIds, $enabled)
    {
        if (empty($methodIds)) {
            return 0;
        }
        
        return $this->db->table('lingxing_transport_method')
            ->whereIn('method_id', $methodIds)
            ->update([
                'enabled' => $enabled,
                'updated_time' => date('Y-m-d H:i:s')
            ]);
    }

    /**
     * 获取最近同步时间
     * @return string|null
     */
    public function getLastSyncTime()
    {
        $result = $this->db->table('lingxing_transport_method')
            ->field('MAX(updated_time) as last_sync_time')
            ->one();
        
        return $result['last_sync_time'] ?? null;
    }

    /**
     * 获取运输方式数据用于导出
     * @param array $params
     * @return array
     */
    public function getTransportMethodsForExport($params = [])
    {
        $where = '1=1';
        $whereData = [];
        
        // 应用筛选条件
        if (isset($params['enabled']) && $params['enabled'] !== '') {
            $where .= ' AND enabled = :enabled';
            $whereData['enabled'] = $params['enabled'];
        }
        
        if (isset($params['is_system']) && $params['is_system'] !== '') {
            $where .= ' AND is_system = :is_system';
            $whereData['is_system'] = $params['is_system'];
        }
        
        if (!empty($params['name'])) {
            $where .= ' AND name LIKE :name';
            $whereData['name'] = '%' . $params['name'] . '%';
        }
        
        return $this->db->table('lingxing_transport_method')
            ->field('*')
            ->where($where, $whereData)
            ->order('code ASC, id DESC')
            ->list();
    }
}
