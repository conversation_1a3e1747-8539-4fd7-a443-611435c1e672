<?php
/**
 * @author: zhangguoming
 * @Time: 2024/11/26 17:00
 */

namespace financial\form;

use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;
use financial\models\userModel;

class userForm
{
    //获取财务用户
    public static function getFinancialUser($params) {
        $dbF = dbFMysql::getInstance();
        $user_roles = $dbF->table('user_roles')
            ->where('where is_delete = 0 and status = 1')
            ->field('user_id')
            ->groupBy(['user_id'])
            ->list();
        $user_ids = array_column($user_roles,'user_id');
        //查询
        $db = dbMysql::getInstance();
        $db->table('qwuser','a')
            ->leftJoinOut('financial','user_info','b','b.user_id = a.id')
            ->where('where a.is_delete = 0')
            ->whereIn('a.id',$user_ids);
        if (!empty($params['wname'])) {
            $db->andWhere('and a.wname like :wname',['wname'=>"%{$params['wname']}%"]);
        }
        $list = $db->field('a.id,a.wname,b.is_all_data,b.begin_time,b.end_time,b.id as info_id')
            ->pages($params['page'],$params['page_size']);
        if (count($list)) {
            foreach ($list['list'] as &$v) {
                if (!$v['info_id']) {
                    $v['begin_time'] = '';
                    $v['end_time'] = '';
                    $v['is_all_data'] = -1;
                    $v['info_id'] = 0   ;
                }
            }
        }
        return $list;
    }
    //设置用户时间权限
    public static function setUserInfo($params) {
        $list = json_decode($params['list'],true);
        $db = dbFMysql::getInstance();
        $user_ids = array_column($list,'user_id');
        $user = $db->table('user_info')
            ->whereIn('user_id',$user_ids)
            ->field('id,user_id')
            ->list();
        //已设置的用户
        $user_list = array_column($user,'id','user_id');
        $db->beginTransaction();
        try {
            foreach ($list as $v) {
                $user_id = (int)$v['user_id'];
                $is_all_data = (int)$v['is_all_data'];
                if ($user_id && $is_all_data != -1) {
                    if (isset($user_list[$user_id])) {
                        //修改
                        $db->table('user_info')
                            ->where('where user_id = :user_id',['user_id'=>$user_id])
                            ->update([
                                'is_all_data'=>(int)$v['is_all_data'],
                                'begin_time'=>$v['begin_time'],
                                'end_time'=>$v['end_time'],
                                'updated_time'=>date('Y-m-d H:i:s')
                            ]);
                    } else {
                        $db->table('user_info')
                            ->insert([
                                'user_id'=>$user_id,
                                'is_all_data'=>(int)$v['is_all_data'],
                                'begin_time'=>$v['begin_time'],
                                'end_time'=>$v['end_time'],
                                'created_time'=>date('Y-m-d H:i:s')
                            ]);
                    }
                }
            }
            $db->commit();
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //获取用户时间权限（当前用户）
    public static function getUserInfo() {
        $db = dbFMysql::getInstance();
        $user = $db->table('user_info')
            ->where('where user_id = :user_id',['user_id'=>userModel::$qwuser_id])
            ->field('is_all_data, begin_time, end_time')
            ->one();
        if ($user) {
            return $user;
        } else {
            return [];
        }
    }
}