<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/7 17:27
 */
namespace financial\controller;

use admin\form\qwuserFrom;
use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use financial\models\userRolesModel;

class loginController
{
    public function login(){
        returnError('不可直接登录');
        // 检查参数
//        $param = $_POST;
//        $paraList = array('account', 'password');
//        $missField = checkParaments($param, $paraList);
//        if ($missField != null) {
//            SetReturn(-1,'缺少必传参数');
//        }
//        $user_name = trim($param["account"]);  // 用户名
//        $password = trim($param["password"]);  // 密码
//        if (!$user_name || !$password) {
//            SetReturn(-1, "用户名或密码不能为空");
//        }
//        $db = dbMysql::getInstance();
//        $sql = 'select id,wname,wid,avatar from oa_qwuser where wstatus = 1 and is_delete = 0 and (wname=:account or wphone=:account) limit 1';
//        $w_user = $db->query($sql, ['account'=>$user_name]);
//        if ($w_user) {
//            //密码解析：
//            $user = $db->query("select id,pwd,uniqueid,super_admin from oa_user where qw_userid = '{$w_user['wid']}' and is_delete = 0 limit 1");
//            $user_mac_list = $db->table('user_mac')
//                ->where('where user_id=:user_id',['user_id'=>$user['id']])->list();
//            $user_mac = array_column($user_mac_list,'mac');
//            if (!in_array(MAC,$user_mac)) {
//                SetReturn(-1, "mac   地址错误");
//            }
//            if (!$user) {
//                SetReturn(-1, "非管理员不可登录");
//            }
//            $pwd = getPwdMd5($password, $user['uniqueid']);
//            if ($pwd != $user['pwd']) {
//                SetReturn(-1, "用户名或密码不正确");
//            }
//            if (!$user['super_admin']) {
//                //查询是否为财务系统成员
//                $dbF = dbFMysql::getInstance();
//                $f_user = $dbF->table('user_roles')
//                    ->where('where user_id=:user_id and is_delete=0 and status=1',['user_id'=>$w_user['id']])
//                    ->one();
//                if (!$f_user) {
//                    SetReturn(-1, "非财务系统成员不可登录");
//                }
//            }
//            $result = $this->getTokenRedis($w_user, $user, $user_mac);
//            SetReturn(0,'',$result);
//        } else {
//            SetReturn(-1, "用户名或密码不正确");
//        }
    }
    /**
     * @param $user  企微用户信息
     * @param $user_ 后台用户信息
     * @return array
     * @throws \RedisException
     * @throws \core\lib\ExceptionError
     */
    public function getTokenRedis($user, $user_,$user_mac,$code_token='')
    {
        // todo 不要轻易改动字多，系统很多功能要用
        $redis = (new \core\lib\predisV())::$client;
        $prefix = config::get('financial_token_key_prefix', 'app');
        if (!empty($code_token) && $redis->exists($prefix.$code_token)) {
            $token = $code_token;
        } else {
            $token = getPwdMd5($user['id'].$user['wname'], getuniqId());
        }
        $key = $prefix.$token;
        $data = [
            'id'=>$user['id'],
            'avatar'=>$user['avatar'],
            'wid'=>$user['wid'],
            'wname'=>$user['wname'],
            'is_super'=>$user_['super_admin'],
            'auth'=>'',
            'role_name'=>'',
            'role_type'=>[],
            'user_mac'=>json_encode($user_mac)
        ];
        $auth_ = userRolesModel::getAuthByQuserId($user['id']);
        if ($auth_) {
            $data['auth'] = $auth_['auth'];
            $data['list_auth'] = $auth_['list_auth'];
            $data['role_name'] = $auth_['role_name'];
            $data['role_type'] = $auth_['role_type'];
        }
        $data['role_type'] = json_encode($data['role_type']);
        $redis->hmset($key, $data);
        $redis->expire($key, 4*60*60);
        return [
            'token'=> $token,
            'data'=>$data
        ];
    }

    public function codeLogin(){
        // 检查参数
        $param = $_POST;
        $paraList = array('code');
        $missField = checkParaments($param, $paraList);
        if ($missField != null) {
            SetReturn(-1,'缺少必传参数');
        }
        $db = dbMysql::getInstance();
        $code_data = $db->table('login_code')
            ->where('code = :code',['code'=>$param['code']])
            ->one();
        if (!$code_data) {
            returnError('CODE已失效');
        }
        $db->table('login_code')
            ->where('code = :code',['code'=>$param['code']])
            ->update([
                'status'=>0,
            ]);
        $created_time = $code_data['created_time'];
        if ((time() - $created_time) > 60 || $code_data['status'] == 0) {
            returnError('CODE已过期');
        }
        $w_user = $db->table('qwuser')
            ->where('id=:id',['id'=>$code_data['user_id']])
            ->field('id,wname,wid,avatar')
            ->one();
        if ($w_user) {
            //密码解析：
            $user = $db->table('user')
                ->where('qw_userid = :qw_userid and is_delete = 0',['qw_userid'=>$w_user['wid']])
                ->one();
            //用户mac地址查询
            $user_mac = $db->table('sys_user_mac')
                ->where('where qw_userid=:qw_userid',['qw_userid'=>$w_user['id']])
                ->one();
            $user_macs = [];
            if ($user_mac && !empty($user_mac['mac'])) {
                $user_macs = json_decode($user_mac['mac']);
            }
            if (!in_array(MAC,$user_macs)) {
                SetReturn(-1, "mac地址错误");
            }
            if (!$user) {
                SetReturn(-1, "非管理员不可登录");
            }
            $result = $this->getTokenRedis($w_user, $user, $user_macs,$code_data['token2']);
            SetReturn(0,'',$result);
        } else {
            SetReturn(-1, "code数据异常");
        }
    }

    public function logOut(){
        $prefix = config::get('financial_token_key_prefix', 'app');
        $key = $prefix.USER_TOKEN;
        $redis = (new \core\lib\predisV())::$client;
        $user_info = $redis->hmget($key,['id'])??'';
        if (!empty($user_info)) {
            $user_id = $user_info['id']??0;
            if ($user_id) {
                qwuserFrom::cleanUpToken($user_id);
            }
            $redis->del($key);
        }
        returnSuccess([],'成功登出');
    }
}