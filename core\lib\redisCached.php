<?php

namespace core\lib;

use core\lib\db\dbAfMysql;
use core\lib\db\dbMysql;
use core\lib\db\dbShopMysql;
use core\lib\db\dbSMysql;
use plugins\shop\models\shopApplyModel;

class redisCached
{
    const YWX_USER_INFO_KEY  = 'ywx_user_info';
    const YWX_DEPARTMENT_KEY = 'ywx_department';
    const YWX_CORP_KEY       = 'ywx_corp';
    const YWX_COUNTRY        = 'ywx_country';
    const YWX_PHONE_CARD     = 'ywx_phone_card';
    const YWX_EMAIL          = 'ywx_email';
    const YWX_DOMAIN         = 'ywx_domain';
    const YWX_TRADEMARK      = 'ywx_trademark';
    const YWX_CREDIT_CARD    = 'ywx_credit_card';
    const YWX_LEGAL_PERSON    = 'ywx_legal_person';
    const YWX_COMPANY    = 'ywx_company';
    const YWX_SHOP           = 'ywx_shop';
    const YWX_SHOP_APPLY           = 'ywx_shop_apply';
    const YWX_RECEIVE_CARD   = 'ywx_receive_card';
    const YWX_RECEIVE_ACCOUNT   = 'ywx_receive_account';
    const YWX_RECEIVE_ACCOUNT_ALL   = 'ywx_receive_account_all';


    public static function getUserInfo($is_new = 0)
    {
        $is_new && self::del(self::YWX_USER_INFO_KEY);
        return self::get(self::YWX_USER_INFO_KEY, function () {
            $db = dbMysql::getInstance();
            $list = $db->table('qwuser', 'u')
                ->field('u.id as user_id,u.wid as user_wid, u.wname as user_name, u.wphone as user_wphone, u.wdepartment_ids as user_wdepartment_ids,u.wmain_department, u.position as user_position, ui.*')
                ->leftJoin('user_info', 'ui', 'u.id=ui.qwuser_id')
                ->list();
            return $list ?: [];

        }, 60 * 5);
    }

    public static function getDepartment($is_new = 0)
    {
        $is_new && self::del(self::YWX_DEPARTMENT_KEY);
        return self::get(self::YWX_DEPARTMENT_KEY, function () {
            $db = dbMysql::getInstance();
            $department = $db->table('qwdepartment')->list();
            return $department ?: [];
        }, 60 * 5);

    }

    public static function getCorp($is_new = 0)
    {
        $is_new && self::del(self::YWX_CORP_KEY);
        return self::get(self::YWX_CORP_KEY, function () {
            $sdb = dbSMysql::getInstance();
            $corps = $sdb->table('corp')->list();
            return $corps ?: [];
        }, 60 * 5);
    }

    public static function getCountry()
    {
        return self::get(self::YWX_COUNTRY, function () {
            $db = dbAfMysql::getInstance();
            $country = $db->table('world_countries')->list();
            $ret = [];
            foreach ($country as $value) {
                $translations = json_decode($value['translations'], true);
                $ret[] = [
                    'id' => $value['id'],
                    'country_code' => $value['iso2'],
                    'name' => $translations['cn'],
                ];
            }
            return $ret;
        }, 60 * 60);
    }

    public static function getPhoneCard($is_new = 0)
    {
        $is_new && self::del(self::YWX_PHONE_CARD);
        return self::get(self::YWX_PHONE_CARD, function () {
            $db = dbShopMysql::getInstance();
            $phone_card = $db->table('phone_card')->order('id desc')->list();
            $ret = [];

            $users = redisCached::getUserInfo();
            $users = array_column($users, 'user_name', 'user_id');

            foreach ($phone_card as $value) {
                $ret[] = [
                    'id' => $value['id'],
                    'phone_number' => $value['phone_number'],
                    'user_id' => $value['user_id'],
                    'user_name' => $value['user_id'] ? ($users[$value['user_id']] ?? ''): $value['user_name'],
                    'phone_manager' => $value['phone_manager'],
                    'card_status' => $value['card_status'],
                ];
            }
            return $ret;
        }, 5 * 60);
    }

    public static function getEmail($is_new = 0)
    {
        $is_new && self::del(self::YWX_EMAIL);
        return self::get(self::YWX_EMAIL, function () {
            $db = dbShopMysql::getInstance();
            $email = $db->table('email')->order('id desc')->list();
            $ret = [];

            $phone_card = self::getPhoneCard();
            $phone_card = array_column($phone_card, null, 'id');

            foreach ($email as $value) {
                $ret[] = [
                    'id' => $value['id'],
                    'use_status' => $value['use_status'],
                    'email_account' => $value['email_account'],
                    'email_password' => $value['email_password'],
                    'email_assistant_email' => $value['email_assistant_email'],
                    'email_safe_phone_id' => $value['email_safe_phone_id'],
                    'email_safe_phone_number' => $phone_card[$value['email_safe_phone_id']]['phone_number'] ?? '',
                ];
            }
            return $ret;
        }, 5 * 60);
    }

    public static function getDomain($is_new = 0)
    {
        $is_new && self::del(self::YWX_DOMAIN);
        return self::get(self::YWX_DOMAIN, function () {
            $db = dbShopMysql::getInstance();
            $domain = $db->table('domain')->order('id desc')->list();
            $ret = [];

            foreach ($domain as $value) {
                $ret[] = [
                    'id' => $value['id'],
                    'domain' => $value['domain'],
                ];
            }
            return $ret;
        }, 5 * 60);
    }

    public static function getCreditCard($is_new = 0)
    {
        $is_new && self::del(self::YWX_CREDIT_CARD);
        return self::get(self::YWX_CREDIT_CARD, function () {
            $db = dbShopMysql::getInstance();
            $credit_card = $db->table('credit_card')->order('id desc')->list();
            $ret = [];

            foreach ($credit_card as $value) {
                $ret[] = [
                    'id' => $value['id'],
                    'card_number' => $value['card_number'],
                    'credit_card_status' => $value['credit_card_status'],
                    'activation_status' => $value['activation_status'],
                    'validity_period' => json_decode($value['validity_period'], true),
                ];
            }
            return $ret;
        }, 5 * 60);
    }

    public static function getTrademark($is_new = 0)
    {
        $is_new && self::del(self::YWX_TRADEMARK);
        return self::get(self::YWX_TRADEMARK, function () {
            $db = dbShopMysql::getInstance();
            $trademark = $db->table('trademark')->order('id desc')->list();
            $ret = [];

            foreach ($trademark as $value) {
                $ret[] = [
                    'id' => $value['id'],
                    'dep_id' => $value['dep_id'],
                    'user_id' => $value['user_id'],
                    'brand_name' => $value['brand_name'],
                    'trademark_holder' => $value['trademark_holder'],
                    'use_status' => $value['use_status'],
                    'category' => $value['category'],
                ];
            }
            return $ret;
        }, 5 * 60);
    }

    public static function getLegalPerson($is_new = 0)
    {
        $is_new && self::del(self::YWX_LEGAL_PERSON);
        return self::get(self::YWX_LEGAL_PERSON, function () {
            $db = dbShopMysql::getInstance();
            $legal_person = $db->table('legal_person')->where('is_delete = 0')->order('id desc')->list();
            $ret = [];

            foreach ($legal_person as $value) {
                $ret[] = [
                    'id' => $value['id'],
                    'id_card' => $value['id_card'],
                    'name' => $value['name'],
                    'is_register_company' => $value['is_register_company'],
                    'cooperation_level' => $value['cooperation_level'],
                    'id_card_expire' => $value['id_card_expire'],
                    'credit_card_id' => $value['credit_card_id'],
                    'type' => $value['type']
                ];
            }
            return $ret;
        }, 5 * 60);

    }

    public static function getCompany($is_new = 0)
    {
        $is_new && self::del(self::YWX_COMPANY);
        return self::get(self::YWX_COMPANY, function () {
            $db = dbShopMysql::getInstance();
            $company = $db->table('company')->order('id desc')->list();
            $ret = [];

            $legal_person = self::getLegalPerson();
            $legal_person = array_column($legal_person, null, 'id');

            foreach ($company as $value) {
                $id_card_expire = $legal_person[$value['legal_person_id']]['id_card_expire'] ?? null;
                $id_card_expire && $id_card_expire = json_decode($id_card_expire, true) ?? null;
                $ret[] = [
                    'id' => $value['id'],
                    'legal_person_id' => $value['legal_person_id'],
                    'legal_person_name' => $legal_person[$value['legal_person_id']]['name'] ?? '',
                    'legal_person_id_card_expire' =>  $id_card_expire,
                    'legal_person_cooperation_level' => $legal_person[$value['legal_person_id']]['cooperation_level'] ?? '',
                    'legal_person_credit_card_id' => $legal_person[$value['legal_person_id']]['credit_card_id'] ?? '',
                    'legal_person_type' => $legal_person[$value['legal_person_id']]['type'] ?? '',
                    'company_name' => $value['company_name'],
                    'company_status' => $value['company_status'],
                    'register_country' => $value['register_country'],
                ];
            }
            return $ret;
        }, 5 * 60);

    }

    public static function getShop($is_new = 0)
    {
        $is_new && self::del(self::YWX_SHOP);
        return self::get(self::YWX_SHOP, function () {
            $db = dbShopMysql::getInstance();
            $shop = $db->table('shop')->order('id desc')->list();
            $ret = [];

            foreach ($shop as $value) {
                $ret[] = [
                    'id' => $value['id'],
                    'shop_number' => $value['shop_number'],
                    'seller_id' => $value['seller_id'],
                    'shop_status' => $value['shop_status'],
                ];
            }
            return $ret;
        }, 5 * 60);
    }

    public static function getShopApply($is_new = 0)
    {
        $is_new && self::del(self::YWX_SHOP_APPLY);
        return self::get(self::YWX_SHOP_APPLY, function () {
            $db = dbShopMysql::getInstance();

            // 查询已被绑定的申请
            $list = $db->table('shop_register')
                ->where('shop_apply_id > 0')
                ->field('shop_apply_id')
                ->list();
            $ids = array_column($list, 'shop_apply_id');

            $db->table('shop_apply')
                ->where('status=:status', ['status' => shopApplyModel::STATUS_WAIT_ASSIGN]);
            if ($ids) {
                $db->andWhere('id not in (:ids)', ['ids' => implode(',', $ids)]);
            }
            $shop_apply = $db->order('id desc')->list();
            $ret = [];

            $deps = redisCached::getDepartment();
            $deps = array_column($deps, 'name', 'id');

            foreach ($shop_apply as $value) {
                $ret[] = [
                    'id' => $value['id'],
                    'dep_id' => $value['dep_id'],
                    'dep_name' => $deps[$value['dep_id']] ?? '',
                    'country_site' => $value['country_site'],
                    'shop_type' => $value['shop_type'],
                    'expect_date' => $value['expect_date'],
                ];
            }
            return $ret;
        }, 5 * 60);
    }
    public static function getReceiveCard($is_new = 0)
    {
        $is_new && self::del(self::YWX_RECEIVE_CARD);
        return self::get(self::YWX_RECEIVE_CARD, function () {
            $db = dbShopMysql::getInstance();
            $receive_card = $db->table('receive_card')->order('id desc')->list();
            $ret = [];

            foreach ($receive_card as $value) {
                $ret[] = [
                    'id' => $value['id'],
                    'card_number' => $value['card_number'],
                    'receive_platform' => $value['receive_platform'],
                    'card_status' => $value['card_status'],
                ];
            }
            return $ret;
        }, 5 * 60);
    }
    //收款账号，树状结构
    public static function getReceiveAccount($is_new = 0)
    {
        $is_new && self::del(self::YWX_RECEIVE_ACCOUNT);
        return self::get(self::YWX_RECEIVE_ACCOUNT, function () {
            $db = dbShopMysql::getInstance();
            $receive_account = $db->table('receive_account')->list();
            $ret = [];

            foreach ($receive_account as $value) {
                $item = [
                    'id' => $value['id'],
                    'pid' => $value['pid'],
                    'account_name' => $value['account_name'],
                ];
                if ($value['pid'] == 0) {
                    $ret[$value['id']]['id'] = $item['id'];
                    $ret[$value['id']]['pid'] = $item['pid'];
                    $ret[$value['id']]['account_name'] = $item['account_name'];
                    !isset($ret[$value['id']]['children']) && $ret[$value['id']]['children'] = [];
                } else {
                    !isset($ret[$value['pid']]['children']) && $ret[$value['pid']]['children'] = [];
                    $ret[$value['pid']]['children'][] = $item;
                }
            }
            $ret = array_values($ret);
            return $ret;
        }, 5 * 60);
    }

    public static function getReceiveAccountAll($is_new = 0)
    {
        $is_new && self::del(self::YWX_RECEIVE_ACCOUNT_ALL);
        return self::get(self::YWX_RECEIVE_ACCOUNT_ALL, function () {
            $db = dbShopMysql::getInstance();
            $receive_account = $db->table('receive_account')->list();
            $ret = [];

            foreach ($receive_account as $value) {
                $ret[] = [
                    'id' => $value['id'],
                    'pid' => $value['pid'],
                    'account_name' => $value['account_name']
                ];
            }
            return $ret;
        }, 5 * 60);
    }

    public static function get($key, $queryFunction, $expire = 60)
    {
        // 1. 尝试从 Redis 获取数据
        $redis = (new predisV())::$client;
        $value = $redis->get($key);

        if ($value !== false) {
            return json_decode($value, true);
        }

        // 2. Redis 无数据，执行数据库查询
        $data = $queryFunction();

        if (!empty($data)) {
            $redis_data = json_encode($data, JSON_UNESCAPED_UNICODE);
            // 3. 数据库查询结果存入 Redis，设置过期时间
            $redis->set($key, $redis_data);
            $redis->expire($key, $expire);
        }

        return $data;
    }

    // 删除
    public static function del($key)
    {
        $redis = (new predisV())::$client;
        $redis->del($key);
    }


}