<?php

namespace plugins\checkin\models;
/**
 * vacationModel
 *
 * This is the model class for the vacation controllerCREATE TABLE `oa_c_vacation` (
 * `id` int NOT NULL AUTO_INCREMENT,
 * `name` varchar(255) DEFAULT NULL COMMENT '假期名称',
 * `time_attr` tinyint DEFAULT NULL COMMENT '假期时间刻度：0-按天请假；1-按小时请假',
 * `duration_type` tinyint DEFAULT NULL COMMENT '时长计算类型：0-自然日；1-工作日',
 * `quota_attr` text COMMENT '假期发放相关配置',
 * `perday_duration` int DEFAULT NULL COMMENT '单位换算值，即1天对应的秒数，可将此值除以3600得到一天对应的小时。',
 * `is_newovertime` tinyint DEFAULT NULL COMMENT '是否关联加班调休，0-不关联，1-关联，关联后改假期类型变为调休假',
 * `enter_comp_time_limit` int DEFAULT NULL COMMENT '入职时间大于n个月可用该假期，单位为月',
 * `expire_rule` varchar(255) DEFAULT NULL COMMENT '假期过期规则',
 * PRIMARY KEY (`id`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
 *
 * @package checkin
 * @subpackage models
 */
class vacationModel
{
    public static int $id;
    public static string $name;
    public static int $time_attr;
    public static int $duration_type;
    public static string $quota_attr;
    public static int $perday_duration;
    public static int $is_newovertime;
    public static int $enter_comp_time_limit;
    public static string $expire_rule;

    public static string $table = 'vacation';
    public static string $full_table = 'oa_c_vacation';

}