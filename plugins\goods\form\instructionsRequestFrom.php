<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/29 10:35
 */

namespace  plugins\goods\form;

use plugins\goods\models\userModel;
use core\lib\db\dbMysql;

class instructionsRequestFrom
{
    public static array $msg_info = [];
    public static array $check_msg_wid = [];
    public static array $operator_wids = [];//审核通过时，要推送给运营人员的wid
    public static array $check_operator_msg_wid = [];//运营审核的通知人
    //生成说明书任务+待办
    public static function setIsRequest(int $project_id, array $goods_info,string $goods_matter) {
        $db = dbMysql::getInstance();
        $request = $db->table('instructions_request')
            ->where('where goods_id=:goods_id',['goods_id'=>$goods_info['id']])
            ->one();
        if ($request) {
            return '';
        }
        //获取配置人
        $allocation_user = configFrom::getConfigData(['pic_manage']);
        if (!$allocation_user['pic_manage']) {
            SetReturn(-1,'图片负责人未配置，请联系系统管理员');
        }
        $manage_info = json_decode($allocation_user['pic_manage'],true);
        //保存说明书需求
        $insert_data = [
            'goods_id'=>$goods_info['id'],
            'project_id'=>$project_id,
            'user_id'=>userModel::$qwuser_id,
            'request_name'=>$goods_info['goods_name'].'说明书',
            'status'=>0,
            'created_time'=>date('Y-m-d H:i:s'),
        ];
        $id = $db->table('instructions_request')
            ->insert($insert_data);
        //生成待办事项
        goodsMattersFrom::addCreateMatter($goods_info['goods_name'].'_说明书',$goods_info['id'],'制作人配置',0,5,$manage_info[0]['id'],$id,0);
        //消息
        self::$msg_info = [
            'wids'=>array_column($manage_info,'wid'),
            'msg'=>"产品【{$goods_info['goods_name']}】说明书任务已提交给您，请及时查看并分配制作员。",
            'other_data'=>[
                'user_id'=>userModel::$qwuser_id,
                'model_id'=>$id,
                'msg_type'=>5
            ],
        ];
    }

    //美工提交时 生成交办数据
    public static function setCheckMatter($request,$goods_info) {
        //修改提交的待办事件为完成
        $db = dbMysql::getInstance();
        $db->table('goods_matters')
            ->where('where type = 5 and model_id=:reques_id and status <> 1 and create_type = 0',['reques_id'=>$request['id']])
            ->update(['status'=>1,'completion_time'=>time()]);
        //生成审核待办
        $check_info = configFrom::getConfigByName('fs_check_user');
        $check_info = json_decode($check_info,true);
        goodsMattersFrom::addCreateMatter($goods_info['goods_name'].'_说明书',$goods_info['id'],'图片审核',1,5,$check_info[0]['id'],$request['id'],0);
        //消息推送
        $msg = messagesFrom::getMsgTxtForImgRequest(2,$goods_info['goods_name'],$request['request_name']);
        $other_data = [
            'user_id'=>userModel::$qwuser_id,
            'model_id'=>$request['id'],
            'msg_type'=>7
        ];
        messagesFrom::senMeg([$check_info[0]['wid']],$msg,$other_data);
    }

    //需求审核状态更新+待办事件处理（审核人审核）
    public static function setPassStatus($is_pass,$request,$goods_info,$remarks) {
        $is_check = $is_pass?1:2;
        $db = dbMysql::getInstance();
        //修改审核状态
        $db->table('instructions_request')
            ->where('where id=:request_id',['request_id'=>$request['id']])
            ->update(['is_check'=>$is_check,'operator_pass_wid'=>'']);
        //记录处理日志
        $db->table('instructions_request_check_log');
        $db->insert([
            'request_id'=>$request['id'],
            'check_user_id'=>userModel::$qwuser_id,
            'is_check'=>$is_check,
            'reason'=>$remarks,
            'created_time'=>date('Y-m-d H:i:s'),
            'type'=>1,
        ]);
        if ($is_pass) {
            //设需求状态为已完成
            $db->table('instructions_request')
                ->where('where id=:id',['id'=>$request['id']])
                ->update(['status'=>3,'operator_pass_wid'=>'']);
        } else {
            //设需求状态为进行中
            $db->table('instructions_request')
                ->where('where id=:id',['id'=>$request['id']])
                ->update(['status'=>1,'operator_pass_wid'=>'']);
            //重新添加待办事项
            goodsMattersFrom::addCreateMatter($goods_info['goods_name'].'_说明书',$request['goods_id'],'图片制作',0,5,$request['qwuser_id'],$request['id'],$request['expected_time']);
        }
        //审核待办事项为已完成
        $db->table('goods_matters')
            ->where('where type = 5 and model_id=:reques_id and status <> 1 and create_type = 1',['reques_id'=>$request['id']])
            ->update(['status'=>1]);
        //给运维生成待办事项
        $operator_info = json_decode($goods_info['operator_info'],true);
        $operator_wids = [];
        foreach ($operator_info as $operator) {
            if (!empty($operator['id'])) {
                goodsMattersFrom::addCreateMatter($goods_info['goods_name'].'_说明书',$request['goods_id'],'运营审核',1,5,$operator['id'],$request['id'],$request['expected_time']);
                $operator_wids[] = $operator['wid'];
            }
        }
        self::$operator_wids = array_unique($operator_wids);
        //抄送人员
        self::msgForCheckWid($request);
    }

    //需求审核状态更新+待办事件处理（运营人员审核）
    public static function setOperatorPassStatus($is_pass,$request,$goods_info,$remarks) {
        $goods_operator = json_decode($goods_info['operator_info'],true);
        $is_check = $is_pass?1:2;
        $db = dbMysql::getInstance();
        //记录处理日志
        $db->table('instructions_request_check_log');
        $db->insert([
            'request_id'=>$request['id'],
            'check_user_id'=>userModel::$qwuser_id,
            'is_check'=>$is_check,
            'reason'=>$remarks,
            'created_time'=>date('Y-m-d H:i:s'),
            'type'=>2,
        ]);
        $operator_pass_wid = json_decode($request['operator_pass_wid']);
        $operator_pass_wid[] = userModel::$wid;
        if ($is_pass) {
            $operator_pass_wid = array_unique($operator_pass_wid);
            if (count($operator_pass_wid) == count($goods_operator)) {
                //设置上传的图片为确认状态
                $db->table('imgs_request_collection')
                    ->where('where request_id=:request_id and is_delete = 0 and is_confirm = 0 and source_type = 1',['request_id'=>$request['id']])
                    ->update(['is_confirm'=>1 ]);
                //设需求状态为已完成
                $db->table('instructions_request')
                    ->where('where id=:id',['id'=>$request['id']])
                    ->update([
                        'status'=>4,
                        'operator_pass_wid'=>json_encode($operator_pass_wid)
                    ]);
                //将说明书放到产品文档库
                $fs_list =  $db->table('imgs_request_collection')
                    ->where('where request_id=:request_id and is_delete = 0 and is_confirm = 1 and source_type = 1 and (file_type=4 or file_type=5)',['request_id'=>$request['id']])->list();
                $db->table('goods_project_file')
                    ->where('where goods_id =:goods_id and source_type=1',['goods_id'=>$request['goods_id']])
                    ->update(['is_delete'=>1]);
                foreach ($fs_list as $file) {
                    $db->table('goods_project_file')
                        ->insert([
                            'user_id'=>userModel::$qwuser_id,
                            'wid'=>userModel::$wid,
                            'source_type'=>1,
                            'goods_id'=>$request['goods_id'],
                            'project_id'=>$request['project_id'],
                            'src'=>$file['url'],
                            'extension'=>$file['extension'],
                            'thumb_src'=>$file['thumb_src'],
                            'filename'=>'说明书'.($file['file_type']==4?'pdf版':'竖版').$file['extension']
                        ]);
                }
            } else {
                $db->table('instructions_request')
                    ->where('where id=:id',['id'=>$request['id']])
                    ->update(['operator_pass_wid'=>json_encode(array_unique($operator_pass_wid))]);
            }
            $db->table('goods_matters')
                ->where('where type = 5 and model_id=:reques_id and status <> 1 and create_type = 1 and qwuser_id=:qwuser_id',['reques_id'=>$request['id']])
                ->update(['status'=>1,'is_pass'=>1,'check_reason'=>$remarks,'qwuser_id'=>userModel::$qwuser_id]);
        } else {
            //设需求状态为进行中
            $db->table('instructions_request')
                ->where('where id=:id',['id'=>$request['id']])
                ->update(['status'=>1,'operator_pass_wid'=>'']);
            //重新添加待办事项 给美工
            goodsMattersFrom::addCreateMatter($goods_info['goods_name'].'_说明书',$request['goods_id'],'图片制作',0,5,$request['qwuser_id'],$request['id'],$request['expected_time']);
            //将所有审核待办完成,审核不通过
            $db->table('goods_matters')
                ->where('where type = 5 and model_id=:reques_id and status <> 1 and create_type = 1',['reques_id'=>$request['id']])
                ->update(['status'=>1,'is_pass'=>0,'check_reason'=>$remarks]);
        }
        //抄送人员
        self::msgForOperatorCheckWid($request);
    }

    //图片审核推送数据获取 不可在循环中使用
    public static function msgForCheckWid($request) {
        $db = dbMysql::getInstance();
        //推给配置人，美工
        $user_ids = [$request['allocation_user_id'],$request['qwuser_id']];
        $user_list = $db->table('qwuser')
            ->whereIn('id',$user_ids)
            ->field('wid')
            ->list();
        $wids = array_column($user_list,'wid');
        $wids = array_unique($wids);
        self::$check_msg_wid = array_unique($wids);
    }
    //发送消息给抄送人
    public static function sendMsgForCheck($is_pass,$request,$goods_info,$copy_user,$remarks = ''){
        $msg = messagesFrom::getMsgTxtForImgRequest(($is_pass==1?3:4),$goods_info['goods_name'],$request['request_name']);
        $wids = array_column($copy_user,'wid');
        $wids = array_unique(array_merge($wids,self::$check_msg_wid));
        $other_data = [
            'user_id'=>userModel::$qwuser_id,
            'model_id'=>$request['id'],
            'msg_type'=>7
        ];
        messagesFrom::senMeg($wids,$msg,$other_data,$remarks);
        //推送审核消息给运营
        if ($is_pass == 1) {
            $wids = self::$operator_wids;
            $msg = messagesFrom::getMsgTxtForImgRequest(6,$goods_info['goods_name'],$request['request_name']);
            messagesFrom::senMeg($wids,$msg,$other_data,$remarks);
        }
    }
    //运营人员审核推送的人
    public static function msgForOperatorCheckWid($request) {
        $db = dbMysql::getInstance();
        //推给配置人，美工
        $user_ids = [$request['allocation_user_id'],$request['qwuser_id']];
        $user_list = $db->table('qwuser')
            ->whereIn('id',$user_ids)
            ->field('wid')
            ->list();
        $wids = array_column($user_list,'wid');
        //配置的推送人
        $default_user = configFrom::getConfigData(['fs_operate_check_copy_user','fs_check_user']);
        $operate_check_copy_user = $default_user['fs_operate_check_copy_user'];
        $check_user = $default_user['fs_check_user'];
        if ($operate_check_copy_user) {
            $default_wid = array_column(json_decode($operate_check_copy_user, true), 'wid');
            $wids = array_merge($wids,$default_wid);
        }
        if ($check_user) {
            $default_wid = array_column(json_decode($check_user, true), 'wid');
            $wids = array_merge($wids,$default_wid);
        }
        $wids = array_unique($wids);
        self::$check_operator_msg_wid = array_unique($wids);
    }
    //运营人员审核
    public static function sendMsgForOperatorCheck($is_pass,$request,$goods_info,$copy_user,$remarks = ''){
        $goods_operator = json_decode($goods_info['operator_info'],true);
        $operator_wid = array_column($goods_operator,'wid');
        $other_data = [
            'user_id'=>userModel::$qwuser_id,
            'model_id'=>$request['id'],
            'msg_type'=>7
        ];
        if ($is_pass) {
            $msg = messagesFrom::getMsgTxtForImgRequest(7,$goods_info['goods_name'],$request['request_name']);
            $wids = array_unique(array_merge($operator_wid,self::$check_operator_msg_wid));
            messagesFrom::senMeg($wids,$msg,$other_data,$remarks);
        } else {
            $msg1 = messagesFrom::getMsgTxtForImgRequest(8,$goods_info['goods_name'],$request['request_name']);
            $wids = self::$check_operator_msg_wid;
            messagesFrom::senMeg($wids,$msg1,$other_data,$remarks);
            $msg2 = messagesFrom::getMsgTxtForImgRequest(9,$goods_info['goods_name'],$request['request_name']);
            messagesFrom::senMeg($operator_wid,$msg2,$other_data,$remarks);
        }
    }

}