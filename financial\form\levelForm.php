<?php
/**
 * @author: zhangguoming
 * @Time: 2024/11/27 14:05
 */

namespace financial\form;

use core\lib\config;
use core\lib\db\dbFMysql;
use financial\models\userModel;

class levelForm
{
    public static array $market_list = [];
    private static array $column_list = [];
    /**
     * @param $db
     * @param $params 请求参数
     * @param $type   1新增，2修改，3删除
     * @return void
     */
    public static function setLog($db,$params,$id,$type) {
        $db->table('goods_level_log')
            ->insert([
                'user_id'=>userModel::$qwuser_id,
                'goods_level_id'=>$id,
                'data_type'=>$type,
                'level_name'=>$params['level_name'],
                'status'=>$params['status'],
                'type'=>$params['type'],
                'rules'=>$params['rules'],
                'start_time'=>$params['start_time'],
                'created_time'=>date('Y-m-y H:i:s')
            ]);

    }

    /**
     * @param $rules
     * @return void 获取规则文本
     */
    public static function getRulestext(array $rules) {
        if (!count(self::$market_list)) {
            $db = dbFMysql::getInstance();
            $market_list = $db->table('market')
                ->field('id, country')
                ->list();
            self::$market_list = array_column($market_list,'country','id');
            $db = dbFMysql::getInstance();
            $column_list = $db->table('column')
                ->where('show_type <> 0')
                ->field('id, column_name')
                ->list();
            self::$column_list = array_column($column_list,'column_name','id');
        }
        $market_list = self::$market_list;
        $rules_month = config::get('level_rules_month','data_financial');//等级规则的月份选择
        $rules_month = array_column($rules_month,'name','id');
        $rules_value = config::get('level_rules_value','data_financial');//等级规则的值选择
        $rules_value = array_column($rules_value,'name','id');
        $rules_symbol = config::get('waring_rules_symbol','data_financial');//计算符号
        $rules_symbol = array_column($rules_symbol,'name','id');
        $rules_text = [];
        foreach ($rules as $k=>$value) {
            $tiem = [];
            if (!empty($value)) {
                if (!count($value['market_id'])) {
                    $tiem['country'] = ['全部国家'];
                } elseif ($value['market_id'][0] == -1) {
                    $tiem['country'] = ['全部国家'];
                } else {
                    $market_l = array_flip($value['market_id']);
                    $market_l = array_intersect_key($market_list,$market_l);
                    $tiem['country'] = array_values($market_l);
                }
                $rule_text = '';
                if (isset($value['conditions'])) {
                    $condition_text = [];
                    foreach ($value['conditions'] as $k=>$condition) {
                        if ($condition['is_group']) { //组合
                            $group_text = [];//每个小组的文本
                            foreach ($condition['group'] as $k2=>$group) {
                                $group_t = self::getSmallRuleText($group, $rules_month, $rules_value, $rules_symbol);
                                if ($k2 > 0) {
                                    $group_t = ($group['type'] == 1?"且":"或").$group_t;
                                }
                                $group_text[] = $group_t;
                            }
                            $condition_t = "(".implode('，',$group_text).")";
                        } else {
                            $group_text = self::getSmallRuleText($condition['group'][0], $rules_month, $rules_value, $rules_symbol);
                            $condition_t = $group_text;
                        }
                        if ($k > 0) {
                            $condition_t = ($condition['type'] == 1?"且":"或").$condition_t;
                        }
                        $condition_text[] = $condition_t;
                    }
                    $rule_text = implode('，',$condition_text);
                }
                $tiem['rule_text'] = $rule_text;
            }
            $rules_text[] = $tiem;
        }
        return $rules_text;
    }
    private static function getSmallRuleText($rule,$rules_month, $rules_value, $rules_symbol) {
        $column_list = self::$column_list;
        $rule_text = '【'.($column_list[$rule['index']]??'未知数据列').'】'.$rules_month[$rule['compare_month']] ?? '未知月份';
        if ($rule['compare_month'] == 2) {
            $rule_text .= "({$rule['Interval_value']})";
        }
        $rule_text .= $rules_value[$rule['reference']] ?? '未知参考值';
        $rule_text .= $rules_symbol[$rule['symbol']] ?? '未知运算符';
        if ($rule['symbol'] == 6) {
            $rule_text .= "[{$rule['value1']},{$rule['value2']}]";
        } else {
            $rule_text .=   $rule['value1'];
        }
        return $rule_text;
    }
    //获取产品等级详情
    public static function getLevelDetail($param)
    {
        // 获取数据库实例
        $db = dbFMysql::getInstance();
        $data = $db->table('goods_level')
            ->where('where id = :id', ['id' => $param['id']])
            ->one();
        //转换时间戳
        if (!empty($data['start_time'])) {
            $data['start_time'] = date('Y-m-d H:i:s', $data['start_time']);
        }
        return $data;
    }

}