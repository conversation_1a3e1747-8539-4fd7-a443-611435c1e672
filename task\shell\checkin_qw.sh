#!/bin/bash
# 处理定时任务
token='576d9908a51f11ee97f19c2dcd695fd0'
#base_url='http://39.101.133.112:8082/'
base_url='http://171.223.214.187:8901/'

last_month=$(date -d "last month" "+%Y-%m")

#打卡规则同步
response=$(curl -s -X POST -d "token=$token&month=$last_month" $base_url"task/checkin/getCheckinOption")
echo "$response"
# 从响应中提取code字段的值
code=$(echo "$response" | grep -oP '"code":\K-?\d+')
echo "code=$code"
echo "checkin group rule finish，runtime：$(date "+%Y-%m-%d %H:%M:%S")"

#假期同步
response=$(curl -s -X POST -d "token=$token&month=$last_month" $base_url"task/checkin/getVacation")
echo "$response"
# 从响应中提取code字段的值
code=$(echo "$response" | grep -oP '"code":\K-?\d+')
echo "code=$code"
echo "checkin vacation finish，runtime：$(date "+%Y-%m-%d %H:%M:%S")"

#审批单同步
values=(
    "3TmmciPWC9yqKH3PEaPKW5nkTYp899ovm2QxsaCx" # 成都请假
    "Bs7uczbiSALR5afcNsZQLCRUa1JbwGhA5SizQj5Z2" # 深圳请假
    "3TmACnDrGDY8ipW91ssqnarVuLZ5pXX2PdqekCaf" # 成都加班
    "C4RYyf8DKd8a9ftbEhCENBkWooDxVid34g6UooTDb" # 深圳加班
    "BsAbKmQWjXVNbNuGNqg6XrwDa3sGar639NyUFMU5d" # 补卡
)


for value in "${values[@]}"; do
    echo "$value"
    new_next_cursor=''
    sp_no_list_array=()
    while :; do
        # 发送请求
        response=$(curl -s -X POST -d "token=$token&value=$value&month=$last_month&next_cursor=$new_next_cursor" "${base_url}task/checkin/getApproval")
        # 输出响应
        echo "$response"
        # 提取 new_next_cursor
        new_next_cursor=$(echo "$response" | grep -oP '"new_next_cursor":"\K[^"]+')
        # 提取 sp_no_list 并追加到数组
        sp_no_list=$(echo "$response" | grep -oP '"sp_no_list":\["\K[^]]+' | tr -d '"')
        if [[ -n "$sp_no_list" ]]; then
              IFS=',' read -ra sp_no_items <<< "$sp_no_list"
              sp_no_list_array+=("${sp_no_items[@]}")
        fi
        # 如果 new_next_cursor 为空，则退出循环
        if [[ -z "$new_next_cursor" ]]; then
            echo "拉取结束"
            break
        fi
        echo "new_next_cursor=$new_next_cursor, 继续请求"
    done

    # 审批单详情
    # 需要控制时间频率
    for sp_no in "${sp_no_list_array[@]}"; do
        echo "$sp_no"
        response=$(curl -s -X POST -d "token=$token&sp_no=$sp_no" $base_url"task/checkin/getApprovalDetail")
        sleep 0.5
    done
done


#打卡数据
response=$(curl -s -X POST -d "token=$token&month=$last_month" $base_url"task/checkin/getUser")
echo "$response"
ids=$(echo "$response" | grep -oP '"id":"\K\d+')
users=$(echo "$response" | grep -oP '"users":\[[^]]+\]')

index=0
for id in $ids; do
    echo "规则ID: $id"
    user_list=$(echo "$users" | sed -n "$((index+1))p" | sed 's/\["//g' | sed 's/"]//g' | sed 's/","/\n/g')
    for user in $user_list; do
        echo "用户ID: $user"
        response=$(curl -s -X POST -d "token=$token&id=$id&user_id=$user&month=$last_month" $base_url"task/checkin/getUserCheckin")
        echo "$response"
        # 从响应中提取code字段的值
        code=$(echo "$response" | grep -oP '"code":\K-?\d+')
        echo "code=$code"
        sleep 0.5
    done
    index=$((index+1))
done

