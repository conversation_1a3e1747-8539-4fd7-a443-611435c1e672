<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/26 13:33
 */

namespace  plugins\goods\form;

use plugins\goods\models\userModel;
use core\lib\db\dbMysql;
use core\lib\log;

class goodsQualityBookFrom
{
    //检测标准书填写记录
    public static function setEditLog($old_quality_book,$new_quality_data,$project){
        $need_log = 0;
        if (!$old_quality_book) {
            $need_log = 1;
            $describe = '检测标准书数据新增。';
        } else {
            $old_quality_data = json_decode($old_quality_book['quality_data'],true);
            $old_row = array_column($old_quality_data,'row_name');
            $new_row = array_column($new_quality_data,'row_name');
            $add_row = array_diff($new_row,$old_row);
            $del_row = array_diff($old_row,$new_row);
            $describe = '检测标准书数据修改。';
            foreach ($old_quality_data as $v) {
                foreach ($new_quality_data as $v1) {
                    if ($v['row_name'] == $v1['row_name']) {
                        if ($v['data'] != $v1['data']) {
                            $need_log = 1;
                            if ($v['type']==1) {
                                $describe.='修改'.$v['row_name']."参数为【{$v1['data']['content']}】；";
                            } else {
                                $describe.='修改'.$v['row_name']."上传图片【".json_encode($v1['data']['imgs'])."】；";
                            }
                        }
                    }
                }
            }
            //新增
            foreach ($new_quality_data as $v) {
                if (in_array($v['row_name'],$add_row)) {
                    $need_log = 1;
                    if ($v['type']==1) {
                        $describe.='新增'.$v['row_name']."参数为【{$v1['data']['content']}】；";
                    } else {
                        $describe.='新增'.$v['row_name']."上传图片【".json_encode($v1['data']['imgs'])."】；";
                    }
                }
            }
            //删除
            foreach ($old_quality_data as $v) {
                if (in_array($v['row_name'],$del_row)) {
                    $need_log = 1;
                    $describe.='删除'.$v['row_name']."；";
                }
            }
        }
        if ($need_log) {
            log::setGoodsProjectLog($project['goods_id'],$project['id'], $describe, $project['matter_name'],5);
        }
    }
    //检测标准书提交验证
    public static function varifyQualityBookTestSubmit(int $project_id,array $goods_info) {
        $db = dbMysql::getInstance();
        $quality_book = $db->table('goods_quality_book')
            ->where('where project_id=:project_id',['project_id'=>$project_id])
            ->one();
        if (!$quality_book) {
            SetReturn(-1,'未找到检测标准书数据');
        }
        if (!$quality_book['quality_data']) {
            SetReturn(-1,'检测标准书中无数据，请检查');
        }
        if (!$quality_book['url']) {
            $pdf_data = self::setQualityBookPdf($quality_book,$goods_info);
            //保存提交事件和地址
            $db->table('goods_quality_book')
                ->where('where id=:id',['id'=>$quality_book['id']])
                ->update([
                    'url'=>$pdf_data['path'],
                    'submit_time'=>date('Y-m-d H:i:s'),
                ]);
            //生成项目文件
            $project = $db->query('select flow_path_id from oa_goods_project where id=:project_id',['project_id'=>$project_id]);
            goodsProjectFileFrom::saveProjectFile('检测标准书',$project['flow_path_id'],$project_id,5,$pdf_data['path'],$goods_info['id']);
        }

    }

    private static function getQualityBookPdfHtml(array $quality_data,$goods_name) {
        $html = "<table style='text-align: center;font-size: 14px;!important;width: 100%'><tr><th colspan=\"3\">$goods_name</th></tr>";
        $html .= '<tr style="width: 100%">
                    <th style="width: 15%">序号</th>
                    <th style="width: 30%">参数名</th>
                    <th style="width: 55%">参数数据</th>
                </tr>';
        foreach ($quality_data as $v) {
            $html .= '<tr>';
            $html .= "<td>{$v['id']}</td>";
            $html .= "<td>{$v['row_name']}</td>";
            $html .= "<td>";
            if ($v['type'] == 2) {
                //图片
                foreach ($v['data']['imgs'] as $img) {
                    $image_path = SELF_FK . $img;
                    $html.= '<img src="' . $image_path . '" style="width:50px;">';
                }
            } else {
                $html.= $v['data']['content'];
            }
            $html .= "</td>";
            $html .= '</tr>';
        }
        $html .= '</table>';
        return $html;
    }

    //生成质检标准书
    static public function setQualityBookPdf($quality_book,$goods_info)
    {
        $quality_data = json_decode($quality_book['quality_data'],true);
        $table_html = self::getQualityBookPdfHtml($quality_data,$goods_info['goods_name']);
        $company = "深圳易威行科技创新有限公司";
        $title = "检测标准书";
        $logo_img = SELF_FK.'/public/static/logo.png';
        //填写的内容
        $mpdf = new \Mpdf\Mpdf();
        $mpdf->autoLangToFont = true;
        $mpdf->autoScriptToLang = true;
        $mpdf->useSubstitutions = true;
        $mpdf->shrink_tables_to_fit = 0;
        // 添加水印图片
        $mpdf->showImageErrors = true;//图片错误就显示
        $img_url = '/public/static/api/seal/quality.png';
        $image_path = SELF_FK . $img_url;
        $mpdf->SetHTMLHeader('
                    <div style="width:160px;height: 160px;position: absolute;right: 50px; top: 0px;">
                        <img src="' . $image_path . '" style="width:100%;">
                    </div>
                ');

        //文件保存路径
        $save_path = "/public/upload/attr_form_pdf/{$quality_book['goods_id']}-{$quality_book['project_id']}/".userModel::$wid;
        $url = SELF_FK.$save_path;
        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path.'/'.time() . '.pdf';
        $url = SELF_FK.$path;
        //pdfhtml数据

        $html = '<style>
                .attr_table table {border-collapse: collapse;border: 1px solid #aaa;}
                .attr_table th,td {border: 1px solid #aaa;padding: 10px 0;}</style>';
        $html .= '<body style="">';
        //公司logo
        $html .= ' <img src="' . $logo_img . '" style="width:40px;position: absolute;left: 50px; top: 0px;">';
        //标题
        $html .= '<h3 style="text-align: center">' . $company . '</h3>';
        $html .= '<h4 style="text-align: center; margin-top: 20px;">' . $title . '</h4>';
        //日期
        $html .= '<div style="text-align: right; margin-top: 20px;">日期：' .date('Y-m-d H:i:s'). '</div>';
        //表单
        $html .= '<div class="attr_table" style="margin-top: 20px;padding: 0 10px; line-height: 30px;">';

        $html .= $table_html;
        $html .= '</div></body>';
        $mpdf->WriteHTML($html);
        // I生成，F保存到本地
        $mpdf->Output($url, 'F');
        return ['title'=>$title,'path'=>$path];
    }
}