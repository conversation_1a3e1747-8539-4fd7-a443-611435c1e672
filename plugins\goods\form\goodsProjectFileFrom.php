<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/28 9:15
 */

namespace  plugins\goods\form;

use plugins\goods\common\publicMethod;
use plugins\goods\models\userModel;
use core\lib\db\dbMysql;

class goodsProjectFileFrom
{
    //流程文件保存，生成的检测报告
    public static function saveProjectFile(string $file_name,int $flow_path_id,int $project_id,int $event_type, string $url, int $goods_id,string $nodex_index=''){
        $db = dbMysql::getInstance();
        if ($event_type == 4) {
            //硬件检测爆改
            $file = $db->table('goods_project_file')
                ->where('where user_id=:user_id and project_id=:project_id and event_type=:event_type and nodex_index=:nodex_index',['user_id'=>userModel::$qwuser_id,'project_id'=>$project_id,'event_type'=>$event_type,'nodex_index'=>$nodex_index])
                ->one();
        } else {
            $file = $db->table('goods_project_file')
                ->where('where user_id=:user_id and project_id=:project_id and event_type=:event_type',['user_id'=>userModel::$qwuser_id,'project_id'=>$project_id,'event_type'=>$event_type])
                ->one();
        }
        if ($file) {
            $db->table('goods_project_file')
                ->where('where id = :id ',['id'=>$file['id']])
                ->update(['src'=>$url]);
        } else {
            $insert_data = [
                'user_id'=>userModel::$qwuser_id,
                'wid'=>userModel::$wid,
                'event_type'=>$event_type,
                'goods_id'=>$goods_id,
                'project_id'=>$project_id,
                'extension'=>'pdf',
                'filename'=>$file_name,
                'created_at'=>date('Y-m-d H:i:s'),
                'flow_path_id'=>$flow_path_id,
                'src'=>$url,
            ];
            if (!empty($nodex_index)) {
                $insert_data['nodex_index'] = $nodex_index;
            }
            $db->table('goods_project_file')
                ->insert($insert_data);
        }
    }
    //事件提交时验证 事件是否已经上传文件
    public static function varifyFileSubmit(int $project_id,string $node_index, string $event_index) {
        $db = dbMysql::getInstance();
        $list = $db->table('goods_project_file')
            ->where('where project_id=:project_id and nodex_index=:nodex_index and event_index=:event_index and is_delete = 0',['project_id'=>$project_id,'nodex_index'=>$node_index,'event_index'=>$event_index])
            ->list();
        if (!count($list)) {
            SetReturn(-1,'请先上传文件');
        }
        foreach ($list as $v) {
            if ($v['extension'] == 'pdf') {
                $user_id = configFrom::getConfigByName('quality_test_user_id');
                if (userModel::$qwuser_id == $user_id) {
                    $new_pdf_url = publicMethod::addPorjectPdfSeal($v['src']);
                    $db->table('goods_project_file')
                        ->where('where id=:id',['id'=>$v['id']])
                        ->update(['src'=>$new_pdf_url]);
                }
            }
        }
    }
}