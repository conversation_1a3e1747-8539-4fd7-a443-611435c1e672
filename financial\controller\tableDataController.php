<?php
/**
 * @author: zhangguoming
 * @Time: 2024/8/14 9:42
 */

namespace financial\controller;

//报表数据汇总记录
use financial\form\tableDataForm;
use financial\models\mskuReportModel;

/**
 * 此类不记录百分比字段，百分比字段需要使用规则计算
 */
class tableDataController
{
    //月度汇总
    public function setMonthTable() {
        $m_date = $_POST['m_date'];
        if (empty($m_date)) {
            returnError('月度最小汇总：请设置月份');
        }
        $year = date('Y',strtotime($m_date));
        $from = new tableDataForm();
        //领星数据
        $field = ['sid','asin','msku','parentAsin','localSku','countryCode','project_id','yunying_id','supplier_id'];
        $group = ['sid','asin','msku','parentAsin','localSku','countryCode','project_id','yunying_id','supplier_id'];
        $lx_list = $from::getLxData($field,$group,$m_date,$year);
        //自定义数据
        $from::arrangeList($lx_list,$m_date,$year);
        returnSuccess([],'更新成功');
    }
}