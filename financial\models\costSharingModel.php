<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/28 11:36
 */

namespace financial\models;

use core\lib\db\dbFMysql;
use financial\common\importNeedDataBase;

class costSharingModel
{
    public static array $dimension_list = ['asin'=>'ASIN','parentAsin'=>'父ASIN','store_name'=>'店铺名','msku'=>'MSKU','sku'=>"SKU",'continents'=>"洲",'country'=>"国家",'project1'=>"项目",'project3'=>"运营组"];
    public static array $dimension = ['asin','parentAsin','store_name','msku','sku','continents','country','project1','project3'];
    public static array $import_key_list = [
        'ASIN'=>'asin',
        '父ASIN'=>'parentAsin',
        '店铺名'=>'store_name',
        'MSKU'=>'msku',
        'SKU'=>'sku',
        '洲'=>'continents',
        '国家'=>'country',
        '项目'=>'project1',//一级
        '运营组'=>'project3',//三级
        '费用名称'=>'cost_name',
        '币种'=>'incon',
        '金额'=>'amount',
        '分摊规则'=>'share_rule',//销量1，2销售额
        '备注'=>'remark',
    ];
    public static array $export_key_list = [
        'sharing_no'=> '费用单',
        'user_wname'=>'上传人',
        'm_date'=>'分摊月份',
        'dimension'=>'维度',
        'cost_name'=>'费用名称',
        'status'=>'状态',
        'amount'=>'金额',
        'share_rule'=>'分摊规则',
        'remark'=>'备注',
        'error_reason'=>'失败原因',
    ];
    //导入表头判断
    public static function verifyExcel($data,$dimension,$m_date) {
        //表头验证(和维度必传验证)
        $request_keys = array_merge($dimension,['cost_name','incon','amount','share_rule','remark']);
        $import_key_list = self::$import_key_list;
        $intersect = array_intersect_key($import_key_list,$data[0]);
        $diff_name = [];
        foreach ($intersect as $k=>$v) {
            if (!in_array($v,$request_keys)) {
                $diff_name[] = $k;
            }
        }
        if (count($diff_name)) {
            returnError('表格缺少列【'.implode('，',$diff_name).'】');
        }
        //表内数据完整性判断
        foreach ($data as $k=>$v) {
            foreach ($v as $key=>$val) {
                if (empty($val)) {
                    returnError('表格第'.($k+1).'行【'.$key.'】数据不能为空');
                }
            }
        }
        //验证费用名称
        $column_array = array_column($data,'费用名称');
        $column_ = importNeedDataBase::getColumnByName($column_array,[1,2,3,4,5]);
        if (!count($column_)) {
            returnError('系统中未找到任何表格相关的【费用名】');
        }
        foreach ($column_array as $v) {
            if (!isset($column_[$v])) {
                returnError("系统中未找到费用名【{$v}】");
            } else {
                if ($column_[$v]['show_type'] != 1) {
                    returnError("费用名【{$v}】为非金额字段，不可分摊");
                } elseif ($column_[$v]['custom_id'] > 0) {
                    returnError('自定义字段【'.$v.'】不可分摊');
                }
            }
        }
        //币种汇率
        $code_array = array_column($data,'币种');
        $routing_ = importNeedDataBase::getRoutingByCode($code_array,$m_date);
        if (!count($routing_)) {
            returnError('系统中未找到任何表格相关的【币种】');
        }
        //洲
        $continents_ = [];
        if (in_array('continents',$dimension)) {
            //国家
            $continents_array = array_column($data,'洲');
            $continents_ = importNeedDataBase::getContinentsByName($continents_array);
            if (!count($continents_)) {
                returnError('系统中未找到任何表格相关的【洲】');
            }
        }
        //国家
        $country_ = [];
        if (in_array('country',$dimension)) {
            //国家
            $country_array = array_column($data,'国家');
            $country_ = importNeedDataBase::getCountryListByName($country_array);
            if (!count($country_)) {
                returnError('系统中未找到任何表格相关的【国家】');
            }
        }
        //项目（1级）
        $project1_ = [];
        if (in_array('project1',$dimension)) {
            //项目获取
            $project_name = array_column($data,'项目');
            $project1_ = importNeedDataBase::getProject3ByName1($project_name);
            if (!count($project1_)) {
                returnError('系统中未找到任何表格相关的【项目】');
            }
        }
        //项目（3级）
        $project3_ = [];
        if (in_array('project3',$dimension)) {
            //部门获取
            $project3_ = importNeedDataBase::getProjectAll();
            if (!count($project3_)) {
                returnError('系统中未找到任何表格相关的【运营组】');
            }
        }
        //店铺
        $seller_ = [];
        if (in_array('store_name',$dimension)) {
            //部门获取
            $store_name_array = array_column($data,'店铺名');
            $seller_ = importNeedDataBase::getSellerByName($store_name_array);
            if (!count($seller_)) {
                returnError('系统中未找到任何表格相关的【店铺名】');
            }
        }
        //表内数据合格性判断
        $new_data = [];
        foreach ($data as $k=>$v) {
            $row_num = $k+1;
            $item = [];
            //费用
            if($v['金额'] == 0){
                returnError("第{$row_num}行数据【金额】不能为0");
            }
            //店铺名称
            if (in_array('store_name',$dimension)) {
                if(!isset($seller_[$v['店铺名']])){
                    returnError("第{$row_num}行数据【店铺名】，未在系统中查到。");
                } else {
                    $item['sid'] = $seller_[$v['店铺名']]['sid'];
                }
            }
            //分摊规则
            if(!in_array($v['分摊规则'],["按销量","按销售额"])){
                returnError("第{$row_num}行数据【分摊规则】不存在，请在【按销量】【按销售额】中设置");
            } else {
                $item['share_rule_type'] = $v['分摊规则']=="按销量"?1:2;
            }
            //费用名称
            if(!isset($column_[$v['费用名称']])){
                returnError("第{$row_num}行数据【费用名称】，未在系统中查到。");
            } else {
                $item['cost_key'] = $column_[$v['费用名称']]['key_name'];
            }
            //币种
            if(!isset($routing_[$v['币种']])){
                returnError("第{$row_num}行数据【币种】，未在系统中查到。");
            } else {
                $item['my_rate'] = $routing_[$v['币种']]['my_rate'];
                //直接将分摊数据保存为人民币
                $item['amount'] = round($v['金额']*$item['my_rate'],2);
            }
            //洲 获取国家集合
            $country_code_array = [];
            if (in_array('continents',$dimension)) {
                if(!isset($continents_[$v['洲']])){
                    returnError("第{$row_num}行数据【洲】，未在系统中查到。");
                } else {
                    $country_code_array = $continents_[$v['洲']]['country_code'];
                    if (!count($country_code_array)) {
                        returnError("第{$row_num}行数据【洲】中没有对应国家的数据");
                    }
                }
            }
            //国家 获取单个国家
            $country_code = '';
            if (in_array('country',$dimension)) {
                if(!isset($country_[$v['国家']])){
                    returnError("第{$row_num}行数据【国家】，未在系统中查到。");
                } else {
                    $item['country_code'] = $country_code = [$country_[$v['国家']]['code']];
                }
            }
            //洲和国家都存在
            if (in_array('country',$dimension) && in_array('continents',$dimension)) {
                $same_country = array_intersect($country_code_array,$country_code);
                if (!count($same_country)) {
                    returnError("第{$row_num}行数据【周】不存在该【国家】。");
                } else {
                    $item['country_code'] = $same_country;
                }
            }
            //项目
            $project3_ids = [];
            if (in_array('project1',$dimension)) {
                if(!isset($project1_[$v['项目']])){
                    returnError("第{$row_num}行数据【项目】，未在系统中查到。");
                } else {
                    foreach ($project1_[$v['项目']]['child'] as $p_c1) {
                        $project_ids = array_column($p_c1['child'],'id');
                        $project3_ids = array_merge($project3_ids,$project_ids);
                    }
                    $item['project_ids'] = $project3_ids;
                }
            }
            $project_id = 0;
            if (in_array('project3',$dimension)) {
                if(!isset($project3_[$v['运营组']])){
                    returnError("第{$row_num}行数据【运营组】，未在系统中查到。");
                } else {
                    $project_id = $project3_[$v['运营组']]['id'];
                    $item['project_ids'] = [$project_id];
                }
            }
            if (in_array('project1',$dimension) && in_array('project3',$dimension)) {
                if (!in_array($project_id,$project3_ids)) {
                    returnError("第{$row_num}行数据【项目】不存在该【运营组】。");
                } else {
                    $item['project_ids'] = [$project_id];
                }
            }
            foreach ($v as $k=>$v) {
                if (isset(self::$import_key_list[$k])) {
                    if (!isset($item[self::$import_key_list[$k]])) {
                        $item[self::$import_key_list[$k]] = $v;
                    }
                }
            }
            //其他的
            $new_data[] = $item;
        }
        return $new_data;
    }

}