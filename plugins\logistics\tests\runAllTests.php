<?php
/**
 * 海外仓备货单SKU拆分项目测试运行器
 * @purpose 运行所有相关测试
 * @Author: System
 * @Time: 2025/07/01
 */

require_once __DIR__ . '/../../../core/fk.php';

echo "🚀 海外仓备货单SKU拆分项目测试套件\n";
echo "=====================================\n\n";

$startTime = microtime(true);
$totalTests = 0;
$passedTests = 0;
$failedTests = 0;

// 测试文件列表
$testFiles = [
    'overseasInboundDetailTest.php' => '海外仓备货单明细模型单元测试',
    'integrationTest.php' => '集成测试'
];

foreach ($testFiles as $testFile => $description) {
    echo "📋 运行测试: {$description}\n";
    echo "文件: {$testFile}\n";
    echo "----------------------------------------\n";
    
    $totalTests++;
    
    try {
        // 捕获输出
        ob_start();
        include __DIR__ . '/' . $testFile;
        $output = ob_get_clean();
        
        echo $output;
        
        // 检查是否有错误
        if (strpos($output, '❌') !== false) {
            $failedTests++;
            echo "❌ 测试失败\n\n";
        } else {
            $passedTests++;
            echo "✅ 测试通过\n\n";
        }
        
    } catch (Exception $e) {
        $failedTests++;
        ob_end_clean();
        echo "❌ 测试执行异常: " . $e->getMessage() . "\n";
        echo "位置: " . $e->getFile() . ":" . $e->getLine() . "\n\n";
    } catch (Error $e) {
        $failedTests++;
        ob_end_clean();
        echo "❌ 测试执行错误: " . $e->getMessage() . "\n";
        echo "位置: " . $e->getFile() . ":" . $e->getLine() . "\n\n";
    }
}

$endTime = microtime(true);
$totalTime = round($endTime - $startTime, 2);

// 输出测试总结
echo "=====================================\n";
echo "📊 测试总结\n";
echo "=====================================\n";
echo "总测试数: {$totalTests}\n";
echo "通过: {$passedTests}\n";
echo "失败: {$failedTests}\n";
echo "总耗时: {$totalTime}秒\n";

if ($failedTests === 0) {
    echo "\n🎉 所有测试通过！项目功能正常。\n";
    exit(0);
} else {
    echo "\n⚠️ 有{$failedTests}个测试失败，请检查相关功能。\n";
    exit(1);
}
