<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/3 15:36
 */

namespace financial\models;

use core\lib\config;
use financial\common\importNeedDataBase;

class customColumnModel
{
    private static array $data_types;
    private static array $symbols;
    private static array $all_coulmn_key;
    public static array $relation_column = [];
    public static array $last_relation_column = [];
    //规则验证
    public static function verifyRule(array $rules,$show_type) {
        if (!count($rules)) {
            return [];
        }
        //可选的字段
        self::$all_coulmn_key = columnModel::getAllKeys();
        //可选的数类型
        $custom_column_data_type = config::get('custom_column_data_type','data_financial');
        self::$data_types = array_column($custom_column_data_type,'id');
        //可选的符号
        $custom_column_symbol = config::get('custom_column_symbol','data_financial');
        self::$symbols = array_column($custom_column_symbol,'id');
        $res_rules = [];
        foreach ($rules as $v) {
            $type = (int)$v['type'];
            if (!in_array($type,[1,2,3])) {
                returnError("规则类型不存在");
            }
            if ($type == 2) {
                if (!count($v['country_code'])) {
                    returnError("国家必选");
                }
            }
            foreach ($v['rules'] as $k1=>$r) {
                //group_type ，1规则，2规则组
                if ($r['group_type'] == 1) {
                    $v['rules'][$k1] = self::verifySingleRule($r);
                } else {
                    if (!isset($r['list'])) {
                        returnError("运算组中不存在数据。");
                    }
                    $v['rules'][$k1]['is_absolute'] = empty($v['rules'][$k1]['is_absolute'])?0:1;
                    foreach ($r['list'] as $k2=>$rr) {
                        $v['rules'][$k1]['list'][$k2] = self::verifySingleRule($rr);
                    }
                }
            }
            $res_rules[] = [
                'type'=>$type,
                'country_code'=>$v['country_code'],
                'rules'=>$v['rules']
            ];
        }
        $type_list = array_column($res_rules,'type');
        if (count($type_list) > 1) {
            if (!in_array(2,$type_list) || !in_array(3,$type_list)) {
                returnError('规则异常');
            }
            if (end($type_list) != 3) {
                returnError('规则异常');
            }
            if (in_array(1,$type_list)) {
                returnError('规则异常');
            }
            if ($show_type == 2) {
                returnError('利率字段规则不可有国家之分');
            }
        } else {
            if ($type_list[0] != 1) {
                returnError('规则异常');
            }
        }

        self::$last_relation_column = array_values(array_unique(self::$last_relation_column));
        self::$relation_column = array_values(array_unique(self::$relation_column));
        return $res_rules;
    }

    //单条规则验证
    private static function verifySingleRule($single_r) {
        //符号验证
        if (!in_array($single_r['symbol'],self::$symbols)) {
            returnError("运算符号有误");
        }
        //type 1本月，2上月，3自定义值,数据类型判断
        if (!in_array($single_r['type'],self::$data_types)) {
            returnError("数据类型不存在");
        }
        //字段验证
        if ($single_r['type'] != 3) {
            if (!in_array($single_r['coulmn_key'],self::$all_coulmn_key)) {
                returnError("字段{$single_r['coulmn_key']}不存在");
            }
            if ($single_r['type'] == 2) {
                self::$last_relation_column[] = $single_r['coulmn_key'];
            } else {
                self::$relation_column[] = $single_r['coulmn_key'];
            }
        }
        //数据类型验证
        $single_r['val'] = round($single_r['val'],4);
        $single_r['is_absolute'] = (int)$single_r['is_absolute']==0?0:1;
        return $single_r;
    }
}