<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/12 14:09
 */

namespace task\controller;

use Cassandra\Set;
use core\lib\db\dbFMysql;
use core\lib\log;
use core\lib\rediskeys;
use financial\common\lingXingApiBase;
use financial\form\checkoutForm;
use task\form\lixingXingApiForm;
use task\form\lixingXingApiNewForm;

class lingXingApiNewController extends lingXingApiBase
{
    public function __construct()
    {
        $token = $_POST['token']??'';
        if ($token != '01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0') {
            returnSuccess($_POST,'token验证失败555');
        }
    }
    //msku报告 数据同步
    public function synMskuReport() {
        $page_size = 100;
        $redis_key = rediskeys::$oa_syn_msku_report;
        $date = $_POST['date'] ?? '';
        if (!$date) {
            SetReturn(2,'请设置月份');
        }
        checkoutForm::addMdate($date);
        $method = '/bd/profit/report/open/report/msku/list';
        $redis = (new \core\lib\predisV())::$client;
//        $redis->del($redis_key);
//        SetReturn(2,'同步成功');
        if ($redis->exists($redis_key)) {
            $r_data = json_decode($redis->get($redis_key),true);
            $page = $r_data['page'] + 1;
        } else {
            SetReturn(2,'同步成功');
            $page = 1;
        }
        $offset = ($page - 1)*$page_size;
        $param = [
            'offset'=>$offset,
            'length'=>$page_size,
            'monthlyQuery'=>1,
            'startDate'=>$date,
            'endDate'=>$date,
            'summaryEnabled'=>1,
            'currencyCode'=>'CNY',
        ];
        $queryInfo = self::bizBuild($param);
        $url = $queryInfo['url'].$method.'?'.$queryInfo['str'].'&sign='.urlencode($queryInfo['sign']);
        $ret = requestLingXing($url,$queryInfo['body']);
        $data = self::errorCheck($ret);
//        log::lingXingApi('MskuReport')->info('同步'.$date.'msku报告数据'.json_encode($data['data']));
        if (count($data['data'])) {
            if (count($data['data']['records'])) {
                lixingXingApiNewForm::saveMskuReport($data['data']['records'],$date,$page);
            }
        }
        $offset += count($data['data']['records']);
        if ($offset >= $data['data']['total']) {
            $redis->del($redis_key);
            lixingXingApiNewForm::setMskuLog($date);
            self::countAginGoodsDataAndSockt($redis,$date);
            SetReturn(2,'同步成功');
        } else {
            $msg = "msku报表数据同步：已同步{$offset}条，一共{$data['data']['total']}条";
            $r_data = [
                'page'=>$page,
                'month'=>$r_data['month']??'',
                'total'=>$data['data']['total'],
                'mesage'=>$msg
            ];
            $redis->set($redis_key,json_encode($r_data));
            $redis->exists(0.5*60*60);
            returnSuccess([],$msg);
        }
    }

    //重新匹配或者商品增量数据
    public function countAginGoodsDataAndSockt($redis,$m_date) {
        //跟新原始数据集合
        $targetFile = SELF_FK . '/task/shell/count_msku_month_original_data.sh ' . $m_date . '  > /dev/null 2>&1 &';
        shell_exec($targetFile);
//        //增量数据
//        $redis_key = rediskeys::$oa_count_goods_data.$m_date;
//        $r_data = [
//            'page'=>1,
//            'total'=>0,
//            'message'=>$m_date.'增量数据正在准备重新匹配'
//        ];
//        $redis->set($redis_key,json_encode($r_data));
//        $redis->expire($redis_key,0.5*60*60);
//        $targetFile = SELF_FK . '/task/shell/count_goods_data_agin.sh ' . $m_date . '  > /dev/null 2>&1 &';
        //库存更新
//        shell_exec($targetFile);
        $redis_key = rediskeys::$oa_count_goods_stock.$m_date;
        $r_data = [
            'page'=>1,
            'total'=>0,
            'message'=>$m_date.'库存数据正在准备重新匹配'
        ];
        $redis->set($redis_key,json_encode($r_data));
        $redis->expire($redis_key,0.5*60*60);
        $targetFile = SELF_FK . '/task/shell/count_goods_stock_agin.sh ' . $m_date . '  > /dev/null 2>&1 &';
        shell_exec($targetFile);
    }
}