-- 领星FBA货件原始数据表
-- 用于存储从领星API获取的原始数据，只读不改
-- 数据库：dbErpMysql

CREATE TABLE `lingxing_fba_inbound_shipment` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shipment_sn` varchar(100) NOT NULL DEFAULT '' COMMENT '发货单号',
  `shipment_data` longtext COMMENT '完整API返回数据JSON',
  `relate_list` longtext COMMENT '关联货件列表JSON',
  `logistics_data` text COMMENT '物流信息JSON',
  
  -- API原始字段
  `original_id` int(11) NOT NULL DEFAULT '0' COMMENT '领星发货单ID',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '发货单状态 -1:待配货 0:待发货 1:已发货 2:已完成 3:已作废',
  `wname` varchar(200) NOT NULL DEFAULT '' COMMENT '仓库名称',
  `wid` int(11) NOT NULL DEFAULT '0' COMMENT '仓库ID',
  `create_time` varchar(50) NOT NULL DEFAULT '' COMMENT '创建时间',
  `gmt_create` varchar(50) NOT NULL DEFAULT '' COMMENT '创建时间(精确到时分秒)',
  `shipment_time` varchar(50) NOT NULL DEFAULT '' COMMENT '发货时间',
  `shipment_time_second` varchar(50) NOT NULL DEFAULT '' COMMENT '发货时间(精确到时分秒)',
  `logistics_provider_id` varchar(50) NOT NULL DEFAULT '' COMMENT '物流商ID',
  `logistics_provider_name` varchar(200) NOT NULL DEFAULT '' COMMENT '物流商名称',
  `logistics_channel_name` varchar(200) NOT NULL DEFAULT '' COMMENT '物流渠道名称',
  `method_id` varchar(50) NOT NULL DEFAULT '' COMMENT '运输方式ID',
  `method_name` varchar(200) NOT NULL DEFAULT '' COMMENT '运输方式名称',
  `expected_arrival_date` varchar(50) NOT NULL DEFAULT '' COMMENT '到货时间',
  `etd_date` varchar(50) NOT NULL DEFAULT '' COMMENT '开船时间',
  `eta_date` varchar(50) NOT NULL DEFAULT '' COMMENT '预计到港时间',
  `delivery_date` varchar(50) NOT NULL DEFAULT '' COMMENT '实际妥投时间',
  `create_user` varchar(100) NOT NULL DEFAULT '' COMMENT '创建用户',
  `shipment_user` varchar(100) NOT NULL DEFAULT '' COMMENT '发货人',
  `remark` text COMMENT '备注',
  `destination_fulfillment_center_id` varchar(200) NOT NULL DEFAULT '' COMMENT '物流中心编码',
  `status_name` varchar(50) NOT NULL DEFAULT '' COMMENT '状态名称',
  `head_fee_type` int(11) NOT NULL DEFAULT '0' COMMENT '头程费分配方式 0:按计费重 1:按实重 2:按体积重 3:按SKU数量 4:自定义 5:按箱子体积',
  `head_fee_type_name` varchar(100) NOT NULL DEFAULT '' COMMENT '头程分摊名称',
  `is_pick` int(11) NOT NULL DEFAULT '0' COMMENT '拣货状态 0:未拣货 1:已拣货',
  `is_print` int(11) NOT NULL DEFAULT '0' COMMENT '是否打印 0:否 1:是',
  `pick_time` varchar(50) NOT NULL DEFAULT '' COMMENT '拣货时间',
  `print_num` int(11) NOT NULL DEFAULT '0' COMMENT '打印次数',
  `file_id` varchar(100) NOT NULL DEFAULT '' COMMENT '附件文件',
  `is_return_stock` int(11) NOT NULL DEFAULT '0' COMMENT '是否恢复库存 0:否 1:是',
  `pay_status` int(11) NOT NULL DEFAULT '0' COMMENT '付款状态 0:未申请 1:已申请 2:部分付款 3:已付清 4:无',
  `audit_status` int(11) NOT NULL DEFAULT '0' COMMENT '审批状态 121:待审核 122:驳回 123:通过 124:作废',
  `is_exist_declaration` int(11) NOT NULL DEFAULT '0' COMMENT '是否关联报关单 0:否 1:是',
  `is_exist_clearance` int(11) NOT NULL DEFAULT '0' COMMENT '是否关联清关单 0:否 1:是',
  `third_party_order_mode` int(11) NOT NULL DEFAULT '0' COMMENT '下单模式 0:无 1:系统下单 2:手工下单',
  `third_party_order_status` int(11) NOT NULL DEFAULT '0' COMMENT '第三方仓下单状态 1:未下单 2:已下单 3:异常 4:已发货',
  `vat_code` varchar(100) NOT NULL DEFAULT '' COMMENT '店铺VAT税号',
  `is_custom_shipment_time` int(11) NOT NULL DEFAULT '0' COMMENT '是否自定义发货时间 1:是 0:否',
  `update_time` varchar(50) NOT NULL DEFAULT '' COMMENT '更新时间',
  `is_delete` int(11) NOT NULL DEFAULT '0' COMMENT '删除状态 0:未删除 1:已删除',
  
  -- 系统字段
  `sync_date` date NOT NULL COMMENT '同步日期',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-否 1-是',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_shipment_sn` (`shipment_sn`, `is_deleted`) COMMENT '发货单号唯一索引',
  KEY `idx_original_id` (`original_id`) COMMENT '原始ID索引',
  KEY `idx_sync_date` (`sync_date`) COMMENT '同步日期索引',
  KEY `idx_wid` (`wid`) COMMENT '仓库ID索引',
  KEY `idx_status` (`status`) COMMENT '状态索引',
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='领星FBA货件原始数据表';
