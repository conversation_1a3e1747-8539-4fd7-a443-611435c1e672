<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/2 11:36
 */

namespace  plugins\goods\controller;

use plugins\goods\form\goodsMattersFrom;
use plugins\goods\form\messagesFrom;
use plugins\goods\models\combinedProjectModel;
use plugins\goods\models\goodsMattersModel;
use plugins\goods\models\userModel;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;

class goodsMattersController
{
    //待办列表
//    public function getList(){
//        $paras_list = array('matter_name', 'status', 'create_type','node_id','agent_qwuser_id','create_qwuser_id','expected_time','created_time','is_advance_submit','order_by','page_size','page','complete_time');
//        $param = arrangeParam($_POST, $paras_list);
//        $db = dbMysql::getInstance();
//        $db->table('goods_matters','a');
//        $db->where('where qwuser_id = :qwuser_id',['qwuser_id'=>userModel::$qwuser_id]);
//        if ($param['status'] > -1) {
//            if ($param['status'] == 0) {
//                $db->andWhere('and ((a.status=0 and a.is_advance_submit=0) or a.is_advance_submit=1)');
//            }
//            if ($param['status'] == 1) {
//                $db->andWhere('and a.status=1 and (a.is_advance_submit=2 or a.is_advance_submit=0)');
//            }
//            if ($param['status'] == 3) {
//                $db->andWhere('and a.status=3');
//            }
//        }
//        if ($param['is_advance_submit'] > -1) {
//            $db->andWhere('and a.is_advance_submit =:is_advance_submit',['is_advance_submit'=>(int)$param['is_advance_submit']]);
//        }
//        if (!empty($param['matter_name'])) {
//            $db->andWhere('and a.matter_name like :matter_name',['matter_name'=>'%'.$param['matter_name'].'%']);
//        }
//        if ($param['create_type'] > -1) {
//            if ($param['create_type'] == 0) {
//                $db->andWhere('and (a.create_type=0 or  a.create_type=3) and a.is_advance_submit=0');
//            } else {
//                $db->andWhere('and a.create_type = :create_type and a.is_advance_submit=0',['create_type'=>$param['create_type']]);
//            }
//        }
//        if ($param['node_id'] > -1) {
//            $db->andWhere('and a.node_id = :node_id',['node_id'=>$param['node_id']]);
//        }
//        if ((int)$param['agent_qwuser_id'] > 0) {
//            $db->andWhere('and a.qwuser_id = :qwuser_id',['qwuser_id'=>$param['agent_qwuser_id']]);
//        }
//        if ((int)$param['create_qwuser_id'] > 0) {
//            $db->andWhere('and a.user_id = :user_id',['user_id'=>$param['create_qwuser_id']]);
//        }
//        if (!empty($param['expected_time'])) {
//            $param['expected_time'] = json_decode($param['expected_time']);
//            $start_time = strtotime($param['expected_time'][0]);
//            $end_time = strtotime($param['expected_time'][1]);
//            $db->andWhere('and a.expected_time >= :start_time and a.expected_time <= :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
//        }
//        if (!empty($param['created_time'])) {
//            $param['created_time'] = json_decode($param['created_time']);
//            $start_time = strtotime($param['created_time'][0]);
//            $end_time = strtotime($param['created_time'][1]);
//            $db->andWhere('and a.created_at >= :start_time and a.created_at <= :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
//        }
//        if (!empty($param['complete_time'])) {
//            $param['complete_time'] = json_decode($param['complete_time']);
//            $start_time = strtotime($param['complete_time'][0]);
//            $end_time = strtotime($param['complete_time'][1]);
//            $db->andWhere('and a.completion_time >= :start_time and a.completion_time <= :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
//        }
//        $order_str = '';
//        if (!empty($param['order_by'])) {
//            $order_by = json_decode($param['order_by'],true);
//            foreach ($order_by as $k=>$ord){
//                $order_str .= 'a.'.$k.' '.$ord.',';
//            }
//        }
//        if (empty($order_str)) {
//            $db->order('a.id desc');
//        } else {
//            $db->order(trim($order_str,','));
//        }
//        $db->leftJoin('qwuser','b','b.id = a.user_id');
//        $db->leftJoin('qwuser','c','c.id = a.qwuser_id');
//        $db->leftJoin('goods_project','d','d.id = a.goods_project_id');
//        $db->field('a.id,a.is_advance_submit,a.after_expected_time,a.after_delay_hour,a.goods_project_id,a.matter_name,a.type,a.create_type,a.goods_id,a.status,a.node_name,a.event_name,a.event_type,a.expected_day,b.wname as created_wname,c.wname as agent_wname,a.created_at,d.expected_day as project_expected_day,a.expected_time,d.created_time as project_created_time,d.status as project_status,a.completion_time');
//        $list = $db->pages($param['page'],$param['page_size']);
//        foreach ($list['list'] as &$v) {
//            if (!empty($v['event_name'])) {
//                $v['node_name'] = $v['node_name'].'/'.$v['event_name'];
//            }
//            $v['expected_time'] = empty($v['expected_time'])?'':$v['expected_time'];
//        }
//        $list['export_key'] = goodsMattersModel::$export_key;
//        returnSuccess($list);
//    }
    //待办数量统计
//    public function getCountList(){
//        $db = dbMysql::getInstance();
//        //待办全部
//        $all_count_agent = $db->query('select count(1) from oa_goods_matters where qwuser_id = :qwuser_id',['qwuser_id'=>userModel::$qwuser_id])['count(1)'];
//        //待办全部
//        $count_agent_all = $db->query('select count(1) from oa_goods_matters where qwuser_id = :qwuser_id and ((status=0 and is_advance_submit=0) or is_advance_submit=1)',['qwuser_id'=>userModel::$qwuser_id])['count(1)'];
//        //待办-办理
//        $wait_agent = $db->query('select count(1) from oa_goods_matters where qwuser_id = :qwuser_id and ((status=0 and is_advance_submit=0) or (status=1 and is_advance_submit=1)) and (create_type = 0 or create_type=3) and is_advance_submit=0',['qwuser_id'=>userModel::$qwuser_id])['count(1)'];
//        //已办-办理全部
//        $had_agent_all = $db->query('select count(1) from oa_goods_matters where qwuser_id = :qwuser_id and status = 1',['qwuser_id'=>userModel::$qwuser_id])['count(1)'];
//        //已办-办理
//        $had_agent = $db->query('select count(1) from oa_goods_matters where qwuser_id = :qwuser_id and status = 1 and create_type = 0 and (is_advance_submit = 0 or is_advance_submit = 2)',['qwuser_id'=>userModel::$qwuser_id])['count(1)'];
//        //已关闭
//        $colsed_agent = $db->query('select count(1) from oa_goods_matters where qwuser_id = :qwuser_id and status=3',['qwuser_id'=>userModel::$qwuser_id])['count(1)'];
//        //待审批
//        $count_wait_approval = $db->query('select count(1) from oa_goods_matters where qwuser_id = :qwuser_id and status = 0 and create_type = 2',['qwuser_id'=>userModel::$qwuser_id])['count(1)'];
//        $approvaled_count = $db->query('select count(1) from oa_goods_matters where qwuser_id = :qwuser_id and status = 1 and create_type = 2',['qwuser_id'=>userModel::$qwuser_id])['count(1)'];
//        //待审核
//        $count_wait_check = $db->query('select count(1) from oa_goods_matters where qwuser_id = :qwuser_id and status = 0 and create_type = 1',['qwuser_id'=>userModel::$qwuser_id])['count(1)'];
//        $agent_checked = $db->query('select count(1) from oa_goods_matters where qwuser_id = :qwuser_id and status = 1 and create_type = 1',['qwuser_id'=>userModel::$qwuser_id])['count(1)'];
//        //抄送
//        $count_copy = $db->query('select count(1) from oa_messages where qw_userid=:qw_userid and type = 2 and is_read = 0',['qw_userid'=>userModel::$wid])['count(1)'];
//        $count_copy_all = $db->query('select count(1) from oa_messages where qw_userid=:qw_userid and type = 2',['qw_userid'=>userModel::$wid])['count(1)'];
//        //暂停
//        $count_stop = $db->query('select count(1) from oa_combined_project where is_stop = 1')['count(1)'];
//        //延时任务
//        $wait_delay_matter_count = $db->query('select count(1) from oa_goods_matters where qwuser_id = :qwuser_id and is_advance_submit = 1',['qwuser_id'=>userModel::$qwuser_id])['count(1)'];
//        //延时任务已办理
//        $delay_matter_count = $db->query('select count(1) from oa_goods_matters where qwuser_id = :qwuser_id and is_advance_submit = 2',['qwuser_id'=>userModel::$qwuser_id])['count(1)'];
//
//        //申请的流程数量
//        $applay_project_count = $db->query('select count(1) from oa_combined_project where user_id = :user_id and flow_path_id<>1',['user_id'=>userModel::$qwuser_id])['count(1)'];
//        //申请的流程数量未完成
//        $applay_project_not_count = $db->query('select count(1) from oa_combined_project where user_id = :user_id and (status=0 or status=1 or status=3) and flow_path_id<>1',['user_id'=>userModel::$qwuser_id])['count(1)'];
//        $data = [
//            'agent_all'=>$all_count_agent,
//            'wait_agent_all'=>$count_agent_all,
//            'wait_agent'=>$wait_agent,
//            'had_agent_all'=>$had_agent_all,
//            'had_agent'=>$had_agent,
//            'wait_agent_check'=>$count_wait_check,
//            'agent_checked'=>$agent_checked,
//            'count_wait_approval'=>$count_wait_approval,
//            'approvaled_count'=>$approvaled_count,
//            'project_copy'=>$count_copy,
//            'project_copy_all'=>$count_copy_all,
//            'project_stop'=>$count_stop,
//            'wait_delay_matter_count'=>$wait_delay_matter_count,
//            'delay_matter_count'=>$delay_matter_count,
//            'colsed_agent'=>$colsed_agent,
//            'applay_project_count'=>$applay_project_count,
//            'applay_project_not_count'=>$applay_project_not_count,
//        ];
//        returnSuccess($data);
//    }
    //提交待办事件
//    public function submitMatters() {
//        $id = $_POST['id'];
//        $db = dbMysql::getInstance();
//        $db->beginTransaction();
//        try {
//            goodsMattersFrom::submitMattersById($id,0);
//            if (goodsMattersFrom::$submit_result_error_count > 0) {
//                SetReturn(-1,goodsMattersFrom::$submit_result[0]['msg']);
//            }
//            $db->commit();
//            returnSuccess('','提交成功');
//        } catch (ExceptionError $error) {
//            $db->rollBack();
//            throw new ExceptionError($error->getMessage());
//        }
//    }
    //提交待办事项多个
//    public function submitMuchMatters() {
//        $ids = $_POST['ids'];
//        if (empty($ids)) {
//            SetReturn(-1,'请选择需要提交待办事项');
//        }
//        $ids = json_decode($ids,true);
//        if (!count($ids)) {
//            SetReturn(-1,'请选择需要提交的待办事项');
//        }
//        $db = dbMysql::getInstance();
//        try {
//            $error_count = 0;
//            foreach ($ids as $id) {
//                $db->beginTransaction();
//                goodsMattersFrom::submitMattersById($id,0);
//                if (goodsMattersFrom::$submit_result_error_count > $error_count) {
//                    $error_count = goodsMattersFrom::$submit_result_error_count;
//                    $db->rollBack();
//                } else {
//                    $db->commit();
//                }
//            }
//            returnSuccess(goodsMattersFrom::$submit_result,'提交成功');
//        } catch (ExceptionError $error) {
//            $db->rollBack();
//            throw new ExceptionError($error->getMessage());
//        }
//    }
    //催办 一个或者多个
//    public function matterAgentRemind () {
//        $paras_list = array('ids', 'remarks');
//        $param = arrangeParam($_POST, $paras_list);
//        $ids = json_decode($param['ids'],true);
//        if (count($ids) == 0) {
//            SetReturn(-1,'请选择要催办的事项');
//        }
//        $remind_result = [];
//
//        $db = dbMysql::getInstance();
//        $matter_list = $db->table('goods_matters','a')
//            ->leftJoin('qwuser','b','b.id = a.qwuser_id')
//            ->where('where a.status = 0')
//            ->whereIn('a.id',$ids)
//            ->field('a.*,b.wid')
//            ->list();
//        foreach ($matter_list as $v) {
//            if ($v['status'] == 1) {
//                $remind_result[] = [
//                    'id'=>$v['id'],
//                    'matter_name'=>$v['matter_name'],
//                    'node_name'=>$v['node_name'],
//                    'success'=>0,
//                    'msg'=>'已完成',
//                ];
//            } else {
//                $remind_msg[] = [
//                    'wids'=>[$v['wid']],
//                    'msg'=>messagesFrom::getMsgTxt(4,$v['matter_name'],$v['node_name'],$v['event_name']),
//                    'other_data'=>[
//                        'user_id'=>userModel::$qwuser_id,
//                        'model_id'=>($v['goods_project_id']>0?$v['goods_project_id']:$v['model_id']),
//                        'node_index'=>$v['node_index'],
//                        'event_index'=>$v['event_index'],
//                        'msg_type'=>4
//                    ],
//                ];
//                $remind_result[] = [
//                    'id'=>$v['id'],
//                    'matter_name'=>$v['matter_name'],
//                    'node_name'=>$v['node_name'],
//                    'success'=>1,
//                    'msg'=>'',
//                ];
//            }
//
//        }
//        foreach ($remind_msg as $msg) {
//            messagesFrom::senMeg($msg['wids'],$msg['msg'],$msg['other_data'],$param['remarks']);
//        }
//        returnSuccess($remind_result,'催办消息已发送');
//    }
//    //交办
//    public function changeMatterAgent() {
//        $paras_list = array('ids', 'agent_user', 'remarks');
//        $request_list = ['ids'=>'事项id', 'agent_user'=>'交办人'];
//        $param = arrangeParam($_POST, $paras_list, $request_list);
//        $ids = json_decode($param['ids'],true);
//        $agent_user = json_decode($param['agent_user'],true);
//        $agent_user = $agent_user[0];
//        if (!count($agent_user)) {
//            SetReturn(-1,'请选择交办人');
//        }
//        if (!count($agent_user) > 1) {
//            SetReturn(-1,'交办人只能是一人');
//        }
//        $db = dbMysql::getInstance();
//        $matter_list = $db->table('goods_matters','a')
//            ->leftJoin('qwuser','b','b.id = a.qwuser_id')
//            ->where('where a.status = 0')
//            ->whereIn('a.id',$ids)
//            ->field('a.*,b.wid')
//            ->list();
//        $db->beginTransaction();
//        try {
//            foreach ($matter_list as $v) {
//                goodsMattersFrom::changeMatterAgentById($v,$agent_user);
//            }
//            $change_matter_msg = goodsMattersFrom::$change_matter_msg;
//            if (count($change_matter_msg)) {
//                foreach ($change_matter_msg as $msg) {
//                    messagesFrom::senMeg($msg['wids'],$msg['msg'],$msg['other_data'],$param['remarks']);
//                }
//            }
//            if (count($ids) == 1 && goodsMattersFrom::$submit_result_error_count) {
//                $db->rollBack();
//                SetReturn(-1,goodsMattersFrom::$submit_result[0]['msg']);
//            }
//            $db->commit();
//            returnSuccess(goodsMattersFrom::$submit_result,'交办成功');
//        } catch (ExceptionError $error) {
//            $db->rollBack();
//            throw new ExceptionError($error->getMessage());
//        }
//    }
    //导出
//    public function exportList() {
//        $paras_list = array('matter_name', 'status', 'create_type','node_id','agent_qwuser_id','create_qwuser_id','expected_time','created_time','ids','export_key','is_advance_submit');
//        $param = arrangeParam($_POST, $paras_list);
//        $db = dbMysql::getInstance();
//        $db->table('goods_matters','a');
//        $db->where('where qwuser_id = :qwuser_id',['qwuser_id'=>userModel::$qwuser_id]);
//        if ($param['status'] > -1) {
//            if ($param['status'] == 0) {
//                $db->andWhere('and ((a.status=0 and a.is_advance_submit=0) or a.is_advance_submit=1)');
//            }
//            if ($param['status'] == 1) {
//                $db->andWhere('and a.status=1 and (a.is_advance_submit=2 or a.is_advance_submit=0)');
//            }
//            if ($param['status'] == 3) {
//                $db->andWhere('and a.status=3');
//            }
//        }
//        if ($param['is_advance_submit'] > -1) {
//            $db->andWhere('and a.is_advance_submit =:is_advance_submit',['is_advance_submit'=>(int)$param['is_advance_submit']]);
//        }
//        if (!empty($param['matter_name'])) {
//            $db->andWhere('and a.matter_name like :matter_name',['matter_name'=>'%'.$param['matter_name'].'%']);
//        }
//        if ($param['create_type'] > -1) {
//            if ($param['create_type'] == 0) {
//                $db->andWhere('and (a.create_type=0 or  a.create_type=3) and a.is_advance_submit=0');
//            } else {
//                $db->andWhere('and a.create_type = :create_type and a.is_advance_submit=0',['create_type'=>$param['create_type']]);
//            }
//        }
//        if ($param['node_id'] > -1) {
//            $db->andWhere('and a.node_id = :node_id',['node_id'=>$param['node_id']]);
//        }
//        if ((int)$param['agent_qwuser_id'] > 0) {
//            $db->andWhere('and a.qwuser_id = :qwuser_id',['qwuser_id'=>$param['agent_qwuser_id']]);
//        }
//        if ((int)$param['create_qwuser_id'] > 0) {
//            $db->andWhere('and a.user_id = :user_id',['user_id'=>$param['create_qwuser_id']]);
//        }
//        if (!empty($param['expected_time'])) {
//            $param['expected_time'] = json_decode($param['expected_time']);
//            $start_time = strtotime($param['expected_time'][0]);
//            $end_time = strtotime($param['expected_time'][1]);
//            $db->andWhere('and a.expected_time >= :start_time and a.expected_time <= :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
//        }
//        if (!empty($param['created_time'])) {
//            $param['created_time'] = json_decode($param['created_time']);
//            $start_time = strtotime($param['created_time'][0]);
//            $end_time = strtotime($param['created_time'][1]);
//            $db->andWhere('and a.created_at >= :start_time and a.created_at <= :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
//        }
//        if (!empty($param['complete_time'])) {
//            $param['complete_time'] = json_decode($param['complete_time']);
//            $start_time = strtotime($param['complete_time'][0]);
//            $end_time = strtotime($param['complete_time'][1]);
//            $db->andWhere('and a.completion_time >= :start_time and a.completion_time <= :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
//        }
//        $db->leftJoin('qwuser','b','b.id = a.user_id');
//        $db->leftJoin('qwuser','c','c.id = a.qwuser_id');
//        $export_key = empty($param['export_key'])?'[]':$param['export_key'];
//        $export_key = json_decode($export_key);
//        if (!count($export_key)) {
//            SetReturn(-1,'请选择要导出的字段');
//        }
//        $db->field('a.id,a.matter_name,a.type,a.create_type,a.status,a.node_name,a.event_name,a.expected_day,b.wname as created_wname,c.wname as agent_wname,a.created_at,a.expected_time,a.completion_time');
//        $ids = empty($param['ids'])?'[]':$param['ids'];
//        $ids = json_decode($ids);
//        if (count($ids)) {
//            $db->whereIn('a.id',$ids);
//        }
//        $list = $db->list();
//        if (!count($list)) {
//            SetReturn(-1,'未查询到可导出的数据');
//        }
//        $url = goodsMattersModel::export($list,$export_key);
//        returnSuccess(['url'=>$url]);
//    }

//    //获取全部流程
//    public function getAllProject() {
//        $paras_list = array('matter_name', 'status','is_stop', 'is_apply','expected_time','created_time','order_by','page_size','page','is_me_created','is_me_part','complete_time','is_abolish');
//        $param = arrangeParam($_POST, $paras_list);
//        $is_apply = (int)$param['is_apply'];
//        $is_me_created = (int)$param['is_me_created'];
//        $is_me_part = (int)$param['is_me_part'];
//        $is_abolish = (int)$param['is_abolish'];
//        $db = dbMysql::getInstance();
//        $project_ids = [];
//        if ($is_me_part) {
//            $participant = $db->table('goods_project_participant')
//                ->where('where qwuser_id=:qwuser_id',['qwuser_id'=>userModel::$qwuser_id])
//                ->field('project_id')
//                ->list();
//            $project_ids = array_column($participant,'project_id');
//        }
//        $db->table('combined_project','a')
//            ->leftJoin('qwuser','b','b.id=a.user_id')
//            ->where('where 1=1');
//        if (!empty($param['matter_name'])) {
//            $db->andWhere('and a.matter_name like :matter_name',['matter_name'=>'%'.$param['matter_name'].'%']);
//        }
//        if ($param['status'] > -1) {
//            $db->andWhere('and a.status=:status',['status'=>$param['status']]);
//        }
//        if ($is_apply) {
//            $db->andWhere('and a.user_id=:user_id and flow_path_id<>1',['user_id'=>userModel::$qwuser_id]);
//        }
//        if ($is_me_created) {
//            $db->andWhere('and a.user_id=:user_id',['user_id'=>userModel::$qwuser_id]);
//        }
//        if ($is_abolish > -1) {
//            if ($is_abolish) {
//                $db->andWhere('and a.status = 4');
//            } else {
//                $db->andWhere('and a.status <> 4');
//            }
//        }
//        if ($is_me_part) {
//            $db->whereIn('a.project_id',$project_ids);
//        }
//        if (!empty($param['expected_time']) && $param['expected_time']!='null' && $param['expected_time']!='[]') {
//            $param['expected_time'] = json_decode($param['expected_time']);
//            $start_time = strtotime($param['expected_time'][0]);
//            $end_time = strtotime($param['expected_time'][1]);
//            $db->andWhere('and (a.created_time+a.expected_day*24*60*60) > :start_time and (a.created_time+a.expected_day*24*60*60) < :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
//        }
//        if (!empty($param['created_time']) && $param['created_time']!='null' && $param['created_time']!='[]') {
//            $param['created_time'] = json_decode($param['created_time']);
//            $start_time = strtotime($param['created_time'][0]);
//            $end_time = strtotime($param['created_time'][1]);
//            $db->andWhere('and a.created_time > :start_time and a.created_time < :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
//        }
//        if (!empty($param['complete_time'])) {
//            $param['complete_time'] = json_decode($param['complete_time']);
//            if (count($param['complete_time'])) {
//                $start_time = strtotime($param['complete_time'][0]);
//                $end_time = strtotime($param['complete_time'][1]);
//                $db->andWhere('and a.complete_time >= :start_time and a.complete_time <= :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
//            }
//        }
//        $order_str = '';
//        if (!empty($param['order_by'])) {
//            $order_by = json_decode($param['order_by'],true);
//            foreach ($order_by as $k=>$ord){
//                $order_str .= 'a.'.$k.' '.$ord.',';
//            }
//        }
//        if (empty($order_str)) {
//            $db->order('a.created_time desc');
//        } else {
//            $db->order(trim($order_str,','));
//        }
//
//        $db->field('a.*,(a.created_time+expected_day*24*60*60) expected_time,b.wname as created_wname');
//        $list = $db->pages($param['page'],$param['page_size']);
//        foreach ($list['list'] as &$v) {
//            $current_node_info = json_decode($v['current_node_info'],true);
//            $node_name_a = [];
//            $manage_info = [];
//            foreach ($current_node_info as $v1) {
//                $node_name_a[] = $v1['node_name'];
//                $manage_info = array_merge($manage_info,json_decode($v1['manage_info'],true));
//            }
//            $v['node_name'] = $node_name_a;
//            $v['manage_info'] = arrayUnique2($manage_info,'id');
//            unset($v);
//        }
//        $list['export_key'] = combinedProjectModel::$export_key;
//        returnSuccess($list);
//    }
    //导出
//    public function exportAllProject() {
//        $paras_list = array('matter_name', 'status','export_key','is_stop', 'is_apply','expected_time','created_time','order_by','is_me_created','is_me_part','complete_time','is_abolish');
//        $param = arrangeParam($_POST, $paras_list);
//        $is_apply = (int)$param['is_apply'];
//        $is_me_created = (int)$param['is_me_created'];
//        $is_me_part = (int)$param['is_me_part'];
//        $is_abolish = (int)$param['is_abolish'];
//        $db = dbMysql::getInstance();
//        $project_ids = [];
//        if ($is_me_part) {
//            $participant = $db->table('goods_project_participant')
//                ->where('where qwuser_id=:qwuser_id',['qwuser_id'=>userModel::$qwuser_id])
//                ->field('project_id')
//                ->list();
//            $project_ids = array_column($participant,'project_id');
//        }
//        $db->table('combined_project','a')
//            ->leftJoin('qwuser','b','b.id=a.user_id')
//            ->where('where 1=1');
//        if (!empty($param['matter_name'])) {
//            $db->andWhere('and a.matter_name like :matter_name',['matter_name'=>'%'.$param['matter_name'].'%']);
//        }
//        if ($param['status'] > -1) {
//            $db->andWhere('and a.status=:status',['status'=>$param['status']]);
//        }
//        if ($is_apply) {
//            $db->andWhere('and a.user_id=:user_id and flow_path_id<>1',['user_id'=>userModel::$qwuser_id]);
//        }
//        if ($is_me_created) {
//            $db->andWhere('and a.user_id=:user_id',['user_id'=>userModel::$qwuser_id]);
//        }
//        if ($is_abolish > -1) {
//            if ($is_abolish) {
//                $db->andWhere('and a.status = 4');
//            } else {
//                $db->andWhere('and a.status <> 4');
//            }
//        }
//        if ($is_me_part) {
//            $db->whereIn('a.project_id',$project_ids);
//        }
//        if (!empty($param['expected_time']) && $param['expected_time']!='null' && $param['expected_time']!='[]') {
//            $param['expected_time'] = json_decode($param['expected_time']);
//            $start_time = strtotime($param['expected_time'][0]);
//            $end_time = strtotime($param['expected_time'][1]);
//            $db->andWhere('and (a.created_time+a.expected_day*24*60*60) > :start_time and (a.created_time+a.expected_day*24*60*60) < :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
//        }
//        if (!empty($param['created_time']) && $param['created_time']!='null' && $param['created_time']!='[]') {
//            $param['created_time'] = json_decode($param['created_time']);
//            $start_time = strtotime($param['created_time'][0]);
//            $end_time = strtotime($param['created_time'][1]);
//            $db->andWhere('and a.created_time > :start_time and a.created_time < :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
//        }
//        if (!empty($param['complete_time'])) {
//            $param['complete_time'] = json_decode($param['complete_time']);
//            if (count($param['complete_time'])) {
//                $start_time = strtotime($param['complete_time'][0]);
//                $end_time = strtotime($param['complete_time'][1]);
//                $db->andWhere('and a.complete_time >= :start_time and a.complete_time <= :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
//            }
//        }
//        $order_str = '';
//        if (!empty($param['order_by'])) {
//            $order_by = json_decode($param['order_by'],true);
//            foreach ($order_by as $k=>$ord){
//                $order_str .= 'a.'.$k.' '.$ord.',';
//            }
//        }
//        if (empty($order_str)) {
//            $db->order('a.created_time desc');
//        } else {
//            $db->order(trim($order_str,','));
//        }
//        $export_key = empty($param['export_key'])?'[]':$param['export_key'];
//        $export_key = json_decode($export_key);
//        if (!count($export_key)) {
//            SetReturn(-1,'请选择要导出的字段');
//        }
//        $db->field('a.*,b.wname as created_wname');
//        $ids = empty($param['ids'])?'[]':$param['ids'];
//        $ids = json_decode($ids);
//        if (count($ids)) {
//            $db->whereIn('a.id',$ids);
//        }
//        $list = $db->list();
//        if (!count($list)) {
//            SetReturn(-1,'未查询到可导出的数据');
//        }
//        $url = combinedProjectModel::export($list,$export_key);
//        returnSuccess(['url'=>$url]);
//    }


}