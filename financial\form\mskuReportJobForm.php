<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/28 17:34
 */

namespace financial\form;

use core\lib\db\dbFMysql;
use financial\common\importNeedDataBase;
use financial\common\mskuReportBase;

class mskuReportJobForm extends mskuReportBase
{
    public static array $res_share_list = [];//计算出来的分享数据
    public static string $share_error_msg = '';//分享过程中，错误信息
    public static float $has_share_amount = 0; //需要分摊的金额
    /**
     * @param array $condition 根据维度得来的查询条件
     * @param string $cost_key //分摊的键
     * @param float $amount  // 分摊金额
     * @param float $my_rate // 汇率
     * @param int $share_rule_type //分摊类型（1销量or2销售额）
     * @return void 均摊动作
     */
    public function shareAction(array $condition,string $cost_key,float $amount, int $share_rule_type) {
        self::$share_error_msg = '';
        //获取要分摊的数据
        $share_list = $this->getShareList($condition,$cost_key,$share_rule_type);
        if (count($share_list)) {
            if ($share_rule_type == 1) {
                //销量查询
                $data = $this->getTotalSales($share_list,1);
            } else {
                //销售额查询
                $data = $this->getTotalSales($share_list,2);
            }
            $totalSales = $data['amount'];
            $share_list = $data['list'];
            if ($totalSales == 0) {
                self::$share_error_msg = '被分摊数据总销量为0';
                return false;
            }
            //分摊金额获取
            $all_share_count = count($share_list);//分摊数量
            $has_share_amount = 0;//已分摊的金额
            foreach ($share_list as $k=>&$v) {
                if (($k+1) < $all_share_count) {
                    if ($share_rule_type == 1) {
                        $share_amount = floor($amount*$v['total']*100/$totalSales)/100;
                    } else {
                        $share_amount = floor($amount*$v['total']*100/$totalSales)/100;
                    }
                    $has_share_amount += $share_amount;
                } else {
                    $share_amount = ($amount*100 - $has_share_amount*100)/100;
                }
                $v['share_amount'] = $share_amount;
            }
            //保存
            self::$res_share_list = $share_list;
            self::$has_share_amount = $amount;
            return true;
        } else {
            self::$share_error_msg = '未匹配到可分摊的数据';
            return false;
        }
    }
    //保存分摊结果
    public function saveShareRes($import_data_id, $cost_key) {
        $db = dbFMysql::getInstance();
        $db->beginTransaction();
        $db->table('cost_sharing_data')
            ->where('where id=:id',['id'=>$import_data_id])
            ->update(['status'=>2,'share_amount'=>self::$has_share_amount]);
        //删除之前的分摊log
        $db->table('cost_sharing_log')
            ->where('where csd_id=:csd_id',['csd_id' => $import_data_id])
            ->delete();
        if (self::$has_share_amount) {
            foreach (self::$res_share_list as $v) {
                //保存记录
                $db->table('cost_sharing_log')
                    ->insert([
                        'csd_id' => $import_data_id,
                        'msku_report_id' => $v['id'],
                        'share_key' => $cost_key,
                        'share_val' => $v['share_amount'],
                    ]);
                //分摊数据换算
                $res_data = ($v[$cost_key]*100 + $v['share_amount']*100)/100;
                //修改报告数据
                $db->table($this->table_name)
                    ->where('where id=:base_id',['base_id'=>$v['id']])
                    ->update([$cost_key => $res_data]);
            }
        }
        $db->commit();
    }
    //根据维度获取分摊数据,查询原数据
    public function getShareList($condition,$cost_key,$type) {
        $db = dbFMysql::getInstance();
        $db->table($this->table_name)
            ->where('where is_delete = 0');
        //条件
        if (isset($condition['project_ids']))  {
            if (count($condition['project_ids']) > 1) {
                $db->whereIn('project_id',$condition['project_ids']);
            } else {
                $db->andWhere('project_id=:project_id',['project_id'=>$condition['project_ids'][0]]);
            }
            unset($condition['project_ids']);
        }
        if (isset($condition['country_code']))  {
            $db->andWhere('countryCode=:countryCode',['countryCode'=>$condition['country_code'][0]]);
            unset($condition['country_code']);
        }
        $condition_keys = array_keys($condition);
        $condition_str_array = [];
        foreach ($condition_keys as $cond_k) {
            $condition_str_array[] = ($cond_k.'=:'.$cond_k);
        }
        $condition_str = implode(' and ',$condition_str_array);
        $db->andWhere($condition_str,$condition);
        //计算字段
        $field = 'id';
        if ($type == 1) {
            //销量
            $field .= ',fbaSalesQuantity,fbmSalesQuantity,reshipFbaProductSalesQuantity,reshipFbaProductSalesQuantity,key19';
        } else {
            //销售额
            $field .= ',fbaSaleAmount,fbmSaleAmount';
        }
        $field .= ','.$cost_key;
        $db->field($field);
        $list = $db->list();
        return $list;
    }
    //获取此次分摊的总销售额
    public function getTotalSales($list,$type) {
        $total_amount = 0;
        foreach ($list as $k=>$v) {
            if ($type == 1) {
                $total = $v['fbaSalesQuantity'] + $v['fbaSalesQuantity'] + $v['reshipFbaProductSalesQuantity'] +  $v['reshipFbaProductSalesQuantity'] + $v['key19'];
            } else {
                $total = $v['fbaSaleAmount'] + $v['fbmSaleAmount'];
            }
            $total_amount += $total;
            $list[$k]['total'] = $total;
        }
        return ['amount'=>$total_amount,'list'=>$list];
    }
}