<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/22 16:39
 */

namespace  plugins\goods\models;

use core\lib\config;
use core\lib\db\dbMysql;

class formGoodsAttrModel
{
    private static array $old_data=[];
    private static array $new_data;
    //表格字段
    public static array $field_lsit = [
        'material'=>'材质',
        'color'=>'颜色',
        'size'=>'尺寸（L*W*H）',
        'weight'=>'重量',
        'waterproof_level'=>'防水等级',
        'rcb_type'=>'遥控器电池类型',
        'rcip'=>'遥控器绝缘保护',
        'rcs_time'=>'遥控器待机时间',
        'battery_type'=>'产品电池类型',
        'battery_capacity'=>'产品电池容量',
        'battery_lift'=>'产品电池寿命',
        'battery_protect'=>'电池保护',
        'charging_cs'=>'充电线规格',
        'battery_rv'=>'电池额定电压',
        'external_cv'=>'外接充电电压',
        'charging_i'=>'充电电流',
        'quiescent_i'=>'静态电流',
        'working_i'=>'工作电流',
        'motor_ms'=>'马达型号规格',
        'motor_data'=>'马达参数',
        'motor_lift'=>'马达寿命',
        'dental_box_type'=>'牙箱类型',
        'dental_box_lift'=>'牙箱寿命',
        'endurance'=>'产品续航时间',
        'charging_time'=>'充电时间',
        'auto_shutdowm_v'=>'自动关机电压',
        'noise_standards'=>'噪音标准',
        'working_heat'=>'工作温度',
        'stored_environment'=>'储藏温度/湿度',
        'silicone_hardness'=>'硅胶硬度',
        'key_lift'=>'按键寿命',
        'charging_plugging'=>'充电插拔',
        'authentication'=>'产品认证'
    ];

    private static string $field_str = '';

    private static function verifyFormData($form_data){
        $update_date = [];
        foreach (self::$field_lsit as $k=>$field) {
            if (!isset($form_data[$k])) {
                SetReturn(-1,self::$field_lsit[$k].'必填');
            }
            $update_date[$k] = $form_data[$k];
            self::$field_str .= $k.',';
        }
        self::$field_str = trim(self::$field_str,',');
        return $update_date;
    }

    //填写表格
    public static function editGoodsAttr($param) {
        //验证表单必传信息
        $form_data = $param['form_data'];
        $update_data = self::verifyFormData($form_data);
        self::$new_data = $update_data;
        //检查该事件的表格是否填写
        $db = dbMysql::getInstance();
        $db->table('form_goods_attr');
        $form_info = $db->query('select id from oa_form_goods_attr where goods_id = '.$param['goods_id'].' limit 1');
        if ($form_info) {
            $form_id = $form_info['id'];
            self::$old_data = $form_info;
            $update_data['updated_at'] = date('Y-m-d H:i:s');
            $db->table('form_goods_attr');
            $db->where('where id='.$form_id);
            if (!$db->update($update_data)) {
                $db->rollBack();
                SetReturn(-1,'修改失败');
            }
            return $form_id;
        } else {
            $base_data = [
                'goods_id'=>$param['goods_id'],
                'project_id'=>$param['id'],
                'project_index'=>$param['index_id'],
                'event_index'=>$param['event_index_id']
            ];
            $update_data['flow_path_id'] = $param['flow_path_id'];
            $update_data['created_at'] = date('Y-m-d H:i:s');
            $update_data = array_merge($update_data,$base_data);
            $form_id = $db->insert($update_data);
            if (!$form_id) {
                $db->rollBack();
                SetReturn(-1,'保存失败');
            }
            return $form_id;
        }
    }

    //修改字段查询
    static public function getUpdateInfo()
    {
        $msg = '';
        $has_chaunge = 0;
        if (count(self::$old_data)) {
            $mgs = '修改表单，';
            foreach (self::$new_data as $k=>$v) {
//                if ($k == 'material') {
//                    echo self::$old_data[$k].PHP_EOL;
//                    echo $v.PHP_EOL;
//                    echo self::$field_lsit[$k];die;
//                }
                if (isset(self::$old_data[$k]) && (self::$old_data[$k] != $v) && isset(self::$field_lsit[$k])) {
                    $mgs .= self::$field_lsit[$k].'：【'.$v.'】，';
                    $has_chaunge = 1;
                }
            }
            if ($has_chaunge == 1) {
                $mgs = trim($mgs, '，');
            } else {
                $mgs = '';
            }
        } else {
            $mgs = '填写表单';
        }

        return $mgs;
    }

    //查询列表
    static public function getList($param){
        $db = dbMysql::getInstance();
        $sql_str = 'where 1=1';
        $sql_data = [];
        if (!empty($param['tpl_name'])) {
            $sql_str .= ' and b.tpl_name like :tpl_name';
            $sql_data['tpl_name'] = '%'.$param['tpl_name'].'%';
        }
        $flow_path_id = (int)$param['flow_path_id'];
        if ($flow_path_id > 0) {
            $sql_str .= ' and a.flow_path_id = :flow_path_id';
            $sql_data['flow_path_id'] = $param['flow_path_id'];
        }
        if (!empty($param['goods_name'])) {
            $sql_str .= ' and b.goods_name like :goods_name';
            $sql_data['goods_name'] = '%'.$param['goods_name'].'%';
        }
        $db->table('form_goods_attr a');
        $db->leftJoin('goods_project','b','b.id = a.project_id');
        $db->where($sql_str, $sql_data);
        $db->field('a.*,b.goods_name,b.tpl_name');
        $db->order('a.id desc');
        $list = $db->pages($param['page'],$param['page_size']);
        return $list;
    }

    //获取详情
    static public function getDetail($id){
        $db = dbMysql::getInstance();
        $db->table('form_goods_attr');
        $db->where('where id = '.$id);
        $data = $db->one();
        return $data;
    }

    //规格书硬件测试(检查是否完成所有字段的测试)
    static public function getAchieveStatus($goods_id){
        $db = dbMysql::getInstance();
        $db->table('form_goods_attr');
        $db->where('where goods_id = '.$goods_id);
        $db->field('achieve_key');
        $data = $db->one();
        if (!$data) {
            SetReturn(-1,'该项目未填写规格书');
        } else {
            $achieve_key = !empty($data['achieve_key'])?json_decode($data['achieve_key'], true):[];
            if (count($achieve_key) < count(self::$field_lsit)) {
                return 0;
            } else {
                foreach (self::$field_lsit as $k=>$v){
                    if (!in_array($k,$achieve_key)) {
                        return 0;
                    }
                }
            }
        }
        return 1;
    }

    /**
     * @param $filled_from_id 填写的表单id
     * @param $add_watermark 使用水印了水印就说明已经硬件测试
     * @return array|void //生成pdf文件
     * @throws \core\lib\ExceptionError
     */
    static public function getFormGoodsAttr($filled_from_id, $add_watermark)
    {
        $db = dbMysql::getInstance();
        $filled_form = $db->query('select * from oa_form_goods_attr where id = ' . $filled_from_id . ' limit 1');
        if (!$filled_form) {
            SetReturn(-1, '未找到数据');
        } else {
            //内容
            $form = $db->query('select form_name from oa_form where id = 1 limit 1');
            $goods = $db->query('select * from oa_goods_new where id = ' . $filled_form['goods_id'] . ' limit 1');
            //流程和新品名称
            $flow_path = config::getDataName('flow_path', $filled_form['flow_path_id']);
            $title = ($goods['goods_name']??'') . $flow_path . $form['form_name'];
            //填写的内容
            $html = '';
            $key = formGoodsAttrModel::$field_lsit;
            $achieve_key = !empty($filled_form['achieve_key'])?json_decode($filled_form['achieve_key'],true):'';
            foreach ($filled_form as $k => $v) {
                if (isset($key[$k])) {
                    $html .= '<tr>';
                    $html .= "<td>$key[$k]</td>";
                    $html .= "<td>$v</td>";
                    if ($add_watermark > 0) {
                        if ($achieve_key) {
                            $html .= '<td class="gou">'.(isset($k,$achieve_key)?'通过':'').'</td>';
                        } else {
                            $html .= '<td class="gou"></td>';
                        }
                    }
                    $html .= '</tr>';
                }
            }
            return ['title' => $title, 'html' => $html];
        }
    }

}