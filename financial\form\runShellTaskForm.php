<?php
/**
 * @author: zhangguoming
 * @Time: 2024/8/13 17:08
 */

namespace financial\form;

//自定执行脚本
use core\lib\db\dbFMysql;
use core\lib\rediskeys;

class runShellTaskForm
{
    /**产信息有改动时需要更新的数据
     *  1、跟新自定义字段
     * 2、更新等级
     * 3、更新缓存
     */
    //msku报表数据统计
    public static function countMskuMonth()
    {
        $log_path = self::setLogFile('count_month_msku_data.log');
        $targetFile = SELF_FK . '/task/shell/count_month_msku_data.sh > '.$log_path.' /dev/null 2>&1 &';
        shell_exec($targetFile);
    }
    //生成预警商品数据
    public static function setWaringGoods()
    {
        $redis_key = rediskeys::$oa_count_msku_month;
        $redis = (new \core\lib\predisV())::$client;
        if (!$redis->exists(rediskeys::$oa_count_msku_month)) {
            return;
        }
        $r_data = json_decode($redis->get(rediskeys::$oa_count_msku_month),true);
        $r_data['message'] = $r_data['month'].'月数据结账中。当前进度：生成产品等级';
        $redis->set($redis_key,json_encode($r_data));
        $redis->expire($redis_key,4*60*60);
        $log_path = self::setLogFile('update_waring_goods.log');
        $targetFile = SELF_FK . "/task/shell/update_waring_goods.sh {$r_data['month']} >  {$log_path} /dev/null 2>&1 &";
        shell_exec($targetFile);
    }
    //生成产品等级数据
    public static function setGoodsLeve()
    {
        $redis_key = rediskeys::$oa_count_msku_month;
        $redis = (new \core\lib\predisV())::$client;
        if (!$redis->exists(rediskeys::$oa_count_msku_month)) {
            return;
        }
        $r_data = json_decode($redis->get(rediskeys::$oa_count_msku_month),true);
        $r_data['message'] = $r_data['month'].'月数据结账中。当前进度：生成产品等级';
        $redis->set($redis_key,json_encode($r_data));
        $redis->expire($redis_key,4*60*60);
        $log_path = self::setLogFile('update_goods_level.log');
        $targetFile = SELF_FK . "/task/shell/update_goods_level.sh {$r_data['month']} > {$log_path} /dev/null 2>&1 &";
        shell_exec($targetFile);
    }
    //生成输出得文件
    public static function setLogFile($file) {
        $log_path = SELF_FK.'/log/shell';
        if (!file_exists($log_path)) {
            mkdir($log_path, 0777, true);
        }
        $log_path .= '/'.$file;
        if (!file_exists($log_path)) {
            file_put_contents($log_path, $log_path.PHP_EOL);
        } else {
            file_put_contents($log_path, $log_path.PHP_EOL, FILE_APPEND);
        }
        return $log_path;
    }

    //生成报表缓存
}