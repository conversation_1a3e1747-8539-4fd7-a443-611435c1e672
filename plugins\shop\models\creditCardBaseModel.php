<?php

namespace plugins\shop\models;

use core\lib\redisCached;
use Exception;

abstract class creditCardBaseModel extends baseModel
{
    public string $table = 'credit_card';

     public static array $json_keys = [
         'validity_period',
         'use_platform',
         'fee_year'
     ];

    public function __construct()
    {
        parent::__construct();
    }

    public function formatItem($item, $maps = [])
    {
        $users = $maps['users'] ?? [];
        if (empty($users)) {
            $users = redisCached::getUserInfo();
            $users = array_column($users, 'user_name', 'user_id');
        }

        $deps = $maps['deps'] ?? [];
        if (empty($deps)) {
            $deps = redisCached::getDepartment();
            $deps = array_column($deps, 'name', 'id');
        }

        $receive_account = $maps['receive_account'] ?? [];
        if (empty($receive_account)) {
            $receive_account = redisCached::getReceiveAccountAll();
            $receive_account = array_column($receive_account, 'account_name', 'id');
        }

        $company = $maps['company'] ?? [];
        if (empty($company)) {
            $company = redisCached::getCompany();
        }

        $maps = [
            ['name' => 'operator_name', 'maps' => $users, 'key' => 'operator'],
            ['name' => 'record_keeper_name', 'maps' => $users, 'key' => 'record_keeper'],
            ['name' => 'dep_name', 'maps' => $deps, 'key' => 'dep_id'],
            ['name' => 'receive_account_name', 'maps' => $receive_account, 'key' => 'receive_account_id'],
            ['name' => 'legal_person_type', 'maps' => array_column($company, 'legal_person_type', 'id'), 'key' => 'company_id'],
        ];
        return parent::formatItem($item, $maps);
    }

    // 获取卡类型，子类必须实现
    abstract protected function getCardType(): int;

    // 数据校验
    public function dataValidCheck(array $data, array $param_list = [], $is_throw = true, array $error = []): array
    {
        if (empty($param_list)) $param_list = static::$paras_list;
        return parent::dataValidCheck($data, $param_list);
    }

    // 获取列表的基础查询条件
    protected function getBaseListCondition($param)
    {
        $this->db->table($this->table, 'c')
            ->field('c.*, s.id as shop_id, s.shop_number, s.shop_status, s.company_id, r.updated_at as bind_time, u.wname as record_keeper_name')
            ->leftJoin('receive_account', 'ra', 'ra.id = c.receive_account_id')
            ->leftJoin('relations', 'r', "r.from_table = 'shop' AND r.to_table = 'credit_card' and r.to_id = c.id")
            ->leftJoin('shop', 's', 's.id = r.from_id')
            ->leftJoin('company', 'com', 'com.id = s.company_id')
            ->leftJoin('legal_person', 'lp', 'com.legal_person_id = lp.id')
            ->leftJoinOut('db', 'qwuser', 'u', 'u.id = c.record_keeper')
        ->where('c.type = :type', ['type' => $this->getCardType()]);

        // 固定筛选
        if (!empty($param['ids'])) {
            $this->db->whereIn('c.id', $param['ids']);
        }

        // 卡号
        if (!empty($param['card_number'])) {
            $this->db->andWhere('card_number like :card_number', ['card_number' => '%' . $param['card_number'] . '%']);
        }
        // 有效期
        if (!empty($param['validity_date'])) {
            $this->db->andWhere('JSON_EXTRACT(c.validity_period, "$[1]") >= :start_time
                                and JSON_EXTRACT(c.validity_period, "$[0]") <= :end_time ',
                [
                    'start_time' => $param['validity_date'][0],
                    'end_time' => $param['validity_date'][1]
                ]);
        }
        // 使用店铺
        if (!empty($param['shop_number'])) {
            $this->db->andWhere('s.shop_number LIKE :shop_number', ['shop_number' => '%' . $param['shop_number'] . '%']);
        }
        // 信用卡状态
        if (!empty($param['credit_card_status'])) {
            $this->db->whereIn('c.credit_card_status', $param['credit_card_status']);
        }
        // 店铺状态
        if (!empty($param['shop_status'])) {
            $this->db->andWhere('s.shop_status = :shop_status', ['shop_status' => $param['shop_status']]);
        }
        // 绑定店铺时间
        if (!empty($param['bind_shop_date'])) {
            $this->db->andWhere('r.updated_at >= :bind_shop_date_start and r.updated_at <= :bind_shop_date_end', [
                'bind_shop_date_start' => $param['bind_shop_date'][0] . ' 00:00:00',
                'bind_shop_date_end'   => $param['bind_shop_date'][1] . ' 23:59:59',
            ]);
        }
        if (!empty($param['update_date'])) {
            $this->db->andWhere('c.updated_at >= :update_time_start and c.updated_at <= :update_time_end', [
                'update_time_start' => $param['update_date'][0],
                'update_time_end'   => $param['update_date'][1] . ' 23:59:59',
            ]);
        }
    }

    // 获取列表
    public function getList($param, $order = 'c.id desc', $is_export = false)
    {
        // 设置基础查询条件
        $this->getBaseListCondition($param);
        
        // 子类特定查询条件
        $this->getSpecificListCondition($param);

        $this->db->order($order);

        if (!$is_export && !empty($param['page']) && !empty($param['page_size'])) {
            $list = $this->db->pages($param['page'], $param['page_size']);
            $maps = self::getMaps();
            foreach ($list['list'] as &$item) {
                $item = $this->formatItem($item, $maps);
            }
            return $list;
        }  else {
            $list = $this->db->list();
            if ($is_export) {
                $export_data = [];
                $maps = self::getMaps();
                $paras_list = static::$paras_list;
                if ($this->getCardType() == 1) {
                    $paras_list['dep_name'] = '部门';
                    $paras_list['record_keeper_name'] = '登记负责人';
                } else {
                    $paras_list['receive_account_name'] = '主账户';
                }
                foreach ($list as $item) {
                    $item = $this->formatItem($item, $maps);
                    // 使用参数对应的中文作为键名返回
                    $export_data[] = self::changeToCnKey($item, $paras_list);
                }
                return $export_data;
            }
            return $list;
        }
    }

    public static function getMaps()
    {
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        $deps = redisCached::getDepartment();
        $deps = array_column($deps, 'name', 'id');

        $receive_account = redisCached::getReceiveAccountAll();
        $receive_account = array_column($receive_account, 'account_name', 'id');

        $company = redisCached::getCompany();

        return [
            'users' => $users,
            'deps' => $deps,
            'receive_account' => $receive_account,
            'company' => $company
        ];

    }

    // 获取特定类型的查询条件，子类实现
    abstract protected function getSpecificListCondition($param);

    // 添加数据时的通用处理
    protected function beforeAdd(&$data)
    {
        $data['type'] = $this->getCardType();
    }

    // 添加数据
    public function add($data, $type = '新增')
    {
        $this->beforeAdd($data);
        $receiveAccountId = $data['receive_account_id'] ?? null;

        $id = parent::add($data, $type);

        // 创建关联关系
        $relationModel = new relationModel();
        if ($receiveAccountId) {
            $relationModel->updateRelation($this->table, $id, 'receive_account', $receiveAccountId);
        }

        return $id;
    }

    // 编辑数据
    public function edit($data, $id, $old_data = [], $type = '编辑', $remark = '', $result = '', $other_attach = [])
    {

        $receiveAccountId = $data['receive_account_id'] ?? null;
        parent::edit($data, $id, $old_data, $type, $remark, $result, $other_attach);

        // 创建关联关系
        $relationModel = new relationModel();
        if ($receiveAccountId) {
            $relationModel->updateRelation($this->table, $id, 'receive_account', $receiveAccountId);
        }
    }

    // 根据ID获取数据
    public function getById($id)
    {
        if (empty($id)) {
            return null;
        }
        
        $item = $this->db->table($this->table)
            ->where('id = :id AND type = :type', ['id' => $id, 'type' => $this->getCardType()])
            ->one();
            
        return $item;
    }
    
    // 根据卡号获取数据
    public function getByCardNumber($card_number, $id = null)
    {
        if (empty($card_number)) {
            return null;
        }
        
        $this->db->table($this->table)
            ->where('card_number = :card_number AND type = :type', 
                ['card_number' => $card_number, 'type' => $this->getCardType()]);

        if (!empty($id)) {
            $this->db->andWhere('id != :id', ['id' => $id]);
        }
        return $this->db->one();
    }

    // 批量编辑
    public function editBatch($data)
    {
        $db = $this->db->table($this->table);
        $ids = array_column($data, 'id');
        $db->whereIn('id', $ids);
        $list = $db->list();
        $list = array_column($list, null, 'id');

        $error_ids = [];
        foreach ($data as $item) {
            try {
                $item_id = $item['id'];
                unset($item['id']);
                $this->validateData($item);
                // 唯一性校验
                $detail = $this->getByCardNumber($item['card_number'], $item_id);
                if ($detail) {
                    throw new Exception('信用卡已存在');
                }
                self::edit($item, $item_id, $list[$item_id], '批量编辑');
            } catch (Exception $e) {
                $error_ids[] = [
                    'id' => $item_id,
                    'error' => $e->getMessage(),
                ];
            }
        }
        
        return $error_ids;
    }
}
