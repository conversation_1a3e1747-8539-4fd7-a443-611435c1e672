<?php
/**
 * @author: zhangguoming
 * @Time: 2024/9/5 13:55
 */

namespace financial\models;

use core\lib\db\dbFMysql;

class projectListingModel
{
    public static function creatProjectListingTable(string $year) {
        $dbF = dbFMysql::getInstance();
        //源数据表
        $dbF->query("CREATE TABLE IF NOT EXISTS `oa_f_project_listing_$year` (
            `id` int unsigned NOT NULL AUTO_INCREMENT,
            `project_id` int DEFAULT '0',
            `yunying_id` int DEFAULT '0',
            `sid` varchar(100) NOT NULL COMMENT '店铺id',
            `marketplace` varchar(50) NOT NULL COMMENT '国家',
            `seller_sku` varchar(100) NOT NULL COMMENT 'MSKU',
            `fnsku` varchar(100) NOT NULL COMMENT 'FNSKU',
            `asin` varchar(100) NOT NULL COMMENT 'ASIN',
            `parent_asin` varchar(100) NOT NULL COMMENT '父ASIN',
            `local_sku` varchar(100) NOT NULL COMMENT '本地产品SKU',
            `m_date` varchar(20) NOT NULL COMMENT '月份',
            PRIMARY KEY (`id`),
            UNIQUE KEY `project_id` (`project_id`,`yunying_id`,`sid`,`marketplace`,`seller_sku`,`fnsku`,`asin`,`parent_asin`,`local_sku`,`m_date`) USING BTREE
        ) ENGINE=InnoDB AUTO_INCREMENT=6523 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='listing';");
    }
}