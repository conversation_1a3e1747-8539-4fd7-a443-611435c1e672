<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/13 13:37
 */

namespace core\lib\app_auth\plugins;

use admin\models\userModel as baseUserModel;
use plugins\assessment\models\userModel;
use plugins\assessment\models\userRolesModel;

class authAssessment
{
    //公用方法（不用验证权限的接口）
    static array $common_auth = [
        'assessment/index/index',
        //用户
        'assessment/user/updateUserPwd', //当前登录人修改自己密码
        'assessment/user/getUserInfo',   //当前登录人的基本信息
        'assessment/user/getOftenUsedUser', //获取常用用户
        'assessment/user/setOftenUsedUser', //获取常用用户
        'assessment/user/getList', //查看成员列表
        'assessment/qwPartment/getList', //查看部门列表
        //角色
        'assessment/role/getList',//角色列表
        'assessment/role/getDefaultAuth', //获取权限列表
        'assessment/message/getMsgDetail',//系统外获取消息
        'assessment/assessmentManage/getOptionData', //获取绩效配置项
        //消息
        'assessment/message/getList',   //消息列表
        'assessment/message/getDetail', //消息详情
        'assessment/message/setAllRead',//全部已读
        'assessment/message/getMessageCount', //获取数量
        'assessment/message/getMsgDetail',//系统外获取消息

    ];

    //验证用户请求权限
    public static function checkAuth($url, $token_perfix){

        //先重写userModel中数据
        $auth_ = userRolesModel::getAuthByQuserId(baseUserModel::$qwuser_id);
        $this_auth = [];
        if ($auth_) {
            $this_auth = json_decode($auth_['auth'], true);
            userModel::$qwuser_id = baseUserModel::$qwuser_id;
            userModel::$wid = baseUserModel::$wid;
            userModel::$wname = baseUserModel::$wname;
            userModel::$is_super = baseUserModel::$is_super;
            userModel::$avatar = baseUserModel::$avatar;
            userModel::$auth = $auth_['auth'] ?? '[]';
            userModel::$role_type = $auth_['role_type'];
        }
        //权限验证
        if (baseUserModel::$is_super == 1) {
            return true;
        }
        if (in_array($url, self::$common_auth)) {
            return true;
        }
        if (!in_array($url,$this_auth)) {
            SetReturn(-1,'暂无权限');
        } else {
            return true;
        }
        return false;
    }

}