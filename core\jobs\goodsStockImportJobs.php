<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/26 17:58
 */

namespace core\jobs;

use core\lib\config;
use financial\form\customColumnForm;
use financial\form\goodsStorkForm;
use financial\form\runShellTaskForm;
use financial\models\goodsStorkModel;
use Rap2hpoutre\FastExcel\FastExcel;

class goodsStockImportJobs
{
    public string $unqueid = '';
    public string $key = '';//到表得redis_key
    public int $page_size = 500;
    public function __construct($key){
        $this->key = $key;
        $this->unqueid = uniqid();
    }
    public function task() {
        $redis = (new \core\lib\predisV())::$client;
        if (!$redis->exists($this->key)) {
            return true;
        }
        $export_data = json_decode($redis->get($this->key),true);
        $offset = $export_data['offset'];
        $date = $export_data['date'];
        $excel_src = $export_data['excel_src'];
        $import_id = $export_data['import_id'];
        $excel_data = (new FastExcel)->import($excel_src);
        $data = $excel_data->toArray();
        $slice = array_slice($data, $offset, $this->page_size, true);
        if (count($slice)) {
            //整理数据
            $data_new = [];
            $import_key_list = goodsStorkModel::$import_key_list;
            foreach ($slice as $v) {
                $new_item = [];
                foreach ($v as $key=>$v1) {
                    if (isset($import_key_list[$key])) {
                        $new_item[$import_key_list[$key]] = $v1;
                    }
                }
                $data_new[] = $new_item;
            }
            //保存数据
            $goodsStorkForm = new goodsStorkForm($date);
            $res = $goodsStorkForm->importGoodsStock($data_new,$date,$import_id,$export_data);
            $export_data['success_count'] = $res['success_count'];
            $export_data['error_count'] = $res['error_count'];
            $export_data['offset'] += $this->page_size;
            //看看是否要继续异步
            $total_import = $export_data['success_count'] + $export_data['error_count'];
            $redis->set($this->key,json_encode($export_data));
            $redis->expire($this->key,60*60);
            if ( $total_import < $export_data['total']) {
                $queue_key = config::get('delay_queue_key', 'app');
                $task = new goodsStockImportJobs($this->key); // 创建任务类实例
                $redis->zAdd($queue_key, [], 0, serialize($task));
                return true;
            }
        }
        //redis更新
        $redis->del($this->key);
    }
}