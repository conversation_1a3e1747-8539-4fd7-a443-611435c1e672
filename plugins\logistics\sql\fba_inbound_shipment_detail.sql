-- FBA货件明细业务表
-- 用于存储业务处理数据，支持编辑
-- 数据库：dbLMysql

CREATE TABLE `fba_inbound_shipment_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shipment_sn` varchar(100) NOT NULL DEFAULT '' COMMENT '发货单号',
  `relate_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联明细ID',
  
  -- 展示字段（24个）
  `warehouse_name` varchar(200) NOT NULL DEFAULT '' COMMENT '发货仓库',
  `plan_date` date DEFAULT NULL COMMENT '计划日期',
  `group_name` varchar(100) NOT NULL DEFAULT '' COMMENT '组别',
  `responsible_person` varchar(100) NOT NULL DEFAULT '' COMMENT '负责人',
  `site` varchar(50) NOT NULL DEFAULT '' COMMENT '站点',
  `transport_method` varchar(200) NOT NULL DEFAULT '' COMMENT '运输方式',
  `product_name` varchar(500) NOT NULL DEFAULT '' COMMENT '品名',
  `asin` varchar(50) NOT NULL DEFAULT '' COMMENT 'ASIN',
  `fnsku` varchar(50) NOT NULL DEFAULT '' COMMENT 'FNSKU',
  `plan_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '计划数量',
  `box_count` int(11) NOT NULL DEFAULT '0' COMMENT '箱数',
  `auxiliary_materials` varchar(200) NOT NULL DEFAULT '' COMMENT '辅料',
  `shipment_code` varchar(100) NOT NULL DEFAULT '' COMMENT '货件编码',
  `delivery_address` text COMMENT '收货地址',
  `tracking_number` varchar(100) NOT NULL DEFAULT '' COMMENT '跟踪单号（可编辑）',
  `ship_date` date DEFAULT NULL COMMENT '发货日期',
  `shipment_status` varchar(50) NOT NULL DEFAULT '' COMMENT '货件状态',
  `delivery_time_slot` varchar(100) NOT NULL DEFAULT '' COMMENT '送达时段',
  `received_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '签收数量',
  `difference` int(11) NOT NULL DEFAULT '0' COMMENT '差异',
  `transparent_label` varchar(200) NOT NULL DEFAULT '' COMMENT '透明标签（可编辑）',
  `is_label_changed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否换标（可编辑） 0-否 1-是',
  `remark` text COMMENT '备注（可编辑）',
  `remark2` text COMMENT '备注2（可编辑）',
  
  -- 筛选字段
  `country` varchar(50) NOT NULL DEFAULT '' COMMENT '国家',
  `shop_code` varchar(100) NOT NULL DEFAULT '' COMMENT '店铺代码',
  `sku` varchar(100) NOT NULL DEFAULT '' COMMENT 'SKU',
  `warehouse_code` varchar(100) NOT NULL DEFAULT '' COMMENT '入仓编号',
  
  -- 原始数据关联字段
  `original_shipment_id` int(11) NOT NULL DEFAULT '0' COMMENT '原始数据表ID',
  `original_relate_detail_id` int(11) NOT NULL DEFAULT '0' COMMENT '原始关联明细ID',
  `shipment_id` varchar(100) NOT NULL DEFAULT '' COMMENT '货件ID',
  `destination_fulfillment_center_id` varchar(100) NOT NULL DEFAULT '' COMMENT '物流中心编码',
  `quantity_shipped` int(11) NOT NULL DEFAULT '0' COMMENT '申报量',
  `num` int(11) NOT NULL DEFAULT '0' COMMENT '发货数量',
  `apply_num` int(11) NOT NULL DEFAULT '0' COMMENT '关联货件量',
  `product_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品ID',
  `parent_asin` varchar(50) NOT NULL DEFAULT '' COMMENT '父ASIN',
  `msku` varchar(100) NOT NULL DEFAULT '' COMMENT 'seller_sku',
  `fulfillment_network_sku` varchar(100) NOT NULL DEFAULT '' COMMENT 'listing的fnsku',
  `pic_url` varchar(500) NOT NULL DEFAULT '' COMMENT '图片URL',
  `packing_type` int(11) NOT NULL DEFAULT '0' COMMENT '混装类型 1:混装 2:原装',
  `packing_type_name` varchar(50) NOT NULL DEFAULT '' COMMENT '包装名称',
  `is_combo` int(11) NOT NULL DEFAULT '0' COMMENT '是否组合商品 1:是 0:否',
  `create_by_mws` int(11) NOT NULL DEFAULT '0' COMMENT '创建发货单途径 1:货件 0:发货计划',
  `product_valid_num` int(11) NOT NULL DEFAULT '0' COMMENT '可用量',
  `product_qc_num` int(11) NOT NULL DEFAULT '0' COMMENT '待检量',
  `diff_num` int(11) NOT NULL DEFAULT '0' COMMENT '差额',
  `sta_shipment_id` varchar(100) NOT NULL DEFAULT '' COMMENT 'sta货件的shipmentId',
  `is_sta` varchar(10) NOT NULL DEFAULT '0' COMMENT '是否sta货件 1:是 0:否',
  `sta_inbound_plan_id` varchar(100) NOT NULL DEFAULT '' COMMENT 'sta货件所属的sta任务编号',
  
  -- 系统字段
  `sync_date` date NOT NULL COMMENT '同步日期',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-否 1-是',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_shipment_relate` (`shipment_sn`, `relate_id`, `is_deleted`) COMMENT '发货单号+关联ID唯一索引',
  KEY `idx_shipment_code` (`shipment_code`) COMMENT '货件编码索引',
  KEY `idx_tracking_number` (`tracking_number`) COMMENT '跟踪单号索引',
  KEY `idx_plan_date` (`plan_date`) COMMENT '计划日期索引',
  KEY `idx_warehouse_name` (`warehouse_name`) COMMENT '仓库名称索引',
  KEY `idx_country` (`country`) COMMENT '国家索引',
  KEY `idx_transport_method` (`transport_method`) COMMENT '运输方式索引',
  KEY `idx_shop_code` (`shop_code`) COMMENT '店铺代码索引',
  KEY `idx_sku` (`sku`) COMMENT 'SKU索引',
  KEY `idx_fnsku` (`fnsku`) COMMENT 'FNSKU索引',
  KEY `idx_warehouse_code` (`warehouse_code`) COMMENT '入仓编号索引',
  KEY `idx_sync_date` (`sync_date`) COMMENT '同步日期索引',
  KEY `idx_original_shipment_id` (`original_shipment_id`) COMMENT '原始数据ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='FBA货件明细业务表';
