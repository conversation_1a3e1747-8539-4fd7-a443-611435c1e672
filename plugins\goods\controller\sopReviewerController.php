<?php

namespace plugins\goods\controller;

use plugins\goods\form\sopReviewerFrom;
//SOP审核配置
class sopReviewerController
{
    //拉取列表
    public function getList()
    {
        $paras_list = [
            'page',                 // 页码
            'page_size',            // 每页数量
            'category_id',        // 产品类目
            'country_code',         // 国家code
            'user_id',             // 负责人id
            'wq_id'                 // 部门id
        ];

        $param = arrangeParam($_POST, $paras_list);
        
        sopReviewerFrom::getList($param);
    }
    /**
     * 删除SOP审核人配置
     */
    public function deleteSopReviewer()
    {
        $paras_list = [
            'id'                 // 配置ID
        ];
        
        $param = arrangeParam($_POST, $paras_list);
        sopReviewerFrom::deleteSopReviewer($param['id']);
    }

    /**
     * 新增或编辑SOP审核人配置
     */
    public function saveSopReviewer()
    {
        $paras_list = [
            'id',                // 配置ID，不传为新增，传为编辑
            'country_code',      // 站点
            'category_id',     // 类目
            'wq_id',             // 申请人部门
            'check_id',          // 负责人
            'user_id'            // 创建人ID，新增时必传
        ];
        
        $param = arrangeParam($_POST, $paras_list);
        sopReviewerFrom::saveSopReviewer($param);
    }
    //终线设置
    public function getFinalLine()
    {
        $paras_list = [
            'user_ids'
        ];

        $param = arrangeParam($_POST, $paras_list);

        sopReviewerFrom::getFinalLine($param);
    }
    //获取终线设置当前信息
    public function getFinalLineCurrent()
    {
        sopReviewerFrom::getFinalLineCurrent();
    }

}