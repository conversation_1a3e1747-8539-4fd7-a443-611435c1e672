<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/8 14:17
 */
namespace core\lib;

class ExceptionError extends \Exception
{
    public function __construct(string $message = "", int $code = 0, ?Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
            $this->returnError($message);
    }

    public function returnError($message){
        showError($message);
    }
}