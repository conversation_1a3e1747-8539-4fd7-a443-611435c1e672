<?php
namespace plugins\logistics\form;

use core\lib\db\dbMysql;
use core\lib\ExceptionError;
use plugins\logistics\models\userModel;

class ApprovalForm
{
    /**
     * 启动审批流程
     * @param string $businessType 业务类型
     * @param int $businessId 业务ID
     * @param string $title 审批标题
     * @param array $businessData 业务数据
     * @return int 审批实例ID
     */
    public static function startApproval(string $businessType, int $businessId, string $title, array $businessData = []): int
    {
        $db = dbMysql::getInstance();
        $db->beginTransaction();
        
        try {
            // 获取审批配置
            $configKey = $businessType . '_approval_config';
            $config = self::getConfigByName($configKey);
            
            if (empty($config)) {
                throw new ExceptionError("未找到审批配置");
            }
            
            // 创建审批实例
            $instanceData = [
                'business_type' => $businessType,
                'business_id' => $businessId,
                'title' => $title,
                'initiator_id' => userModel::$qwuser_id,
                'initiator_name' => userModel::$wname,
                'status' => 0, // 审批中
                'config_snapshot' => json_encode($config, JSON_UNESCAPED_UNICODE),
                'business_data' => json_encode($businessData, JSON_UNESCAPED_UNICODE),
                'created_time' => date('Y-m-d H:i:s')
            ];
            
            $instanceId = $db->table('approval_instances')->insert($instanceData);
            
            // 创建审批流程节点
            self::createApprovalFlows($instanceId, $config);
            
            $db->commit();
            return $instanceId;
        } catch (\Exception $e) {
            $db->rollBack();
            throw new ExceptionError("启动审批失败: " . $e->getMessage());
        }
    }
    
    /**
     * 处理审批
     * @param int $flowId 流程ID
     * @param int $action 操作(1-同意,2-拒绝)
     * @param string $comment 审批意见
     * @return bool
     */
    public static function processApproval(int $flowId, int $action, string $comment = ''): bool
    {
        $db = dbMysql::getInstance();
        $db->beginTransaction();
        
        try {
            // 获取流程节点
            $flow = $db->table('approval_flows')
                ->where('id = :id', ['id' => $flowId])
                ->one();
                
            if (!$flow || $flow['is_active'] != 1) {
                throw new ExceptionError("无效的审批节点");
            }
            
            // 更新流程节点
            $db->table('approval_flows')
                ->where('id = :id', ['id' => $flowId])
                ->update([
                    'action' => $action,
                    'comment' => $comment,
                    'is_active' => 0,
                    'action_time' => date('Y-m-d H:i:s')
                ]);
            
            // 获取审批实例
            $instance = $db->table('approval_instances')
                ->where('id = :id', ['id' => $flow['instance_id']])
                ->one();
            
            if ($action == 2) { // 拒绝
                // 更新审批状态为拒绝
                $db->table('approval_instances')
                    ->where('id = :id', ['id' => $instance['id']])
                    ->update([
                        'status' => 2,
                        'completed_time' => date('Y-m-d H:i:s')
                    ]);
                
                // 发送拒绝消息
                self::sendResultMessage($instance, false);
                
                // 更新业务状态
                self::updateBusinessStatus($instance, false);
            } else { // 同意
                // 查找下一个节点
                $nextFlow = $db->table('approval_flows')
                    ->where('instance_id = :instance_id AND node_index > :node_index AND node_type = "approver"', 
                        ['instance_id' => $flow['instance_id'], 'node_index' => $flow['node_index']])
                    ->order('node_index ASC')
                    ->one();
                
                if ($nextFlow) {
                    // 激活下一个节点
                    $db->table('approval_flows')
                        ->where('id = :id', ['id' => $nextFlow['id']])
                        ->update(['is_active' => 1]);
                    
                    // 发送通知给下一个审批人
                    self::sendNotification($nextFlow);
                } else {
                    // 所有节点已审批，更新状态为通过
                    $db->table('approval_instances')
                        ->where('id = :id', ['id' => $instance['id']])
                        ->update([
                            'status' => 1,
                            'completed_time' => date('Y-m-d H:i:s')
                        ]);
                    
                    // 发送通过消息
                    self::sendResultMessage($instance, true);
                    
                    // 更新业务状态
                    self::updateBusinessStatus($instance, true);
                }
            }
            
            $db->commit();
            return true;
        } catch (\Exception $e) {
            $db->rollBack();
            throw new ExceptionError("处理审批失败: " . $e->getMessage());
        }
    }
    
    /**
     * 撤销审批
     * @param int $instanceId 审批实例ID
     * @return bool
     */
    public static function cancelApproval(int $instanceId): bool
    {
        $db = dbMysql::getInstance();
        $db->beginTransaction();
        
        try {
            $instance = $db->table('approval_instances')
                ->where('id = :id', ['id' => $instanceId])
                ->one();
            
            if (!$instance || $instance['initiator_id'] != userModel::$qwuser_id) {
                throw new ExceptionError("无权撤销此审批");
            }
            
            if ($instance['status'] != 0) {
                throw new ExceptionError("审批已完成，无法撤销");
            }
            
            // 更新审批状态为撤销
            $db->table('approval_instances')
                ->where('id = :id', ['id' => $instanceId])
                ->update([
                    'status' => 3,
                    'completed_time' => date('Y-m-d H:i:s')
                ]);
            
            // 取消所有活动节点
            $db->table('approval_flows')
                ->where('instance_id = :instance_id AND is_active = 1', ['instance_id' => $instanceId])
                ->update(['is_active' => 0]);
            
            $db->commit();
            return true;
        } catch (\Exception $e) {
            $db->rollBack();
            throw new ExceptionError("撤销审批失败: " . $e->getMessage());
        }
    }
    
    /**
     * 获取审批状态
     * @param string $businessType 业务类型
     * @param int $businessId 业务ID
     * @return array|null
     */
    public static function getApprovalStatus(string $businessType, int $businessId): ?array
    {
        $db = dbMysql::getInstance();
        
        $instance = $db->table('approval_instances')
            ->where('business_type = :type AND business_id = :id', 
                ['type' => $businessType, 'id' => $businessId])
            ->order('id DESC')
            ->one();
        
        if (!$instance) {
            return null;
        }
        
        $flows = $db->table('approval_flows')
            ->where('instance_id = :id', ['id' => $instance['id']])
            ->order('node_index ASC')
            ->all();
        
        return [
            'instance' => $instance,
            'flows' => $flows
        ];
    }
    
    /**
     * 从配置获取审批流程
     * @param string $configName 配置名称
     * @return array|null
     */
    private static function getConfigByName(string $configName): ?array
    {
        $db = dbMysql::getInstance();
        $config = $db->table('config')
            ->where('key_name = :name', ['name' => $configName])
            ->one();
        
        if (!$config) {
            return null;
        }
        
        return json_decode($config['data'], true);
    }
    
    /**
     * 创建审批流程节点
     * @param int $instanceId 审批实例ID
     * @param array $config 审批配置
     * @return void
     */
    private static function createApprovalFlows(int $instanceId, array $config): void
    {
        $db = dbMysql::getInstance();
        $nodeIndex = 0;
        
        // 创建审批节点
        foreach ($config['nodes'] as $node) {
            $approverIds = self::resolveApprovers($node);
            
            foreach ($approverIds as $approverId) {
                $user = $db->table('qwuser')
                    ->where('id = :id', ['id' => $approverId])
                    ->one();
                
                $flowData = [
                    'instance_id' => $instanceId,
                    'node_index' => $nodeIndex,
                    'node_type' => 'approver',
                    'node_name' => $node['name'],
                    'approver_id' => $approverId,
                    'approver_name' => $user ? $user['wname'] : '',
                    'is_active' => $nodeIndex == 0 ? 1 : 0,
                    'created_time' => date('Y-m-d H:i:s')
                ];
                
                $flowId = $db->table('approval_flows')->insert($flowData);
                
                // 发送通知给第一个审批人
                if ($nodeIndex == 0) {
                    self::sendNotification(['id' => $flowId, 'approver_id' => $approverId, 'instance_id' => $instanceId]);
                }
            }
            
            $nodeIndex++;
        }
        
        // 创建抄送节点
        if (!empty($config['cc_users'])) {
            foreach ($config['cc_users'] as $userId) {
                $user = $db->table('qwuser')
                    ->where('id = :id', ['id' => $userId])
                    ->one();
                
                $flowData = [
                    'instance_id' => $instanceId,
                    'node_index' => 999, // 抄送节点放在最后
                    'node_type' => 'cc',
                    'node_name' => '抄送',
                    'approver_id' => $userId,
                    'approver_name' => $user ? $user['wname'] : '',
                    'is_active' => 0,
                    'created_time' => date('Y-m-d H:i:s')
                ];
                
                $db->table('approval_flows')->insert($flowData);
            }
        }
    }
    
    /**
     * 解析审批人
     * @param array $node 节点配置
     * @return array 审批人ID列表
     */
    private static function resolveApprovers(array $node): array
    {
        $db = dbMysql::getInstance();
        $approverIds = [];
        
        switch ($node['type']) {
            case 'user':
                if (!empty($node['user_id'])) {
                    $approverIds[] = $node['user_id'];
                }
                break;
                
            case 'role':
                if (!empty($node['role_id'])) {
                    $users = $db->table('qwuser')
                        ->where('role_id = :role_id', ['role_id' => $node['role_id']])
                        ->all();
                    
                    foreach ($users as $user) {
                        $approverIds[] = $user['id'];
                    }
                }
                break;
                
            case 'dept':
                if (!empty($node['dept_id'])) {
                    $dept = $db->table('department')
                        ->where('id = :id', ['id' => $node['dept_id']])
                        ->one();
                    
                    if ($dept && !empty($dept['manager_id'])) {
                        $approverIds[] = $dept['manager_id'];
                    }
                }
                break;
        }
        
        return $approverIds;
    }
    
    /**
     * 发送审批通知
     * @param array $flow 流程节点
     * @return void
     */
    private static function sendNotification(array $flow): void
    {
        $db = dbMysql::getInstance();
        
        $instance = $db->table('approval_instances')
            ->where('id = :id', ['id' => $flow['instance_id']])
            ->one();
        
        if (!$instance) {
            return;
        }
        
        $user = $db->table('qwuser')
            ->where('id = :id', ['id' => $flow['approver_id']])
            ->one();
        
        if (!$user) {
            return;
        }
        
        // 发送企业微信消息通知
        // 这里需要根据实际情况调用消息发送接口
        // 例如：
        // MessageService::send($user['wid'], "您有一个新的审批任务：{$instance['title']}");
    }
    
    /**
     * 发送审批结果消息
     * @param array $instance 审批实例
     * @param bool $approved 是否通过
     * @return void
     */
    private static function sendResultMessage(array $instance, bool $approved): void
    {
        $db = dbMysql::getInstance();
        
        $initiator = $db->table('qwuser')
            ->where('id = :id', ['id' => $instance['initiator_id']])
            ->one();
        
        if (!$initiator) {
            return;
        }
        
        $result = $approved ? '通过' : '拒绝';
        
        // 发送企业微信消息通知
        // 这里需要根据实际情况调用消息发送接口
        // 例如：
        // MessageService::send($initiator['wid'], "您的审批【{$instance['title']}】已{$result}");
    }
    
    /**
     * 更新业务状态
     * @param array $instance 审批实例
     * @param bool $approved 是否通过
     * @return void
     */
    private static function updateBusinessStatus(array $instance, bool $approved): void
    {
        $db = dbMysql::getInstance();
        
        switch ($instance['business_type']) {
            case 'prepare_audit':
                $status = $approved ? 1 : 2; // 1-通过, 2-拒绝
                $db->table('prepare_audit')
                    ->where('id = :id', ['id' => $instance['business_id']])
                    ->update(['approval_status' => $status]);
                break;
                
            // 可以添加其他业务类型
        }
    }
}

