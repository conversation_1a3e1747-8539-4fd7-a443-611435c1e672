<?php

namespace plugins\checkin\controller;

use plugins\checkin\models\holidayModel;
use plugins\checkin\models\ruleModel;
use plugins\checkin\models\userModel;
use core\lib\config;
use core\lib\db\dbCMysql;
use core\lib\db\dbMysql;

class holidayController
{

    // 获取规则列表
    public function getList()
    {
        $paras_list = array('holiday_name', 'status', 'page', 'page_size');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;

        $cdb = dbCMysql::getInstance();
        $cdb->table('holiday');
        $cdb->where('where is_delete = 0');
        if ($param['holiday_name']) {
            $cdb->andWhere('holiday_name like :holiday_name', ['holiday_name' => '%' . $param['holiday_name'] . '%']);
        }
        if (isset($param['status']) && in_array($param['status'], [0, 1])) {
            $cdb->andWhere('status = :status', ['status' => $param['status']]);
        }
        $cdb->order('id desc');
        $list = $cdb->pages($page, $limit);
        $user_ids = array_column($list['list'], 'user_id');
        $db = dbMysql::getInstance();
        $users = $db->table('qwuser')->whereIn('id', $user_ids)->field('id, wname')->list();
        $userMap = array_column($users, 'wname', 'id');

        foreach ($list['list'] as &$item) {
            $item['workday'] = json_decode($item['workday'], true);
            $item['nonworkday'] = json_decode($item['nonworkday'], true);
            $item['holiday'] = json_decode($item['holiday'], true);
            $item['user_name'] = $userMap[$item['user_id']];
        }

        returnSuccess($list);
    }

    // 新增/编辑等级规则
    public function add()
    {
        $paras_list = array('id', 'holiday_name', 'workday', 'nonworkday', 'holiday');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        empty($param['holiday_name']) && returnError('节日名称必填');
        !isset($param['workday']) && returnError('调休上班日期必填');
        !isset($param['nonworkday']) && returnError('节日日期必填');
        !isset($param['holiday']) && returnError('法定节假日必填');
        $workday = json_decode($param['workday'], true);
        $nonworkday = json_decode($param['nonworkday'], true);
        $holiday = json_decode($param['holiday'], true);
        if (!empty($workday) && !empty($nonworkday)) {
            foreach ($workday as $d) {
                if (strtotime($d) <= strtotime($nonworkday[1]) && strtotime($d) >= strtotime($nonworkday[0])) {
                    returnError('工作日'.$d.'在已在休息日期中');
                }
            }
        }

        if (!empty($holiday)) {
            foreach ($holiday as $d) {
                if (!empty($workday) && in_array($d, $workday)) {
                    returnError('法定节假日'.$d.'在调休上班日期中');
                }
                if (!empty($nonworkday) && !(strtotime($d) <= strtotime($nonworkday[1]) && strtotime($d) >= strtotime($nonworkday[0])) ) {
                    returnError('法定节假日'.$d.'不在在节日日期中');
                }
            }
        }

        // 获取所有的节假日
        $cdb = dbCMysql::getInstance();
        $cdb->table('holiday');
        $cdb->where('where is_delete = 0');
        $list = $cdb->list();

        foreach ($list as $item) {
            $item['workday'] = json_decode($item['workday'], true);
            $item['nonworkday'] = json_decode($item['nonworkday'], true);
            if (!empty($workday) && !empty($item['nonworkday'])) {
                foreach ($workday as $d) {
                    if (strtotime($d) <= strtotime($item['nonworkday'][1]) && strtotime($d) >= strtotime($item['nonworkday'][0])) {
                        returnError('工作日'.$d.'已在'.$item['holiday_name'].'休息日期中');
                    }
                }
            }
            if (!empty($nonworkday) && !empty($item['workday'])) {
                foreach ($item['workday'] as $d) {
                    if (strtotime($d) <= strtotime($nonworkday[1]) && strtotime($d) >= strtotime($nonworkday[0])) {
                        returnError('休息日中存在'.$item['holiday_name'].'工作日'.$d);
                    }
                }
            }
        }

        if ($param['id']) {
            $cdb = dbCMysql::getInstance();
            $cdb->table('holiday');
            $cdb->where('id = :id', ['id' => $param['id']]);
            $cdb->update([
                'user_id' => userModel::$qwuser_id,
                'holiday_name' => $param['holiday_name'],
                'workday' => $param['workday'],
                'nonworkday' => $param['nonworkday'],
                'holiday' => $param['holiday'],
            ]);
        } else {
            $cdb = dbCMysql::getInstance();
            $cdb->table('holiday');
            $cdb->insert([
                'user_id' => userModel::$qwuser_id,
                'holiday_name' => $param['holiday_name'],
                'workday' => $param['workday'],
                'nonworkday' => $param['nonworkday'],
                'holiday' => $param['holiday'],
            ]);
        }
        returnSuccess([], '操作成功');
    }

    // 删除规则
    public function delete()
    {
        $paras_list = array('id');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        empty($param['id']) && returnError('id必填');
        $cdb = dbCMysql::getInstance();
        $cdb->table('holiday');
        $cdb->where('id = :id', ['id' => $param['id']]);
        $one = $cdb->one();
        if (empty($one)) {
            returnError('节假日不存在');
        }
        $cdb->update(['is_delete' => 1]);
        returnSuccess([], '删除成功');
    }

    // 修改状态
    public static function changeStatus()
    {
        $paras_list = array('id', 'status');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        empty($param['id']) && returnError('id必填');
        if (!in_array($param['status'], [1, 0])) {
            returnError('是否启用错误');
        }
        $cdb = dbCMysql::getInstance();
        $cdb->table('holiday');
        $cdb->where('id = :id', ['id' => $param['id']]);
        $cdb->update(['status' => $param['status']]);
        returnSuccess([], '修改成功');
    }

    // 获取周期内的节假日
    public function getHolidayByMonth()
    {
        $paras_list = array('month');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        empty($param['month']) && returnError('月份必填');
        $start_time = date('Y-m-01', strtotime($param['month']));
        $end_time = date('Y-m-t', strtotime($param['month']));

        $res = holidayModel::getHolidayByTimeRange($start_time, $end_time);

        returnSuccess(['workday' => $res['workday'], 'nonworkday' => $res['nonworkday'], 'holiday' => $res['holiday']]);
    }

}