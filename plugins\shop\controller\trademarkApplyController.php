<?php

namespace plugins\shop\controller;

use core\lib\db\dbAMysql;
use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use Exception;
use plugins\shop\form\messagesFrom;
use plugins\shop\models\configModel;
use plugins\shop\models\trademarkApplyModel;
use plugins\shop\models\trademarkModel;
use plugins\shop\models\userModel;
use Rap2hpoutre\FastExcel\FastExcel;

class trademarkApplyController extends baseController
{
    private $model;

    public function __construct()
    {
        parent::__construct();
        $this->model = new trademarkApplyModel();
    }

    /**
     * 获取商标列表
     */
    public function getList()
    {
        $paras_list = ['brand_name', 'trademark_holder', 'transfer_lawyer', 'trademark_holder_pre', 'transfer_provider',
            'service_provider', 'certificate_number', 'original_storage', 'type', 'dep_id', 'user_id', 'operator',
            'status', 'bind_time', 'country', 'category', 'page', 'page_size'];
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $param['page'] = $param['page'] ?? 1;
        $param['page_size'] = $param['page_size'] ?? 10;

        $result = $this->model->getList($param);
        returnSuccess($result);
    }

    /**
     * 申请商标
     */
    public function apply()
    {
        $paras_list = [
            'dep_id'            => '部门ID',
            'country'           => '国家',
            'category'          => '类目',
            'reminder_interval' => '提醒间隔'
        ];
        $param = array_intersect_key($_POST, array_flip(array_keys($paras_list)));

        try {
            if (empty($param['dep_id']) || empty($param['country']) ||
                empty($param['category']) || empty($param['reminder_interval'])) {
                returnError('缺少必要参数');
            }

            $param['status'] = trademarkApplyModel::STATUS_RECEIVE_WAITING;
            $result = $this->model->applyTrademark($param);

            if ($result) {
                $users = configModel::noticeUser('trademark', 'apply', ['dep_id' => $param['dep_id']]);
                if (!empty($users)) {
                    $user_name = userModel::$wname;
                    $content = "【{$user_name}】申请【{$param['country']}_{$param['category']}】商标， 请及时跟进";
                    messagesFrom::senMeg($users, 1 , $content, $result, '', '商标申请');
                }
                $adb = dbAMysql::getInstance();
                // 添加定时任务
                $crontab_data = [
                    'is_crontab_task' => 1,
                    'link_id'         => $result,
                    'link_type'       => 1,
                    'link_module'       => 3,
                    'runtime'         => date('Y-m-d H:i:s', time() + ($param['reminder_interval'] * 60)),
                ];
                $adb->table('custom_crontab');
                $adb->insert($crontab_data);
            }
            returnSuccess('申请成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 分配商标
     */
    public function assign()
    {
        $paras_list = [
            'id'           => 'ID',
            'trademark_id' => '商标ID'
        ];
        $param = array_intersect_key($_POST, array_flip(array_keys($paras_list)));

        try {
            $this->model->assignToTrademark($param['id'], $param['trademark_id']);
            returnSuccess('分配成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    // 接收
    public function receive()
    {
        $paras_list = [
            'id'     => 'ID',
            'type'   => '类型',
            'status' => '状态',
        ];
        $param = array_intersect_key($_POST, array_flip(array_keys($paras_list)));

        try {
            $this->model->receiveApplication($param);
            returnSuccess('接收成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 取消申请
     */
    public function cancel()
    {
        if (empty($_POST['id'])) {
            returnError('缺少ID');
        }

        try {
            $result = $this->model->cancelApplication($_POST['id']);
            returnSuccess('取消成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 启动注册（自注册类型）
     */
    public function startRegistration()
    {
        if (empty($_POST['id'])) {
            returnError('缺少商标ID');
        }

        $paras_list = [
            'id'               => 'ID',
            'brand_name'       => '品牌名',
            'trademark_holder' => '商标持有人',
            'domain_id'        => '域名',
            'email_id'         => '注册邮箱',
            'service_provider' => '服务商'
        ];
        $param = array_intersect_key($_POST, array_flip(array_keys($paras_list)));

        try {
            $this->model->dataValidCheck($param, $paras_list);
            $this->model->startRegistration($param['id'], $param);
            returnSuccess('操作成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }


    /**
     * 更新商标进展
     */
    public function updateProgress()
    {
        $paras_list = [
            'id'     => 'ID',
            'status' => '状态',
            'remark' => '进展描述'
        ];
        $param = array_intersect_key($_POST, array_flip(array_keys($paras_list)));

        try {
            $this->model->dataValidCheck($param, $paras_list);
            $this->model->updateProgress($param);
            returnSuccess('更新成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 商标信息登记（外购类型）
     */
    public function registerInfo()
    {
        if (empty($_POST['id'])) {
            returnError('缺少商标ID');
        }

        $paras_list = [
            'id'                        => 'ID',
            // 基本信息
            "brand_name"                => "品牌名",
            // 自注册信息
            "register_date"             => "注册/购买时间",
            "domain_id"                 => "域名",
            "email_id"                  => "注册邮箱",
            "service_provider"          => "服务商",
            // 外购信息
            "transfer_lawyer"           => "转让律师",
            "trademark_holder"          => "商标持有人",
            "trademark_holder_pre"      => "商标原持有人",
            "transfer_date"             => "转让日期",
            "transfer_provider"         => "转让服务商",
            "price"                     => "价格",
            "currency"                  => "币种",
            // 共用信息
            "certificate_date"          => "下证日期",
            "certificate_number"        => "注册号/证书号",
            "original_storage"          => "原件保管地",
            "validity_period"           => "商标有效期",
            "earliest_declaration_date" => "最早宣誓日期",
//            "shop_id"                   => "备案店铺",
            "record_date"               => "备案日期",
            "use_status"                => "使用状态",
            "remark"                    => "备注",
        ];
        $param = array_intersect_key($_POST, array_flip(array_keys($paras_list)));

        try {
            $this->model->registerTrademarkInfo($param['id'], $param);
            returnSuccess('登记成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }


    /**
     * 获取商标详情
     */
    public function getDetail()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        try {
            $detail = $this->model->getDetail($id);
            if (!$detail) {
                returnError('数据不存在');
            }
            returnSuccess($detail);
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    // 获取日志
    public static function getLog()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $db = dbShopMysql::getInstance();
        $list = $db->table('operation_log')->where('where table_name = :table_name and table_id = :table_id',
            ['table_name' => 'trademark_apply', 'table_id' => $id])->order('id desc')->list();

        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        $model = new trademarkApplyModel();
        $maps = $model::getMaps();

        foreach ($list as &$item) {
            $item['operator_name'] = $users[$item['operator']] ?? '';
            $attach = json_decode($item['attach'], true);
            $item['before'] = $attach['before'] ?? null;
            $item['before'] = $model->formatItem($item['before'], $maps);
            $item['after'] = $attach['after'] ?? null;
            $item['after'] = $model->formatItem($item['after'], $maps);
            unset($item['attach']);
        }
        returnSuccess($list);
    }
}
