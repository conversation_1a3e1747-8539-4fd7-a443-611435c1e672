<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/16 13:42
 */

namespace plugins\goods\common;

use plugins\goods\models\userModel;
use core\lib\db\dbMysql;
use Overtrue\Pinyin\Pinyin;
use setasign\Fpdi\Fpdi;

class publicMethod
{
    /**
     * @param string $name
     * @return void 获取汉字首拼
     */
    public static function getFirstPinyin(string $name){
        $pinyin = Pinyin::abbr($name);
        $pinyin = str_replace(' ','',strtoupper($pinyin));
        return $pinyin;
    }
    /**
     * @param $img_list
     * @param $url
     * @param $type  0其他，1产品图
     * @return int|string|void   //生成压缩包
     */
    public static function setZip($img_list,$url,$goods_id,$file_type=0) {
        $file_count = 0;
        // 创建压缩文件;
        $zipFile = SELF_FK . $url;
        $zip = new \ZipArchive();
        if ($zip->open($zipFile, \ZipArchive::CREATE) !== TRUE) {
            exit("Cannot open <$zipFile>\n");
        }
        foreach ($img_list as $v) {
            $url_split = explode('.',$v['url']);
            $ext_suffix = end($url_split);
            $img_dir = SELF_FK.$v['url'];
//            if (in_array($ext_suffix, ['jpg', 'png', 'gif','jpeg'])) {
//                //加内容
//                $new_img = self::addTxtToPic($img_dir,$ext_suffix,$goods_id,$file_type);
//                if ($new_img) {
//                    if ($zip->addFile($new_img, basename($new_img))) {
//                        $file_count ++;
//                        continue;
//                    }
//
//                }
//            }
            if ($zip->addFile($img_dir, basename($img_dir))) {
                $file_count ++;
            }
        }
        $zip->close();
        return $file_count;
    }
    //项目pdf+鲜章
    static public function addPorjectPdfSeal($pdf_url)
    {
        $pdf_url = SELF_FK.$pdf_url;
        $img_url = SELF_FK.'/public/static/api/seal/reply.png';
        //文件保存路径
        $save_path = "/public/upload/project_pdf_reply/".userModel::$wid.'/'.date('Ymd');
        $save_url = SELF_FK.$save_path;
        if (!file_exists($save_url)) {
            mkdir($save_url, 0777, true);
        }
        $save_path = $save_path.'/'.time() . '.pdf';
        $save_url = SELF_FK.$save_path;
        //填写的内容
        $pdf = new Fpdi();
        // 导入现有的PDF文件 ;获取PDF文件的总页数
        $numPages = $pdf->setSourceFile($pdf_url);
        // 遍历每一页
        for ($pageNo = 1; $pageNo <= $numPages; $pageNo++) {
            // 添加一个新页面，大小和原始页面一致
            $pdf->AddPage();
            // 导入当前页
            $tplIdx = $pdf->importPage($pageNo);
            // 将导入的页面添加到当前文档中
            $pdf->useTemplate($tplIdx, 0, 0);
            // 获取当前页面的尺寸
            $pageSize = $pdf->getTemplateSize($tplIdx);
//            $pageWidth = $pageSize['width'];
//            $pageHeight = $pageSize['height'];
////            // 计算图片在右上角的位置
//            $imageX = $pageWidth - 30; // 10是右边距
//            $imageY = $pageHeight; // 10是上边距
            // 在右上角添加图片
//            $pdf->Image($img_url, $imageX, $imageY, 160, 160);
            $pdf->Image($img_url,150,0,40,40);
        }

        // 输出PDF
        $pdf->Output($save_url, 'F');
        return $save_path;
    }
    //图片加内容
    public static function addTxtToPic(string $url, string $ext_suffix,int $goods_id = 0,int $file_type = 0) {
        $txt = 'Envision';
        switch ($file_type) {
            case 1;
                $txt .= '-product';break;
            default:
                $txt .= '-other';break;
        }
        if (!empty($goods_id)) {
            $db = dbMysql::getInstance();
            $goods_info = $db->table('goods_new')
                ->where('where id=:id',['id'=>$goods_id])
                ->field('e_name')
                ->one();
            if (!empty($goods_info['e_name'])) {
                $txt .= '-'.$goods_info['e_name'];
            }
        }
        $txt .= '-'.date('YmdHi').'-'.userModel::$qwuser_id;
        $paddingChar = '-';
        $paddedString = mb_str_pad($txt, 50, $paddingChar, STR_PAD_RIGHT);
        //植入图片临时路径
        $save_path = SELF_FK."/public/temp/add_txt_imgs/".userModel::$wid;
        $temp_img = $save_path.'/'.uniqid('e').'.'.$ext_suffix;
        if (!file_exists($save_path)) {
            mkdir($save_path, 0777, true);
        }
        if (embedTextInImage($url,$temp_img,$paddedString)) {
//            dd($temp_img);
            return $temp_img;
        } else {
            return false;
        }


    }
}