# !/bin/bash
# 领星msku报告数据同步脚本
# 获取第一个参数
#offset=0
token='01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0'
#先更商品
while true; do
    # 发送POST请求，这里假设URL为http://example.com/api，根据实际情况替换
    response=$(curl -s -X POST -d "token=$token" 'http://171.223.214.187:8901/task/lingXingApi/synGoods')
#    response=$(curl -s -X POST -d "token=$token" 'http://39.101.133.112:8082/task/lingXingApi/synGoods')
     echo "$response"
    # 从响应中提取code字段的值
    code=$(echo "$response" | grep -oP '"code":\K-?\d+')
    # 检查code字段的值
    current_time=$(date +"%Y-%m-%d %H:%M:%S")
    if [ "$code" -ne 2 ]; then
        echo "code=$code，继续请求$current_time"
    else
       break ;
    fi
done
echo "商品情同步完成，$current_time"
# 在更新商品详情
while true; do
    # 发送POST请求，这里假设URL为http://example.com/api，根据实际情况替换
    response=$(curl -s -X POST -d "token=$token" 'http://171.223.214.187:8901/task/lingXingApi/synGoodsDetailBatch')
#    response=$(curl -s -X POST -d "token=$token" 'http://39.101.133.112:8082/task/lingXingApi/synGoodsDetailBatch')
     echo "$response"
    # 从响应中提取code字段的值
    code=$(echo "$response" | grep -oP '"code":\K-?\d+')
    # 检查code字段的值
    current_time=$(date +"%Y-%m-%d %H:%M:%S")
    if [ "$code" -ne 2 ]; then
      echo "code=$code，继续请求$current_time"
    else
       break ;
    fi
done
echo "商品详情同步完成，$current_time"


