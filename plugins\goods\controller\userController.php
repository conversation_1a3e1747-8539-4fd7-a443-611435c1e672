<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/8 14:51
 */
namespace plugins\goods\controller;


use core\lib\db\dbMysql;
use plugins\goods\models\roleModel;
use plugins\goods\models\userModel;

class userController
{
    //通过token获取用户信息
    public function getUserInfo(){
        $data = [
            //基础信息
            'id'=>userModel::$qwuser_id,
            'wid'=>userModel::$wid,
            'wname'=>userModel::$wname,
            'avatar'=>userModel::$avatar,
            'is_super'=>userModel::$is_super,
            //其它信息
            'auth'=>userModel::$auth,
            'role_type'=>userModel::getRoleType(),
            'is_developer'=>userModel::isDeveloper(),
        ];
        returnSuccess(['data'=>$data]);
    }
    //产品用户列表
    public function getList(){
        $paras_list = array('wname', 'qw_partment_id', 'wstatus', 'page', 'page_size', 'order_by', 'roles_id');
        $param = arrangeParam($_POST, $paras_list);
        $db = dbMysql::getInstance();
        $db->table('qwuser','a')->field('a.id,a.wid,a.wname,a.wphone,a.wdepartment_ids,a.wstatus,a.updated_at,a.is_manage,a.avatar,a.position')->where('where a.is_delete = 0');
        if (!empty($param['wname'])) {
            $db->andWhere('and a.wname like :wname',['wname'=>'%'.$param['wname'].'%']);
        }
        if (!empty(trim($param['qw_partment_id']))) {
            $qw_partment_ids = explode(',',$param['qw_partment_id']);
            $db->andWhere('and JSON_CONTAINS(a.wdepartment_ids,:qw_partment_id)',['qw_partment_id'=>json_encode($qw_partment_ids,JSON_NUMERIC_CHECK)]);
        }
        if (!empty($param['wstatus'])) {
            $db->andWhere('and a.wstatus = :wstatus',['wstatus'=>$param['wstatus']]);
        }
        $order_str = '';
        if (!empty($param['order_by'])) {
            $order_by = json_decode($param['order_by'],true);
            $order_str = '';
            foreach ($order_by as $k=>$ord){
                $order_str .= 'a.'.$k.' '.$ord.',';
            }
        }
        if(empty($order_str)) {
            $db->order('a.id desc');
        } else {
            $db->order(trim($order_str,','));
        }
        if (!empty($param['roles_id'])) {
            $roles_id = getArryForType(json_decode($param['roles_id']));
            if ($roles_id) {
                $user_roles = $db->queryAll('select qwuser_id from oa_user_roles where role_id in ('.implode(',',$roles_id).') group by qwuser_id');
                if (count($user_roles)) {
                    $user_roles = array_column($user_roles,'qwuser_id');
                    $db->whereIn('id', $user_roles);
                }
            }
        }
        $data = $db->pages($param['page'], $param['page_size']);
        $data['list'] = roleModel::getUserListRole($data['list']);
        returnSuccess($data);
    }
//
//    //根据角色类型来获取用户
    public function getUserByRole(){
        $paras_list = array('role_type');
        $param = arrangeParam($_POST, $paras_list);
        $db = dbMysql::getInstance();
        $role = $db->table('role')
            ->where('where type=:type',['type'=>$param['role_type']])
            ->field('id')
            ->one();
        $result = [];
        if ($role) {
            $user_list = $db->table('user_roles')
                ->where('where role_id=:role_id',['role_id'=>$role['id']])
                ->field('qwuser_id')
                ->list();
            if (count($user_list)) {
                $ids = array_column($user_list,'qwuser_id');
                $result = $db->table('qwuser')
                    ->whereIn('id',$ids)
                    ->field('id,wid,wname')
                    ->list();
            }
        }
        returnSuccess($result);
    }
//
//    //将企微成员设为管理员
//    public function setAdminUser(){
//        $paras_list = array('user_name', 'password', 'qw_userid');
//        $param = arrangeParam($_POST, $paras_list);
//        if (!$param['user_name'] || !$param['password'] || !$param['qw_userid']) {
//            SetReturn(-1, "缺少必传参数");
//        }
//        $db = dbMysql::getInstance();
//        $db->beginTransaction();
//        try {
//            //修改用户为管理员
//            $wuser = $db->query('select id from oa_qwuser where wid = :qw_userid and is_delete = 0 limit 1',['qw_userid'=>$param['qw_userid']]);
//            if (!$wuser) {
//                SetReturn( -1,'用户不存在');
//                $db->rollBack();
//            }
//            $db->table('qwuser');
//            $db->where('where wid = :qw_userid',['qw_userid'=>$param['qw_userid']]);
//            $db->update(['is_manage'=>1,'updated_at'=>date('Y-m-d H:i:s')]);
//
//            //查询用户是否存在
//            $user = $db->query('select id,uniqueid from oa_user where qw_userid = :qw_userid and is_delete = 0', ['qw_userid'=>$param['qw_userid']]);
//            if (!$user) {
//                $db->table('user');
//                $unique_id = getuniqId('oa');
//                $pwd = getPwdMd5($param['password'], $unique_id);
//                $in_data = ['name'=>$param['user_name'],'pwd'=>$pwd,'created_at'=>time(),'uniqueid'=>$unique_id,'qw_userid'=>$param['qw_userid']];
//                if ($db->insert($in_data)) {
//                    $db->commit();
//                    returnSuccess([],'设置成功');
//                } else {
//                    SetReturn(-1,'设置失败');
//                }
//            } else {
//                $pwd = getPwdMd5($param['password'], $user['uniqueid']);
//                $in_data = ['name'=>$param['user_name'],
//                    'pwd'=>$pwd,
//                    'is_delete'=>0,
//                    'updated_at'=>time()
//                ];
//                $db->table('user');
//                $db->where('where qw_userid = :qw_userid and is_delete = 0',['qw_userid'=>$param['qw_userid']]);
//                $db->update($in_data);
//                $db->commit();
//                returnSuccess([],'设置成功');
//            }
//        } catch (ExceptionError $e) {
//            $db->rollBack();
//            throw new ExceptionError($e->getMessage());
//        }
//
//
//    }
//
//    //删除管理员身份
//    public function delAdminUser(){
//        $paras_list = array('qw_userid');
//        $param = arrangeParam($_POST, $paras_list);
//        $id = $param['qw_userid'];
//        if (!$id) {
//            SetReturn(-1, "缺少必传参数或参数错误");
//        }
//        $db = dbMysql::getInstance();
//        try {
//            $db->beginTransaction();
//            //修改状态
//            $wuser = $db->query('select id from oa_qwuser where wid = :qw_userid and is_delete = 0 limit 1',['qw_userid'=>$param['qw_userid']]);
//            if (!$wuser) {
//                SetReturn( -1,'用户不存在');
//                $db->rollBack();
//            }
//            $db->table('qwuser');
//            $db->where('where wid = :qw_userid',['qw_userid'=>$param['qw_userid']]);
//            $db->update(['is_manage'=>0,'updated_at'=>date('Y-m-d H:i:s')]);
//            //删除管用户角色信息
//            $db->table('user_roles');
//            $db->where('where qwuser_id = :qwuser_id', ['qwuser_id'=>$wuser['id']]);
//            $db->delete();
//
//            //修改信息
//            $db->table('user')->field('id');
//            $where_str = 'where qw_userid=:qw_userid and is_delete = 0';
//            $user = $db->where($where_str,['qw_userid'=>$id])->one();
//            if ($user) {
//                $db->where('where id = :id',['id'=>$user['id']]);
//                $db->update(['is_delete'=>1, 'updated_at'=>time()]);
//            }
//
//            $db->commit();
//            returnSuccess([],'删除成功');
//        } catch (ExceptionError $e) {
//            $db->rollBack();
//            throw new ExceptionError($e->getMessage());
//        }
//
//    }
//
//    //修改员工登录密码
//    public function updatePwd(){
//        $paras_list = array('qw_userid', 'password');
//        $param = arrangeParam($_POST, $paras_list);
//        $id = $param['qw_userid'];
//        if (!$id) {
//            SetReturn(-1, "缺少必传参数或参数错误");
//        }
//        if (!$param['password']) {
//            SetReturn(-1, "密码不能为空");
//        }
//
//        $db = dbMysql::getInstance();
//        $user = $db->query("select id,uniqueid from oa_user where qw_userid = :qw_userid and is_delete = 0", ['qw_userid'=>$param['qw_userid']]);
//        if (!$user) {
//            SetReturn(-1, "该用户还不是管理员");
//        }
//        $pwd = getPwdMd5($param['password'], $user['uniqueid']);
//
//        $db->table('user');
//        $db->where('where id = :id',['id'=>$user['id']]);
//        $up_data = ['pwd'=>$pwd,'updated_at'=>time()];
//        if ($db->update($up_data)) {
//            returnSuccess([],'修改成功');
//        } else {
//            SetReturn(-1,'修改失败');
//        }
//    }
//
//    //删除员工
//    public function delQwUser(){
//        $paras_list = array('id');
//        $param = arrangeParam($_POST, $paras_list);
//        $id = (int)$param['id'];
//        if (!$id) {
//            SetReturn(-1,'缺少必传参数');
//        }
//        $db = dbMysql::getInstance();
//        $db->table('qwuser');
//        $db->where('where id = :id',['id'=>$id]);
//        $up_data = ['is_delete'=>1];
//        if ($db->update($up_data)) {
//            returnSuccess([],'修改成功');
//        } else {
//            SetReturn(-1,'修改失败');
//        }
//    }
//
//    //个人修改密码
//    public function updateUserPwd(){
//        $paras_list = array('password');
//        $param = arrangeParam($_POST, $paras_list);
//
//        $prefix = config::get('token_key_prefix', 'app');
//        $key = $prefix.USER_TOKEN;
//        $redis = (new \core\lib\predisV())::$client;
//        $login_data = $redis->hMGet($key, ['id','wid']);
//
//        $wid = $login_data['wid'];
////        if (!$id) {
////            SetReturn(-1, "缺少必传参数或参数错误");
////        }
//        if (!$param['password']) {
//            SetReturn(-1, "密码不能为空");
//        }
//        $db = dbMysql::getInstance();
//        $user = $db->query("select id,uniqueid from oa_user where qw_userid = :qw_userid and is_delete = 0", ['qw_userid'=>$wid]);
//        if (!$user) {
//            SetReturn(-1, "该用户还不是管理员");
//        }
//
//        $pwd = getPwdMd5($param['password'], $user['uniqueid']);
//
//        $db->table('user');
//        $db->where('where id = :id',['id'=>$user['id']]);
//        $up_data = ['pwd'=>$pwd,'updated_at'=>time()];
//        if ($db->update($up_data)) {
//            returnSuccess([],'修改成功');
//        } else {
//            SetReturn(-1,'修改失败');
//        }
//    }
//
    //角色设置
    public function setRole(){
        $paras_list = array('qwuser_id', 'roles_id');
        $param = arrangeParam($_POST, $paras_list);
        $id = $param['qwuser_id'];
        if (!$id || empty($param['roles_id'])) {
            SetReturn(-1, "缺少必传参数或参数错误");
        }
        $roles_id = json_decode($param['roles_id']);
        $db = dbMysql::getInstance();
        //查询之前的角色
        $old_roles = $db->queryAll('select * from oa_user_roles where qwuser_id = :qwuser_id', ['qwuser_id'=>$param['qwuser_id']]);
        $old_role_ids = array_column($old_roles,'role_id');
        //找出不同
        $need_del_role = array_diff($old_role_ids,$roles_id);
        $need_add_role = array_diff($roles_id,$old_role_ids);
        $db->beginTransaction();
        try {
            //删除之前的数据
            if (count($need_del_role)) {
                $db->table('user_roles')->where('where qwuser_id = :qwuser_id', ['qwuser_id'=>$id]);
                $db->whereIn('role_id',$need_del_role);
                $db->delete();
            }
            //新增角色
            if (count($need_add_role)) {
                $insert_sql = 'insert into oa_user_roles (qwuser_id,role_id,created_at) values ';
                $insert_data = [
                    'qwuser_id'=>$id,
                    'created_at'=>date('Y-m-d H:i:s')
                ];
                foreach ($need_add_role as $k=>$v) {
                    $insert_sql.=("(:qwuser_id,:role_id_$k,:created_at),");
                    $insert_data["role_id_$k"] = $v;
                }
                $insert_sql = trim($insert_sql,',');
                $db->query($insert_sql, $insert_data);
            }

            $db->commit();
            returnSuccess([],'设置成功');
        } catch (ExceptionError $e) {
            $db->rollBack();
            throw new ExceptionError($e->getMessage());
        }

    }
//
//    public function getDetail(){
//        $paras_list = array('id');
//        $param = arrangeParam($_GET, $paras_list);
//        $id = (int)$param['id'];
//        if (!$id) {
//            SetReturn(-1, "缺少必传参数或参数错误");
//        }
//        $db = dbMysql::getInstance();
//        $db->table('qwuser');
//        $db->where('where id = '.$id);
//        $db->field('id,wstatus,wdata');
//        $user = $db->one();
//        //获取角色
//        $db->table('user_roles','a');
//        $db->leftJoin('role','b','b.id = a.role_id');
//        $db->where('where a.qwuser_id =:qwuser_id',['qwuser_id'=>$id]);
//        $user['roles'] = $db->field('b.id,b.role_name')->list();
//        $user['wdata'] = json_decode($user['wdata'],true);
//        returnSuccess(['data'=>$user]);
//    }
//
//
//    /**
//     * @return void   常用用户查询
//     * @throws ExceptionError
//     */
//    public function getOftenUsedUser(){
//        $db = dbMysql::getInstance();
//        $db->table('user_often_used','a');
//        $db->leftJoin('qwuser','b','b.id = a.used_qwuser_id');
//        $db->where('where qwuser_id=:qwuser_id',['qwuser_id'=>userModel::$qwuser_id]);
//        $db->order('a.updated_at desc');
//        $db->field('b.id,b.wid,b.wname,b.avatar,b.position');
//        $list = $db->list();
//        returnSuccess(['data'=>$list]);
//    }
//
//    public function setOftenUsedUser() {
//        $paras_list = array('qwuser_ids');
//        $param = arrangeParam($_POST, $paras_list);
//        if (!empty($param['qwuser_ids'])) {
//            $qwuser_ids = $need_add_ids = json_decode($param['qwuser_ids']);
//            $db = dbMysql::getInstance();
//            //查询这些用户是否在数据中
//            $db->table('user_often_used');
//            $db->where('where qwuser_id=:qwuser_id',['qwuser_id'=>userModel::$qwuser_id]);
//            $db->field('used_qwuser_id');
//            $db->whereIn('used_qwuser_id',$qwuser_ids);
//            $old_user = $db->list();
//            //修改
//            $db->update(['updated_at'=>time()]);
//
//            if (count($old_user)) {
//                $old_used_qwuser_ids = array_column($old_user,'used_qwuser_id');
//                $need_add_ids = array_diff($qwuser_ids,$old_used_qwuser_ids);
//            }
//            if (count($need_add_ids)) {
//                $insert_sql = 'insert into oa_user_often_used (qwuser_id,used_qwuser_id,updated_at) values ';
//                $insert_data = ['qwuser_id'=>userModel::$qwuser_id,'updated_at'=>time()];
//                foreach ($need_add_ids as $k=>$v) {
//                    $insert_sql .= "(:qwuser_id,:used_qwuser_id_$k,:updated_at),";
//                    $insert_data["used_qwuser_id_$k"] = $v;
//                }
//                $insert_sql = trim($insert_sql,',');
//                $db->query($insert_sql,$insert_data);
//            }
//            returnSuccess('','');
//        }
//    }
//

}