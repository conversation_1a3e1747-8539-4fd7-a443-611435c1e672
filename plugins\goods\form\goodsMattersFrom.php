<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/2 11:37
 */

namespace  plugins\goods\form;

use plugins\goods\models\goodsProjectAbnormalModel;
use plugins\goods\models\userModel;
use core\lib\config;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;
use core\lib\log;

class goodsMattersFrom
{
    //提交结果
    public static array $submit_result = [];
    public static int $submit_result_success_count = 0;
    public static int $submit_result_error_count = 0;
    //事件审核人需发送的消息
    public static array $project_event_check_msg = [];
    //交办消息
    public static array $change_matter_msg = [];
    public static array $check_node_event_type = [];//审核节点时节点中包含的事件类型
    /**
     * @param $goods_id
     * @return void  推送给负责人取英文名
     */
    public static function addGoodsEnameMatter($goods_id,$goods_name='') {
        //新增待办事项
        $db = dbMysql::getInstance();
        $matter = $db->query('select id from oa_goods_matters where goods_id=:goods_id and type=1 and status=0',['goods_id'=>$goods_id]);
        if (!$matter) {
            $manage_info = json_decode(configFrom::getConfigByName('goods_ename_manage'),true);
            $insert_data = [
                'user_id'=>userModel::$qwuser_id,
                'matter_name'=>"新增产品",
                'goods_id'=>$goods_id,
                'type'=>1,
                'node_name'=>'录入产英文名',
                'qwuser_id'=>$manage_info[0]['id'],
                'created_at'=>time(),
            ];

            $db->table('goods_matters');
            $db->insert($insert_data);
            //推送消息
            $w_userids = array_column($manage_info,'wid');
            $text = "请您给产品取英文名";
            $other_data = [
                'user_id'=>userModel::$qwuser_id,
                'model_id'=>$goods_id,
                'msg_type'=>5,
            ];
            messagesFrom::senMeg($w_userids, $text, $other_data);
        }
    }

    /**
     * @param $goods_id
     * @param $e_name
     * @return void 推送给负责人取中文名
     * @throws \core\lib\ExceptionError
     */
    public static function addGoodsNameMatter($goods_id,$manage_info) {
        $db = dbMysql::getInstance();
        $matter = $db->query('select id from oa_goods_matters where goods_id=:goods_id and type=2 and status=0',['goods_id'=>$goods_id]);
        if (!$matter) {
            $manage_info = json_decode($manage_info,true);
            //新增待办事项
            $insert_data = [
                'user_id'=>userModel::$qwuser_id,
                'matter_name'=>"新增产品",
                'goods_id'=>$goods_id,
                'type'=>2,
                'qwuser_id'=>$manage_info[0]['id'],
                'node_name'=>'录入产品中文名',
                'created_at'=>time(),
            ];
            $db->table('goods_matters');
            $db->insert($insert_data);
            //推送消息
            $w_userids = array_column($manage_info,'wid');
            $text = "请您给产品取中文名";
            $other_data = [
                'user_id'=>userModel::$qwuser_id,
                'model_id'=>$goods_id,
                'msg_type'=>5,
            ];
            messagesFrom::senMeg($w_userids, $text,$other_data);
        }
    }


    /**
     * @param $project_id
     * @param $node_index
     * @param $event_index
     * @param $is_advance_submit  //是否事件内容已经完成  1是，0否
     * @param $event_index
     * @return void
     * @throws \core\lib\ExceptionError  修改事件或节点 待办事项为已完成()
     */
    public static function setProjectMatterStatus1($create_type,$project_id,$node_index,$event_index=null,$is_advance_submit = 1,$delay_hour = 0){
        $db = dbMysql::getInstance();
        $db->table('goods_matters');
        $db->where('where qwuser_id=:qwuser_id and goods_project_id=:project_id and node_index=:node_index and create_type=:create_type',['project_id'=>$project_id,'node_index'=>$node_index,'qwuser_id'=>userModel::$qwuser_id,'create_type'=>$create_type]);
        if (is_null($event_index)) {
            $db->andWhere('and event_index is null');
        } else {
            $db->andWhere('and event_index=:event_index',['event_index'=>$event_index]);
        }
        $matter_info = $db->one();
        if (!$matter_info) {
            SetReturn(-1,'未找到您的待办事项');
        }
        $update_data = [
            'status'=>1,
            'completion_time'=>time()
        ];
        if ($is_advance_submit == 0) {
            $update_data['is_advance_submit'] = 1;
            $update_data['after_delay_hour'] = $delay_hour;
            $update_data['after_expected_time'] = strtotime('+'.$delay_hour.' hours', time());
        }
        $db->where('where id=:id',['id'=>$matter_info['id']])->update($update_data);
    }

    /**
     * @param $node_info
     * @return void 给下一个节点负责 所有成员生成待办事项
     */
    public static function addNextNodeMatter(int $project_id,array $node_info,array $goods_info,int $next_node_index) {
        $goods_manage_info = json_decode($goods_info['manage_info'],true);
        $msg_array = [];
//        $goods_imgs_request = [];
        $db = dbMysql::getInstance();
        $db->table('goods_matters');

        $common_data = [
            'matter_name'=>$goods_info['matter_name'],
            'user_id'=>userModel::$qwuser_id,
            'goods_project_id'=>$project_id,
            'create_type'=>0,
            'goods_id'=>$goods_info['id'],
            'created_at'=>time(),
        ];

        foreach ($node_info as $k=>$node) {
            //节点管理员只有一个
            $node_manage_info = json_decode($node['manage_info'],true);
            $insert_data = [
                'qwuser_id'=>$node_manage_info[0]['id'],
                'type'=>3,
                'node_id'=>$node['id'],
                'node_name'=>$node['node_name'],
                'node_index'=>$next_node_index.'-'.$k,
                'event_id'=>0,
                'event_name'=>'',
                'event_index'=>null,
                'event_type'=>0,
                'expected_day'=>$node['expected_day'],
                'expected_time'=>strtotime("+{$node['expected_day']} days", time()),
            ];
            $insert_data = array_merge($common_data,$insert_data);
            $db->insert($insert_data);
            //事件处理
            $event_list = $node['event_detail'];
            //消息
            $msg_array[] = [
                'wids'=>[$node_manage_info[0]['wid']],
                'msg'=>"【{$insert_data['matter_name']}】流程已到达【{$node['node_name']}】，请及时处理，注意待办事件的预计完成时间。",
                'other_data'=>[
                    'user_id'=>$goods_manage_info['id']??0,
                    'model_id'=>$goods_info['id'],
                    'node_index'=>$insert_data["node_index"],
                    'msg_type'=>5
                ]
            ];
            foreach ($event_list as $k1=>$event_) {
                foreach ($event_ as $k2=>$event) {
                    $manage_info_event = json_decode($event['manage_info'],true);
                    $event_comome_data = [
                        "type"=>3,
                        'node_id'=>$node['id'],
                        'node_name'=>$node['node_name'],
                        'node_index'=>$next_node_index.'-'.$k,
                        "event_type"=>$event['event_type'],
                        "event_id"=>$event['id'],
                        "event_name"=>$event['event_name'],
                        "event_index"=> $k1.'-'.$k2,
                        "expected_day"=>$event['expected_day'],
                        "expected_time"=>strtotime("+{$event['expected_day']} days", time()),
                    ];
                    //消息数据
                    foreach ($manage_info_event as $k3=>$v3) {
                        $insert_data = [
                            'qwuser_id'=>$v3['id']
                        ];
                        $insert_data = array_merge($common_data,$insert_data,$event_comome_data);
                        $matter_id = $db->insert($insert_data);
                        if ($event['event_type'] == 2) {
                            $event['matter_id']=$matter_id;
//                            $goods_imgs_request[] = $event;
                        }
                    }
                    $msg_array[] = [
                        'wids'=>array_column($manage_info_event,'wid'),
                        'msg'=>"【{$insert_data['matter_name']}】流程已到达【{$node['node_name']}/{$event['event_name']}】，请及时处理，注意待办事件的预计完成时间。",
                        'other_data'=>[
                            'user_id'=>$goods_manage_info['id']??0,
                            'model_id'=>$goods_info['id'],
                            'node_index'=>$insert_data["node_index"],
                            'event_index'=>$insert_data["event_index"],
                            'msg_type'=>5
                        ]
                    ];
                }
            }
        }
        //图片的待办事项需生成一条图片需求
//        if (count($goods_imgs_request)) {
//            imgsRequestFrom::setAppPicRequest($goods_imgs_request,$goods_info,$project_id);
//        }
        $msg_array = array_merge($msg_array,imgsRequestFrom::$msg_info);
        //要发的消息赋值
        messagesFrom::$next_node_info_data = $msg_array;
    }

    /**
     * @param array $new_matters
     * @param int $goods_id
     * @param int $project_id
     * @return void
     * @throws \core\lib\ExceptionError  节点待办修改
     */
    public static function updateNodeAgentForProject(array $new_matters,int $goods_id, int $project_id, string $matter_name) {
        $db = dbMysql::getInstance();
        $send_msg = [];
        $project_log = [];
        foreach ($new_matters as $v) {
            $matters_where = [
                'qwuser_id'=>$v['agent_info']['manage']['id'],
                'project_id'=>$project_id,
                'goods_id'=>$goods_id,
                'node_index'=>$v['node_index'],
            ];
            $matters_info = $db->query('select * from oa_goods_matters where qwuser_id=:qwuser_id and goods_id=:goods_id and goods_project_id=:project_id and node_index=:node_index and event_index is null',$matters_where);
            if ($matters_info) {
                //修改待办人
                $db->table('goods_matters')->where('where id=:id',['id'=>$matters_info['id']]);
                $db->update([
                    'qwuser_id'=>$v['agent_info']['agent']['id'],
                    'assignment_time'=>date('Y-m-d H:i:s'),
                ]);
                //交办记录
                $db->table('goods_matters_agent_log');
                $db->insert([
                    'user_id'=>userModel::$qwuser_id,
                    'goods_matters_id'=>$matters_info['id'],
                    'agent_id'=>$v['agent_info']['manage']['id'],
                    'last_agent_id'=>$v['agent_info']['agent']['id'],
                    'created_time'=>date('Y-m-d H:i:s'),
                ]);
                $send_msg[] = [
                    'wids'=>[$v['agent_info']['agent']['wid']],
                    'msg'=>"用户【".userModel::$wname."】已将{$matter_name}流程的【{$v['node_name']}】交办给您，请及时处理。",
                    'other_data'=>[
                        'user_id'=>userModel::$qwuser_id,
                        'model_id'=>$project_id,
                        'node_index'=>$v['node_index'],
                        'event_index'=>null,
                        'msg_type'=>3
                    ],
                ];
                $project_log[] = [
                    'goods_id'=>$goods_id,
                    'project_id'=>$project_id,
                    'describe'=>"将{$matter_name}流程的{$v['node_name']}待办从【{$v['agent_info']['manage']['wname']}】交办到【{$v['agent_info']['agent']['wname']}】",
                    'matter_name'=>$matter_name,
                ];
            }
        }
        if (count($send_msg)) {
            messagesFrom::$project_agent_info = array_merge(messagesFrom::$project_agent_info,$send_msg);
        }
        if (count($project_log)) {
            goodsProjectLogFrom::$change_agent_data = array_merge(goodsProjectLogFrom::$change_agent_data,$project_log);
        }
    }
    public static function updateEventAgentForProject(array $new_matters, int $goods_id, int $project_id, string $matter_name) {
        $db = dbMysql::getInstance();
        $send_msg = [];
        $project_log = [];
        foreach ($new_matters as $v) {
            $matters_where = [
                'qwuser_id'=>$v['agent_info']['manage']['id'],
                'project_id'=>$project_id,
                'goods_id'=>$goods_id,
                'node_index'=>$v['node_index'],
                'event_index'=>$v['event_index']
            ];
            $matters_info = $db->query('select * from oa_goods_matters where qwuser_id=:qwuser_id and goods_id=:goods_id and goods_project_id=:project_id and node_index=:node_index and event_index=:event_index',$matters_where);
            if ($matters_info) {
                //修改待办人
                $db->table('goods_matters')->where('where id=:id',['id'=>$matters_info['id']]);
                $db->update([
                    'qwuser_id'=>$v['agent_info']['agent']['id'],
                    'assignment_time'=>date('Y-m-d H:i:s'),
                ]);
                //交办记录
                $db->table('goods_matters_agent_log');
                $db->insert([
                    'user_id'=>userModel::$qwuser_id,
                    'goods_matters_id'=>$matters_info['id'],
                    'agent_id'=>$v['agent_info']['manage']['id'],
                    'last_agent_id'=>$v['agent_info']['agent']['id'],
                    'created_time'=>date('Y-m-d H:i:s'),
                ]);
                //消息
                $send_msg[] = [
                    'wids'=>[$v['agent_info']['agent']['wid']],
                    'msg'=>"用户【".userModel::$wname."】已将【{$matter_name}】流程的【{$v['node_name']}/{$v['event_name']}】交办给您，请及时处理。",
                    'other_data'=>[
                        'user_id'=>userModel::$qwuser_id,
                        'model_id'=>$project_id,
                        'node_index'=>$v['node_index'],
                        'event_index'=>null,
                        'msg_type'=>3
                    ],
                ];
                $project_log[] = [
                    'goods_id'=>$goods_id,
                    'project_id'=>$project_id,
                    'describe'=>"将{$matter_name}流程的【{$v['node_name']}/{$v['event_name']}】待办事项从【{$v['agent_info']['manage']['wname']}】交办到【{$v['agent_info']['agent']['wname']}】",
                    'matter_name'=>$matter_name,
                ];
            }

        }
        if (count($send_msg)) {
            messagesFrom::$project_agent_info = array_merge(messagesFrom::$project_agent_info,$send_msg);
        }
        if (count($project_log)) {
            goodsProjectLogFrom::$change_agent_data = array_merge(goodsProjectLogFrom::$change_agent_data,$project_log);
        }
    }

    /**
     * @param $matter_name
     * @param $goods_id
     * @param $node_name
     * @param $create_type   0办理，1审核，2审批
     * @param $type 1英文名，2中文名称，3流程详情，4图片需求列表，5说明书列表
     * @param $agent_id
     * @return void 创建待办-非流程
     */
    public static function addCreateMatter($matter_name,$goods_id,$node_name,$create_type,$type,$agent_id,$model_id,$expected_time){
        $insert_data = [
            'user_id'=>userModel::$qwuser_id,
            'matter_name'=>$matter_name,
            'goods_id'=>$goods_id,
            'type'=>$type,
            'model_id'=>$model_id,
            'node_name'=>$node_name,
            'create_type'=>$create_type,
            'qwuser_id'=>$agent_id,
            'created_at'=>time(),
            'expected_time'=>$expected_time,
        ];
        $db = dbMysql::getInstance();
        $id = $db->table('goods_matters')->insert($insert_data);
        return $id;
    }


    /**
     * @param $matter_name
     * @param $goods_id
     * @param $node_name
     * @param $create_type
     * @param $type
     * @param $project_id
     * @param $model_id
     * @param $expected_time
     * @return mixed
     * @throws ExceptionError  创建单个待办-流程
     */
    public static function addCreatePojectMatter($matter_name,$goods_id,$node_name,$create_type,$agent_id,$project_id,$expected_time,$model_id){
        $insert_data = [
            'user_id'=>userModel::$qwuser_id,
            'qwuser_id'=>$agent_id,
            'matter_name'=>$matter_name,
            'goods_id'=>$goods_id,
            'type'=>3,
            'node_name'=>$node_name,
            'create_type'=>$create_type,
            'goods_project_id'=>$project_id,
            'created_at'=>time(),
            'expected_time'=>$expected_time,
            'model_id'=>$model_id,
        ];
        $db = dbMysql::getInstance();
        $id = $db->table('goods_matters')->insert($insert_data);
        return $id;
    }

    /**
     * @param $id
     * @param $from_type //来自哪儿，0待办列表，1产品图片审核(已废除)
     * @throws ExceptionError  流程的 提交待办事件
     */
    public static function submitMattersById($id,$from_type){
        $db = dbMysql::getInstance();
        $matter = $db->table('goods_matters')
            ->where('where id =:id',['id'=>$id])
            ->one();
        if (!$matter) {
            SetReturn(-1,'未查询到待办事项');
        }
        if ($matter['qwuser_id'] != userModel::$qwuser_id  && $from_type == 0) {
            self::setSubmitResultData($matter,0,'非本人无权提交');
            return;
        }
        if ($matter['create_type'] != 0) {
            self::setSubmitResultData($matter,0,'该待办类型不可直接提交，请的点击查看进入相关页面处理。');
            return;
        }
        if ($matter['status'] == 1) {
            self::setSubmitResultData($matter,0,'已办不可重复办理');
            return;
        }
        if ($matter['status'] == 3) {
            self::setSubmitResultData($matter,0,'已关闭的事项不可操作');
            return;
        }
        $db->table('goods_matters')
            ->where('where id =:id',['id'=>$id])
            ->update([
                'status'=>1,
                'completion_time'=>time()
            ]);
        $type = $matter['type'];
        if ($type == 3) {
            if ($matter['event_index']) {
                //完成事件
                self::submitEventByMatter($matter,$from_type);
            } else {
                //完成节点
                self::submitNodeByMatter($matter,$from_type);
            }
        } else {
            self::setSubmitResultData($matter, 0, '该待办类型不可直接提交，请的点击查看进入相关页面处理。');
            return;
        }
        return;
    }
    public static function submitEventByMatter($matter,$from_type) {
        $type = $matter['event_type'];
        $project_id = $matter['goods_project_id'];
        $node_index = $matter['node_index'];
        $event_index = $matter['event_index'];
        $db = dbMysql::getInstance();
        $project_data =  $db->query('select id,is_stop,goods_id,tpl_name,status,tpl_data,current_index,flow_path_id,sample_batch,matter_name from oa_goods_project where id=:project_id',['project_id'=>$project_id]);
        if (!$project_data) {
            self::setSubmitResultData($matter,0,'项目流程不存在');
            return;
        }
        if ($project_data['status'] == 4) {
            self::setSubmitResultData($matter,0,'项目流程已被废除不可操作');
            return;
        }
        if ($project_data['is_stop']) {
            self::setSubmitResultData($matter,0,'该项目流程已暂停，不可操作');
            return;
        }
        $goods_info = $db->query('select id,goods_name,manage_info from oa_goods_new where id=:goods_id',['goods_id'=>$project_data['goods_id']]);
        $tpl_data = json_decode($project_data['tpl_data'],true);
        $current_node = goodsProjectFrom::getCurrentNodeData($tpl_data,$node_index);
        $current_event = goodsProjectFrom::getCurrentEventData($current_node,$event_index);
        //状态验证
        if ($current_event['status'] == 2) {
            self::setSubmitResultData($matter,1);
            return;
        }

        //当前事件是否以提交
        if ($type!= $current_event['event_type']) {
            self::setSubmitResultData($matter,0,'处理的事件类型不匹配');
            return;
        }
        //获取下方是否有数据未处理完成
        $count = goodsProjectAbnormalModel::getAbnormalCount(3, $project_id, $node_index,$event_index);
        if ($count > 0) {
            self::setSubmitResultData($matter,0,'该事件内有异常还未处理完');
            return;
        }
        $event_type_name = config::getDataName('event_type',$type);
        switch ($type) {
            case 1:
                break;
            case 10:
                break;
            case 2:
                //图片
                $db = dbMysql::getInstance();
                $imgs_request = $db->table('imgs_request')
                    ->where('where goods_id=:goods_id and type=1 and is_delete = 0',['goods_id'=>$project_data['goods_id']])
                    ->one();
                if ($imgs_request['status'] != 3) {
                    self::setSubmitResultData($matter,0,'图片需求还未完成');
                    return;
                }
                break;
            default:
                self::setSubmitResultData($matter,0,$event_type_name.'不可直接提交');
                return;
        }
        $tpl_data = goodsProjectFrom::setEventStatus($tpl_data,$node_index,$event_index,2);
        $db->table('goods_project')
            ->where('where id = :project_id',['project_id'=>$project_id])
            ->update([
                'tpl_data'=>json_encode($tpl_data,JSON_UNESCAPED_UNICODE),
                'updated_time'=>date('Y-m-d H:i:s')
            ]);
        //操作记录
        $matter_name = $project_data['matter_name'];
        $goods_info['matter_name'] = $matter_name;
        $msg = "提交了【{$current_node['node_name']}】节点下的【{$current_event['event_name']}】事件";
        log::setGoodsProjectLog($project_data['goods_id'],$project_id,$msg, $matter_name,$type);
        //消息通知,通知人+抄送人+节点负责人+产品开发
        messagesFrom::sendMsgForSubmitEvent($project_id,$current_node,$goods_info,$node_index,$event_index);
        //本地记录数据
        log::goodspProjectLog()->info('['.userModel::$wid.']将'.$project_id.'修改为：'.$msg);
        self::setSubmitResultData($matter,1);
    }
    public static function submitNodeByMatter($matter,$from_type) {
        $project_id = $matter['goods_project_id'];
        $node_index = $matter['node_index'];
        $db = dbMysql::getInstance();
        $project_data =  $db->query('select id,is_stop,matter_name,goods_id,tpl_name,status,tpl_data,current_index,flow_path_id,sample_batch from oa_goods_project where id=:project_id',['project_id'=>$project_id]);
        if(!$project_data) {
            self::setSubmitResultData($matter,0,'当前项目流程不存在');
            return;
        }
        if ($project_data['status'] == 4) {
            self::setSubmitResultData($matter,0,'项目流程已被废除不可操作');
            return;
        }
        if ($project_data['is_stop']) {
            self::setSubmitResultData($matter,0,'该项目流程已暂停，不可操作');
            return;
        }
        $goods_info = $db->query('select * from oa_goods_new where id=:goods_id',['goods_id'=>$project_data['goods_id']]);
        $goods_info['matter_name'] = $project_data['matter_name'];
        $tpl_data = json_decode($project_data['tpl_data'],true);
        $current_node = goodsProjectFrom::getCurrentNodeData($tpl_data,$node_index);
        //状态验证
        if ($current_node['status'] == 2) {
            self::setSubmitResultData($matter,1);
            return;
        }
        //验证可操作性
        $res = self::verifySubmitNodeByMatter($matter,$current_node,$project_id,$node_index);
        if (!$res) {
            return;
        }
        //更新当前节点
        $tpl_data = goodsProjectFrom::setNodeStatus($tpl_data,$node_index,2,$project_id);
        $update_data = [
            'updated_time'=>date('Y-m-d H:i:s'),
        ];
        //节点集合中是否所有节点已完成
        $is_all_finished = goodsProjectFrom::getNodeListStatus($tpl_data[$project_data['current_index']]);
        if ($is_all_finished){
            if (isset($tpl_data[$project_data['current_index']+1])) {
                //获取下个节点简要信息
                $next_node_ = goodsProjectFrom::getNextNodeInfo($tpl_data,$project_data['current_index']);
                $update_data['current_index'] = $project_data['current_index']+1;
                $update_data['current_node_info'] = json_encode($next_node_);
                //更新下个节点开始时间
                $tpl_data = goodsProjectFrom::setNodeBegintime($tpl_data,$update_data['current_index']);
                $next_node = $tpl_data[$update_data['current_index']];
                //生成下个节点待办事项
                self::addNextNodeMatter($project_id,$next_node,$goods_info,$update_data['current_index']);
            } else {
                //完成流程
                $update_data['status'] = 2;
                $update_data['complete_time'] = time();
                //将流程中的待办全部设置为已办
                $db->table('goods_matters')
                    ->where('where goods_project_id=:project_id and status=0',['project_id'=>$project_id])
                    ->update(['status'=>1]);
            }
        }
        $update_data['tpl_data'] = json_encode($tpl_data,JSON_UNESCAPED_UNICODE);
        $db->table('goods_project')
            ->where('where id = :project_id',['project_id'=>$project_id])
            ->update($update_data);

        //操作记录
        $matter_name = $project_data['matter_name'];
        $goods_info['matter_name'] = $matter_name;
        $msg = "提交了【{$current_node['node_name']}】节点。";
        log::setGoodsProjectLog($project_data['goods_id'],$project_id,$msg, $matter_name);
        //消息通知当前节点,通知人+抄送人+节点负责人+产品开发
        messagesFrom::sendMsgForSubmitNode($project_id,$current_node,$goods_info,$node_index);
        //通知下个节点的开发
        if ($is_all_finished) {
            messagesFrom::sendNextNodeInfo();
            if (in_array(10,self::$check_node_event_type)) {
                imgsRequestFrom::setAppPicRequest($goods_info);
            }
        }
        //本地记录数据
        log::goodspProjectLog()->info('['.userModel::$wid.']将'.$project_id.'修改为：'.$msg);
        self::setSubmitResultData($matter,1);
    }
    //节点提交的验证
    private static function verifySubmitNodeByMatter($matter,$current_node,$project_id,$node_index) {
        //节点内是否还有事件未完成
        $is_finish_event = 1;
        $is_finish_event_check = 1;
        $event_type_array = [];
        foreach ($current_node['event_detail'] as $e_list) {
            foreach ($e_list as $event) {
                if (!in_array($event['event_type'],$event_type_array)) {
                    $event_type_array[] = $event['event_type'];
                }
                if ($event['status'] != 2) {
                    $is_finish_event = 0;
                    continue;
                }
                if ($event['event_type'] == 4) {
                    self::setSubmitResultData($matter,0,'硬件检测节点不可直接提交。');
                    return false;
                }
                //完成的事件才能审核
                if ($event['status'] == 2 && ($event['event_type'] == 6 || $event['need_check'] == 1)) {
                    //只有下首单需要审核
                    if (empty($event['check_user_info'])) {
                        $is_finish_event_check = 0;
                        continue;
                    }
                    $check_user_info = json_decode($event['check_user_info'],true);
                    foreach ($check_user_info as $check_) {
                        if ($check_['is_pass'] !=1) {
                            $is_finish_event_check = 0;
                        }
                    }
                }
            }
        }
        if ($is_finish_event == 0) {
            self::setSubmitResultData($matter,0,'节点内有事件还未完成');
            return false;
        }
        if ($is_finish_event_check == 0) {
            self::setSubmitResultData($matter,0,'节点内有事件未审核');
            return false;
        }
        //节点内是否异常事件已经处理完成
        $count = goodsProjectAbnormalModel::getAbnormalCount(2, $project_id, $node_index);
        if ($count > 0) {
            self::setSubmitResultData($matter,0,'该节点内有异常还未处理完');
            return false;
        }
        self::$check_node_event_type = $event_type_array;
        return true;
    }

    //添加流程中的审核待办事件
    public static function setEventCheckMatter($project,$node_info,$event_info,$node_index,$event_index){
        $db = dbMysql::getInstance();
        $check_user_info = json_decode($event_info['check_user_info'],true);
        foreach ($check_user_info as $user) {
            $where_data = [
                'qwuser_id'=>$user['id'],
                'goods_project_id'=>$project['id'],
                'node_index'=>$node_index,
                'event_index'=>$event_index,
            ];
            $db->table('goods_matters')
                ->where('where qwuser_id=:qwuser_id and goods_project_id=:goods_project_id and node_index=:node_index and status = 0 and create_type=1 and event_index =:event_index',$where_data);
            $matter = $db->one();

            if (!$matter) {
                $db->table('goods_matters')
                    ->insert([
                        'user_id'=>userModel::$qwuser_id,
                        'matter_name'=>$project['matter_name'],
                        'qwuser_id'=>$user['id'],
                        'type'=>3,
                        'create_type'=>1,
                        'goods_id'=>$project['goods_id'],
                        'model_id'=>$project['id'],
                        'node_id'=>$node_info['id'],
                        'goods_project_id'=>$project['id'],
                        'node_index'=>$node_index,
                        'node_name'=>$node_info['node_name'],
                        'event_name'=>$event_info['event_name'],
                        'event_index'=>$event_index,
                        'event_id'=>$event_info['id'],
                        'event_type'=>$event_info['event_type'],
                        'created_at'=>time(),
                    ]);
            }
        }

        self::$project_event_check_msg = [
            'wids'=>array_column($check_user_info,'wid'),
            'msg'=>messagesFrom::getMsgTxt(8,$project['matter_name'],$node_info['node_name'],$event_info['event_name']),
            'other_data'=>[
                'user_id'=>userModel::$qwuser_id,
                'model_id'=>$project['id'],
                'node_index'=>$node_index,
                'event_index'=>$event_index,
                'msg_type'=>7
            ]
        ];


    }
    //添加硬件检测审批事项
    public static function addApprovalMatter($project,$agent_id,$node_name,$node_index){
        $insert_data = [
            'user_id'=>userModel::$qwuser_id,
            'goods_project_id'=>$project['id'],
            'matter_name'=>$project['matter_name'],
            'goods_id'=>$project['goods_id'],
            'type'=>3,
            'model_id'=>$project['id'],
            'node_name'=>$node_name,
            'create_type'=>2,
            'event_type'=>4,
            'node_index'=>$node_index,
            'qwuser_id'=>$agent_id,
            'created_at'=>time(),
        ];
        $db = dbMysql::getInstance();
        $db->table('goods_matters');
        $db->insert($insert_data);
    }
    //交办
    public static function changeMatterAgentById($matter,$agent_user) {
        if ($matter['status'] == 1) {
            self::setSubmitResultData($matter,0,'已完成');
            return;
        }
        elseif ($matter['create_type'] != 0)
        {
            self::setSubmitResultData($matter,0,'审核/审批/异常暂不可交办');
            return;
        }
        elseif ($matter['type'] != 3)
        {
            self::setSubmitResultData($matter,0,'流程外的待办事项的交办请到对应模块儿交办');
            return;
        }
        else
        {
            $db = dbMysql::getInstance();
            //流程信息更改
            if ($matter['type'] == 3 && $matter['goods_project_id']>0) {
                $project =  $db->query('select id,goods_id,matter_name,tpl_name,status,tpl_data,current_index,flow_path_id,sample_batch,is_stop,status from oa_goods_project where id=:project_id',['project_id'=>$matter['goods_project_id']]);
                if(!$project) {
                    self::setSubmitResultData($matter, 0, '当前项目流程不存在');
                    return;
                }
                if ($project['is_stop']) {
                    self::setSubmitResultData($matter,0,'该项目流程已暂停，不可操作');
                    return;
                }
                if ($project['status'] == 4) {
                    self::setSubmitResultData($matter,0,'该项目流程已废除，不可操作');
                    return;
                }
                $tpl_data = json_decode($project['tpl_data'],true);
                $current_node = goodsProjectFrom::getCurrentNodeData($tpl_data,$matter['node_index']);
                if ($current_node['status'] == 2) {
                    self::setSubmitResultData($matter,0,'该节点已完成');
                    return;
                }
                if (is_null($matter['event_index'])) {
                    //节点
                    $current_node = self::updateNodeManage($current_node,$agent_user,$matter);
                    if (!$current_node) {
                        return;
                    }
                } else {
                    //事件
                    $current_event = goodsProjectFrom::getCurrentEventData($current_node,$matter['event_index']);
                    $event_index_array = explode('-',$matter['event_index']);
                    if ($current_event['status'] == 2) {
                        self::setSubmitResultData($matter,0,'该事件已完成');
                        return;
                    }
                    $current_event = self::updateEventManage($current_event,$agent_user,$matter);
                    if (!$current_event) {
                        return;
                    }
                    $current_node['event_detail'][$event_index_array[0]][$event_index_array[1]] = $current_event;

                }
                $node_index_array = explode('-',$matter['node_index']);
                $tpl_data[$node_index_array[0]][$node_index_array[1]] = $current_node;
                $db->table('goods_project')
                    ->where('where id = :id',['id'=>$project['id']])
                    ->update(['tpl_data'=>json_encode($tpl_data,JSON_UNESCAPED_UNICODE)]);
            } else {
                self::setSubmitResultData($matter,0,'非流程事项，暂不可交办');
                return;
            }
            //交办记录
            $db = dbMysql::getInstance();
            $db->table('goods_matters_agent_log');
            $db->insert([
                'user_id'=>userModel::$qwuser_id,
                'goods_matters_id'=>$matter['id'],
                'agent_id'=>$matter['qwuser_id'],
                'last_agent_id'=>$agent_user['id'],
                'created_time'=>date('Y-m-d H:i:s'),
            ]);
            //保存修改待办人员
            $db->table('goods_matters')
                ->where('where id=:id',['id'=>$matter['id']])
                ->update(['qwuser_id'=>$agent_user['id']]);
            self::$change_matter_msg[] = [
                'wids'=>[$agent_user['wid']],
                'msg'=>messagesFrom::getMsgTxt(13,$matter['matter_name'],$matter['node_name'],$matter['event_name']),
                'other_data'=>[
                    'user_id'=>userModel::$qwuser_id,
                    'model_id'=>($matter['goods_project_id']>0?$matter['goods_project_id']:$matter['model_id']),
                    'node_index'=>$matter['node_index'],
                    'event_index'=>$matter['event_index'],
                    'msg_type'=>3
                ],
            ];
            self::setSubmitResultData($matter,1);
        }
    }



    //修改节点负责人
    private static function updateNodeManage($current_node,$agent_user,$matter) {
        $node_nanage = json_decode($current_node['manage_info'],true);
        $node_wids = array_column($node_nanage,'wid');
        if (in_array($agent_user['wid'],$node_wids)) {
            self::setSubmitResultData($matter,0,'已是该节点处理人');
            return false;
        } else {
            $has_man = 0;
            foreach ($node_nanage as $k=>$manage) {
                if ($manage['id'] == $matter['qwuser_id']) {
                    $has_man = 1;
                    $node_nanage[$k] = $agent_user;
                    break;
                }
            }
            if (!$has_man) {
                self::setSubmitResultData($matter,0,'流程节点中未找到该待办人');
                return false;
            }
        }
        $current_node['manage_info'] = json_encode($node_nanage,JSON_UNESCAPED_UNICODE);
        return $current_node;
    }
    //修改事件负责人
    private static function updateEventManage($current_event,$agent_user,$matter) {
        $event_manage = json_decode($current_event['manage_info'],true);
        $node_wids = array_column($event_manage,'wid');
        if (in_array($agent_user['wid'],$node_wids)) {
            self::setSubmitResultData($matter,0,'交办人已是该事件处理人');
            return false;
        } else {
            $has_man = 0;
            foreach ($event_manage as $k=>$manage) {
                if ($manage['id'] == $matter['qwuser_id']) {
                    $has_man = 1;
                    $event_manage[$k] = $agent_user;
                    break;
                }
            }
            if (!$has_man) {
                self::setSubmitResultData($matter,0,'流程事件中未找到该待办人');
                return false;
            }
        }
        $current_event['manage_info'] = json_encode($event_manage,JSON_UNESCAPED_UNICODE);
        return $current_event;
    }

    //非流程事项，交办修改-修改交办数据。（单个修改）
    public static function setChangeAgent($matter,$agent_user) {
        //交办记录
        $db = dbMysql::getInstance();
        $db->table('goods_matters_agent_log');
        $db->insert([
            'user_id'=>userModel::$qwuser_id,
            'goods_matters_id'=>$matter['id'],
            'agent_id'=>$matter['qwuser_id'],
            'last_agent_id'=>$agent_user['id'],
            'created_time'=>date('Y-m-d H:i:s'),
        ]);
        //保存修改待办人员
        $db->table('goods_matters')
            ->where('where id=:id',['id'=>$matter['id']])
            ->update(['qwuser_id'=>$agent_user['id']]);
        self::$change_matter_msg[] = [
            'wids'=>[$agent_user['wid']],
            'msg'=>messagesFrom::getMsgTxtForImgRequest(1,$matter['matter_name'],$matter['node_name']),
            'other_data'=>[
                'user_id'=>userModel::$qwuser_id,
                'model_id'=>$matter['model_id'],
                'msg_type'=>3,
            ]
        ];
    }

    private static function setSubmitResultData($matter,$success,$msg='') {
        self::$submit_result[] = [
            'id'=>$matter['id'],
            'matter_name'=>$matter['matter_name'],
            'node_name'=>$matter['node_name'],
            'success'=>$success,
            'msg'=>$msg,
        ];
        if ($success) {
            self::$submit_result_success_count = self::$submit_result_success_count+1;
        } else {
            self::$submit_result_error_count = self::$submit_result_error_count+1;
        }
    }

}