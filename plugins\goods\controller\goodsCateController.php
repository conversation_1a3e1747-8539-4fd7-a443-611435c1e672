<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/22 14:22
 */
namespace  plugins\goods\controller;

use plugins\goods\models\userModel;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;
use core\lib\log;

class goodsCateController
{
    public function getList()
    {
        $paras_list = array('cate_name','p_id');
        $param = arrangeParam($_POST, $paras_list);
        $where_str = 'where is_delete = 0';
        $where_array = [];
        if (!empty($param['cate_name'])) {
            $where_str .= ' and cate_name like :cate_name';
            $where_array['cate_name'] = '%'.$param['cate_name'].'%';
        }
        $db = dbMysql::getInstance();
        $db->table('goods_cate');
        $db->field('id,cate_name,p_id,has_3D_model');
        $db->where($where_str, $where_array);
        $data = $db->list();

        if (count($data)) {
            function child($list, $pid) {
                $new_list = [];
                $i = 0;
                foreach ($list as $k=>$cate) {
                    if ($cate['p_id'] == $pid) {
                        $new_list[$i] = $cate;
                        unset($list[$k]);
                        $new_list[$i]['child'] = child($list,$cate['id']);
                        $i++;
                    }
                }
                return $new_list;
            }
            $new_list = child($data,0);
        }
        returnSuccess($new_list);
    }

    //新增修改
    public function editCate()
    {
        returnError('暂停使用');
        $paras_list = array('list');
        $param_ = arrangeParam($_POST, $paras_list);
        $list = json_decode($param_['list'],true);
        $db = dbMysql::getInstance();
        $db->beginTransaction();
        try {
            foreach ($list as $param) {
                $id = empty($param['id'])?0:(int)$param['id'];
                $p_id = (int)$param['p_id'];
                if (empty($param['cate_name'])) {
                    SetReturn(-1, '分类名称不能为空');
                }
                if ($id) {
                    $cate = $db->query('select id from oa_goods_cate where id = '.$id);
                    if (!$cate) {
                        SetReturn(-1, '未找到分类【'.$param['cate_name'].'】');
                    }
                    $db->table('goods_cate');
                    $db->where('where id = '.$id);
                    $update_data = ['cate_name'=>$param['cate_name'],'updated_at'=>date('Y-m-d H:i:s')];
                    $db->update($update_data);
                    log::goodsCateLog()->info('用户【'.userModel::$wid.'】修改：'.json_encode($param,JSON_UNESCAPED_UNICODE));
                } else {
                    $data_ = [
                        'p_id'=>$p_id,
                        'cate_name'=>$param['cate_name'],
                        'created_at'=>date('Y-m-d H:i:s')
                    ];
                    $db = dbMysql::getInstance();
                    $db->table('goods_cate');
                    if ($db->insert($data_)) {
                        log::goodsCateLog()->info('用户【'.userModel::$wid.'】新增：'.json_encode($param,JSON_UNESCAPED_UNICODE));
                    } else {
                        SetReturn(-1, '添加失败');
                    }
                }
            }
            $db->commit();
            SetReturn(0, '操作成功');
        } catch (ExceptionError $e){
            $db->rollBack();
            SetReturn(-1,$e->getMessage());
        }

    }

    //删除
    public function delCate(){
        returnError('暂停使用');
        $id = $_POST['id'];
        $db = dbMysql::getInstance();
        $list = $db->table('goods_cate')
            ->where('where p_id=:p_id and is_delete=0',['p_id'=>$id])
            ->count();
        if ($list) {
            SetReturn(0, '请先删除该分类的子分类');
        } else {
            $db->table('goods_cate')
                ->where('where id=:id',['id'=>$id])
                ->update(['is_delete'=>1,'del_user_id'=>userModel::$qwuser_id]);
            SetReturn(0, '操作成功');
        }
    }
}