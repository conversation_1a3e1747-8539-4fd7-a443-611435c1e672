<?php
/**
 * @author=>zhangguoming
 * @Time=>2024/6/21 9:43
 */

namespace financial\models;

use core\lib\db\dbFMysql;

class mskuReportModel
{
    public static $import_key_list = [
        //基础信息
        '项目'=>'project_name',
        '运营部门'=>'project_part',
        "运营人员"=>"yunying",
        "日期"=>"reportDateMonth",
        "型号"=>"goods_model",
        "店铺"=>"storeName",
        "供应商名称"=>"supplier_name",
        "MSKU"=>"msku",
        "ASIN"=>"asin",
        "父ASIN"=>"parentAsin",
        "国家"=>"country",
        "SKU"=>"localSku",
        "品名"=>"localName",
        "标题"=>"itemName",
        "Listing负责人"=>"principalRealname",
        "开发负责人"=>"productDeveloperRealname",
        "一级分类"=>"categoryName1",
        "二级分类"=>"categoryName2",
        "三级分类"=>"categoryName3",
        "品牌"=>"brandName",
        "币种"=>"currencyCode",
        "listing标签id"=>"listingTagIds",
        //平台收入
        "FBA销量"=>"fbaSalesQuantity",
        "FBM销量"=>"fbmSalesQuantity",
        "FBM补（换）货量"=>"reshipFbmProductSalesQuantity",
        "FBM补（换）货退回量"=>"reshipFbmProductSaleRefundsQuantity",
        "FBA补（换）货量"=>"reshipFbaProductSalesQuantity",
        "FBA补（换）货退回量"=>"reshipFbaProductSaleRefundsQuantity",
        "多渠道销量"=>"mcFbaFulfillmentFeesQuantity",
        "SD广告销售额"=>"adsSdSales",
        "SP广告销售额"=>"adsSpSales",
        "SB广告销售额"=>"sharedAdsSbSales",
        "SBV广告销售额"=>"sharedAdsSbvSales",
        "SD广告销量"=>"adsSdSalesQuantity",
        "SP广告销量"=>"adsSpSalesQuantity",
        "SB广告销量"=>"sharedAdsSbSalesQuantity",
        "SBV广告销量"=>"sharedAdsSbvSalesQuantity",
        "FBA销售额"=>"fbaSaleAmount",
        "FBM销售额"=>"fbmSaleAmount",
        "买家运费"=>"shippingCredits",
        "促销折扣"=>"promotionalRebates",
        "FBA库存赔偿"=>"fbaInventoryCredit",
        "赔偿量"=>"fbaInventoryCreditQuantity",
        "COD"=>"cashOnDelivery",
        "退款量"=>"refundsQuantity",
        "退款率"=>"refundsRate",
        "退货量（可售）"=>"fbaReturnsSaleableQuantity",
        "退货量（不可售）"=>"fbaReturnsUnsaleableQuantity",
        "退货率"=>"fbaReturnsQuantityRate",
        "包装收入"=>"giftWrapCredits",
        "买家交易保障索赔额"=>"guaranteeClaims",
        "积分抵减收入"=>"costOfPoIntegersGranted",
        "清算收入"=>"fbaLiquidationProceeds",
        "亚马逊运费赔偿"=>"amazonShippingReimbursement",
        "Safe-T索赔"=>"safeTReimbursement",
        "Netco交易"=>"netcoTransaction",
        "赔偿收入"=>"reimbursements",
        "追索收入"=>"clawbacks",
        "其他收入-其他"=>"others",
        "清算调整"=>"fbaLiquidationProceedsAdjustments",
        "混合VAT收入"=>"sharedComminglingVatIncome",
        "FBA销售退款额"=>"fbaSalesRefunds",
        "FBM销售退款额"=>"fbmSalesRefunds",
        "买家运费退款额"=>"shippingCreditRefunds",
        "买家包装退款额"=>"giftWrapCreditRefunds",
        "促销折扣退款额"=>"promotionalRebateRefunds",
        "买家拒付"=>"chargebacks",
        "积分抵减退回"=>"costOfPoIntegersReturned",
        "平台费退款项"=>"sellingFeeRefunds",
        "发货费退款项"=>"fbaTransactionFeeRefunds",
        "其他订单费退款项"=>"otherTransactionFeeRefunds",
        "运输标签费退款"=>"shippingLabelRefunds",
        "交易费用退款额"=>"refundAdministrationFees",
        "积分费用"=>"pointsAdjusted",
        "广告退款额"=>"refundForAdvertiser",
        //平台支出
        "平台费"=>"platformFee",
        "FBA发货费(FBA)"=>"fbaDeliveryFee",
        "FBA发货费(多渠道)"=>"mcFbaDeliveryFee",
        "其他订单费用"=>"otherTransactionFees",
        "FBA国际物流货运费"=>"sharedFbaIntegerernationalInboundFee",
        "调整费用"=>"adjustments",
        "SP广告"=>"adsSpCost",
        "SB广告"=>"adsSbCost",
        "SBV广告"=>"adsSbvCost",
        "SD广告"=>"adsSdCost",
        "差异分摊"=>"sharedCostOfAdvertising",
        "订阅费"=>"sharedSubscriptionFee",
        "秒杀费"=>"sharedLdFee",
        "优惠券"=>"sharedCouponFee",
        "早期评论人计划"=>"sharedEarlyReviewerProgramFee",
        "VINE"=>"sharedVineFee",
        "其他仓储费"=>"sharedOtherFbaInventoryFees",
        "月仓储费"=>"fbaStorageFee",
        "月仓储费差异"=>"sharedFbaStorageFee",
        "月仓储费本月计提"=>"fbaStorageFeeAccrual",
        "月仓储费上月冲销"=>"fbaStorageFeeAccrualDifference",
        "长期仓储费"=>"longTermStorageFee",
        "长期仓储费差异"=>"sharedLongTermStorageFee",
        "长期仓储费本月计提"=>"longTermStorageFeeAccrual",
        "长期仓储费上月冲销"=>"longTermStorageFeeAccrualDifference",
        "库存续订费用"=>"sharedStorageRenewalBilling",
        "FBA销毁费"=>"sharedFbaDisposalFee",
        "销毁量"=>"disposalQuantity",
        "FBA移除费"=>"sharedFbaRemovalFee",
        "移除量"=>"removalQuantity",
        "入仓手续费"=>"sharedFbaInboundTransportationProgramFee",
        "标签费"=>"sharedLabelingFee",
        "塑料包装费"=>"sharedPolybaggingFee",
        "FBA卖家退回费"=>"sharedFbaCustomerReturnFee",
        "FBA仓储费-入库缺陷费"=>"sharedFbaInboundDefectFee",
        "库存调整费"=>"sharedItemFeeAdjustment",
        "合作承运费"=>"sharedAmazonPartneredCarrierShipmentFee",
        "入库配置费（原合仓费）"=>"sharedFbaInboundConvenienceFee",
        "超量仓储费"=>"sharedFbaOverageFee",
        "泡沫包装费"=>"sharedBubblewrapFee",
        "胶带费"=>"sharedTapingFee",
        "购买配送费（原运输标签费）"=>"shippingLabelPurchases",
        "承运人装运标签调整费"=>"sharedCarrierShippingLabelAdjustments",
        "其他服务费"=>"sharedOtherServiceFees",
        "人工处理费用"=>"sharedManualProcessingFee",
        "清算费"=>"sharedLiquidationsFees",
        "MFNPostageFee"=>"sharedMfnPostageFee",
        "站外推广费"=>'customOrderFeePrincipal',//用的是站外推广费本金。
        //自定义字段
        "（调整的采购成本）弃置清算亚马逊破损成本"=>'key1',
        "服务商费用"=>'key2',
        "店铺相关费用"=>'key3',
        "测评费+推广费-推广部返款"=>'key4',
        "测评费+推广费-服务商返款"=>'key5',
        "预留资金"=>'key6',
        "（调整的头程成本）FBM多渠道独立站调整"=>'key7',
        "欧洲VAT税金"=>'key8',
        "（调整的尾程成本）站内多渠道尾程成本调整"=>'key9',
        "物流其他费用"=>'key10',
        "站外广告费"=>'key11',
        "物流商赔偿收入"=>'key12',
        "福利政策-采购成本"=>'key13',
        "福利政策-头程成本"=>'key14',
        "福利政策-其他成本"=>'key18',
        "海外营销费用"=>'key19',
        "站内数据调整"=>'key20',
        "调整的数量"=>'key21',
        //税费
        "VAT/GST"=>"taxCollected",
        "TCS_IGST"=>"tcsIgstCollected",
        "TCS_CGST"=>"tcsCgstCollected",
        "TCS_SGST"=>"tcsSgstCollected",
        "混合VAT"=>"sharedComminglingVatExpenses",
        "商品税调整"=>"sharedTaxAdjustment",
        "礼品包装税"=>"taxCollectedGiftWrap",
        "买家运费税"=>"taxCollectedShipping",
        "促销折扣税"=>"taxCollectedDiscount",
        "商品价格税"=>"taxCollectedProduct",
        "VAT/GST退款"=>"taxRefunded",
        "TCS_IGST退款"=>"tcsIgstRefunded",
        "TCS_CGST退款"=>"tcsCgstRefunded",
        "TCS_SGST退款"=>"tcsSgstRefunded",
        "礼品包装税退款"=>"taxRefundedGiftWrap",
        "买家运费税退款"=>"taxRefundedShipping",
        "促销折扣税退款"=>"taxRefundedDiscount",
        "商品价格税退款"=>"taxRefundedProduct",
        "市场税"=>"salesTaxWithheld",
        "市场税退款额"=>"refundTaxWithheld",
        "混合网路费用"=>"tdsSection194ONet",
        //商品成本支付
        "采购成本"=>"cgPriceTotal",
        "采购均价"=>"cgUnitPrice",
        "采购占比"=>"proportionOfCg",
        "头程成本"=>"cgTransportCostsTotal",
        "头程均价"=>"cgTransportUnitCosts",
        "头程占比"=>"proportionOfCgTransport",
        "其他成本"=>"cgOtherCostsTotal",
        "其他均价"=>"cgOtherUnitCosts",
        "其他占比"=>"proportionOfCgOtherCosts",
        "合计成本"=>"totalCost",
        "合计成本占比"=>"proportionOfTotalCost",
        //利润
        "毛利润"=>"grossProfit",
        "毛利率"=>"grossRate",
        'ROI'=>"roi",
    ];
    public static $export_key_list = [
        "storeName"=>"店铺",
        "supplier_name"=>"供应商名称",
        "yunying"=>"运营人员",
        "msku"=>"MSKU",
        "asin"=>"ASIN",
        "parentAsin"=>"父ASIN",
        "country"=>"国家",
        "localSku"=>"SKU",
        "localName"=>"品名",
        "itemName"=>"标题",
        "principalRealname"=>"Listing负责人",
        "productDeveloperRealname"=>"开发负责人",
        "categoryName"=>"分类",
        "brandName"=>"品牌",
        "currencyCode"=>"币种",
        "listingTagIds"=>"listing标签id",
        "fbaSalesQuantity"=>"FBA销量",
        "fbmSalesQuantity"=>"FBM销量",
        "reshipFbmProductSalesQuantity"=>"FBM补（换）货量",
        "reshipFbmProductSaleRefundsQuantity"=>"FBM补（换）货退回量",
        "reshipFbaProductSalesQuantity"=>"FBA补（换）货量",
        "reshipFbaProductSaleRefundsQuantity"=>"FBA补（换）货退回量",
        "mcFbaFulfillmentFeesQuantity"=>"多渠道销量",
        "adsSdSales"=>"SD广告销售额",
        "adsSpSales"=>"SP广告销售额",
        "sharedAdsSbSales"=>"SB广告销售额",
        "sharedAdsSbvSales"=>"SBV广告销售额",
        "adsSdSalesQuantity"=>"SD广告销量",
        "adsSpSalesQuantity"=>"SP广告销量",
        "sharedAdsSbSalesQuantity"=>"SB广告销量",
        "sharedAdsSbvSalesQuantity"=>"SBV广告销量",
        "fbaSaleAmount"=>"FBA销售额",
        "fbmSaleAmount"=>"FBM销售额",
        "shippingCredits"=>"买家运费",
        "promotionalRebates"=>"促销折扣",
        "fbaInventoryCredit"=>"FBA库存赔偿",
        "fbaInventoryCreditQuantity"=>"赔偿量",
        "cashOnDelivery"=>"COD",
        "refundsQuantity"=>"退款量",
        "refundsRate"=>"退款率",
        "fbaReturnsSaleableQuantity"=>"退货量（可售）",
        "fbaReturnsUnsaleableQuantity"=>"退货量（不可售）",
        "fbaReturnsQuantityRate"=>"退货率",
        "giftWrapCredits"=>"包装收入",
        "guaranteeClaims"=>"买家交易保障索赔额",
        "costOfPoIntegersGranted"=>"积分抵减收入",
        "fbaLiquidationProceeds"=>"清算收入",
        "amazonShippingReimbursement"=>"亚马逊运费赔偿",
        "safeTReimbursement"=>"Safe-T索赔",
        "netcoTransaction"=>"Netco交易",
        "reimbursements"=>"赔偿收入",
        "clawbacks"=>"追索收入",
        "others"=>"其他收入-其他",
        "fbaLiquidationProceedsAdjustments"=>"清算调整",
        "sharedComminglingVatIncome"=>"混合VAT收入",
        "fbaSalesRefunds"=>"FBA销售退款额",
        "fbmSalesRefunds"=>"FBM销售退款额",
        "shippingCreditRefunds"=>"买家运费退款额",
        "giftWrapCreditRefunds"=>"买家包装退款额",
        "promotionalRebateRefunds"=>"促销折扣退款额",
        "chargebacks"=>"买家拒付",
        "costOfPoIntegersReturned"=>"积分抵减退回",
        "sellingFeeRefunds"=>"平台费退款项",
        "fbaTransactionFeeRefunds"=>"发货费退款项",
        "otherTransactionFeeRefunds"=>"其他订单费退款项",
        "shippingLabelRefunds"=>"运输标签费退款",
        "refundAdministrationFees"=>"交易费用退款额",
        "pointsAdjusted"=>"积分费用",
        "refundForAdvertiser"=>"广告退款额",
        "platformFee"=>"平台费",
        "fbaDeliveryFee"=>"FBA发货费(FBA)",
        "mcFbaDeliveryFee"=>"FBA发货费(多渠道)",
        "otherTransactionFees"=>"其他订单费用",
        "sharedFbaIntegerernationalInboundFee"=>"FBA国际物流货运费",
        "adjustments"=>"调整费用",
        "adsSpCost"=>"SP广告费",
        "adsSbCost"=>"SB广告费",
        "adsSbvCost"=>"SBV广告费",
        "adsSdCost"=>"SD广告费",
        "sharedCostOfAdvertising"=>"差异分摊",
        "sharedSubscriptionFee"=>"订阅费",
        "sharedLdFee"=>"秒杀费",
        "sharedCouponFee"=>"优惠券",
        "sharedEarlyReviewerProgramFee"=>"早期评论人计划",
        "sharedVineFee"=>"VINE",
        "sharedOtherFbaInventoryFees"=>"其他仓储费",
        "fbaStorageFee"=>"月仓储费",
        "sharedFbaStorageFee"=>"月仓储费差异",
        "fbaStorageFeeAccrual"=>"月仓储费本月计提",
        "fbaStorageFeeAccrualDifference"=>"月仓储费上月冲销",
        "longTermStorageFee"=>"长期仓储费",
        "sharedLongTermStorageFee"=>"长期仓储费差异",
        "longTermStorageFeeAccrual"=>"长期仓储费本月计提",
        "longTermStorageFeeAccrualDifference"=>"长期仓储费上月冲销",
        "sharedStorageRenewalBilling"=>"库存续订费用",
        "sharedFbaDisposalFee"=>"FBA销毁费",
        "disposalQuantity"=>"销毁量",
        "sharedFbaRemovalFee"=>"FBA移除费",
        "removalQuantity"=>"移除量",
        "sharedFbaInboundTransportationProgramFee"=>"入仓手续费",
        "sharedLabelingFee"=>"标签费",
        "sharedPolybaggingFee"=>"塑料包装费",
        "sharedFbaCustomerReturnFee"=>"FBA卖家退回费",
        "sharedFbaInboundDefectFee"=>"FBA仓储费-入库缺陷费",
        "sharedItemFeeAdjustment"=>"库存调整费",
        "sharedAmazonPartneredCarrierShipmentFee"=>"合作承运费",
        "sharedFbaInboundConvenienceFee"=>"入库配置费（原合仓费）",
        "sharedFbaOverageFee"=>"超量仓储费",
        "sharedBubblewrapFee"=>"泡沫包装费",
        "sharedTapingFee"=>"胶带费",
        "shippingLabelPurchases"=>"购买配送费（原运输标签费）",
        "sharedCarrierShippingLabelAdjustments"=>"承运人装运标签调整费",
        "sharedOtherServiceFees"=>"其他服务费",
        "sharedManualProcessingFee"=>"人工处理费用",
        "sharedLiquidationsFees"=>"清算费",
        "sharedMfnPostageFee"=>"MFNPostageFee",
        "customOrderFeePrincipal"=>"站外推广费",
        "key1"=>"（调整的采购成本）弃置清算亚马逊破损成本",
        "key2"=>"服务商费用",
        "key3"=>"店铺相关费用",
        "key4"=>"测评费+推广费-推广部返款",
        "key5"=>"测评费+推广费-服务商返款",
        "key6"=>"预留资金",
        "key7"=>"（调整的头程成本）FBM多渠道独立站调整",
        "key8"=>"欧洲VAT税金",
        "key9"=>"（调整的尾程成本）站内多渠道尾程成本调整",
        "key10"=>"物流其他费用",
        "key11"=>"站外广告费",
        "key12"=>"物流商赔偿收入",
        "key13"=>"福利政策-采购成本",
        "key14"=>"福利政策-头程成本",
        "key18"=>"福利政策-其他成本",
        "key19"=>"海外营销费用",
        "key20"=>"站内数据调整",
        "key21"=>"调整的数量",
        "taxCollected"=>"VAT/GST",
        "tcsIgstCollected"=>"TCS-IGST",
        "tcsCgstCollected"=>"TCS-CGST",
        "tcsSgstCollected"=>"TCS_SGST",
        "sharedComminglingVatExpenses"=>"混合VAT",
        "sharedTaxAdjustment"=>"商品税调整",
        "taxCollectedGiftWrap"=>"礼品包装税",
        "taxCollectedShipping"=>"买家运费税",
        "taxCollectedDiscount"=>"促销折扣税",
        "taxCollectedProduct"=>"商品价格税",
        "taxRefunded"=>"VAT/GST退款",
        "tcsIgstRefunded"=>"TCS_IGST退款",
        "tcsCgstRefunded"=>"TCS_CGST退款",
        "tcsSgstRefunded"=>"TCS_SGST退款",
        "taxRefundedGiftWrap"=>"礼品包装税退款",
        "taxRefundedShipping"=>"买家运费税退款",
        "taxRefundedDiscount"=>"促销折扣税退款",
        "taxRefundedProduct"=>"商品价格税退款",
        "salesTaxWithheld"=>"市场税",
        "refundTaxWithheld"=>"市场税退款额",
        "tdsSection194ONet"=>"混合网路费用",
        "cgPriceTotal"=>"采购成本",
        "cgUnitPrice"=>"采购均价",
        "proportionOfCg"=>"采购占比",
        "cgTransportCostsTotal"=>"头程成本",
        "cgTransportUnitCosts"=>"头程均价",
        "proportionOfCgTransport"=>"头程占比",
        "cgOtherCostsTotal"=>"其他成本",
        "cgOtherUnitCosts"=>"其他均价",
        "proportionOfCgOtherCosts"=>"其他成本占比",
        "totalCost"=>"合计成本",
        "proportionOfTotalCost"=>"合计成本占比",
        "grossProfit"=>"毛利润",
        "grossRate"=>"毛利率",
        "roi"=>"ROI"
    ];
    //msku报告数据表创建
    public static function creatMskuReportTable($year) {
        $table = 'msku_report_data_'.$year;
        $dbF = dbFMysql::getInstance();
        //基础表
        $dbF->query("CREATE TABLE IF NOT EXISTS `oa_f_$table` (
              `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
              `lingxing_id` int(11) DEFAULT '0' COMMENT '领星记录id',
              `sid` varchar(50) NOT NULL COMMENT '店铺id(oa系统中也是这个)',
              `reportDateMonth` varchar(50) NOT NULL COMMENT '按月时间',
              `postedDateLocale` varchar(50) DEFAULT '' COMMENT '按天汇总',
              `msku` varchar(100) NOT NULL COMMENT 'msku',
              `asin` varchar(100) NOT NULL COMMENT 'asin',
              `parentAsin` varchar(100) NOT NULL COMMENT '父asin',
              `storeName` varchar(100) NOT NULL COMMENT '店铺',
              `countryCode` varchar(10) NOT NULL COMMENT '国家编码',
              `localName` varchar(100) NOT NULL COMMENT '品名',
              `localSku` varchar(100) NOT NULL COMMENT 'sku',
              `itemName` varchar(255) DEFAULT NULL COMMENT '标题',
              `principalRealname` varchar(100) DEFAULT NULL COMMENT '负责人',
              `productDeveloperRealname` varchar(100) DEFAULT NULL COMMENT '产品开发负责人',
              `categoryName` varchar(100) DEFAULT NULL COMMENT '分类',
              `brandName` varchar(100) DEFAULT NULL COMMENT '品牌',
              `currencyCode` varchar(20) NOT NULL COMMENT '币种',
              `currencyIcon` varchar(10) NOT NULL COMMENT '币种符号',
              `listingTagIds` varchar(255) DEFAULT NULL COMMENT 'listing标签id',
              `country` varchar(20) NOT NULL COMMENT '国家',
              `import_id` int(11) DEFAULT '0' COMMENT '导入的记录id',
              `is_delete` tinyint(1) DEFAULT '0',
              `del_user_id` int(11) DEFAULT '0',
              `del_time` datetime DEFAULT NULL,
              `project_id` int(11) NOT NULL COMMENT 'oa项目id',
              `yunying_id` int(11) NOT NULL COMMENT 'oa用户id（运营）',
              `category_id` int(11) NOT NULL COMMENT 'oa分类id',
              `supplier_id` int(11) NOT NULL DEFAULT '0' COMMENT 'oa供应商id',
              `totalFbaAndFbmQuantity` int(11) DEFAULT '0' COMMENT 'fba和fbm销量加总，用于计算占比',
              `totalFbaAndFbmAmount` decimal(10,2) DEFAULT '0.00' COMMENT 'fba和fbm销售额加总，用于计算占比',
              `totalSalesQuantity` int(11) DEFAULT '0' COMMENT '销量',
              `fbaSalesQuantity` int(11) DEFAULT '0' COMMENT 'FBA销量',
              `fbmSalesQuantity` int(11) DEFAULT '0' COMMENT 'FBM销量',
              `totalReshipQuantity` int(11) DEFAULT '0' COMMENT '补换货量',
              `reshipFbmProductSalesQuantity` int(11) DEFAULT '0' COMMENT 'FBM补（换）货量',
              `reshipFbmProductSaleRefundsQuantity` int(11) DEFAULT '0' COMMENT 'FBM补（换）货退回量',
              `reshipFbaProductSalesQuantity` int(11) DEFAULT '0' COMMENT 'FBA补（换）货量',
              `reshipFbaProductSaleRefundsQuantity` int(11) DEFAULT '0' COMMENT 'FBA补（换）货退回量',
              `cgAbsQuantity` decimal(12,2) DEFAULT '0.00' COMMENT '成本数量绝对值',
              `cgQuantity` int(11) DEFAULT '0' COMMENT '成本数量',
              `totalAdsSales` decimal(12,2) DEFAULT '0.00' COMMENT '广告销售额',
              `adsSdSales` decimal(12,2) DEFAULT '0.00' COMMENT 'sd广告销售额',
              `adsSpSales` decimal(12,2) DEFAULT '0.00' COMMENT 'sp广告销售额',
              `sharedAdsSbSales` decimal(12,2) DEFAULT '0.00' COMMENT 'sb广告销售额',
              `sharedAdsSbvSales` decimal(12,2) DEFAULT '0.00' COMMENT 'sbv广告销售额',
              `totalAdsSalesQuantity` int(11) DEFAULT '0' COMMENT '广告销量',
              `adsSdSalesQuantity` int(11) DEFAULT '0' COMMENT 'sd广告销量',
              `adsSpSalesQuantity` int(11) DEFAULT '0' COMMENT 'sp广告销量',
              `sharedAdsSbSalesQuantity` int(11) DEFAULT '0' COMMENT 'sb广告销量',
              `sharedAdsSbvSalesQuantity` int(11) DEFAULT '0' COMMENT 'sbv广告销量',
              `totalSalesAmount` decimal(12,2) DEFAULT '0.00' COMMENT '销售额',
              `fbaSaleAmount` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA销售额',
              `fbmSaleAmount` decimal(12,2) DEFAULT '0.00' COMMENT 'FBM销售额',
              `shippingCredits` decimal(12,2) DEFAULT '0.00' COMMENT '买家运费',
              `promotionalRebates` decimal(12,2) DEFAULT '0.00' COMMENT '促销折扣',
              `fbaInventoryCredit` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA库存赔偿',
              `cashOnDelivery` decimal(12,2) DEFAULT '0.00' COMMENT 'COD',
              `otherInAmount` decimal(12,2) DEFAULT '0.00' COMMENT '其他收入',
              `fbaLiquidationProceeds` decimal(12,2) DEFAULT '0.00' COMMENT '清算收入',
              `fbaLiquidationProceedsAdjustments` decimal(12,2) DEFAULT '0.00' COMMENT '清算调整',
              `amazonShippingReimbursement` decimal(12,2) DEFAULT '0.00' COMMENT '亚马逊运费赔偿',
              `mcFbaFulfillmentFeesQuantity` int DEFAULT '0' COMMENT '多渠道销量',
              `safeTReimbursement` decimal(12,2) DEFAULT '0.00' COMMENT 'Safe-T索赔',
              `netcoTransaction` decimal(12,2) DEFAULT '0.00' COMMENT 'Netco交易',
              `reimbursements` decimal(12,2) DEFAULT '0.00' COMMENT '赔偿收入',
              `clawbacks` decimal(12,2) DEFAULT '0.00' COMMENT '追索收入',
              `sharedComminglingVatIncome` decimal(12,2) DEFAULT '0.00' COMMENT '混合VAT收入',
              `giftWrapCredits` decimal(12,2) DEFAULT '0.00' COMMENT '包装收入',
              `guaranteeClaims` decimal(12,2) DEFAULT '0.00' COMMENT '买家交易保障索赔额',
              `costOfPoIntegersGranted` decimal(12,2) DEFAULT '0.00' COMMENT '积分抵减收入',
              `totalSalesRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '收入退款额',
              `fbaSalesRefunds` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA销售退款额',
              `fbmSalesRefunds` decimal(12,2) DEFAULT '0.00' COMMENT 'FBM销售退款额',
              `shippingCreditRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '买家运费退款额',
              `giftWrapCreditRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '买家包装退款额',
              `chargebacks` decimal(12,2) DEFAULT '0.00' COMMENT '买家拒付',
              `costOfPoIntegersReturned` decimal(12,2) DEFAULT '0.00' COMMENT '积分抵减退回',
              `promotionalRebateRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '促销折扣退款额',
              `totalFeeRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '费用退款额',
              `sellingFeeRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '平台费退款项',
              `fbaTransactionFeeRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '发货费退款项',
              `refundAdministrationFees` decimal(12,2) DEFAULT '0.00' COMMENT '交易费用退款额',
              `otherTransactionFeeRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '其他订单费退款项',
              `refundForAdvertiser` decimal(12,2) DEFAULT '0.00' COMMENT '广告退款额',
              `pointsAdjusted` decimal(12,2) DEFAULT '0.00' COMMENT '积分费用',
              `shippingLabelRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '运输标签费退款',
              `refundsQuantity` int(11) DEFAULT '0' COMMENT '退款量',
              `refundsRate` decimal(12,4) DEFAULT '0.0000' COMMENT '退款率',
              `fbaReturnsQuantity` int(11) DEFAULT '0' COMMENT '退货量',
              `fbaReturnsSaleableQuantity` int(11) DEFAULT '0' COMMENT '退货量（可售）',
              `fbaReturnsUnsaleableQuantity` int(11) DEFAULT '0' COMMENT '退货量（不可售）',
              `fbaReturnsQuantityRate` decimal(12,4) DEFAULT '0.0000' COMMENT '退货率',
              `platformFee` decimal(12,2) DEFAULT '0.00' COMMENT '平台费',
              `totalFbaDeliveryFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA发货费',
              `fbaDeliveryFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA发货费(多渠道)',
              `mcFbaDeliveryFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA发货费合计',
              `otherTransactionFees` decimal(12,2) DEFAULT '0.00' COMMENT '其他订单费用',
              `totalAdsCost` decimal(10,2) DEFAULT '0' COMMENT '广告费',
              `adsSpCost` decimal(10,2) DEFAULT '0' COMMENT 'SP广告费',
              `adsSbCost` decimal(10,2) DEFAULT '0' COMMENT 'SB广告费',
              `adsSbvCost` decimal(10,2) DEFAULT '0' COMMENT 'SBV广告费',
              `adsSdCost` decimal(10,2) DEFAULT '0' COMMENT 'SD广告费',
              `sharedCostOfAdvertising` decimal(12,2) DEFAULT '0.00' COMMENT '差异分摊',
              `promotionFee` decimal(12,2) DEFAULT '0.00' COMMENT '推广费',
              `sharedSubscriptionFee` decimal(12,2) DEFAULT '0.00' COMMENT '订阅费',
              `sharedLdFee` decimal(12,2) DEFAULT '0.00' COMMENT '秒杀费',
              `sharedCouponFee` decimal(12,2) DEFAULT '0.00' COMMENT '优惠卷',
              `sharedEarlyReviewerProgramFee` decimal(12,2) DEFAULT '0.00' COMMENT '早期评论人计划',
              `sharedVineFee` decimal(12,2) DEFAULT '0.00' COMMENT 'vine',
              `totalStorageFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA仓储费',
              `fbaStorageFee` decimal(12,2) DEFAULT '0.00' COMMENT '月度仓库费',
              `sharedFbaStorageFee` decimal(12,2) DEFAULT '0.00' COMMENT '月度仓储费差异',
              `longTermStorageFee` decimal(12,2) DEFAULT '0.00' COMMENT '长期仓储费',
              `sharedLongTermStorageFee` decimal(12,2) DEFAULT '0.00' COMMENT '长期仓储费差异',
              `sharedStorageRenewalBilling` decimal(12,2) DEFAULT '0.00' COMMENT '库存续订费用',
              `sharedFbaDisposalFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA销毁费',
              `sharedFbaRemovalFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA移除费',
              `sharedFbaInboundTransportationProgramFee` decimal(12,2) DEFAULT '0.00' COMMENT '入仓手续费',
              `sharedLabelingFee` decimal(12,2) DEFAULT '0.00' COMMENT '标签费',
              `sharedPolybaggingFee` decimal(12,2) DEFAULT '0.00' COMMENT '塑料包装费',
              `sharedBubblewrapFee` decimal(12,2) DEFAULT '0.00' COMMENT '泡沫包装费',
              `sharedTapingFee` decimal(12,2) DEFAULT '0.00' COMMENT '胶带费',
              `sharedFbaCustomerReturnFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA卖家退回费',
              `sharedFbaInboundDefectFee` decimal(12,2) DEFAULT '0.00' COMMENT '计划外服务费',
              `sharedFbaOverageFee` decimal(12,2) DEFAULT '0.00' COMMENT '超量仓储费',
              `sharedAmazonPartneredCarrierShipmentFee` decimal(12,2) DEFAULT '0.00' COMMENT '合作承运费',
              `sharedFbaInboundConvenienceFee` decimal(12,2) DEFAULT '0.00' COMMENT '合仓费',
              `sharedItemFeeAdjustment` decimal(12,2) DEFAULT '0.00' COMMENT '库存调整费用',
              `sharedOtherFbaInventoryFees` decimal(12,2) DEFAULT '0.00' COMMENT '其他仓储费',
              `fbaStorageFeeAccrual` decimal(12,2) DEFAULT '0.00' COMMENT '月仓储费-本月计提',
              `fbaStorageFeeAccrualDifference` decimal(12,2) DEFAULT '0.00' COMMENT '月仓储费-上月冲销',
              `longTermStorageFeeAccrual` decimal(12,2) DEFAULT '0.00' COMMENT '长期仓储费-本月计提',
              `longTermStorageFeeAccrualDifference` decimal(12,2) DEFAULT '0.00' COMMENT '长期仓储费-上月冲销',
              `sharedFbaIntegerernationalInboundFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA国际物流货运费',
              `adjustments` decimal(12,2) DEFAULT '0.00' COMMENT '调整费用',
              `totalPlatformOtherFee` decimal(12,2) DEFAULT '0.00' COMMENT '平台其他费',
              `shippingLabelPurchases` decimal(12,2) DEFAULT '0.00' COMMENT '运输标签费',
              `fbaInventoryCreditQuantity` int DEFAULT '0' COMMENT '赔偿量',
              `disposalQuantity` int DEFAULT '0' COMMENT '销毁量',
              `removalQuantity` int DEFAULT '0' COMMENT '移除量',
              `others` decimal(10,2) DEFAULT '0' COMMENT '平台收入中其他收入的其他费用',
              `sharedCarrierShippingLabelAdjustments` decimal(12,2) DEFAULT '0.00' COMMENT '承运人装运标签调整费',
              `sharedLiquidationsFees` decimal(12,2) DEFAULT '0.00' COMMENT '清算费',
              `sharedManualProcessingFee` decimal(12,2) DEFAULT '0.00' COMMENT '人工处理费用',
              `sharedOtherServiceFees` decimal(12,2) DEFAULT '0.00' COMMENT '其他服务费',
              `sharedMfnPostageFee` decimal(12,2) DEFAULT '0.00' COMMENT '多渠道邮资费',
              `totalSalesTax` decimal(12,2) DEFAULT '0.00' COMMENT '销售税',
              `tcsIgstCollected` decimal(12,2) DEFAULT '0.00' COMMENT 'TCS-IGST',
              `tcsSgstCollected` decimal(12,2) DEFAULT '0.00' COMMENT 'TCS-SGST',
              `tcsCgstCollected` decimal(12,2) DEFAULT '0.00' COMMENT 'TCS-CGST',
              `sharedComminglingVatExpenses` decimal(12,2) DEFAULT '0.00' COMMENT '混合VAT',
              `taxCollected` decimal(12,2) DEFAULT '0.00' COMMENT 'VAT/GST',
              `taxCollectedProduct` decimal(12,2) DEFAULT '0.00' COMMENT '商品价格税',
              `taxCollectedDiscount` decimal(12,2) DEFAULT '0.00' COMMENT '促销折扣税',
              `taxCollectedShipping` decimal(12,2) DEFAULT '0.00' COMMENT '买家运费税',
              `taxCollectedGiftWrap` decimal(12,2) DEFAULT '0.00' COMMENT '礼品包装税',
              `sharedTaxAdjustment` decimal(12,2) DEFAULT '0.00' COMMENT '商品税调整',
              `salesTaxRefund` decimal(12,2) DEFAULT '0.00' COMMENT '销售税退款额',
              `tcsIgstRefunded` decimal(12,2) DEFAULT '0.00' COMMENT 'TCS-IGST',
              `tcsSgstRefunded` decimal(12,2) DEFAULT '0.00' COMMENT 'TCS-SGST',
              `tcsCgstRefunded` decimal(12,2) DEFAULT '0.00' COMMENT 'TCS-CGST',
              `taxRefunded` decimal(12,2) DEFAULT '0.00' COMMENT 'VAT/GST',
              `taxRefundedProduct` decimal(12,2) DEFAULT '0.00' COMMENT '商品价格税退款',
              `taxRefundedDiscount` decimal(12,2) DEFAULT '0.00' COMMENT '促销折扣税退款',
              `taxRefundedShipping` decimal(12,2) DEFAULT '0.00' COMMENT '买家运费税退款',
              `taxRefundedGiftWrap` decimal(12,2) DEFAULT '0.00' COMMENT '礼品包装税退款',
              `salesTaxWithheld` decimal(12,2) DEFAULT '0.00' COMMENT '市场税',
              `refundTaxWithheld` decimal(12,2) DEFAULT '0.00' COMMENT '市场税退款额',
              `tdsSection194ONet` decimal(12,2) DEFAULT '0.00' COMMENT '混合网路费用',
              `cgPriceTotal` decimal(12,2) DEFAULT '0.00' COMMENT '采购成本',
              `cgPriceAbsTotal` decimal(12,2) DEFAULT '0.00' COMMENT '采购成本绝对值',
              `hasCgPriceDetail` int(11) DEFAULT '0' COMMENT '是否有采购成本明细',
              `cgUnitPrice` decimal(12,2) DEFAULT '0.00' COMMENT '采购均价',
              `proportionOfCg` decimal(12,4) DEFAULT '0.0000' COMMENT '采购占比',
              `cgTransportCostsTotal` decimal(12,2) DEFAULT '0.00' COMMENT '头程成本',
              `hasCgTransportCostsDetail` int(11) DEFAULT '0' COMMENT '是否有物流（头程）成本明细',
              `cgTransportUnitCosts` decimal(12,2) DEFAULT '0.00' COMMENT '头程均价',
              `proportionOfCgTransport` decimal(12,4) DEFAULT '0.0000' COMMENT '头程占比',
              `totalCost` decimal(12,2) DEFAULT '0.00' COMMENT '合计成本',
              `proportionOfTotalCost` decimal(12,4) DEFAULT '0.0000' COMMENT '合计成本占比',
              `cgOtherCostsTotal` decimal(12,2) DEFAULT '0.00' COMMENT '其他成本',
              `cgOtherUnitCosts` decimal(12,2) DEFAULT '0.00' COMMENT '其他均价',
              `hasCgOtherCostsDetail` int(11) DEFAULT '0' COMMENT '是否有其他成本明细',
              `proportionOfCgOtherCosts` decimal(12,4) DEFAULT '0.0000' COMMENT '其他成本占比',
              `grossProfit` decimal(12,2) DEFAULT '0.00' COMMENT '毛利润',
              `grossProfitTax` decimal(12,4) DEFAULT '0.0000' COMMENT '毛利润税费',
              `roi` decimal(12,4) DEFAULT '0.0000' COMMENT 'ROI',
              `grossProfitIncome` decimal(12,4) DEFAULT '0.0000' COMMENT '毛利润收入',
              `grossRate` decimal(12,4) DEFAULT '0.0000' COMMENT '毛利率',
              `customOrderFee` decimal(12,2) DEFAULT '0.00' COMMENT '订单其他费',
              `customOrderFeePrincipal` decimal(12,2) DEFAULT '0.00' COMMENT '站外推广费-本金',
              `customOrderFeeCommission` decimal(12,2) DEFAULT '0.00' COMMENT '站外推广费-佣金',
              `key1` decimal(12,2) DEFAULT '0.00' COMMENT '（调整的采购成本）弃置清算亚马逊破损成本',
              `key2` decimal(12,2) DEFAULT '0.00' COMMENT '服务商费用',
              `key3` decimal(12,2) DEFAULT '0.00' COMMENT '店铺相关费用',
              `key4` decimal(12,2) DEFAULT '0.00' COMMENT '测评费+推广费-推广部返款',
              `key5` decimal(12,2) DEFAULT '0.00' COMMENT '测评费+推广费-服务商返款',
              `key6` decimal(12,2) DEFAULT '0.00' COMMENT '预留资金',
              `key7` decimal(12,2) DEFAULT '0.00' COMMENT '调整的头程成本）FBM多渠道独立站调整',
              `key8` decimal(12,2) DEFAULT '0.00' COMMENT '欧洲VAT税金',
              `key9` decimal(12,2) DEFAULT '0.00' COMMENT '（调整的尾程成本）站内多渠道尾程成本调整',
              `key10` decimal(12,2) DEFAULT '0.00' COMMENT '物流商赔偿收入',
              `key11` decimal(12,2) DEFAULT '0.00' COMMENT '站外广告费',
              `key12` decimal(12,2) DEFAULT '0.00' COMMENT '物流商赔偿收入',
              `key13` decimal(12,2) DEFAULT '0.00' COMMENT '福利政策-采购成本',
              `key14` decimal(12,2) DEFAULT '0.00' COMMENT '福利政策-头程成本',
              `key15` int(11) DEFAULT '0' COMMENT 'FBA在库总库存数量',
              `key16` int(11) DEFAULT '0' COMMENT '（FBA在途+在库+海外仓）总库存数量',
              `key17` decimal(12,2) DEFAULT '0.00' COMMENT '（FBA在途+在库+海外仓）总库存采购成本+头程成本',
              `key18` decimal(12,2) DEFAULT '0.00' COMMENT '福利政策-其他成本',
              `key19` decimal(12,2) DEFAULT '0.00' COMMENT '海外营销费用',
              `key20` decimal(12,2) DEFAULT '0.00' COMMENT '站内数据调整',
              `key21` decimal(12,2) DEFAULT '0.00' COMMENT '调整的数量',
              PRIMARY KEY (`id`),
              KEY `import_id` (`import_id`),
              KEY `reportDateMonth` (`reportDateMonth`),
              KEY `sid` (`sid`,`asin`,`msku`,`parentAsin`,`localSku`,`countryCode`,`project_id`,`yunying_id`,`supplier_id`) USING BTREE
            ) ENGINE=InnoDB AUTO_INCREMENT=74152 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='msku利润报表-$year';");
    }
    //msku报告数据表创建
    public static function creatMskuReportOriginalTable($year) {
        $table = 'msku_original_data_'.$year;
        $dbF = dbFMysql::getInstance();
        //源数据表
        $dbF->query("CREATE TABLE IF NOT EXISTS `oa_f_$table` (
            `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
            `sid` varchar(50) NOT NULL COMMENT '店铺id',
            `localSku` varchar(50) NOT NULL,
            `msku` varchar(50) NOT NULL,
            `asin` varchar(50) NOT NULL,
            `parentAsin` varchar(50) NOT NULL,
            `countryCode` varchar(10) NOT NULL,
            `project_id` int(11) NOT NULL,
            `yunying_id` int(11) NOT NULL,
            `supplier_id` int(11) NOT NULL DEFAULT '0' COMMENT 'oa供应商id',
            `reportDateMonth` varchar(10) NOT NULL,
            `itemName` varchar(255) DEFAULT NULL COMMENT '标题',
            `principalRealname` varchar(100) DEFAULT NULL COMMENT '负责人',
            `productDeveloperRealname` varchar(100) DEFAULT NULL COMMENT '产品开发负责人',
            `categoryName` varchar(100) DEFAULT NULL COMMENT '分类',
            `brandName` varchar(100) DEFAULT NULL COMMENT '品牌',
            `currencyCode` varchar(20) NOT NULL COMMENT '币种',
            `listingTagIds` varchar(255) DEFAULT NULL COMMENT 'listing标签id',
            `created_time` datetime DEFAULT NULL,
            `updated_time` datetime DEFAULT NULL,
            `is_delete` tinyint(1) NOT NULL DEFAULT '0',
            `totalFbaAndFbmQuantity` int(11) DEFAULT '0' COMMENT 'fba和fbm销量加总，用于计算占比',
            `totalFbaAndFbmAmount` decimal(10,2) DEFAULT '0.00' COMMENT 'fba和fbm销售额加总，用于计算占比',
            `totalSalesQuantity` int(11) DEFAULT '0' COMMENT '销量',
            `fbaSalesQuantity` int(11) DEFAULT '0' COMMENT 'FBA销量',
            `fbmSalesQuantity` int(11) DEFAULT '0' COMMENT 'FBM销量',
            `totalReshipQuantity` int(11) DEFAULT '0' COMMENT '补换货量',
            `reshipFbmProductSalesQuantity` int(11) DEFAULT '0' COMMENT 'FBM补（换）货量',
            `reshipFbmProductSaleRefundsQuantity` int(11) DEFAULT '0' COMMENT 'FBM补（换）货退回量',
            `reshipFbaProductSalesQuantity` int(11) DEFAULT '0' COMMENT 'FBA补（换）货量',
            `reshipFbaProductSaleRefundsQuantity` int(11) DEFAULT '0' COMMENT 'FBA补（换）货退回量',
            `cgAbsQuantity` decimal(12,2) DEFAULT '0.00' COMMENT '成本数量绝对值',
            `cgQuantity` int(11) DEFAULT '0' COMMENT '成本数量',
            `totalAdsSales` decimal(12,2) DEFAULT '0.00' COMMENT '广告销售额',
            `adsSdSales` decimal(12,2) DEFAULT '0.00' COMMENT 'sd广告销售额',
            `adsSpSales` decimal(12,2) DEFAULT '0.00' COMMENT 'sp广告销售额',
            `sharedAdsSbSales` decimal(12,2) DEFAULT '0.00' COMMENT 'sb广告销售额',
            `sharedAdsSbvSales` decimal(12,2) DEFAULT '0.00' COMMENT 'sbv广告销售额',
            `totalAdsSalesQuantity` int(11) DEFAULT '0' COMMENT '广告销量',
            `adsSdSalesQuantity` int(11) DEFAULT '0' COMMENT 'sd广告销量',
            `adsSpSalesQuantity` int(11) DEFAULT '0' COMMENT 'sp广告销量',
            `sharedAdsSbSalesQuantity` int(11) DEFAULT '0' COMMENT 'sb广告销量',
            `sharedAdsSbvSalesQuantity` int(11) DEFAULT '0' COMMENT 'sbv广告销量',
            `totalSalesAmount` decimal(12,2) DEFAULT '0.00' COMMENT '销售额',
            `fbaSaleAmount` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA销售额',
            `fbmSaleAmount` decimal(12,2) DEFAULT '0.00' COMMENT 'FBM销售额',
            `shippingCredits` decimal(12,2) DEFAULT '0.00' COMMENT '买家运费',
            `promotionalRebates` decimal(12,2) DEFAULT '0.00' COMMENT '促销折扣',
            `fbaInventoryCredit` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA库存赔偿',
            `cashOnDelivery` decimal(12,2) DEFAULT '0.00' COMMENT 'COD',
            `otherInAmount` decimal(12,2) DEFAULT '0.00' COMMENT '其他收入',
            `fbaLiquidationProceeds` decimal(12,2) DEFAULT '0.00' COMMENT '清算收入',
            `fbaLiquidationProceedsAdjustments` decimal(12,2) DEFAULT '0.00' COMMENT '清算调整',
            `amazonShippingReimbursement` decimal(12,2) DEFAULT '0.00' COMMENT '亚马逊运费赔偿',
            `mcFbaFulfillmentFeesQuantity` int DEFAULT '0' COMMENT '多渠道销量',
            `safeTReimbursement` decimal(12,2) DEFAULT '0.00' COMMENT 'Safe-T索赔',
            `netcoTransaction` decimal(12,2) DEFAULT '0.00' COMMENT 'Netco交易',
            `reimbursements` decimal(12,2) DEFAULT '0.00' COMMENT '赔偿收入',
            `clawbacks` decimal(12,2) DEFAULT '0.00' COMMENT '追索收入',
            `sharedComminglingVatIncome` decimal(12,2) DEFAULT '0.00' COMMENT '混合VAT收入',
            `giftWrapCredits` decimal(12,2) DEFAULT '0.00' COMMENT '包装收入',
            `guaranteeClaims` decimal(12,2) DEFAULT '0.00' COMMENT '买家交易保障索赔额',
            `costOfPoIntegersGranted` decimal(12,2) DEFAULT '0.00' COMMENT '积分抵减收入',
            `totalSalesRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '收入退款额',
            `fbaSalesRefunds` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA销售退款额',
            `fbmSalesRefunds` decimal(12,2) DEFAULT '0.00' COMMENT 'FBM销售退款额',
            `shippingCreditRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '买家运费退款额',
            `giftWrapCreditRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '买家包装退款额',
            `chargebacks` decimal(12,2) DEFAULT '0.00' COMMENT '买家拒付',
            `costOfPoIntegersReturned` decimal(12,2) DEFAULT '0.00' COMMENT '积分抵减退回',
            `promotionalRebateRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '促销折扣退款额',
            `totalFeeRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '费用退款额',
            `sellingFeeRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '平台费退款项',
            `fbaTransactionFeeRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '发货费退款项',
            `refundAdministrationFees` decimal(12,2) DEFAULT '0.00' COMMENT '交易费用退款额',
            `otherTransactionFeeRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '其他订单费退款项',
            `refundForAdvertiser` decimal(12,2) DEFAULT '0.00' COMMENT '广告退款额',
            `pointsAdjusted` decimal(12,2) DEFAULT '0.00' COMMENT '积分费用',
            `shippingLabelRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '运输标签费退款',
            `refundsQuantity` int(11) DEFAULT '0' COMMENT '退款量',
            `refundsRate` decimal(12,4) DEFAULT '0.0000' COMMENT '退款率',
            `fbaReturnsQuantity` int(11) DEFAULT '0' COMMENT '退货量',
            `fbaReturnsSaleableQuantity` int(11) DEFAULT '0' COMMENT '退货量（可售）',
            `fbaReturnsUnsaleableQuantity` int(11) DEFAULT '0' COMMENT '退货量（不可售）',
            `fbaReturnsQuantityRate` decimal(12,4) DEFAULT '0.0000' COMMENT '退货率',
            `platformFee` decimal(12,2) DEFAULT '0.00' COMMENT '平台费',
            `totalFbaDeliveryFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA发货费合计',
            `fbaDeliveryFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA发货费',
            `mcFbaDeliveryFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA发货费(多渠道)',
            `otherTransactionFees` decimal(12,2) DEFAULT '0.00' COMMENT '其他订单费用',
            `totalAdsCost` decimal(10,2) DEFAULT '0' COMMENT '广告费',
            `adsSpCost` decimal(10,2) DEFAULT '0' COMMENT 'SP广告费',
            `adsSbCost` decimal(10,2) DEFAULT '0' COMMENT 'SB广告费',
            `adsSbvCost` decimal(10,2) DEFAULT '0' COMMENT 'SBV广告费',
            `adsSdCost` decimal(10,2) DEFAULT '0' COMMENT 'SD广告费',
            `sharedCostOfAdvertising` decimal(12,2) DEFAULT '0.00' COMMENT '差异分摊',
            `promotionFee` decimal(12,2) DEFAULT '0.00' COMMENT '推广费',
            `sharedSubscriptionFee` decimal(12,2) DEFAULT '0.00' COMMENT '订阅费',
            `sharedLdFee` decimal(12,2) DEFAULT '0.00' COMMENT '秒杀费',
            `sharedCouponFee` decimal(12,2) DEFAULT '0.00' COMMENT '优惠卷',
            `sharedEarlyReviewerProgramFee` decimal(12,2) DEFAULT '0.00' COMMENT '早期评论人计划',
            `sharedVineFee` decimal(12,2) DEFAULT '0.00' COMMENT 'vine',
            `totalStorageFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA仓储费',
            `fbaStorageFee` decimal(12,2) DEFAULT '0.00' COMMENT '月度仓库费',
            `sharedFbaStorageFee` decimal(12,2) DEFAULT '0.00' COMMENT '月度仓储费差异',
            `longTermStorageFee` decimal(12,2) DEFAULT '0.00' COMMENT '长期仓储费',
            `sharedLongTermStorageFee` decimal(12,2) DEFAULT '0.00' COMMENT '长期仓储费差异',
            `sharedStorageRenewalBilling` decimal(12,2) DEFAULT '0.00' COMMENT '库存续订费用',
            `sharedFbaDisposalFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA销毁费',
            `sharedFbaRemovalFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA移除费',
            `sharedFbaInboundTransportationProgramFee` decimal(12,2) DEFAULT '0.00' COMMENT '入仓手续费',
            `sharedLabelingFee` decimal(12,2) DEFAULT '0.00' COMMENT '标签费',
            `sharedPolybaggingFee` decimal(12,2) DEFAULT '0.00' COMMENT '塑料包装费',
            `sharedBubblewrapFee` decimal(12,2) DEFAULT '0.00' COMMENT '泡沫包装费',
            `sharedTapingFee` decimal(12,2) DEFAULT '0.00' COMMENT '胶带费',
            `sharedFbaCustomerReturnFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA卖家退回费',
            `sharedFbaInboundDefectFee` decimal(12,2) DEFAULT '0.00' COMMENT '计划外服务费',
            `sharedFbaOverageFee` decimal(12,2) DEFAULT '0.00' COMMENT '超量仓储费',
            `sharedAmazonPartneredCarrierShipmentFee` decimal(12,2) DEFAULT '0.00' COMMENT '合作承运费',
            `sharedFbaInboundConvenienceFee` decimal(12,2) DEFAULT '0.00' COMMENT '合仓费',
            `sharedItemFeeAdjustment` decimal(12,2) DEFAULT '0.00' COMMENT '库存调整费用',
            `sharedOtherFbaInventoryFees` decimal(12,2) DEFAULT '0.00' COMMENT '其他仓储费',
            `fbaStorageFeeAccrual` decimal(12,2) DEFAULT '0.00' COMMENT '月仓储费-本月计提',
            `fbaStorageFeeAccrualDifference` decimal(12,2) DEFAULT '0.00' COMMENT '月仓储费-上月冲销',
            `longTermStorageFeeAccrual` decimal(12,2) DEFAULT '0.00' COMMENT '长期仓储费-本月计提',
            `longTermStorageFeeAccrualDifference` decimal(12,2) DEFAULT '0.00' COMMENT '长期仓储费-上月冲销',
            `sharedFbaIntegerernationalInboundFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA国际物流货运费',
            `adjustments` decimal(12,2) DEFAULT '0.00' COMMENT '调整费用',
            `totalPlatformOtherFee` decimal(12,2) DEFAULT '0.00' COMMENT '平台其他费',
            `shippingLabelPurchases` decimal(12,2) DEFAULT '0.00' COMMENT '运输标签费',
            `fbaInventoryCreditQuantity` int DEFAULT '0' COMMENT '赔偿量',
            `disposalQuantity` int DEFAULT '0' COMMENT '销毁量',
            `removalQuantity` int DEFAULT '0' COMMENT '移除量',
            `others` decimal(10,2) DEFAULT '0' COMMENT '平台收入中其他收入的其他费用',
            `sharedCarrierShippingLabelAdjustments` decimal(12,2) DEFAULT '0.00' COMMENT '承运人装运标签调整费',
            `sharedLiquidationsFees` decimal(12,2) DEFAULT '0.00' COMMENT '清算费',
            `sharedManualProcessingFee` decimal(12,2) DEFAULT '0.00' COMMENT '人工处理费用',
            `sharedOtherServiceFees` decimal(12,2) DEFAULT '0.00' COMMENT '其他服务费',
            `sharedMfnPostageFee` decimal(12,2) DEFAULT '0.00' COMMENT '多渠道邮资费',
            `totalSalesTax` decimal(12,2) DEFAULT '0.00' COMMENT '销售税',
            `tcsIgstCollected` decimal(12,2) DEFAULT '0.00' COMMENT 'TCS-IGST',
            `tcsSgstCollected` decimal(12,2) DEFAULT '0.00' COMMENT 'TCS-SGST',
            `tcsCgstCollected` decimal(12,2) DEFAULT '0.00' COMMENT 'TCS-CGST',
            `sharedComminglingVatExpenses` decimal(12,2) DEFAULT '0.00' COMMENT '混合VAT',
            `taxCollected` decimal(12,2) DEFAULT '0.00' COMMENT 'VAT/GST',
            `taxCollectedProduct` decimal(12,2) DEFAULT '0.00' COMMENT '商品价格税',
            `taxCollectedDiscount` decimal(12,2) DEFAULT '0.00' COMMENT '促销折扣税',
            `taxCollectedShipping` decimal(12,2) DEFAULT '0.00' COMMENT '买家运费税',
            `taxCollectedGiftWrap` decimal(12,2) DEFAULT '0.00' COMMENT '礼品包装税',
            `sharedTaxAdjustment` decimal(12,2) DEFAULT '0.00' COMMENT '商品税调整',
            `salesTaxRefund` decimal(12,2) DEFAULT '0.00' COMMENT '销售税退款额',
            `tcsIgstRefunded` decimal(12,2) DEFAULT '0.00' COMMENT 'TCS-IGST',
            `tcsSgstRefunded` decimal(12,2) DEFAULT '0.00' COMMENT 'TCS-SGST',
            `tcsCgstRefunded` decimal(12,2) DEFAULT '0.00' COMMENT 'TCS-CGST',
            `taxRefunded` decimal(12,2) DEFAULT '0.00' COMMENT 'VAT/GST',
            `taxRefundedProduct` decimal(12,2) DEFAULT '0.00' COMMENT '商品价格税退款',
            `taxRefundedDiscount` decimal(12,2) DEFAULT '0.00' COMMENT '促销折扣税退款',
            `taxRefundedShipping` decimal(12,2) DEFAULT '0.00' COMMENT '买家运费税退款',
            `taxRefundedGiftWrap` decimal(12,2) DEFAULT '0.00' COMMENT '礼品包装税退款',
            `salesTaxWithheld` decimal(12,2) DEFAULT '0.00' COMMENT '市场税',
            `refundTaxWithheld` decimal(12,2) DEFAULT '0.00' COMMENT '市场税退款额',
            `tdsSection194ONet` decimal(12,2) DEFAULT '0.00' COMMENT '混合网路费用',
            `cgPriceTotal` decimal(12,2) DEFAULT '0.00' COMMENT '采购成本',
            `cgPriceAbsTotal` decimal(12,2) DEFAULT '0.00' COMMENT '采购成本绝对值',
            `hasCgPriceDetail` int(11) DEFAULT '0' COMMENT '是否有采购成本明细',
            `cgUnitPrice` decimal(12,2) DEFAULT '0.00' COMMENT '采购均价',
            `proportionOfCg` decimal(12,4) DEFAULT '0.0000' COMMENT '采购占比',
            `cgTransportCostsTotal` decimal(12,2) DEFAULT '0.00' COMMENT '头程成本',
            `hasCgTransportCostsDetail` int(11) DEFAULT '0' COMMENT '是否有物流（头程）成本明细',
            `cgTransportUnitCosts` decimal(12,2) DEFAULT '0.00' COMMENT '头程均价',
            `proportionOfCgTransport` decimal(12,4) DEFAULT '0.0000' COMMENT '头程占比',
            `totalCost` decimal(12,2) DEFAULT '0.00' COMMENT '合计成本',
            `proportionOfTotalCost` decimal(12,4) DEFAULT '0.0000' COMMENT '合计成本占比',
            `cgOtherCostsTotal` decimal(12,2) DEFAULT '0.00' COMMENT '其他成本',
            `cgOtherUnitCosts` decimal(12,2) DEFAULT '0.00' COMMENT '其他均价',
            `hasCgOtherCostsDetail` int(11) DEFAULT '0' COMMENT '是否有其他成本明细',
            `proportionOfCgOtherCosts` decimal(12,4) DEFAULT '0.0000' COMMENT '其他成本占比',
            `grossProfit` decimal(12,2) DEFAULT '0.00' COMMENT '毛利润',
            `grossProfitTax` decimal(12,4) DEFAULT '0.0000' COMMENT '毛利润税费',
            `roi` decimal(12,4) DEFAULT '0.0000' COMMENT 'ROI',
            `grossProfitIncome` decimal(12,4) DEFAULT '0.0000' COMMENT '毛利润收入',
            `grossRate` decimal(12,4) DEFAULT '0.0000' COMMENT '毛利率',
            `customOrderFee` decimal(12,2) DEFAULT '0.00' COMMENT '订单其他费',
            `customOrderFeePrincipal` decimal(12,2) DEFAULT '0.00' COMMENT '站外推广费-本金',
            `customOrderFeeCommission` decimal(12,2) DEFAULT '0.00' COMMENT '站外推广费-佣金',
            `key1` decimal(12,2) DEFAULT '0.00' COMMENT '（调整的采购成本）弃置清算亚马逊破损成本',
            `key2` decimal(12,2) DEFAULT '0.00' COMMENT '服务商费用',
            `key3` decimal(12,2) DEFAULT '0.00' COMMENT '店铺相关费用',
            `key4` decimal(12,2) DEFAULT '0.00' COMMENT '测评费+推广费-推广部返款',
            `key5` decimal(12,2) DEFAULT '0.00' COMMENT '测评费+推广费-服务商返款',
            `key6` decimal(12,2) DEFAULT '0.00' COMMENT '预留资金',
            `key7` decimal(12,2) DEFAULT '0.00' COMMENT '调整的头程成本）FBM多渠道独立站调整',
            `key8` decimal(12,2) DEFAULT '0.00' COMMENT '欧洲VAT税金-第二版',
            `key9` decimal(12,2) DEFAULT '0.00' COMMENT '（调整的尾程成本）站内多渠道尾程成本调整',
            `key10` decimal(12,2) DEFAULT '0.00' COMMENT '物流商赔偿收入',
            `key11` decimal(12,2) DEFAULT '0.00' COMMENT '站外广告费',
            `key12` decimal(12,2) DEFAULT '0.00' COMMENT '物流商赔偿收入',
            `key13` decimal(12,2) DEFAULT '0.00' COMMENT '福利政策-采购成本',
            `key14` decimal(12,2) DEFAULT '0.00' COMMENT '福利政策-头程成本',
            `key15` int(11) DEFAULT '0' COMMENT 'fbm_local_numFBA在库总库存数量',
            `key16` int(11) DEFAULT '0' COMMENT 'fbm_local_overseas_num（FBA在途+在库+海外仓）总库存数量',
            `key17` decimal(12,2) DEFAULT '0.00' COMMENT 'fbm_local_overseas_price（FBA在途+在库+海外仓）总库存采购成本+头程成本',
            `key18` decimal(12,2) DEFAULT '0.00' COMMENT '福利政策-其他成本',
            `key19` decimal(12,2) DEFAULT '0.00' COMMENT '海外营销费用',
            `key20` decimal(12,2) DEFAULT '0.00' COMMENT '站内数据调整',
            `key21` decimal(12,2) DEFAULT '0.00' COMMENT '调整的数量',
             PRIMARY KEY (`id`),
            KEY `sid` (`sid`,`localSku`,`msku`,`asin`,`parentAsin`,`countryCode`,`project_id`,`yunying_id`,`supplier_id`,`reportDateMonth`,`is_delete`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='月度汇总统计';");
    }
}