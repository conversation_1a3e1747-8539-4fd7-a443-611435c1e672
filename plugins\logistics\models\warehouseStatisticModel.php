<?php
/**
 * @author: warehouseStatistic
 * @Time: 2024/8/10 10:00
 */

namespace plugins\logistics\models;

use core\lib\db\dbLMysql;
use Exception;

class warehouseStatisticModel
{
    // 导出表头定义
    public static $table_key_list = [
        // 基础信息字段
        "sys_wid"         => "系统仓库ID",
        "ware_house_name" => "仓库名称",
        "seller_name"     => "店铺名称",
        "sku"             => "SKU",
        "fnsku"           => "FNSKU",
        "product_name"    => "产品名称",
        // 期初统计字段
        "total_day_start_count"             => "期初总数量(含移仓+在途)",
        "total_day_start_cost"              => "期初总成本(含移仓+在途)",
        "day_start_count_with_transferring" => "期初库存数量(含移仓)",
        "day_start_cost_with_transferring"  => "期初库存成本(含移仓)",
        "day_start_count"                   => "期初库存-数量",
        "day_start_cost"                    => "期初库存-成本",
        "day_start_count_transferring"      => "期初移仓在途-数量",
        "day_start_cost_transferring"       => "期初移仓在途-总成本",
        "day_start_count_in_transit"        => "期初在途-数量",
        "day_start_cost_in_transit"         => "期初在途-成本",

        // 增减项统计
        "total_in_count"  => "数量增加项",
        "total_out_count" => "数量减少项",
        "total_count"     => "数量增减项（合计）",
        "total_in_cost"   => "成本增加项",
        "total_out_cost"  => "成本减少项",
        "total_cost"      => "成本增减项（合计）",

        // 期末详细统计
        "total_day_end_count"           => "期末总数量(含移仓+在途)",
        "total_day_end_cost"            => "期末总成本(含移仓+在途)",
        "day_end_count"                 => "期末库存-数量", // end_count
        "day_end_cost"                  => "期末库存-成本", // end_total_amount
        "allocation_in_transit_count"   => "期末在途-数量", // FBA中为end_on_way_count
        "allocation_in_transit_cost"    => "期末在途-成本", // FBA中为end_on_way_total_amount
        "transferring_out_count"        => "移仓在途-数量",
        "transferring_out_total_amount" => "移仓在途-总成本",
    ];    // 创建统计表
    public static function createWarehouseStatisticTable($year_month)
    {
        $year_array = explode('-', $year_month);
        $table_name = 'warehouse_statistic_' . $year_array[0];
        $dbF = dbLMysql::getInstance();

        // 创建统计表
        $dbF->query("CREATE TABLE IF NOT EXISTS `oa_l_$table_name` (
          `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
          `m_date` varchar(10) NOT NULL COMMENT '月份',
          `sys_wid` int(10) NOT NULL DEFAULT 0 COMMENT '系统仓库ID',
          `ware_house_name` varchar(100) NOT NULL DEFAULT '' COMMENT '仓库名称',
          `from_type` int(11) NOT NULL DEFAULT '0' COMMENT '来源类型：1本地仓，2海外仓，3FB仓',
          `from_id` int(11) NOT NULL DEFAULT '0' COMMENT '来源ID',
          `country_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
          `country` varchar(10) NOT NULL COMMENT '国家名称',
          `msku` varchar(100) NOT NULL,
          `warehouse_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '仓库类型：1 本地仓，3 海外仓，4 平台仓，6 AWD仓',
          
          `seller_name` varchar(100) NOT NULL DEFAULT '' COMMENT '店铺名称',
          `sku` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'SKU',
          `fnsku` varchar(100) NOT NULL DEFAULT '' COMMENT 'FNSKU',
          `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '产品名称',
          
          `total_day_start_count` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '期初总数量(含移仓+在途)',
          `total_day_start_cost` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '期初总成本(含移仓+在途)',
          `day_start_count_with_transferring` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '期初库存数量(含移仓)',
          `day_start_cost_with_transferring` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '期初库存成本(含移仓)',
          `day_start_count` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '期初库存-数量',
          `day_start_cost` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '期初库存-成本',
          `day_start_count_transferring` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '期初移仓在途-数量',
          `day_start_cost_transferring` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '期初移仓在途-总成本',
          `day_start_count_in_transit` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '期初在途-数量',
          `day_start_cost_in_transit` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '期初在途-成本',
          
          `total_in_count` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '数量增加项',
          `total_out_count` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '数量减少项',
          `total_count` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '数量增减项（合计）',
          `total_in_cost` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '成本增加项',
          `total_out_cost` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '成本减少项',
          `total_cost` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '成本增减项（合计）',
          
          `total_day_end_count` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '期末总数量(含移仓+在途)',
          `total_day_end_cost` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '期末总成本(含移仓+在途)',
          `day_end_count` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '期末库存-数量',
          `day_end_cost` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '期末库存-成本',
          `allocation_in_transit_count` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '期末在途-数量',
          `allocation_in_transit_cost` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '期末在途-成本',
          `transferring_out_count` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '移仓在途-数量',
          `transferring_out_total_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '移仓在途-总成本',
          
          `updated_time` datetime DEFAULT NULL,
          `created_time` datetime DEFAULT NULL,
          `is_delete` tinyint(1) NOT NULL DEFAULT '0',
          `remark` varchar(255) DEFAULT NULL COMMENT '备注',
          PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='仓库统计数据{$year_array[0]}（金额默认人民币）';");
    }

    // 统计并合并三种仓库数据
    public static function mergeWarehouseData($year_month)
    {
        $db = dbLMysql::getInstance();

        // 确保统计表存在
        self::createWarehouseStatisticTable($year_month);

        // 获取当前月份和上一个月份
        $current_date = date('Y-m', strtotime($year_month));
        $prev_date = date('Y-m', strtotime("-1 month", strtotime($year_month)));

        // 表名
        $current_year = date('Y', strtotime($year_month));
        $prev_year = date('Y', strtotime($prev_date));
        $table_name = "warehouse_statistic_" . $current_year;
        $prev_table_name = "warehouse_statistic_" . $prev_year;

        // 1. 清空当前月份的数据
        $db->table("oa_f_$table_name")
            ->where("m_date = :m_date", ['m_date' => $current_date])
            ->delete();

        // 2. 获取上个月的数据作为期初数据
        $prev_data = [];
        // 如果上个月和当前月不在同一年，需要检查上一年的表是否存在
        if ($current_year != $prev_year) {
            $exists = $db->query("SHOW TABLES LIKE 'oa_l_$prev_table_name'");
            if ($exists) {
                $prev_data = $db->table("oa_l_$prev_table_name")
                    ->where("m_date = :m_date AND is_delete = 0", ['m_date' => $prev_date])
                    ->list();
            }
        } else {
            $prev_data = $db->table("oa_f_$table_name")
                ->where("m_date = :m_date AND is_delete = 0", ['m_date' => $prev_date])
                ->list();
        }

        // 3. 处理每个仓库数据
        self::processLocalWarehouse($db, $current_date, $prev_data, $table_name);
        self::processOverseasWarehouse($db, $current_date, $prev_data, $table_name);
        self::processFBWarehouse($db, $current_date, $prev_data, $table_name);

        // 4. 合并相同SKU的数据
        self::mergeWarehouseDataBySKU($db, $current_date, $table_name);

    }

    // 处理本地仓数据
    protected static function processLocalWarehouse($db, $current_date, $prev_data, $table_name)
    {

    }

    // 处理海外仓数据
    protected static function processOverseasWarehouse($db, $current_date, $prev_data, $table_name)
    {

    }

    // 处理FB仓数据
    protected static function processFBWarehouse($db, $current_date, $prev_data, $table_name)
    {

    }

    // 合并相同SKU的仓库数据
    protected static function mergeWarehouseDataBySKU($db, $current_date, $table_name)
    {
        // 查询出所有相同SKU的数据
        $duplicate_skus = $db->query("
            SELECT sku, msku, asin, p_asin, country, store_name
            FROM oa_f_$table_name
            WHERE m_date = '$current_date' AND is_delete = 0
            GROUP BY sku, msku, asin, p_asin, country, store_name
            HAVING COUNT(*) > 1
        ");

        foreach ($duplicate_skus as $item) {
            // 获取该SKU的所有记录
            $records = $db->table("oa_f_$table_name")
                ->where("m_date = :m_date AND sku = :sku AND msku = :msku AND asin = :asin AND p_asin = :p_asin AND country = :country AND store_name = :store_name AND is_delete = 0", [
                    'm_date'     => $current_date,
                    'sku'        => $item['sku'],
                    'msku'       => $item['msku'],
                    'asin'       => $item['asin'],
                    'p_asin'     => $item['p_asin'],
                    'country'    => $item['country'],
                    'store_name' => $item['store_name']
                ])
                ->list();

            if (count($records) <= 1) {
                continue;
            }

            // 合并所有记录的数值
            $merged_data = [
                // 保留第一条记录的基础信息
                'yunying_id'                           => $records[0]['yunying_id'],
                'project_id'                           => $records[0]['project_id'],
                'project_name'                         => $records[0]['project_name'],
                'yunying'                              => $records[0]['yunying'],
                'country_code'                         => $records[0]['country_code'],
                'goods_name'                           => $records[0]['goods_name'],
                'm_date'                               => $current_date,
                'is_error'                             => 0,
                'is_delete'                            => 0,
                'updated_time'                         => date('Y-m-d H:i:s'),

                // 初始化需要累加的字段
                'init_inventory_quantity'              => 0,
                'init_inventory_cost'                  => 0,
                'init_inventory_quantity_with_transit' => 0,
                'init_inventory_cost_with_transit'     => 0,
                'init_in_transit_quantity'             => 0,
                'init_in_transit_cost'                 => 0,
                'quantity_increase'                    => 0,
                'quantity_decrease'                    => 0,
                'quantity_change_total'                => 0,
                'cost_increase'                        => 0,
                'cost_decrease'                        => 0,
                'cost_change_total'                    => 0,
                'final_inventory_quantity'             => 0,
                'final_inventory_cost'                 => 0,
                'final_in_transit_quantity'            => 0,
                'final_in_transit_cost'                => 0,
                'final_transit_warehouse_quantity'     => 0,
                'final_transit_warehouse_cost'         => 0,
                'final_total_quantity'                 => 0,
                'final_total_cost'                     => 0,
                'final_total_quantity_with_transit'    => 0,
                'final_total_cost_with_transit'        => 0,
            ];

            // 累加所有记录的数据
            foreach ($records as $record) {
                $numeric_fields = [
                    'init_inventory_quantity', 'init_inventory_cost', 'init_inventory_quantity_with_transit',
                    'init_inventory_cost_with_transit', 'init_in_transit_quantity', 'init_in_transit_cost',
                    'quantity_increase', 'quantity_decrease', 'quantity_change_total',
                    'cost_increase', 'cost_decrease', 'cost_change_total',
                    'final_inventory_quantity', 'final_inventory_cost', 'final_in_transit_quantity',
                    'final_in_transit_cost', 'final_transit_warehouse_quantity', 'final_transit_warehouse_cost',
                    'final_total_quantity', 'final_total_cost', 'final_total_quantity_with_transit',
                    'final_total_cost_with_transit'
                ];

                foreach ($numeric_fields as $field) {
                    $merged_data[$field] += floatval($record[$field]);
                }
            }

            // 删除原有记录
            $db->table("oa_f_$table_name")
                ->where("m_date = :m_date AND sku = :sku AND msku = :msku AND asin = :asin AND p_asin = :p_asin AND country = :country AND store_name = :store_name AND is_delete = 0", [
                    'm_date'     => $current_date,
                    'sku'        => $item['sku'],
                    'msku'       => $item['msku'],
                    'asin'       => $item['asin'],
                    'p_asin'     => $item['p_asin'],
                    'country'    => $item['country'],
                    'store_name' => $item['store_name']
                ])
                ->delete();

            // 插入合并后的记录
            $merged_data['sku'] = $item['sku'];
            $merged_data['msku'] = $item['msku'];
            $merged_data['asin'] = $item['asin'];
            $merged_data['p_asin'] = $item['p_asin'];
            $merged_data['country'] = $item['country'];
            $merged_data['store_name'] = $item['store_name'];
            $merged_data['created_time'] = date('Y-m-d H:i:s');
            $merged_data['remark'] = '多仓库合并数据';

            $db->table("oa_f_$table_name")->insert($merged_data);
        }
    }

    public static function calculateTotalCountAndCost($data)
    {
        $inTypes = [
            'allocation_in',             // 调拨入库
            'change_of_standard_in',     // 换标入库
            'inventory_surplus_in',      // 盘盈入库
            'other_in',                  // 其他入库
            'outsourcing_in',            // 委外入库
            'processing_in',             // 加工入库
            'purchase_in',               // 采购入库
            'remove_in',                 // 移除入库
            'return_goods_in',           // 退货入库
            'split_in',                  // 拆分入库
            'gifts_in',                  // 赠品入库
        ];
        $outTypes = [
            'allocation_out',            // 调拨出库
            'change_of_standard_out',    // 换标出库
            'fba_out',                   // FBA出库
            'fbm_out',                   // FBM出库
            'inventory_deficit_out',     // 盘亏出库
            'other_out',                 // 其他出库
            'outsourcing_out',          // 委外出库
            'processing_out',            // 加工出库
            'purchase_return',           // 退货出库（无 _out 后缀，特殊）
            'split_out',                 // 拆分出库
            'wfs_out',                   // WFS出库

            'destruction_out', // 销毁出库
            'shein_out', // SHEIN出库
            'temu_out', // Temu出库
            'vc_po_out', // VC-PO出库
            'vc_df_out' // VC-DF出库

        ];

        $total_in_count = 0;
        $total_in_cost = 0;
        $total_out_count = 0;
        $total_out_cost = 0;
        foreach ($inTypes as $type) {
            if (isset($data[$type . '_count']) && isset($data[$type . '_cost'])) {
                $total_in_count += floatval($data[$type . '_count']);
                $total_in_cost += floatval($data[$type . '_cost']);
            }
        }
        foreach ($outTypes as $type) {
            if (isset($data[$type . '_count']) && isset($data[$type . '_cost'])) {
                $total_out_count += floatval($data[$type . '_count']);
                $total_out_cost += floatval($data[$type . '_cost']);
            }
        }

        // FBA出库和FBM出库的特殊处理
        $fbaInType = [
            'receipts_', // 货件补货
            'customer_returns_', // 买家退货
            'found_' // 已找到
        ];
        $fbaOutType = [
            'shipments_', // 订单发货
            'vendor_returns_', // 库存移除
            'damaged_', // 已残损
            'lost_', // 丢失
            'disposed_' // 弃置
        ];
        foreach ($fbaInType as $type) {
            if (isset($data[$type . 'count']) && isset($data[$type . 'total_amount'])) {
                $total_in_count += floatval($data[$type . 'count']);
                $total_in_cost += floatval($data[$type . 'total_amount']);
            }
        }
        foreach ($fbaOutType as $type) {
            if (isset($data[$type . 'count']) && isset($data[$type . 'total_amount'])) {
                $total_out_count += floatval($data[$type . 'count']);
                $total_out_cost += floatval($data[$type . 'total_amount']);
            }
        }

        return [
            'total_in_count'  => $total_in_count,
            'total_in_cost'   => $total_in_cost,
            'total_out_count' => $total_out_count,
            'total_out_cost'  => $total_out_cost,
            'total_count'     => $total_in_count + $total_out_count,
            'total_cost'      => $total_in_cost + $total_out_cost
        ];
    }

    public function getAggregatedStatistic($param)
    {
        $db = dbLMysql::getInstance();
        $year_month = $param['year_month'] ?? date('Y-m');
        $year = date('Y', strtotime($year_month));
        $table_name = 'warehouse_statistic_' . $year;

        // 聚合查询 - 按仓库(sys_wid)分组统计
        $query = $db->table($table_name)
            ->field(
                'm_date, sys_wid, MAX(ware_house_name) as ware_house_name, ' .
                'MAX(country_code) as country_code, MAX(country) as country, ' .
                'MAX(warehouse_type) as warehouse_type, ' .
                'GROUP_CONCAT(DISTINCT msku SEPARATOR ",") as msku, ' .
                'GROUP_CONCAT(DISTINCT sku SEPARATOR ",") as sku, ' .
                'GROUP_CONCAT(DISTINCT fnsku SEPARATOR ",") as fnsku, ' .
                'GROUP_CONCAT(DISTINCT product_name SEPARATOR ",") as product_name, ' .
                'SUM(total_day_start_count) as total_day_start_count, ' .
                'SUM(total_day_start_cost) as total_day_start_cost, ' .
                'SUM(day_start_count_with_transferring) as day_start_count_with_transferring, ' .
                'SUM(day_start_cost_with_transferring) as day_start_cost_with_transferring, ' .
                'SUM(day_start_count) as day_start_count, ' .
                'SUM(day_start_cost) as day_start_cost, ' .
                'SUM(day_start_count_transferring) as day_start_count_transferring, ' .
                'SUM(day_start_cost_transferring) as day_start_cost_transferring, ' .
                'SUM(day_start_count_in_transit) as day_start_count_in_transit, ' .
                'SUM(day_start_cost_in_transit) as day_start_cost_in_transit, ' .
                'SUM(total_in_count) as total_in_count, ' .
                'SUM(total_out_count) as total_out_count, ' .
                'SUM(total_count) as total_count, ' .
                'SUM(total_in_cost) as total_in_cost, ' .
                'SUM(total_out_cost) as total_out_cost, ' .
                'SUM(total_cost) as total_cost, ' .
                'SUM(total_day_end_count) as total_day_end_count, ' .
                'SUM(total_day_end_cost) as total_day_end_cost, ' .
                'SUM(day_end_count) as day_end_count, ' .
                'SUM(day_end_cost) as day_end_cost, ' .
                'SUM(allocation_in_transit_count) as allocation_in_transit_count, ' .
                'SUM(allocation_in_transit_cost) as allocation_in_transit_cost, ' .
                'SUM(transferring_out_count) as transferring_out_count, ' .
                'SUM(transferring_out_total_amount) as transferring_out_total_amount, ' .
                'COUNT(*) as sku_count'
            )
            ->where("m_date = :m_date AND is_delete = 0", ['m_date' => $year_month]);

        // 筛选条件
        if (!empty($param['warehouse_type'])) {
            if ($param['warehouse_type'] == '-1') {
                // -1表示查询移除仓
                $query->andWhere('ware_house_name LIKE :warehouse_name', ['warehouse_name' => '%移除%']);
            } else {
                // 其他类型直接筛选
                $query->andWhere('warehouse_type = :warehouse_type', ['warehouse_type' => $param['warehouse_type']]);
            }
        }
        if (!empty($param['product_name'])) {
            $query->andWhere('product_name LIKE :product_name', ['product_name' => '%' . $param['product_name'] . '%']);
        }
        if (!empty($param['sku'])) {
            $query->andWhere('sku LIKE :sku', ['sku' => '%' . $param['sku'] . '%']);
        }
        if (!empty($param['warehouse_name'])) {
            $query->andWhere('ware_house_name LIKE :warehouse_name', ['warehouse_name' => '%' . $param['warehouse_name'] . '%']);
        }

        // 按sys_wid分组
        $query->groupBy(['sys_wid', 'm_date']);

        // 排序 - 支持所有SUM字段排序
        $sort_field = $param['sort_field'] ?? 'total_day_end_cost';
        $sort_order = $param['sort_order'] ?? 'desc';
        
        // 定义所有可排序的SUM字段
        $sortable_sum_fields = [
            'total_day_start_count',
            'total_day_start_cost', 
            'day_start_count_with_transferring',
            'day_start_cost_with_transferring',
            'day_start_count',
            'day_start_cost',
            'day_start_count_transferring',
            'day_start_cost_transferring',
            'day_start_count_in_transit',
            'day_start_cost_in_transit',
            'total_in_count',
            'total_out_count',
            'total_count',
            'total_in_cost',
            'total_out_cost',
            'total_cost',
            'total_day_end_count',
            'total_day_end_cost',
            'day_end_count',
            'day_end_cost',
            'allocation_in_transit_count',
            'allocation_in_transit_cost',
            'transferring_out_count',
            'transferring_out_total_amount',
            'sku_count'
        ];
        
        // 其他可排序字段
        $other_sortable_fields = [
            'sys_wid',
            'ware_house_name',
            'warehouse_type',
            'm_date'
        ];
        
        // 合并所有可排序字段
        $all_sortable_fields = array_merge($sortable_sum_fields, $other_sortable_fields);
        
        // 验证排序字段和排序方向
        if (!empty($sort_field) && in_array($sort_field, $all_sortable_fields)) {
            $sort_order = strtolower($sort_order);
            if (!in_array($sort_order, ['asc', 'desc'])) {
                $sort_order = 'desc'; // 默认降序
            }
            $query->order($sort_field . ' ' . $sort_order);
        } else {
            // 默认排序
            $query->order('total_day_end_cost desc');
        }

        // 分页查询
        $list = $query->pages(
            $param['page'] ?? 1,
            $param['page_size'] ?? 20
        );

        return $list;
    }
}
