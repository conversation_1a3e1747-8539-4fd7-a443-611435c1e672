<?php

namespace plugins\assessment\controller;

use plugins\assessment\models\userModel;
use core\lib\db\dbAMysql;
use core\lib\redisCached;

class myAssessmentController
{
    // 获取考核档案列表
    public function getList()
    {
        $paras_list = array('assessment_name', 'assessment_cycle_type', 'assessment_cycle', 'level', 'page', 'page_size');
        $param = arrangeParam($_POST, $paras_list);

        // 需要拉取考核人员列表
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_users', 'au')->field('au.*');
        $adb->leftJoin('assessment', 'a', 'a.id = au.a_id');
        $adb->where('where a.is_delete = 0 and au.status = 1 and a.status = 1 and confirm_config is not null')
            ->andWhere('au.user_id = :user_id', ['user_id' => userModel::$qwuser_id]);

        if ($param['assessment_name']) {
            $adb->andWhere(' a.assessment_name like :assessment_name', ['assessment_name' => "%{$param['assessment_name']}%"]);
        }
        if ($param['assessment_cycle_type']) {
            $assessment_cycle_type = json_decode($param['assessment_cycle_type'], true);
            $adb->whereIn('JSON_EXTRACT(a.attach, "$.scheme.assessment_cycle")',$assessment_cycle_type);

        }
        if (!empty($param['assessment_cycle'])) {
            $assessment_cycle = json_decode($param['assessment_cycle'], true);
            $adb->andWhere('JSON_UNQUOTE(JSON_EXTRACT(a.assessment_cycle, "$[1]")) >= :start_time 
                    and JSON_UNQUOTE(JSON_EXTRACT(a.assessment_cycle, "$[0]")) <= :end_time ',
                ['start_time' => $assessment_cycle[0], 'end_time' => $assessment_cycle[1]]);
        }
        if ($param['level']) {
            $adb->andWhere(' JSON_EXTRACT(au.result, \'$.level\') = \'' . $param['level'] . '\'');
        }

        $assessment_users = $adb->pages($param['page'], $param['page_size']);

        $users = redisCached::getUserInfo();
        $user_map = array_column($users, 'wname', 'user_id');

        if (empty($assessment_users['list'])) {

            returnSuccess($assessment_users);
        }

        $adb = dbAMysql::getInstance();
        $adb->table('assessment');
        $adb->whereIn('id', array_column($assessment_users['list'], 'a_id'));
        $assessment = $adb->list();
        $assessment_map = array_column($assessment, null,'id');

        foreach ($assessment_users['list'] as &$assessment_user) {
            $assessment_user['result'] = json_decode($assessment_user['result'], true);
            $assessment_user['attach'] = json_decode($assessment_user['attach'], true);
            $assessment_user['targets'] = json_decode($assessment_user['targets'], true);
            $assessment_user['process'] = json_decode($assessment_user['process'], true);
            $assessment_user['confirm_config'] = json_decode($assessment_user['confirm_config'], true);

            $assessment_user['assessment'] = $assessment_map[$assessment_user['a_id']];
            $assessment_user['user_name'] = $user_map[$assessment_user['user_id']];
            $assessment_user['audit_user_name'] = $user_map[$assessment_user['audit_user_id']] ?? '';
        }
        returnSuccess($assessment_users);

    }

    // 获取考核详情
    public function getDetail()
    {
        $paras_list = array('id');
        $param = arrangeParam($_POST, $paras_list);
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_users', 'au')->field('au.*');
        $adb->where('where id = :id', ['id' => $param['id']]);
        $assessment_user = $adb->one();
        $assessment_user['result'] = json_decode($assessment_user['result'], true);
        $assessment_user['attach'] = json_decode($assessment_user['attach'], true);
        $assessment_user['targets'] = json_decode($assessment_user['targets'], true);
        $assessment_user['process'] = json_decode($assessment_user['process'], true);
        $assessment_user['confirm_config'] = json_decode($assessment_user['confirm_config'], true);

        // 获取考核
        $adb->table('assessment', 'a')->field('a.*');
        $adb->where('where a.id = :id', ['id' => $assessment_user['a_id']]);
        $assessment = $adb->one();

        $all_users = redisCached::getUserInfo();
        $all_users = array_column($all_users->list(), null, 'id');

        returnSuccess([
            'assessment_user' => $assessment_user,
            'assessment'      => $assessment,
        ]);
    }

    // 确认
    public function confirm()
    {
        $paras_list = array('id', 'confirm_status');
        $param = arrangeParam($_POST, $paras_list);

        $adb = dbAMysql::getInstance();
        // 查询用户考核任务
        $adb->table('assessment_users');
        $assessment_user = $adb->where('where id = :id', ['id' => $param['id']])->one();
        if (!$assessment_user) returnError('考核用户不存在！');
        if (empty($assessment_user['confirm_config'])) returnError('当前考核不能进行确认');

        $confirm_config = json_decode($assessment_user['confirm_config'], true);
        $confirm_config['confirm_status'] = $param['confirm_status'];

        $adb->table('assessment_users')->where('id = :id', ['id' => $param['id']])
            ->update(['confirm_config' => json_encode($confirm_config)]);
        returnSuccess([], '确认成功');
    }

}