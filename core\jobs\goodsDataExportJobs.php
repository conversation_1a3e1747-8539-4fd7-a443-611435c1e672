<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/24 9:57
 *
 * 导出报告数据
 */

namespace core\jobs;

use core\lib\config;
use core\lib\db\dbFMysql;
use financial\form\customColumnForm;
use financial\form\goodsDataForm;

class goodsDataExportJobs
{
    public string $unqueid = '';
    public string $key = '';
    public string $user_id;
    public array $export_list = [];
    public array $param = [];
    public int $page;
    public int $pages_size = 1000;
    public function __construct($key,$param,$export_list,$user_id,$page){
        $this->unqueid = uniqid();
        $this->key = $key;
        $this->export_list = $export_list;
        $this->user_id = $user_id;
        $this->param = $param;
        $this->page = $page;
    }
    public function task(){
        $redis = (new \core\lib\predisV())::$client;
        $export_data = json_decode($redis->get($this->key),true);
        $db = dbFMysql::getInstance();
        if ($this->param['type'] == 0 ) {
            $param_ = [
                'date_time'=>$this->param['date_time'],
                'is_error'=>json_decode('is_error'),
                'ids'=>json_decode('ids'),
                'page'=>$this->page,
                'page_size'=>$this->pages_size,
                'project_ids'=>$this->param['project_ids'],
                'search_type'=>$this->param['search_type'],
                'search_value'=>$this->param['search_value'],
                'country_code'=>$this->param['country_code'],
                'import_time'=>$this->param['import_time'],
            ];
        } else {
            $param_ = [
                'date_time'=>$this->param['date_time'],
                'project_ids'=>$this->param['project_ids'],
                'search_type'=>$this->param['search_type'],
                'search_value'=>$this->param['search_value'],
                'is_error'=>json_decode('is_error'),
                'page'=>$this->page,
                'page_size'=>$this->pages_size,
                'country_code'=>$this->param['country_code'],
                'import_time'=>$this->param['import_time'],
            ];
        }
        $form = new goodsDataForm($this->param['date_time']);
        $data = $form->getList($param_);
        if (count($data['list'])) {
            $url = $form->exportData($data['list'],$this->export_list);
            $export_data['success_count'] = $export_data['success_count']+count($data['list']);
            $export_data['excel_url'][] = $url;
            if ($export_data['success_count'] < $export_data['total']) {
                $page = $this->page+1;
                $queue_key = config::get('delay_queue_key', 'app');
                $task = new goodsDataExportJobs($this->key,$this->param,$this->export_list,$this->user_id,$page); // 创建任务类实例
                $redis->zAdd($queue_key, [], 0, serialize($task));
            } else {
                //保存
                //生成压缩包
                $save_path = '/public_financial/downLoad/goods_data/data';
                if (!file_exists(SELF_FK.$save_path)) {
                    mkdir(SELF_FK.$save_path, 0777, true);
                }
                $zip_url = $save_path ."/".date('YmdHis').'.zip';
                //生成压缩包
                setZipByUrl($export_data['excel_url'],$zip_url);
                $export_data['zip_url'] = $zip_url;
                //保存导出历史
                $db->table('goods_stock_import')
                    ->insert([
                        'user_id'=>$this->user_id,
                        'type'=>1,
                        'export_path'=>$zip_url,
                        'total'=>$export_data['total'],
                        'success_count'=>$export_data['success_count'],
                        'fail_count'=>0,
                        'report_date'=>$this->param['date_time'],
                        'created_time'=>date('Y-m-d H:i:s'),
                    ]);
            }
            $redis->set($this->key,json_encode($export_data));
            $redis->expire($this->key,60*60);
        }
    }
}