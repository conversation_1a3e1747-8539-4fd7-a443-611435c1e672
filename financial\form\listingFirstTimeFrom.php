<?php
/**
 * @author: zhangguoming
 * @Time: 2025/1/7 15:26
 */

namespace financial\form;

class listingFirstTimeFrom
{
    /**
     * @param $dbF
     * @param $type
     * @param $asins
     * @return void
     */
    public static function getNotNewList($dbF,$asins = []) {
        //非新品新品查询
        $twoMonthsAgoTime = strtotime('-2 months');

        $dbF->table('listing_first_time')
            ->where('first_time < :first_time',['first_time'=>$twoMonthsAgoTime]);
        if (count($asins)) {
            $dbF->whereIn('asin',$asins);
        }
        $first_time_list = $dbF->field('asin,country_code')
            ->list();
        return $first_time_list;
    }
}