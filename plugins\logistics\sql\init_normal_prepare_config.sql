-- 初始化normal_prepare配置
-- 用于FBA销量统计的产品阶段配置

-- 检查并插入normal_prepare配置
INSERT INTO `oa_l_config` (`key_name`, `data`, `created_at`, `updated_at`) 
VALUES ('normal_prepare', '[
  {
    "type": "1",
    "sales_config": {
      "sales_7": "60",
      "sales_14": "20", 
      "sales_30": "20"
    },
    "data_range": [
      {"country": "JP", "min_value": 1, "max_value": 3, "normal_value": 2},
      {"country": "DE", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "CA", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "IT", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "US", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "FR", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "UK", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "ES", "max_value": 3, "min_value": 1, "normal_value": 2}
    ]
  },
  {
    "type": "2",
    "sales_config": {
      "sales_7": "50",
      "sales_14": "25",
      "sales_30": "25"
    },
    "data_range": [
      {"country": "JP", "min_value": 1, "max_value": 3, "normal_value": 2},
      {"country": "DE", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "CA", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "IT", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "US", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "FR", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "UK", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "ES", "max_value": 3, "min_value": 1, "normal_value": 2}
    ]
  },
  {
    "type": "3",
    "sales_config": {
      "sales_7": "40",
      "sales_14": "30",
      "sales_30": "30"
    },
    "data_range": [
      {"country": "JP", "min_value": 1, "max_value": 3, "normal_value": 2},
      {"country": "DE", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "CA", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "IT", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "US", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "FR", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "UK", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "ES", "max_value": 3, "min_value": 1, "normal_value": 2}
    ]
  },
  {
    "type": "4",
    "sales_config": {
      "sales_7": "70",
      "sales_14": "15",
      "sales_30": "15"
    },
    "data_range": [
      {"country": "JP", "min_value": 1, "max_value": 3, "normal_value": 2},
      {"country": "DE", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "CA", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "IT", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "US", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "FR", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "UK", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "ES", "max_value": 3, "min_value": 1, "normal_value": 2}
    ]
  },
  {
    "type": "5",
    "sales_config": {
      "sales_7": "30",
      "sales_14": "35",
      "sales_30": "35"
    },
    "data_range": [
      {"country": "JP", "min_value": 1, "max_value": 3, "normal_value": 2},
      {"country": "DE", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "CA", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "IT", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "US", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "FR", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "UK", "max_value": 3, "min_value": 1, "normal_value": 2},
      {"country": "ES", "max_value": 3, "min_value": 1, "normal_value": 2}
    ]
  }
]', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
  `data` = VALUES(`data`),
  `updated_at` = NOW();

-- 配置说明:
-- type: 产品阶段 (1:新品期, 2:成长期, 3:稳定期, 4:衰退期, 5:清货)
-- sales_config: 销量权重配置
--   - sales_7: 7天销量权重 (百分比)
--   - sales_14: 14天销量权重 (百分比)  
--   - sales_30: 30天销量权重 (百分比)
-- data_range: 国家范围配置 (暂时保留，未来可能用于其他计算)
--   - country: 国家代码
--   - min_value, max_value, normal_value: 数值范围 (预留字段)

-- 日均销量计算公式:
-- daily_avg_sales = (7天销量均值 × sales_7权重) + (14天销量均值 × sales_14权重) + (30天销量均值 × sales_30权重)

-- 各产品阶段的权重策略:
-- 新品期(1): 重视近期销量 (7天60%, 14天20%, 30天20%)
-- 成长期(2): 平衡近期和中期 (7天50%, 14天25%, 30天25%)  
-- 稳定期(3): 更平衡的权重 (7天40%, 14天30%, 30天30%)
-- 衰退期(4): 重视最近趋势 (7天70%, 14天15%, 30天15%)
-- 清货期(5): 重视长期趋势 (7天30%, 14天35%, 30天35%)
