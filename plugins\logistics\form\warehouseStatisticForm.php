<?php
/**
 * @author: warehouseStatistic
 * @Time: 2024/8/10 10:00
 */

namespace plugins\logistics\form;

use core\lib\db\dbErpMysql;
use core\lib\db\dbLMysql;
use plugins\logistics\models\warehouseStatisticModel;

class warehouseStatisticForm
{
    protected $table_name;
    
    public function __construct($year_month) {
        $year = date('Y', strtotime($year_month));
        $this->table_name = "warehouse_statistic_" . $year;
        // 确保表存在
        warehouseStatisticModel::createWarehouseStatisticTable($year_month);
    }
    
    // 执行统计任务
    public function executeStatistic($year_month) {
        $db = dbLMysql::getInstance();
        $db->beginTransaction();
        
        try {
            // 开始统计
            $result = $this->collectWarehouseData($year_month);
            if (!$result) {
                throw new \Exception('数据收集失败');
            }
            
            $db->commit();
            return ['code' => 0, 'msg' => '统计成功'];
        } catch (\Exception $e) {
            $db->rollBack();
            throw new \Exception('统计失败：' . $e->getMessage());
        }
    }
    
    // 收集所有仓库数据
    protected function collectWarehouseData($year_month) {
        $db = dbLMysql::getInstance();
        $erp_db = dbErpMysql::getInstance();
        
        // 获取日期范围
        $start_date = $year_month . '-01';
        $end_date = date('Y-m-t', strtotime($start_date));
        
        try {
            // 清空当前月份的数据
            $db->table($this->table_name)
                ->where("m_date = :m_date", ['m_date' => $year_month])
                ->delete();
            
            // 1. 收集本地仓数据
            $this->collectLocalWarehouseData($year_month, $start_date, $end_date);
            
            // 2. 收集海外仓数据
            $this->collectOverseasWarehouseData($year_month, $start_date, $end_date);
            
            // 3. 收集FBA仓数据
            $this->collectFBAWarehouseData($year_month, $start_date, $end_date);
            
            return true;
        } catch (\Exception $e) {
            throw new \Exception('数据收集失败：' . $e->getMessage());
        }
    }
    
    // 收集本地仓数据
    protected function collectLocalWarehouseData($year_month, $start_date, $end_date) {
        $erp_db = dbErpMysql::getInstance();
        
        // 获取本地仓数据表名
        $table_name = 'lingxing_inventory_storage_report_local_detail_' . date('Ym', strtotime($start_date));
        
        try {
            // 从本地仓库存报表中获取数据
            $local_data = $erp_db->table($table_name)
                ->where("report_month = :report_month", ['report_month' => $year_month])
                ->list();

            // 查询涉及的仓库
            $sys_wids = array_unique(array_column($local_data, 'sys_wid'));
            $warehouse_list = $erp_db->table('lingxing_warehouse')
                ->whereIn('id', $sys_wids)
                ->field('id,wid,type')
                ->list();
            $warehouse_ = array_column($warehouse_list, 'type', 'wid');

            // 将获取的数据插入到统计表
            foreach ($local_data as $item) {
                $this->insertWarehouseData($item, 1, $year_month, $warehouse_[$item['sys_wid']] ?? 0); // 1表示本地仓
            }
        } catch (\Exception $e) {
            // 如果表不存在或查询失败，记录日志但不抛出异常
            throw new \Exception($e->getMessage());
        }
    }
    
    // 收集海外仓数据
    protected function collectOverseasWarehouseData($year_month, $start_date, $end_date) {
        $erp_db = dbErpMysql::getInstance();
        
        // 获取海外仓数据表名
        $table_name = 'lingxing_inventory_storage_report_overseas_detail_' . date('Ym', strtotime($start_date));
        
        try {
            // 从海外仓库存报表中获取数据
            $overseas_data = $erp_db->table($table_name)
                ->where("report_month = :report_month", ['report_month' => $year_month])
                ->list();

            // 查询涉及的仓库
            $sys_wids = array_unique(array_column($overseas_data, 'sys_wid'));
            $warehouse_list = $erp_db->table('lingxing_warehouse')
                ->whereIn('id', $sys_wids)
                ->field('id,wid,type')
                ->list();
            $warehouse_ = array_column($warehouse_list, 'type', 'wid');
                
            // 将获取的数据插入到统计表
            foreach ($overseas_data as $item) {
                $this->insertWarehouseData($item, 2, $year_month, $warehouse_[$item['sys_wid']] ?? 0); // 3表示海外仓
            }
        } catch (\Exception $e) {
            // 如果表不存在或查询失败，记录日志但不抛出异常
            throw new \Exception($e->getMessage());
        }
    }
    
    // 收集FBA仓数据
    protected function collectFBAWarehouseData($year_month, $start_date, $end_date) {
        $erp_db = dbErpMysql::getInstance();
        
        // 获取FBA仓数据表名
        $table_name = 'lingxing_inventory_storage_report_fba_detail_' . date('Ym', strtotime($start_date));
        
        try {
            // 从FBA仓库存报表中获取数据
            $fba_data = $erp_db->table($table_name)
                ->where("report_month = :report_month", ['report_month' => $year_month])
                ->list();

            // 查询涉及的仓库
            $sys_wids = array_unique(array_column($fba_data, 'wid'));
            $warehouse_list = $erp_db->table('lingxing_warehouse')
                ->whereIn('id', $sys_wids)
                ->field('id,wid,type')
                ->list();
            $warehouse_ = array_column($warehouse_list, 'type', 'wid');
                
            // 将获取的数据插入到统计表
            foreach ($fba_data as $item) {
                $this->insertFBAWarehouseData($item, 3, $year_month, $warehouse_[$item['wid']] ?? 0); // 3表示FBA仓
            }
        } catch (\Exception $e) {
            // 如果表不存在或查询失败，记录日志但不抛出异常
            var_dump('FBA仓数据收集失败: ' . $e->getMessage());
        }
    }
    
    // 插入仓库数据
    protected function insertWarehouseData($item, $from_type, $year_month, $warehouse_type) {
        $db = dbLMysql::getInstance();
        
        // 计算增减项
        $total_data = warehouseStatisticModel::calculateTotalCountAndCost($item);
        
        // 1. 转换数据格式（根据实际的数据结构调整）
        $data = [
            'm_date' => $year_month,
            'sys_wid' => $item['sys_wid'] ?? 0,
            'ware_house_name' => $item['ware_house_name'] ?? '',
            'from_type' => $from_type, // 仅区分本地、海外、FBA
            'from_id' => $item['id'] ?? 0,
            'country_code' => $item['country_code'] ?? '',
            'country' => $item['country'] ?? '',
            'msku' => $item['msku'] ?? $item['sku'] ?? '',
            'warehouse_type' => $warehouse_type,
            'seller_name' => $item['seller_name'] ?? '',
            'sku' => $item['sku'] ?? '',
            'fnsku' => $item['fnsku'] ?? '',
            'product_name' => $item['product_name'] ?? '',
            
            // 期初数据
            'total_day_start_count' => floatval($item['day_early_count'] ?? 0),
            'total_day_start_cost' => floatval($item['day_early_cost'] ?? 0),
            'day_start_count_with_transferring' => floatval($item['day_early_count'] ?? 0),
            'day_start_cost_with_transferring' => floatval($item['day_early_cost'] ?? 0),
            'day_start_count' => floatval($item['day_early_count'] ?? 0),
            'day_start_cost' => floatval($item['day_early_cost'] ?? 0),
            'day_start_count_transferring' => 0,
            'day_start_cost_transferring' => 0,
            'day_start_count_in_transit' => floatval($item['allocation_in_transit_count'] ?? 0),
            'day_start_cost_in_transit' => floatval($item['allocation_in_transit_cost'] ?? 0),
            
            // 增减项
            'total_in_count' => $total_data['total_in_count'],
            'total_out_count' => $total_data['total_out_count'],
            'total_count' => $total_data['total_count'],
            'total_in_cost' => $total_data['total_in_cost'],
            'total_out_cost' => $total_data['total_out_cost'],
            'total_cost' => $total_data['total_cost'],
            
            // 期末数据
            'total_day_end_count' => floatval($item['day_end_count'] ?? 0) + floatval($item['allocation_in_transit_count'] ?? 0),
            'total_day_end_cost' => floatval($item['day_end_cost'] ?? 0) + floatval($item['allocation_in_transit_cost'] ?? 0),
            'day_end_count' => floatval($item['day_end_count'] ?? 0),
            'day_end_cost' => floatval($item['day_end_cost'] ?? 0),
            'allocation_in_transit_count' => floatval($item['allocation_in_transit_count'] ?? 0),
            'allocation_in_transit_cost' => floatval($item['allocation_in_transit_cost'] ?? 0),
            'transferring_out_count' => 0,
            'transferring_out_total_amount' => 0,
            
            'updated_time' => date('Y-m-d H:i:s'),
            'created_time' => date('Y-m-d H:i:s'),
            'is_delete' => 0,
            'remark' => null,
        ];

        // 2. 插入数据库
        $id = $db->table($this->table_name)->insert($data);
    }
    
    // 插入FBA仓库数据
    protected function insertFBAWarehouseData($item, $from_type, $year_month, $warehouse_type) {
        $db = dbLMysql::getInstance();
        
        // FBA仓库数据结构不同，需要特殊处理
        $data = [
            'm_date' => $year_month,
            'sys_wid' => $item['wid'] ?? 0,
            'ware_house_name' => $item['ware_house_name'] ?? '',
            'from_type' => $from_type,
            'from_id' => $item['id'] ?? 0,
            'country_code' => $item['country_code'] ?? '',
            'country' => $item['country_code'] ?? '',
            'msku' => $item['msku'] ?? '',
            'warehouse_type' => $warehouse_type,
            'seller_name' => '', // FBA数据中没有直接的seller_name
            'sku' => $item['local_sku'] ?? '',
            'fnsku' => $item['fnsku'] ?? '',
            'product_name' => $item['local_name'] ?? '',
            
            // 期初数据 - FBA使用start_开头的字段
            'total_day_start_count' => floatval($item['start_count'] ?? 0) + floatval($item['end_on_way_count'] ?? 0),
            'total_day_start_cost' => floatval($item['start_total_amount'] ?? 0) + floatval($item['end_on_way_total_amount'] ?? 0),
            'day_start_count_with_transferring' => floatval($item['start_count'] ?? 0),
            'day_start_cost_with_transferring' => floatval($item['start_total_amount'] ?? 0),
            'day_start_count' => floatval($item['start_count'] ?? 0),
            'day_start_cost' => floatval($item['start_total_amount'] ?? 0),
            'day_start_count_transferring' => floatval($item['transferring_out_count'] ?? 0),
            'day_start_cost_transferring' => floatval($item['transferring_out_total_amount'] ?? 0),
            'day_start_count_in_transit' => 0, // FBA没有在途概念
            'day_start_cost_in_transit' => 0,
            
            // 增减项 - 使用FBA特有的字段计算
            'total_in_count' => $this->calculateFBAIncreaseQuantity($item),
            'total_out_count' => $this->calculateFBADecreaseQuantity($item),
            'total_count' => $this->calculateFBAIncreaseQuantity($item) + $this->calculateFBADecreaseQuantity($item),
            'total_in_cost' => $this->calculateFBAIncreaseCost($item),
            'total_out_cost' => $this->calculateFBADecreaseCost($item),
            'total_cost' => $this->calculateFBAIncreaseCost($item) + $this->calculateFBADecreaseCost($item),
            
            // 期末数据 - FBA使用end_开头的字段
            'total_day_end_count' => floatval($item['end_count'] ?? 0) + floatval($item['end_on_way_count'] ?? 0),
            'total_day_end_cost' => floatval($item['end_total_amount'] ?? 0) + floatval($item['end_on_way_total_amount'] ?? 0),
            'day_end_count' => floatval($item['end_count'] ?? 0),
            'day_end_cost' => floatval($item['end_total_amount'] ?? 0),
            'allocation_in_transit_count' => floatval($item['end_on_way_count'] ?? 0),
            'allocation_in_transit_cost' => floatval($item['end_on_way_total_amount'] ?? 0),
            'transferring_out_count' => floatval($item['transferring_out_count'] ?? 0),
            'transferring_out_total_amount' => floatval($item['transferring_out_total_amount'] ?? 0),
            
            'updated_time' => date('Y-m-d H:i:s'),
            'created_time' => date('Y-m-d H:i:s'),
            'is_delete' => 0,
            'remark' => 'FBA仓库数据',
        ];
        
        // 插入数据库
        $db->table($this->table_name)->insert($data);
    }
    
    // 计算数量增加项
    protected function calculateIncreaseQuantity($item) {
        // 根据实际业务逻辑计算数量增加项
        $increase = 0;
        
        // 采购入库
        $increase += floatval($item['purchase_in_count'] ?? 0);
        
        // 调拨入库
        $increase += floatval($item['allocation_in_count'] ?? 0);
        
        // 委外加工入库
        $increase += floatval($item['outsourcing_in_count'] ?? 0);
        
        // 加工入库
        $increase += floatval($item['processing_in_count'] ?? 0);
        
        // 库存盘盈入库
        $increase += floatval($item['inventory_surplus_in_count'] ?? 0);
        
        // 其他入库
        $increase += floatval($item['other_in_count'] ?? 0);
        
        // 移除入库
        $increase += floatval($item['remove_in_count'] ?? 0);
        
        // 规格变更入库
        $increase += floatval($item['change_of_standard_in_count'] ?? 0);
        
        return $increase;
    }
    
    // 计算数量减少项
    protected function calculateDecreaseQuantity($item) {
        // 根据实际业务逻辑计算数量减少项
        $decrease = 0;
        
        // 采购退回
        $decrease += floatval($item['purchase_return_count'] ?? 0);
        
        // 调拨出库
        $decrease += floatval($item['allocation_out_count'] ?? 0);
        
        // 委外加工出库
        $decrease += floatval($item['outsourcing_out_count'] ?? 0);
        
        // 加工出库
        $decrease += floatval($item['processing_out_count'] ?? 0);
        
        // FBA出库
        $decrease += floatval($item['fba_out_count'] ?? 0);
        
        // FBM出库
        $decrease += floatval($item['fbm_out_count'] ?? 0);
        
        // 库存盘亏出库
        $decrease += floatval($item['inventory_deficit_out_count'] ?? 0);
        
        // 其他出库
        $decrease += floatval($item['other_out_count'] ?? 0);
        
        // 规格变更出库
        $decrease += floatval($item['change_of_standard_out_count'] ?? 0);
        
        return $decrease;
    }
    
    // 计算成本增加项
    protected function calculateIncreaseCost($item) {
        // 根据实际业务逻辑计算成本增加项
        $increase = 0;
        
        // 采购入库成本
        $increase += floatval($item['purchase_in_cost'] ?? 0);
        
        // 调拨入库成本
        $increase += floatval($item['allocation_in_cost'] ?? 0);
        
        // 委外加工入库成本
        $increase += floatval($item['outsourcing_in_cost'] ?? 0);
        
        // 加工入库成本
        $increase += floatval($item['processing_in_cost'] ?? 0);
        
        // 库存盘盈入库成本
        $increase += floatval($item['inventory_surplus_in_cost'] ?? 0);
        
        // 其他入库成本
        $increase += floatval($item['other_in_cost'] ?? 0);
        
        // 移除入库成本
        $increase += floatval($item['remove_in_cost'] ?? 0);
        
        // 规格变更入库成本
        $increase += floatval($item['change_of_standard_in_cost'] ?? 0);
        
        // 成本调整
        $increase += floatval($item['cost_adjustment'] ?? 0);
        
        return $increase;
    }
    
    // 计算成本减少项
    protected function calculateDecreaseCost($item) {
        // 根据实际业务逻辑计算成本减少项
        $decrease = 0;
        
        // 采购退回成本
        $decrease += floatval($item['purchase_return_cost'] ?? 0);
        
        // 调拨出库成本
        $decrease += floatval($item['allocation_out_cost'] ?? 0);
        
        // 委外加工出库成本
        $decrease += floatval($item['outsourcing_out_cost'] ?? 0);
        
        // 加工出库成本
        $decrease += floatval($item['processing_out_cost'] ?? 0);
        
        // FBA出库成本
        $decrease += floatval($item['fba_out_cost'] ?? 0);
        
        // FBM出库成本
        $decrease += floatval($item['fbm_out_cost'] ?? 0);
        
        // 库存盘亏出库成本
        $decrease += floatval($item['inventory_deficit_out_cost'] ?? 0);
        
        // 其他出库成本
        $decrease += floatval($item['other_out_cost'] ?? 0);
        
        // 规格变更出库成本
        $decrease += floatval($item['change_of_standard_out_cost'] ?? 0);
        
        return $decrease;
    }
    
    // 计算FBA仓库数量增加项
    protected function calculateFBAIncreaseQuantity($item) {
        $increase = 0;
        
        // 货件补货
        $increase += floatval($item['receipts_count'] ?? 0);
        
        // 买家退货
        $increase += floatval($item['customer_returns_count'] ?? 0);
        
        // 已找到
        $increase += floatval($item['found_count'] ?? 0);
        
        // 调整
        $increase += floatval($item['adjustments_count'] ?? 0);
        
        // 其他事件
        $increase += floatval($item['other_events_count'] ?? 0);
        
        return $increase;
    }
    
    // 计算FBA仓库数量减少项
    protected function calculateFBADecreaseQuantity($item) {
        $decrease = 0;
        
        // 订单发货
        $decrease += floatval($item['shipments_count'] ?? 0);
        
        // 库存移除
        $decrease += floatval($item['vendor_returns_count'] ?? 0);
        
        // 已残损
        $decrease += floatval($item['damaged_count'] ?? 0);
        
        // 丢失
        $decrease += floatval($item['lost_count'] ?? 0);
        
        // 弃置
        $decrease += floatval($item['disposed_count'] ?? 0);
        
        // 移仓转移
        $decrease += floatval($item['whse_transfers_count'] ?? 0);
        
        return $decrease;
    }
    
    // 计算FBA仓库成本增加项
    protected function calculateFBAIncreaseCost($item) {
        $increase = 0;
        
        // 货件补货成本
        $increase += floatval($item['receipts_total_amount'] ?? 0);
        
        // 买家退货成本
        $increase += floatval($item['customer_returns_total_amount'] ?? 0);
        
        // 已找到成本
        $increase += floatval($item['found_total_amount'] ?? 0);
        
        // 调整成本
        $increase += floatval($item['adjustments_total_amount'] ?? 0);
        
        // 其他事件成本
        $increase += floatval($item['other_events_total_amount'] ?? 0);
        
        return $increase;
    }
    
    // 计算FBA仓库成本减少项
    protected function calculateFBADecreaseCost($item) {
        $decrease = 0;
        
        // 订单发货成本
        $decrease += floatval($item['shipments_total_amount'] ?? 0);
        
        // 库存移除成本
        $decrease += floatval($item['vendor_returns_total_amount'] ?? 0);
        
        // 已残损成本
        $decrease += floatval($item['damaged_total_amount'] ?? 0);
        
        // 丢失成本
        $decrease += floatval($item['lost_total_amount'] ?? 0);
        
        // 弃置成本
        $decrease += floatval($item['disposed_total_amount'] ?? 0);
        
        // 移仓转移成本
        $decrease += floatval($item['whse_transfers_total_amount'] ?? 0);
        
        return $decrease;
    }
    
    // 获取统计数据列表
    public function getStatisticList($param)
    {
        $model = new warehouseStatisticModel();
        return $model->getAggregatedStatistic($param);
    }
}
