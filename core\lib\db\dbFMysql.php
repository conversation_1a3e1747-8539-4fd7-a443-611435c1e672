<?php
/**
 * Created by 漠山.
 * @purpose 描述
 * @Author: 漠山
 * @Time: 2023/12/5 22:12
 */
namespace core\lib\db;

use core\lib\config;
use core\lib\ExceptionError;
use Exception;
use \PDO;
//财务系统实例
class dbFMysql
{
    static private PDO $pdo;
    static private object|NUll $instance = null;
    static protected array $db_config = [];

    static private string $table;
    static private string $filed = '*';
    static private string $where = '';
    static private array $where_data = array();

    static private array $join = array();

    static private string $order;
    static private string $order_by_field;
    static private string $limit;
    static private array $with_arry = [];
    static private $where_in = [];
    static private $where_in_much = [];
    static private $group_by = [];
    static private $where_data_marage = [];//用于UNION ALL连表查询合并查询字段


    private function __construct()
    {
    }
    private function __clone()
    {
    }
    static public function getInstance(Array $config = [])
    {
        if (empty($config))
        {
            $config = config::all('db_financial');
            self::$db_config = $config;
        }
        if (self::$instance == null) {
            try{
                $PDOoptions  = array(
                    PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES utf8',
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_AUTOCOMMIT=>1,
                    PDO::ATTR_STRINGIFY_FETCHES => true,
                );
                $cdn = 'mysql:host='.(string)$config['host'].';dbname='.(string)$config['database'].';port='.(int)$config['port'];
                self::$pdo = new PDO($cdn, (string)$config['username'], (string)$config['pwd'],$PDOoptions);
                self::$instance = new self();
                return self::$instance;
            } catch (\Exception $e) {
                throw new \core\lib\ExceptionError('connect mysql error:'.$e->getMessage());
            }
        } else {
            self::setReset();
            return self::$instance;
        }
    }

    //原生
    public function query(string $sql, $params = []){
        $mdb = self::$pdo->prepare($sql);
        $mdb->setFetchMode(PDO::FETCH_ASSOC);
        $mdb->execute($params);
        $Ret = $mdb->fetch();
        return $Ret;
    }
    //原生
    public function queryAll(string $sql, $params = []){
        $mdb = self::$pdo->prepare($sql);
        $mdb->setFetchMode(PDO::FETCH_ASSOC);
        $mdb->execute($params);
        $Ret = $mdb->fetchAll();
        return $Ret;
    }
    public function getSql() {
        $sql = $this->build_sql('select');
        if (isset($list_num)) {
            $sql .= " limit {$list_num}";
        }
        self::$where_data_marage = array_merge(self::$where_data_marage ,self::$where_data);
        return $sql;
    }

    //返回一条数据
    public function one()
    {
        $sql = $this->build_sql('select') . " limit 1";
        $stmt = self::$pdo->prepare($sql);
        $stmt->execute(self::$where_data);
        $res = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $data = isset($res[0]) ? $res[0] : false;
        if ($data) {
            $data = $this->getWithData($data);
        }
        return $data;
    }

    //返回多条数据
    public function list($list_num = null): array
    {
        $sql = $this->build_sql('select');
        if (isset($list_num)) {
            $sql .= " limit {$list_num}";
        }
//        if (count(self::$group_by) > 0) {
//            dd(self::$where_data);
//            dd($sql);
//        }
//        if (self::$table == 'oa_f_custom_val_2023') {
//            dd($sql);
//        }




        $stmt = self::$pdo->prepare($sql);
        $stmt->execute(self::$where_data);
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        if (count(self::$with_arry) && count($data)) {
            $data = $this->getWithData($data);
        }
        return $data;
    }

    //查询数据总数
    public function count()
    {
        $sql = $this->build_sql('count');
        $stmt = self::$pdo->prepare($sql);
        $stmt->execute(self::$where_data);
        return $stmt->fetchColumn();
    }

    //分页
    public function pages($page, $page_size = 10): array
    {
        $count = $this->count();
        self::$limit = ($page - 1) * $page_size . ',' . $page_size;
        $data = $this->list();
        if (count(self::$with_arry) && count($data)) {
            $data = $this->getWithData($data);
        }
        return array('total' => $count, 'page' => $page, 'list' => $data);
    }

    //插入数据 - 单个
    public function insert($data): int
    {
        $sql = $this->build_sql('insert', $data);
        $stmt = self::$pdo->prepare($sql);
        $stmt->execute($data);
        return self::$pdo->lastInsertId();
    }
    //插入数据 - 批量
    public function insertBatch(array $keys, array $list)
    {
        $data = ['keys'=>$keys,'list'=>$list];
        $sql = $this->build_sql('insert_batch', $data);
        $stmt = self::$pdo->prepare($sql);
        $stmt->execute(self::$where_data);
        self::$pdo->lastInsertId();
    }

    //插入数据
    public function insertIgnore($data): int
    {
        $sql = $this->build_sql('insert_ignore', $data);
        $stmt = self::$pdo->prepare($sql);
        $stmt->execute($data);
        return self::$pdo->lastInsertId();
    }

    //删除数据,并返回影响行数
    public function delete(): int
    {
        if(!self::$where && !count(self::$where_in) && !count(self::$where_in_much)) {
            throw new \Exception('缺少删除条件');
        }
        $sql = $this->build_sql('delete');
        $stmt = self::$pdo->prepare($sql);
        $stmt->execute(self::$where_data);
        return $stmt->rowCount();
    }

    //更新数据,并返回影响行数
    public function update($data): int
    {
        $sql = $this->build_sql('update', $data);
        if(!self::$where && !count(self::$where_in) == 0 && !count(self::$where_in_much)) {
            throw new \Exception('修改缺少条件');
        }
        $stmt = self::$pdo->prepare($sql);
        $param = array_merge($data,self::$where_data);
        $stmt->execute($param);
        return $stmt->rowCount();
    }

    public function table($table, $alias = '')
    {
        self::setReset();
        if(isset(self::$db_config['prefix'])){
            $table = self::$db_config['prefix'].$table;
        }
        if (!empty($alias)) {
            $table .= ' as '.$alias;
        }
        self::$table = $table;
        return self::$instance;
    }
    //不拼接前缀
    public function tablep($table,$alias)
    {
        self::setResetp();
        self::$where_data = self::$where_data_marage;
        self::$where_data_marage = [];
        $table = "($table)";
        if (!empty($alias)) {
            $table .= ' as '.$alias;
        }
        self::$table = $table;
        return self::$instance;
    }

    public function exec($sql)
    {
        self::$pdo->query($sql);
        return self::$instance;
    }

    //查询字段
    public function field($field)
    {
        self::$filed = $field;
        return self::$instance;
    }
    //查询条件
    public function where($where, $where_data = [])
    {
        $where = trim($where);
        $prefix = substr($where,0,5);
        if ($prefix != 'where') {
            $where = 'where '.$where;
        }
        self::$where = $where;
        self::$where_data = $where_data;
        return self::$instance;
    }

    //查询条件and
    public function andWhere($where, $where_data = [])
    {
        if (empty(self::$where)) {
            self::$where = ' where '.$where;
        } else {
            self::$where .= ' and '.$where;
        }
        self::$where_data = array_merge(self::$where_data,$where_data);
        return self::$instance;
    }

    public function whereIn(string $key, array $array = [])
    {
        $item_where_in = [
            'key'=>$key,
            'array'=>$array
        ];
        self::$where_in[] = $item_where_in;
        return self::$instance;
    }
    //$type 1 where in;0 where not in
    public function whereInMuch(array $keys, array $array = [],string $type = '')
    {
        if (count($keys) <= 1) {
            throw new ExceptionError('sql方法使用错误');
        }
        $item_where_in = [
            'type'=>$type,
            'keys'=>$keys,
            'array'=>$array
        ];
        self::$where_in_much[] = $item_where_in;
        return self::$instance;
    }

    public function leftJoin($table, $alias ,string $on)
    {
        if(isset(self::$db_config['prefix'])){
            $table = self::$db_config['prefix'].$table;
        }
        self::$join[] = 'left join '.$table.' as '.$alias.' on '.$on;
        return self::$instance;
    }
    public function rightJoin($table, $alias ,string $on)
    {
        if(isset(self::$db_config['prefix'])){
            $table = self::$db_config['prefix'].$table;
        }
        self::$join[] = 'right join'.$table.' as '.$alias.' on '.$on;
        return self::$instance;
    }
    public function innerJoin($table, $alias ,string $on)
    {
        if(isset(self::$db_config['prefix'])){
            $table = self::$db_config['prefix'].$table;
        }
        self::$join[] = 'inner join'.$table.' as '.$alias.' on '.$on;
        return self::$instance;
    }
    public function leftJoinOut($database, $table, $alias ,string $on)
    {
        if ($database == 'db') {
            $config = config::all('db');
        } elseif($database == 'financial') {
            $config = config::all('db_financial');
        }
        $table_name = $config['database'].'.'.$config['prefix'].$table;
        self::$join[] = 'left join '.$table_name.' as '.$alias.' on '.$on;
        return self::$instance;
    }

    private function build_where(): string
    {
        //插入where in 的数据
        if (count(self::$where_in)) {
            $where_in_value = [];
            $where_in_array = [];
            $i = 0;
            $time = uniqid();
            foreach (self::$where_in as $where_in) {
                $where_in_str = '';
                $key = $where_in['key'];
                if (count($where_in['array']) == 0) {
                    $where_in_str .= "1=0";
                } else {
                    $where_in_str .= "{$key} in(";
                    $where_str = '';
                    foreach ($where_in['array'] as $wa) {
                        $where_str .= (":_{$time}_{$i},");
                        $where_in_value["_{$time}_{$i}"] = $wa;
                        $i++;
                    }
                    $where_in_str .= trim($where_str,',').')';
                }
                $where_in_array[] = $where_in_str;
            }
            $string_ = implode(' and ',$where_in_array);
            if (empty(self::$where)) {
                $string_  = 'where '.$string_;
            } else {
                $string_  = ' and '.$string_;
            }
            self::$where.= $string_;
            self::$where_data = array_merge(self::$where_data,$where_in_value);
        }
        //多列in查询
        if (count(self::$where_in_much)) {
            $where_in_m_value = [];
            $where_in_m_array = [];
            $i = 0;
            $time = uniqid();
            foreach (self::$where_in_much as $where_in) {
                $where_in_str = '';
                $keys = $where_in['keys'];
                $key = '('.implode(',',$keys).')';
                if (count($where_in['array']) == 0) {
                    $where_in_str .= "1=0";
                } else {
                    if (!empty($where_in['type'])) {
                        $where_in_str .= "{$key} not in(";
                    } else {
                        $where_in_str .= "{$key} in(";
                    }
                    $where_str = '';
                    foreach ($where_in['array'] as $wa) {
                        $w_m_sql = [];
                        foreach ($keys as $j=>$key_i) {
                            $kkk_str = ":_{$time}_{$i}";
                            $w_m_sql[] = $kkk_str;
                            $where_in_m_value[$kkk_str] = $wa[$j];
                            $i++;
                        }
                        $where_str .= '('.implode(',',$w_m_sql).'),';
                    }
                    $where_in_str .= trim($where_str,',').')';
                }
                $where_in_m_array[] = $where_in_str;
            }
            $string_ = implode(' and ',$where_in_m_array);
            if (empty(self::$where)) {
                $string_  = 'where '.$string_;
            } else {
                $string_  = ' and '.$string_;
            }
            self::$where.= $string_;
            self::$where_data = array_merge(self::$where_data,$where_in_m_value);
        }
        return self::$where;
    }
    private function build_join(): string
    {
        $join_string = '';
        if (count(self::$join)) {
            $join_string = implode(' ',self::$join);
        }
        return $join_string;
    }
    private function build_sql($type, $data = null): string
    {
        $sql = '';
        if ($type == 'select') {
            $join = $this->build_join();
            $where = $this->build_where();
            $sql = "select ".self::$filed." from ".self::$table." {$join} {$where}";
            if (count(self::$group_by) > 0) {
                $sql .= " group by ".implode(',',self::$group_by);
            }
            if (!empty(self::$order)) {
                $sql .= " order by ".self::$order;
            }
            if (!empty(self::$order_by_field)) {
                $sql .= " ORDER BY FIELD(".self::$order_by_field.')';
            }

            if (!empty(self::$limit)) {
                $sql .= " limit ".self::$limit;
            }
        }
        if ($type == 'insert_batch') {
            $where_data = [];
            $sql_array = [];
            $keys = $data['keys'];
            $list = $data['list'];
            foreach ($list as $key_ => $values) {
                $index_str = '_'.$key_;
                //字段
                $result = array_map(function($element) use ($index_str) {
                    return $element . $index_str;
                }, $keys);
                $sql = '(:'.implode(',:',$result).')';
                $sql_array[] = $sql;
                //字段值
                $array_data = [];
                foreach ($values as $k=>$value) {
                    $array_data[$result[$k]] = $value;
                }
                $where_data = array_merge($where_data,$array_data);
            }
            self::$where_data = $where_data;
            $sql = "insert into ".self::$table." (".implode(',',$keys).") values ".implode(',',$sql_array);
        }
        if ($type == 'insert') {
            $k = '';
            $v = '';
            foreach ($data as $key => $value) {
                $k .= $key . ',';
                $v .= ':'.trim($key,'`'). ',';
            }
            $k = rtrim($k, ',');
            $v = rtrim($v, ',');
            $sql = "insert into ".self::$table." ($k) value ($v) ";
        }
        if ($type == 'insert_ignore') {
            $k = '';
            $v = '';
            foreach ($data as $key => $value) {
                $k .= $key . ',';
                $v .= ':'.trim($key,'`'). ',';
            }
            $k = rtrim($k, ',');
            $v = rtrim($v, ',');
            $sql = "INSERT IGNORE INTO ".self::$table." ($k) value ($v) ";
        }
        if ($type == 'delete') {
            $where = $this->build_where();
            $sql = "delete from ".self::$table." {$where}";
        }
        if ($type == 'update') {
            $where = $this->build_where();
            $set = '';
            foreach ($data as $key => $value) {
                $value = is_string($value) ? "'" . $value . "'" : $value;
                //$set .= "{$key}={$value},";
                $set .= "{$key}=:{$key},";
            }
            $set = rtrim($set, ',');
            $set = $set ? "set {$set}" : $set;
            $sql = "update ".self::$table." {$set} {$where}";
        }
        if ($type == 'count') {
            $where = $this->build_where();
            $join = $this->build_join();
            if (count(self::$group_by) == 0) {
                $sql = "select count(*) from ".self::$table." {$join} {$where}";
            } else {
                $sql = "select count(DISTINCT ".implode(',',self::$group_by).") from ".self::$table." {$join} {$where}";
            }
        }
        //echo $sql;exit();
        return $sql;
    }
    //查询结果排序
    public function order($order)
    {
        self::$order = $order;
        return self::$instance;
    }
    //wherein排序
    public function orderByField($key_,array $order_array) {
        if (count($order_array)) {
            $string_v = [];
            $string_w[] = $key_;
            foreach ($order_array as $k=>$v) {
                $key = ':abf_val'.'_'.$k;
                $string_w[] = $key;
                $string_v[$key] = $v;
            }
            self::$where_data = array_merge(self::$where_data,$string_v);
            self::$order_by_field = implode(',',$string_w);
        }
        return self::$instance;
    }
    //group
    public function groupBy(array $keys) {
        self::$group_by = array_merge(self::$group_by,$keys);
        return self::$instance;
    }

    public function errorInfo() {
        return self::$pdo->errorInfo();
    }

    public function beginTransaction(){
        self::$pdo->beginTransaction();
        return self::$instance;
    }
    public function rollBack(){
        self::$pdo->rollBack();
        return self::$instance;
    }
    public function commit(){
        self::$pdo->commit();
        return self::$instance;
    }

    //不建议用
    public function with(string $table, array $keys, string $field = '*', string $where='', string $array_name = '',array $join=[]){
        if (count($keys)) {
            self::$with_arry[] = [
                'table'=>$table,'field'=>$field,'keys'=>$keys,
                'where'=>$where,'array_name'=>$array_name,'join'=>$join
            ];
        }
    }

    //目前是循环查表，先用着，后续修改为其他
    public function getWithData($data) {
        foreach ($data as &$v_) {
            foreach (self::$with_arry as $w) {
                //table明
                $table_name = $w['table'];
                if(isset(self::$db_config['prefix'])){
                    $table_name = self::$db_config['prefix'].$table_name;
                }
                $sql = 'select '.$w['field'].' from '.$table_name;
                if (count($w['join'])) {
                    $join_string = ' ';
                    foreach ($w['join'] as $j) {
                        $join_string .= $j['join'].' '.self::$db_config['prefix'].$j['table'].' on '.$j['on'];
                    }
                    $sql .= $join_string;
                }
                //条件
                if ($w['where']) {
                    $sql .= (' where '.$w['where']);
                } else {
                    $sql .= ' where 1=1';
                }

                $where_data = [];
                foreach ($w['keys'] as $k=>$where) {
                    $sql.= ' and '.$where[0]."=:on_{$k}";
                    $where_data["on_{$k}"] = $v_[$where[1]];
                }
                //dd($sql);
                $rows = $this->queryAll($sql,$where_data);
                if (empty($w['array_name'])) {
                    $v_[$w['table']] = $rows;
                } else {
                    $v_[$w['array_name']] = $rows;
                }

            }
        }
//        die;
        return $data;
    }

    private static function setReset() {
        self::$table = '';
        self::$filed = '*';
        self::$where = '';
        self::$where_data = [];
        self::$join = [];
        self::$order = '';
        self::$limit = '';
        self::$with_arry = [];
        self::$where_in = [];
        self::$where_in_much = [];
        self::$group_by = [];
        self::$order_by_field = '';
    }
    private static function setResetp() {
        self::$table = '';
        self::$filed = '*';
        self::$where = '';
        self::$join = [];
        self::$order = '';
        self::$limit = '';
        self::$with_arry = [];
        self::$where_in = [];
        self::$where_in_much = [];
        self::$group_by = [];
        self::$order_by_field = '';
    }
}