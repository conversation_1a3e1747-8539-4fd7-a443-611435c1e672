<?php
// 启用错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "开始基本测试...\n";

try {
    // 设置路径
    define('SELF_FK', realpath(__DIR__ . '/../../..'));
    define('CORE', SELF_FK . '/core');
    
    echo "SELF_FK: " . SELF_FK . "\n";
    echo "CORE: " . CORE . "\n";
    
    // 检查并加载环境文件
    $envFile = SELF_FK . '/environment.php';
    if (!file_exists($envFile)) {
        throw new Exception("环境文件不存在: $envFile");
    }
    
    require_once $envFile;
    echo "✓ 环境文件加载完成\n";
    
    // 加载核心文件
    $coreFiles = [
        CORE . '/common/function.php',
        CORE . '/common/return.php', 
        CORE . '/lib/config.php',
        CORE . '/lib/db/dbLMysql.php'
    ];
    
    foreach ($coreFiles as $file) {
        if (file_exists($file)) {
            require_once $file;
            echo "✓ 加载: " . basename($file) . "\n";
        } else {
            echo "✗ 文件不存在: $file\n";
        }
    }
    
    // 加载模型
    $modelFile = __DIR__ . '/../models/festivalActivitiesModel.php';
    if (!file_exists($modelFile)) {
        throw new Exception("模型文件不存在: $modelFile");
    }
    
    require_once $modelFile;
    echo "✓ 模型文件加载完成\n";
    
    // 实例化模型
    $model = new plugins\logistics\models\festivalActivitiesModel();
    echo "✓ 模型实例化成功\n";
    
    // 测试数据库连接
    $result = $model->getList(['limit' => 1]);
    echo "✓ 数据库测试成功，结果类型: " . gettype($result) . "\n";
    
    if (is_array($result) && isset($result['total'])) {
        echo "✓ 返回数据格式正确，总数: " . $result['total'] . "\n";
    }
    
    echo "基本测试完成！\n";
    
} catch (Exception $e) {
    echo "✗ 错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
