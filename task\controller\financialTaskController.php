<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/8 11:22
 */

namespace task\controller;

//一些需求要定时更新但不需要身份验证的接口
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use core\lib\rediskeys;
use financial\form\customColumnForm;
use financial\form\goodsLevelForm;
use financial\form\goodsWaringCountForm;
use financial\form\messagesFrom;
use financial\form\runShellTaskForm;

class financialTaskController
{
    public function __construct()
    {
        $api_token = $_POST['token'] ?? '';
        if ($api_token != '576d9908a51f11ee97f19c2dcd695fd0') {
            returnError('验证失败');
        }
        set_time_limit(0);
    }
    //更新自定义字段的值
    public function recalculateVal(){
        $month = $_POST['month']??0;
        if (!$month) {
            returnSuccess('','请选择月份');
        }
        if (customColumnForm::recalculate(1,[],$month)) {
            if (!empty(customColumnForm::$error_msg)) {
                returnError(customColumnForm::$error_msg);
            } else {
                returnSuccess('','计算中...');
            }
        } else {
            returnSuccess('','未找到可计算的字段');
        }
    }
    //计算商品预警
    public function waringRulesCount() {
        $m_date = $_POST['m_date']??date('Y-m');
        if (!$m_date) {
            returnSuccess($m_date.'请选择时间');
        }
        //生成预警商品数据
        $redis_key = 'oa_waring_rules_count';
        $redis = (new \core\lib\predisV())::$client;
        $oa_count_msku_month_key = rediskeys::$oa_count_msku_month;
        if (!$redis->exists($oa_count_msku_month_key)) {
            SetReturn(2,$m_date.'结账已完成');
        }
        $db = dbFMysql::getInstance();
        if ($redis->get($redis_key)) {
            $ids = json_decode($redis->get($redis_key));
            $redis->expire($redis_key,1*60*60);
        } else {
            $start_time = strtotime($m_date);
            $list = $db->table('waring_rules')
                ->where('where begin_time = :m_date and status = 1 and is_delete = 0',['m_date'=>$start_time])
                ->field('id')
                ->list();
            if (count($list)) {
                $ids = array_column($list,'id');
                //删除该月预警
                $db->table('goods_waring')
                    ->where('where m_date=:m_date',['m_date'=>$m_date])
                    ->delete();
            } else {
                $ids = [];
            }
        }
        if (!count($ids)) {
            //更新账单为已结账
            $db->table('checkout')
                ->where('where m_date = :m_date', ['m_date' => $m_date])
                ->update(['is_lock' => 1]);
            //删除预警
            $redis->del($redis_key);
            //删除结账后的操作信息
            $redis->del(rediskeys::$oa_count_msku_month);
            return SetReturn(2,$m_date.'预警规则匹配完成，无预警规则');
        }
        $id = $ids[0];
        goodsWaringCountForm::setWaringGoods($id,$m_date);
        unset($ids[0]);
        if (count($ids)) {
            $ids = array_values($ids);
            $ids_str = json_encode($ids);
            $redis->set($redis_key,$ids_str);
            $redis->expire($redis_key,1*60*60);
            return returnSuccess('',$m_date."id={$id}更新完成,还剩{$ids_str}未更新");
        } else {
            //更新账单为已结账
            $db->table('checkout')
                ->where('where m_date = :m_date', ['m_date' => $m_date])
                ->update(['is_lock' => 1]);
            //删除预警
            $redis->del($redis_key);
            //删除结账后的操作信息
            $redis->del(rediskeys::$oa_count_msku_month);
            self::sendWaringMsg($m_date);
            return SetReturn(2,$m_date.'预警规则匹配完成');
        }
    }
    //预警规则计算完就发消息
    private function sendWaringMsg($m_date) {
        $db = dbFMysql::getInstance();
        $goods_waring =  $db->table('goods_waring_check')
            ->where('where m_date=:m_date and status=1 ',['m_date'=>$m_date])
            ->list();
        if (count($goods_waring)) {
            $user_ids = [];
            //按人数和数量整理
            foreach ($goods_waring as $v) {
                $yunying_ids = json_decode($v['yunying_id']);
                $zuzhang_ids = json_decode($v['zuzhang_id']);
                $receive_type = json_decode($v['receive_type']);
                if (count($receive_type) == 2) {
                    $user_ids = array_merge($user_ids,$yunying_ids,$zuzhang_ids);
                } else {
                    if ($receive_type[0] == 1) {
                        $user_ids = array_merge($user_ids,$yunying_ids);
                    } else {
                        $user_ids = array_merge($user_ids,$zuzhang_ids);
                    }
                }
            }
            $new_list = [];
            foreach ($user_ids as $user_id) {
                if (isset($new_list[$user_id])) {
                    $new_list[$user_id]++;
                } else {
                    $new_list[$user_id] = 1;
                }
            }
            $user_ids = array_unique($user_ids);
            //用户数查询
            $db_ = dbMysql::getInstance();
            $user_list = $db_->table('qwuser')
                ->whereIn('id',$user_ids)
                ->field('id,wid')
                ->list();
            $user_ = [];
            foreach ($user_list as $user_item) {
                $user_['id'] = $user_item['wid'];
            }
            //消息发送
            foreach ($new_list as $user_id=>$waring_count) {
                if (isset($user_[$user_id])) {
                    messagesFrom::senMeg($user_[$user_id],11,"您当前有{$waring_count}个指标预警待处理，请尽快处理！",0);
                }
            }
        }

    }
    //根据等级规则修改商品等级
    public static function updateGoodsLevel() {
        $m_date = $_POST['m_date']??'';
        if (!$m_date) {
            returnSuccess('请选择时间');
        }
        $redis_key = rediskeys::$oa_updateGoodsLeve;
        $redis = (new \core\lib\predisV())::$client;
        $oa_count_msku_month_key = rediskeys::$oa_count_msku_month;
        if (!$redis->exists($oa_count_msku_month_key)) {
            SetReturn(2,$m_date.'结账已完成');
        }
        if ($redis->get($redis_key)) {
            $ids = json_decode($redis->get($redis_key));
            $redis->expire($redis_key,1*60*60);
        } else {
            $db = dbFMysql::getInstance();
            $start_time = strtotime($m_date);
            $data = $db->table('goods_level')
                ->where('where is_delete = 0 and status = 1 and start_time=:m_date',['m_date'=>$start_time])
                ->field('id')
                ->list();
            if (!count($data)) {
                runShellTaskForm::setWaringGoods();
                return SetReturn(2,$m_date.'更新完成');
            }
            $ids = array_column($data,'id');
            //废除这个月的说有等级
            $db->table('goods_level_relation')
                ->where('where m_date = :m_date',['m_date'=>$m_date])
                ->update(['is_delete'=>1]);
        }
        $id = $ids[0];
        goodsLevelForm::updateGoodsLevel($id,$m_date);
        unset($ids[0]);
        if (count($ids)) {
            $ids = array_values($ids);
            $ids_str = json_encode($ids);
            $redis->set($redis_key,$ids_str);
            $redis->expire($redis_key,1*60*60);
            return returnSuccess('',$m_date."id={$id}更新完成,还剩{$ids_str}未更新");
        } else {
            $redis->del($redis_key);
            //更新预警数据
            runShellTaskForm::setWaringGoods();
            return SetReturn(2,$m_date.'更新完成');
        }

    }
}