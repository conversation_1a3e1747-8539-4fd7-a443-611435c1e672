<?php

namespace plugins\shop\controller;

use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use Exception;
use plugins\shop\models\emailModel;
use plugins\shop\models\relationModel;
use Rap2hpoutre\FastExcel\FastExcel;

class emailController extends baseController
{
    // 列表
    public function getList()
    {
        $paras_list = ['email_account', 'email_assistant_email', 'email_safe_phone', 'phone_manager', 'email_user', 'use_status',
         'use_platform', 'email_usage', 'relations', 'register_date', 'update_time', 'page', 'page_size'];
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $param['page'] = $param['page'] ?? 1;
        $param['page_size'] = $param['page_size'] ?? 10;

        $model = new emailModel();
        $list = $model->getList($param);

        $ids = array_column($list['list'], 'id');
        $relation =  (new relationModel())->getReverseRelation('email', $ids);
        foreach ($list['list'] as &$item) {
            $item['relations'] = $relation[$item['id']] ?? [];
        }

        returnSuccess($list);
    }

    // 新增
    public static function add()
    {
        $model = new emailModel();
        $param = array_intersect_key($_POST, array_flip(array_keys(emailModel::$paras_list)));
        $id = $_POST['id'] ?? null;

        try {
            $model->dataValidCheck($param);
        } catch (Exception $e) {
            returnError($e->getMessage());
        }

        // 唯一性检验
        $detail = $model->getByEmailAccount($param['email_account'], $id);
        if ($detail) {
            returnError('邮箱已存在');
        }

        if ($id) {
            // 验证数据正确性
            $detail = $model->getById($id);
            if (!$detail) {
                returnError('数据不存在');
            }
            $model->edit($param, $id, $detail);
            returnSuccess([], '编辑成功');
        } else {
            $model->add($param);
            returnSuccess([], '添加成功');
        }
    }

    // 详情
    public static function detail()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $model = new emailModel();
        $detail = $model->getById($id);
        $detail = $model->formatItem($detail);

        $relation =  (new relationModel())->getReverseRelation('email', [$id]);
        $detail['relations'] = $relation[$item['id']] ?? [];

        returnSuccess($detail);
    }

    // 获取日志
    public static function getLog()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $db = dbShopMysql::getInstance();
        $list = $db->table('operation_log')->where('where table_name = :table_name and table_id = :table_id', 
        ['table_name' => 'email', 'table_id' => $id])->order('id desc')->list();

        $users =  redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        $model = new emailModel();
        $maps = $model->getMaps();

        foreach ($list as &$item) {
            $item['operator_name'] = $users[$item['operator']] ?? '';
            $attach = json_decode($item['attach'], true);
            $item['before'] = $attach['before'] ?? null;
            $item['before'] = $model->formatItem($item['before'], $maps);
            $item['after'] = $attach['after'] ?? null;
            $item['after'] = $model->formatItem($item['after'], $maps);
            unset($item['attach']);
        }
        returnSuccess($list);
    }

    // 批量导入
    public function import()
    {
        $paras_list = array('excel_src');
        $request_list = ['excel_src' => '表格链接'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $excel_url = SELF_FK . $param['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格不存在');
        }

        $data = (new FastExcel())->import($excel_url, function ($row) {
            // 将每一项 trim 去空格，再判断整行是否为空
            $cleaned = collect($row)->map(function ($value) {
                if (is_string($value)) {
                    return trim((string)$value);
                }
                return $value;
            });
            // 全部为空则跳过
            if ($cleaned->filter()->isEmpty()) {
                return null;
            }
            return $cleaned->toArray(); // 返回处理后的数据
        });

        $first = $data[0];
        if (empty($first['注册时间']) || empty($first['邮箱账号']) || empty($first['邮箱密码']) ||
            empty($first['邮箱辅助邮箱']) || empty($first['邮箱安全手机']) || empty($first['手机号保管人']) ||
            empty($first['邮箱使用人']) || !isset($first['用途']) || !isset($first['使用平台']) || !isset($first['使用状态']) ||
            !isset($first['备注'])) {
            returnError('表头错误');
        }

        $phone_card = redisCached::getPhoneCard();
        $phone_manager = array_column($phone_card, 'phone_manager', 'id');
        $phone_card = array_column($phone_card, 'id', 'phone_number');
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_id', 'user_name');


        $model = new emailModel();
        $paras_list = $model::$paras_list;
        unset($paras_list['email_usage']);
        unset($paras_list['use_platform']);
        unset($paras_list['remark']);
        $import_data = [];
        $error_data = [];
        foreach ($data as $row) {
            $error_msg = [];
            empty($row['注册时间']) && $error_msg[] = '注册时间不能为空';
            empty($row['邮箱账号']) && $error_msg[] = '邮箱账号不能为空';
            // 唯一性检验
            $detail = $model->getByEmailAccount($row['邮箱账号']);
            if ($detail) {
                $error_msg[] = '邮箱账号已存在';
            }
            try {
                $register_date = $row['注册时间']->format('Y-m-d');
                if (empty($row['注册时间']) || strtotime($register_date) === false) {
                    $error_msg[] = '注册时间格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '注册时间格式错误';
            }
            empty($row['邮箱密码']) && $error_msg[] = '邮箱密码不能为空';
            empty($row['邮箱辅助邮箱']) && $error_msg[] = '邮箱辅助邮箱不能为空';
            empty($row['邮箱安全手机']) && $error_msg[] = '邮箱安全手机不能为空';
            if (!isset($phone_card[$row['邮箱安全手机']])) {
                $error_msg[] = '邮箱安全手机不存在';
            } else {
                $email_safe_phone_id = $phone_card[$row['邮箱安全手机']];
                if ($row['手机号保管人'] != $phone_manager[$email_safe_phone_id]) {
                    $error_msg[] = '邮箱安全手机保管人不一致';
                }
            }
            empty($row['邮箱使用人']) && $error_msg[] = '邮箱使用人不能为空';
            if (!empty($row['用途'])) {
                $email_usage = explode(';', $row['用途']);
            }
            if (!empty($row['使用平台'])) {
                $use_platform = explode(';', $row['使用平台']);
            }

            $item_data = [
                'email_account' => $row['邮箱账号'],
                'email_password' => $row['邮箱密码'],
                'email_assistant_email' => $row['邮箱辅助邮箱'],
                'email_safe_phone_id' => $email_safe_phone_id ?? 0,
                'email_user' => $row['邮箱使用人'] ?? '',
                'email_usage' => $email_usage ?? [],
                'use_platform' => $use_platform ?? [],
                'use_status' => $row['使用状态'] ?? '',
                'register_date' => $register_date ?? null,
                'remark' => $row['备注'] ?? '',
            ];
            $check_row = $model->dataValidCheck($item_data, $paras_list, false);
            if (!empty($check_row['error'])) {
                $error_msg = array_merge($error_msg, $check_row['error']);
            }

            if (!empty($error_msg)) {
                $row['失败原因'] = implode(';', $error_msg);
                $error_data[] = $row;
                continue;
            }

            $import_data[] = $item_data;
            $id = $model->add($item_data, '导入');
        }

        if (count($error_data) > 0) {
            // 使用项目根目录动态构建导出文件路径
            $projectRoot = SELF_FK;
            $src = '/public/shop/temp/error/user_import_errors' . time() . rand(100, 1000) . '.xlsx';
            $exportPath = $projectRoot . $src;

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径

            returnSuccess(['error_file' => $src, 'error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $error_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $import_data], '导入成功');
        }

    }

    // 导出
    public function export()
    {
        $paras_list = ['email_account', 'email_assistant_email', 'email_safe_phone', 'phone_manager', 'email_user', 'use_status',
            'use_platform', 'email_usage', 'relations', 'register_date', 'update_time', 'keys', 'ids'];
        $param = array_intersect_key($_POST, array_flip($paras_list));

        $model = new emailModel();
        $data = $model->getList($param, 'id desc', true);

        if (empty($data)) {
            returnError('没有数据可导出');
        }

        $export_data = [];

        foreach ($data as $item) {
            $keys = [
                '注册时间', '邮箱账号', '邮箱密码', '邮箱辅助邮箱', '邮箱安全手机', '手机号保管人', '邮箱使用人', '用途', '使用平台', '使用状态', '备注'
            ];

            $sortedItem = [];
            foreach ($keys as $key) {
                if (!empty($param['keys']) && !in_array($key, $param['keys'])) continue;
                if ($key == '用途') {
                    $sortedItem[$key] = is_array($item['用途']) ? implode(';', $item['用途']) : $item['用途'];
                } elseif ($key == '使用平台') {
                    $sortedItem[$key] = implode(';', $item['使用平台']);
                } else {
                    $sortedItem[$key] = $item[$key] ?? '';
                }
            }
            $export_data[] = $sortedItem;
        }


        $filePath = '/public/shop/temp/email_' . date('YmdHis') . rand(1,1000).  '.xlsx';
        (new FastExcel($export_data))->export(SELF_FK . $filePath);

        returnSuccess(['src' => $filePath], '导出成功');
    }

    // 批量编辑导入（增量更新）
    public static function editBatch()
    {
        $paras_list = array('data');
        $request_list = ['data' => '编辑数据'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

        if (count($param['data']) > 500) {
            returnError('批量编辑数据不能超过500条');
        }

        $emails = array_column($param['data'], 'email_account');
        $emails = array_unique($emails);
        if (count($emails) != count($param['data'])) {
            returnError('批量编辑数据不能有重复的邮箱账号');
        }

        $model = new emailModel();
        $result = $model->editBatch($param['data']);

        returnSuccess($result);
    }

}
