<?php
/**
 * @author: zhangguoming
 * @Time: 2024/3/27 9:02
 */

namespace plugins\goods\models;

use core\lib\db\dbMysql;

class roleModel {

    private static mixed $role_list = '';
    //用户列表获取用户角色
    public static function getUserListRole($list) {
        if(count($list)) {
            if (!empty(self::$role_list)) {
                $role_list = self::$role_list;
            } else {
                $db = dbMysql::getInstance();
                $user_ids = array_column($list,'id');
                $role_list = $db->table('user_roles','a')
                    ->leftJoin('role','b','b.id = a.role_id')
                    ->field('b.*,a.qwuser_id')
                    ->whereIn('a.qwuser_id',$user_ids)
                    ->list();
                self::$role_list = $role_list;
            }
            if ($role_list) {
                foreach ($list as &$v) {
                    $roles = [];
                    foreach ($role_list as $role) {
                        if ($v['id'] == $role['qwuser_id']) {
                            $roles[] = ['id'=>$role['id'],'role_name'=>$role['role_name']];
                        }
                    }
                    $v['roles'] = $roles;
                }
            }
            else {
                foreach ($list as &$v) {
                    $v['roles'] = [];
                }
            }
        }
        return $list;
    }
}