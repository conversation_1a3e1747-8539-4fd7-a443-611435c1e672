<?php

namespace financial\controller;

use core\lib\db\dbFMysql;

class supplierController
{
    // 获取供应商列表或根据名字查询供应商
    public static function getvendor()
    {
        try {
            // 定义所需参数列表
            $paras_list = array('vendorname', 'page', 'page_size');
            $request_list = ['page' => '页码', 'page_size' => '记录数'];

            // 组织参数
            $param = arrangeParam($_POST, $paras_list, $request_list);

            // 设置默认分页参数
            $page = !empty($param['page']) ? $param['page'] : 1;
            $page_size = !empty($param['page_size']) ? $param['page_size'] : 10;

            // 获取数据库实例
            $db = dbFMysql::getInstance();

            // 初始化返回数据
            $response_data = [
                'list' => [],
                'page' => $page,
                'page_size' => $page_size,
                'total' => 0
            ];

            // 判断是否传入供应商名称
            if (!empty($param['vendorname'])) {
                // 根据供应商名称查询供应商
                $vendorname = $param['vendorname'];
                $vendor_list = $db->table('supplier')
                    ->where('where is_delete = 0 AND supplier_name LIKE :supplier_name', ['supplier_name' => '%' . $vendorname . '%'])
                    ->order('id DESC')
                    ->pages($page, $page_size); // 添加分页
                returnSuccess($vendor_list);
                // 如果查询结果为空
                if (empty($vendor_list)) {
                    returnError("未找到名为 $vendorname 的供应商");
                }

                // 返回成功信息和供应商数据
                returnSuccess($vendor_list, "获取供应商成功");
            } else {
                // 获取所有供应商数据
                $suppliers = $db->table('supplier')
                    ->where('where is_delete = 0')
                    ->order('id DESC')
                    ->pages($page, $page_size); // 添加分页

                // 返回成功信息和所有供应商数据
                returnSuccess($suppliers, "获取供应商列表成功");
            }
        } catch (\Exception $e) {
            returnError("获取供应商失败: " . $e->getMessage());
        }
    }

    // 配置供应商信息
    public static function cfgvendor()
    {
        try {
            // 定义所需参数列表
            $paras_list = array('id', 'name', 'remark');
            $request_list = ['id' => '供应商ID'];

            // 组织参数
            $param = arrangeParam($_POST, $paras_list, $request_list);

            // 获取数据库实例
            $db = dbFMysql::getInstance();

            // 检查供应商名称是否重复
            $existingVendor = $db->table('supplier')
                ->where('where supplier_name = :name AND id <> :id', ['name' => $param['name'], 'id' => $param['id']])
                ->one();

            if ($existingVendor) {
                returnError('供应商名称已存在');
            }
            if($existingVendor > 0 && $param['remark']){
                returnError('领星拉取数据不可修改备注');
            }

            // 更新供应商的信息
            $db->table('supplier')
                ->where('where id = :id', ['id' => $param['id']])
                ->update([
                    'supplier_name' => $param['name'],
                    'remark' => $param['remark'],
                    'updated_time' => date('Y-m-d H:i:s')
                ]);

            returnSuccess([], "配置供应商信息成功");
        } catch (\Exception $e) {
            returnError("配置供应商信息失败: " . $e->getMessage());
        }
    }

    //新增供应商信息
    public static function addvendor()
    {
        // 定义所需参数列表
        $paras_list = array('name', 'remark');
        $request_list = ['name' => '供应商名称'];

        // 组织参数
        $param = arrangeParam($_POST, $paras_list, $request_list);

        // 获取数据库实例
        $db = dbFMysql::getInstance();

        // 检查供应商名称是否重复
        $existingVendor = $db->table('supplier')
            ->where('where supplier_name = :name', ['name' => $param['name']])
            ->one();

        if ($existingVendor) {
            returnError('供应商名称已存在');
        }

        // 新增供应商信息
        $insert = $db->table('supplier')
            ->insert(
                [
                    'supplier_name' => $param['name'],
                    'remark' => $param['remark'],
                    'created_time' => date('Y-m-d H:i:s'),
                    'updated_time' => date('Y-m-d H:i:s')

                ]
            );

        returnSuccess([], "新增供应商信息成功");
    }

    //删除
    public static function delvendor()
    {
        // 定义所需参数列表
        $paras_list = array('id');
        $request_list = ['id' => '供应商id'];
        // 组织参数
        $param = arrangeParam($_POST, $paras_list, $request_list);
        // 获取数据库实例
        $db = dbFMysql::getInstance();
        // 获取供应商信息
        $vendor = $db->table('supplier')
            ->where('where id = :id', ['id' => $param['id']])
            ->one();
        if (!$vendor) {
            returnError('供应商不存在');
        }
        // 检查供应商的 lingxing_id 是否存在于 goods 表中的 lingxing_id
        if ($vendor['lx_supplier_id'] != 0) {
            returnError('领星获取的供应商不可删除');
        }
        // 检查供应商是否已关联产品
        $relatedProducts = $db->table('goods')
            ->where('where supplier_id = :id', ['id' => $param['id']])
            ->one();
        if ($relatedProducts) {
            returnError('该供应商已关联产品，不可删除');
        }
        // 删除供应商
        $db->table('supplier')
            ->where('where id = :id', ['id' => $param['id']])
            ->update(['is_delete' => 1]);
        returnSuccess('','供应商删除成功');


    }

}
