<?php
/**
 * @author: zhangguoming
 * @Time: 2024/3/27 11:25
 */

namespace plugins\salary\models;

use plugins\assessment\models\customCrontabModel;
use core\lib\db\dbMysql;
use core\lib\db\dbSMysql;

class userInfoModel
{
    public static function checkInitSalary($data) {
        empty($data['work_place']) && returnError('归属地不能为空');
        empty($data['id_number']) && returnError('身份证号不能为空');
        empty($data['user_type']) && returnError('员工类型不能为空');
        empty($data['hire_date']) && returnError('入职日期不能为空');
//        empty($data['regularization_date']) && returnError('转正日期不能为空');
        empty($data['probation']) && returnError('试用期不能为空');
        empty($data['probation_unit']) && returnError('试用期单位不能为空');
        empty($data['corp_id']) && returnError('公司不能为空');
        empty($data['bank_card_owner']) && returnError('银行卡归属人不能为空');
        empty($data['bank_card']) && returnError('银行卡号不能为空');
        empty($data['bank_name']) && returnError('开户行不能为空');

        $trial_period_salary = json_decode($data['trial_period_salary'], true);
        empty($trial_period_salary)  && returnError('试用期工资不能为空');
        $salary = json_decode($data['salary'], true);
//        empty($salary)  && returnError('转正工资不能为空');

        $social_insurance = json_decode($data['social_insurance'], true);
        empty($social_insurance)  && returnError('社保不能为空');
        $social_insurance['status'] == 0 && !isset($social_insurance['is_allowance']) && returnError('发放社保补贴不能为空');

        $housing_fund = json_decode($data['housing_fund'], true);
        empty($housing_fund)  && returnError('公积金不能为空');
        $housing_fund['status'] == 0 && !isset($housing_fund['is_allowance']) && returnError('发放公积金补贴不能为空');
    }

    public static function changeUserStatus($crontab)
    {
        $id = $crontab['link_id'];
        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();

        $record = $sdb->table('user_salary_change_record')->where('id = :id', ['id' => $id])->one();
        if (empty($record) || $record['is_cancel'] == 1) {
            // 将定时任务状态改为已完成
            customCrontabModel::finishCrontab($crontab['id']);
            return false;
        }

        $user_status = null;
        switch ($record['change_reason']) {
            case 2: // 试用期转正
            case 3: // 实习期转正
                $user_status = 1;
                break;
            case 8: // 实习转试用期
                $user_status = 2;
                break;
        }
        if (!empty($user_status)) {
            $db->table('user_info')->where('id = :id', ['id' => $record['qwuser_id']])->update(['user_status' => $user_status]);
        }
        customCrontabModel::finishCrontab($crontab['id']);
        return true;
    }
}