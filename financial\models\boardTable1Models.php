<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/16 15:56
 */

namespace financial\models;

use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use financial\common\boardBase;
use financial\form\coustomColumnJobForm;

class boardTable1Models extends boardBase
{
    public static array $param;
    public static string $real_table = '';//真实的表类型（有效表用的其他数据，表类型就是其他类型）
    public static array $years; //查询年数据
    public static array $search_month; //搜索时间段
    public static array $aggregation_keys = []; //去除合并字段(搜索传的字段)
    public static string $table; //当前查询的报表表单
    public static array $currency_keys;//货币字段（计算时要按汇率转）
    public static array $need_again_oa_keys1 = ['oa_key_4'];//毛利润相关字段(跟毛利润相关的数字，其他跟毛利润不相关且不为百分比的完全不用重新计算，统计就行);
    public static int $page = 1;
    public static int $page_size = 10;
    public function __construct($table)
    {
        self::$table = $table;
        $paras_list = array('country_code','currency_code','project_ids','yunying_ids','date_time','category_ids',"search_type","search_value",'aggregation_keys','order_by_key','order_by_type','is_new','page','page_size','hidden_detail','waring_id','waring_status','supplier_name');
        $request_data = ['date_time'=>'时间区间'];
        $param = arrangeParam($_POST, $paras_list, $request_data);
        if (empty($param['search_type']) || empty($param['search_value'])) {
            $param['search_value'] = '';
            $param['search_type'] = '';
        }
        $param['date_time'] = json_decode($param['date_time']);
        $param['aggregation_keys'] = empty($param['aggregation_keys'])?'[]':$param['aggregation_keys'];
        self::$aggregation_keys = json_decode($param['aggregation_keys']);
        self::$page = empty($param['page'])?1:$param['page'];
        self::$page_size = empty($param['page_size'])?1:$param['page_size'];
        if (!count($param['date_time'])) {
            returnError('请选择时间区间');
        }
        //月份
        $search_month = [];
        //毛利润报表数据要获取近三年数据
        $month = $param['date_time'][0];
        if (self::$real_table == 'month_total') {
            $month = date('Y-m',strtotime($month.'-01 -1 month'));
            $param['date_time'][0] = $month;
        }
        while (1) {
            if (strtotime($month) <= strtotime($param['date_time'][1])) {
                $search_month[] = $month;
                $month = date('Y-m',strtotime($month.' +1 month'));
            } else {
                break;
            }
        }
        self::$search_month = $search_month;
        $all_years = [];
        //获取时间
        $years = [];
        foreach ($search_month as $m_date) {
            $year = date('Y',strtotime($m_date));
            $years[$year][] = $m_date;
            $all_years[] = $year;
        }
        self::$years = $years;
        self::$param = $param;
        //建表
        $all_years = array_unique($all_years);
        foreach ($all_years as $year) {
            mskuReportModel::creatMskuReportTable($year);
            tableDataModel::creatTableMonth($year);
        }
        self::getColumn();
    }
    //获取要查询的数据
    public static function getColumn() {
        $db = dbFMysql::getInstance();
        //金额字段
        $db->table('column')
            ->where('where is_delete = 0 and show_type = 1')
            ->field('key_name');
        $list = $db->list();
        $currency_keys = [];
        foreach ($list as $v) {
            $currency_keys[] = $v['key_name'];
        }
        //查询自定义字段需要的列（只用找到百分比字段的自定义列，自定义的列都是通过领星列算好的）
        $custom_column = $db->table('custom_column')
            ->where('where is_delete = 0 and show_type<> 2')
            ->field('id,relation_column,last_relation_column,show_type,rules')
            ->list();
        //本月字段(只看百分比的字段，其余字段都是提前算好的)
        if ($custom_column) {
            foreach ($custom_column as $v) {
                $relation_column = json_decode($v['relation_column']);
                if (in_array('oa_key_4',$relation_column)) {
                    self::$need_again_oa_keys1[] = 'oa_key_'.$v;
                }
            }
        }
        self::$currency_keys = $currency_keys;
    }
    //获取查询条件(领星原表,所有数据)
    public static function getList() {
        $db = dbFMysql::getInstance();
        $param = self::$param;
        $goods_param = self::getGoodsSku($db);
        $sku_list = $goods_param['sku_list'];
        $use_sku = $goods_param['use_sku'];
        //获取数据
        $sql_list = [];
        foreach (self::$years as $year=>$m_date_list) {
            $db->table('table_month_count_'.$year)
                ->where('where is_delete=0')
                ->whereIn('m_date',$m_date_list);
            //国家
            if (!empty($param['country_code'])) {
                $db->andWhere('country_code=:country_code',['country_code'=>$param['country_code']]);
            }
            //项目
            if (!empty($param['project_ids']) && $param['project_ids']!='[]') {
                $db->whereIn('project_id',json_decode($param['project_ids']));
            }
            //运营
            if (!empty($param['yunying_ids']) && $param['yunying_ids']!='[]') {
                $db->whereIn('yunying_id',json_decode($param['yunying_ids']));
            }
            //自定义搜索
            if ($param['search_type'] == 'asin') {
                if (self::$table != 'asin') {
                    $db->andWhere('a.asin like :asin',['asin'=>"%{$param['search_value']}%"]);
                }
            }elseif ($param['search_type'] == 'sku') {
                if (self::$table != 'sku') {
                    $db->andWhere('a.localSku like :sku',['sku'=>"%{$param['search_value']}%"]);
                }
            } elseif ($param['search_type'] == 'p_asin') {
                if (self::$table != 'p_asin') {
                    $db->andWhere('a.parentAsin like :parentAsin',['parentAsin'=>"%{$param['search_value']}%"]);
                }
            }
            if ($use_sku) {
                $db->whereIn('sku',$sku_list);
            }
            $db->field('sku,asin,p_asin,country_code,project_id,yunying_id,m_date,data_json');
            $sql_list[] = $db->getSql();
        }
        $mew_tabel = implode(' UNION ALL ',$sql_list);
        $db->tablep($mew_tabel,'un_table');
        $report_data = $db->list();
        return $report_data;
    }
    //其他筛选条件获取sku
    private static function getGoodsSku($db) {
        $param = self::$param;
        //查询可用的sku
        //产品名
        $goods_name = $param['search_type'] == 'goods_name'?$param['search_value']:'';
        //分类
        $category_ids = [];
        if (!empty($param['category_ids']) && $param['category_ids']!='[]') {
            $category_ids = json_decode($param['category_ids']);
        }
        $sku_list = [];
        $user_sku = 0;
        if (!empty($goods_name) || count($category_ids)) {
            if (!in_array(self::$real_table,self::$profit_table) && $param['is_new'] != -1) {
                $user_sku = 1;
                $db->table('goods');
                if (!empty($goods_name)) {
                    $db->where('where product_name=:product_name',['product_name'=>"%$goods_name%"]);
                }
                if (count($category_ids)) {
                    $db->whereIn('cid',$category_ids);
                }
                if ($param['is_new'] != -1) {
                    $db->andWhere('is_new=:is_new',['is_new'=>(int)$param['is_new']]);
                }
                $goods_list = $db->field('sku')->list();
                $sku_list = array_column($goods_list,'sku');
            }
        }
        return ['sku_list'=>$sku_list,'use_sku'=>$user_sku];
    }


























}