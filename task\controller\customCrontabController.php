<?php

namespace task\controller;

use admin\form\customCrontabForm;
use plugins\assessment\models\customCrontabModel;

class customCrontabController
{
    public function __construct()
    {
        $api_token = $_POST['token'] ?? '';
        if ($api_token != 'private_token_steal_my_token_bad') {
            returnError('验证失败');
        }
        set_time_limit(0);
    }
    public static function getNextCrontab()
    {
        $res = customCrontabModel::getNextCrontab();
        if (!$res) {
            SetReturn(2,'fail', '没有任务');
        }
        SetReturn(0,'success', ['id' => intval($res)]);
    }

    //系统定时任务(产品)public static function getNextCrontab()
    public static function takeSysCrontab()
    {
        customCrontabForm::consumeTask();
    }

}