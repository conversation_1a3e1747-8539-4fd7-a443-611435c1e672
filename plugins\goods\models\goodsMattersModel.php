<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/2 11:43
 */

namespace  plugins\goods\models;

use Rap2hpoutre\FastExcel\FastExcel;

class goodsMattersModel
{
    public static array $export_key = [
        'matter_name'=>'流程名称',
        'created_at'=>'创建时间',
        'node_name'=>'当前节点',
        'status'=>'状态',
        'agent_wname'=>'当前办理人',
        'created_wname'=>'创建人',
        'expected_time'=>'预计完成时间',
        'completion_time'=>'完成时间',
    ];
    //导出
    public static function export($list,$export_key) {
        //表头
        $new_data = [];
        //数据
        $xuhao = 1;
        foreach ($list as $v) {
            $item['序号'] = $xuhao;
            foreach ($v as $k=>$v1) {
                //时间转化
                if (in_array($k,['completion_time','expected_time','created_at'])) {
                    $v1 = empty($v1)?'':date('Y-m-d H:i:s',$v1);
                }
                if (in_array($k,$export_key)) {
                    if ($k == 'status') {
                        if ($v1 == 0) {
                            $item[self::$export_key[$k]] = '进行中';
                        } else {
                            $item[self::$export_key[$k]] = '已完成';
                        }
                    } else {
                        $item[self::$export_key[$k]] = $v1;
                    }
                }
            }

            $new_data[] = $item;
            $xuhao++;
        }
        //保存
        $save_path = "/public/downLoad/matter/temp/".userModel::$wid;
        $url = SELF_FK.$save_path;
        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path."/".date('YmdHis').uniqid().'.xlsx';
        $url = SELF_FK.$path;
        if (!file_exists($url)) {
            touch($url);
        }
        (new FastExcel($new_data))->export($url);
        return $path;

//        //保存表格
//        $spreadsheet = new Spreadsheet();
//        $sheet = $spreadsheet->getActiveSheet();
//        //表头
//        $sheet->fromArray($excel_head);
//        //数据
//        $sheet->fromArray($new_data, null, 'A2');
//
//        //保存
//        $save_path = "/public/downLoad/matter/temp/".userModel::$wid;
//        $url = SELF_FK.$save_path;
//        if (!file_exists($url)) {
//            mkdir($url, 0777, true);
//        }
//        $path = $save_path."/".date('YmdHis').uniqid().'.xlsx';
//        $url = SELF_FK.$path;
//        if (!file_exists($url)) {
//            touch($url);
//        }
//        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
//        $writer->save($url);
//        return $save_path;
    }
}