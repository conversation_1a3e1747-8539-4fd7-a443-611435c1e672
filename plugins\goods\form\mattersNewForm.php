<?php
/**
 * @author: zhangguoming
 * @Time: 2025/3/26 15:29
 */

namespace plugins\goods\form;

//用于给总账号提供产品代码数据
use core\lib\db\dbMysql;
use core\lib\ExceptionError;
use plugins\goods\models\combinedProjectModel;
use plugins\goods\models\goodsMattersModel;
use plugins\goods\models\userModel;

class mattersNewForm
{
    public static $qwuser_ids = [];//部门成员企微表id
    //列表获取
    public static function getList($is_export = 0){
        //根据权限查询可查询的代办人id集合
        $paras_list = array('matter_name', 'status', 'create_type','node_id','agent_qwuser_id','create_qwuser_id','expected_time','created_time','is_advance_submit','order_by','page_size','page','complete_time','is_all','nav_id','nav_type','ids');
        $param = arrangeParam($_POST, $paras_list);
        $status = $param['status'];
        $db = dbMysql::getInstance();
        $db->table('goods_matters','a')
            ->leftJoin('qwuser','b','b.id = a.user_id')
            ->leftJoin('qwuser','c','c.id = a.qwuser_id')
            ->leftJoin('goods_project','d','d.id = a.goods_project_id');
        //用户限制
        if (count(self::$qwuser_ids)) {
            $db->whereIn('a.qwuser_id',self::$qwuser_ids);
        }
        if ($status > -1) {
            if ($status == 0) {
                $db->andWhere('and ((a.status=0 and a.is_advance_submit=0) or a.is_advance_submit=1)');
            } elseif ($status == 1) {
                $db->andWhere('and a.status=1 and (a.is_advance_submit=2 or a.is_advance_submit=0)');
            } elseif ($status == 3) {
                $db->andWhere('and a.status=3');
            }
        }
        if ($param['is_advance_submit'] > -1) {
            $db->andWhere('and a.is_advance_submit =:is_advance_submit',['is_advance_submit'=>(int)$param['is_advance_submit']]);
        }
        if (!empty($param['matter_name'])) {
            $db->andWhere('and a.matter_name like :matter_name',['matter_name'=>'%'.$param['matter_name'].'%']);
        }
        if ($param['create_type'] > -1) {
            if ($param['create_type'] == 0) {
                $db->andWhere('and (a.create_type=0 or  a.create_type=3) and a.is_advance_submit=0');
            } else {
                $db->andWhere('and a.create_type = :create_type and a.is_advance_submit=0',['create_type'=>$param['create_type']]);
            }
        }
        if ($param['node_id'] > -1) {
            $db->andWhere('and a.node_id = :node_id',['node_id'=>$param['node_id']]);
        }
        if ((int)$param['agent_qwuser_id'] > 0) {
            $db->andWhere('and a.qwuser_id = :qwuser_id',['qwuser_id'=>$param['agent_qwuser_id']]);
        }
        if ((int)$param['create_qwuser_id'] > 0) {
            $db->andWhere('and a.user_id = :user_id',['user_id'=>$param['create_qwuser_id']]);
        }
//        if (!empty($param['flow_path_id'])) {
//            if ($param['flow_path_id'] == -1) {
//                $db->andWhere('and b.flow_path_id is null');
//            } else {
//                $db->andWhere('and b.flow_path_id = :flow_path_id',['flow_path_id'=>$param['flow_path_id']]);
//            }
//        }
        //导航查询
        if(!empty($param['nav_id'])) {
            $nav_id = (int)$param['nav_id'];
            switch ($nav_id) {
                case 1:
                    //中文名，英文名
                    $db->andWhere('a.type in (1,2,6)');
                    break;
                case 2:
                    //测试样
                    $db->andWhere('d.flow_path_id = :flow_path_id',['flow_path_id'=>1]);
                    break;
                case 3:
                    //出货样
                    $db->andWhere('d.flow_path_id = :flow_path_id',['flow_path_id'=>2]);
                    break;
                case 4:
                    //抽货样
                    $db->andWhere('d.flow_path_id = :flow_path_id',['flow_path_id'=>3]);
                    break;
                case 5:
                    //图片需求
                    $db->andWhere('a.type = 4');
                    break;
                case 6:
                    //摄影需求
                    $db->andWhere('a.type = 7');
                    break;
                case 7:
                    //说明书需求
                    $db->andWhere('a.type = 5');
                    break;
                default:
                    $db->andWhere('1=0');
            }
        }
        if (!empty($param['expected_time']) && $param['expected_time'] != '[]') {
            $param['expected_time'] = json_decode($param['expected_time']);
            $start_time = strtotime($param['expected_time'][0]);
            $end_time = strtotime($param['expected_time'][1]);
            $db->andWhere('and a.expected_time >= :start_time and a.expected_time <= :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
        }
        if (!empty($param['created_time']) && $param['created_time'] != '[]') {
            $param['created_time'] = json_decode($param['created_time']);
            $start_time = strtotime($param['created_time'][0]);
            $end_time = strtotime($param['created_time'][1]);
            $db->andWhere('and a.created_at >= :start_time and a.created_at <= :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
        }
        if (!empty($param['complete_time']) && $param['complete_time'] != '[]') {
            $param['complete_time'] = json_decode($param['complete_time']);
            $start_time = strtotime($param['complete_time'][0]);
            $end_time = strtotime($param['complete_time'][1]);
            $db->andWhere('and a.completion_time >= :start_time and a.completion_time <= :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
        }
        $order_str = '';
        if (!empty($param['order_by'])) {
            $order_by = json_decode($param['order_by'],true);
            foreach ($order_by as $k=>$ord){
                $order_str .= 'a.'.$k.' '.$ord.',';
            }
        }
        if (empty($order_str)) {
            $db->order('a.id desc');
        } else {
            $db->order(trim($order_str,','));
        }
        $db->field('a.id,a.is_advance_submit,a.after_expected_time,a.after_delay_hour,a.goods_project_id,a.matter_name,a.type,a.create_type,a.goods_id,a.status,a.node_name,a.event_name,a.event_type,a.expected_day,b.wname as created_wname,c.wname as agent_wname,a.created_at,d.expected_day as project_expected_day,a.expected_time,d.created_time as project_created_time,d.status as project_status,a.completion_time');
        if ($param['is_all']) {
            $list['list'] = $db->list(10);
        } elseif ($is_export) {
            $ids = $param['ids']??'[]';
            if (!empty($param['ids']) && $param['ids'] != '[]') {
                $ids = json_decode($ids);
                $db->whereIn('a.id',$ids);
            }
            $list['list'] = $db->list();
        } else {
            $list = $db->pages($param['page'],$param['page_size']);
        }
//        dd($list['list']);
        foreach ($list['list'] as &$v) {
            if (!empty($v['event_name'])) {
                $v['node_name'] = $v['node_name'].'/'.$v['event_name'];
            }
            $v['expected_time'] = empty($v['expected_time'])?'':$v['expected_time'];
        }
        return $list;

    }
    //列表导航（全部待办，待办，已办）
    public static function countData(int $nav_id) {
        $paras_list = array('status');
        $param = arrangeParam($_POST, $paras_list);
        $db = dbMysql::getInstance();
        $db->table('goods_matters','a')
            ->leftJoin('goods_project','d','d.id = a.goods_project_id');
        if (count(self::$qwuser_ids)) {
            $db->whereIn('qwuser_id',self::$qwuser_ids);
        }
        if ($param['status'] > -1) {
            if ($param['status'] == 0) {
                //待办
                $db->andWhere('and ((a.status=0 and a.is_advance_submit=0) or a.is_advance_submit=1)');
            }
            if ($param['status'] == 1) {
                //已办
                $db->andWhere('and a.status=1 and (a.is_advance_submit=2 or a.is_advance_submit=0)');
            }
            if ($param['status'] == 3) {
                $db->andWhere('and a.status=3');
            }
        }
        switch ($nav_id) {
            case 1:
                //中文名，英文名
                $db->andWhere('a.type in (1,2,6)');
                break;
            case 2:
                //测试样
                $db->andWhere('d.flow_path_id = :flow_path_id',['flow_path_id'=>1]);
                break;
            case 3:
                //出货样
                $db->andWhere('d.flow_path_id = :flow_path_id',['flow_path_id'=>2]);
                break;
            case 4:
                //抽货样
                $db->andWhere('d.flow_path_id = :flow_path_id',['flow_path_id'=>3]);
                break;
            case 5:
                //图片需求
                $db->andWhere('a.type = 4');
                break;
            case 6:
                //摄影需求
                $db->andWhere('a.type = 7');
                break;
            case 7:
                //说明书需求
                $db->andWhere('a.type = 5');
                break;
            default:
                $db->andWhere('1=0');
        }
        $count = $db->count();
        return$count;
    }
    //列表导航（流程）
    public static function countDataProcess(int $nav_id) {
        $db = dbMysql::getInstance();
        $db->table('goods_project');
        if (count(self::$qwuser_ids)) {
            $db->whereIn('user_id',self::$qwuser_ids);
        }
        $db->andWhere('flow_path_id = :flow_path_id',['flow_path_id'=>$nav_id]);
        $count = $db->count();
        return$count;
    }
    //待办事项统计
    public static function getWatingCountdata(){
        $db = dbMysql::getInstance();
        //待办全部
        $count_agent_all = $db->table('goods_matters')
            ->where('((status=0 and is_advance_submit=0) or is_advance_submit=1)')
            ->andWhere('qwuser_id = :qwuser_id',['qwuser_id'=>userModel::$qwuser_id])
            ->count();
        //待办-办理
        $wait_agent = $db->table('goods_matters')
            ->where('((status=0 and is_advance_submit=0) or (status=1 and is_advance_submit=1)) and is_advance_submit=0')
            ->andWhere('qwuser_id = :qwuser_id',['qwuser_id'=>userModel::$qwuser_id])
            ->count();
        //待审批
        $count_wait_approval = $db->table('goods_matters')
            ->where('status = 0 and create_type = 2')
            ->andWhere('qwuser_id = :qwuser_id',['qwuser_id'=>userModel::$qwuser_id])
            ->count();
        //待审核
        $count_wait_check = $db->table('goods_matters')
            ->where('status = 0 and create_type = 1')
            ->andWhere('qwuser_id = :qwuser_id',['qwuser_id'=>userModel::$qwuser_id])
            ->count();
        //延时任务
        $wait_delay_matter_count = $db->table('goods_matters')
            ->where('is_advance_submit = 1 and is_advance_submit = 1')
            ->andWhere('qwuser_id = :qwuser_id',['qwuser_id'=>userModel::$qwuser_id])
            ->count();
        //申请的流程数量未完成
        $data = [
            'wait_agent_all'=>$count_agent_all,                     //待办事项（带及时任务）
            'wait_agent'=>$wait_agent,                              //待办
            'wait_delay_matters_count'=>$wait_delay_matter_count,    //待办（延时任务）
            'wait_agent_check'=>$count_wait_check,                  //待审核
            'count_wait_approval'=>$count_wait_approval,            //待审批
        ];
        return $data;
    }
    //流程查询
    public static function getProjectList($is_export = 0) {
        $paras_list = array('matter_name', 'status','is_stop', 'is_apply','expected_time','created_time','order_by','page_size','page','is_me_created','complete_time','is_abolish','nav_id','nav_type','ids');
        $param = arrangeParam($_POST, $paras_list);
        $is_apply = (int)$param['is_apply'];
        $is_abolish = (int)$param['is_abolish'];
        $db = dbMysql::getInstance();
        //查询条件是全部：根据权限查出来的用户获取-流程相关用户
        $project_ids = [];
        if (!$is_apply) {
            if (count(self::$qwuser_ids)) {
                $participant = $db->table('goods_project_participant')
                    ->whereIn('qwuser_id',self::$qwuser_ids)
                    ->field('project_id')
                    ->list();
                $project_ids = array_column($participant,'project_id');
            }
        } else {
            $participant = $db->table('goods_project_participant')
                ->where('where qwuser_id=:qwuser_id',['qwuser_id'=>userModel::$qwuser_id])
                ->field('project_id')
                ->list();
            $project_ids = array_column($participant,'project_id');
        }
        $db->table('goods_project','a')
            ->leftJoin('qwuser','b','b.id=a.user_id');
        if (count(self::$qwuser_ids) != 0) {
            $db->whereIn('a.id',$project_ids);
        }
        if (!empty($param['matter_name'])) {
            $db->andWhere('and a.matter_name like :matter_name',['matter_name'=>'%'.$param['matter_name'].'%']);
        }
        if ($param['status'] > -1) {
            $db->andWhere('and a.status=:status',['status'=>$param['status']]);
        }
        if ($is_apply) {
            $db->andWhere('and a.user_id=:user_id',['user_id'=>userModel::$qwuser_id]);
        }
        if ($is_abolish > -1) {
            if ($is_abolish) {
                $db->andWhere('and a.status = 4');
            } else {
                $db->andWhere('and a.status <> 4');
            }
        }
        //导航查询
        if (!empty($param['nav_id'])) {
            $db->andWhere('a.flow_path_id = :flow_path_id',['flow_path_id'=>(int)$param['nav_id']]);
        }
        if (!empty($param['expected_time']) && $param['expected_time']!='null' && $param['expected_time']!='[]') {
            $param['expected_time'] = json_decode($param['expected_time']);
            $start_time = strtotime($param['expected_time'][0]);
            $end_time = strtotime($param['expected_time'][1]);
            $db->andWhere('and (a.created_time+a.expected_day*24*60*60) > :start_time and (a.created_time+a.expected_day*24*60*60) < :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
        }
        if (!empty($param['created_time']) && $param['created_time']!='null' && $param['created_time']!='[]') {
            $param['created_time'] = json_decode($param['created_time']);
            $start_time = strtotime($param['created_time'][0]);
            $end_time = strtotime($param['created_time'][1]);
            $db->andWhere('and a.created_time > :start_time and a.created_time < :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
        }
        if (!empty($param['complete_time'])) {
            $param['complete_time'] = json_decode($param['complete_time']);
            if (count($param['complete_time'])) {
                $start_time = strtotime($param['complete_time'][0]);
                $end_time = strtotime($param['complete_time'][1]);
                $db->andWhere('and a.complete_time >= :start_time and a.complete_time <= :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
            }
        }
        $order_str = '';
        if (!empty($param['order_by'])) {
            $order_by = json_decode($param['order_by'],true);
            foreach ($order_by as $k=>$ord){
                $order_str .= 'a.'.$k.' '.$ord.',';
            }
        }
        if (empty($order_str)) {
            $db->order('a.created_time desc');
        } else {
            $db->order(trim($order_str,','));
        }

        $db->field('a.id as project_id,a.*,(a.created_time+expected_day*24*60*60) expected_time,b.wname as created_wname');
        if ($is_export) {
            $ids = $param['ids']??'[]';
            if (!empty($param['ids']) && $param['ids'] != '[]') {
                $ids = json_decode($ids);
                $db->whereIn('a.id',$ids);
            }
            $list['list'] = $db->list();
        } else {
            $list = $db->pages($param['page'],$param['page_size']);
        }
        foreach ($list['list'] as &$v) {
            $current_node_info = json_decode($v['current_node_info'],true);
            $node_name_a = [];
            $manage_info = [];
            foreach ($current_node_info as $v1) {
                $node_name_a[] = $v1['node_name'];
                $manage_info = array_merge($manage_info,json_decode($v1['manage_info'],true));
            }
            $v['node_name'] = $node_name_a;
            $v['manage_info'] = arrayUnique2($manage_info,'id');
            unset($v);
        }
        return $list;
    }
    //导出流程
    public static function exportAllProject() {
        $export_key = empty($_POST['export_key'])?'[]':$_POST['export_key'];
        $export_key = json_decode($export_key);
        if (!count($export_key)) {
            SetReturn(-1,'请选择要导出的字段');
        }
        $list = (self::getProjectList(1))['list'];
        if (!count($list)) {
            SetReturn(-1,'未查询到可导出的数据');
        }
        $url = combinedProjectModel::export($list,$export_key);
        return $url;
    }
    //批量提交
    public static function submitMuchMatters() {
        $ids = $_POST['ids'];
        if (empty($ids)) {
            SetReturn(-1,'请选择需要提交待办事项');
        }
        $ids = json_decode($ids,true);
        if (!count($ids)) {
            SetReturn(-1,'请选择需要提交的待办事项');
        }
        $db = dbMysql::getInstance();
        try {
            $error_count = 0;
            foreach ($ids as $id) {
                $db->beginTransaction();
                goodsMattersFrom::submitMattersById($id,0);
                if (goodsMattersFrom::$submit_result_error_count > $error_count) {
                    $error_count = goodsMattersFrom::$submit_result_error_count;
                    $db->rollBack();
                } else {
                    $db->commit();
                }
            }
            returnSuccess(goodsMattersFrom::$submit_result,'提交成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //交办
    public static function changeMatterAgent() {
        $paras_list = array('ids', 'agent_user', 'remarks');
        $request_list = ['ids'=>'事项id', 'agent_user'=>'交办人'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $ids = json_decode($param['ids'],true);
        $agent_user = json_decode($param['agent_user'],true);
        $agent_user = $agent_user[0];
        if (!count($agent_user)) {
            SetReturn(-1,'请选择交办人');
        }
        if (!count($agent_user) > 1) {
            SetReturn(-1,'交办人只能是一人');
        }
        $db = dbMysql::getInstance();
        $matter_list = $db->table('goods_matters','a')
            ->leftJoin('qwuser','b','b.id = a.qwuser_id')
            ->where('where a.status = 0')
            ->whereIn('a.id',$ids)
            ->field('a.*,b.wid')
            ->list();
        $db->beginTransaction();
        try {
            foreach ($matter_list as $v) {
                goodsMattersFrom::changeMatterAgentById($v,$agent_user);
            }
            $change_matter_msg = goodsMattersFrom::$change_matter_msg;
            if (count($change_matter_msg)) {
                foreach ($change_matter_msg as $msg) {
                    messagesFrom::senMeg($msg['wids'],$msg['msg'],$msg['other_data'],$param['remarks']);
                }
            }
            if (count($ids) == 1 && goodsMattersFrom::$submit_result_error_count) {
                $db->rollBack();
                SetReturn(-1,goodsMattersFrom::$submit_result[0]['msg']);
            }
            $db->commit();
            returnSuccess(goodsMattersFrom::$submit_result,'交办成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //抄送
    public static function sendToUserMuch() {
        $paras_list = array('ids', 'send_copy_user','remarks');
        $request_list = ['ids'=>'事项id', 'send_copy_user'=>'抄送人'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $ids = json_decode($param['ids'],true);
        if (!count($ids)) {
            SetReturn(-1,'请选择抄送的流程');
        }
        $send_copy_user = json_decode($param['send_copy_user'],true);
        if (!count($send_copy_user)) {
            SetReturn(-1,'请选择抄送人');
        }
        $db = dbMysql::getInstance();
        $poject_list = $db->table('goods_project')
            ->whereIn('id',$ids)
            ->list();

        $send_copy_user_wid = array_column($send_copy_user,'wid');
        $result_copy = [];
        foreach ($poject_list as $v) {
            if ($v['is_stop']) {
                $result_copy[] = [
                    'id'=>$v['id'],
                    'matter_name'=>$v['matter_name'],
                    'success'=>0,
                    'msg'=>'该项目流程已暂停，不可操作',
                ];
                continue;
            }
            if ($v['status'] == 4) {
                $result_copy[] = [
                    'id'=>$v['id'],
                    'matter_name'=>$v['matter_name'],
                    'success'=>0,
                    'msg'=>'该项目流程已废除，不可操作',
                ];
                continue;
            }
            $remind_msg[] = [
                'wids'=>$send_copy_user_wid,
                'msg'=>messagesFrom::getMsgTxt(2,$v['matter_name'],'',''),
                'other_data'=>[
                    'user_id'=>userModel::$qwuser_id,
                    'model_id'=>$v['id'],
                    'msg_type'=>2
                ],
            ];
            $result_copy[] = [
                'id'=>$v['id'],
                'matter_name'=>$v['matter_name'],
                'success'=>1,
                'msg'=>'',
            ];
        }
        foreach ($remind_msg as $msg) {
            messagesFrom::senMeg($msg['wids'],$msg['msg'],$msg['other_data'],$param['remarks']);
        }
        returnSuccess($result_copy,'抄送消息已发送');
    }
    //催办-流程
    public static function nodeAgentRemindMuch() {
        $ids = json_decode($_POST['project_ids']);
        $remarks = $_POST['remarks'];
        if (!count($ids)) {
            SetReturn(-1, '参数有误');
        }
        $db = dbMysql::getInstance();
        $project_list = $db->table('goods_project','a')
            ->leftJoin('goods_new','b','b.id = a.goods_id')
            ->whereIn('a.id',$ids)
            ->field('a.*,b.goods_name,b.manage_info')
            ->list();
        $remind_msg = [];
        foreach ($project_list as $project_data) {
            $tpl_data = json_decode($project_data['tpl_data'],true);
            //当前催办的节点集合
            $current_node_list = $tpl_data[$project_data['current_index']];
            foreach ($current_node_list as $k=>$node) {
                if ($node['status'] == 2) {
                    continue;
                } else {
                    $node_manage_info = json_decode($node['manage_info'], true);
                    $node_manage_wid = array_column($node_manage_info,'wid');
                    //给节点负责人推送
                    $remind_msg[] = [
                        'wids'=>$node_manage_wid,
                        'msg'=>messagesFrom::getMsgTxt(4,$project_data['matter_name'],$node['node_name'],''),
                        'other_data'=>[
                            'user_id'=>userModel::$qwuser_id,
                            'model_id'=>$project_data['id'],
                            'node_index'=>$project_data['current_index'].'-'.$k,
                            'event_index'=>null,
                            'msg_type'=>4
                        ],
                    ];
                    foreach ($node['event_detail'] as $k1=>$ev1) {
                        foreach ($ev1 as $k2=>$event) {
                            if ($event['status'] !=2) {
                                $event_manage_wid = array_column(json_decode($event['manage_info'], true),'wid');
                                $event_manage_wid = array_diff($event_manage_wid,$node_manage_wid);
                                if (count($event_manage_wid)) {
                                    $remind_msg[] = [
                                        'wids'=>$event_manage_wid,
                                        'msg'=>messagesFrom::getMsgTxt(4,$project_data['matter_name'],$node['node_name'],$event['event_name']),
                                        'other_data'=>[
                                            'user_id'=>userModel::$qwuser_id,
                                            'model_id'=>$project_data['id'],
                                            'node_index'=>$project_data['current_index'].'-'.$k,
                                            'event_index'=>$k1.'-'.$k2,
                                            'msg_type'=>4
                                        ],
                                    ];
                                }
                            }
                        }
                    }
                }
            }
        }
        //发送消息
        if (count($remind_msg)) {
            foreach ($remind_msg as $msg) {
                messagesFrom::senMeg($msg['wids'],$msg['msg'],$msg['other_data'],$remarks);
            }
            returnSuccess('','催办消息已发送');
        } else {
            SetReturn(-1, '未能匹配到可催办的事项');
        }
    }
    //催办-待办事项
    public static function matterAgentRemind () {
        $paras_list = array('ids', 'remarks');
        $param = arrangeParam($_POST, $paras_list);
        $ids = json_decode($param['ids'],true);
        if (count($ids) == 0) {
            SetReturn(-1,'请选择要催办的事项');
        }
        $remind_result = [];

        $db = dbMysql::getInstance();
        $matter_list = $db->table('goods_matters','a')
            ->leftJoin('qwuser','b','b.id = a.qwuser_id')
            ->where('where a.status = 0')
            ->whereIn('a.id',$ids)
            ->field('a.*,b.wid')
            ->list();
        foreach ($matter_list as $v) {
            if ($v['status'] == 1) {
                $remind_result[] = [
                    'id'=>$v['id'],
                    'matter_name'=>$v['matter_name'],
                    'node_name'=>$v['node_name'],
                    'success'=>0,
                    'msg'=>'已完成',
                ];
            } else {
                $remind_msg[] = [
                    'wids'=>[$v['wid']],
                    'msg'=>messagesFrom::getMsgTxt(4,$v['matter_name'],$v['node_name'],$v['event_name']),
                    'other_data'=>[
                        'user_id'=>userModel::$qwuser_id,
                        'model_id'=>($v['goods_project_id']>0?$v['goods_project_id']:$v['model_id']),
                        'node_index'=>$v['node_index'],
                        'event_index'=>$v['event_index'],
                        'msg_type'=>4
                    ],
                ];
                $remind_result[] = [
                    'id'=>$v['id'],
                    'matter_name'=>$v['matter_name'],
                    'node_name'=>$v['node_name'],
                    'success'=>1,
                    'msg'=>'',
                ];
            }

        }
        foreach ($remind_msg as $msg) {
            messagesFrom::senMeg($msg['wids'],$msg['msg'],$msg['other_data'],$param['remarks']);
        }
        returnSuccess($remind_result,'催办消息已发送');
    }
    //暂停
    public static function stopProjectMuch($is_stop) {
        $ids = $_POST['ids'];
        if (!$ids) {
            SetReturn(-1,'请选择要操作的流程');
        }
        $ids = json_decode($ids);
        if (!count($ids)) {
            SetReturn(-1,'请选择要操作的流程');
        }
        $db = dbMysql::getInstance();
        $db->table('goods_project')
            ->where('where status <> 4')
            ->whereIn('id',$ids)
            ->update(['is_stop'=>$is_stop,'updated_time'=>date('Y-m-d H:i:s')]);
        SetReturn(0,'操作成功');
    }
    //导出待办,已办
    public static function exportList() {
        $export_key = empty($_POST['export_key'])?'[]':$_POST['export_key'];
        $export_key = json_decode($export_key);
        if (!count($export_key)) {
            SetReturn(-1,'请选择要导出的字段');
        }
        $list = self::getList(1)['list'];
        if (!count($list)) {
            SetReturn(-1,'未查询到可导出的数据');
        }
        $url = goodsMattersModel::export($list,$export_key);
        returnSuccess(['url'=>$url]);
    }

}