#!/bin/bash

# 产品品牌数据同步脚本
# 功能：从领星API同步产品品牌数据到ERP数据库
# 作者：System
# 时间：2025/07/04

# 配置参数
token='01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0'
qwUrl='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c8b5b8b8-8b8b-4b8b-8b8b-8b8b8b8b8b8b'

echo "开始产品品牌数据同步..."
echo "=========================================="
start_time=$(date +"%Y-%m-%d %H:%M:%S")
echo "开始时间: $start_time"

# 同步产品品牌数据
echo "调用产品品牌同步接口..."
max_retries=20
retry_count=0

while [ $retry_count -lt $max_retries ]; do
    retry_count=$((retry_count + 1))
    echo "尝试 $retry_count/$max_retries..."
    
    # 调用API接口
    response=$(curl -s -X POST -d "token=$token" 'http://oa.ywx.com/task/lingXingApi/synProductBrand')
    echo "API响应: $response"
    
    # 检查响应是否为空
    if [ -z "$response" ]; then
        echo "❌ API响应为空，请检查网络连接或服务状态"
        if [ $retry_count -ge $max_retries ]; then
            echo "达到最大重试次数，终止处理"
            # 发送失败通知
            curl -s "$qwUrl" -H "Content-Type: application/json" -d "{\"msgtype\":\"text\",\"text\":{\"content\":\"❌ 产品品牌同步失败\\n📊 错误信息: API响应为空\\n⏰ 失败时间: $(date +"%Y-%m-%d %H:%M:%S")\"}}"
            exit 1
        fi
        echo "等待5秒后重试..."
        sleep 5
        continue
    fi
    
    # 从API响应中提取code字段的值
    code=$(echo "$response" | grep -oP '"code":\K-?\d+')
    # 从API响应中提取msg字段的值
    msg=$(echo "$response" | grep -oP '"message"\s*:\s*"\K[^"]+')
    
    # 如果无法提取code，设置默认值
    if [ -z "$code" ]; then
        code=-999
        msg="无法解析API响应"
    fi
    
    echo "同步结果: code=$code, msg=$msg"
    
    if [ "$code" -eq 2 ]; then
        # 同步完成
        end_time=$(date +"%Y-%m-%d %H:%M:%S")
        echo "✅ 产品品牌数据同步完成"
        echo "结束时间: $end_time"
        
        # 发送成功通知
        curl -s "$qwUrl" -H "Content-Type: application/json" -d "{
            \"msgtype\": \"text\",
            \"text\": {
                \"content\": \"✅ 产品品牌数据同步完成\\n📊 同步结果: $msg\\n⏰ 完成时间: $end_time\"
            }
        }"
        
        echo "成功通知已发送"
        exit 0
        
    elif [ "$code" -eq -1 ]; then
        # 同步失败
        echo "❌ 产品品牌数据同步失败: $msg"
        
        # 发送失败通知
        curl -s "$qwUrl" -H "Content-Type: application/json" -d "{
            \"msgtype\": \"text\",
            \"text\": {
                \"content\": \"❌ 产品品牌数据同步失败\\n📊 错误信息: $msg\\n⏰ 失败时间: $(date +"%Y-%m-%d %H:%M:%S")\"
            }
        }"
        
        echo "失败通知已发送"
        exit 1
        
    else
        # 继续同步
        echo "继续同步产品品牌数据，code=$code"
        echo "等待2秒后继续..."
        sleep 2
    fi
done

# 如果达到最大重试次数仍未完成
echo "❌ 达到最大重试次数，同步可能未完成"

# 发送超时通知
curl -s "$qwUrl" -H "Content-Type: application/json" -d "{
    \"msgtype\": \"text\",
    \"text\": {
        \"content\": \"⚠️ 产品品牌数据同步超时\\n📊 最后状态: code=$code, msg=$msg\\n⏰ 超时时间: $(date +"%Y-%m-%d %H:%M:%S")\\n🔄 重试次数: $retry_count\"
    }
}"

echo "超时通知已发送"
exit 1
