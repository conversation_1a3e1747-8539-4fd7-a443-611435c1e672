<?php
/**
 * FBA库存汇总统计控制器
 * @purpose 提供FBA库存汇总统计的API接口
 * @Author: System
 * @Time: 2025/06/20
 */

namespace plugins\logistics\controller;

use plugins\logistics\models\fbaStorageSummaryModel;
use plugins\logistics\form\fbaStorageSummaryForm;

class fbaStorageSummaryController extends baseController
{
    private $model;
    private $form;

    public function __construct()
    {
        $this->model = new fbaStorageSummaryModel();
        $this->form = new fbaStorageSummaryForm();
        parent::__construct();
    }

    /**
     * 获取FBA库存汇总统计列表
     * GET /plugins/logistics/fbaStorageSummary/list
     */
    public function getList()
    {
        try {
            // 表单验证
            $params = $this->form->validateListParams($_GET);
            
            // 获取数据
            $result = $this->model->getSummaryList($params);
            
            // 格式化数据
            if (!empty($result['list'])) {
                $levelTypeMap = $this->model->getLevelTypeMap();
                $productStageMap = $this->model->getProductStageMap();
                $stockPositioningMap = $this->model->getStockPositioningMap();
                $productPositioningMap = $this->model->getProductPositioningMap();

                $asin = array_column($result['list'], 'asin');
                //查询子项
                $child_map = [];
                $child = $this->model->getChildren($asin);
                // 按照层级拼成树状结构
                foreach ($child as $item) {
                    $asin = $item['asin'];
                    $sku = $item['sku'] ?: '-';
                    $country_code = $item['country_code'] ?: '-';
                    switch ($item['level_type']) {
                        case 1: // 店铺
                            $child_map[$asin][$sku]['children'][$country_code]['children'][] = $item;
                            break;
                        case 2: // 站点
                            $child_map[$asin][$sku]['children'][$country_code] = $item;
                            break;
                        case 3: // sku
                            $child_map[$asin][$sku] = $item;
                            break;
                    }
                }

                // 将children格式化为数组
                foreach ($child_map as $asin => $sku_map) {
                    foreach ($sku_map as $sku => $country_map) {
                        $child_map[$asin][$sku]['children'] = array_values($country_map['children']);
                    }
                    $child_map[$asin] = array_values($child_map[$asin]);
                }

                foreach ($result['list'] as &$item) {
                    $item['level_type_text'] = $levelTypeMap[$item['level_type']] ?? '';
                    $item['product_stage_text'] = $productStageMap[$item['product_stage']] ?? '';
                    $item['stock_positioning_text'] = $stockPositioningMap[$item['stock_positioning']] ?? '';
                    $item['product_positioning_text'] = $productPositioningMap[$item['product_positioning']] ?? '';

                    $item['children'] = $child_map[$item['asin']] ?? [];
                    
                    // 解析JSON字段
                    $list_key = ['shop_list', 'country_code_list', 'sku_list', 'warehouse_list', 'seller_sku_list', 'fnsku_list', 'category_list', 'brand_list'];
                    foreach ($list_key as $key) {
                        if (!empty($item[$key])) {
                            $item[$key] = json_decode($item[$key], true) ?: [];
                        }
                    }
                    
                }
            }
            
            returnSuccess($result);
            
        } catch (\Exception $e) {
            returnError('获取FBA库存汇总统计列表失败'.$e->getMessage());
        }
    }

    /**
     * 批量编辑FBA库存汇总统计
     * POST /plugins/logistics/fbaStorageSummary/batchEdit
     */
    public function edit()
    {
        try {
            // 获取POST数据
            $postData = $_POST;
            if (empty($postData['items']) || !is_array($postData['items'])) {
                returnError('批量编辑数据为空或格式错误');
            }
            
            $successCount = 0;
            $failCount = 0;
            $errors = [];
            
            foreach ($postData['items'] as $item) {
                try {
                    if (empty($item['id']) || !is_numeric($item['id'])) {
                        $failCount++;
                        $errors[] = "ID {$item['id']} 无效";
                        continue;
                    }
                    
                    // 表单验证 
                    $data = $this->form->validateEditParams($item);
                    
                    // 执行更新
                    $affectedRows = $this->model->updateEditableFields($item['id'], $data);
                    
                    if ($affectedRows > 0) {
                        $successCount++;
                    } else {
                        $failCount++;
                        $errors[] = "ID {$item['id']} 更新失败";
                    }
                    
                } catch (\Exception $e) {
                    $failCount++;
                    $errors[] = "ID {$item['id']} 处理失败: " . $e->getMessage();
                }
            }
            
            $message = "批量编辑完成：成功 {$successCount} 条，失败 {$failCount} 条";
            if (!empty($errors)) {
                $message .= "，错误详情：" . implode('; ', array_slice($errors, 0, 5));
                if (count($errors) > 5) {
                    $message .= " 等...";
                }
            }
            
            returnSuccess([
                'message' => $message,
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'errors' => $errors
            ]);
            
        } catch (\Exception $e) {
            returnError('批量编辑失败');
        }
    }
}
