<?php

/**
 * @author: z<PERSON>guoming
 * @Time: 2024/3/27 9:02
 */

namespace plugins\assessment\models;

use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\db\dbAMysql;
class assessmentTargetsModel
{
    public static int $id;
    public static string $target_name; // 指标名称
    public static string $status; // 1启用 0禁用
    public static string $target_type; // 指标类型 1定量 2定性
    public static string $target_detail; // 指标详情
    /** 实例 target_detail
     * 【仅定量】 target_source:指标来源 column_id:指标id target_method:指标考核方式 standard_value:指标考核值 formula:公式  range_type 适用范围 1个人2部门
     * 【仅定性】work_desc:详述工作
     * calc_type:取数规则 after_point:小数点位数
     *{
     *      // 定量 - 公式
     *      "target_source":1,
     *      "column_id":1,
     *      "range_type" :1,
     *      "target_method":1,
     *      "leader_score":1, // 上级评分
     *      "standard_value":{"type":1,"value":100},
     * 
     *      // symbol 连接符号 type 条件/条件组 value_type 值类型（1个人2部门3自定义值） value值 is_abs是否绝对值(0值 1绝对值 2向下取整)
     *      "formula":[
     *          {"symbol":1,"type":1, "value_type":1, "value":1, "is_abs":0},
     *          {"symbol":1,"type":2, "list":[{"symbol":1,"value_type":2, "value":1, "is_abs":0}]}
     *      ],
     *
     *      // 定量 - 阶梯
     *     "target_source":2,
     *     "column_ids":[1,2,3],
     *     "target_method":2, // 按阶梯划分
     *     "formula":[
     *          "rules":[
     *              {"column_id":1,"symbol":"1", "value":[100], "target_symbol":"1"},
     *              {"column_id":1,"symbol":"6", "value":[100,500], "target_symbol":"2"}
     *          ],
     *          "result":[
     *              {"value_type":1, "value":4, "formula_symbol":"1"},
     *              {"value_type":2, "value":null, "formula_symbol":"2"}
     *          ],
     *      ],
     *      "highest_result":100 // 最高值
     * 
     *      // 定量
     *      "currency":"CNY", // 币种
     *
     *      // 定性
     *      "work_desc":"1" //详述工作 1是 0否 ,定性指标
     *
     *      // 通用
     *      "calc_type":2, "after_point":2
     * }
     * */
    public static bool $assessment_standard; // 考核标准
    public static bool $is_delete; // 1删除 0未删除

    //
    public static function checkTargetDetail($target_detail, $target_type) : bool {
        empty($target_detail) && returnError('指标明细不能为空');
        empty($target_detail['calc_type']) && returnError('取数规则不能为空');
        // 原始数据、四舍五入
        in_array($target_detail['calc_type'], [1,2]) && empty($target_detail['after_point']) && returnError('小数点位数不能为空');
        // 定量指标
        if ($target_type == 1) {
            // 绩效1期，存在不可拉取数据的指标（target_source = -1），需要暂时让上级来填写指标考核值和指标实际值
            empty($target_detail['target_source']) && returnError('指标来源不能为空');
            empty($target_detail['target_method']) && returnError('指标考核方式不能为空');
            // 定量 - 财务指标，需要填写币种
            $target_detail['target_source'] == 1 &&empty($target_detail['currency']) && returnError('币种不能为空');
            empty($target_detail['range_type']) && returnError('适用范围不能为空');
            // 按公式计算
            if ($target_detail['target_method'] == 1) {
                $target_detail['target_source'] != -1 && empty($target_detail['column_id']) && returnError('指标id不能为空');
                !isset($target_detail['leader_score']) && returnError('上级评分不能为空');
                empty($target_detail['standard_value']['type']) && returnError('指标考核值类型不能为空');
                // 固定考核值
                if ($target_detail['standard_value']['type'] == 1) {
                    empty($target_detail['standard_value']['value']) && returnError('指标考核值不能为空');
                }
                empty($target_detail['formula']) && returnError('公式不能为空');
                // 公式
                $idx = 0;
                foreach ($target_detail['formula'] as $item) {
                    $idx && empty($item['symbol']) && returnError('符号不能为空');
                    empty($item['type']) && returnError('条件/条件组不能为空');
                    // 条件
                    if ($item['type'] == 1) {
                        empty($item['value_type']) && returnError('值类型不能为空');
                        !in_array($item['value_type'], [$target_detail['range_type'], '3']) && returnError('条件中存在错误的适用范围值');
                        !isset($item['value']) && returnError('值不能为空');
                        $item['value_type'] == 1 && $target_detail['leader_score'] == 0 && in_array($item['value'], [3,4]) && returnError('不需要上级评分时，不能使用自评分数和上级评分');
                        !isset($item['is_abs']) && returnError('是否绝对值不能为空');
                    }
                    // 条件组
                    elseif ($item['type'] == 2) {
                        empty($item['list']) && returnError('条件组不能为空');
                        foreach ($item['list'] as $subItem) {
                            empty($subItem['symbol']) && returnError('符号不能为空');
                            empty($subItem['value_type']) && returnError('值类型不能为空');
                            !in_array($subItem['value_type'], [$target_detail['range_type'], '3']) && returnError('条件组中存在错误的适用范围值');
                            !isset($subItem['value']) && returnError('值不能为空');
                            $subItem['value_type'] == 1 && $target_detail['leader_score'] == 0 && in_array($subItem['value'], [3,4]) && returnError('不需要上级评分时，不能使用自评分数和上级评分');
                            !isset($subItem['is_abs']) && returnError('是否绝对值不能为空');
                        }
                    }
                    $idx++;
                }
            }
            // 按阶段划分
            elseif ($target_detail['target_method'] == 2) {
                $target_detail['target_source'] != -1 && empty($target_detail['column_ids']) && returnError('指标ids不能为空');
                empty($target_detail['formula']) && returnError('阶梯划分不能为空');
                foreach ($target_detail['formula'] as $item) {
                    $idx = 0;
                    foreach ($item['rules'] as $rule) {
                        $target_detail['target_source'] != -1 && empty($rule['column_id']) && returnError('指标id不能为空');
                        $target_detail['target_source'] != -1 && !in_array($rule['column_id'], $target_detail['column_ids']) && returnError('规则指标id不在指标列表中');
                        $idx && empty($rule['symbol']) && returnError('连接符号不能为空');// 第一个不需要符号
                        empty($rule['target_symbol']) && returnError('符号不能为空');
                        empty($rule['value']) && returnError('值不能为空');
                        empty($rule['value']) && !isset($rule['value'][0]) && returnError('值不能为空');
                        // value为数据组，在区间为6时，需要两个值，其他情况只需要一个值
                        if ($rule['symbol'] == 6) {
                            if (!isset($rule['value'][1])) {
                                returnError('值不能为空');
                            }
                        }
                        $idx++;
                    }
                    !isset($item['result']) && returnError('结果不能为空');
                    $idx = 0;
                    foreach ($item['result'] as $result) {
                        $idx && empty($result['formula_symbol']) && returnError('符号不能为空');
                        empty($result['value_type']) && returnError('结果值类型不能为空');
                        in_array($result['value_type'], [1,3]) && !isset($result['value']) && returnError('结果值不能为空');
                        $target_detail['target_source'] != -1 && $result['value_type'] == 1 && !in_array($result['value'], $target_detail['column_ids']) && returnError('结果指标id不在指标列表中');
                        $idx++;
                    }
                    if (isset($item['highest_result'])) {
                        $item['highest_result'] < 0 && returnError('最高值不能为负数');
                    }
                }
            }

        }
        // 定性指标
        elseif ($target_type == 2) {
            empty($target_detail['work_desc']) && returnError('详述工作不能为空');
        }
        return true;
    }

    // 获取考核指标文本
    public static function getTargetText($target_detail, $target_type, $columns_map = [])
    {
        if (!is_array($target_detail) && empty($target_detail)) return '';
        $assessment_targets_source = config::get('assessment_targets_source','data_assessment');
        $assessment_targets_source = array_column($assessment_targets_source, 'list', 'id');
        if (empty($columns_map)) {
            // 业绩指标
            if (in_array($target_detail['target_source'], [1,2])) {
                $columns = $assessment_targets_source[$target_detail['target_source']];
                $columns_map = array_column($columns, 'column_name', 'id');
            } else {
                // todo 目前还没接入的其他系统
                $columns_map = [];
            } 

        }
        // 连接符号(用于阶梯)
        $link_symbol = config::get('link_symbol', 'data_assessment');
        $link_symbol_map = array_column($link_symbol, 'name', 'id');
        // 考核指标符号(用于阶梯)
        $assessment_targets_symbol = config::get('assessment_targets_symbol', 'data_assessment');
        $assessment_targets_symbol_map = array_column($assessment_targets_symbol, 'name', 'id');
        // 公式符号(+-*/)
        $formula_symbol = config::get('formula_symbol', 'data_assessment');
        $formula_symbol_map = array_column($formula_symbol, 'name', 'id');
        // 考核指标值类型（用于公式）1个人2部门3自定义
        $assessment_targets_value_type = config::get('assessment_targets_value_type', 'data_assessment');
        $assessment_targets_value_type_map = array_column($assessment_targets_value_type, 'name', 'id');
        $assessment_targets_value_type_1 = config::get('assessment_targets_value_type_1', 'data_assessment');
        $assessment_targets_value_type_1_map = array_column($assessment_targets_value_type_1, 'name', 'id');
        $assessment_targets_value_type_2 = config::get('assessment_targets_value_type_2', 'data_assessment');
        $assessment_targets_value_type_2_map = array_column($assessment_targets_value_type_2, 'name', 'id');

        $text = '';
        // 定量指标
        if ($target_type == 1) {
            // 按公式计算
            if ($target_detail['target_method'] == 1) {
                $idx = 0;
                foreach ($target_detail['formula'] as $item) {
                    if ($idx) $text .= $formula_symbol_map[$item['symbol']];
                    // 条件
                    if ($item['type'] == 1) {
                        if ($item['value_type'] == 3) { // 自定义值
                            $text .= $item['value'];
                        } else {
                            $value_map = $item['value_type'] == 1 ? $assessment_targets_value_type_1_map : $assessment_targets_value_type_2_map;
                            $is_abs = $item['is_abs'] == 1 ? '的绝对值' : ($item['is_abs'] == 2 ? '向下取整' :  '');
                            $text .= "{$assessment_targets_value_type_map[$item['value_type']]}{$value_map[$item['value']]}{$is_abs}";
                        }
                    } // 条件组
                    elseif ($item['type'] == 2) {
                        $text .= '(';
                        $sub_idx = 0;
                        foreach ($item['list'] as $subItem) {
                            $sub_idx && $text .= $formula_symbol_map[$subItem['symbol']];
                            if ($subItem['value_type'] == 3) { // 自定义值
                                $text .= $subItem['value'];
                            } else {
                                $value_map = $subItem['value_type'] == 1 ? $assessment_targets_value_type_1_map : $assessment_targets_value_type_2_map;
                                $is_abs = $subItem['is_abs'] == 1 ? '的绝对值' : ($subItem['is_abs'] == 2 ? '向下取整' :  '');
                                $text .= "{$assessment_targets_value_type_map[$subItem['value_type']]}{$value_map[$subItem['value']]}{$is_abs}";
                            }
                            $sub_idx++;
                        }
                        $text .= ")";
                    }
                    $idx++;
                }
            } // 按阶段划分
            elseif ($target_detail['target_method'] == 2) {
                foreach ($target_detail['formula'] as $item) {
                    $idx = 0;
                    foreach ($item['rules'] as $rule) {
                        $idx && $text .= $link_symbol_map[$rule['symbol']];
                        $column_name = $target_detail['target_source'] == -1 ? '指标实际值' : $columns_map[$rule['column_id']];
                        if ($rule['target_symbol'] == 6) {
                            if (in_array($column_name, ['毛利率', '销售额环比'])) {
                                $rule['value'][0] = $rule['value'][0] . '%';
                                $rule['value'][1] = $rule['value'][1] . '%';
                            }
                            $text .= "{$column_name}{$assessment_targets_symbol_map[$rule['target_symbol']]}({$rule['value'][0]},{$rule['value'][1]}] ";
                        } else {
                            if (in_array($column_name, ['毛利率', '销售额环比'])) {
                                $rule['value'][0] = $rule['value'][0] . '%';
                                $rule['value'][1] = $rule['value'][1] . '%';
                            }
                            $text .= "{$column_name}{$assessment_targets_symbol_map[$rule['target_symbol']]}{$rule['value'][0]} ";
                        }
                    }
                    $idx++;

                    $idx = 0;
                    $text .= ", 则指标考核结果=";
                    foreach ($item['result'] as $result) {
                        $idx && $text .= $formula_symbol_map[$result['formula_symbol']];
                        if ($result['value_type'] == 1) { // 指标值
                            $text .= $columns_map[$result['value']];
                        } elseif ($result['value_type'] == 2) { // 指标分数
                            $text .= '指标分值';
                        } elseif ($result['value_type'] == 3) { // 自定义值
                            $text .= $result['value'];
                        }
                        $idx++;
                    }
                    if ($item['highest_result']) {
                        $text .= "(最高值为{$item['highest_result']})";
                    }
                }
                $text .= ";";
            }
        }
        return $text;
    }

    // 获取指标结果
    public static function getTargetResult($target_weight, $target_detail, $target_type,$target_score_type, $standard_value, $real_value, $real_value_map, $score)
    {
        if (!is_array($target_detail) && empty($target_detail)) return '';
        // 考核指标符号(用于阶梯)
        $assessment_targets_symbol = config::get('assessment_targets_symbol', 'data_assessment');
        $assessment_targets_symbol_map = array_column($assessment_targets_symbol, 'value', 'id');
        // 公式符号(+-*/)
        $formula_symbol = config::get('formula_symbol', 'data_assessment');
        $formula_symbol_map = array_column($formula_symbol, 'name', 'id');

        // 连接符号
        $link_symbol = config::get('link_symbol', 'data_assessment');
        $link_symbol_map = array_column($link_symbol, 'value', 'id');

        $result = '';
        // 定量指标
        if ($target_type == 1) {
            // 按公式计算
            if ($target_detail['target_method'] == 1) {
                $expression = '';
                $idx = 0;
                foreach ($target_detail['formula'] as $item) {
                    if ($idx) {
                        $expression .= $formula_symbol_map[$item['symbol']];
                    }
                    if ($item['type'] == 1) { // 条件
                        $expression .= self::getFormulaItemValue($item, $standard_value, $real_value, $score);
                    }
                    elseif ($item['type'] == 2) {// 条件组
                        $item_expression = '';
                        $item_idx = 0;
                        foreach ($item['list'] as $subItem) {
                            $item_idx && $item_expression .= $formula_symbol_map[$subItem['symbol']];
                            $item_expression .= self::getFormulaItemValue($subItem, $standard_value, $real_value, $score);
                            $item_idx++;
                        }
                        $expression .= "($item_expression)";
                    }
                    $idx++;
                }
                eval("\$result = $expression;");
            }
            // 按阶梯计算
            if ($target_detail['target_method'] == 2) {
                $i = 0;
                foreach ($target_detail['formula'] as $item) {
                    $idx = 0;
                    $expression = '';
                    // 阶梯的规则
                    foreach ($item['rules'] as $rule) {
                        $idx && $expression .= $link_symbol_map[$rule['symbol']];
                        // 不可拉取的指标
                        $column_id = $rule['column_id'];
                        if ($target_detail['target_source'] == -1) {
                            $column_id = -1;
                        }
                        if ($rule['target_symbol'] == 6) {
                            $expression .= "({$real_value_map[$column_id]} > {$rule['value'][0]} && {$real_value_map[$column_id]} <= {$rule['value'][1]})";
                        } else {
                            $expression .= $real_value_map[$column_id] . $assessment_targets_symbol_map[$rule['target_symbol']] . $rule['value'][0];
                        }
                        $idx++;
                    }
                    $flag = eval("return $expression;");
                    if ($flag){
                        $rule_result = '';
                        $idx_result = 0;
                        $rule_result_expression = '';
                        
                        foreach ($item['result'] as $result_item) {
                            $idx_result && $rule_result_expression .= $formula_symbol_map[$result_item['formula_symbol']];
                            if ($result_item['value_type'] == 1) { // 指标值
                                $rule_result_expression .= $real_value_map[$result_item['value']];
                            } elseif ($result_item['value_type'] == 2) { // 指标权重/分值
                                $rule_result_expression .= $target_score_type == 1 ? ($target_weight / 100) : $target_weight;
                            } elseif ($result_item['value_type'] == 3) { // 自定义值
                                $rule_result_expression .= $result_item['value'];
                            }
                            $idx_result++;
                        }
                        eval("\$rule_result = $rule_result_expression;");
                        if (!empty($rule_result)) {
                            if (!empty($item['highest_result'])) {
                                $rule_result = min($rule_result, $item['highest_result']);
                            }
                        }
                        $result = max($result, $rule_result);
                    }
                    $i++;
                }
                return $result;
            }
        }
        // 定性指标
        elseif ($target_type == 2) {
            $result = $target_score_type == 1 ? ($score['leader_score'] / 100) : $score['leader_score'];
        }

        // 没有匹配到阶梯
        if ($result == '') throw new \Exception('未匹配到阶梯');

        // 取数规则
        switch ($target_detail['calc_type']) {
            case 1: // 原始数据
                for ($i = 0; $i < $target_detail['after_point']; $i++) {
                    $result = $result * 10;
                }
                $result = floor($result);
                for ($i = 0; $i < $target_detail['after_point']; $i++) {
                    $result = $result / 10;
                }
                $result = number_format($result, $target_detail['after_point'],'.','');
                break;
            case 2: // 四舍五入
                $result = round($result, $target_detail['after_point']);
                break;
            case 3: // 向上取整
                $result = ceil($result);
                break;
            case 4: // 向下取整
                $result = floor($result);
                break;
        }

        return $result;
    }

    // 计算单个公式的值
    public static function getFormulaItemValue($item, $standard_value, $real_value, $score)
    {
        $value = 0;
        if ($item['value_type'] == 1) { // 个人
            switch ($item['value']) {
                case 1: // 指标考核值
                    $value = $standard_value;
                    break;
                case 2: // 指标实际值
                    $value = $real_value;
                    break;
                case 3: // 自评分数
                    $value = $score['self_score'];
                    break;
                case 4: // 上级评分
                    $value = $score['leader_score'];
                    break;
            }
        } elseif ($item['value_type'] == 2) { // 部门
            switch ($item['value']) {
                case 1: // 指标考核值
                    $value = $standard_value;
                    break;
                case 2: // 指标实际值
                    $value = $real_value;
                    break;
            }
        } elseif ($item['value_type'] == 3) { // 自定义值
            $value = $item['value'];
        }
        if ($item['is_abs'] == 1) {
            $value = abs($value);
        } elseif ($item['is_abs'] == 2) {
            $value = floor($value);
        }
        return $value;
    }


}