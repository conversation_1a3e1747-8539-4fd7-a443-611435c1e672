<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/17 16:06
 */

namespace  plugins\goods\controller;

use plugins\goods\common\authenticationCommon;
use plugins\goods\common\publicMethod;
use plugins\goods\form\configFrom;
use plugins\goods\form\downLoadFrom;
use plugins\goods\form\goodsColorRelationFrom;
use plugins\goods\form\goodsMattersFrom;
use plugins\goods\form\imgsRequestFrom;
use plugins\goods\form\imgsRequestTestSchemeForm;
use plugins\goods\form\messagesFrom;
use plugins\goods\models\userModel;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;
use core\lib\log;

class imgsRequestController
{
    //获取列表
    public function getList() {
        $paras_list = [
            'request_name',      // 需求名称
            'qwuser_id',         // 执行人(美工id)
            'user_id',           // 创建人id(申请人)
            'order_by',          // 排序方式，默认按ID降序
            'status',            // 状态
            'page',              // 页码
            'page_size',         // 每页数量
            'goods_id',          // 产品id，支持单个ID或ID数组
            'platform_id',       // 业务平台id，支持单个ID或ID数组
            'type',              // 需求类型：1产品需求2品牌需求3内部需求4其他，支持单个类型或类型数组
            'second_type',       // 详细类型，支持单个类型或类型数组
            'category_id',     // 类目，支持单个类目或类目数组
            'color_info',        // 颜色，支持单个颜色或颜色数组
            'nature_demand',     // 需求性质，支持单个性质或性质数组
            'brand',             // 品牌，支持单个品牌或品牌数组
            'language',          // 语言，支持单个语言或语言数组
            'has_sop',           // SOP情况，1表示有SOP，0表示无SOP
            'sample_status',     // 样品情况，1表示有样品，0表示无样品
            'model_status',      // 模型情况，1表示有模型，0表示无模型
            'is_purchase',       // 是否采购，1表示已采购，0表示未采购
            'is_file_established', // 领星建档，1表示已建档，0表示未建档
            'wp_id',             // 运营组别，支持单个组别或组别数组
            'created_time',       // 需求提交时间，格式为JSON数组[开始时间,结束时间]
            'expected_time',     // 预计时间(最晚期望时间)，格式为JSON数组[开始时间,结束时间]
            'real_expected_time', // 预计完成时间(预计交稿时间)，格式为JSON数组[开始时间,结束时间]
            'begin_time',        // 任务开始时间，格式为JSON数组[开始时间,结束时间]
            'completion_time',   // 完成时间，格式为JSON数组[开始时间,结束时间]
            'task_reviewed_time', // 审核时间，格式为JSON数组[开始时间,结束时间]
            'update_time',       // 需求更新时间，格式为JSON数组[开始时间,结束时间]
            'actual_delivery_date', // 实际发货日期，格式为JSON数组[开始时间,结束时间]
            'estimated_arrival_date', // 预估到货日期，格式为JSON数组[开始时间,结束时间]
            'group_name_id',        // 组别，支持单个组别或组别数组
            'latest_expect_time',        // 最晚期望时间
        ];
        $param = arrangeParam($_POST, $paras_list);
        imgsRequestFrom::getList($param);
    }
    //配置处理人
    public function setAgentUser() {
        $paras_list = array('id','real_expected_time','remarks','agent_info','timeout_remind_id','is_expected_completion');
        $param = arrangeParam($_POST, $paras_list);
        if (empty($param['agent_info'])) {
            SetReturn(-1,'参数有误');
        }
        imgsRequestFrom::setAgentUser($param);
        returnSuccess('配置成功');
    }
    //接收
    public function acceptTask() {
        $id = $_GET['id']??0;
        if (!$id) {
            returnError('参数错误');
        }
        imgsRequestFrom::acceptRequest($id);
        returnSuccess('','任务已接收');
    }
    //暂停
    public static function pauseed() {
        $id = $_POST['id']??0;
        if (!$id) {
            returnError('参数错误');
        }
        imgsRequestFrom::pauseed($id,1);
        returnSuccess('','需求已暂停');
    }
    //取消暂停
    public static function unPauseed() {
        $id = $_POST['id']??0;
        if (!$id) {
            returnError('参数错误');
        }
        imgsRequestFrom::pauseed($id,2);
        returnSuccess('','需求已取消暂停');
    }
    //上传文件
    public function uploadImgFile() {
        $paras_list = array('request_id','source_file','rendering_files','images','rendering_path','remarks');
//        $request_list = ['source_file'=>'源文件','images'=>'结果文件','rendering_path'=>'工程文件地址'];
        $request_list = ['source_file'=>'源文件','images'=>'结果文件'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        imgsRequestFrom::uploadImg($param);
        returnSuccess('','上传成功');
    }
    //更新文件
    public function updateImgFile() {
        $paras_list = array('request_id','source_file','rendering_files','images','rendering_path','remarks');
        $request_list = ['source_file'=>'源文件','images'=>'结果文件'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        imgsRequestFrom::updateImg($param);
        returnSuccess('','上传成功');
    }
    //提交
    public function submitTmg() {
        $paras_list = array('request_id');
        $param = arrangeParam($_POST, $paras_list);
        imgsRequestFrom::submitImgs($param);
        returnSuccess('','提交成功');
    }
    //审核
    public function checkImg() {
        $paras_list = array('request_id','remarks','reason','is_pass');
        $length_data = ['remarks'=>['name'=>'备注','length'=>500],'reason'=>['name'=>'原因','length'=>500]];
        $param = arrangeParam($_POST, $paras_list,[],$length_data);
        imgsRequestFrom::checkAppImg($param);
        SetReturn(0,'操作成功');
    }
    //催办
    public function imgRemind() {
        $paras_list = array('id','remark');
        $param = arrangeParam($_POST, $paras_list);
        $id = $param['id']??0;
        if (!$id) {
            returnError('参数错误');
        }
        imgsRequestFrom::remind($id,$param['remark']??'');
        returnSuccess('','已提醒');
    }
    //获取需求图
    public function getImages(){
        $id = (int)$_GET['request_id'];
        if (!$id) {
            SetReturn(-1,'参数有误');
        }
        $db = dbMysql::getInstance();
        $imgs_request = $db->table('imgs_request')
            ->where('id = :id',['id'=>$id])
            ->one();
        if ($imgs_request['status'] == 3) {
            $list = $db->table('imgs_request_collection')
                ->where('request_id=:request_id and is_delete = 0 and source_type=0 and is_confirm = 1',['request_id'=>$id])
                ->list();
        } else {
            $list = $db->table('imgs_request_collection')
                ->where('request_id=:request_id and is_delete = 0 and source_type=0',['request_id'=>$id])
                ->list();
        }
        $share_path = '';
        foreach ($list as $k=>$v) {
            if (!empty($v['share_path'])) {
                $share_path = $v['share_path'];
            }
            if (!empty($v['share_path']) && empty($v['url'])) {
                unset($list[$k]);
            }
        }
        returnSuccess(['rendering_path'=>$share_path,'list'=>$list]);
    }
    //批量下载图片
    public function getZipUrl() {
        $ids = $_POST['ids'];
        $ids = json_decode($ids);
        if (!count($ids)) {
            SetReturn(-1,'请存在下载文件');
        }
        $db = dbMysql::getInstance();
        $img_list = $db->table('imgs_request_collection','a')
            ->leftJoin('goods_color_relation','b','b.color_id=a.color_id and b.goods_id=a.goods_id and b.is_delete = 0 ')
            ->field('a.id,a.url,a.goods_id,b.sku')
            ->where('where a.file_type = 3 and a.is_delete = 0')
            ->whereIn('a.id',$ids)
            ->list();
        //生成压缩包
        $save_path = "/public/downLoad/imgs_request/temp/".userModel::$wid;
        if (!file_exists(SELF_FK.$save_path)) {
            mkdir(SELF_FK.$save_path, 0777, true);
        }
        $zip_url = $save_path ."/".date('YmdHis').'.zip';
        $count = publicMethod::setZip($img_list,$zip_url,$img_list[0]['goods_id'],1);
        log::downLoadLog(json_encode($ids), 3);
        if ($count) {
            returnSuccess(['url'=>$zip_url]);
        } else {
            SetReturn(-1,'未找到本地图片');
        }

    }
    //更换美工
    public function changeAgent() {
        $paras_list = array('id','agent_info','remarks');
        $param = arrangeParam($_POST, $paras_list);
        if (empty($param['agent_info'])) {
            SetReturn(-1,'参数有误');
        }
        $db = dbMysql::getInstance();
        $imgs_reques = $db->table('imgs_request')
            ->where('where id=:id',['id'=>(int)$param['id']])
            ->field('id,qwuser_id,status,goods_id,type')
            ->one();
        if ($imgs_reques['status'] != 1) {
            SetReturn(-1,'进行中的需求才可以更换美工');
        }
        $agent_info = json_decode($param['agent_info'],true);
        if ($imgs_reques['qwuser_id'] == $agent_info[0]['id']) {
            SetReturn(-1,'不可交办给当前美工');
        }
        //判断操作权限
        $pic_manage = configFrom::getConfigByName('pic_manage');
        if (!$pic_manage) {
            SetReturn(-1,'图片管理员未配置，请联系系统管理员');
        }
        $manage_info = json_decode($pic_manage,true);
        $wids = array_column($manage_info,'id');
        if (!in_array(userModel::$qwuser_id,$wids)) {
            SetReturn(-1,'非图片管理员，无权配置美工');
        }
        $db->beginTransaction();
        try {

            $qwuser_id = $agent_info[0]['id'];
            if ($imgs_reques['type'] == 1) {
                //需求修改信息
                $db->table('imgs_request');
                $db->where('where id=:id',['id'=>(int)$param['id']]);
                $db->update([
                    'qwuser_id'=>$qwuser_id,
                ]);
                //更换代办人
                $matter = $db->table('goods_matters')
                    ->where('where type = 4 and model_id=:reques_id and status <> 1 and create_type = 0',['reques_id'=>$imgs_reques['id']])
                    ->one();
                goodsMattersFrom::setChangeAgent($matter,$agent_info[0]);

                //消息通知
                $msg = goodsMattersFrom::$change_matter_msg;
                messagesFrom::senMeg($msg[0]['wids'], $msg[0]['msg'], $msg[0]['other_data'],$param['remarks']);
            } else {
                SetReturn(-1,'其他图片需求暂时不可操作');
            }
            $db->commit();
            returnSuccess('','更换成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //单个下载
    public function downLoad() {
        $id = $_POST['id'];
        if (!$id) {
            SetReturn(-1,'参数有误');
        }
        $db = dbMysql::getInstance();
        $img = $db->table('imgs_request_collection')
            ->field('id,url,goods_id')
            ->where('where id=:id',['id'=>$id])
            ->one();
        if (!$img) {
            SetReturn(-1,'未找到图片');
        }
        $base64Data = downLoadFrom::getdownLoadBase64Encode($img['url'],$img['goods_id'],1);
        log::downLoadLog($id, 3);
        returnSuccess(['data'=>$base64Data]);
    }
    //图片资源申请（新增修改）
    public function applyForImageResource() {
        $paras_list = [
            'id',                 // 要修改的记录 ID
            'request_name',       // 需求名称
            'type',               // 需求类型
            'platform_id',        // 业务平台
            'brand_id',              // 品牌
            'country_code',             // 站点
            'goods_id',           // 商品ID
            'category_id',      // 类目
            'color_info',         // 颜色
            'nature_demand',      // 需求性质
            'wp_id',         // 组别
            'language',           // 语言
            'latest_expect_time',      // 预计时间（最晚期望时间）
            'has_sop',            // SOP 路径
            'description',        // 需求描述
        ];

        $param = arrangeParam($_POST, $paras_list);
        imgsRequestFrom::applyForImageResource($param);
    }
    //审核
    public function audit() {
        $paras_list = [
            'id',                 // 要修改的记录 ID
            'is_check',             // 审核是否通过
            'check_remarks',            // 审核备注
        ];

        $param = arrangeParam($_POST, $paras_list);
        imgsRequestFrom::audit($param);
    }
    //获取产品列表
    public function getProductListWithCategoryAndColor()
    {
        try {
            $db = dbMysql::getInstance();
            
            // 1. 获取所有产品基本信息
            $db->table('goods_new as g')
               ->leftJoin('goods_color_relation', 'r', 'r.goods_id = g.id AND r.is_delete = 0')
               ->leftJoin('goods_color', 'c', 'c.id = r.color_id AND c.is_delete = 0')
               ->where('g.is_delete = 0')
               ->field('g.id, g.goods_name, g.cat_id, r.has_handset, c.id as color_id, c.color_name, c.color_name_en')
               ->order('g.id desc');

            $data = $db->list();
            if (empty($data)) {
                returnSuccess([]);
            }

            // 2. 提取所有分类ID
            $allCateIds = [];
            foreach ($data as $row) {
                $catIdArr = json_decode($row['cat_id'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($catIdArr)) {
                    foreach ($catIdArr as $catId) {
                        $allCateIds[] = (int)$catId;
                    }
                }
            }
            $allCateIds = array_unique($allCateIds);

            // 3. 一次性获取所有类目信息
            $cateMap = [];
            if (!empty($allCateIds)) {
                $cateList = $db->table('goods_cate')
                              ->field('id, cate_name')
                              ->whereIn('id', $allCateIds)
                              ->where('is_delete = 0')
                              ->list();
                
                foreach ($cateList as $cate) {
                    $cateMap[$cate['id']] = $cate['cate_name'];
                }
            }

            // 4. 组装最终结果
            $result = [];
            foreach ($data as $row) {
                $id = $row['id'];
                if (!isset($result[$id])) {
                    $catIdArr = json_decode($row['cat_id'], true);
                    $categoryNames = [];
                    if (json_last_error() === JSON_ERROR_NONE && is_array($catIdArr)) {
                        foreach ($catIdArr as $catId) {
                            if (isset($cateMap[$catId])) {
                                $categoryNames[] = $cateMap[$catId];
                            }
                        }
                    }

                    $result[$id] = [
                        'id' => $id,
                        'goods_name' => $row['goods_name'],
                        'cat_id' => $row['cat_id'],
                        'category_names' => $categoryNames,
                        'colors' => [],
                    ];
                }

                // 只添加有效的颜色信息，并确保颜色不重复
                if (!empty($row['color_id'])) {
                    $colorExists = false;
                    foreach ($result[$id]['colors'] as $existingColor) {
                        if ($existingColor['color_id'] == $row['color_id']) {
                            $colorExists = true;
                            break;
                        }
                    }
                    
                    if (!$colorExists) {
                        $result[$id]['colors'][] = [
                            'color_id' => $row['color_id'],
                            'color_name' => $row['color_name'],
                            'color_name_en' => $row['color_name_en'],
                            'has_handset' => $row['has_handset'],
                        ];
                    }
                }
            }

            // 5. 对颜色数组按 color_id 排序
            foreach ($result as &$item) {
                usort($item['colors'], function($a, $b) {
                    return $a['color_id'] - $b['color_id'];
                });
            }

            returnSuccess(array_values($result));
        } catch (\Exception $e) {
            returnError('获取产品列表失败：' . $e->getMessage());
        }
    }
    //站点列表
    public function getCountryList() {
        // 获取数据库实例
        $db = dbMysql::getInstance();

        $data = $db->table('market')
        ->field('id, country, country_e, code')
        ->list();
        //国家为无的站点
        $data[] = [
            'id' => 0,
            'country' => '无',
            'country_e' => 'No',
            'code' => 'NONE',
        ];
        // 返回成功的响应
        returnSuccess($data);
    }
    //获取品牌列表
    public function getBrandList() {
        // 获取数据库实例
        $db = dbMysql::getInstance();

        $data = $db->table('brands')
        ->field('id, name')
        ->list();
        // 返回成功的响应
        returnSuccess($data);
    }
    //删除
    public function delete() {
        $paras_list = [
            'id',
        ];
        $param = arrangeParam($_POST, $paras_list);
        imgsRequestFrom::delete($param);
    }
    //详情获取
    public function getDetail() {
        $paras_list = [
            'id',
        ];
        $param = arrangeParam($_POST, $paras_list);
        imgsRequestFrom::getDetail($param);
    }
    //相关人员编辑
    public function updateParticipant() {
        $paras_list = ['request_id','type','old_user_id','user_id','user_ids'];
        $param = arrangeParam($_POST, $paras_list);
        imgsRequestFrom::updateParticipant($param);
    }
    //更新信息
    public function update() {
        // 获取参数
        $paras_list = [
            'id',                   // 需求ID
            'user_id',             // 申请人
            'nature_demand',                // 需求性质
            'qwuser_id',           // 作图美工
            'real_expected_time',  // 预计交稿时间
            'alter_note',          // 变更说明
            'sample_status',       // 样品情况
            'model_status',        // 模型情况
            'is_purchase',         // 是否采购
            'is_file_established', // 领星建档
            'actual_delivery_date',// 实际发货日期
            'estimated_arrival_date',// 预估到货日期
            'first_batch_date',// 首批物流
            'update_remark'        // 备注
        ];
        
        $param = arrangeParam($_POST, $paras_list);
        imgsRequestFrom::update($param);
    }
    //图片测试
    public function imgTest() {
        // 获取参数
        $paras_list = [
            'request_id',                  // 需求ID
            'channel_id',          // 测试渠道id
            'test_scheme',        // 测试方案 imgs图片集，push_id,remark
            'begin_time',          // 开始是时间
            'end_time',            // 统计时间
            'conclusion',          // 结论
        ];
        $param = arrangeParam($_POST, $paras_list);
        $param['channel_id'] = (int)$param['channel_id'];
        //验证channel_id不等于0,且需要等于数字
        if ($param['channel_id'] == 0) {
            returnError('推送ID格式错误');
        }

        $test_scheme = json_decode(!empty($param['test_scheme'])?$param['test_scheme']:'[]',true);
        if (count($test_scheme) > 5) {
            returnError('测试方案不得超过5个');
        }
        if (count($test_scheme) == 0) {
            returnError('测试方案必传');
        }
        foreach ($test_scheme as $v) {
            if (!count($v['imgs'])) {
                returnError('请上传方案图片');
            }
            if (empty($v['push_id'])) {
                returnError('请填写方案推送ID');
            }
        }
        $param['test_scheme'] = $test_scheme;
        $begin_time = strtotime($param['begin_time']);
        $end_time = strtotime($param['end_time']);
        if ($end_time <= $begin_time) {
            returnError('统计时间需要大于结束时间');
        }
        imgsRequestFrom::saveTestScheme($param);
        returnSuccess('','操做成功');
    }
    //测试详情
    public function imgTestDetail() {
        // 获取参数
        $request_id = $_POST['request_id']??0;
        if (!$request_id) {
            returnError('参数有误');
        }
        $data = imgsRequestFrom::testSchemeDetail($request_id);
        returnSuccess($data);
    }
    //上传文件
    public function uploadFile() {
        $paras_list = [
            'id',
            'source_file',      // 源文件
            'project_file',     // 工程文件
            'result_file',      // 结果文件
            'upload_file_remark' // 上传文件备注
        ];

        $param = arrangeParam($_POST, $paras_list);
        imgsRequestFrom::uploadFile($param);

    }
    //下载指定文件
    public function downLoadFile() {
        $paras_list = [
            'ids', // 文件ID数组
        ];
        $param = arrangeParam($_POST, $paras_list);
        imgsRequestFrom::downLoadFile($param);
    }
    //获取组名列表
    public function getGroupList() {
        // 获取数据库实例
        $db = dbMysql::getInstance();

        $data = $db->table('imgs_request_distributor')
        ->field('id, group_name')
        ->list();
        // 返回成功的响应
        returnSuccess($data);
    }
    //保存用户列配置
    public static function saveUserColumn()
    {
        $paras_list = [
            'key_list',
            'type'
        ];
        $param = arrangeParam($_POST, $paras_list);

        //获取用户ID
        $user_id = userModel::$qwuser_id;

        $db = dbMysql::getInstance();

        // 准备更新的数据，只允许 key_list 和 type 这两个字段更新
        $data = [
            'key_list'    => $param['key_list'],
            'type'        => $param['type'],
            'update_time' => date('Y-m-d H:i:s')
        ];

        // 根据唯一约束 user_id 和 type 判断记录是否存在
        $existing = $db->table('user_table_keys_setting')
            ->where('user_id = :user_id AND type = :type', [
                'user_id' => $user_id,
                'type'    => $data['type']
            ])
            ->one();
        //启动事务
        $db->beginTransaction();
        if ($existing) {
            // 存在则更新记录
            $db->table('user_table_keys_setting')
                ->where('user_id = :user_id AND type = :type', [
                    'user_id' => $user_id,
                    'type'    => $data['type']
                ])
                ->update($data);
        } else {
            // 不存在则插入新记录，并设置 created_time
            $data['user_id'] = $user_id;
            $data['created_time'] = date('Y-m-d H:i:s');
            $db->table('user_table_keys_setting')->insert($data);
        }
        // 提交事务
        $db->commit();
        returnSuccess ('','配置成功');
    }
    /**
     * 获取用户列配置，若不存在则根据 type 返回默认列表
     * @return void Json 返回数组
     */
    public static function getUserColumn()
    {
        // 获取 type 参数
        $params = arrangeParam($_POST, ['type']);
        $type   = $params['type'];

        $db = dbMysql::getInstance();
        $row = $db->table('user_table_keys_setting')
            ->field('key_list')
            ->where('user_id = :user_id and type = :type', ['user_id' => userModel::$qwuser_id, 'type' => $type])
            ->one();

        if ($row && $row['key_list']) {
            $list = json_decode($row['key_list'], true);
        } else {
            // 默认列表
            $list = self::getDefaultListByType($type);
        }

        returnSuccess($list);
    }

    /**
     * 根据 type 返回默认列配置
     * @param int $type
     * @return array
     */
    private static function getDefaultListByType(int $type): array
    {
        if ($type === 1) {
            return [
                "id",
                "request_name",
                "type",
                "platform_id",
                "country_name",
                "goods_name",
                "category_names",
                "status",
                "color_names",
                "brand_name",
                "qw_group_name",
                "language_code",
                "has_sop",
                "sample_status",
                "model_status",
                "is_purchase",
                "is_file_established",
                "created_wname",
                "actual_delivery_date",
                "first_batch_date",
                "estimated_arrival_date",
                "group_name",
                "agent_wname",
                "manage_names",
                "chat_name",
                "description",
                "created_time",
                "latest_expect_time",
                "real_expected_time",
                "assigned_time",
                "begin_time",
                "task_reviewed_time",
                "completion_time",
                "update_time"
            ];
        } elseif ($type === 2) {
            return [
                "id",
                "type_name",
                "platform_name",
                "goods_name",
                "cat_data",
                "request_name",
                "upload_wname",
                "created_time",
                "file_type_name",
                "share_path"
            ];
        }
        return [];
    }






}