<?php

namespace plugins\logistics\models;

use core\lib\db\dbErpMysql;
use core\lib\db\dbLMysql;
use core\lib\db\dbAfMysql;
use core\lib\log;

/**
 * FBA销量统计模型
 * 处理从利润统计数据计算销量并更新到FBA库存汇总表
 */
class fbaSalesStatisticsModel
{
    private $erpDb;
    private $logisticsDb;
    private $afDb;
    
    // 利润统计表
    private $profitTable = 'lingxing_profit_statistics_msku_2025';
    // FBA库存汇总表
    private $summaryTable = 'fba_storage_summary';

    public function __construct()
    {
        $this->erpDb = dbErpMysql::getInstance();
        $this->logisticsDb = dbLMysql::getInstance();
        $this->afDb = dbAfMysql::getInstance();
    }

    /**
     * 更新FBA库存汇总表的销量数据
     * @param string $syncDate 同步日期
     * @param int $batchSize 批处理大小
     * @return array 处理结果
     */
    public function updateFbaSalesData($syncDate, $batchSize = 100)
    {
        $result = [
            'success_count' => 0,
            'error_count' => 0,
            'error_list' => []
        ];

        try {
            log::lingXingApi('FbaSalesStatistics')->info("开始更新FBA销量数据，同步日期: {$syncDate}");

            // 获取需要更新的FBA库存汇总记录
            $summaryRecords = $this->getFbaSummaryRecords($syncDate);
            
            if (empty($summaryRecords)) {
                log::lingXingApi('FbaSalesStatistics')->info("没有找到需要更新的FBA库存汇总记录");
                return $result;
            }

            log::lingXingApi('FbaSalesStatistics')->info("找到 " . count($summaryRecords) . " 条FBA库存汇总记录需要更新");

            // 获取normal_prepare配置
            $normalPrepareConfig = $this->getNormalPrepareConfig();

            // 分批处理
            $batches = array_chunk($summaryRecords, $batchSize);
            
            foreach ($batches as $batchIndex => $batch) {
                log::lingXingApi('FbaSalesStatistics')->info("处理第 " . ($batchIndex + 1) . " 批，共 " . count($batch) . " 条记录");
                
                $batchResult = $this->processBatch($batch, $syncDate, $normalPrepareConfig);
                
                $result['success_count'] += $batchResult['success_count'];
                $result['error_count'] += $batchResult['error_count'];
                $result['error_list'] = array_merge($result['error_list'], $batchResult['error_list']);
            }

            log::lingXingApi('FbaSalesStatistics')->info("FBA销量数据更新完成，成功: {$result['success_count']}条，失败: {$result['error_count']}条");

        } catch (\Exception $e) {
            log::lingXingApi('FbaSalesStatistics')->error('更新FBA销量数据异常: ' . $e->getMessage());
            throw $e;
        }

        return $result;
    }

    /**
     * 获取FBA库存汇总记录
     * @param string $syncDate 同步日期
     * @return array
     */
    private function getFbaSummaryRecords($syncDate)
    {
        return $this->logisticsDb->table($this->summaryTable)
            ->where('sync_date = :sync_date AND is_deleted = 0', ['sync_date' => $syncDate])
            ->field('id, asin, sku, site_code, sid, product_stage, level_type')
            ->list();
    }

    /**
     * 获取normal_prepare配置
     * @return array
     */
    private function getNormalPrepareConfig()
    {
        $config = $this->logisticsDb->table('config')
            ->where('key_name = :key_name', ['key_name' => 'normal_prepare'])
            ->one();

        if (empty($config)) {
            log::lingXingApi('FbaSalesStatistics')->warning('未找到normal_prepare配置，使用默认配置');
            return $this->getDefaultNormalPrepareConfig();
        }

        $configData = json_decode($config['data'], true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            log::lingXingApi('FbaSalesStatistics')->error('normal_prepare配置JSON解析失败: ' . json_last_error_msg());
            return $this->getDefaultNormalPrepareConfig();
        }

        return $configData;
    }

    /**
     * 获取默认的normal_prepare配置
     * @return array
     */
    private function getDefaultNormalPrepareConfig()
    {
        return [
            [
                "type" => "1",
                "sales_config" => ["sales_7" => "60", "sales_14" => "20", "sales_30" => "20"],
                "data_range" => [
                    ["country" => "US", "min_value" => 1, "max_value" => 3, "normal_value" => 2],
                    ["country" => "UK", "min_value" => 1, "max_value" => 3, "normal_value" => 2],
                    ["country" => "DE", "min_value" => 1, "max_value" => 3, "normal_value" => 2],
                    ["country" => "FR", "min_value" => 1, "max_value" => 3, "normal_value" => 2],
                    ["country" => "IT", "min_value" => 1, "max_value" => 3, "normal_value" => 2],
                    ["country" => "ES", "min_value" => 1, "max_value" => 3, "normal_value" => 2],
                    ["country" => "CA", "min_value" => 1, "max_value" => 3, "normal_value" => 2],
                    ["country" => "JP", "min_value" => 1, "max_value" => 3, "normal_value" => 2]
                ]
            ],
            [
                "type" => "2", 
                "sales_config" => ["sales_7" => "50", "sales_14" => "25", "sales_30" => "25"],
                "data_range" => [
                    ["country" => "US", "min_value" => 1, "max_value" => 3, "normal_value" => 2],
                    ["country" => "UK", "min_value" => 1, "max_value" => 3, "normal_value" => 2],
                    ["country" => "DE", "min_value" => 1, "max_value" => 3, "normal_value" => 2],
                    ["country" => "FR", "min_value" => 1, "max_value" => 3, "normal_value" => 2],
                    ["country" => "IT", "min_value" => 1, "max_value" => 3, "normal_value" => 2],
                    ["country" => "ES", "min_value" => 1, "max_value" => 3, "normal_value" => 2],
                    ["country" => "CA", "min_value" => 1, "max_value" => 3, "normal_value" => 2],
                    ["country" => "JP", "min_value" => 1, "max_value" => 3, "normal_value" => 2]
                ]
            ]
        ];
    }

    /**
     * 处理一批FBA库存汇总记录
     * @param array $batch 批次数据
     * @param string $syncDate 同步日期
     * @param array $normalPrepareConfig 配置数据
     * @return array 处理结果
     */
    private function processBatch($batch, $syncDate, $normalPrepareConfig)
    {
        $result = [
            'success_count' => 0,
            'error_count' => 0,
            'error_list' => []
        ];

        $this->logisticsDb->beginTransaction();

        try {
            foreach ($batch as $record) {
                try {
                    // 计算销量数据
                    $salesData = $this->calculateSalesData($record, $syncDate, $normalPrepareConfig);

                    // 更新FBA库存汇总记录
                    $updateResult = $this->updateSummaryRecord($record['id'], $salesData);

                    if ($updateResult) {
                        $result['success_count']++;
                    } else {
                        $result['error_count']++;
                        $result['error_list'][] = [
                            'record_id' => $record['id'],
                            'error' => '更新记录失败'
                        ];
                    }

                } catch (\Exception $e) {
                    $result['error_count']++;
                    $result['error_list'][] = [
                        'record_id' => $record['id'],
                        'error' => $e->getMessage()
                    ];
                    log::lingXingApi('FbaSalesStatistics')->error("处理记录失败，ID: {$record['id']}，错误: " . $e->getMessage());
                }
            }

            $this->logisticsDb->commit();

        } catch (\Exception $e) {
            $this->logisticsDb->rollBack();
            throw $e;
        }

        return $result;
    }

    /**
     * 计算销量数据
     * @param array $record FBA库存汇总记录
     * @param string $syncDate 同步日期
     * @param array $normalPrepareConfig 配置数据
     * @return array 销量数据
     */
    private function calculateSalesData($record, $syncDate, $normalPrepareConfig)
    {
        // 获取国家代码
        $countryCode = $this->getCountryCodeBySiteCode($record['site_code']);

        // 计算各周期销量均值
        $sales7DaysAvg = $this->calculatePeriodSalesAvg($record, $syncDate, 7);
        $sales14DaysAvg = $this->calculatePeriodSalesAvg($record, $syncDate, 14);
        $sales30DaysAvg = $this->calculatePeriodSalesAvg($record, $syncDate, 30);

        // 获取前10天销量明细
        $sales10DaysDetail = $this->getSales10DaysDetail($record, $syncDate);

        // 计算日均销量
        $dailyAvgSales = $this->calculateDailyAvgSales(
            $sales7DaysAvg,
            $sales14DaysAvg,
            $sales30DaysAvg,
            $record['product_stage'],
            $countryCode,
            $normalPrepareConfig
        );

        return [
            'sales_7days_qty' => round($sales7DaysAvg * 7), // 7天总销量
            'sales_14days_qty' => round($sales14DaysAvg * 14), // 14天总销量
            'sales_30days_qty' => round($sales30DaysAvg * 30), // 30天总销量
            'sales_10days_detail' => json_encode($sales10DaysDetail, JSON_UNESCAPED_UNICODE),
            'daily_avg_sales_qty' => round($dailyAvgSales, 2)
        ];
    }

    /**
     * 根据站点代码获取国家代码
     * @param string $siteCode 站点代码
     * @return string 国家代码
     */
    private function getCountryCodeBySiteCode($siteCode)
    {
        $siteCountryMap = [
            'US' => 'US',
            'UK' => 'UK',
            'DE' => 'DE',
            'FR' => 'FR',
            'IT' => 'IT',
            'ES' => 'ES',
            'CA' => 'CA',
            'JP' => 'JP'
        ];

        return $siteCountryMap[$siteCode] ?? $siteCode;
    }

    /**
     * 计算指定周期的销量均值
     * @param array $record FBA库存汇总记录
     * @param string $syncDate 同步日期
     * @param int $days 天数
     * @return float 销量均值
     */
    private function calculatePeriodSalesAvg($record, $syncDate, $days)
    {
        $startDate = date('Y-m-d', strtotime($syncDate . " -{$days} days"));
        $endDate = date('Y-m-d', strtotime($syncDate . " -1 day"));

        // 构建查询条件
        $conditions = $this->buildProfitQueryConditions($record);

        $query = $this->erpDb->table($this->profitTable)
            ->where('dataDate BETWEEN :startDate AND :endDate', [
                'startDate' => $startDate,
                'endDate' => $endDate
            ]);

        // 添加匹配条件
        foreach ($conditions as $field => $value) {
            if (!empty($value)) {
                $query->andWhere("{$field} = :{$field}", [$field => $value]);
            }
        }

        $result = $query->field('SUM(totalSalesQuantity) as total_quantity, COUNT(DISTINCT dataDate) as days_count')
            ->one();

        $totalQuantity = $result['total_quantity'] ?? 0;
        $daysCount = $result['days_count'] ?? 1;

        // 计算均值，避免除零
        return $daysCount > 0 ? ($totalQuantity / $daysCount) : 0;
    }

    /**
     * 获取前10天销量明细
     * @param array $record FBA库存汇总记录
     * @param string $syncDate 同步日期
     * @return array 前10天销量明细
     */
    private function getSales10DaysDetail($record, $syncDate)
    {
        $startDate = date('Y-m-d', strtotime($syncDate . " -10 days"));
        $endDate = date('Y-m-d', strtotime($syncDate . " -1 day"));

        // 构建查询条件
        $conditions = $this->buildProfitQueryConditions($record);

        $query = $this->erpDb->table($this->profitTable)
            ->where('dataDate BETWEEN :startDate AND :endDate', [
                'startDate' => $startDate,
                'endDate' => $endDate
            ]);

        // 添加匹配条件
        foreach ($conditions as $field => $value) {
            if (!empty($value)) {
                $query->andWhere("{$field} = :{$field}", [$field => $value]);
            }
        }

        $results = $query->field('dataDate, SUM(totalSalesQuantity) as daily_quantity')
            ->groupBy('dataDate')
            ->orderBy('dataDate DESC')
            ->list();

        $details = [];
        foreach ($results as $row) {
            $details[] = [
                'date' => $row['dataDate'],
                'quantity' => (int)$row['daily_quantity']
            ];
        }

        return $details;
    }

    /**
     * 构建利润统计表查询条件
     * @param array $record FBA库存汇总记录
     * @return array 查询条件
     */
    private function buildProfitQueryConditions($record)
    {
        $conditions = [];

        // 根据汇总层级构建不同的查询条件
        switch ($record['level_type']) {
            case 1: // 店铺级 (asin+sku+country_code+sid)
                $conditions['asin'] = $record['asin'];
                $conditions['sid'] = $record['sid'];
                $conditions['countryCode'] = $this->getCountryCodeBySiteCode($record['site_code']);
                if (!empty($record['sku'])) {
                    $conditions['msku'] = $record['sku']; // 假设sku对应msku
                }
                break;

            case 2: // 站点级 (asin+sku+country_code)
                $conditions['asin'] = $record['asin'];
                $conditions['countryCode'] = $this->getCountryCodeBySiteCode($record['site_code']);
                if (!empty($record['sku'])) {
                    $conditions['msku'] = $record['sku'];
                }
                break;

            case 3: // SKU级 (sku+country_code)
                $conditions['countryCode'] = $this->getCountryCodeBySiteCode($record['site_code']);
                if (!empty($record['sku'])) {
                    $conditions['msku'] = $record['sku'];
                }
                break;

            case 4: // ASIN级 (asin+country_code)
                $conditions['asin'] = $record['asin'];
                $conditions['countryCode'] = $this->getCountryCodeBySiteCode($record['site_code']);
                break;
        }

        return $conditions;
    }

    /**
     * 计算日均销量
     * @param float $sales7DaysAvg 7天销量均值
     * @param float $sales14DaysAvg 14天销量均值
     * @param float $sales30DaysAvg 30天销量均值
     * @param int $productStage 产品阶段
     * @param string $countryCode 国家代码
     * @param array $normalPrepareConfig 配置数据
     * @return float 日均销量
     */
    private function calculateDailyAvgSales($sales7DaysAvg, $sales14DaysAvg, $sales30DaysAvg, $productStage, $countryCode, $normalPrepareConfig)
    {
        // 查找匹配的配置
        $config = null;
        foreach ($normalPrepareConfig as $configItem) {
            if ($configItem['type'] == $productStage) {
                $config = $configItem;
                break;
            }
        }

        // 如果没有找到匹配的配置，使用默认配置（type=1）
        if (!$config) {
            $config = $normalPrepareConfig[0] ?? null;
        }

        if (!$config || !isset($config['sales_config'])) {
            log::lingXingApi('FbaSalesStatistics')->warning("未找到产品阶段 {$productStage} 的配置，使用默认权重");
            // 使用默认权重
            return ($sales7DaysAvg * 0.6) + ($sales14DaysAvg * 0.2) + ($sales30DaysAvg * 0.2);
        }

        $salesConfig = $config['sales_config'];
        $weight7 = (float)($salesConfig['sales_7'] ?? 60) / 100;
        $weight14 = (float)($salesConfig['sales_14'] ?? 20) / 100;
        $weight30 = (float)($salesConfig['sales_30'] ?? 20) / 100;

        // 计算加权日均销量
        $dailyAvgSales = ($sales7DaysAvg * $weight7) + ($sales14DaysAvg * $weight14) + ($sales30DaysAvg * $weight30);

        log::lingXingApi('FbaSalesStatistics')->debug("日均销量计算: 7天均值={$sales7DaysAvg}, 14天均值={$sales14DaysAvg}, 30天均值={$sales30DaysAvg}, 权重=({$weight7},{$weight14},{$weight30}), 结果={$dailyAvgSales}");

        return $dailyAvgSales;
    }

    /**
     * 更新FBA库存汇总记录
     * @param int $recordId 记录ID
     * @param array $salesData 销量数据
     * @return bool 更新结果
     */
    private function updateSummaryRecord($recordId, $salesData)
    {
        return $this->logisticsDb->table($this->summaryTable)
            ->where('id = :id', ['id' => $recordId])
            ->update($salesData);
    }
}
