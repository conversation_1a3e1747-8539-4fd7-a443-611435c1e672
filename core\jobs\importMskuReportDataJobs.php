<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/24 9:57
 */

namespace core\jobs;

use core\lib\config;
use financial\form\customColumnForm;
use financial\form\mskuReportForm;
use financial\form\runShellTaskForm;
use financial\models\mskuReportModel;
use Rap2hpoutre\FastExcel\FastExcel;

class importMskuReportDataJobs
{
    public string $unqueid = '';
    public string $key = '';//到表得redis_key
    public int $page_size = 1000;
    public function __construct($key){
        $this->key = $key;
        $this->unqueid = uniqid();
    }

    public function task(){
        $redis = (new \core\lib\predisV())::$client;
        if (!$redis->exists($this->key)) {
            return true;
        }
        $export_data = json_decode($redis->get($this->key),true);
        $offset = $export_data['offset'];
        $date = $export_data['date'];
        $excel_src = $export_data['excel_src'];
        $import_id = $export_data['import_id'];
        $excel_data = (new FastExcel)->import($excel_src);
        $data = $excel_data->toArray();
        //分页数据
        $slice = array_slice($data, $offset, $this->page_size, true);
        if (count($slice)) {
            //整理数据
            $data_new = [];
            $import_key_list = mskuReportModel::$import_key_list;
            foreach ($slice as $k=>$v) {
                $new_item = [];
                foreach ($v as $key=>$v1) {
                    if (isset($import_key_list[$key])) {
                        $new_item[$import_key_list[$key]] = $v1;
                    }
                }
                $data_new[] = $new_item;
            }
            //保存数据
            $mskuReportModel = new mskuReportForm($date);
            $res = $mskuReportModel->importSellerReportData($data_new,$date,$import_id);
            $error_list = $res['error_list'];
            $export_data['success_count'] += $res['success_count'];
            $export_data['error_count'] += count($error_list);
            $export_data['offset'] += $this->page_size;
            //记录保存
            $mskuReportModel->saveReportLog($error_list,$export_data['success_count'],$export_data['error_count'],$import_id);
        }
        $total_import = $export_data['success_count'] + $export_data['error_count'];
        if ( $total_import < $export_data['total']) {
            //redis更新
            $redis->set($this->key,json_encode($export_data));
            $redis->expire($this->key,60*60);
            $queue_key = config::get('delay_queue_key', 'app');
            $task = new importMskuReportDataJobs($this->key); // 创建任务类实例
            $redis->zAdd($queue_key, [], 0, serialize($task));
        } else {
            $redis->del($this->key);
        }
    }
}