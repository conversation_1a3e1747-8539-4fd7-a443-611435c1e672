<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/25 17:08
 */

namespace  plugins\goods\controller;

use plugins\goods\form\goodsAttrBookFrom;
use plugins\goods\form\goodsProjectFileFrom;
use plugins\goods\form\goodsProjectFrom;
use plugins\goods\form\goodsQualityBookFrom;
use plugins\goods\models\userModel;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;
use core\lib\log;

class goodsQualityBookController
{
    //获取规格书识别数据
    public function getAttrData() {
        $goods_id = (int)$_POST['goods_id'];
        $db = dbMysql::getInstance();
        $data = $db->table('goods_attr_book')
            ->where('where goods_id=:goods_id',['goods_id'=>$goods_id])
            ->one();
        if (!$data) {
            $attr_data = goodsAttrBookFrom::getAtrrData($goods_id,0);
        } else {
            $attr_data = json_decode($data['attr_data'],true);
        }
        foreach ($attr_data as &$row) {
            $row['data'] = [
                'imgs'=>[],
                'content'=>$row['data'],

            ];
            $row['type'] = 1;
        }
        returnSuccess($attr_data);
    }
    //获取详情
    public function getQualityBookDetail(){
        $project_id = (int)$_GET['project_id'];
        $db = dbMysql::getInstance();
        $data = $db->table('goods_quality_book')
            ->where('where project_id=:project_id',['project_id'=>$project_id])
            ->field('id,qwuser_id,quality_data,submit_time')
            ->one();
        if ($data) {
            $data['quality_data'] = json_decode($data['quality_data'],true);
        } else {
            $data = '';
        }
        returnSuccess($data);
    }
    //规格书数保存
    public function saveQualityBook(){
        $paras_list = array('project_id','quality_data','node_index','event_index','event_type');
        $request_list = ['project_id' => '项目ID','quality_data'=>'检测标准书内容'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $project_id = (int)$param['project_id'];
        $data = goodsProjectFrom::verifyEvenRequestData($param);
        $project = $data['project'];
        $quality_data = json_decode($param['quality_data'],true);
        $db = dbMysql::getInstance();
        $db->beginTransaction();
        try {
            //保存数据
            $quality_book = $db->table('goods_quality_book')
                ->where('where project_id=:project_id',['project_id'=>$project_id])
                ->one();
            if ($quality_book) {
                $db->table('goods_quality_book')
                    ->where('where id=:id',['id'=>$quality_book['id']])
                    ->update([
                        'quality_data'=>json_encode($quality_data,JSON_UNESCAPED_UNICODE),
                        'updated_time'=>date('Y-m-d H:i:s')
                    ]);
            } else {
                $db->table('goods_quality_book')
                    ->insert([
                        'goods_id'=>$project['goods_id'],
                        'project_id'=>$project_id,
                        'qwuser_id'=>userModel::$qwuser_id,
                        'quality_data'=>json_encode($quality_data,JSON_UNESCAPED_UNICODE),
                        'created_time'=>date('Y-m-d H:i:s')
                    ]);
            }
            //记录
            goodsQualityBookFrom::setEditLog($quality_book,$quality_data,$project);
            $db->commit();
            returnSuccess('','保存成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }

        returnSuccess('','保存成功');
    }
    //下载
    public function downLoadQualityBook(){
        $project_id = (int)$_POST['project_id'];
        $db = dbMysql::getInstance();
        $file_data = $db->table('goods_quality_book')
            ->where('where project_id=:project_id',['project_id'=>$project_id])
            ->one();
        if (!$file_data) {
            SetReturn(-1,'未找到数据');
        }
        $project = $db->query('select flow_path_id from oa_goods_project where id=:project_id',['project_id'=>$project_id]);
        if ($file_data['url']) {
            $path = SELF_FK.$file_data['url'];
        } else {
            $goods_info = $db->query('select goods_name,manage_info from oa_goods_new where id=:goods_id',['goods_id'=>$file_data['goods_id']]);
            $pdf_data = goodsQualityBookFrom::setQualityBookPdf($file_data,$goods_info);

            //保存提交事件和地址
            $db->table('goods_quality_book')
                ->where('where id=:id',['id'=>$file_data['id']])
                ->update([
                    'url'=>$pdf_data['path'],
                    'submit_time'=>date('Y-m-d H:i:s'),
                ]);
            goodsProjectFileFrom::saveProjectFile('检测标准书',$project['flow_path_id'],$project_id,5,$pdf_data['path'],$goods_info['id']);
        }
        $data = file_get_contents($path);
        $base64Data = base64_encode($data);
        //记录下载日志
        log::downLoadLog($file_data['id'], 0);
        SetReturn(0,'',[
            'filename'=>'产品检测报告',
            'base64Data'=>$base64Data,
        ]);
    }
}