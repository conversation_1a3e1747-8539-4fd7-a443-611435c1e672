<?php

namespace financial\controller;

use core\jobs\mskuOriginalMskuDataJobs;
use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\rediskeys;
use financial\form\checkoutForm;
use financial\form\originalDataForm;
use financial\models\checkoutModel;
use financial\models\mskuReportModel;
use financial\models\originaldataModel;



class originaldataController
{
    //拉取原始数据列表
    public function getList()
    {
        // 定义所需参数列表
        $paras_list = ['page', 'page_size', 'time', 'types', 'state', 'Search'];
        $request_list = ['time' => '时间年月'];
        // 组织参数
        $param = arrangeParam($_POST, $paras_list,$request_list);
        // 设置默认分页参数
        $param['page'] = !empty($param['page']) ? (int)$param['page'] : 1;
        $param['page_size'] = !empty($param['page_size']) ? (int)$param['page_size'] : 10;
        //处理时间
        $list = originalDataForm::getList($param);
        $syn_time = originalDataForm::getSynTime($param['time']);
        $list['syn_time'] = $syn_time;
        //同步时间获取
        returnSuccess($list, '获取成功');
    }
    //编辑
    public function edit()
    {
        // 定义所需参数列表
        $paras_list = ['data', 'yunying_id','time'];
        // 组织参数
        $param = arrangeParam($_POST, $paras_list);

        $year = substr($param['time'], 0, 4);
        $yunying_id = intval($param['yunying_id']);
        $data = json_decode($param['data'], true);

        //查询修改原表
        $db = dbFMysql::getInstance();
        $db->table('msku_report_data_' . $year);
        $field = ['sid', 'asin', 'parentAsin', 'localSku', 'msku', 'countryCode','reportDateMonth'];
        $firstCondition = true;
        foreach ($data as $key_ => $val_) {
            if (in_array($key_, $field)) {
                if ($firstCondition) {
                    // 添加第一个 WHERE 条件
                    $db->where("where $key_ = :$key_", [":$key_" => $val_]);
                    $firstCondition = false;
                } else {
                    // 添加后续的 AND 条件
                    $db->andWhere("$key_ = :$key_", [":$key_" => $val_]);
                }
            }
        }

        $db->update(['yunying_id' => $yunying_id]);

        //查询修改聚合表
        $db = dbFMysql::getInstance();
        $db->table('msku_original_data_' . $year);
        $field = ['sid', 'asin', 'parentAsin', 'localSku', 'msku', 'countryCode','reportDateMonth'];
        $firstCondition = true;
        foreach ($data as $key_ => $val_) {
            if (in_array($key_, $field)) {
                if ($firstCondition) {
                    // 添加第一个 WHERE 条件
                    $db->where("where $key_ = :$key_", [":$key_" => $val_]);
                    $firstCondition = false;
                } else {
                    // 添加后续的 AND 条件
                    $db->andWhere("$key_ = :$key_", [":$key_" => $val_]);
                }
            }
        }

        $db->update(['yunying_id' => $yunying_id]);
        returnSuccess('','修改成功');
    }
    //传递规则映射表
    public function rule()
    {
        originaldataModel::getColumnList();
        $data = originaldataModel::$ColumnList;

        returnSuccess($data);
    }
    //拉取原始数据(领星同步)
    public function pullLxMskuData() {
        $m_date = $_POST['m_date']??'';
        if (!$m_date) {
            returnError('请选择要同步的月份');
        }
        checkoutModel::verifySyn($m_date);
        $redis = (new \core\lib\predisV())::$client;
//        $db = dbFMysql::getInstance();
//        $routing = $db->table('routing')
//            ->where('where date=:date',['date'=>$m_date])
//            ->field('code,my_rate')
//            ->one();
//        if (!$routing) {
//            returnError('请先同步该月汇率');
//        }
        //验证数据是否再使用
        checkoutForm::verifySynMskuData($redis,$m_date);
        //验证Msku数据是否正在同步中
        $redis_key = rediskeys::$oa_syn_msku_report;
        if ($redis->exists($redis_key)) {
            $r_data = json_decode($redis->get($redis_key),true);
            $month = isset($r_data['month'])?$r_data['month'].'月':'';
            if (empty($month)) {
                returnError($r_data['mesage']);
            } else {
                returnError($month.$r_data['mesage']);
            }
        } else {
            $r_data = [
                'page'=>0,
                'month'=>$m_date,
                'total'=>0,
                'mesage'=>'准备同步中, 请耐心等待'
            ];
            $redis->set($redis_key,json_encode($r_data));
            $redis->expire($redis_key,0.5*60*60);
        }
        $targetFile = SELF_FK . '/task/shell/lingxing_msku_report_new.sh ' . $m_date . '  > /dev/null 2>&1 &';
        shell_exec($targetFile);
        returnSuccess([],'同步中，请耐心等待');
    }

    public function export() {
        // 定义所需参数列表
        $paras_list = ['time'];
        $request_list = ['time' => '时间年月'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $redis = (new \core\lib\predisV())::$client;
        $redis_key = 'oa_export_origina_msku_data_'.$param['time'];
        $redis->del($redis_key);
        if ($redis->exists($redis_key)) {
            returnError($param['time'].'原始数据正在导出，请稍等。');
        }
        $db = dbFMysql::getInstance();
        $year = substr($param['time'], 0, 4);
        mskuReportModel::creatMskuReportOriginalTable($year);
        $count = $db->table('msku_original_data_'.$year)
            ->where('where reportDateMonth = :reportDateMonth', ['reportDateMonth' => $param['time']])->count();
        if (!$count) {
            returnError('未查询到数据');
        }
        $r_data = [
            'page'=>1,
            'm_date'=> $param['time'],
            'success_count'=>0,
            'total'=>$count,
            'excel_url'=>[],
            'zip_url'=>''
        ];
        $redis->set($redis_key,json_encode($r_data));
        $task = new mskuOriginalMskuDataJobs($redis_key);
//        $task->task();
        $queue_key = config::get('delay_queue_key', 'app');
        $redis->zAdd($queue_key, [], 0, serialize($task));
        returnSuccess(['key'=>$redis_key]);

    }
}