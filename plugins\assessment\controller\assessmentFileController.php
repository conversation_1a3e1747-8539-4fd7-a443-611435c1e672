<?php

namespace plugins\assessment\controller;

use admin\models\qwdepartmentModel;
use core\lib\db\dbAMysql;
use core\lib\db\dbMysql;
use plugins\assessment\models\userModel;
use core\lib\redisCached;
use plugins\salary\models\salaryCalculationModel;

class assessmentFileController
{
    // 获取考核档案列表
    public function getList()
    {
        $paras_list = array('assessment_name', 'users', 'departments', 'assessment_cycle_type', 'assessment_cycle', 'level', 'step', 'page', 'page_size', 'is_audit');
        $param = arrangeParam($_POST, $paras_list);

        if ($param['is_audit']) {
            // 需要拉取考核人员列表
            $adb = dbAMysql::getInstance();
            $adb->table('assessment_users', 'au')->field('au.*');
            $adb->leftJoin('assessment', 'a', 'a.id = au.a_id');
            $adb->where('where a.is_delete = 0 and au.status = 1 and a.status = 1 and au.step = 1');
            $adb->andWhere('au.audit_user_id = :audit_user_id', ['audit_user_id' => userModel::$qwuser_id]);
            $list = $adb->list();


            $users = redisCached::getUserInfo();
            $user_map = array_column($users, 'user_name', 'user_id');

            $adb->table('assessment');
            $adb->whereIn('id', array_column($list, 'a_id'));
            $assessment = $adb->list();
            $assessment_map = array_column($assessment, null,'id');

            foreach ($list as &$assessment_user) {
                $assessment_user['result'] = json_decode($assessment_user['result'], true);
                $assessment_user['attach'] = json_decode($assessment_user['attach'], true);
                $assessment_user['targets'] = json_decode($assessment_user['targets'], true);
                $assessment_user['process'] = json_decode($assessment_user['process'], true);
                $assessment_user['confirm_config'] = json_decode($assessment_user['confirm_config'], true);

                $assessment_user['assessment'] = $assessment_map[$assessment_user['a_id']];
                $assessment_user['user_name'] = $user_map[$assessment_user['user_id']];
                $assessment_user['audit_user_name'] = $user_map[$assessment_user['audit_user_id']] ?? '';
            }
            returnSuccess($list);
        }

        // 权限控制
        $user_id = userModel::$qwuser_id;
        $all_department_users = qwdepartmentModel::getUserDepartment(userModel::$wid)['all_department_users'] ?: []; // 当前用户管理的部门及其子部门下的用户
        $user_ids = array_column($all_department_users, 'id');

        // 需要拉取考核人员列表
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_users', 'au')->field('au.*');
        $adb->leftJoin('assessment', 'a', 'a.id = au.a_id');
        $adb->where('where a.is_delete = 0 and au.status = 1 and a.status = 1');

        if (userModel::getUserListAuth('assessmentFileAll')) {
            // 全部权限
        } elseif (userModel::getUserListAuth('assessmentFileDepartment')) {
            if (!empty($all_department_users)) {
                $user_str = implode(',', $user_ids);
                $adb->andWhere("JSON_OVERLAPS(related_users, JSON_ARRAY('{$user_str}'))");
            }
        } elseif (userModel::getUserListAuth('assessmentFileRelated')) {
            $adb->andWhere("JSON_OVERLAPS(related_users, JSON_ARRAY('{$user_id}'))");
        }

        if ($param['assessment_name']) {
            $adb->andWhere(' a.assessment_name like :assessment_name', ['assessment_name' => "%{$param['assessment_name']}%"]);
        }
        if (!empty($param['users'])) {
            $users = json_decode($param['users'], true);
            if (!empty($users)) {
                $adb->whereIn('au.user_id', $users);
            }
        }
        if (!empty($param['departments'])) {
            $departments = json_decode($param['departments'], true);
            $or_arr = [];
            foreach ($departments as $id) {
                $or_arr[] = "JSON_CONTAINS(JSON_EXTRACT(au.attach, '$.user_department_ids'), '{$id}')";
            }
            $adb->andWhere('('.implode(' or ', $or_arr).')');
        }
        if ($param['assessment_cycle_type']) {
            $assessment_cycle_type = json_decode($param['assessment_cycle_type'], true);
            $adb->whereIn('JSON_EXTRACT(a.attach, "$.scheme.assessment_cycle")',$assessment_cycle_type);

        }
        if (!empty($param['assessment_cycle'])) {
            $assessment_cycle = json_decode($param['assessment_cycle'], true);
            $adb->andWhere('JSON_UNQUOTE(JSON_EXTRACT(a.assessment_cycle, "$[1]")) >= :start_time 
                    and JSON_UNQUOTE(JSON_EXTRACT(a.assessment_cycle, "$[0]")) <= :end_time ',
                ['start_time' => $assessment_cycle[0], 'end_time' => $assessment_cycle[1]]);
        }
        if ($param['level']) {
            $adb->andWhere(' JSON_EXTRACT(au.result, \'$.level\') = \'' . $param['level'] . '\'');
        }

        if (!empty($param['step'])) {
            $step = json_decode($param['step'], true);
            if (!empty($step)) {
                $adb->whereIn('au.step', $step);
            }
        }

        $assessment_users = $adb->pages($param['page'], $param['page_size']);

        $users = redisCached::getUserInfo();
        $user_map = array_column($users, 'user_name', 'user_id');

        if (empty($assessment_users['list'])) {

            returnSuccess($assessment_users);
        }

        $adb = dbAMysql::getInstance();
        $adb->table('assessment');
        $adb->whereIn('id', array_column($assessment_users['list'], 'a_id'));
        $assessment = $adb->list();
        $assessment_map = array_column($assessment, null,'id');

        foreach ($assessment_users['list'] as &$assessment_user) {
            $assessment_user['result'] = json_decode($assessment_user['result'], true);
            $assessment_user['attach'] = json_decode($assessment_user['attach'], true);
            $assessment_user['targets'] = json_decode($assessment_user['targets'], true);
            $assessment_user['process'] = json_decode($assessment_user['process'], true);

            $assessment_user['assessment'] = $assessment_map[$assessment_user['a_id']];
            $assessment_user['user_name'] = $user_map[$assessment_user['user_id']];
            $assessment_user['audit_user_name'] = $user_map[$assessment_user['audit_user_id']] ?? '';
        }
        returnSuccess($assessment_users);

    }

    // 获取考核详情
    public function getDetail()
    {
        $paras_list = array('id');
        $param = arrangeParam($_POST, $paras_list);
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_users', 'au')->field('au.*');
        $adb->where('where id = :id', ['id' => $param['id']]);
        $assessment_user = $adb->one();
        $assessment_user['result'] = json_decode($assessment_user['result'], true);
        $assessment_user['attach'] = json_decode($assessment_user['attach'], true);
        $assessment_user['targets'] = json_decode($assessment_user['targets'], true);
        $assessment_user['process'] = json_decode($assessment_user['process'], true);
        $assessment_user['confirm_config'] = json_decode($assessment_user['confirm_config'], true);

        // 获取考核
        $adb->table('assessment', 'a')->field('a.*');
        $adb->where('where a.id = :id', ['id' => $assessment_user['a_id']]);
        $assessment = $adb->one();

        $db = dbMysql::getInstance();
        $all_users = $db->table('qwuser', 'a')->field('a.id,a.wid,a.wname,a.avatar')->where('where a.is_delete = 0');
        $all_users = array_column($all_users->list(), null, 'id');
        $all_users[0] = ['wname' => '系统', 'id' => 0, 'wid' => 0, 'avatar' => ''];

        // 获取考核日志
        $adb->table('assessment_users_log', 'al')->field('al.*');
        $adb->where('where al.au_id = :au_id', ['au_id' => $assessment_user['id']]);
        $adb->order('al.id desc');
        $log = $adb->list();
        foreach ($log as &$item) {
            $item['changes'] = json_decode($item['changes'], true);
            $item['user_name'] = $all_users[$item['user_id']]['wname'];
        }

        returnSuccess([
            'assessment_user' => $assessment_user,
            'assessment'      => $assessment,
            'log'             => $log
        ]);
    }

    // 调整
    public function changeAssessment()
    {
        returnError('功能已废弃');
        $paras_list = array('id', 'performance');
        $param = arrangeParam($_POST, $paras_list);

        $adb = dbAMysql::getInstance();
        // 查询用户考核任务
        $adb->table('assessment_users');
        $assessment_user = $adb->where('where id = :id', ['id' => $param['id']])->one();
        if (!$assessment_user) returnError('考核用户不存在！');
        if ($assessment_user['status'] != 1) returnError('当前考核不能进行调整');

        $assessment_user['result'] = json_decode($assessment_user['result'], true);
        $result = $assessment_user['result'];
        $assessment_user['result']['final_performance'] = $param['performance'];
        $adb->table('assessment_users')->where('id = :id', ['id' => $param['id']])->update(['result' => json_encode($assessment_user['result'])]);

        // 记录日志
        $adb->table('assessment_users_log');
        $adb->insert([
            'user_id' => userModel::$qwuser_id,
            'a_id'    => $assessment_user['a_id'],
            'au_id'   => $assessment_user['id'],
            'changes' => json_encode([
                'type'   => 1,
                'before' => $result,
                'after'  => $assessment_user['result']
            ], JSON_UNESCAPED_UNICODE)
        ]);
        returnSuccess([], '调整成功');
    }

    // 嘉奖
    public function prizeAssessment()
    {
        $paras_list = array('id', 'prize','prize_desc');
        $param = arrangeParam($_POST, $paras_list);

        $adb = dbAMysql::getInstance();
        // 查询用户考核任务
        $adb->table('assessment_users');
        $assessment_user = $adb->where('where id = :id', ['id' => $param['id']])->one();
        if (!$assessment_user) returnError('考核用户不存在！');
        if ($assessment_user['status'] != 1) returnError('当前考核不能进行嘉奖');
        if ($assessment_user['step'] == 4 && !empty($assessment_user['salary_month'])) returnError('已提交算薪的考核不能进行嘉奖');

        $assessment_user['result'] = json_decode($assessment_user['result'], true);
        $result = $assessment_user['result'];
        if (!isset($assessment_user['result']['prize'])) {
            $assessment_user['result']['prize'] = 0;
        }
        $assessment_user['result']['prize'] += $param['prize'];
        $adb->table('assessment_users')->where('id = :id', ['id' => $param['id']])->update(['result' => json_encode($assessment_user['result'])]);

        // 记录日志
        $adb->table('assessment_users_log');
        $adb->insert([
            'user_id' => userModel::$qwuser_id,
            'a_id'    => $assessment_user['a_id'],
            'au_id'   => $assessment_user['id'],
            'changes' => json_encode([
                'type'   => 2,
                'before' => $result,
                'after'  => $assessment_user['result'],
                'desc'  => $param['prize_desc']
            ], JSON_UNESCAPED_UNICODE)
        ]);
        returnSuccess([], '嘉奖成功');
    }

    // 提交审批
    public function submitAudit()
    {
        $paras_list = array('ids', 'audit_user_id');
        $param = arrangeParam($_POST, $paras_list);
        empty($param['ids']) && returnError('考核ID不能为空');
        $ids = json_decode($param['ids'], true);
        empty($param['audit_user_id']) && returnError('月份不能为空');
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_users');
        $assessment_users = $adb->whereIn('id', $ids)->list();
        if (!$assessment_users) returnError('考核不存在！');
        $update_ids = [];
        foreach ($assessment_users as $item) {
            if ($item['status'] != 1) returnError('只有已完成的考核任务才能提交审批');
            if (!in_array($item['step'], [0,3])) returnError('只有待提审和审批不通过的考核任务才能提交审批');
            $update_ids[] = $item['id'];
        }

        if (!empty($update_ids)) {
            $adb->table('assessment_users')->whereIn('id', $update_ids)->update([
                'audit_user_id' => $param['audit_user_id'],
                'step' => 1,
            ]);
        }

        returnSuccess([], '提交成功');
    }

    // 审批
    public function audit()
    {
        $paras_list = array('data');
        $param = arrangeParam($_POST, $paras_list);
        $param['data'] = json_decode($param['data'], true);
        empty($param['data']) && returnError('考核ID不能为空');
        $ids = array_column($param['data'], 'id');
        empty($ids) && returnError('考核ID不能为空');

        $adb = dbAMysql::getInstance();
        // 计算是否有审批中的调整薪资
        $adb->table('assessment_users', 'au')
            ->whereIn('au.id', $ids);
        $assessment_users = $adb->list();
        $assessment_users = array_column($assessment_users, null, 'id');
        $update_data = [];
        $log_data = [];

        foreach ($param['data'] as $item) {
            $assessment_user = $assessment_users[$item['id']];
            if (!$assessment_user) continue;
            if ($assessment_user['step'] != 1) continue;
            if ($assessment_user['audit_user_id'] != userModel::$qwuser_id) continue;
            $update_data[] = [
                'id' => $assessment_user['id'],
                'step' => $item['audit_status'] == 1 ? 2 : 3, //2审批通过 3审批不通过
            ];
            $log_data[] = [
                'user_id' => userModel::$qwuser_id,
                'a_id'    => $assessment_user['a_id'],
                'au_id'   => $assessment_user['id'],
                'changes' => json_encode([
                    'type'   => 6, // 审批
                    'status' => $item['audit_status'],
                    'desc'   => $item['audit_desc']
                ], JSON_UNESCAPED_UNICODE)
            ];
        }

        if (!empty($update_data)) {
            $adb->table('assessment_users')->updateBatch($update_data);
        }
        if (!empty($log_data)) {
            $keys = array_keys($log_data[0]);
            $list = array_map(function ($item) {
                return array_values($item);
            }, $log_data);
            $chunkedArray = array_chunk($list, 500);
            foreach ($chunkedArray as $v) {
                $adb->table('assessment_users_log')->insertBatch($keys, $v);
            }
        }

        returnSuccess($update_data, '审批成功');
    }

    // 绩效发布
    public function publishConfirm()
    {
        $paras_list = array('ids', 'result_view');
        $param = arrangeParam($_POST, $paras_list);
        empty($param['ids']) && returnError('考核ID不能为空');
        $ids = json_decode($param['ids'], true);
        $param['result_view'] = json_decode($param['result_view'], true) ?? [];
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_users');
        $assessment_users = $adb->whereIn('id', $ids)->list();
        if (!$assessment_users) returnError('考核不存在！');
        $update_ids = [];
        foreach ($assessment_users as $item) {
            if ($item['status'] != 1) continue;
            if (!in_array($item['step'], [2,4,5])) continue;
            if (!empty($item['confirm_config'])) continue;
            $update_ids[] = $item['id'];
        }
        if (!empty($update_ids)) {
            $adb->table('assessment_users')->whereIn('id', $update_ids)->update([
                'confirm_config' => json_encode([
                    'result_view' => $param['result_view'],
                    'confirm_status' => null
                ], JSON_UNESCAPED_UNICODE),
            ]);
        }

        returnSuccess([], '提交成功');
    }

    // 提交到算薪
    public function submitSalary()
    {
        $paras_list = array('ids', 'month');
        $param = arrangeParam($_POST, $paras_list);
        empty($param['ids']) && returnError('考核ID不能为空');
        $ids = json_decode($param['ids'], true);
        empty($param['month']) && returnError('月份不能为空');

        $adb = dbAMysql::getInstance();
        $adb->table('assessment_users');
        $assessment_users = $adb->whereIn('id', $ids)->list();
        if (!$assessment_users) returnError('考核不存在！');
        $user_ids = array_column($assessment_users, 'user_id');
        $calculation = salaryCalculationModel::getCalculationUserByMonth($param['month'], $user_ids);
        if ($calculation) {
            returnError($param['month'].'月份已经算薪，不能提交');
        }

        $assessments = $adb->table('assessment')->whereIn('id', array_column($assessment_users, 'a_id'))->list();
        $assessments = array_column($assessments, null, 'id');

        $update_ids = [];
        $error_msg = [];
        foreach ($assessment_users as $item) {
            $assessment = $assessments[$item['a_id']];
            $assessment['attach'] = json_decode($assessment['attach'], true);
            if ($assessment['attach']['scheme']['assessment_type'] == 3) {
                $error_msg[] = [
                    'id' => $item['id'],
                    'msg' => '薪资类型的考核不能提交算薪'
                ];
                continue;
            }
            if ($item['status'] != 1) {
                $error_msg[] = [
                    'id' => $item['id'],
                    'msg' => '只有已完成的考核任务才能提交算薪'
                ];
                continue;
            }
            if ($item['step'] == 4) {
                $error_msg[] = [
                    'id' => $item['id'],
                    'msg' => '已提交算薪不能再次提交'
                ];
                continue;
            }
            if (!in_array($item['step'], [2, 5])) {
                $error_msg[] = [
                    'id' => $item['id'],
                    'msg' => '当前状态不能提交算薪'
                ];
                continue;
            }
            $update_ids[] = $item['id'];
        }

        if (!empty($update_ids)) {
            $adb->table('assessment_users')->whereIn('id', $update_ids)->update([
                'step' => 4,
                'salary_month' => $param['month'],
            ]);
        }

        returnSuccess($error_msg, empty($error_msg) ? '提交成功' : '部分考核不能提交算薪');
    }

    // 撤回提交
    public function cancelSubmit() {
        $paras_list = array('ids');
        $param = arrangeParam($_POST, $paras_list);
        empty($param['ids']) && returnError('考核ID不能为空');
        $ids = json_decode($param['ids'], true);

        $adb = dbAMysql::getInstance();
        $adb->table('assessment_users');
        $assessment_users = $adb->whereIn('id', $ids)->list();
        if (!$assessment_users) returnError('考核不存在！');
        $update_ids = [];

        $error_msg = [];
        foreach ($assessment_users as $item) {
            if (empty($item['salary_month'])) continue;
            // 查询是否有当月的算薪
            $calculation = salaryCalculationModel::getCalculationByMonth($item['salary_month']);
            if ($calculation) {
                $error_msg[] = [
                    'id' => $item['id'],
                    'msg' => $item['salary_month'].'月份已经算薪，不能撤回'
                ];
                continue;
            }
            if ($item['status'] != 1) continue;
            if ($item['step'] != 4) continue;
            $update_ids[] = $item['id'];
        }

        if (!empty($update_ids)) {
            $adb->table('assessment_users')->whereIn('id', $update_ids)->update([
                'step' => 5,
                'salary_month' => null,
            ]);
        }

        returnSuccess($error_msg, empty($error_msg) ? '撤回提交成功' : '部分考核不能撤回');
    }

}