<?php
/**
 * 海外仓备货单控制器
 * @purpose 海外仓备货单管理
 * @Author: System
 * @Time: 2025/06/24
 */

namespace plugins\logistics\controller;

use plugins\logistics\models\overseasInboundModel;
use plugins\logistics\form\overseasInboundForm;
use core\lib\log;
use plugins\logistics\models\overseasInboundDetailModel;

class overseasInboundController extends baseController
{
    private $model;
    
    public function __construct()
    {
        $this->model = new overseasInboundModel();
        parent::__construct();
    }

    /**
     * 获取备货单列表
     */
    public function getInboundList()
    {
        try {
            $model = new overseasInboundDetailModel();
            $result = $model->getDetailList($_GET);
            returnSuccess($result);
        } catch (\Exception $e) {
            log::lingXingApi('OverseasInbound')->error('获取备货单列表失败：' . $e->getMessage());
            returnError('获取备货单列表失败');
        }
    }

    /**
     * 更新可编辑字段
     */
    public function edit()
    {
        try {
            $form = new overseasInboundForm();
            $data = $form->validateEditableFields($_POST);
            
            if (!empty($form->getErrors())) {
                returnError(implode(', ', $form->getErrors()));
            }
            
            $id = $_POST['id'] ?? 0;
            if (empty($id)) {
                returnError('备货单ID不能为空');
            }
            
            $result = $this->model->updateEditableFields($id, $data);
            if ($result) {
                log::lingXingApi('OverseasInbound')->info("更新备货单可编辑字段成功，ID：{$id}");
                returnSuccess(['updated' => $result], '更新成功');
            } else {
                returnError('更新失败');
            }
        } catch (\Exception $e) {
            log::lingXingApi('OverseasInbound')->error('更新可编辑字段失败：' . $e->getMessage());
            returnError('更新失败');
        }
    }

        /**
     * 海外仓备货单明细数据导出
     * @return void
     */
    public function export()
    {
        try {
            $syncDate = $_POST['sync_date'] ?? date('Y-m-d');
            $orderNo = $_POST['order_no'] ?? '';
            $sku = $_POST['sku'] ?? '';

            $detailModel = new overseasInboundDetailModel();
            $data = $detailModel->getDetailForExport($syncDate, $orderNo, $sku);

            if (empty($data)) {
                SetReturn(-1, "没有找到符合条件的数据", []);
                return;
            }

            // 生成Excel文件
            $filename = $this->generateExcelFile($data, $syncDate);

            SetReturn(2, "导出成功", [
                'filename' => $filename,
                'count' => count($data),
                'sync_date' => $syncDate
            ]);

        } catch (\Exception $e) {
            SetReturn(-1, "导出失败：" . $e->getMessage(), []);
        }
    }

    /**
     * 海外仓备货单明细数据新增导入
     * @return void
     */
    public function importNew()
    {
        try {
            if (!isset($_FILES['import_file'])) {
                SetReturn(-1, "请选择要导入的文件", []);
                return;
            }

            $file = $_FILES['import_file'];
            $importData = $this->parseImportFile($file);

            if (empty($importData)) {
                SetReturn(-1, "导入文件为空或格式错误", []);
                return;
            }

            $detailModel = new overseasInboundDetailModel();
            $result = $detailModel->batchInsertNewDetails($importData);

            SetReturn(2, "新增导入完成", [
                'total_count' => count($importData),
                'success_count' => $result['success_count'],
                'error_count' => $result['error_count'],
                'errors' => $result['errors']
            ]);

        } catch (\Exception $e) {
            SetReturn(-1, "新增导入失败：" . $e->getMessage(), []);
        }
    }

    /**
     * 海外仓备货单明细数据批量修改导入
     * @return void
     */
    public function importUpdate()
    {
        try {
            if (!isset($_FILES['import_file'])) {
                SetReturn(-1, "请选择要导入的文件", []);
                return;
            }

            $file = $_FILES['import_file'];
            $importData = $this->parseImportFile($file);

            if (empty($importData)) {
                SetReturn(-1, "导入文件为空或格式错误", []);
                return;
            }

            $detailModel = new overseasInboundDetailModel();
            $result = $detailModel->batchUpdateDetails($importData);

            SetReturn(2, "批量修改导入完成", [
                'total_count' => count($importData),
                'success_count' => $result['success_count'],
                'error_count' => $result['error_count'],
                'errors' => $result['errors']
            ]);

        } catch (\Exception $e) {
            SetReturn(-1, "批量修改导入失败：" . $e->getMessage(), []);
        }
    }

    /**
     * 解析导入文件
     * @param array $file 上传文件信息
     * @return array
     */
    private function parseImportFile($file)
    {
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);

        if (!in_array(strtolower($extension), ['xlsx', 'xls', 'csv'])) {
            throw new \Exception("不支持的文件格式，请使用Excel或CSV文件");
        }

        // 这里需要根据实际情况实现Excel/CSV解析
        // 暂时返回示例数据结构
        return $this->parseExcelFile($file['tmp_name']);
    }

    /**
     * 解析Excel文件
     * @param string $filePath 文件路径
     * @return array
     */
    private function parseExcelFile($filePath)
    {
        // 实际项目中需要使用PhpSpreadsheet或类似库
        // 这里提供基本的解析逻辑框架
        $data = [];

        // 示例：读取CSV文件
        if (($handle = fopen($filePath, "r")) !== FALSE) {
            $headers = fgetcsv($handle); // 读取表头

            while (($row = fgetcsv($handle)) !== FALSE) {
                if (count($row) >= count($headers)) {
                    $rowData = array_combine($headers, $row);
                    $data[] = $this->validateImportRow($rowData);
                }
            }
            fclose($handle);
        }

        return array_filter($data); // 过滤掉无效数据
    }

    /**
     * 验证导入行数据
     * @param array $rowData 行数据
     * @return array|null
     */
    private function validateImportRow($rowData)
    {
        // 必填字段验证
        if (empty($rowData['overseas_order_no']) || empty($rowData['sku'])) {
            return null;
        }

        return [
            'overseas_order_no' => trim($rowData['overseas_order_no']),
            'sku' => trim($rowData['sku']),
            'product_name' => trim($rowData['product_name'] ?? ''),
            'shop_code' => trim($rowData['shop_code'] ?? ''),
            'fnsku' => trim($rowData['fnsku'] ?? ''),
            'quantity' => (int)($rowData['quantity'] ?? 0),
            'box_count' => (int)($rowData['box_count'] ?? 0),
            'warehouse_code' => trim($rowData['warehouse_code'] ?? ''),
            'transparent_label' => trim($rowData['transparent_label'] ?? ''),
            'logistics_method' => trim($rowData['logistics_method'] ?? ''),
            'target_warehouse' => trim($rowData['target_warehouse'] ?? ''),
            'plan_time' => $this->parseDateTime($rowData['plan_time'] ?? ''),
            'ship_time' => $this->parseDateTime($rowData['ship_time'] ?? ''),
            'shipping_status' => trim($rowData['shipping_status'] ?? ''),
            'warehouse_arrival' => $this->parseDateTime($rowData['warehouse_arrival'] ?? ''),
            'receive_difference' => (int)($rowData['receive_difference'] ?? 0),
            'remaining_available' => (int)($rowData['remaining_available'] ?? 0),
            'shipping_remark' => trim($rowData['shipping_remark'] ?? ''),
            'other_remark' => trim($rowData['other_remark'] ?? ''),
        ];
    }

    /**
     * 解析日期时间
     * @param string $dateStr 日期字符串
     * @return string|null
     */
    private function parseDateTime($dateStr)
    {
        if (empty($dateStr)) {
            return null;
        }

        $timestamp = strtotime($dateStr);
        return $timestamp ? date('Y-m-d H:i:s', $timestamp) : null;
    }

    /**
     * 生成Excel导出文件
     * @param array $data 数据
     * @param string $syncDate 同步日期
     * @return string 文件名
     */
    private function generateExcelFile($data, $syncDate)
    {
        $filename = "overseas_inbound_detail_{$syncDate}_" . date('His') . ".csv";
        $filepath = "/tmp/" . $filename;

        $fp = fopen($filepath, 'w');

        // 写入表头
        $headers = [
            '海外仓备货单号', 'SKU', '产品名称', '店铺代码', 'FNSKU', '数量', '箱数',
            '入仓编号', '透明标', '物流方式', '发往仓库', '计划时间', '发货时间',
            '发货在途', '仓库到货', '收货差异', '剩余可用', '发货备注', '其他备注'
        ];
        fputcsv($fp, $headers);

        // 写入数据
        foreach ($data as $row) {
            $csvRow = [
                $row['overseas_order_no'],
                $row['sku'],
                $row['product_name'],
                $row['shop_code'],
                $row['fnsku'],
                $row['quantity'],
                $row['box_count'],
                $row['warehouse_code'],
                $row['transparent_label'],
                $row['logistics_method'],
                $row['target_warehouse'],
                $row['plan_time'],
                $row['ship_time'],
                $row['shipping_status'],
                $row['warehouse_arrival'],
                $row['receive_difference'],
                $row['remaining_available'],
                $row['shipping_remark'],
                $row['other_remark']
            ];
            fputcsv($fp, $csvRow);
        }

        fclose($fp);
        return $filename;
    }
}
