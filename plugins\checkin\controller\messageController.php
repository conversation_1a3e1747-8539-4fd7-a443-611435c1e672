<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/22 15:07
 */

namespace plugins\checkin\controller;

use core\lib\db\dbCMysql;
use plugins\checkin\models\userModel;

class messageController
{
    public function getList()
    {
        $paras_list = array('is_read','page','page_size','type');
        $param = arrangeParam($_GET, $paras_list);
        $is_read = (int)$param['is_read'];
        $type = (int)$param['type'];
        $cdb = dbCMysql::getInstance();
        $cdb->table('messages')
            ->where('where qw_userid=:qw_userid',['qw_userid'=>userModel::$wid]);
        if ($is_read > -1) {
            $cdb->andWhere('is_read=:is_read',['is_read'=>$is_read]);
        }
        if ($type == 2) {
            $cdb->andWhere('type=:type',['type'=>$type]);
        }
        $cdb->field('id,title,qw_userid,text,type,is_read,created_at,remarks');
        $cdb->order('is_read asc,id desc');
        $list = $cdb->pages($param['page'],$param['page_size']);
        returnSuccess($list);
    }
    public function getMessageCount() {
        //计数
        $cdb = dbCMysql::getInstance();
        $project_msg_count = $cdb->table('messages')
            ->where('where qw_userid=:qw_userid and is_read=0',['qw_userid'=>userModel::$wid])
            ->count();
        $project_copy_msg_count = $cdb->table('messages')
            ->where('where qw_userid=:qw_userid and is_read=0',['qw_userid'=>userModel::$wid])
            ->andWhere('type=2')
            ->count();
        $list = [
            'project_msg_count'=>$project_msg_count,
            'project_copy_msg_count'=>$project_copy_msg_count,
        ];
        returnSuccess($list);
    }
    public function getDetail()
    {
        $id = (int)$_GET['id'];
        if (!$id) {
            SetReturn(-1, '参数有误');
        }
        $cdb = dbCMysql::getInstance();
        $cdb->table('messages');
        $cdb->where('where id = '.$id);
        $cdb->field('id,title,qw_userid,type,text,model_id,created_at,remarks');
        $data = $cdb->one();
        $cdb->update(['is_read'=>1]);
        returnSuccess(['data'=>$data]);
    }

    //全部标记已读
    public function setAllRead()
    {
        $type = (int)$_GET['type'];
        $cdb = dbCMysql::getInstance();
        $user = json_decode(USER_INFO,true);

        $cdb->table('messages');
        $cdb->where('where qw_userid = :qw_userid and is_read = 0',['qw_userid'=>$user['wid']]);
        if ($type == 2) {
            $cdb->andWhere('type=2');
        }
        $cdb->update(['is_read'=>1]);
        SetReturn(0, '全部已读');
    }

    //获取企微通知的消息
    public function getMsgDetail() {
        $secret_code = $_POST['data']??'';
        if (empty($secret_code)) {
            SetReturn(-1,'参数有误');
        }
        $data = qwMsgDecryption($secret_code);
        $data  = json_decode($data,true);
        $id = $data['id']??0;
        if (!$id) {
            SetReturn(-1,'参数有误');
        }
        $cdb = dbCMysql::getInstance();
        $cdb->table('messages');
        $cdb->where('where id = '.$id);
        $data = $cdb->one();
        $cdb->update(['is_read'=>1]);
        returnSuccess(['data'=>$data]);
    }
}