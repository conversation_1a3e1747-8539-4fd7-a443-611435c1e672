<?php

namespace financial\models;

use core\lib\db\dbFMysql;

class exportModel
{
    //基本信息字段数组
    public static array $export_list = [
        'asin' => 'ASIN',
        'p_asin' => '父ASIN',
        'supplier_name' => '供应商',
        'sku' => 'SKU',
        'product_name' => '品名',
        'store_name' => '店铺',
        'project1' => '项目',
        'project' => '运营部门',
        'yunying' => '运营人员',
        'product_developer' => '开发负责人',
        'level_name' => '产品等级',
        'is_new' => '新品',
        'category_name' => '分类',
        'year_amount'=>'总合计',
        'month_amount'=>'月度总计',
        'waring_name'=>'预警名称',
        'waring_time'=>'预警时间',
        'waring_status'=>'预警状态',
        'waring_rules'=>'预警监控条件',
        'waring_reason_txt'=>'预警值',
        'country'=>'国家',
    ];
    //获取导出字段
    public static function get_export_list()
    {
        $db = dbFMysql::getInstance();
        $file_list = $db->table('column')->field('key_name,column_name')->list();
        foreach ($file_list as $k => $v) {
            self::$export_list[$v['key_name']] = $v['column_name'];
        }
        return self::$export_list;
    }
    //利润贡献年度月份字段添加
    public static function get_year_month_list($data,$list)
    {
        foreach ($data['data']['list'][0] as $k => $v){
            if ($k == 'year_data' || $k == 'month_data'){
                foreach ($v as $k1 => $v1){
                    $list[$k1] = $k1;
                }
            }
        }
        return $list;
    }
    //获取需要转换为百分比的列表
    public static function get_percent_list()
    {
        $db = dbFMysql::getInstance();
        $list = $db->table('column')->field('key_name')->where('where show_type = 2')->list();
        $list = array_column($list, 'key_name');
        return $list;
    }

}