<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/20 11:37
 */

namespace financial\form;

use Rap2hpoutre\FastExcel\FastExcel;

class sellerForm
{
    public static array $export_list = [
        'lx_name'=>'领星店铺名称','real_name'=>'店铺名称', 'name'=>'异常明细','is_error'=>'是否异常', 'is_local'=>'本土店铺', 'is_ban'=>'被封店铺', 'is_jing'=>'精铺店铺', 'is_fanou'=>'泛欧店铺', 'updated_time'=>'最新修改时间', 'updated_wname'=>'最新修改人'
    ];
    //导出数据
    public static function exportSeler($list,$export_list) {
        //表头
        $new_data = [];
        //数据
        $xuhao = 1;
        foreach ($list as $v) {
            $item['序号'] = $xuhao;
            foreach ($v as $k=>$v1) {
                if (!in_array($k,$export_list)) {
                    continue;
                }
                if (!isset(self::$export_list[$k])) {
                    continue;
                }
                $value = $v1;
                //时间转化
                if ($k == 'name') {
                    if ($v['real_name'] != $v1) {
                        $value = $v1;
                    } else {
                        $value = '-';
                    }
                }
                if (in_array($k,['is_local','is_ban','is_jing','is_fanou','is_error'])) {
                    if ($v1 === '1') {
                        $value = '是';
                    } else if ($v1 === '0') {
                        $value = '否';
                    } else {
                        $value = '-';
                    }
                }
                $item[self::$export_list[$k]] = $value;
            }
            $new_data[] = $item;
            $xuhao++;
        }
        //保存
        $save_path = "/public_financial/temp/seller";
        $url = SELF_FK.$save_path;
        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path."/".date('YmdHis').uniqid().'.xlsx';
        $url = SELF_FK.$path;
        if (!file_exists($url)) {
            touch($url);
        }
        (new FastExcel($new_data))->export($url);
        return $path;
    }




}