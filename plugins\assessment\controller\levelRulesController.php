<?php

namespace plugins\assessment\controller;

use core\lib\config;
use core\lib\db\dbAMysql;
use plugins\assessment\models\assessmentModel;
use plugins\assessment\models\assessmentSchemesModel;
use plugins\assessment\models\levelRulesModel;

class levelRulesController
{
    // 获取等级规则列表
    public function getLevelRulesList()
    {
        $paras_list = array('rule_name', 'has_coefficient', 'page', 'page_size');
        $param = arrangeParam($_POST, $paras_list);
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        $adb = dbAMysql::getInstance();
        $adb->table('level_rules');
        $adb->where('where is_delete = 0');
        if ($param['rule_name']) {
            $adb->andWhere('rule_name like :rule_name', ['rule_name' => '%' . $param['rule_name'] . '%']);
        }
        if (isset($param['has_coefficient']) && in_array($param['has_coefficient'], [0, 1])) {
            $adb->andWhere('has_coefficient = :has_coefficient', ['has_coefficient' => $param['has_coefficient']]);
        }
        $adb->order('id desc');
        $data = $adb->pages($page, $limit);

        foreach ($data['list'] as &$item) {
            $item['level_rules'] = json_decode($item['level_rules'], true);
            $item['coefficient_rules'] = json_decode($item['coefficient_rules'], true) ?: null;
            $item['level_rule_text'] = levelRulesModel::getLevelRuleText($item['level_rules']);
        }

        returnSuccess($data);
    }

    // 新增/编辑等级规则
    public function addLevelRules()
    {
        $paras_list = array('id', 'rule_name', 'level_rules', 'has_coefficient', 'coefficient_rules', 'default_coefficient');
        $length_data = ['rule_name' => ['name' => '规则名称', 'length' => 30]];
        $request_list = ['rule_name' => '规则名称', 'level_rules' => '等级规则'];
        $param = arrangeParam($_POST, $paras_list, $request_list, $length_data);

        // 校验等级规则
        try {
            $ruleCheck = levelRulesModel::checkLevelRules(json_decode($param['level_rules'], true));
        } catch (\Throwable $error) {
            returnError($error->getMessage());
        }

        if ($param['has_coefficient']) {
            if (empty($param['coefficient_rules'])) returnError('系数规则不能为空');
            if (empty($param['default_coefficient'])) returnError('默认系数不能为空');
            // 校验系数规则
            try {
                $ruleCheck = levelRulesModel::checkCoefficientRules(json_decode($param['coefficient_rules'], true));
            } catch (\Throwable $error) {
                returnError($error->getMessage());
            }
        } else {
            // 无系数规则时，系数规则和默认系数置空
            $param['coefficient_rules'] = null;
            $param['default_coefficient'] = 0;
        }

        $adb = dbAMysql::getInstance();
        $adb->table('level_rules');

        // 判断规则是否存在
        if ($param['id']) {
            $adb->where('where id = :id and is_delete = 0', ['id' => $param['id']]);
            $is_exist = $adb->one();
            if (!$is_exist) returnError('规则不存在');

            // 判断是否有正在进行中的绩效考核在使用该规则
            if (in_array($param['id'], assessmentModel::getRunningAssessmentDetail('level'))) {
                returnError('有进行中的绩效考核在使用该规则，不允许修改');
            }
        }

        // 判断规则名称是否重复
        $adb->table('level_rules');
        $adb->where('where rule_name = :rule_name and is_delete = 0', ['rule_name' => $param['rule_name']]);
        if ($param['id']) {
            $adb->andWhere(' id != :id', ['id' => $param['id']]);
        }
        $is_exist = $adb->one();
        if ($is_exist) returnError('规则名称已存在');

        $data = [
            'rule_name'           => $param['rule_name'],
            'level_rules'         => $param['level_rules'],
            'has_coefficient'     => $param['has_coefficient'] ?: 0,
            'coefficient_rules'   => $param['coefficient_rules'] ?: null,
            'default_coefficient' => $param['default_coefficient'] ?: 0
        ];
        if ($param['id']) {
            $adb->table('level_rules')->where('where id = :id and is_delete = 0', ['id' => $param['id']]);
            $res = $adb->update($data);
        } else {
            $res = $adb->table('level_rules')->insert($data);
            if (!$res) {
                returnError('fail');
            }
        }
        returnSuccess([], 'success');
    }

    // 删除等级规则
    public function delLevelRules()
    {
        $id = $_POST['id'];
        if (!$id) returnError('id不能为空');
        $adb = dbAMysql::getInstance();
        $adb->table('level_rules');
        $adb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $is_exist = $adb->one();
        if (!$is_exist) returnError('规则不存在');

        // 是否有方案引用该规则
        if (in_array($id, assessmentSchemesModel::getSchemeDetail('level'))) {
            returnError('有方案在使用该规则，不允许删除');
        }

        // 判断是否有正在进行中的绩效考核在使用该规则
        if (in_array($id, assessmentModel::getRunningAssessmentDetail('level'))) {
            returnError('有进行中的绩效考核在使用该规则，不允许删除');
        }

        $data = [
            'is_delete' => 1,
        ];
        $adb->table('level_rules');
        $adb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $res = $adb->update($data);
        if ($res) {
            returnSuccess([], 'success');
        } else {
            returnError('fail');
        }
    }

    // 获取等级规则详情
    public function getLevelRulesDetail()
    {
        $id = $_POST['id'];
        if (!$id) returnError('id不能为空');
        $adb = dbAMysql::getInstance();
        $adb->table('level_rules');
        $adb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $detail = $adb->one();

        $detail['level_rules'] = $detail['level_rules'] ? json_decode($detail['level_rules'], true) : null;
        $detail['coefficient_rules'] = $detail['coefficient_rules'] ? json_decode($detail['coefficient_rules'], true) : null;
        $detail['level_rule_text'] = levelRulesModel::getLevelRuleText($detail['level_rules']);

        returnSuccess($detail);
    }

}