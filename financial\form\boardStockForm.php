<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/8 14:22
 */

namespace financial\form;


use core\lib\db\dbFMysql;
use financial\models\boardModel;
use financial\models\goodsStorkModel;
use financial\models\userModel;

class boardStockForm extends boardModel  //非自自定义字段
{
    //库存统计
    public function stockEndStatus() {
        $res_list = [];
        $db = dbFMysql::getInstance();
        foreach (self::$years as $year=>$m_date_list) {
            goodsStorkModel::creatGoodsStockTable($year);
            //获取本年份的值
            $dbs = boardModel::getSqlWhereForStock($db,$year,$m_date_list);
            $dbs->field("m_date,country_code,country,sum(fbm_local_overseas_num) as stock,sum(fbm_local_overseas_price) as price")
                ->andWhere('is_error=0 and is_delete = 0')
                ->groupBy(['country_code','country','m_date']);
            $list = $db->list();
            $res_list = array_merge($res_list,$list);
        }
        //按照汇率计算数据
//        $res_list = self::getPriceByRoute($res_list,self::$search_month,["price"],["price"]);
        $res_data = [];
        //按照不同国家分组
        $total_price = 0;
        foreach ($res_list as $data) {
            if (!isset($res_data[$data['country_code']])) {
                $res_data[$data['country_code']] = [
                    'country_code'=>$data['country_code'],
                    'country'=>$data['country'],
                    'stock_total'=>0,
                    'price_total'=>0,
                    'price_rate'=>0,
                    'cost_list'=>[]
                ];
            }
            $res_data[$data['country_code']]['stock_total'] += $data['stock'];
            $res_data[$data['country_code']]['price_total'] += $data['price'];
            $total_price += $data['price'];
            //库存末期情况
            foreach (self::$search_month as $month) {
                if (!isset($res_data[$data['country_code']]['stock_list'][$month])) {
                    $res_data[$data['country_code']]['stock_list'][$month] = [
                        'm_date'=>$month,
                        'total'=>0
                    ];
                }
                if ($month == $data['m_date']) {
                    $res_data[$data['country_code']]['stock_list'][$month]['total'] = $data['stock'];
                }
            }
            //库存成本
            foreach (self::$search_month as $month) {
                if (!isset($res_data[$data['country_code']]['cost_list'][$month])) {
                    $res_data[$data['country_code']]['cost_list'][$month] = [
                        'm_date'=>$month,
                        'total'=>0
                    ];
                }
                if ($month == $data['m_date']) {
                    $res_data[$data['country_code']]['cost_list'][$month]['total'] = $data['price'];
                }
            }
        }
        usort($res_data,fn($a,$b)=>$a['price_total']<=>$a['price_total']);
        //库存末期情况
        $stock_list = [];
        $cost_list = [];
        $rate_cost_total = 0;
        $other_cost_data_list = [];
        $other_cost_rate = 0;
        foreach ($res_data as $k=>$item) {
            //库存情况
            $stock_list[] = [
                'country_code'=>$item['country_code'],
                'country'=>$item['country'],
                'total'=>roundToString($item['stock_total']),
                'list'=>$item['stock_list'],
            ];
            //库存成本金额增量
            $cost_item = [
                'country_code'=>$item['country_code'],
                'country'=>$item['country'],
                'total'=>roundToString($item['price_total']),
                'rate'=>'0',
                'list'=>$item['cost_list']
            ];
            if ($total_price == 0) {
                $rate = '0';
            } else {
                $rate = roundToString($cost_item['total']*100/$total_price);
                $rate_cost_total += $rate;
                if ($k >= (count($res_list)-1) && $k>0) {
                    $rate = roundToString(100-$rate_cost_total);
                }
            }
            if ($rate <= 5) {
                $other_cost_rate += $rate;
                $other_cost_data_list = array_merge($other_cost_data_list,array_values($item['cost_list']));
            } else {
                $cost_item['rate'] = $rate;
                $cost_list[] = $cost_item;
            }
        }
        if (count($other_cost_data_list)) {
            $new_other_list = [];
            foreach ($other_cost_data_list as $v) {
                if (!isset($new_other_list[$v['m_date']])) {
                    $new_other_list[$v['m_date']] = $v;
                } else {
                    $new_other_list[$v['m_date']]['total'] = roundToString($new_other_list[$v['m_date']]['total']+$v['total']);
                }
            }
            $cost_list[] = [
                'country_code'=>'',
                'country'=>'其他',
                'total'=>roundToString(array_sum(array_column($new_other_list,'total'))),
                'rate'=>roundToString($other_cost_rate),
                'list'=>$new_other_list
            ];
        }
        //金额转换
        return ['stock_list'=>$stock_list,'cost_list'=>$cost_list];
    }
    //被封资金统计
    public function key6() {
        $res_list = [];
        $db = dbFMysql::getInstance();
        foreach (self::$years as $year=>$m_date_list) {
            //获取本年份的值
            $fields = 'reportDateMonth as m_date,countryCode as country_code,sum(key6) as total';
            $group_by = ['reportDateMonth,countryCode'];
            $list = boardModel::getSqlWhereList($db,$year,$m_date_list,$group_by,$fields,[]);
            $res_list = array_merge($res_list,$list);
        }
        $res_list = self::getPriceByRoute1($res_list,self::$search_month);
        $new_list = [];
        foreach (self::$search_month as $month) {
            $item = [
                'm_date'=>$month,
                'total'=>0,
                'list'=>[],
            ];
            foreach ($res_list as $v){
                if ($v['m_date'] == $month) {
                    $item['total'] += $v['total'];
                    $item['list'][$v['country_code']] = $v;
                }
            }
            $new_list[] = $item;
        }
        $country_data = marketForm::getCountryList();
        //格式处理
        foreach ($new_list as &$v) {
            $v['total'] = roundToString($v['total']);
            $v['list'] = array_values($v['list']);
            foreach ($v['list'] as &$v1) {
                $v1['country'] = isset($country_data[$v1['country_code']])?$country_data[$v1['country_code']]['country']:'';
                $v1['total'] = roundToString($v1['total']??0);
            }
        }
        return $new_list;
    }














}