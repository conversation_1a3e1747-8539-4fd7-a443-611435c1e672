<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/11 10:08
 */

namespace plugins\logistics\models;

use core\lib\db\dbLMysql;

class authModel
{
    //获取系统权限
    public static function getAuth() {
        $cdb = dbLMysql::getInstance();
        $all_auth = $cdb->table('auth')
            ->where('where status=1')
            ->order('sort asc')->list();
//        dd(self::getAuthList(0,$all_auth));
        return self::getAuthList(0,$all_auth);
    }
    private static function getAuthList($pid,$auth_list) {
        $res_data = [];
        if (count($auth_list) > 0) {
            foreach ($auth_list as $key=>$val) {
                if ($val['p_id'] == $pid) {
                    $res_data[$key] = $val;
                    unset($auth_list[$key]);
                    $res_data[$key]['child'] = array_values(self::getAuthList($val['id'],$auth_list));
                }
            }
        }
        $res_data = array_values($res_data);
        return $res_data;
    }
}