<?php

namespace admin\controller;

use admin\form\sysRoleForm;

class sysRoleController
{
    //角色列表
    public function getlist()
    {
        $paras_list = array('name', 'page', 'page_size');
        $param = arrangeParam($_POST, $paras_list);
        $list = sysRoleForm::getlist($param);
        returnSuccess($list);
    }
    //获取角色用户列表
    public function getdetail()
    {
        $paras_list = array('id','page','page_size');
        $param = arrangeParam($_POST, $paras_list);
        $info = sysRoleForm::getdetail($param);
        returnSuccess($info);
    }

    //新增编辑角色
    public function edit()
    {
        $paras_list = array('name', 'role_id', 'id');
        $param = arrangeParam($_POST, $paras_list);
        sysRoleForm::edit($param);
    }
    //删除角色
    public function delete()
    {
        $paras_list = array('id');
        $param = arrangeParam($_POST, $paras_list);
        sysRoleForm::delete($param);
    }
    //批量导入
    public function import(){
        $paras_list = array('excel_src');
        $param = arrangeParam($_POST, $paras_list);
        sysRoleForm::import($param);
        returnSuccess('','Setting successful');

    }
    //导出模版生成
    public function getTemplate(){
        sysRoleForm::getTemplate();
    }

    //新增角色用户
    public function adduser()
    {
        $paras_list = array('role_id', 'user_ids');
        $param = arrangeParam($_POST, $paras_list);
        sysRoleForm::addUser($param);
        returnSuccess('', '用户已成功添加到角色');
    }
    //移除角色用户
    public function removeuser(){
        $paras_list = array('role_id','user_id');
        $param = arrangeParam($_POST, $paras_list);
        sysRoleForm::removeUser($param);
        returnSuccess('','删除成功');
    }
    //启用禁用角色用户
    public function disable(){
        $paras_list = array('role_id','user_id');
        $param = arrangeParam($_POST, $paras_list);
        sysRoleForm::disable($param);
        returnSuccess('','修改状态成功');
    }
    //权限设置
    public function auth(){
        $paras_list = array('role_id','auth');
        $param = arrangeParam($_POST, $paras_list);
        sysRoleForm::auth($param);
        returnSuccess('','Setting successful');
    }
    //获取权限列表
    public function getAuth(){
        $list = sysRoleForm::getAuth();
        returnSuccess($list);
    }
    //获取该角色权限
    public static function getRoleAuth()
    {
        $paras_list = array('role_id');
        $param = arrangeParam($_POST, $paras_list);
        $list = sysRoleForm::getRoleAuth($param);
        returnSuccess($list);

    }
}