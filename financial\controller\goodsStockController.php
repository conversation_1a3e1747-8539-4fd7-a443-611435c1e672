<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/26 15:36
 */

namespace financial\controller;

use admin\form\goodsMattersFrom;
use core\jobs\goodsStockExportJobs;
use core\jobs\goodsStockImportErrorDataJobs;
use core\jobs\goodsStockImportJobs;
use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\rediskeys;
use financial\form\checkoutForm;
use financial\form\goodsStorkForm;
use financial\form\runShellTaskForm;
use financial\models\checkoutModel;
use financial\models\goodsStorkModel;
use financial\models\userModel;
use Rap2hpoutre\FastExcel\FastExcel;

class goodsStockController
{
    public function getList(){
        $paras_list = array('date_time','is_error','project_ids','search_type','search_value','page_size','page');
        $request_list = ['date_time'=>'时间',"is_error"=>"是否异常"];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $form = new goodsStorkForm($param['date_time']);
        $data = $form->getList($param);
        $data['export_list'] = goodsStorkModel::$export_key_list;
        returnSuccess($data);
    }
    //总计查询
    public function getColumnCount() {
        $paras_list = array('date_time','is_error','project_ids','search_type','search_value','page_size','page');
        $request_list = ['date_time'=>'时间',"is_error"=>"是否异常"];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $form = new goodsStorkForm($param['date_time']);
        $data = $form->getCount($param);
        returnSuccess($data);
    }
    public function del(){
        $paras_list = array('date_time','is_error','project_ids','search_type','search_value','ids','type');
        $request_list = ['date_time'=>'时间','ids'=>'要操作的列'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $m_date = date('Y-m',strtotime($param['date_time']));
        $type = (int)$param['type'];
        if (!in_array($type,[1,2,3])) {
            returnError('删除方式错误');
        }
        checkoutModel::verifyLock([$param['date_time']]);
        $form = new goodsStorkForm($param['date_time']);
        //删除数据
        if ($type == 1) {
            //删除勾选数据
            $ids = json_decode($param['ids']);
            if (!count($ids)) {
                returnError('参数有误');
            }
            $form->delById($ids);
        } elseif ($type == 2) {
            //删除查询数据
            goodsStorkForm::$list_type = 1;
            $ids = array_column($form->getList($param),'id');
            if (!count($ids)) {
                returnError('未能查询到可删除的数据');
            }
            $form->delById($ids);
        } else {
            //再删除当月数据
            $form->delByMdata($m_date);
        }
        returnSuccess('删除成功');
    }
    //导入
    public function import() {
        $paras_list = array('excel_src', 'date', 'filename');
        $request_list = ['excel_src'=>'表格链接','date'=>'时间','filename'=>'表格名称'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $excel_url = SELF_FK.$param['excel_src'];
        $excel_name = $param['filename'];
        if (!file_exists($excel_url)) {
            returnError('表格不存在');
        }
        $m_date = date('Y-m',strtotime($param['date']));
        $year = date('Y',strtotime($m_date));
        $redis = (new \core\lib\predisV())::$client;
        //验证其他操作是否完成
        checkoutForm::verifyGoodsStock($redis,$m_date);
        //验证数据是否可以上传
        checkoutModel::verifyLock([$m_date]);
        $excel_data = (new FastExcel)->import($excel_url);
        $data = $excel_data->toArray();
        $count_num = count($data);
        if (!$count_num) {
            returnError('表格内不存在数据');
        }
        if ($count_num > 10000) {
            returnError('表格单次限制导入数据10000行');
        }
        //验证表头
        goodsStorkModel::verifyExcelHead($data[0]);
        //导入记录
        $db = dbFMysql::getInstance();
        goodsStorkModel::creatGoodsStockTable($year);
        $import_id = $db->table('goods_stock_import')
            ->insert([
                'user_id'=>userModel::$qwuser_id,
                'excel_name'=>$excel_name,
                'excel_path'=>$param['excel_src'],
                'total'=>$count_num,
                'success_count'=>0,
                'fail_count'=>0,
                'report_date'=>$m_date,
                'created_time'=>date('Y-m-d H:i:s'),
            ]);
        $key = rediskeys::$export_goods_stock.$m_date;
        $data = [
            'success_count'=>0, //采购数
            'error_count'=>0,  //失败数
            'total'=>$count_num, //总数
            'excel_src'=>$excel_url, //上传文件地址
            'date'=>$m_date,
            'import_id'=>$import_id,
            'offset'=>0
        ];
        $redis->set($key,json_encode($data));
        $redis->expire($key,60*60);
        $queue_key = config::get('delay_queue_key', 'app');
        $task = new goodsStockImportJobs($key);
        $redis->zAdd($queue_key, [], 0, serialize($task));
//        $task->task();
        returnSuccess(['progress_key'=>$key,'import_id'=>$import_id],'导入中');

    }
    //查看导入，导出历史记录
    public function getImportLog(){
        $paras_list = array('date_time','page_size','page');
        $param = arrangeParam($_POST, $paras_list);
        $db = dbFMysql::getInstance();
        $db->table('goods_stock_import','a')
            ->leftJoinOut('db','qwuser','b','b.id=a.user_id');
        if (!empty($param['date_time'])) {
            $begin_time = $param['date_time'].'-01 00:00:00';
            $end_time = date('Y-m-d H:i:s',strtotime($begin_time.'+1 month'));
            $db->where('where a.created_time>=:begin_time and a.created_time<=:end_time',['begin_time'=>$begin_time,'end_time'=>$end_time]);
        }
        $data = $db->order('a.id desc')
            ->field('a.*,b.wname as user_name')
            ->pages($param['page'],$param['page_size']);
        if (count($data['list'])) {
            //获取异常标签
            $data['list'] = goodsStorkForm::getLogErrorList($data['list']);
        }
        returnSuccess($data);
    }
    //下载导入的错误数据
    public function exportImportErrorLog(){
        $paras_list = array('import_id');
        $request_list = ['import_id'=>'导入记录ID'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $import_id = (int)$param['import_id'];
        $db = dbFMysql::getInstance();
        $count = $db->table('goods_stock_import_error_log')
            ->where('where import_id=:import_id',['import_id'=>$import_id])
            ->count();
        if (!$count) {
            returnError('未查询到该记录有错误数据');
        }
        $redis = (new \core\lib\predisV())::$client;
        $key = uniqid('goods_stock_import_error');
        $data = [
            'success_count'=>0,
            'total'=>$count,
            'excel_url'=>[],
            'zip_url'=>'',
        ];
        $redis->set($key,json_encode($data));
        $redis->expire($key,60*60);
        $queue_key = config::get('delay_queue_key', 'app');
        $task = new goodsStockImportErrorDataJobs($key,$import_id,1); // 创建任务类实例
        $redis->zAdd($queue_key, [], 0, serialize($task));
//        $task->task();
        returnSuccess(['progress_key'=>$key],'导出中');
    }
    //获取到入异常结果
    public function importErrorList() {
        $paras_list = array('import_id','page','page_size');
        $request_list = ['import_id'=>'导入记录ID'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $import_id = (int)$param['import_id'];
        $data = goodsStorkForm::getImportErrorList($import_id,$param['page'],$param['page_size']);
        returnSuccess($data);
    }
    //导出
    public function exportData() {
        $paras_list = array('date_time','is_error',"search_type","search_value","project_ids",'ids','type','export_list');
        $request_list = ['date_time'=>'时间','ids'=>'要操作的列','type'=>'导出类型','export_list'=>'要导出的字段'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        if (empty($param['date_time'])) {
            returnError('请选择查询的时间');
        }
        if (empty($param['export_list']) || $param['export_list']=='[]') {
            returnError('请选择要导出的字段');
        }
        $export_list = json_decode($param['export_list']);
        $date_time = $param['date_time'];
        $form = new goodsStorkForm($param['date_time']);
        $db = dbFMysql::getInstance();
        $db->table($form->table_name)
            ->where('where is_delete = 0 and m_date=:date_time',['date_time'=>$date_time]);
        if ($param['type'] == 0) {
            $ids = json_decode($param['ids']);
            if (!count($ids)) {
                returnError('请选择要导出得数据');
            }
            $db->whereIn('id',$ids);
        }
        $count = $db->count();
        if (!$count) {
            returnSuccess('未查询到任何数据');
        }
        $redis = (new \core\lib\predisV())::$client;
        $key = uniqid('export_goods_stock');
        $data = [
            'success_count'=>0,
            'total'=>$count,
            'excel_url'=>[],
            'zip_url'=>'',
        ];
        $redis->set($key,json_encode($data));
        $redis->expire($key,60*60);
        $queue_key = config::get('delay_queue_key', 'app');
        $task = new goodsStockExportJobs($key,$param,$export_list,userModel::$qwuser_id,1); // 创建任务类实例
        $redis->zAdd($queue_key, [], 0, serialize($task));
//        $task->task();
        returnSuccess(['progress_key'=>$key],'导出中');
    }
    //修改
    public function edit(){
        $paras_list = array('id','m_date','project_id','project_name','yunying_id','yunying_name','p_asin','asin','sku','goods_name','country_code','country','fbm_local_num','fbm_local_overseas_num','fbm_local_overseas_price','store_name');
        $request_list = ['id'=>'ID','m_date'=>'数据时间','project_id'=>'项目','project_name'=>'项目名','yunying_name'=>'运营姓名','yunying_id'=>'运营','country_code'=>'国家编码','country'=>'国家名','p_asin'=>'父ASIN','asin'=>'ASIN','sku'=>'库存SKU','goods_name'=>'品名','store_name'=>"店铺名称",'fbm_local_num'=>'FBA在库总库存数量','fbm_local_overseas_num'=>'（FBA在途+在库+海外仓）总库存数量','fbm_local_overseas_price'=>'（FBA在途+在库+海外仓）总库存采购成本+头程成本'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        checkoutModel::verifyLock([$param['m_date']]);
        $redis = (new \core\lib\predisV())::$client;
        $form = new goodsStorkForm($param['m_date']);
        $form->editData($param);
        returnSuccess([],'修改成功');
    }
    //重新匹配增量数据
    public function countAgin() {
        $paras_list = array('date_time');
        $request_list = ['date_time'=>'月份'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $m_date = $param['date_time'];
        if (empty($m_date)) {
            returnError('请选择重新匹配的月份');
        }
        //验证数据是否可以上传
        checkoutModel::verifyLock([$m_date]);
        //命令执行
        $redis_key = 'oa_count_goods_data_'.$m_date;
        $redis = (new \core\lib\predisV())::$client;
        if ($redis->exists($redis_key)) {
            $data = json_decode($redis->get($redis_key),true);
            returnError($data['message']);
        }
        $r_data = [
            'page'=>1,
            'total'=>0,
            'message'=>$m_date.'库存数据正在准备重新匹配'
        ];
        $redis->set($redis_key,json_encode($r_data));
        $redis->expire($redis_key,0.5*60*60);
        $targetFile = SELF_FK . '/task/shell/count_goods_stock_agin.sh ' . $m_date . '  > /dev/null 2>&1 &';
        shell_exec($targetFile);
        returnSuccess([],'开始重新计算');
    }















}