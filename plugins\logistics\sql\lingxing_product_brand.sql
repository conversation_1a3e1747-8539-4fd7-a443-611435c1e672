-- 领星产品品牌表
-- 用于存储从领星API同步的产品品牌数据
-- 数据库：dbErpMysql

CREATE TABLE `lingxing_product_brand` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bid` int(11) NOT NULL DEFAULT '0' COMMENT '品牌ID',
  `title` varchar(500) NOT NULL DEFAULT '' COMMENT '品牌名称',
  `brand_code` varchar(100) NOT NULL DEFAULT '' COMMENT '品牌简码',
  
  -- 系统字段
  `sync_date` date NOT NULL COMMENT '同步日期',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-否 1-是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bid` (`bid`),
  KEY `idx_sync_date` (`sync_date`),
  KEY `idx_is_deleted` (`is_deleted`),
  KEY `idx_brand_code` (`brand_code`),
  KEY `idx_title` (`title`(191))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='领星产品品牌表';

-- 创建索引优化查询性能
-- ALTER TABLE `lingxing_product_brand` ADD INDEX `idx_sync_date_deleted` (`sync_date`, `is_deleted`);
-- ALTER TABLE `lingxing_product_brand` ADD INDEX `idx_brand_search` (`title`(100), `brand_code`);
