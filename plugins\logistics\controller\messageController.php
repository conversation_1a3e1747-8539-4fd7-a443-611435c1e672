<?php
/**
 * @author: zhangguoming
 * @Time: 2025/5/27 17:14
 */

namespace plugins\logistics\controller;

use core\lib\db\dbLMysql;
use plugins\logistics\models\userModel;

class messageController
{
    public function getList()
    {
        $paras_list = array('is_read','page','page_size','type');
        $param = arrangeParam($_GET, $paras_list);
        $is_read = (int)$param['is_read'];
        $type = (int)$param['type'];
        $ldb = dbLMysql::getInstance();
        $ldb->table('messages')
            ->where('where qw_userid=:qw_userid',['qw_userid'=>userModel::$wid]);
        if ($is_read > -1) {
            $ldb->andWhere('is_read=:is_read',['is_read'=>$is_read]);
        }
        if ($type == 2) {
            $ldb->andWhere('type=:type',['type'=>$type]);
        }
        $ldb->field('id,title,qw_userid,text,type,is_read,created_at,remarks');
        $ldb->order('is_read asc,id desc');
        $list = $ldb->pages($param['page'],$param['page_size']);
        returnSuccess($list);
    }
    
    public function getMessageCount() {
        //计数
        $ldb = dbLMysql::getInstance();
        $project_msg_count = $ldb->table('messages')
            ->where('where qw_userid=:qw_userid and is_read=0',['qw_userid'=>userModel::$wid])
            ->count();
        $project_copy_msg_count = $ldb->table('messages')
            ->where('where qw_userid=:qw_userid and is_read=0',['qw_userid'=>userModel::$wid])
            ->andWhere('type=2')
            ->count();
        $list = [
            'project_msg_count'=>$project_msg_count,
            'project_copy_msg_count'=>$project_copy_msg_count,
        ];
        returnSuccess($list);
    }
    
    public function getDetail()
    {
        $id = (int)$_GET['id'];
        if (!$id) {
            SetReturn(-1, '参数有误');
        }
        $ldb = dbLMysql::getInstance();
        $ldb->table('messages');
        $ldb->where('where id = '.$id);
        $ldb->field('id,title,qw_userid,type,text,model_id,created_at,remarks');
        $data = $ldb->one();
        $ldb->update(['is_read'=>1]);
        returnSuccess(['data'=>$data]);
    }

    //全部标记已读
    public function setAllRead()
    {
        $type = (int)$_GET['type'];
        $ldb = dbLMysql::getInstance();
        $user = json_decode(USER_INFO,true);

        $ldb->table('messages');
        $ldb->where('where qw_userid = :qw_userid and is_read = 0',['qw_userid'=>$user['wid']]);
        if ($type == 2) {
            $ldb->andWhere('type=2');
        }
        $ldb->update(['is_read'=>1]);
        SetReturn(0, '全部已读');
    }

    //获取企微通知的消息
    public function getMsgDetail() {
        $secret_code = $_POST['data']??'';
        if (empty($secret_code)) {
            SetReturn(-1,'参数有误');
        }
        $data = qwMsgDecryption($secret_code);
        $data  = json_decode($data,true);
        $id = $data['id']??0;
        if (!$id) {
            SetReturn(-1,'参数有误');
        }
        $ldb = dbLMysql::getInstance();
        $ldb->table('messages');
        $ldb->where('where id = '.$id);
        $data = $ldb->one();
        $ldb->update(['is_read'=>1]);
        returnSuccess(['data'=>$data]);
    }
}
