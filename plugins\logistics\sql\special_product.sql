CREATE TABLE IF NOT EXISTS `special_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `product_id` int(11) NOT NULL COMMENT '商品ID',
  `product_name` varchar(255) NOT NULL COMMENT '商品名称',
  `sku` varchar(100) NOT NULL COMMENT 'SKU',
  `country_code` varchar(10) NOT NULL COMMENT '站点',
  `listing_stage` varchar(50) NOT NULL COMMENT 'Listing阶段',
  `special_days` int(11) NOT NULL DEFAULT '0' COMMENT '特殊天数',
  `remark` text COMMENT '备注',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sku` (`sku`),
  KEY `idx_country_stage` (`country_code`,`listing_stage`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购特殊产品表';