<?php

namespace plugins\shop\models;

use Exception;

class relationModel extends baseModel
{
    public string $table = 'relations';

    private $table_map = [
        'email'      => '邮箱',
        'phone_card' => '电话卡',
        'domain'     => '域名',
        'credit_card'     => '信用卡',
        'receive_account' => '收款账户',
        'receive_card' => '收款卡号',
        'trademark' => '商标',
        'legal_person' => '法人',
        'company' => '公司',
        'shop' => '店铺',
    ];

    private $field_map = [
        'email'      => 'email_account',
        'phone_card' => 'phone_number',
        'domain' => 'domain',
        'credit_card' => 'card_number',
        'receive_account' => 'account_name',
        'receive_card' => 'card_number',
        'trademark' => 'brand_name',
        'legal_person' => 'name',
        'company' => 'company_name',
        'shop' => 'shop_number',

    ];

    /**
     * 验证ID是否存在于指定表中
     * @param string $table 表名
     * @param int $id ID
     * @return bool
     * @throws Exception 如果ID不存在则抛出异常
     */
    public function validateIdExists(string $table, int $id): bool
    {
        $exists = $this->db->table($table)
            ->where('id = :id', ['id' => $id])
            ->one();
            
        if (!$exists) {
            returnError($this->table_map[$table] . '数据不存在');
        }
        
        return true;
    }

    public static array $paras_list = [
        'from_table' => '来源表',
        'from_id'    => '来源ID',
        'to_table'   => '目标表',
        'to_id'      => '目标ID',
    ];

      /**
     * 获取关联记录
     * @param string $fromTable 来源表
     * @param int $fromId 来源ID
     * @param string $toTable 目标表
     * @return array|null|bool
     */
    public function getRelation(string $fromTable, int $fromId, string $toTable): ?array
    {
        return $this->db->table($this->table)
            ->where('from_table = :from_table AND from_id = :from_id AND to_table = :to_table', [
                'from_table' => $fromTable,
                'from_id'    => $fromId,
                'to_table'   => $toTable
            ])
            ->one() ?: null;
    }

    /**
     * @param string $toTable
     * @param int $toId
     * @param string|null $fromTable
     * @return array|null 
     */
    public function getReverseRelation(string $toTable, array $toId, mixed $fromTable = null): ?array
    {
        $this->db->table($this->table)
            ->where('to_table = :to_table', [
                'to_table'   => $toTable,
            ])->whereIn('to_id', $toId);

        if ($fromTable) {
            $this->db->andWhere('from_table = :from_table', ['from_table' => $fromTable]);
        }

        $list = $this->db->list();
        $ret = [];
    
        // 查出具体的数据
        foreach ($list as $item) {
            $relation = $this->db->table($item['from_table'])
                ->field($this->field_map[$item['from_table']].' as name')
                ->where('id = :id', ['id' => $item['from_id']])
                ->one();
            $item['relation_from_name'] = $relation['name'];
            !isset($ret[$item['to_id']]) && $ret[$item['to_id']] = [];
            $ret[$item['to_id']][] = $item;
        }
        return $ret;
    }

    public function getIdsByRelation(string $toTable, mixed $fromTable = null): array
    {
        $this->db->table($this->table)->where('to_table = :to_table', ['to_table'   => $toTable]);
        if ($fromTable) {
            $this->db->whereIn('from_table', $fromTable);
        }

        $list = $this->db->list();

        return array_column($list, 'to_id');
    }

      /**
     * 创建关联关系
     * @param string $fromTable 来源表
     * @param int $fromId 来源ID
     * @param string $toTable 目标表
     * @param int $toId 目标ID
     * @return int
     */
    public function createRelation(string $fromTable, int $fromId, string $toTable, int $toId): int
    {
          // 验证目标ID是否存在
        $this->validateIdExists($toTable, $toId);
        
        return $this->add([
            'from_table' => $fromTable,
            'from_id'    => $fromId,
            'to_table'   => $toTable,
            'to_id'      => $toId,
        ]);
    }

      /**
     * 更新关联关系
     * @param string $fromTable 来源表
     * @param int $fromId 来源ID
     * @param string $toTable 目标表
     * @param int $toId 新的目标ID
     * @return void
     */
    public function updateRelation(string $fromTable, int $fromId, string $toTable, int $toId): void
    {
          // 验证目标ID是否存在
        $this->validateIdExists($toTable, $toId);
        
        $relation = $this->getRelation($fromTable, $fromId, $toTable);

        if ($relation) {
              // 如果存在关联且目标ID不同,则更新
            if ($relation['to_id'] != $toId) {
                $this->db->table($this->table)
                    ->where('id = :id', ['id' => $relation['id']])
                    ->update([
                        'to_id'      => $toId,
                    ]);
            }
        } else {
              // 如果不存在关联则创建
            $this->createRelation($fromTable, $fromId, $toTable, $toId);
        }
    }

      /**
     * 删除关联关系
     * @param string $fromTable 来源表
     * @param int $fromId 来源ID
     * @param string $toTable 目标表
     * @return bool
     */
    public function deleteRelation(string $fromTable, int $fromId, string $toTable): bool
    {
        return $this->db->table($this->table)
            ->where('from_table = :from_table AND from_id = :from_id AND to_table = :to_table', [
                'from_table' => $fromTable,
                'from_id'    => $fromId,
                'to_table'   => $toTable
            ])
            ->delete();
    }

    public function getListByIds($to_table, $to_ids, $from_table = '')
    {
        $this->db->table($this->table)
            ->where('to_table = :to_table', ['to_table' => $to_table])
            ->whereIn('to_id', $to_ids);

        if ($from_table) {
            $this->db->where('from_table = :from_table', ['from_table' => $from_table]);
        }

        return $this->db->list();
    }
}
