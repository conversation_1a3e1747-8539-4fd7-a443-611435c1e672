<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/20 9:32
 */

namespace financial\controller;

use core\lib\db\dbFMysql;
use financial\form\checkoutForm;
use financial\form\mskuReportForm;
use financial\form\sellerForm;
use financial\models\checkoutModel;
use financial\models\userModel;
use Rap2hpoutre\FastExcel\FastExcel;
use task\form\lixingXingApiForm;

class sellerController
{
    public function getList()
    {
        $paras_list = array('real_name', 'is_new', 'is_local', 'is_ban', 'is_jing', 'is_fanou', 'order_by', 'is_error', 'page', 'page_size');
        $param = arrangeParam($_POST, $paras_list);
        $is_new = (int)$param['is_new'];
        $is_local = (int)$param['is_local'];
        $is_ban = (int)$param['is_ban'];
        $is_jing = (int)$param['is_jing'];
        $is_fanou = (int)$param['is_fanou'];
        $is_error = (int)$param['is_error'];
        $db = dbFMysql::getInstance();
        $db->table('seller', 'a')
            ->leftJoinOut('db', 'qwuser', 'b', 'b.id=a.updated_user_id')
            ->where('where a.is_delete=0');
        if (!empty($param['real_name'])) {
            $db->andWhere('a.real_name like :real_name', ['real_name' => '%' . $param['real_name'] . '%']);
        }
        if ($is_new > -1) {
            $db->andWhere('a.is_new = :is_new', ['is_new' => $is_new]);
        }
        if ($is_local > -1) {
            $db->andWhere('a.is_local = :is_local', ['is_local' => $is_local]);
        }
        if ($is_ban > -1) {
            $db->andWhere('a.is_ban = :is_ban', ['is_ban' => $is_ban]);
        }
        if ($is_jing > -1) {
            $db->andWhere('a.is_jing = :is_jing', ['is_jing' => $is_jing]);
        }
        if ($is_fanou > -1) {
            $db->andWhere('a.is_fanou = :is_fanou', ['is_fanou' => $is_fanou]);
        }
        if ($is_error > -1) {
            $db->andWhere('a.is_error = :is_error', ['is_error' => $is_error]);
        }
        $order_str = '';
        if (!empty($param['order_by'])) {
            $order_by = json_decode($param['order_by'], true);
            foreach ($order_by as $k => $ord) {
                $order_str .= 'a.' . $k . ' ' . $ord . ',';
            }
        }
        if (empty($order_str)) {
            $db->order('a.is_new desc,a.id desc');
        } else {
            $db->order(trim($order_str, ','));
        }
        $list = $db->field('a.id,a.sid,a.real_name,a.name,a.lx_name,a.is_new,a.is_error,a.is_local,a.is_ban,a.is_jing,a.is_fanou,a.syn_time,a.updated_time,b.wname as updated_wname')
            ->pages($param['page'], $param['page_size']);
        $list['export_list'] = sellerForm::$export_list;
        //最新同步时间
        $list['syn_time'] = ($db->table('seller')
            ->order('syn_time desc')->one())['syn_time'];
        returnSuccess($list);
    }

    //新增和编辑
    public function edit()
    {
        $paras_list = array('id', 'real_name', 'is_fanou');
        $request_list = ['real_name' => '店铺名称', 'is_fanou' => '泛欧'];
        $length_list = ['real_name' => ['name' => '店铺名称', 'length' => 100]];
        $param = arrangeParam($_POST, $paras_list, $request_list, $length_list);
        $id = (int)$param['id'];
        $db = dbFMysql::getInstance();
        if ($id) {
            $seller = $db->table('seller')
                ->where('where id=:id', ['id' => $id])->one();
            if (!$seller) {
                returnError('未查询到到店铺');
            }
            //重复查询
            $other_seller = $db->table('seller')
                ->where('where id<>:id and real_name=:real_name', ['id' => $id, 'real_name' => $param['real_name']])
                ->one();
            if ($other_seller) {
                returnError('该店铺名已存在');
            }
            $is_error = ($other_seller['name'] == $param['real_name'] ? 0 : 1);

            $db->table('seller')
                ->where('where id=:id', ['id' => $id])
                ->update([
                    'is_error' => $is_error,
                    'is_new' => 0,
                    'real_name' => $param['real_name'],
                    'is_fanou' => (int)$param['is_fanou'],
                    'updated_time' => date('Y-m-d H:i:s'),
                    'updated_user_id' => userModel::$qwuser_id
                ]);
            returnSuccess('', '修改成功');
        } else {
            dd('暂时不支持新增');
            $db->table('seller')
                ->insert([
                    'name' => (int)$param['real_name'],
                    'real_name' => (int)$param['real_name'],
                    'is_ban' => (int)$param['is_ban'],
                    'is_jing' => (int)$param['is_jing'],
                    'is_fanou' => (int)$param['is_fanou'],
                ]);
            returnSuccess('', '新增成功');
        }
    }

    //删除本月店铺msku数据
    public function delMskuReportData()
    {
        $paras_list = array('sid', 'm_date');
        $request_list = ['sid' => '店铺Sid', 'm_date' => '月份'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $sids = json_decode($param['sid']);
        if (!count($sids)) {
            returnError('店铺sid不正确');
        }
        $m_date = $param['m_date'];
        //验证数据是否可以上传
        checkoutModel::verifyLock([$m_date]);
        //验证其他操作是否都已经完成
        $redis = (new \core\lib\predisV())::$client;
        checkoutForm::verifyQueue($redis,$m_date);
        $form = new mskuReportForm($m_date);
        $form->delSellerData($sids, $m_date);
        returnSuccess('', '删除成功');
    }

    //导出
    public function export()
    {
        $paras_list = array('real_name', 'is_new', 'is_local', 'is_ban', 'is_jing', 'is_fanou', 'is_error', 'export_list', 'ids', 'type');
        $request_list = ['export_list' => '导出的字段', 'type' => '导出的类型'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $is_new = (int)$param['is_new'];
        $is_local = (int)$param['is_local'];
        $is_ban = (int)$param['is_ban'];
        $is_jing = (int)$param['is_jing'];
        $is_fanou = (int)$param['is_fanou'];
        $is_error = (int)$param['is_error'];
        $type = (int)$param['type'];
        $export_list = json_decode($param['export_list']);
        if (!count($export_list)) {
            returnError('请选择要导出的字段');
        }
        $db = dbFMysql::getInstance();
        $db->table('seller', 'a')
            ->leftJoinOut('db', 'qwuser', 'b', 'b.id=a.updated_user_id')
            ->where('where a.is_delete=0');
        if (!$type) {
            $ids = json_decode($param['ids']);
            if (!count($ids)) {
                returnError('请选择要导出的数据');
            }
            $db->whereIn('a.id', $ids);
        } else {
            if (!empty($param['real_name'])) {
                $db->andWhere('a.real_name like :real_name', ['real_name' => $param['real_name']]);
            }
            if ($is_new > -1) {
                $db->andWhere('a.is_new = :is_new', ['is_new' => $is_new]);
            }
            if ($is_local > -1) {
                $db->andWhere('a.is_local = :is_local', ['is_local' => $is_local]);
            }
            if ($is_ban > -1) {
                $db->andWhere('a.is_ban = :is_ban', ['is_ban' => $is_ban]);
            }
            if ($is_jing > -1) {
                $db->andWhere('a.is_jing = :is_jing', ['is_jing' => $is_jing]);
            }
            if ($is_fanou > -1) {
                $db->andWhere('a.is_fanou = :is_fanou', ['is_fanou' => $is_fanou]);
            }
            if ($is_error > -1) {
                $db->andWhere('a.is_error = :is_error', ['is_error' => $is_error]);
            }
        }
        $db->order('is_new desc')
            ->field('a.id,a.real_name,a.lx_name,a.name,a.is_error,a.is_new,a.is_local,a.is_ban,a.is_jing,a.is_fanou,a.updated_time,b.wname as updated_wname');
        $list = $db->list();
        $url = sellerForm::exportSeler($list, $export_list);
        returnSuccess(['url' => $url]);
    }

    //同步店铺数据
    public function synStore()
    {
        //3s内可以更新完，所以就不用异步了
        lixingXingApiForm::synSeller('');
        returnSuccess('', '店铺更新成功');
    }

    //店铺导入
    public function import()
    {
        $paras_list = array('excel_src');
        $request_list = ['excel_src' => '导入表格地址'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

        $excel_url = SELF_FK . $param['excel_src'];

        // 检查 Excel 文件是否存在
        if (!file_exists($excel_url)) {
            returnError('表格不存在');
        }

        // 使用 FastExcel 库导入 Excel 文件内容
        $excel_data = (new FastExcel)->import($excel_url);
        $data = $excel_data->toArray();

        // 检查 Excel 文件是否为空
        if (!count($data)) {
            returnError('表格内不存在数据');
        }

        // 获取数据库实例
        $db = dbFMysql::getInstance();
        $currentTime = date('Y-m-d H:i:s');

        try {
            foreach ($data as $index => $row) {
                // 将 '-' 转换为空字符串 '' 或 null
                $real_name = isset($row['店铺名称']) && $row['店铺名称'] === '-' ? '' : $row['店铺名称'];
                $is_ban = isset($row['被封店铺(1是,0否)']) && $row['被封店铺(1是,0否)'] === '-' ? null : $row['被封店铺(1是,0否)'];
                $is_jing = isset($row['精铺店铺(1是,0否)']) && $row['精铺店铺(1是,0否)'] === '-' ? null : $row['精铺店铺(1是,0否)'];
                $is_fanou = isset($row['泛欧店铺(1是,0否)']) && $row['泛欧店铺(1是,0否)'] === '-' ? null : $row['泛欧店铺(1是,0否)'];
                $is_local = isset($row['本土店铺(1是,0否)']) && $row['本土店铺(1是,0否)'] === '-' ? null : $row['本土店铺(1是,0否)'];

                $sid = isset($row['SID']) && $row['SID'] === '-' ? null : $row['SID'];
                $lx_name = isset($row['领星店铺名']) && $row['领星店铺名'] === '-' ? null : $row['领星店铺名'];

                // 如果 SID 存在，则更新记录，否则插入新记录
                if ($sid) {


                    $existingSeller = $db->table('seller')->where('where sid = :sid', ['sid' => $sid])->one();

                    if ($existingSeller) {
                        // 更新现有记录
                        $db->table('seller')->where('where sid = :sid', ['sid' => $sid])->update([
                            'real_name' => $real_name,
                            'lx_name' => $lx_name,
                            'is_local' => $is_local,
                            'is_ban' => $is_ban,
                            'is_jing' => $is_jing,
                            'is_fanou' => $is_fanou,
                            'updated_time' => $currentTime,
                        ]);
                    } else {
                        // 插入新记录
                        $db->table('seller')->insert([
                            'real_name' => $real_name,
                            'lx_name' => $lx_name,
                            'sid' => $sid,
                            'is_local' => $is_local,
                            'is_ban' => $is_ban,
                            'is_jing' => $is_jing,
                            'is_fanou' => $is_fanou,
                            'updated_time' => $currentTime,
                            'mid' => 0,
                            'name' => '-',
                            'seller_id' => 0,
                            'account_name' => '-',
                            'seller_account_id' => 0,
                            'region' => '-',
                            'country' => '-',
                            'status' => 2,
                        ]);
                    }
                }
            }

            returnSuccess('', '导入成功');
        } catch (\Exception $e) {
            returnError('导入失败: ' . $e->getMessage());
        }
    }



}