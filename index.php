<?php
/**
 * Created by 漠山.
 * @purpose 描述
 * @Author: 漠山
 * @Time: 2023/12/3 14:28
 * 入口文件
 * 加载函数
 * 启动框架
 */
//echo phpinfo();die;//memory_get_usage

//set_time_limit(0);
define('SELF_FK', realpath('')); //当前框架所在的根目录
//define('SELF_FK', $_SERVER['DOCUMENT_ROOT']); //当前框架所在的根目录
define('CORE', SELF_FK.'/core'); //核心文件目录
define('DEBUG', true); //调试模式设置
define('LOG_PATH', SELF_FK.'/log'); //调试模式设置
define('SYS_PUBLISH',false); //系统发版中

define('ROOT', SELF_FK."/");
require_once "environment.php"; //系统环境
require_once ROOT."/vendor/autoload.php";
include  CORE.'/common/function.php';
include  CORE.'/common/return.php';
include  CORE.'/common/phperror.php';
//方法请求
include CORE.'/fk.php';
//自动加载类
spl_autoload_register('\core\fk::load');

\core\fk::run();