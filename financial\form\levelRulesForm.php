<?php
namespace financial\form;

use core\lib\config;
use core\lib\db\dbFMysql;
use financial\models\userModel;
use Rap2hpoutre\FastExcel\FastExcel;

class levelRulesForm {
    public static array $columns = [
        'level_name' => '等级名称',
        'status' => '状态',
//        'is_fixed' => '内置',
        'type' => '维度',
        'description' => '描述',
        'rules' => '规则',
        'created_time' => '创建时间',
        'updated_time' => '修改时间',
    ];
    public static function verifyRules($rules) {
        if (empty($rules) || $rules == '[]') {
            returnError('规则不能为空');
        }
        $rules = json_decode($rules, true);
        // 验证规则
        foreach ($rules as $v) {
            if (isset($v['market_id']) && $v['market_id'] == -1) {
                $v['market_id'] = [];
            }
            if (empty($v['market_id']) && $v['market_id'] !== []) {
                returnError('请选择目标国家');
            }
            if (empty($v['conditions']) || $v['conditions'] == '[]') {
                returnError('请设置产品数据条件');
            }
            $conditionGroups = $v['conditions'];
            foreach ($conditionGroups as $group) {
                if (isset($group['is_group']) && $group['is_group']) {
                    if (!isset($group['type'])) {
                        returnError('请设置条件组的逻辑类型（且或）');
                    }
                    if (empty($group['group']) || $group['group'] == '[]') {
                        returnError('请设置条件组内的条件');
                    }
                    foreach ($group['group'] as $c) {
                        if (!isset($c['index'])) {
                            returnError('请设置产品数据条件中的指标');
                        }
                        if (intval($c['index']) == 0){
                            returnError('传入数据有误');
                        }
                        if (!isset($c['compare_month'])) {
                            returnError('请设置产品数据条件的比较月份');
                        }
                        if (empty($c['symbol'])) {
                            returnError('请设置产品数据条件的计算符号');

                        }
                        if($c['compare_month'] == 2){
                            if (!isset($c['Interval_value'])) {
                                returnError('请设置区间对比的值');
                            }
                        }else {
                            $c['Interval_value'] = '';
                        }
                        if (empty($c['reference'])) {
                            returnError('请设置产品数据条件的对比对象');
                        }
                        if (!isset($c['value1'])) {
                            returnError('请设置产品数据条件对比的值');
                        }
                        if (!isset($c['value2'])) {
                            returnError('请设置产品数据条件对比的区间值');
                        }
                        if (!isset($c['type'])) {
                            returnError('请设置产品数据条件的类型（且或）');
                        }
                    }
                } else {
                    if (isset($group['group'])) {
                        foreach ($group['group'] as $c) {
                            if (!isset($c['index'])) {
                                returnError('请设置产品数据条件中的指标');
                            }
                            if (intval($c['index']) == 0){
                                returnError('传入数据有误');
                            }
                            if (!isset($c['compare_month'])) {
                                returnError('请设置产品数据条件的比较月份');
                            }
                            if (empty($c['symbol'])) {
                                returnError('请设置产品数据条件的计算符号');
                            }
                            if($c['compare_month'] == 2){
                                if (!isset($c['Interval_value'])) {
                                    returnError('请设置区间对比的值');
                                }
                            }else {
                                $c['Interval_value'] = '';
                            }
                            if (empty($c['reference'])) {
                                returnError('请设置产品数据条件的对比对象');
                            }
                            if (!isset($c['value1'])) {
                                returnError('请设置产品数据条件对比的值');
                            }
                            if (!isset($c['value2'])) {
                                returnError('请设置产品数据条件对比的区间值');
                            }
                            if (!isset($c['type'])) {
                                returnError('请设置产品数据条件的类型（且或）');
                            }
                        }
                    } else {
                        if (!isset($group['index'])) {
                            returnError('请设置产品数据条件中的指标');
                        }
                        if (intval($c['index']) == 0){
                            returnError('传入数据有误');
                        }
                        if (!isset($group['compare_month'])) {
                            returnError('请设置产品数据条件的比较月份');
                        }
                        if (empty($group['symbol'])) {
                            returnError('请设置产品数据条件的计算符号');
                        }
                        if($c['compare_month'] == 2){
                            if (!isset($c['Interval_value'])) {
                                returnError('请设置区间对比的值');
                            }
                        }else {
                            $c['Interval_value'] = '';
                        }
                        if (empty($group['reference'])) {
                            returnError('请设置产品数据条件的对比对象');
                        }
                        if (!isset($group['value1'])) {
                            returnError('请设置产品数据条件对比的值');
                        }
                        if (!isset($group['value2'])) {
                            returnError('请设置产品数据条件对比的区间值');
                        }
                        if (!isset($group['type'])) {
                            returnError('请设置产品数据条件的类型（且或）');
                        }
                    }
                }
            }
        }
        return $rules;
    }
}