<?php
/**
 * @author: fbaStorageDetail
 * @Time: 2025/6/20 09:10
 */

define('SELF_FK', realpath('')); //当前框架所在的根目录
//define('SELF_FK', $_SERVER['DOCUMENT_ROOT']); //当前框架所在的根目录
define('CORE', SELF_FK.'/core'); //核心文件目录
define('DEBUG', true); //调试模式设置
define('LOG_PATH', SELF_FK.'/log'); //调试模式设置
define('SYS_PUBLISH',false); //系统发版中

define('ROOT', SELF_FK."/");
require_once "environment.php"; //系统环境
require_once ROOT."/vendor/autoload.php";
include  CORE.'/common/function.php';
include  CORE.'/common/return.php';
include  CORE.'/common/phperror.php';
//方法请求
include CORE.'/fk.php';
//自动加载类
spl_autoload_register('\core\fk::load');

/**
 * FBA库存明细功能测试类
 */
class FbaStorageDetailTest
{
    private $form;
    private $controller;
    private $test_count = 0;
    private $pass_count = 0;
    private $fail_count = 0;
    
    public function __construct()
    {
        $this->form = new \plugins\logistics\form\fbaStorageDetailForm();
        $this->controller = new \plugins\logistics\controller\fbaStorageDetailController();
    }
    
    /**
     * 断言函数 - 检查是否为数组
     */
    private function assertIsArray($value, $message = '')
    {
        $this->test_count++;
        if (is_array($value)) {
            $this->pass_count++;
            return true;
        } else {
            $this->fail_count++;
            throw new \Exception($message ?: 'Expected array, got ' . gettype($value));
        }
    }
    
    /**
     * 断言函数 - 检查数组是否包含键
     */
    private function assertArrayHasKey($key, $array, $message = '')
    {
        $this->test_count++;
        if (array_key_exists($key, $array)) {
            $this->pass_count++;
            return true;
        } else {
            $this->fail_count++;
            throw new \Exception($message ?: "Array does not contain key: {$key}");
        }
    }
    
    /**
     * 断言函数 - 检查值是否相等
     */
    private function assertEquals($expected, $actual, $message = '')
    {
        $this->test_count++;
        if ($expected == $actual) {
            $this->pass_count++;
            return true;
        } else {
            $this->fail_count++;
            throw new \Exception($message ?: "Expected {$expected}, got {$actual}");
        }
    }
    
    /**
     * 断言函数 - 检查是否为整数
     */
    private function assertIsInt($value, $message = '')
    {
        $this->test_count++;
        if (is_int($value)) {
            $this->pass_count++;
            return true;
        } else {
            $this->fail_count++;
            throw new \Exception($message ?: 'Expected integer, got ' . gettype($value));
        }
    }
    
    /**
     * 断言函数 - 检查是否为浮点数
     */
    private function assertIsFloat($value, $message = '')
    {
        $this->test_count++;
        if (is_float($value)) {
            $this->pass_count++;
            return true;
        } else {
            $this->fail_count++;
            throw new \Exception($message ?: 'Expected float, got ' . gettype($value));
        }
    }
    
    /**
     * 断言函数 - 检查是否大于等于
     */
    private function assertGreaterThanOrEqual($expected, $actual, $message = '')
    {
        $this->test_count++;
        if ($actual >= $expected) {
            $this->pass_count++;
            return true;
        } else {
            $this->fail_count++;
            throw new \Exception($message ?: "Expected {$actual} >= {$expected}");
        }
    }
    
    /**
     * 断言函数 - 检查数组是否包含值
     */
    private function assertContains($needle, $haystack, $message = '')
    {
        $this->test_count++;
        if (in_array($needle, $haystack)) {
            $this->pass_count++;
            return true;
        } else {
            $this->fail_count++;
            throw new \Exception($message ?: "Array does not contain value: {$needle}");
        }
    }
    
    /**
     * 测试获取FBA库存明细列表
     */
    public function testGetFbaStorageDetailList()
    {
        echo "🧪 测试获取FBA库存明细列表...\n";
        
        $param = [
            'page' => 1,
            'page_size' => 10,
            'sync_date' => date('Y-m-d'),
            'search_field' => 'sku',
            'search_value' => 'TEST',
            'is_hide_zero_stock' => '0'
        ];
        
        try {
            $result = $this->form->getFbaStorageDetailList($param);
            
            // 验证返回结果结构
            $this->assertIsArray($result);
            $this->assertArrayHasKey('list', $result);
            $this->assertArrayHasKey('total', $result);
            $this->assertArrayHasKey('page', $result);
            
            // 验证分页参数
            $this->assertEquals(1, $result['page']);
            
            echo "✅ 测试获取FBA库存明细列表 - 通过\n";
            
        } catch (\Exception $e) {
            echo "❌ 获取FBA库存明细列表测试失败: " . $e->getMessage() . "\n";
            $this->fail_count++;
        }
    }
    
    /**
     * 测试获取FBA库存明细统计信息
     */
    public function testGetFbaStorageDetailStatistics()
    {
        echo "🧪 测试获取FBA库存明细统计信息...\n";
        
        $param = [
            'sync_date' => date('Y-m-d'),
            'is_hide_zero_stock' => '0'
        ];
        
        try {
            $statistics = $this->form->getFbaStorageDetailStatistics($param);
            
            // 验证统计数据结构
            $this->assertIsArray($statistics);
            $this->assertArrayHasKey('total_count', $statistics);
            $this->assertArrayHasKey('total_quantity', $statistics);
            $this->assertArrayHasKey('total_amount', $statistics);
            $this->assertArrayHasKey('available_quantity', $statistics);
            $this->assertArrayHasKey('available_amount', $statistics);
            $this->assertArrayHasKey('fba_quantity', $statistics);
            $this->assertArrayHasKey('fba_amount', $statistics);
            $this->assertArrayHasKey('fbm_quantity', $statistics);
            $this->assertArrayHasKey('fbm_amount', $statistics);
            
            // 验证数据类型
            $this->assertIsInt($statistics['total_count']);
            $this->assertIsInt($statistics['total_quantity']);
            // 注意：数据库返回的可能是字符串，需要转换
            if (is_string($statistics['total_amount'])) {
                $statistics['total_amount'] = floatval($statistics['total_amount']);
            }
            $this->assertIsFloat($statistics['total_amount']);
            
            echo "✅ 测试获取FBA库存明细统计信息 - 通过\n";
            
        } catch (\Exception $e) {
            echo "❌ 获取FBA库存明细统计信息测试失败: " . $e->getMessage() . "\n";
            $this->fail_count++;
        }
    }
    
    /**
     * 测试搜索条件过滤
     */
    public function testSearchFilters()
    {
        echo "🧪 测试搜索条件过滤...\n";
        
        $test_cases = [
            ['search_field' => 'sku', 'search_value' => 'TEST_SKU', 'description' => 'SKU搜索'],
            ['search_field' => 'product_name', 'search_value' => '测试产品', 'description' => '产品名称搜索'],
            ['search_field' => 'seller_sku', 'search_value' => 'MSKU123', 'description' => 'MSKU搜索'],
            ['search_field' => 'fnsku', 'search_value' => 'FN123', 'description' => 'FNSKU搜索'],
            ['search_field' => 'asin', 'search_value' => 'B0123456', 'description' => 'ASIN搜索']
        ];
        
        foreach ($test_cases as $case) {
            try {
                $param = [
                    'page' => 1,
                    'page_size' => 5,
                    'search_field' => $case['search_field'],
                    'search_value' => $case['search_value']
                ];
                
                $result = $this->form->getFbaStorageDetailList($param);
                $this->assertIsArray($result);
                echo "  ✅ 测试{$case['description']} - 通过\n";
                
            } catch (\Exception $e) {
                echo "  ❌ 测试{$case['description']}失败: " . $e->getMessage() . "\n";
                $this->fail_count++;
            }
        }
    }
    
    /**
     * 测试筛选条件
     */
    public function testFilters()
    {
        echo "🧪 测试筛选条件...\n";
        
        $test_cases = [
            ['filter' => ['share_type' => 0], 'description' => '非共享类型筛选'],
            ['filter' => ['share_type' => 1], 'description' => '北美共享类型筛选'],
            ['filter' => ['share_type' => 2], 'description' => '欧洲共享类型筛选'],
            ['filter' => ['is_hide_zero_stock' => '1'], 'description' => '隐藏零库存筛选'],
            ['filter' => ['fulfillment_channel_type' => 'FBA'], 'description' => 'FBA配送方式筛选'],
            ['filter' => ['fulfillment_channel_type' => 'FBM'], 'description' => 'FBM配送方式筛选']
        ];
        
        foreach ($test_cases as $case) {
            try {
                $param = array_merge(['page' => 1, 'page_size' => 5], $case['filter']);
                $result = $this->form->getFbaStorageDetailList($param);
                $this->assertIsArray($result);
                echo "  ✅ 测试{$case['description']} - 通过\n";
                
            } catch (\Exception $e) {
                echo "  ❌ 测试{$case['description']}失败: " . $e->getMessage() . "\n";
                $this->fail_count++;
            }
        }
    }
    
    /**
     * 测试排序功能
     */
    public function testSorting()
    {
        echo "🧪 测试排序功能...\n";
        
        $sort_fields = ['id', 'asin', 'product_name', 'seller_sku', 'total', 'available_total', 'afn_fulfillable_quantity', 'sync_date'];
        
        $test_count = 0;
        foreach ($sort_fields as $field) {
            foreach (['asc', 'desc'] as $order) {
                try {
                    $param = [
                        'page' => 1,
                        'page_size' => 5,
                        'sort_field' => $field,
                        'sort_order' => $order
                    ];
                    
                    $result = $this->form->getFbaStorageDetailList($param);
                    $this->assertIsArray($result);
                    $test_count++;
                    
                } catch (\Exception $e) {
                    echo "  ❌ 测试{$field}字段{$order}排序失败: " . $e->getMessage() . "\n";
                    $this->fail_count++;
                }
            }
        }
        echo "  ✅ 测试排序功能完成，共测试{$test_count}种排序组合\n";
    }
    
    /**
     * 测试导出功能
     */
    public function testExportFbaStorageDetail()
    {
        echo "🧪 测试导出功能...\n";
        
        $param = [
            'sync_date' => date('Y-m-d'),
            'search_field' => '',
            'search_value' => ''
        ];
        
        try {
            $export_data = $this->form->exportFbaStorageDetail($param);
            
            // 验证导出数据结构
            $this->assertIsArray($export_data);
            $this->assertArrayHasKey('headers', $export_data);
            $this->assertArrayHasKey('data', $export_data);
            $this->assertIsArray($export_data['headers']);
            $this->assertIsArray($export_data['data']);
            
            // 验证表头包含必要字段
            $required_headers = ['仓库名', 'ASIN', '品名', 'MSKU', 'FNSKU', 'SKU'];
            foreach ($required_headers as $header) {
                $this->assertContains($header, $export_data['headers']);
            }
            
            echo "✅ 测试导出FBA库存明细数据 - 通过\n";
            
        } catch (\Exception $e) {
            echo "❌ 测试导出功能失败: " . $e->getMessage() . "\n";
            $this->fail_count++;
        }
    }
    
    /**
     * 测试参数验证
     */
    public function testParameterValidation()
    {
        echo "🧪 测试参数验证...\n";
        
        // 测试无效的排序字段
        try {
            $param = [
                'page' => 1,
                'page_size' => 5,
                'sort_field' => 'invalid_field',
                'sort_order' => 'desc'
            ];
            
            $result = $this->form->getFbaStorageDetailList($param);
            $this->assertIsArray($result);
            echo "  ✅ 测试无效排序字段处理 - 通过\n";
            
        } catch (\Exception $e) {
            echo "  ❌ 测试无效排序字段处理失败: " . $e->getMessage() . "\n";
            $this->fail_count++;
        }
        
        // 测试无效的排序方向
        try {
            $param = [
                'page' => 1,
                'page_size' => 5,
                'sort_field' => 'id',
                'sort_order' => 'invalid_order'
            ];
            
            $result = $this->form->getFbaStorageDetailList($param);
            $this->assertIsArray($result);
            echo "  ✅ 测试无效排序方向处理 - 通过\n";
            
        } catch (\Exception $e) {
            echo "  ❌ 测试无效排序方向处理失败: " . $e->getMessage() . "\n";
            $this->fail_count++;
        }
    }
    
    /**
     * 测试清理过期数据功能
     */
    public function testCleanExpiredData()
    {
        echo "🧪 测试清理过期数据功能...\n";
        
        try {
            // 测试清理30天前的数据
            $count = $this->form->cleanExpiredData(30);
            
            $this->assertIsInt($count);
            $this->assertGreaterThanOrEqual(0, $count);
            
            echo "✅ 测试清理过期数据功能 - 通过，清理了{$count}条记录\n";
            
        } catch (\Exception $e) {
            echo "❌ 测试清理过期数据功能失败: " . $e->getMessage() . "\n";
            $this->fail_count++;
        }
    }
    
    /**
     * 测试边界条件
     */
    public function testBoundaryConditions()
    {
        echo "🧪 测试边界条件...\n";
        
        // 测试最小页码
        try {
            $param = ['page' => 0, 'page_size' => 10];
            $result = $this->form->getFbaStorageDetailList($param);
            $this->assertIsArray($result);
            echo "  ✅ 测试最小页码边界条件 - 通过\n";
            
        } catch (\Exception $e) {
            echo "  ❌ 测试最小页码边界条件失败: " . $e->getMessage() . "\n";
            $this->fail_count++;
        }
        
        // 测试最大页面大小
        try {
            $param = ['page' => 1, 'page_size' => 1000];
            $result = $this->form->getFbaStorageDetailList($param);
            $this->assertIsArray($result);
            echo "  ✅ 测试最大页面大小边界条件 - 通过\n";
            
        } catch (\Exception $e) {
            echo "  ❌ 测试最大页面大小边界条件失败: " . $e->getMessage() . "\n";
            $this->fail_count++;
        }
        
        // 测试空搜索值
        try {
            $param = ['page' => 1, 'page_size' => 10, 'search_field' => 'sku', 'search_value' => ''];
            $result = $this->form->getFbaStorageDetailList($param);
            $this->assertIsArray($result);
            echo "  ✅ 测试空搜索值边界条件 - 通过\n";
            
        } catch (\Exception $e) {
            echo "  ❌ 测试空搜索值边界条件失败: " . $e->getMessage() . "\n";
            $this->fail_count++;
        }
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "\n=== 🚀 FBA库存明细功能测试开始 ===\n\n";
        
        $start_time = microtime(true);
        
        try {
            $this->testGetFbaStorageDetailList();
            $this->testGetFbaStorageDetailStatistics();
            $this->testSearchFilters();
            $this->testFilters();
            $this->testSorting();
            $this->testExportFbaStorageDetail();
            $this->testParameterValidation();
            $this->testCleanExpiredData();
            $this->testBoundaryConditions();
            
            $end_time = microtime(true);
            $duration = round($end_time - $start_time, 2);
            
            echo "\n=== 📊 测试结果统计 ===\n";
            echo "总测试数: {$this->test_count}\n";
            echo "通过数: {$this->pass_count}\n";
            echo "失败数: {$this->fail_count}\n";
            echo "耗时: {$duration}秒\n";
            
            if ($this->fail_count == 0) {
                echo "\n🎉 所有测试通过 ✅\n";
            } else {
                echo "\n⚠️  部分测试失败 ❌\n";
            }
            
        } catch (\Exception $e) {
            echo "\n=== ❌ 测试执行异常 ===\n";
            echo "错误信息: " . $e->getMessage() . "\n";
            echo "文件: " . $e->getFile() . "\n";
            echo "行号: " . $e->getLine() . "\n";
        }
    }
}

// 如果直接运行此文件，执行所有测试f
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    echo "🔧 开始执行FBA库存明细单元测试...\n";
    
    try {
        $test = new FbaStorageDetailTest();
        $test->runAllTests();
    } catch (\Exception $e) {
        echo "❌ 测试执行异常: " . $e->getMessage() . "\n";
        echo "文件: " . $e->getFile() . " 行号: " . $e->getLine() . "\n";
    }
    
    echo "\n测试结束。\n";
}
