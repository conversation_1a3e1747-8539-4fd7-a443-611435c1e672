<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/24 9:57
 *
 * 导出报告数据
 */

namespace core\jobs;

use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\ExceptionError;
use financial\form\costSharingForm;
use financial\form\customColumnForm;
use financial\form\mskuReportForm;
use financial\form\mskuReportJobForm;
use financial\form\runShellTaskForm;

//分摊
class costSharingJobs
{
    public string $unqueid = '';
    public string $import_data_id;
    public function __construct($import_data_id){
        $this->unqueid = uniqid();
        $this->import_data_id = $import_data_id;
    }
    public function task(){
        //获取当前要分摊的数据
        $key = 'oa_cost_sharing_import';
        $redis = (new \core\lib\predisV())::$client;
        if (!$redis->get($key)) {
            return;
        }
        $db = dbFMysql::getInstance();
        $sharing_data_ = $db->table('cost_sharing_data','a')
            ->leftJoin('cost_sharing','b','b.id=a.import_id')
            ->field('a.id,a.import_id,a.status,a.data,b.dimension,b.m_date')
            ->where('where a.id=:id',['id'=>$this->import_data_id])
            ->one();
        $import_id_ = $sharing_data_?$sharing_data_['import_id']:0;
        if ($sharing_data_) {
            if ($sharing_data_['status'] == 0) {
                //修改状态 为进行中
                $db->table('cost_sharing_data')
                    ->where('where id=:id',['id'=>$sharing_data_['id']])
                    ->update(['status'=>1]);
                //开始
                $m_date = $sharing_data_['m_date'];
                $dimension = json_decode($sharing_data_['dimension']);
                $row = json_decode($sharing_data_['data'],true);
                //获取查询条件
                $condition = self::getCondition($m_date,$dimension,$row);
                //均摊数据整理
                $form = new mskuReportJobForm($m_date);
                $share_status = $form->shareAction($condition,$row['cost_key'],$row['amount'],$row['share_rule_type']);
                if (!$share_status) {
                    //更新失败记录问题
                    $db->table('cost_sharing_data')
                        ->where('where id=:id',['id'=>$sharing_data_['id']])
                        ->update(['status'=>2,'error_reason'=>$form::$share_error_msg]);
                } else {
                    $form->saveShareRes($sharing_data_['id'],$row['cost_key']);
                }
            }
        }
        if ($import_id_) {
            $cost_sharing =  $db->table('cost_sharing')
                ->where('where id=:id',['id'=>$import_id_])
                ->one();
            if ($cost_sharing) {
                $db->table('cost_sharing')
                    ->where('where id=:id',['id'=>$sharing_data_['import_id']])
                    ->update(['status'=>1]);
                //获取下一个表格未开始记录。有就继续，无则完结
                $next_sharing_data = $db->table('cost_sharing_data')
                    ->where('where status = 0')
                    ->one();
                if ($next_sharing_data) {
                    $task = new costSharingJobs($next_sharing_data['id']);
                    $queue_key = config::get('delay_queue_key', 'app');
                    $redis->zAdd($queue_key, [], 0, serialize($task));
                    $redis->expire($key,60*60);
                } else {
                    //修改费用状态
                    $data = json_decode($redis->get($key),true);
                    $import_ids = $data['import_ids'];
                    $redis->del($key);
                    costSharingForm::updateSharingStatus($import_ids);
                    //自定义计算
                    customColumnForm::recalculate(1,[],$cost_sharing['m_date']);
                }
            } else {
                $redis->del($key);
            }
        } else {
            $redis->del($key);
        }

    }

    //获取查询销售额、销量的查询条件
    private static function getCondition($m_date,$dimension,$row) {
        $condition['reportDateMonth'] = $m_date;
        foreach ($dimension as $v) {
            switch ($v) {
                case 'asin':
                    $condition['asin'] = $row['asin'];break;
                case 'parentAsin':
                    $condition['parentAsin'] = $row['parentAsin'];break;
                case 'sid':
                    $condition['sid'] = $row['sid'];break;
                case 'msku':
                    $condition['msku'] = $row['msku'];break;
                case 'sku':
                    $condition['localSku'] = $row['sku'];break;
                case 'continents':
                case 'country':
                    $condition['country_code'] = $row['country_code'];break;
                case 'project1':
                case 'project3':
                    $condition['project_ids'] = $row['project_ids'];break;
            }
        }
        return $condition;
    }

}