<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/16 15:44
 */

namespace financial\controller;

//报表数据——表格
use financial\form\boardTableForm;
use financial\form\leveTableForm;
use financial\form\waringTableForm;
use financial\models\userModel;

class boardTableController
{
    public function __construct()
    {
        ini_set('memory_limit', '1024M');
        set_time_limit(60);
    }
    //sain
    public function asinTable() {
        boardTableForm::$auth_key_ = 'asin_table';
        boardTableForm::$real_table = 'asin';
        //获取该用能查的字段
        $form = new boardTableForm('asin');
        $list = $form::getAsinList(1);
        returnSuccess($list);
    }
    //pasin(父asin)
    public function pasinTable() {
        boardTableForm::$auth_key_ = 'pasin_table';
        boardTableForm::$real_table = 'p_asin';
        //获取该用能查的字段
        $form = new boardTableForm('p_asin');
        $list = $form::getPasinList(1);
        returnSuccess($list);
    }
    //sku
    public function skuTable() {
        boardTableForm::$auth_key_ = 'sku_table';
        boardTableForm::$real_table = 'sku';
        //获取该用能查的字段
        $form = new boardTableForm('sku');
        $list = $form::getSkuList(1);
        returnSuccess($list);
    }
    //运营榜单
    public function yunyingTable() {
        boardTableForm::$auth_key_ = 'yunying_table';
        boardTableForm::$real_table = 'yunying_id';
        //获取该用能查的字段
        $form = new boardTableForm('yunying_id');
        $list = $form::getYunyingList(1);
        returnSuccess($list);
    }
    //店铺
    public function sidTable() {
        boardTableForm::$auth_key_ = 'store';
        boardTableForm::$real_table = 'sid';
        //获取该用能查的字段
        $form = new boardTableForm('sid');
        $list = $form::getSidList(1);
        returnSuccess($list);
    }
    //月度汇总
    public function monthTotalTable() {
        boardTableForm::$auth_key_ = 'month_total';
        //获取该用能查的字段
        boardTableForm::$real_table = 'month_total';
        $form = new boardTableForm('asin');
        $list = $form::getMonthTotal();
        returnSuccess($list);
    }
    //爆款
    public function hotGoods() {
        boardTableForm::$auth_key_ = 'hot';
        //获取该用能查的字段
        leveTableForm::$real_table = 'hot_goods';
        $form = new leveTableForm('p_asin');
        $list = $form::hotGoodsTotal(1);
        returnSuccess($list);
    }
    //清仓
    public function clearSale() {
        boardTableForm::$auth_key_ = 'qingcang';
        //获取该用能查的字段
        leveTableForm::$real_table = 'clear_sale';
        $form = new leveTableForm('asin');
        $list = $form::clearSaleTotal(1);
        returnSuccess($list);
    }
    //预警
    public function waringGoods() {
        boardTableForm::$auth_key_ = 'waring';
        //获取该用能查的字段
//        $form = new boardTableForm('waring_goods');
        $form = new waringTableForm('waring_goods');
        $form::$list_type = 1;
        $list = $form::waringGoodsTotal();
        returnSuccess($list);
    }
    //供应商利润贡献
    public function supplierGrossProfit() {
        boardTableForm::$auth_key_ = 'supplier_gross_table';
        //获取该用能查的字段
        boardTableForm::$real_table = 'supplier_sku';
        $form = new boardTableForm('sku');
        $list = $form::supplierGoodsTotal(1);
        returnSuccess($list);
    }
    //产品利润贡献
    public function goodsGrossProfit() {
        boardTableForm::$auth_key_ = 'goods_gross_table';
        //获取该用能查的字段
        boardTableForm::$real_table = 'goods_sku';
        $form = new boardTableForm('sku');
//        $list = $form::goodsTotal(1);
        $list = $form::goodsGrossTotal(1);
        returnSuccess($list);
    }
    //产品利润贡献底部统计
    public function goodsGrossProfitCount() {
        boardTableForm::$auth_key_ = 'goods_gross_table';
        //获取该用能查的字段
        boardTableForm::$real_table = 'goods_sku';
        $form = new boardTableForm('sku');
        $list = $form::goodsTotalCount();
        returnSuccess($list);
    }







}