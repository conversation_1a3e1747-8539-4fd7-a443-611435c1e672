<?php
/**
 * @author: zhangguoming
 * @Time: 2024/1/4 17:47
 */


use Swoole\Coroutine;
use Swoole\Coroutine\Http\Client;
use function Swoole\Coroutine\run;

run(function () {
    //$client = new Client('127.0.0.1', 9502);
    $client = new Client('39.101.133.112', 9502);
    $ret = $client->upgrade('/');
    if ($ret) {
        $i = 0;
        while(true) {
            $data = ['id'=>1,'msg'=>'hello'.++$i];
            $client->push(json_encode($data));
            Coroutine::sleep(5);
        }
    }
});