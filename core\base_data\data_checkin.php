<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/14 9:20
 */
return array(
    // 统计规则
    'rules' => [
        ['id' => '1', 'name' => '请假扣款规则'],
        ['id' => '2', 'name' => '旷工扣款规则'],
        ['id' => '3', 'name' => '缺卡扣款规则'],
        ['id' => '4', 'name' => '迟到早退扣款规则'],
        ['id' => '5', 'name' => '加班计薪规则'],
        ['id' => '6', 'name' => '全勤奖规则'],
    ],
    // 条件类型
    'rule_type' => [
        ['id' => '1', 'name' => '条件'],
        ['id' => '2', 'name' => '条件组'],
    ],

    // 规则的计算符号
    'rules_symbol' => [
        ['id' => '1', 'name' => '>', 'value' => '>'],
        ['id' => '2', 'name' => '≥', 'value' => '>='],
        ['id' => '3', 'name' => '<', 'value' => '<'],
        ['id' => '4', 'name' => '≤', 'value' => '<='],
        ['id' => '5', 'name' => '=', 'value' => '=='],
        ['id' => '6', 'name' => '≠', 'value' => '!='],
    ],

    // 取数规则
    'calc_type' => [
        ['id' => '1', 'name' => '原始数据', 'method' => ''],
        ['id' => '2', 'name' => '四舍五入', 'method' => 'round'],
        ['id' => '3', 'name' => '向上取整', 'method' => 'ceil'],
        ['id' => '4', 'name' => '向下取整', 'method' => 'floor'],
    ],

    // 公式计算符号
    'formula_symbol' => [
        ['id' => '1', 'name' => '+'],
        ['id' => '2', 'name' => '-'],
        ['id' => '3', 'name' => '*'],
        ['id' => '4', 'name' => '/'],
    ],

    // 事假扣款公式值类型
    'personal_absence_value_type' => [
        ['id' => '1', 'name' => '个人', 'list' => [
            ['id' => '1', 'name' => '基本工资'],
            ['id' => '2', 'name' => '综合工资'],
            ['id' => '3', 'name' => '满勤天数'],
            ['id' => '4', 'name' => '实际出勤天数'],
            ['id' => '5', 'name' => '应出勤天数'],
            ['id' => '6', 'name' => '事假天数']
        ]],
        ['id' => '3', 'name' => '自定义'],
    ],

    // 病假扣款
    'sick_absence_value_type' => [
        ['id' => '1', 'name' => '个人', 'list' => [
            ['id' => '1', 'name' => '基本工资'],
            ['id' => '2', 'name' => '综合工资'],
            ['id' => '3', 'name' => '满勤天数'],
            ['id' => '4', 'name' => '实际出勤天数'],
            ['id' => '5', 'name' => '应出勤天数'],
            ['id' => '6', 'name' => '病假天数'],
            ['id' => '7', 'name' => '最低薪资标准'],
        ]],
        ['id' => '3', 'name' => '自定义'],
    ],

    // 旷工扣款
    'absenteeism_value_type' => [
        ['id' => '1', 'name' => '个人', 'list' => [
            ['id' => '1', 'name' => '基本工资'],
            ['id' => '2', 'name' => '综合工资'],
            ['id' => '3', 'name' => '满勤天数'],
            ['id' => '4', 'name' => '实际出勤天数'],
            ['id' => '5', 'name' => '应出勤天数'],
            ['id' => '6', 'name' => '旷工天数'],
        ]],
        ['id' => '3', 'name' => '自定义'],
    ],

    // 扣款
    'value_type' => [
        ['id' => '1', 'name' => '按次数'],
        ['id' => '2', 'name' => '按旷工'],
        ['id' => '3', 'name' => '按事假'],
    ]


);