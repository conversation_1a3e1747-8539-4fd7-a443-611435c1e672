<?php

/**
 * @author: zhangguoming
 * @Time: 2024/3/27 9:02
 */

namespace plugins\assessment\models;

use admin\models\qwuserModel;
use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\db\dbAMysql;
use core\lib\db\dbMysql;
use Cron\CronExpression;
use plugins\assessment\form\messagesFrom;
use plugins\assessment\models\userModel;
use DateTime;
use Exception;
use Throwable;

class assessmentSchemesModel
{
    public static int $id;
    public static string $user_id; // 发起人 qwuser_id
    public static string $scheme_name; // 方案名称
    public static string $assessment_cycle; // 考核周期
    public static string $assessment_type; // 方案类型 1绩效 2提成 3薪资
    public static string $attach; // 方案详情
    /** 实例 attach
     *      "score_type":"2", // 计分方式 1加权 2加和
     *      "score_system":"2" // 【计分方式为加和时】分制，1百分制 2十级制 3自定义分制
     *      "department":"194", // 考核部门
     *      "apply_range":{"users":["339"],"roles":["1"],"departments":["19"]}, // 考核对象
     *      "no_need_assessment_type":"1", // 无需考核对象
     *      "no_need_assessment":[1,2], // 【仅无需考核对象为指定人员时】无需考核对象列表
     *
     * */
    public static bool $assessment_template; // 考核模版
    /** 实例 assessment_template
     *      "assessment_template":1, // 考核模版 1系统 2设计组（还没做）
     *      "list":[{"id": 1, "value":"50"},{"id": 2, "value":"50"}] // 考核模板指标，id指标id value权重/分值
     *
     * */
    public static bool $assessment_scheme_process; // 考核流程设计
    /** 实例 assessment_scheme_process
     *     [
     *      // 1 发起考核 {"id":1, assessment_launch_type 发起方式 1自动2手动 launch_time 发起时间 result_view 结果查看 }
     *      {"id":1,"assessment_launch_type":1,"launch_time":["12","18","10:30"],"result_view":[1,2,3,4,5]},
     *
     *      // 2 工作简述 {"id":2, confirm_day 确认天数，status 状态 1启用0禁用}
     *      {"id":2,"status":1,"confirm_day":3},
     *
     *      // 3 自评 {"id":3, confirm_day 确认天数，default_value 默认值}
     *      {"id":3,"confirm_day":3,"default_value":80},
     *
     *      // 4 上级评分 {"id":4, leader 上级 confirm_day 确认天数}
     *      {"id":4,"leader":"399","confirm_day":3},
     *
     *      // 5 绩效核算 {"id":5, performance_calculation_method 核算方式 1按比例2按阶梯3按等级4手动录入}
     *      // 公式
     *      // { symbol 符号 type 条件/条件组 value_type 值类型（1个人2部门3自定义值） value值 is_abs是否绝对值(0值 1绝对值 2向下取整) users 部门-员工绩效结果指定人员 }
     *      {"id":5,"performance_calculation_method":1,"prize_commission_rule_id":22,
     *          "formula":[
     *              {"symbol":1,"type":1, "value_type":1, "value":[1,19,'CNY'], "is_abs":0},
     *              {"symbol":1,"type":1, "value_type":2, "value":[3,1], "users":[], "is_abs":0},
     *              {"symbol":1,"type":1, "value_type":2, "value":[3,2,19,'CNY',12], "users":[], "is_abs":0},
     *              {"symbol":1,"type":1, "value_type":3, "value":500, "is_abs":0},
     *              {"symbol":1,"type":2, "list":[{"symbol":1,"value_type":1, "value":[3,1], "is_abs":0}]}
     *           ],
     *      },
     *      // 阶梯
     *      // { result绩效/奖金  list 规则 [{symbol 连接符号 target_id 指标id value 值 assessment_calc_symbol 运算符号 }]}
     *      {"id":5,"performance_calculation_method":2,
     *          "formula":[
     *              { "result":100, "list":[{"symbol":1,"target_id":2, "value":1, "assessment_calc_symbol":1}]}
     *              { "result":100, "list":[{"symbol":1,"target_id":2, "value":1, "assessment_calc_symbol":2}]}
     *          ]
     *      },
     *      // 等级核算
     *      {"id":5,"performance_calculation_method":3,"level_rule_id":1},
     *      // 手动录入
     *      {"id":5,"performance_calculation_method":4},
     *
     *      // 6 审批 {"id":6, status 状态 1启用0禁用 approver 审批人 1指定人员2直属上级 approver_user 审批人id}
     *      {"id":6,"status":1,"approver":1,"approver_user":"399"},
     *
     *      // 7 绩效确认 {"id":7, status 状态 1启用0禁用 confirm_day 确认天数}
     *      {"id":7,"status":1,"confirm_day":3}
     * ]
     *
     * */
    public static bool $is_auto; // 是否自动发起
    public static bool $next_auto_time; // 下次自动发起时间
    public static bool $release_time; // 最新发布时间

    // 校验指标详情
    public static function checkAttach($attach): bool
    {
        empty($attach) && returnError('方案详情不能为空');
        empty($attach['score_type']) && returnError('计分方式不能为空');
        $attach['score_type'] == 2 && empty($attach['score_system']) && returnError('分制不能为空');
        empty($attach['department']) && returnError('考核部门不能为空');
        empty($attach['apply_range']) && returnError('考核对象不能为空');
//        empty($attach['no_need_assessment_type']) && returnError('无需考核对象不能为空');
        if ($attach['no_need_assessment_type'] == 1) {
            empty($attach['no_need_assessment']) && returnError('无需考核对象列表不能为空');
        }
        return true;
    }

    // 校验考核模板
    public static function checkAssessmentTemplate($assessment_template): bool
    {
        empty($assessment_template) && returnError('考核模板配置不能为空');
        empty($assessment_template['assessment_template']) && returnError('考核模板不能为空');
        empty($assessment_template['list']) && returnError('考核模板指标不能为空');
        foreach ($assessment_template['list'] as $item) {
            empty($item['id']) && returnError('指标id不能为空');
            empty($item['value']) && returnError('权重/分值不能为空');
        }
        return true;
    }

    // 校验考核流程设计
    public static function checkAssessmentSchemeProcess($assessment_scheme_process): bool
    {
        empty($assessment_scheme_process) && returnError('考核流程设计不能为空');
        foreach ($assessment_scheme_process as $item) {
            empty($item['id']) && returnError('流程id不能为空');
            switch ($item['id']) {
                case 1: // 发起考核
                    empty($item['assessment_launch_type']) && returnError('发起方式不能为空');
                    $item['assessment_launch_type'] == 1 && empty($item['launch_time']) && returnError('发起时间不能为空');
                    $item['assessment_launch_type'] == 1 && empty($item['result_view']) && returnError('结果查看不能为空');
                    break;
                case 2: // 工作简述
                    !isset($item['status']) && returnError('工作简述状态不能为空');
                    $item['status'] == 1 && empty($item['confirm_day']) && returnError('工作简述确认天数不能为空');
                    break;
                case 3: // 自评
                    !isset($item['status']) && returnError('自评状态不能为空');
                    $item['status'] == 1 && empty($item['confirm_day']) && returnError('自评确认天数不能为空');
                    $item['status'] == 1 && empty($item['default_value']) && returnError('自评默认值不能为空');
                    break;
                case 4: // 上级评分
                    empty($item['leader']) && returnError('上级不能为空');
                    empty($item['confirm_day']) && returnError('上级评分确认天数不能为空');
                    break;
                case 5: // 绩效核算
                    empty($item['performance_calculation_method']) && returnError('核算方式不能为空');
                    if ($item['performance_calculation_method'] == 1) {
                        empty($item['formula']) && returnError('按比率核算公式不能为空');
                        $idx = 0;
                        $flag = false;
                        $type = [];
                        foreach ($item['formula'] as $formula) {
                            $idx && empty($formula['symbol']) && returnError('按比率核算符号不能为空');
                            empty($formula['type']) && returnError('按比率核算条件/条件组不能为空');
                            if ($formula['type'] == 1) {
                                empty($formula['value_type']) && returnError('按比率核算条件值类型不能为空');
                                if ($formula['value_type'] == 1 && $formula['value'][0] == 2) { // 个人-提成比例
                                    $flag = true;
                                    $type[] = 1;
                                }
                                if ($formula['value_type'] == 2 && $formula['value'][0] == 2) { // 部门-提成比例
                                    $flag = true;
                                    $type[] = 2;
                                }
                                if ($formula['value_type'] == 3 && empty($formula['value'])) returnError('按比率核算自定义值不能为空');
                                // 非自定义值时，值不能为空
                                if ($formula['value_type'] != 3 && empty($formula['value'][0])) returnError('按比率核算条件值1不能为空');
                                if ($formula['value_type'] != 3 && empty($formula['value'][1])) returnError('按比率核算条件值2不能为空');
                                !isset($formula['is_abs']) && returnError('按比率核算条件是否绝对值不能为空');
                                // 部门 -员工绩效
                                if ($formula['value_type'] == 2 && $formula['value'][0] == 3 && $formula['value'][1] == 1 && empty($formula['users'])) returnError('按比率核算条件指定人员不能为空');
                                // 部门 - 员工业绩
                                if ($formula['value_type'] == 2 && $formula['value'][0] == 4 && empty($formula['value'][1])) returnError('按比率核算条件财务业绩指标不能为空');
                                if ($formula['value_type'] == 2 && $formula['value'][0] == 4 && empty($formula['value'][2])) returnError('按比率核算条件财务业绩指标币种不能为空');
                                if ($formula['value_type'] == 2 && $formula['value'][0] == 4 && empty($formula['value'][3])) returnError('按比率核算条件提成比例不能为空');
                                if ($formula['value_type'] == 2 && $formula['value'][0] == 4 && empty($formula['users'])) returnError('按比率核算条件指定人员不能为空');
                            } elseif ($formula['type'] == 2) {
                                empty($formula['list']) && returnError('按比率核算条件组不能为空');
                                $list_idx = 0;
                                foreach ($formula['list'] as $list) {
                                    $list_idx && empty($list['symbol']) && returnError('按比率核算条件组连接符号不能为空');
                                    empty($list['value_type']) && returnError('按比率核算条件组值类型不能为空');
                                    if ($list['value_type'] == 1 && $list['value'][0] == 2) { // 个人-提成比例
                                        $flag = true;
                                        $type[] = 1;
                                    }
                                    if ($list['value_type'] == 2 && $list['value'][0] == 2) { // 部门-提成比例
                                        $flag = true;
                                        $type[] = 2;
                                    }
                                    if ($list['value_type'] == 3 && empty($list['value'])) returnError('按比率核算条件组自定义值不能为空');
                                    // 非自定义值时，值不能为空
                                    if ($list['value_type'] != 3 && empty($list['value'][0])) returnError('按比率核算条件组值1不能为空');
                                    if ($list['value_type'] != 3 && empty($list['value'][1])) returnError('按比率核算条件组值2不能为空');
                                    !isset($list['is_abs']) && returnError('按比率核算条件组是否绝对值不能为空');
                                    // 部门 -员工绩效
                                    if ($list['value_type'] == 2 && $list['value'][0] == 3 && $list['value'][1] == 1 && empty($list['users'])) returnError('按比率核算条件组指定人员不能为空');
                                    // 部门 - 员工业绩
                                    if ($list['value_type'] == 2 && $list['value'][0] == 4 && empty($list['value'][1])) returnError('按比率核算条件组财务业绩指标不能为空');
                                    if ($list['value_type'] == 2 && $list['value'][0] == 4 && empty($list['value'][2])) returnError('按比率核算条件组财务业绩指标币种不能为空');
                                    if ($list['value_type'] == 2 && $list['value'][0] == 4 && empty($list['value'][3])) returnError('按比率核算条件组提成比例不能为空');
                                    if ($list['value_type'] == 2 && $list['value'][0] == 4 && empty($list['users'])) returnError('按比率核算条件组指定人员不能为空');
                                    $list_idx++;
                                }
                            }
                            $idx++;
                        }
                        $flag && empty($item['prize_commission_rule_id']) && returnError('提成比例不能为空');
                        $flag && empty($item['prize_commission_rule_type']) && returnError('提成比例类型不能为空');
                        $flag && !in_array($item['prize_commission_rule_type'], $type) && returnError('提成比例值不能为空');
                    } elseif ($item['performance_calculation_method'] == 2) {
                        empty($item['formula']) && returnError('按阶梯奖励不能为空');
                        foreach ($item['formula'] as $formula) {
                            !isset($formula['result']) && returnError('按阶梯奖励绩效/奖金不能为空');
                            empty($formula['list']) && returnError('按阶梯奖励规则不能为空');
                            foreach ($formula['list'] as $list) {
                                empty($list['symbol']) && returnError('按阶梯奖励连接符号不能为空');
                                empty($list['target_id']) && returnError('按阶梯奖励指标id不能为空');
                                !isset($list['value']) && returnError('按阶梯奖励值不能为空');
                                empty($list['assessment_calc_symbol']) && returnError('按阶梯奖励运算符号不能为空');
                            }
                        }
                    } elseif ($item['performance_calculation_method'] == 3) {
                        empty($item['level_rule_id']) && returnError('按等级核算等级规则不能为空');
                    } elseif ($item['performance_calculation_method'] == 4) {
                        // 手动录入无需校验
                    }
                    break;
                case 6: // 审批
                    !isset($item['status']) && returnError('审批状态不能为空');
                    $item['status'] == 1 && empty($item['approver']) && returnError('审批人不能为空');
                    $item['status'] == 1 && $item['approver'] == 1 && empty($item['approver_user']) && returnError('审批人id不能为空');
                    break;
                case 7: //绩效确认
                    !isset($item['status']) && returnError('绩效确认状态不能为空');

                    // 20250326 将绩效确认流程移除，因为涉及逻辑太多，不好处理，现默认传禁用该节点来适配原逻辑
                    $item['status'] != 0 && returnError('绩效确认状态只能为禁用');

                    $item['status'] == 1 && empty($item['confirm_day']) && returnError('绩效确认确认天数不能为空');
                    break;
                default:
                    returnError('未知流程');
            }
        }
        return true;
    }


    //  获取方案中的所有涉及的指标、提成比例、等级
    public static function getSchemeDetail($type): array
    {
        if (!in_array($type, ['target', 'commission', 'level'])) return [];
        // 获取非考核完成、终止的考核
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_schemes');
        $adb->where('where is_delete = 0');
        $schemes = $adb->list();

        $target_ids = [];
        $commission_ids = [];
        $level_ids = [];
        foreach ($schemes as $scheme) {
            $template = $scheme['assessment_template'] ? json_decode($scheme['assessment_template'], true) : null;
            $process = $scheme['assessment_scheme_process'] ? json_decode($scheme['assessment_scheme_process'], true) : null;
            if (empty($template['list'])) continue;
            foreach ($template['list'] as $item) {
                $target_ids[] = $item['id'];
            }
            if ($process[4]['performance_calculation_method'] == 1) { // 按公式核算
                foreach ($process[4]['formula'] as $formula) {
                    if ($formula['type'] == 1) { // 条件
                        if ($formula['value'][0] == 2) { // 提成比例
                            $commission_ids[] = $formula['value'][1];
                        }
                    } elseif ($formula['type'] == 2) { // 条件组
                        foreach ($formula['list'] as $sub_formula) {
                            if ($sub_formula['value'][0] == 2) { // 提成比例
                                $commission_ids[] = $sub_formula['value'][1];
                            }
                        }
                    }
                }
            }
            if ($process[4]['performance_calculation_method'] == 3) { // 按等级核算
                $level_ids[] = $process[4]['level_rule_id'];
            }
        }

        // 指标校验
        if ($type == 'target') return array_values(array_unique($target_ids));
        if ($type == 'commission') return array_values(array_unique($commission_ids));
        if ($type == 'level') return array_values(array_unique($level_ids));

        return [];
    }

    // 获取自动生成考核名称
    public static function getAutoAssessmentName($scheme, $assessment_cycle): string
    {
        $assessment_cycle_str = '';
        if ($scheme['assessment_cycle'] == 1) {
            $assessment_cycle_str = date('Y-m', strtotime($assessment_cycle[0]));
        } elseif ($scheme['assessment_cycle'] == 2) {
            $assessment_cycle_str = date('Y-m', strtotime($assessment_cycle[0])) . "-" . date('Y-m', strtotime($assessment_cycle[1]));
        } elseif ($scheme['assessment_cycle'] == 3) {
            $assessment_cycle_str = date('Y-m', strtotime($assessment_cycle[0])) . "-" . date('Y-m', strtotime($assessment_cycle[1]));
        } elseif ($scheme['assessment_cycle'] == 4) {
            $assessment_cycle_str = date('Y', strtotime($assessment_cycle[0]));
        }

        return $scheme['scheme_name'] . "_" . $assessment_cycle_str . "_绩效考核（自动）";
    }

    // 获取考核周期
    public static function getSchemeCycle($scheme)
    {
        if (!$scheme) returnError('id不能为空');
        $assessment_scheme_process = $scheme['assessment_scheme_process'];
        $create_info = $assessment_scheme_process[0];
        $start = '';
        $end = '';
        switch ($scheme['assessment_cycle']) {
            case 1: // 月度
                $start = date('Y-m-01');
                $end = date('Y-m-t');
                break;
            case 2: // 季度
                $diff_month = $create_info['launch_time'][0]; // 第几个月
                $month = date('m');
                $s_month = $month - $diff_month + 1;
                $start = date('Y-' . sprintf('%02d', $s_month) . '-01');
                $end = date('Y-' . sprintf('%02d', $s_month + 2) . '-t');
                break;
            case 3: // 半年度
                $diff_month = $create_info['launch_time'][0]; // 第几月
                $month = date('m');
                $s_month = $month - $diff_month + 1;
                $start = date('Y-' . sprintf('%02d', $s_month) . '-01');
                $end = date('Y-' . sprintf('%02d', $s_month + 5) . '-t');
                break;
            case 4: // 年度
                $start = date('Y-01-01');
                $end = date('Y-12-31');
                break;
            default:
                break;
        }
        return [$start, $end];
    }

    // 获取上个考核周期
    public static function getPreSchemeCycle($assessment_cycle, $type)
    {
        if (!$assessment_cycle || !$assessment_cycle[0] || !$assessment_cycle['1']) returnError('考核周期有误');
        $start = '';
        $end = '';
        switch ($type) {
            case 1: // 月度
                $start = date('Y-m-01', strtotime($assessment_cycle[0] . ' -1 month'));
                $end = date('Y-m-t', strtotime($start));
                break;
            case 2: // 季度
                $start = date('Y-m-01', strtotime($assessment_cycle[0] . ' -3 month'));
                $end = date('Y-m-t', strtotime($start . ' +2 month'));
                break;
            case 3: // 半年度
                $start = date('Y-m-01', strtotime($assessment_cycle[0] . ' -6 month'));
                $end = date('Y-m-t', strtotime($start . ' +5 month'));
                break;
            case 4: // 年度
                $start = date('Y-01-01', strtotime($assessment_cycle[0] . ' -1 year'));
                $end = date('Y-12-31', strtotime($start));
                break;
            default:
                break;
        }
        return [$start, $end];
    }


    // 将执行时间转换为cron表达式
    public static function getSchemeRuntime($type, $runtime)
    {
        $crontab = '';
        // cron语法： 秒 分 小时 日期 月份 星期
        switch ($type) {
            case 1:// 月度 runtime [日期, 时:分]
                $hour_min = explode(':', $runtime[1]);
                $hour = intval($hour_min[0]);
                $min = intval($hour_min[1]);
                $day = intval($runtime[0]);
                $crontab = "{$min} {$hour} {$day} * ?";
                break;
            case 2: // 季度 runtime [第几月, 日期, 时:分]
                $hour_min = explode(':', $runtime[2]);
                $hour = intval($hour_min[0]);
                $min = intval($hour_min[1]);
                $day = intval($runtime[1]);
                $month = $runtime[0] . "," . ($runtime[0] + 3) . "," . ($runtime[0] + 6) . "," . ($runtime[0] + 9);
                $crontab = "{$min} {$hour} {$day} {$month} ?";
                break;
            case 3: // 半年度 runtime [第几月, 日期, 时:分]
                $hour_min = explode(':', $runtime[2]);
                $hour = intval($hour_min[0]);
                $min = intval($hour_min[1]);
                $day = intval($runtime[1]);
                $month = $runtime[0] . "," . ($runtime[0] + 6);
                $crontab = "{$min} {$hour} {$day} {$month} ?";
                break;
            case 4: // 年度 runtime [月份, 日期, 时:分]
                $hour_min = explode(':', $runtime[2]);
                $hour = intval($hour_min[0]);
                $min = intval($hour_min[1]);
                $day = $runtime[1];
                $month = $runtime[0];
                $crontab = "{$min} {$hour} {$day} {$month} ?";
                break;
            default:
                break;
        }
        return $crontab;

    }

    // 获取考核方案中的人员
    public static function getAssessmentSchemesUsers($scheme, $user_roles = [], $department_users = [])
    {
        $apply_range = $scheme['attach']['apply_range'];
        $user_ids = [];
        if (empty($user_roles)) { // 获取所有角色及其用户
            $user_roles = userRolesModel::getRolesAndUsers();
        }
        if (empty($department_users)) { // 获取所有部门及其用户
            $department_users = qwuserModel::getDepartmentUsers();
        }
        $user_role_map = array_column($user_roles, null, 'id');
        $department_users_map = array_column($department_users, null, 'id');

        // 包含三个部分users、roles、departments，然后去除不需要考核的人员
        if (!empty($apply_range['users'])) {
            $users = $apply_range['users'];
            $user_ids = array_merge($user_ids, $users);
        }
        if (!empty($apply_range['roles'])) {
            $roles = $apply_range['roles'];
            foreach ($roles as $role) {
                $role_users = $user_role_map[$role]['users'];
                $user_ids = array_merge($user_ids, $role_users);
            }
        }
        if (!empty($apply_range['departments'])) {
            $departments = $apply_range['departments'];
            foreach ($departments as $department) {
                if (empty($department_users_map[$department])) {
                    continue;
                }
                $user_ids = array_merge($user_ids, $department_users_map[$department]['users']);
            }
        }
        // 去除考核对象中不需要考核的人员
        if (!empty($scheme['attach']['no_need_assessment'])) {
            $no_need_assessment = $scheme['attach']['no_need_assessment'];
            $user_ids = array_diff($user_ids, $no_need_assessment);
        }

        return array_values(array_unique($user_ids));
    }

    // 自动生成考核
    public static function autoAssessment($crontab)
    {
        $scheme_id = $crontab['link_id'];
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_schemes');
        $adb->where('where id = :id and is_delete = 0', ['id' => $scheme_id]);
        $scheme = $adb->one();
        if (!$scheme) {
            // 定时任务设置为已完成
            $adb->table('custom_crontab')->where('where id = :id', ['id' => $crontab['id']])->update(
                ['status' => 1]
            );
            returnError('方案不存在');
        }
        $scheme['attach'] = $scheme['attach'] ? json_decode($scheme['attach'], true) : null;
        $scheme['assessment_template'] = $scheme['assessment_template'] ? json_decode($scheme['assessment_template'], true) : null;
        $scheme['assessment_scheme_process'] = $scheme['assessment_scheme_process'] ? json_decode($scheme['assessment_scheme_process'], true) : null;

        // 当前考核周期
        $assessment_cycle = self::getSchemeCycle($scheme);
        // 获取当前考核方案下的所有考核任务
        $adb->table('assessment');
        $assessments = $adb->where('where is_delete = 0 and a_s_id = :a_s_id', ['a_s_id' => $scheme_id])->list();
        // 当前周期存在考核任务，则不会自动生成考核
        foreach ($assessments as $assessment) {
            $a_time = json_decode($assessment['assessment_cycle'], true);
            if (checkTimeHasIntersect(strtotime($a_time[0]), strtotime($a_time[1]), strtotime($assessment_cycle[0]), strtotime($assessment_cycle[1]))) {
                // 将定时任务设置为待执行，并更新下次执行时间
                // 创建 CronExpression 对象
                $cron = new CronExpression($crontab['cron_expression']);
                $currentDate = new DateTime();
                // 计算下一次执行时间
                $next_run = $cron->getNextRunDate($currentDate)->format('Y-m-d H:i:s');
                $adb->table('custom_crontab');
                $adb->where('where id = :id', ['id' => $crontab['id']]);
                $adb->update(['status' => 0, 'runtime' => $next_run]);
                returnError('已有该考核周期的考核任务，不能发起考核');
            }
        }

        // 生成考核
        $data = [
            'scheme'           => $scheme,
            'assessment_name'  => self::getAutoAssessmentName($scheme, $assessment_cycle),
            'user_id'          => 0,
            'a_s_id'           => $scheme_id,
            'assessment_cycle' => json_encode($assessment_cycle),
            'assessment_desc'  => "自动发起",
            'apply_range'      => json_encode($scheme['attach']['apply_range']),
            'result_view'      => json_encode($scheme['assessment_scheme_process'][0]['result_view']),
            'status'           => 0,
        ];

        try {
            // 生成考核
            $aid = self::createAssessment($data);
        } catch (Throwable $error) {
            // 发起考核失败通知发起人
            $remind_msg = "【{$data['assessment_name']}】已发起考核";
            $db = dbMysql::getInstance();
            $to_user = $db->table('qwuser')->where('where id = :id', ['id' => $scheme['user_id']])->field('wid')->one();
            //  发给当前节点的人
            messagesFrom::senMeg([$to_user], 1, $remind_msg, $scheme_id);

            // 将定时任务设置为待执行，并更新下次执行时间
            // 创建 CronExpression 对象
            $cron = new CronExpression($crontab['cron_expression']);
            $currentDate = new DateTime();
            // 计算下一次执行时间
            $next_run = $cron->getNextRunDate($currentDate)->format('Y-m-d H:i:s');
            $adb->table('custom_crontab');
            $adb->where('where id = :id', ['id' => $crontab['id']]);
            $adb->update(['status' => 0, 'runtime' => $next_run]);
            returnError($error->getMessage());
        }

        // 将定时任务设置为待执行，并更新下次执行时间
        // 创建 CronExpression 对象
        $cron = new CronExpression($crontab['cron_expression']);
        $currentDate = new DateTime();
        // 计算下一次执行时间
        $next_run = $cron->getNextRunDate($currentDate)->format('Y-m-d H:i:s');
        // 更新方案的下次执行时间
        $adb->table('assessment_schemes');
        $adb->where('where id = :id', ['id' => $scheme_id]);
        $adb->update(['next_auto_time' => $next_run]);

        $adb->table('custom_crontab');
        $adb->where('where id = :id', ['id' => $crontab['id']]);
        $adb->update(['status' => -1, 'runtime' => $next_run]);

        returnSuccess([], 'success');
    }

    // 生成考核
    public static function createAssessment($data)
    {
        $scheme = $data['scheme'];
        $users = self::getAssessmentSchemesUsers($scheme);
        if (empty($users)) {
            returnError('没有需要考核人员');
        }
        $adb = dbAMysql::getInstance();
        $data = [
            'assessment_name'  => $data['assessment_name'],
            'user_id'          => $data['user_id'] ?: 0,
            'a_s_id'           => $data['a_s_id'],
            'assessment_cycle' => $data['assessment_cycle'],
            'assessment_desc'  => $data['assessment_desc'] ?? "手动发起",
            'apply_range'      => $data['apply_range'],
            'result_view'      => $data['result_view'],
            'status'           => -1, // 未开始
        ];

        // 获取方案的指标、提成、等级、财务指标
        $target_ids = [];
        $commission_ids = []; // 考核人员提成
        $dep_commission_ids = []; // 部门提成
        $user_commission_ids = []; // 员工提成
        $prize_commission_rule_id = null; // 奖励提成比例
        $prize_commission_rule_type = null; // 奖励提成比例类型

        $level_ids = [];
        $finance_column_ids = []; // 财务指标
        $dep_finance_column_ids = []; // 部门财务指标
        $user_finance_column_ids = []; // 员工财务指标

        $template = $scheme['assessment_template'];
        $process = $scheme['assessment_scheme_process'];
        foreach ($template['list'] as $item) {
            $target_ids[] = $item['id'];
        }
        if ($process[4]['performance_calculation_method'] == 1) { // 按公式核算
            foreach ($process[4]['formula'] as $formula) {
                if ($formula['type'] == 1) { // 条件
                    if ($formula['value_type'] == 1) { // 个人
                        switch ($formula['value'][0]) {
                            case 1: // 财务指标
                                $finance_column_ids[] = [
                                    'id' => $formula['value'][1],
                                    'currency' => $formula['value'][2],
                                ];
                                break;
                            case 2: // 提成比例
                                $commission_ids[] = $formula['value'][1];
                                break;
                            default:
                                break;
                        }
                    } elseif ($formula['value_type'] == 2) { // 部门
                        switch ($formula['value'][0]) {
                            case 1: // 财务指标
                                $dep_finance_column_ids[] = [
                                    'id' => $formula['value'][1],
                                    'currency' => $formula['value'][2]
                                ];
                                break;
                            case 2: // 提成比例
                                $dep_commission_ids[] = $formula['value'][1];
                                break;
                            case 4: // 员工业绩
                                $user_finance_column_ids[] = [
                                    'id' => $formula['value'][1],
                                    'currency' => $formula['value'][2]
                                ];
                                $user_commission_ids[] = $formula['value'][3];
                                break;
                            default:
                                break;
                        }
                    }
                } elseif ($formula['type'] == 2) { // 条件组
                    foreach ($formula['list'] as $sub_formula) {
                        if ($sub_formula['value_type'] == 1) { // 个人
                            switch ($sub_formula['value'][0]) {
                                case 1: // 财务指标
                                    $finance_column_ids[] = [
                                        'id' => $sub_formula['value'][1],
                                        'currency' => $sub_formula['value'][2],
                                    ];
                                    break;
                                case 2: // 提成比例
                                    $commission_ids[] = $sub_formula['value'][1];
                                    break;
                                default:
                                    break;
                            }
                        } elseif ($sub_formula['value_type'] == 2) { // 部门
                            switch ($sub_formula['value'][0]) {
                                case 1: // 财务指标
                                    $dep_finance_column_ids[] = [
                                        'id' => $sub_formula['value'][1],
                                        'currency' => $sub_formula['value'][2],
                                    ];
                                    break;
                                case 2: // 提成比例
                                    $dep_commission_ids[] = $sub_formula['value'][1];
                                    break;
                                case 4: // 员工业绩
                                    $user_finance_column_ids[] = [
                                        'id' => $sub_formula['value'][1],
                                        'currency' => $sub_formula['value'][2],
                                    ];
                                    $user_commission_ids[] = $sub_formula['value'][3];
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                }
            }

            // 奖励提成比例
            $prize_commission_rule_id = $process[4]['prize_commission_rule_id'] ?? null;
            $prize_commission_rule_type = $process[4]['prize_commission_rule_type'] ?? null;
        }
        if ($process[4]['performance_calculation_method'] == 3) { // 按等级核算
            $level_ids[] = $process[4]['level_rule_id'];
        }

        // 指标校验
        $target_ids = array_values(array_unique($target_ids));
        $commission_ids = array_values(array_unique($commission_ids));
        $level_ids = array_values(array_unique($level_ids));

        $targets = [];
        $commissions = [];
        $level_rules = [];
        if (!empty($target_ids)) {
            $adb->table('assessment_targets');
            $adb->whereIn('id', $target_ids);
            $targets = $adb->list();
        }

        $all_commission_ids = array_merge($commission_ids, $dep_commission_ids, [$prize_commission_rule_id]);
        $all_commission_ids = array_values(array_unique($all_commission_ids));
        if (!empty($all_commission_ids)) {
            $adb->table('commission_rules');
            $adb->whereIn('id', $all_commission_ids);
            $commissions = $adb->list();
        }
        if (!empty($level_ids)) {
            $adb->table('level_rules');
            $adb->whereIn('id', $level_ids);
            $level_rules = $adb->list();
        }

        // 3、 提成比例
        if (!empty($commissions)) {
            $commission_map = array_column($commissions, null, 'id');

            foreach ($commission_ids as $commission_id) {
                $commission = $commission_map[$commission_id];
                $finance_column_ids[] = [
                    'id' => $commission['column_id'],
                    'currency' => $commission['currency'],
                ];
            }
            foreach ($dep_commission_ids as $commission_id) {
                $commission = $commission_map[$commission_id];
                $dep_finance_column_ids[] = [
                    'id' => $commission['column_id'],
                    'currency' => $commission['currency'],
                ];
            }

            // 提成比例奖励
            $prize_commission_rule = $commission_map[$prize_commission_rule_id] ?? null;
            if ($prize_commission_rule) {
                $prize_rules = json_decode($prize_commission_rule['prize_rules'], true);
                foreach ($prize_rules as $item) {
                    if ($prize_commission_rule_type == 1) {
                        $finance_column_ids[] = [
                            'id' => $item['column_id'],
                            'currency' => $prize_commission_rule['currency'],
                        ];
                    } elseif ($prize_commission_rule_type == 2) {
                        $dep_finance_column_ids[] = [
                            'id' => $item['column_id'],
                            'currency' => $prize_commission_rule['currency'],
                        ];
                    }
                }
            }
        }
        // 3、 指标
        foreach ($targets as $target) {
            $target['target_detail'] = json_decode($target['target_detail'], true);
            $currency = $target['target_detail']['currency'];
            if ($target['target_type'] == 1 && $target['target_detail']['target_source'] == 1) { // 定量指标 财务
                if ($target['target_detail']['target_method'] == 1) { // 按公式
                    if ($target['target_detail']['range_type'] == 1) { // 个人
                        $finance_column_ids[$target['target_detail']['column_id']] = [
                            'id' => $target['target_detail']['column_id'],
                            'currency' => $currency,
                            'is_pre' => !($target['target_detail']['standard_value']['type'] == 1)
                        ];
                    } elseif ($target['target_detail']['range_type'] == 2) { // 部门
                        $dep_finance_column_ids[$target['target_detail']['column_id']] = [
                            'id' => $target['target_detail']['column_id'],
                            'currency' => $currency,
                            'is_pre' => !($target['target_detail']['standard_value']['type'] == 1)
                        ];
                    }
                } elseif ($target['target_detail']['target_method'] == 2) { // 按阶梯
                    foreach ($target['target_detail']['column_ids'] as $column_id) {
                        if ($target['target_detail']['range_type'] == 1) { // 个人
                            $finance_column_ids[$column_id] = [
                                'id' => $column_id,
                                'currency' => $currency,
                            ];
                        } elseif ($target['target_detail']['range_type'] == 2) { // 部门
                            $dep_finance_column_ids[$column_id] = [
                                'id' => $column_id,
                                'currency' => $currency,
                            ];
                        }
                    }
                }
            }
        }

        /* 1.0.0 废弃此类逻辑，用户实际考核部门为考核方案的部门
        // 校验不在当前考核部门的用户是否多部门
        try {
            self::checkUsersDepartment($users, $scheme['attach']['department']);
        } catch (Throwable $error) {
            returnError($error->getMessage());
        }
        */

        // 校验财务相关
        $finance_data = [];
        $currency = [];
        if (!empty($finance_column_ids) || !empty($dep_finance_column_ids)) {
            $real_finance_column_ids = [];
            $real_dep_finance_column_ids = [];
            foreach ($finance_column_ids as $item) {
                !isset($real_finance_column_ids[$item['id']]) && $real_finance_column_ids[$item['id']] = [];
                $real_finance_column_ids[$item['id']]['currency'][] = $item['currency'];
                $currency[] = $item['currency'];
                if (isset($item['is_pre']) && $item['is_pre']) { // 需要上个周期
                    $real_finance_column_ids[$item['id']]['is_pre'] = $item['is_pre'];
                }
            }
            foreach ($dep_finance_column_ids as $item) {
                !isset($real_dep_finance_column_ids[$item['id']]) && $real_dep_finance_column_ids[$item['id']] = [];
                $real_dep_finance_column_ids[$item['id']]['currency'][] = $item['currency'];
                $currency[] = $item['currency'];
                if (isset($item['is_pre']) && $item['is_pre']) { // 需要上个周期
                    $real_dep_finance_column_ids[$item['id']]['is_pre'] = $item['is_pre'];
                }
            }
            $currency = array_values(array_unique($currency));
            try {
                $finance_data = self::checkFinanceCheckOut($data['assessment_cycle'], $scheme['assessment_cycle'], $real_finance_column_ids, $real_dep_finance_column_ids, $users, $scheme['attach']['department'], $currency);
            } catch (Throwable $error) {
                returnError($error->getMessage()); 
            }
        }


        $remind_user = $users; // 消息通知人
        // 上级
        $leader = $scheme['assessment_scheme_process'][3]['leader'] ?? null;
        $remind_user[] = $leader;
        // 审批人
        if ($scheme['assessment_scheme_process'][5]['status']) {
            $approver_user = $scheme['assessment_scheme_process'][5]['approver'] == 1 ? $scheme['assessment_scheme_process'][5]['approver_user'] : $leader;
            $remind_user[] = $approver_user;
        } else {
            $approver_user = null;
        }

        // 解析出所有的考核用户
        $data['attach'] = json_encode([
            'apply_users'            => $users,
            'scheme'                 => $scheme,
            'finance_column_ids'     => $real_finance_column_ids,
            'dep_finance_column_ids' => $real_dep_finance_column_ids,
            'currency'               => $currency,
            'targets'                => $targets,
            'commissions'            => $commissions,
            'level_rules'            => $level_rules,
            'user_id'                => userModel::$qwuser_id ?? $scheme['user_id'], // 考核发起人或、考核方案发起人
            'leader'                 => $leader,
            'approver_user'          => $approver_user,
        ], JSON_UNESCAPED_UNICODE);

        $aid = $adb->table('assessment')->insert($data);

        // 添加定时任务，生成用户考核任务
        $crontab_data = [
            'is_crontab_task' => 0,
            'link_id'         => $aid,
            'runtime'         => date('Y-m-d H:i:s'),
            'link_type'       => 5,
        ];
        $adb->table('custom_crontab')->insert($crontab_data);

        return $aid;
    }

    // 获取公式规则
    public static function getFormulaText($node, $department_name = '', $commission_rules_map = [], $targets_map = [], $columns_map = [])
    {
        if (empty($columns_map)) {
            // 业绩指标
            $assessment_targets_source = config::get('assessment_targets_source','data_assessment');
            $assessment_targets_source = array_column($assessment_targets_source, 'list', 'id');
            $columns = $assessment_targets_source['1'];
            $columns_map = array_column($columns, 'column_name', 'id');
        }
        if (empty($commission_rules_map)) {
            $adb = dbAMysql::getInstance();
            // 提成规则
            $adb->table('commission_rules')
                ->field('id,rule_name')
                ->where('where is_delete = 0');
            $commission_rules = $adb->list();
            $commission_rules_map = array_column($commission_rules, 'rule_name', 'id');
        }
        if (empty($targets_map)) {
            $adb = dbAMysql::getInstance();
            // 指标
            $adb->table('assessment_targets')
                ->field('id,target_name')
                ->where('where is_delete = 0');
            $targets = $adb->list();
            $targets_map = array_column($targets, 'target_name', 'id');
        }
        // 公式符号(+-*/)
        $link_symbol = config::get('link_symbol', 'data_assessment');
        $link_symbol_map = array_column($link_symbol, 'name', 'id');
        $formula_symbol = config::get('formula_symbol', 'data_assessment');
        $formula_symbol_map = array_column($formula_symbol, 'name', 'id');
        $assessment_calc_symbol = config::get('assessment_calc_symbol', 'data_assessment');
        $assessment_calc_symbol_map = array_column($assessment_calc_symbol, 'name', 'id');
        $assessment_calc_value_type = config::get('assessment_calc_value_type', 'data_assessment');
        $assessment_calc_value_type_map = array_column($assessment_calc_value_type, 'name', 'id');
        $assessment_calc_value_type_1 = config::get('assessment_calc_value_type_1', 'data_assessment');
        $assessment_calc_value_type_1_map = array_column($assessment_calc_value_type_1, 'name', 'id');
        $assessment_calc_value_type_2 = config::get('assessment_calc_value_type_2', 'data_assessment');
        $assessment_calc_value_type_2_map = array_column($assessment_calc_value_type_2, 'name', 'id');

        $text = '';
        // 按比率核算
        if ($node['performance_calculation_method'] == 1) {
            $idx = 0;
            foreach ($node['formula'] as $item) {
                if ($idx) $text .= ' ' . $formula_symbol_map[$item['symbol']] . ' ';
                // 条件
                $item_text = '';
                if ($item['type'] == 1) {
                    if ($item['value_type'] == 3) { // 自定义值
                        $item_text = $item['value'];
                    } else {
                        $value_map_1 = $item['value_type'] == 1 ? $assessment_calc_value_type_1_map : $assessment_calc_value_type_2_map;
                        if ($item['value'][0] == 3) {
                            // 员工绩效/ 部门员工绩效
                            if ($item['value_type'] == 2) {
                                foreach ($item['users'] as &$user) {
                                    $user['text'] = $user['wname'] . '的绩效';
                                }
                                unset($user);
                                $user_str = implode('+', array_column($item['users'], 'text'));
                                $item_text = "({$department_name}{$user_str})";
                            } else {
                                $item_text = "{$value_map_1[$item['value']['0']]}";
                            }
                        } else {
                            $value_map_2 = $item['value'][0] == 1 ? $columns_map : $commission_rules_map;
                            $item_text = "{$value_map_1[$item['value']['0']]}:{$value_map_2[$item['value']['1']]}";
                        }

                    }
                } // 条件组
                elseif ($item['type'] == 2) {
                    $item_idx = 0;
                    $item_text .= '(';
                    foreach ($item['list'] as $subItem) {
                        $item_item_text = '';
                        $item_idx && $item_text .= ' ' . $formula_symbol_map[$subItem['symbol']] . ' ';
                        if ($subItem['value_type'] == 3) { // 自定义值
                            $item_item_text = $subItem['value'];
                        } else {
                            $value_map_1 = $subItem['value_type'] == 1 ? $assessment_calc_value_type_1_map : $assessment_calc_value_type_2_map;
                            if ($subItem['value'][0] == 3) {
                                // 员工绩效/ 部门员工绩效
                                if ($subItem['value_type'] == 2) {
                                    foreach ($subItem['users'] as &$user) {
                                        $user['text'] = $user['wname'] . '的绩效';
                                    }
                                    unset($user);
                                    $user_str = implode('+', array_column($subItem['users'], 'text'));
                                    $item_item_text = "({$department_name}{$user_str})";
                                } else {
                                    $item_item_text = "{$value_map_1[$subItem['value']['0']]}";
                                }
                            } else {
                                $value_map_2 = $subItem['value'][0] == 1 ? $columns_map : $commission_rules_map;
                                $item_item_text = "{$assessment_calc_value_type_map[$subItem['value_type']]}{$value_map_1[$subItem['value']['0']]}:{$value_map_2[$subItem['value']['1']]}";
                            }

                        }
                        $item_idx++;
                        if ($subItem['is_abs'] == 1) {
                            $item_item_text = '|' . $item_item_text . '|';
                        } elseif  ($subItem['is_abs'] == 2) {
                            $item_item_text = 'floor('.$item_item_text.')';
                        }
                        $item_text .= $item_item_text;
                    }
                    $item_text .= ")";
                }
                $idx++;
                if ($item['is_abs'] == 1){
                    $item_text = '|' . $item_text . '|';
                } elseif ($item['is_abs'] == 2) {
                    $item_text = 'floor('.$item_text.')';
                }
                $text .= $item_text;
            }

        } // 按阶梯奖励
        elseif ($node['performance_calculation_method'] == 2) {
            foreach ($node['formula'] as $item) {
                $idx = 0;
                foreach ($item['list'] as $subItem) {
                    $idx && $text .= $link_symbol_map[$subItem['symbol']];
                    $text .= "{$targets_map[$subItem['target_id']]}{$assessment_calc_symbol_map[$subItem['assessment_calc_symbol']]}{$subItem['value']}";
                    $idx++;
                }
                $text .= ",则绩效/奖金={$item['result']}";
            }
        }
        return $text;
    }

    // 校验不在当前考核部门的用户是否多部门
    public static function checkUsersDepartment($user_ids, $department_id)
    {
        $db = dbMysql::getInstance();
        $db->table('qwuser')
            ->field('id,wname,wdepartment_ids')
            ->whereIn('id', $user_ids);
        $users = $db->list();
        $user_map = array_column($users, null, 'id');
        foreach ($user_map as $user) {
            $departments = json_decode($user['wdepartment_ids'], true);
            if (count($departments) > 1 && !in_array($department_id, $departments)) {
                throw new Exception('用户' . $user['wname'] . '存在多部门且不在当前考核部门， 不能发起考核');
            }
        }
    }

    // 检验考核周期内财务结账情况
    public static function checkFinanceCheckOut($assessment_cycle, $scheme_type, $finance_column_ids, $dep_finance_column_ids, $users, $assessment_department_id, $currency): array
    {
        $assessment_cycle = json_decode($assessment_cycle, true);
        $start_time = date('Y-m', strtotime($assessment_cycle[0]));
        $end_time = date('Y-m', timestamp: strtotime($assessment_cycle[1]));
        // 上个周期
        $pre_assessment_cycle = assessmentSchemesModel::getPreSchemeCycle($assessment_cycle, $scheme_type);
        // 上上个周期
        $pre_pre_assessment_cycle = assessmentSchemesModel::getPreSchemeCycle($pre_assessment_cycle, $scheme_type);
        $pre_pre_start = date('Y-m', strtotime($pre_pre_assessment_cycle[0]));
        $fdb = dbFMysql::getInstance();
        // 财务自定义指标
        $assessment_targets_source = config::get('assessment_targets_source_custom','data_assessment');
        $real_custom_ids = array_column($assessment_targets_source, 'custom_id');
        $columns = $fdb->table('column')
            ->where('where is_delete = 0')
            ->whereIn('id', $real_custom_ids)
            ->list();
        if (count($columns) != count($real_custom_ids)) {
            throw new Exception('存在被删除的财务指标，请重新配置');
        }

        $check_outs = $fdb->table('checkout')
            ->field('m_date,is_lock')
            ->where('where m_date >= :start_time and m_date <= :end_time', ['start_time' => $pre_pre_start, 'end_time' => $end_time])
            ->list();
        $check_outs = array_column($check_outs, null, 'm_date');

        if (empty($check_outs)) {
            throw new Exception('考核周期内存在财务未结账月份');
        }

        // 查询周期内的币种情况
        $currency_map = [];
        $currency[] = 'CNY';
        $currency_list = $fdb->table('routing')
            ->where('where date >= :date and date <= :end_time', ['date' => $pre_pre_start, 'end_time' => $end_time])
            ->whereIn('code', $currency)
            ->field('date, code, name, rate_org, my_rate')
            ->list();
        foreach ($currency_list as $item) {
            $currency_map[$item['code']][$item['date']] = $item['my_rate'];
        }

        $current = strtotime($pre_pre_start);
        $end = strtotime($end_time);

        // 校验上个考核周期+本次考核周期内的财务结账情况
        // 循环按月增加
        while ($current <= $end) {
            // 没有结账数据或者未结账/结账中
            $current_month = date('Y-m', $current);
            if (!array_key_exists($current_month, $check_outs)) {
                throw new Exception('考核周期内存在财务未结账月份' . $current_month);
            }
            if ($check_outs[$current_month]['is_lock'] != 1) {
                throw new Exception('考核周期内存在财务未结账月份' . $current_month);
            }
            foreach ($currency_map as $currency => $currency_value) {
                if (!array_key_exists($current_month, $currency_value)) {
                    throw new Exception('考核周期内'.$current_month.'不存在币种：' . $currency);
                }
            }
            $current = strtotime('+1 month', $current);
        }

        // 校验当前考核用户是否有财务数据
        $db = dbMysql::getInstance();
        $user_map = $db->table('qwuser')->whereIn('id', $users)->field('id, wid, wname, avatar,wdepartment_ids')->list();
        $user_map = array_column($user_map, null, 'id');
        $pre_assessment_cycle = assessmentSchemesModel::getPreSchemeCycle($assessment_cycle, $scheme_type);
        $user_finance_data = [];
        foreach ($users as $user) {
            /* 考勤 1.0.1 均使用考核方案的部门
            $user_department_ids = json_decode($user_map[$user]['wdepartment_ids'], true);
            // 单部门用户
            if (count($user_department_ids) == 1) {
                // 这里存在一种情况，用户仅在部门B，但是参与了部门A的考核，在实际计算时，使用用户在部门B的指标数据
                $assessment_department_id = $user_department_ids[0];
            }
            */
            $projects_res = assessmentSchemesModel::getProjectByDepartmentId($assessment_department_id);
            $projects = $projects_res['projects'];
            $department_name = $projects_res['department_name'];
            if (!empty($projects)) {
                try {
                    $finance_column_ids && $user_result = assessmentSchemesModel::getFinanceColumnValue($finance_column_ids, $user, $projects, $department_name, $currency_map, $assessment_cycle, $pre_assessment_cycle, $pre_pre_assessment_cycle);
                    $dep_finance_column_ids && $dep_result = assessmentSchemesModel::getFinanceColumnValue($dep_finance_column_ids, 0, $projects, $department_name, $currency_map, $assessment_cycle, $pre_assessment_cycle, $pre_pre_assessment_cycle);
                } catch (Exception $e) {
                    returnError('当前周期内，'.$user_map[$user]['wname'].$e->getMessage());
                }
            }
            $user_finance_data[$user] = [
                'column_result'         => $user_result['column_result'] ?? [],
                'pre_column_result'     => $user_result['pre_column_result'] ?? [],
                'dep_column_result'     => $dep_result['column_result'] ?? [],
                'dep_pre_column_result' => $dep_result['pre_column_result'] ?? [],
            ];
        }
        return $user_finance_data;
    }

    // 获取考核指标实际值
    public static function getFinanceColumnValue($column_ids, $user_id, $projects, $department_name, $currency_map, $assessment_cycle, $pre_assessment_cycle, $pre_pre_assessment_cycle)
    {
        $pre_column = []; // 当前字段是否需要上个周期
        $pre_pre_column = []; // 当前字段是否需要上上个周期

        foreach ($column_ids as $column_id => $column) {
            if (!empty($column['currency']) ) {
                $currency[] = $column['currency'];
            }
            if ((isset($column['is_pre']) && $column['is_pre']) || $column_id == '4') {
                $pre_column[$column_id] = $column_id;
            }
        }

        if (array_key_exists('4', $pre_column)) { // 销售额环比 需要拉取上个月订单售价
            $pre_pre_column[] = '3';
        }

        // 销售额，使用订单售价1
        $columns_map = self::getColumnValue($assessment_cycle, $user_id, $projects, $currency_map) ?: [];

        if (!empty($pre_column)) { // 计算上个周期的业绩指标
            $pre_column_map = self::getColumnValue($pre_assessment_cycle, $user_id, $projects, $currency_map) ?: [];
        }
        if (!empty($pre_pre_column)) { // 计算上上个周期的业绩指标
            $pre_pre_column_map = self::getColumnValue($pre_pre_assessment_cycle, $user_id, $projects, $currency_map) ?: [];
        }


        /*********************************上上个周期，只会使用订单售价********************************* */
        $pre_pre_custom_result = [];
        if (!empty($pre_pre_column)) {
            // 上上个考核周期业绩指标值
            foreach ($currency_map as $currency => $currency_value) {
                $pre_pre_custom_result['3'][$currency] = self::getValueByCycleAndCurrency($pre_pre_column_map, '3', $pre_pre_assessment_cycle, $currency);
            }
        }

        /********************************上个周期**************************************************** */
        // 上个考核周期业绩指标值
        $pre_custom_result = [];
        $pre_column_result = [];
        if (!empty($pre_column)) {
            foreach ($currency_map as $currency => $currency_value) {
                $pre_custom_result['3'][$currency] = self::getValueByCycleAndCurrency($pre_column_map, '3', $pre_assessment_cycle, $currency);
                $pre_custom_result['4'][$currency] = self::getValueByCycleAndCurrency($pre_column_map, '4', $pre_assessment_cycle, $currency);
                $pre_custom_result['59'][$currency] = self::getValueByCycleAndCurrency($pre_column_map, '59', $pre_assessment_cycle, $currency);
                $pre_custom_result['76'][$currency] = self::getValueByCycleAndCurrency($pre_column_map, '76', $pre_assessment_cycle, $currency);
                $pre_custom_result['key6'][$currency] = self::getValueByCycleAndCurrency($pre_column_map, 'key6', $pre_assessment_cycle, $currency);
                $pre_custom_result['key19'][$currency] = self::getValueByCycleAndCurrency($pre_column_map, 'key19', $pre_assessment_cycle, $currency);

                // 毛利润 = 毛利润 + 预留资金 + VAT(76) + 海外推广 + 福利政策(59)
                if ($pre_custom_result['4'][$currency] == 'error' &&
                    $pre_custom_result['59'][$currency] == 'error' &&
                    $pre_custom_result['76'][$currency] == 'error' &&
                    $pre_custom_result['key6'][$currency] == 'error' &&
                    $pre_custom_result['key19'][$currency] == 'error') {
                    $pre_custom_result['4'][$currency] = 'error';
                } else {
                    $pre_custom_result['4'][$currency] = $pre_custom_result['4'][$currency] != 'error' ? $pre_custom_result['4'][$currency] : 0;
                    $pre_custom_result['4'][$currency] += ($pre_custom_result['59'][$currency] != 'error' ? $pre_custom_result['59'][$currency] : 0);
                    $pre_custom_result['4'][$currency] += ($pre_custom_result['76'][$currency] != 'error' ? $pre_custom_result['76'][$currency] : 0);
                    $pre_custom_result['4'][$currency] += ($pre_custom_result['key6'][$currency] != 'error' ? $pre_custom_result['key6'][$currency] : 0);
                    $pre_custom_result['4'][$currency] += ($pre_custom_result['key19'][$currency] != 'error' ? $pre_custom_result['key19'][$currency] : 0);
                }
            }

            $pre_column_result = [
                '1' => $pre_custom_result['3'], // 销售额
                '2' => $pre_custom_result['4'], // 毛利润
                '3' => [], // 毛利率
                '4' => [], // 销售额环比
                '5' => [], // 毛利率达成率
            ];
            self::calcColumnValue($pre_column_result, $column_ids, $currency_map, $pre_custom_result,$department_name, $pre_pre_custom_result);

        }

        /********************************当前周期**************************************************** */

        // 当前周期业绩指标值
        $custom_result = [];
        foreach ($currency_map as $currency => $currency_value) {
            $custom_result['3'][$currency] = self::getValueByCycleAndCurrency($columns_map, '3', $assessment_cycle, $currency);
            $custom_result['4'][$currency] = self::getValueByCycleAndCurrency($columns_map, '4', $assessment_cycle, $currency);
            $custom_result['59'][$currency] = self::getValueByCycleAndCurrency($columns_map, '59', $assessment_cycle, $currency);
            $custom_result['76'][$currency] = self::getValueByCycleAndCurrency($columns_map, '76', $assessment_cycle, $currency);
            $custom_result['key6'][$currency] = self::getValueByCycleAndCurrency($columns_map, 'key6', $assessment_cycle, $currency);
            $custom_result['key19'][$currency] = self::getValueByCycleAndCurrency($columns_map, 'key19', $assessment_cycle, $currency);

            // 毛利润 = 毛利润 + 预留资金 + VAT + 海外推广 + 福利政策
            if ($custom_result['4'][$currency] == 'error' &&
                $custom_result['59'][$currency] == 'error' &&
                $custom_result['76'][$currency] == 'error' &&
                $custom_result['key6'][$currency] == 'error' &&
                $custom_result['key19'][$currency] == 'error') {
                $custom_result['4'][$currency] = 'error';
            } else {
                $custom_result['4'][$currency] = $custom_result['4'][$currency] != 'error' ? $custom_result['4'][$currency] : 0;
                $custom_result['4'][$currency] += ($custom_result['59'][$currency] != 'error' ? $custom_result['59'][$currency] : 0);
                $custom_result['4'][$currency] += ($custom_result['76'][$currency] != 'error' ? $custom_result['76'][$currency] : 0);
                $custom_result['4'][$currency] += ($custom_result['key6'][$currency] != 'error' ? $custom_result['key6'][$currency] : 0);
                $custom_result['4'][$currency] += ($custom_result['key19'][$currency] != 'error' ? $custom_result['key19'][$currency] : 0);
            }
        }

        $column_result = [
            '1' => $custom_result['3'] ?? [], // 销售额
            '2' => $custom_result['4'] ?? [], // 毛利润
            '3' => [], // 毛利率
            '4' => [], // 销售额环比
            '5' => [], // 毛利率达成率
        ];

        self::calcColumnValue($column_result, $column_ids, $currency_map, $custom_result,$department_name, $pre_custom_result);
        return ['column_result' => $column_result, 'pre_column_result' => $pre_column_result];
    }

    // 获取周期内的指标数据
    public static function getColumnValue($assessment_cycle, $user_id, $projects, $currency_map) {
        // 自定义指标custom_id 
        $custom_id = config::get('assessment_targets_source_custom','data_assessment');
        $custom_id = array_column($custom_id, 'custom_id');

        $columns_map = array_fill_keys($custom_id, []);
        $columns_map['key6'] = [];
        $columns_map['key19'] = [];

        $year = date('Y', timestamp: strtotime($assessment_cycle[0]));
        $start_time = date('Y-m', strtotime($assessment_cycle[0]));
        $end_time = date('Y-m', strtotime($assessment_cycle[1]));
        $fdb = dbFMysql::getInstance();
        // key6 预留资金 （领星）
        // 取出领星字段
        $table1 = 'table_month_count_' . ($year);
        $fdb->table($table1)
            ->field('sum(key6) as sum, sum(key19) as sum19, reportDateMonth')
            ->where('where reportDateMonth >= :start_time and reportDateMonth <= :end_time', ['start_time' => $start_time, 'end_time' => $end_time])
            ->whereIn('project_id', $projects);
        if ($user_id) {
            $fdb->andWhere('yunying_id = :user_id', ['user_id' => $user_id]);
        }
        $key6_map = $fdb->groupBy(['reportDateMonth'])->list();
        foreach ($key6_map as $item) {
            // 初始数据
            $columns_map['key6'][$item['reportDateMonth']]['CNY'] = [
                'currency' => 'CNY',
                'sum'      => $item['sum'],
                'my_rate'  => $currency_map['CNY'][$item['reportDateMonth']],
            ];
            $columns_map['key19'][$item['reportDateMonth']]['CNY'] = [
                'currency' => 'CNY',
                'sum'      => $item['sum19'],
                'my_rate'  => $currency_map['CNY'][$item['reportDateMonth']],
            ];
            foreach ($currency_map as $currency => $currency_value) { // 预留资金的币种，直接使用毛利率的币种
                if($currency == 'CNY') continue;
                $columns_map['key6'][$item['reportDateMonth']][$currency] = [
                    'currency' => $currency,
                    'sum'      => $item['sum'],
                    'my_rate'  => $currency_map[$currency][$item['reportDateMonth']],
                ];
                $columns_map['key19'][$item['reportDateMonth']][$currency] = [
                    'currency' => $currency,
                    'sum'      => $item['sum19'],
                    'my_rate'  => $currency_map[$currency][$item['reportDateMonth']],
                ];
            }
        }
        
        // 自定义字段
        $table_name = 'custom_val_' . $year;
        $fdb->table($table_name)
            ->field('sum(custom_val) as sum, custom_id, m_date')
            ->where('where m_date >= :start_time and m_date <= :end_time', ['start_time' => $start_time, 'end_time' => $end_time])
            ->whereIn('custom_id', $custom_id)
            ->whereIn('project_id', $projects);
        if ($user_id) {
            $fdb->andWhere('yunying_id = :user_id', ['user_id' => $user_id]);
        }
        $columns = $fdb->groupBy(['custom_id, m_date']);
        $columns = $columns->list();
        foreach ($columns as $value) {
            $columns_map[$value['custom_id']][$value['m_date']]['CNY'] = [
                'currency' => 'CNY',
                'sum'      => $value['sum'],
                'my_rate'  => $currency_map['CNY'][$value['m_date']],
            ];
            foreach ($currency_map as $currency => $currency_value) {
                $columns_map[$value['custom_id']][$value['m_date']][$currency] = [
                    'currency' => $currency,
                    'sum'      => $value['sum'],
                    'my_rate'  => $currency_map[$currency][$value['m_date']],
                ];
            }
        }
        return $columns_map;

    }

    // 计算比率数据
    public static function calcColumnValue(&$column_result, $column_ids, $currency_map, $custom_result,$department_name, $pre_custom_result) {
        foreach ($currency_map as $currency => $currency_value) {
            try {
                // 毛利率
                if (array_key_exists('3', $column_ids) || array_key_exists('5', $column_ids)) {
                    $divisor = floatval($custom_result['3'][$currency]);
                    if ($divisor == 'error' || $custom_result['4'][$currency] == 'error' ) { // 销售额或毛利润为error
                        $column_result['3'][$currency] = 'error';
                        continue;
                    }
                    $value_4 = $custom_result['4'][$currency]; // 毛利润
                    $value_59 = $custom_result['59'][$currency] == 'error' ? 0 : $custom_result['59'][$currency]; // 福利政策
                    $value_key6 = $custom_result['key6'][$currency] == 'error' ? 0 : $custom_result['key6'][$currency]; // 预留资金

                    // 德英毛利率 = （毛利润（OA）1 - 预留资金（领星）- 福利政策）/ 订单售价1
                    if (strpos($department_name, '英国') !== false || strpos($department_name, '德国') !== false) {
                        $column_result['3'][$currency] = round(($value_4 - $value_59 - $value_key6) / $divisor, 4);
                    } else {
                        // 毛利率 = （毛利润（OA）1 - 预留资金（领星））/ 订单售价1
                        $column_result['3'][$currency] = round(($value_4 - $value_key6) / $divisor, 4);
                    }
                }
                // 销售额环比 = 当月实际销售额 /上月销售额 * 100%
                if (array_key_exists('4', $column_ids)) {
                    if ($pre_custom_result['3'][$currency] == 'error' || $custom_result['3'][$currency] == 'error') {
                        $column_result['4'][$currency] = 'error';
                        continue;
                    }
                    $column_result['4'][$currency] = round($custom_result['3'][$currency] / $pre_custom_result['3'][$currency], 4);
                }

                // 毛利率达成率 = 毛利率 / 0.25
                if (array_key_exists('5', $column_ids)) {
                    if ($column_result['3'][$currency] == 'error') {
                        $column_result['5'][$currency] = 'error';
                        continue;
                    }
                    $column_result['5'][$currency] = round($column_result['3'][$currency] / 0.25, 4);
                }
            } catch (\Throwable $e) {
                if ($e instanceof \DivisionByZeroError) {
                    throw new \Exception(' 销售额（除数）为0');
                }
                throw new \Exception($e->getMessage());
            }
            
        }
        return true;
    }

    // 按周期、币种计算业绩指标值
    public static function getValueByCycleAndCurrency($columns_map, $custom_id, $cycle, $currency)
    {
        $sum = 'error';
        foreach ($columns_map as $custom_key => $month_column) {
            if ($custom_key != $custom_id) continue;
            foreach ($month_column as $month => $currency_value) {
                if (!isset($currency_value[$currency]) || empty($currency_value[$currency])) continue;
                $currency_item = $currency_value[$currency];
                if (strtotime($month) >= strtotime($cycle[0]) && strtotime($month) <= strtotime($cycle[1])) {
                    $sum = $sum == 'error' ? 0 : $sum;
                    $sum += $currency_item['sum'] / $currency_item['my_rate'];
                }
            }
        }
        return $sum == 'error' ? 'error' : round($sum, 4);
    }

    // 获取企微部门对应的财务项目
    public static function getProjectByDepartmentId($department_id)
    {
        $project = [];
        $fdb = dbFMysql::getInstance();
        $db = dbMysql::getInstance();
        $projects = $fdb->table('project')
            ->where('where is_delete = 0 and level = 3 ')
            ->field('id,p_id,project_name,level')
            ->list();
        $projects_map = array_column($projects, null, 'id');

        $department = $db->table('qwdepartment')
            ->where('where wp_id = :id', ['id' => $department_id])
            ->one();
        $department_name = $department['name'];
        $country_map = ['US' => '美国', 'UK' => '英国', 'DE' => '德国', 'JP' => '日本'];
        // 替换部门中的国家
        $department_name = str_replace(array_keys($country_map), array_values($country_map), $department_name);
        // 找到部门的项目
        foreach ($projects_map as $item) {
            if (strpos($item['project_name'], $department_name) !== false) { // 财务项目名称包含部门名称
                $project[] = $item['id'];
            }
        }
        return ['projects' => $project, 'department_name' => $department_name];
    }

    // 计算指标考核结果
    public static function calcCeo(&$targets, $score_type, $target_weight)
    {
        $target_coe = 0;
        foreach ($targets as &$result_target) {
            if ($score_type == 1) { // 加权
                $result_target['weight_result'] = floatval(floatval($result_target['result']) * $target_weight[$result_target['id']] / 100);
                $target_coe += floatval(floatval($result_target['result']) * $target_weight[$result_target['id']] / 100);
            } elseif ($score_type == 2) { // 加和
                $result_target['weight_result'] = floatval($result_target['result']);
                $target_coe += floatval($result_target['result']);
            }
            $result_target['weight_result'] = round($result_target['weight_result'], 4);
        }
        return round($target_coe, 4);
    }

    // 计算绩效
    public static function calcPerformance($calculation_method, $user_id, $scheme, $assessment_cycle,
                                           $finance_data, $targets, $score, $level_rules, $coefficient_rules,
                                           $default_coefficient, $formula, $commissions, $prize_commission_rule,
                                           $prize_commission_type)
    {
        $scheme_id = $scheme['id'];
        $formula_symbol = config::get('formula_symbol', 'data_assessment');
        $formula_symbol_map = array_column($formula_symbol, 'name', 'id');
        $performance = 'error';
        $level = '';
        switch ($calculation_method) {
            case 1: // 按比率核算
                $expression = '';
                $idx = 0;
                foreach ($formula as $item) {
                    $idx && $expression .= $formula_symbol_map[$item['symbol']];
                    if ($item['type'] == 1) {
                        $item['users'] = $item['users'] ?? [];
                        $item['user_scores'] = $item['user_scores'] ?? [];
                        $expression .= self::getFormulaItemValue($item, $finance_data, $score, $item['users'], $item['user_scores'], $commissions, $scheme, $assessment_cycle);
                    } elseif ($item['type'] == 2) {
                        $item_expression = '';
                        $item_idx = 0;
                        foreach ($item['list'] as $subItem) {
                            $item_idx && $item_expression .= $formula_symbol_map[$subItem['symbol']];
                            $subItem['users'] = $subItem['users'] ?? [];
                            $subItem['user_scores'] = $subItem['user_scores'] ?? [];
                            $item_expression .= self::getFormulaItemValue($subItem, $finance_data, $score, $subItem['users'], $subItem['user_scores'], $commissions, $scheme, $assessment_cycle);
                            $item_idx++;
                        }
                        if ($item['is_abs'] == 1) {
                            $item_expression = "abs($item_expression)";
                        } elseif ($item['is_abs'] == 2) {
                            $item_expression = "floor($item_expression)";
                        }
                        $expression .= "($item_expression)";
                    }
                    $idx++;
                }
                eval("\$performance = $expression;");

                // 增加提成比例奖励
                if (!empty($prize_commission_rule)) {
                    $column_result = $prize_commission_type == 1 ? $finance_data['column_result'] : $finance_data['dep_column_result'];
                    $prize = commissionRulesModel::getPrize($prize_commission_rule, $column_result);
                    $performance += $prize;
                }
                break;
            case 2: // 按阶梯奖励
                $link_symbol = config::get('link_symbol', 'data_assessment');
                $link_symbol_map = array_column($link_symbol, 'value', 'id');
                $assessment_calc_symbol = config::get('assessment_calc_symbol', 'data_assessment');
                $assessment_calc_symbol_map = array_column($assessment_calc_symbol, 'value', 'id');
                $targets = array_column($targets, null, 'id');
                foreach ($formula as $item) {
                    $expression = '';
                    $idx = 0;
                    foreach ($item['list'] as $subItem) {
                        $idx && $expression .= $link_symbol_map[$subItem['symbol']];
                        $sub_flag = $targets[$subItem['target_id']]['result'] . $assessment_calc_symbol_map[$subItem['assessment_calc_symbol']] . $subItem['value'];
                        $expression .= eval("return $sub_flag;") ? "true" : "false";
                        $idx++;
                    }
                    $flag = eval("return $expression;");
                    if (!$flag) continue;
                    // 循环找出最大的值
                    if ($performance != 'error') {
                        $performance = max($performance, $item['result']);
                    } else {
                        $performance = $item['result'];
                    }

                }
                break;
            case 3: // 按等级核算
                $level_result = levelRulesModel::getResult($user_id, $scheme_id, $assessment_cycle, $score, $level_rules, $coefficient_rules, $default_coefficient);
                $performance = $level_result['performance'];
                $level = $level_result['level'];
                break;
            case 4: // 手动录入
                $performance = 'manual';
                break;
            default:
                $performance = 'error';
                break;
        }
        if ($performance != 'error') {
            $performance = round($performance, 2);
        }
        return ['performance' => $performance, 'level' => $level];
    }

    // 获取公式项的值
    public static function getFormulaItemValue($item, $finance_data, $score, $users, $user_scores, $commission_map, $scheme, $assessment_cycle)
    {
        $value = 0;
        if ($item['value_type'] == 1) { // 个人
            switch ($item['value'][0]) {
                case 1: // 财务业绩指标
                    $value = $finance_data['column_result'][$item['value'][1]][$item['value'][2]];
                    break;
                case 2: // 提成比例
                    $commission = $commission_map[$item['value'][1]];
                    $commission_column_id = $commission['column_id'];
                    $currency = $commission['currency'];
                    $value = commissionRulesModel::getResult($commission['rules'], $finance_data['column_result'][$commission_column_id][$currency]);
                    break;
                case 3: // 绩效
                    $value = $score;
                    break;
            }
        }
        elseif ($item['value_type'] == 2) { // 部门
            switch ($item['value'][0]) {
                case 1: // 财务指标
                    $value = $finance_data['dep_column_result'][$item['value'][1]][$item['value'][2]];
                    break;
                case 2: // 提成比例
                    $commission = $commission_map[$item['value'][1]];
                    $commission_column_id = $commission['column_id'];
                    $currency = $commission['currency'];
                    $value = commissionRulesModel::getResult($commission['rules'], $finance_data['dep_column_result'][$commission_column_id][$currency]);
                    break;
                case 3: // 员工绩效
                    foreach ($users as $user) {
                        $value += $user_scores[$user['id']];
                    }
                    break;
                case 4: // 业绩
                    $assessment_department_id = $scheme['attach']['department'];
                    $projects_res = assessmentSchemesModel::getProjectByDepartmentId($assessment_department_id);

                    foreach ($users as $user) {
                        $value += self::getUserResult($user['id'], $item['value'][1], $item['value'][2], $commission_map[$item['value'][3]], $projects_res, $scheme, $assessment_cycle);
                    }
                    break;
            }
        }
        elseif ($item['value_type'] == 3) { // 自定义值
            $value = $item['value'];
        }
        if ($item['is_abs'] == 1) {
            $value = abs($value);
        } elseif ($item['is_abs'] == 2) {
            $value = floor($value);
        }
        return $value;
    }

    // 获取员工业绩值
    public static function getUserResult($user, $column_id, $currency, $commission, $projects_res, $scheme, $assessment_cycle)
    {
        $scheme_type = $scheme['assessment_cycle'];
        $end_time = date('Y-m', timestamp: strtotime($assessment_cycle[1]));
        // 上个周期
        $pre_assessment_cycle = assessmentSchemesModel::getPreSchemeCycle($assessment_cycle, $scheme_type);
        // 上上个周期
        $pre_pre_assessment_cycle = assessmentSchemesModel::getPreSchemeCycle($pre_assessment_cycle, $scheme_type);
        $pre_pre_start = date('Y-m', strtotime($pre_pre_assessment_cycle[0]));
        $currency_map = [];
        $currency_all = ['CNY', $currency, $commission['currency']];
        // 财务指标
        $finance_column_id[$column_id] = [
            'id' => $column_id,
            'currency' => $currency,
        ];
        // 员工提成比例
        if ($commission['column_id'] != $column_id) {
            $finance_column_id[$commission['column_id']] = [
                'id' => $commission['column_id'],
                'currency' => $commission['currency'],
            ];
        } else {
            if ($commission['currency'] != $currency) {
                $finance_column_id[$column_id]['currency'][] = $commission['currency'];
            }
        }
        // 获取员工的业绩值
        $projects = $projects_res['projects'];
        $department_name = $projects_res['department_name'];

        $fdb = dbFMysql::getInstance();
        // 查询周期内的币种情况
        $currency_list = $fdb->table('routing')
            ->where('where date >= :date and date <= :end_time', ['date' => $pre_pre_start, 'end_time' => $end_time])
            ->whereIn('code', $currency_all)
            ->field('date, code, name, rate_org, my_rate')
            ->list();
        foreach ($currency_list as $currency_item) {
            $currency_map[$currency_item['code']][$currency_item['date']] = $currency_item['my_rate'];
        }

        $finance_data = assessmentSchemesModel::getFinanceColumnValue($finance_column_id, $user, $projects, $department_name, $currency_map, $assessment_cycle, $pre_assessment_cycle, $pre_pre_assessment_cycle);

        // 员工的提成比例
        $commission_result = commissionRulesModel::getResult($commission['rules'], $finance_data['column_result'][$commission['column_id']][$commission['currency']]);
        return $commission_result * $finance_data['column_result'][$column_id][$currency];
    }


    // 考核方案里的数据
    public static function compareSchemeData($scheme, $db_scheme)
    {
        $db_scheme['scheme_name'] != $scheme['scheme_name'] && throw new Exception('考核方案名称不一致');
        $db_scheme['assessment_cycle'] != $scheme['assessment_cycle'] && throw new Exception('考核周期不一致');
        $db_scheme['assessment_type'] != $scheme['assessment_type'] && throw new Exception('考核类型不一致');

        $db_scheme['attach']['department'] != $scheme['attach']['department'] && throw new Exception('考核部门不一致');
        $db_scheme['attach']['score_type'] != $scheme['attach']['score_type'] && throw new Exception('评分方式不一致');
        isset($scheme['attach']['score_system']) && $db_scheme['attach']['score_system'] != $scheme['attach']['score_system'] && throw new Exception('分制不一致');

        $db_scheme['assessment_template']['assessment_template'] != $scheme['assessment_template']['assessment_template'] && throw new Exception('考核模板不一致');
        count($db_scheme['assessment_template']['list']) != count($scheme['assessment_template']['list']) && throw new Exception('考核模板指标数量不一致');
        foreach ($db_scheme['assessment_template']['list'] as $key => $value) {
            if ($value['id'] != $scheme['assessment_template']['list'][$key]['id']) {
                throw new Exception('考核模板指标不一致');
            }
            if ($value['value'] != $scheme['assessment_template']['list'][$key]['value']) {
                throw new Exception('考核模板指标分数不一致');
            }
        }

        foreach ($db_scheme['assessment_scheme_process'] as $key => $value) {
            switch ($value['id']) {
                case 2: // 工作简述
                    $value['status'] != $scheme['assessment_scheme_process'][1]['status'] && throw new Exception('工作简述不一致');
                    break;
                case 3: // 自评
                    $value['status'] != $scheme['assessment_scheme_process'][2]['status'] && throw new Exception('自评不一致');
                    break;
                case 4: // 上级评分
                    $value['leader'] != $scheme['assessment_scheme_process'][3]['leader'] && throw new Exception('上级评分不一致');
                    break;
                case 5: // 绩效核算
                    $value['performance_calculation_method'] != $scheme['assessment_scheme_process'][4]['performance_calculation_method'] && throw new Exception('绩效核算方式不一致');
                    $formula = serialize($scheme['assessment_scheme_process'][4]['formula']);
                    serialize($value['formula']) != $formula && throw new Exception('绩效核算公式不一致');
                    break;
                case 6: // 审批
                    $value['status'] != $scheme['assessment_scheme_process'][5]['status'] && throw new Exception('审批不一致');
                    isset($value['approver']) && $value['approver'] != $scheme['assessment_scheme_process'][5]['approver'] && throw new Exception('审批人不一致');
            }
        }
        return true;
    }

    // 对比指标数据
    public static function compareTarget($target, $db_target)
    {
        $db_target['status'] != $target['status'] && throw new Exception('指标状态不一致');
        $db_target['target_name'] != $target['target_name'] && throw new Exception('指标名称不一致');
        $db_target['target_type'] != $target['target_type'] && throw new Exception('指标类型不一致');
        if ($target['target_type'] == 1) {
            $db_target['target_detail']['target_source'] != $target['target_detail']['target_source'] && throw new Exception('指标来源不一致');
            $db_target['target_detail']['target_method'] != $target['target_detail']['target_method'] && throw new Exception('指标考核方式不一致');
            $db_target['target_detail']['range_type'] != $target['target_detail']['range_type'] && throw new Exception('指标适用范围不一致');
            // 定量 - 财务指标，需要填写币种
            $db_target['target_detail']['target_source'] == 1 && $db_target['target_detail']['currency'] != $target['target_detail']['currency'] && returnError('币种不一致');
            // 按公式计算
            if ($db_target['target_detail']['target_method'] == 1) {
                $db_target['target_detail']['target_source'] != -1 && $db_target['target_detail']['column_id'] != $target['target_detail']['column_id'] && returnError('指标不一致');
                $db_target['target_detail']['leader_score'] != $target['target_detail']['leader_score'] && returnError('上级评分不一致');
                $db_target['target_detail']['standard_value']['type'] != $target['target_detail']['standard_value']['type'] && returnError('指标考核值类型不一致');
                // 固定考核值
                if ($db_target['target_detail']['standard_value']['type'] == 1) {
                    $db_target['target_detail']['standard_value']['value'] != $target['target_detail']['standard_value']['value'] && returnError('指标考核值不一致');
                }
            }
            // 按阶段划分
            elseif ($db_target['target_detail']['target_method'] == 2) {
                $db_target['target_detail']['target_source'] != -1 && empty($db_target['target_detail']['column_ids']) && returnError('指标ids不能为空');
            }

        }
        // 定性指标
        elseif ($target['target_type'] == 2) {
            $db_target['target_detail']['work_desc'] != $target['target_detail']['work_desc'] && throw new Exception('详述工作不一致');
        }
        return true;

    }

    // 对比提成比例数据
    public static function compareCommission($commission, $db_commission)
    {
        $db_commission['rule_name'] != $commission['rule_name'] && throw new Exception('提成比例名称不一致');
        $db_commission['qw_department_id'] != $commission['qw_department_id'] && throw new Exception('提成比例部门不一致');
        $db_commission['column_id'] != $commission['column_id'] && throw new Exception('提成比例指标不一致');
        $db_commission['currency'] != $commission['currency'] && throw new Exception('提成比例币种不一致');
        count($db_commission['prize_rules']) != count($commission['prize_rules']) && throw new Exception('提成比例规则数量不一致');

        foreach ($db_commission['prize_rules'] as $key => $value) {
            $value['column_id'] != $commission['prize_rules'][$key]['column_id'] && throw new Exception('提成比例奖励规则指标名称不一致');
        }
        return true;
    }

}