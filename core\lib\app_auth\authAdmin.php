<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/13 13:37
 */

namespace core\lib\app_auth;

use admin\models\userModel;

class authAdmin
{
    //公用方法（不用验证权限的接口）
    static array $common_auth = [
        //登录
        'admin/login/login', //登入
        'admin/login/logOut', //登出
        'admin/login/codeLogin', //code免登录
        'admin/user/getNoLoginCode',//免登录code获取
        //用户
        'admin/user/updateUserPwd', //当前登录人修改自己密码
        'admin/user/getUserInfo',   //当前登录人的基本信息
        'admin/user/setOftenUsedUser', //设置常用用户
        'admin/user/getOftenUsedUser', //获取常用用户
        'admin/user/getUserList', //查看成员列表
        //部门
        'admin/qwPartment/getList', //查看部门列表
        'admin/qwPartment/getTree', //查看部门树
        //应用
        'admin/application/getList',//应用列表获取
        'admin/application/setQuickApp',//设置应用快捷入口
        'admin/application/getQuickApp',//获取快捷入口
        //首页统计
        'admin/statistics/getCountList',//首页统计
        //角色管理
        'admin/sysRole/getAuth',  //获取权限列表
        'admin/sysRole/getRoleAuth',//获取角色权限
        //消息
        'admin/sysMessage/getMsgDetail',
        'admin/sysMessage/getList',   //消息列表
        'admin/sysMessage/getDetail',
        'admin/sysMessage/setAllRead',
        'admin/sysMessage/getUnreadList', //所有未读消息
        //上传接口
        'admin/upload/getExcelData',//获取excel信息
        'admin/upload/upload',//文件上传
        //待办
        'admin/matters/getWatingList',//待办列表
        'admin/matters/submitMatters',//待办提交
        'admin/matters/getCount',//待办左侧导航
        //已办
        'admin/matters/getCompletedList',//已办列表
        //全部流程
        'admin/matters/getAllProcess',//全部流程列表
        'admin/matters/getCountForProcess',//全部流程左侧导航
        //综合应用列表
        'admin/plugins/getList',
        //修改个人密码
        'admin/user/updateUserPwd',
        //角色列表
        'admin/sysRole/getlist',
        'admin/sysRole/getTemplate',//用户导入模板下载
    ];

    //验证用户请求权限
    public static function checkAuth($url, $module){
        if ($module == 'task') {
            return true;
        } else {
            if (in_array($url, self::$common_auth)) {
                return;
            }
            if (userModel::$is_super == 1) {
                return true;
            }
            $user_auth = json_decode(userModel::$auth);
            if (!in_array($url,$user_auth)) {
                SetReturn(-1,'暂无权限');
            } else {
                return true;
            }
        }
    }

}