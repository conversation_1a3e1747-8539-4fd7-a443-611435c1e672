<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/12 11:51
 */

namespace financial\common;

use core\lib\log;
use core\lib\predisV;

class lingXingApiBase
{
    private static string $api_key = 'ak_Uf3GtbPYDtgzg'; //appId
    //private $api_secret = 'fzdIXAh9rW8LYORqpXgPKA== ';
    private static string $api_base = 'https://openapi.lingxingerp.com';
    private static string $token_base = 'http://mailserv.best-envision.com/serve/system/token?Operator=sys_user&Channel=sz';
    private static string $token_key = 'oa_linxing_api_token_key';
    public static $param=[];//脚本请求的参数

    //获取token
    public static function getToken()
    {
        $redis = (new predisV())::$client;
        $token = $redis->get(self::$token_key);
        if ($token) {
            return $token;
        } else {
            $url = self::$token_base;
            $data = requestHttp($url);
            if (isset($data['resultCode'])) {
                if ($data['resultCode'] == 0) {
                    $redis->set(self::$token_key, $data['resultData']['token']);
                    $redis->expire(self::$token_key,5*60);
                    return $token;
                } else {
                    log::lingXingApi('token')->error('token获取失败'.$data);
                    echo 'token获取失败';
                    die;
                }
            } else {
                log::lingXingApi('token')->error('token获取失败'.$data);
                echo 'token获取失败';
                die;
            }
        }
    }
    //业务
    public static function bizBuild(array $data_arr){
        $to_sign_arr = [];
        if(!empty($data_arr)){
            foreach($data_arr as $k=>$v){
                if(is_object($v) || is_array($v)){
                    $v = json_encode($v,JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                }
                $to_sign_arr[$k] = $v;
            }
        }
        $tem_arr = array(
            //'app_key'=>$this->api_secret,
            'app_key'=>self::$api_key,
            'access_token' => self::getToken(),
            'timestamp'=>time()
        );
        //连接的参数
        $tem_str_ = '';
        foreach($tem_arr as $key=>$val){
            $tem_str_ .= $key.'='.$val.'&';
        }
        $tem_str_ = rtrim($tem_str_,'&');
        //签名的参数
        $tem_arr = array_merge($tem_arr,$to_sign_arr);
        ksort($tem_arr);
        $tem_str = '';
        foreach($tem_arr as $key=>$val){
            $tem_str .= $key.'='.$val.'&';
        }
        $sign_str = rtrim($tem_str,'&');
        $sign_ori = strtoupper(md5($sign_str));
        $sign = openssl_encrypt($sign_ori,'AES-128-ECB',self::$api_key,OPENSSL_RAW_DATA);
        return [
            'str'=>$tem_str_,
            'sign'=>base64_encode($sign),
            'body'=>json_encode($data_arr,JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
            'url'=>self::$api_base
        ];

    }
    //数据检测
    public static function errorCheck($res){
        if(!is_array($res)){
            returnError('数据错误');
        }
        if($res['code'] != 0){//curl网络错误
            $msg = !empty($res['msg'])?$res['msg'] : 'network error!';
            $msg = !empty($res['error_details']) ? $res['error_details'] : $msg;
            SetReturn('-1',$msg,self::$param);
        }else{
            $return['data'] = $res['data'];
            $return['total'] = isset($res['total']) ? $res['total'] : 0;
            return $return;
        }
    }

}