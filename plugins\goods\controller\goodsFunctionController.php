<?php
/**
 * @author: zhangguoming
 * @Time: 2024/1/11 9:22
 */

namespace  plugins\goods\controller;

use plugins\goods\models\userModel;
use core\lib\db\dbMysql;
use core\lib\log;

class goodsFunctionController
{
    public function getList(){
        $paras_list = array('fc_name','page','page_size');
        $param = arrangeParam($_POST, $paras_list);

        $where_str = 'where is_delete=0';
        $where_array = [];
        if (!empty($param['fc_name'])) {
            $where_str .= ' and (fc_name like :fc_name or fc_name_en like :fc_name)';
            $where_array['fc_name'] = '%'.$param['fc_name'].'%';
        }
        $db = dbMysql::getInstance();
        $db->table('goods_function');
        $db->where($where_str, $where_array);
        $data = $db->pages($param['page'], $param['page_size']);
        returnSuccess($data);
    }

    public function editFunction() {
        $paras_list = array('id','fc_name','fc_name_en');
        $request_list = ['fc_name'=>'功能名称','fc_name_en'=>'功能英文名'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $id = (int)($param['id']);
        $up_data = [
            'fc_name'=>$param['fc_name'],
            'fc_name_en'=>$param['fc_name_en'],
        ];
        if ($id) {
            $db = dbMysql::getInstance();
            $db->table('goods_function');
            $db->where('where id = '.$id);
            $up_data['updated_at'] = date('Y-m-d H:i:s');
            if ($db->update($up_data)) {
                log::goodspFuntionLog()->info('用户【'.userModel::$wid.'】修改：'.json_encode($param,JSON_UNESCAPED_UNICODE));
                SetReturn(0,'修改成功');
            } else {
                SetReturn(-1,'修改失败');
            }
        } else {
            $db = dbMysql::getInstance();
            $db->table('goods_function');
            $up_data['created_at'] = date('Y-m-d H:i:s');
            if ($db->insert($up_data)) {
                log::goodspFuntionLog()->info('用户【'.userModel::$wid.'】新增：'.json_encode($param,JSON_UNESCAPED_UNICODE));
                SetReturn(0,'添加成功');
            } else {
                SetReturn(-1,'添加失败');
            }
        }
    }

    //删除
    public function delFunction(){
        $id = $_POST['id'];
        $db = dbMysql::getInstance();
        $db->table('goods_function')
            ->where('where id=:id',['id'=>$id])
            ->update(['is_delete'=>1,'del_user_id'=>userModel::$qwuser_id]);
        SetReturn(0, '操作成功');
    }
}