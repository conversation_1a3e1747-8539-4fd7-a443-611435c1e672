<?php

namespace plugins\salary\form;

use core\lib\db\dbSMysql;

class auditForm
{
    // 获取审批配置
    public static function getAuditConfig($key) {
        $sdb = dbSMysql::getInstance();
        $config_data = $sdb->table('config')->where('where key_name = :key_name', ['key_name' => $key])->one();
        if (empty($config_data) || empty($config_data['data'])) {
            return [];
        }
        return json_decode($config_data['data'], true);
    }

    // 获取下一个审批节点
    public static function getNextNode($rule, $current_node = 0, $current_audit_user = 0) {
        $next_node = [];
        $node = [];
        $idx = 1;
        foreach ($rule as $item) {
            $item['node'] = $idx;
            if ($idx == $current_node) {
                $node = $item;

            }
            if ($idx > $current_node) {
                $next_node = $item;
                break;
            }
            $idx++;
        }
        if ($current_node) {
            // 先判断是否是依次审批
            $user_id = null;
            if ($node['type'] == 1) {
                $flag = false;
                foreach ($node['user'] as $user) {
                    if ($flag) {
                        $user_id = $user['id'];
                    }
                    if ($user['id'] == $current_audit_user) {
                        $flag = true;
                    }
                }
                $node['audit_user'] = $user_id;
            }

            // 当前节点未结束，下一个审批人
            if (!empty($user_id)) {
                return $node;
            }
        }

        switch ($next_node['type']) {
            case 1: // 依次审批
                $next_node['audit_user'] = $next_node['user'][0]['id'];
                break;
            case 2: // 会签
                $next_node['audit_user'] = array_column($next_node['user'], 'id');
                break;
            case 3: // 或签
                $next_node['audit_user'] = array_column($next_node['user'], 'id');
                break;
        }

        return $next_node;
    }

}