<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/12 15:35
 */

namespace  plugins\goods\form;

use core\lib\log;

class goodsProjectLogFrom
{
    public static array $change_agent_data = [];

    public static function changeAgentLog(){
        if (count(self::$change_agent_data)) {
            foreach (self::$change_agent_data as $v) {
                log::setGoodsProjectLog($v['goods_id'],$v['project_id'],$v['describe'],$v['matter_name']);
            }
        }
    }
}