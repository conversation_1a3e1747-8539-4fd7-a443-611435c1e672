<?php

namespace admin\form;

use admin\models\userModel;
use core\lib\db\dbAfMysql;
use core\lib\db\dbAMysql;
use core\lib\db\dbCMysql;
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use core\lib\db\dbShopMysql;
use core\lib\db\dbSMysql;

class sysMessageForm
{


    public static function getList($param)
    {
        $model_id = (int)$param['model_id'];
        $is_read = (int)$param['is_read'];
        $db = self::getDbInstance($model_id);
        $db->table('messages');

        if ($model_id == 2) {
            // 售后的表字段不同
            $db->where('wid = :wid', ['wid' => userModel::$wid]);
            $db->field('id, title, wid as qw_userid, type, text, model_id, created_at, remarks');
        } else {
            $db->where('qw_userid = :qw_userid', ['qw_userid' => userModel::$wid]);
            $db->field('id, title, qw_userid, type, text, model_id, created_at, remarks');
        }

        if ($is_read > -1) {
            $db->andWhere('is_read = :is_read', ['is_read' => $is_read]);
        }

        $db->order('is_read asc, id desc');

        return $db->pages($param['page'], $param['page_size']);
    }

    //获取各个模块未读消息数量
    public static function getUnreadList()
    {
        // 定义各模块的数据库实例和对应的用户字段
        $modules = [
            'good' => ['db' => dbMysql::getInstance(), 'userField' => 'qw_userid'],
            'aftersale' => ['db' => dbAfMysql::getInstance(), 'userField' => 'wid'],
            'financial' => ['db' => dbFMysql::getInstance(), 'userField' => 'qw_userid'],
            'salary' => ['db' => dbSMysql::getInstance(), 'userField' => 'qw_userid'],
            'assessment' => ['db' => dbAMysql::getInstance(), 'userField' => 'qw_userid'],
            'checkin' => ['db' => dbCMysql::getInstance(), 'userField' => 'qw_userid'],
            'shop' => ['db' => dbShopMysql::getInstance(), 'userField' => 'qw_userid'],
        ];

        $result = [];
        foreach ($modules as $key => $module) {
            $db = $module['db'];
            $userField = $module['userField'];
            $result[$key] = $db->table('messages')
                ->where("$userField = :userField", ['userField' => userModel::$wid])
                ->andWhere('is_read = :is_read', ['is_read' => 0])
                ->count();
        }

        // 汇总所有模块的未读消息数量
        $result['total'] = array_sum($result);

        return $result;
    }

    //获取企微通知的消息
    public static function getMsgDetail($secret_code)
    {
        $data = qwMsgDecryption($secret_code);
        $data = json_decode($data, true);
        $model_id = $data['system_type'] ?? 1;
        $id = $data['id'] ?? 0;
        if (!$id) {
            SetReturn(-1, '参数有误');
        }
        $db = self::getDbInstance($model_id);
        $db->table('messages');
        $db->where('where id = ' . $id);
        $data = $db->one();
        $db->update(['is_read' => 1]);
        returnSuccess(['data' => $data]);
    }


    //根据$model_id选择对应的数据库实例
    public static function getDbInstance($model_id)
    {
        switch ($model_id) {
            case 1:
                return dbMysql::getInstance();
            case 2:
                return dbAfMysql::getInstance();
            case 3:
                return dbFMysql::getInstance();
            case 4:
                return dbSMysql::getInstance();
            case 5:
                return dbAMysql::getInstance();
            case 6:
                return dbCMysql::getInstance();
            case 7:
                return dbShopMysql::getInstance();
            default:
                SetReturn(-1, '无效的模块ID');
        }
    }

    //消息详情
    public static function getDetail($id, $model_id)
    {
        $db = self::getDbInstance($model_id);
        // 查询消息详情
        $db->table('messages');
        $db->where('id = :id', ['id' => $id]);
        if ($model_id == 2) {
            $db->field('id, title, wid as qw_userid, type, text, model_id, created_at, remarks');
        } else {
            $db->field('id, title, qw_userid, type, text, model_id, created_at, remarks');
        }
        $data = $db->one();
        if (!$data) {
            SetReturn(-1, '消息不存在');
        }

        // 将消息标记为已读
        $db->table('messages');
        $db->where('id = :id', ['id' => $id]);
        $db->update(['is_read' => 1]);

        return $data;
    }


    //标记全部已读
    public static function setAllRead($type, $model_id)
    {
        $db = self::getDbInstance($model_id);

        // 拼接查询条件
        $db->table('messages');
        if ($model_id == 2) {
            $db->where('wid = :wid and is_read = 0', ['wid' => userModel::$wid]);
        } else {
            $db->where('qw_userid = :qw_userid and is_read = 0', ['qw_userid' => userModel::$wid]);
        }
        if ($type == 2) {
            $db->andWhere('and type=2');
        }
        $db->update(['is_read' => 1]);
        SetReturn(0, '全部已读');
    }


    public static function getMessageCount()
    {
        // 定义各模块的数据库实例和对应的用户字段
        $modules = [
            ['db' => dbMysql::getInstance(), 'userField' => 'qw_userid'], // 产品消息
            ['db' => dbAfMysql::getInstance(), 'userField' => 'wid'],       // 售后消息
            ['db' => dbFMysql::getInstance(), 'userField' => 'qw_userid'], // 财务消息
            ['db' => dbSMysql::getInstance(), 'userField' => 'qw_userid'], // 薪酬消息
            ['db' => dbAMysql::getInstance(), 'userField' => 'qw_userid'], // 绩效消息
            ['db' => dbCMysql::getInstance(), 'userField' => 'qw_userid'],  // 考勤消息
            ['db' => dbShopMysql::getInstance(), 'userField' => 'qw_userid']  // 考勤消息
        ];

        $total_msg_count = 0;
        $total_msg_copy_count = 0;

        // 当前用户ID
        $user_id = userModel::$wid;

        foreach ($modules as $module) {
            $db = $module['db'];
            $userField = $module['userField'];

            // 未读消息数量
            $msg_count = $db->table('messages')
                ->where("$userField = :user_id AND is_read = 0", ['user_id' => $user_id])
                ->count();
            $total_msg_count += $msg_count;

            // 未读且 type=2 的消息数量
            $msg_copy_count = $db->table('messages')
                ->where("$userField = :user_id AND is_read = 0", ['user_id' => $user_id])
                ->andWhere("type = 2")
                ->count();
            $total_msg_copy_count += $msg_copy_count;
        }

        $list = [
            'total_msg_count'      => $total_msg_count,
            'total_msg_copy_count' => $total_msg_copy_count,
        ];
        returnSuccess($list);
    }


}