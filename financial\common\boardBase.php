<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/18 13:54
 */

namespace financial\common;

use core\lib\db\dbFMysql;

class boardBase
{
    /**
     * @param array $res_list
     * @param array $m_date
     * @param $keys
     * @param int $type -1不整理，0按月整理，1按项目整理，
     * @return array
     * @throws \core\lib\ExceptionError //整理月数据，将数据按月为维度合并整理 (按规则计算需要)
     */
    public static function mergeMdataLx(array $res_list,array $m_date,string $country_code,array $keys,array $currency_keys=[],int $type=0) {
//        $report_data = self::getPriceByRoute($res_list,$m_date,$country_code,$currency_keys,$keys);
        $report_data = $res_list;
        $new_data = [];
        switch ($type) {
            case 0:
                foreach ($m_date as $month) {
                    if (!isset($new_data[$month])) {
                        $new_data[$month]['m_date'] = $month;
                        foreach ($keys as $key_1) {
                            $new_data[$month][$key_1] = 0;
                        }
                    }
                    foreach ($report_data as $data_) {
                        if ($data_['m_date'] == $month) {
                            foreach ($keys as $key_) {
                                $new_data[$month][$key_] += $data_[$key_];
                            }
                        }
                    }
                }
                break;
            case 1:
                $project_ids = array_unique(array_column($res_list,'project_id'));
                foreach ($project_ids as $project_id) {
                    if (!isset($new_data[$project_id])) {
                        foreach ($keys as $key_1) {
                            $new_data[$project_id][$key_1] = 0;
                        }
                    }
                    foreach ($report_data as $data_) {
                        if ($data_['project_id'] == $project_id) {
                            foreach ($keys as $key_) {
                                $new_data[$project_id][$key_] += $data_[$key_];
                            }
                        }
                    }
                }
                break;
            case -1:
                $new_data = $report_data;
                break;
        }
        return $new_data;
    }
    /**
     * @param $res_list
     * @param $month_list
     * @param $keys //计算的字段，为[]时表示计算一个字段，否则表示根据规则要计算的多个字段
     * @param $currency_keys //需要换算的字段
     * @return mixed
     * @throws \core\lib\ExceptionError 数据金额换算 汇总(根据规则计算的时候使用此方法, 费用鄙不是total)
     */
    protected static function getPriceByRoute($res_list,$month_list,$currency_code,$currency_keys=[],$keys=[]) {
        //获取要用的汇率
        $routing_ = [];
        if (!empty($currency_code)) {
            $db = dbFMysql::getInstance();
            $routing_list = $db->table('routing')
                ->where('where code=:code',['code'=>$currency_code])
                ->whereIn('date',$month_list)
                ->field('code,date,code,my_rate')
                ->list();
            foreach ($routing_list as $routing) {
                $routing_[$routing['date'].'_'.$routing['code']] = $routing['my_rate'];
            }
        }
        //计算每条金额数据换算
        if (count($keys)) {
            foreach ($res_list as &$item) {
                foreach ($keys as $key_) {
                    if (in_array($key_,$currency_keys)) {
                        $val_total = $item[$key_];
                        if (!empty($currency_code)) {
                            $routing_key = $item['m_date'].'_'.$currency_code;
                            if (isset($routing_[$routing_key])) {
                                $val_total = $val_total/$routing_[$routing_key];
                            }
                        }
                        $item[$key_] = $val_total;
                    }
                }
            }
        } else {
            foreach ($res_list as &$item) {
                $val_total = $item['total'];
                if (in_array($item['val_key'],$currency_keys)) {
                    if (!empty($currency_code)) {
                        $routing_key = $item['m_date'].'_'.$currency_code;
                        if (isset($routing_[$routing_key])) {
                            $val_total = $val_total/$routing_[$routing_key];
                        }
                    }
                }
                $item['total'] = $val_total;
            }
        }
        return $res_list;
    }
    //根据最新一个月汇率计算数据(报表表格使用)
    protected static function getPriceByNewRoute($res_list,$currency_code,$currency_keys,$keys,$month_list=[]) {
        //获取要用的汇率
        $rate = 1;
        if (!empty($currency_code) && $currency_code != 'CNY') {
            $db = dbFMysql::getInstance();
            $db->table('routing')
                ->where('where code=:code',['code'=>$currency_code]);
            if (count($month_list)) {
                $db->whereIn('date',$month_list);
            }
            $routing_ = $db->field('code,date,code,my_rate')
                ->order('date desc')
                ->one();
            if (!$routing_) {
                returnError('未找到'.$currency_code.'汇率信息');
            }
            $rate = $routing_['my_rate'];
            foreach ($res_list as &$item) {
                foreach ($keys as $key_) {
                    if (in_array($key_,$currency_keys)) {
                        $val_total = $item[$key_];
                        if ($rate != 1) {
                            $val_total = roundToString($val_total/$rate);
                        }
                        $item[$key_] = $val_total;
                    }
                }
            }
        }
        return $res_list;
    }
}