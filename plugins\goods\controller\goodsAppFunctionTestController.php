<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/22 11:27
 */

namespace  plugins\goods\controller;

use plugins\goods\form\goodsAppFunctionTestFrom;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;

class goodsAppFunctionTestController
{
    //APP功能验证 保存
    public function saveAppTest()
    {
        $paras_list = array('project_id', 'noise', 'noise_des', 'stall', 'stall_des','stall_not_link','stall_not_link_des','button_lights', 'button_lights_des', 'bt_switch', 'bt_switch_des', 'recommendation', 'recommendation_des', 'video', 'video_des', 'diy', 'diy_des', 'voice', 'voice_des', 'built', 'built_des','node_index','event_index');
        $request_list = ['project_id' => '项目ID', 'node_index' => '节点角标', 'event_index' => '事件角标'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $noise = (int)$param['noise'];
        $stall = (int)$param['stall'];
        $stall_not_link = (int)$param['stall_not_link'];
        $button_lights = (int)$param['button_lights'];
        $bt_switch = (int)$param['bt_switch'];
        $recommendation = (int)$param['recommendation'];
        $video = (int)$param['video'];
        $diy = (int)$param['diy'];
        $voice = (int)$param['voice'];
        $built = (int)$param['built'];
        if ($noise == 2) {
            if (empty($param['noise_des'])) {
                SetReturn(-1, '请填写未通过原因');
            }
        }
        if ($stall == 2) {
            if (empty($param['stall_des'])) {
                SetReturn(-1, '请填写未通过原因');
            }
        }
        if ($stall_not_link == 2) {
            if (empty($param['stall_not_link_des'])) {
                SetReturn(-1, '请填写未通过原因');
            }
        }
        if ($button_lights == 2) {
            if (empty($param['button_lights_des'])) {
                SetReturn(-1, '请填写未通过原因');
            }
        }
        if ($bt_switch == 2) {
            if (empty($param['bt_switch_des'])) {
                SetReturn(-1, '请填写未通过原因');
            }
        }
        if ($recommendation == 2) {
            if (empty($param['recommendation_des'])) {
                SetReturn(-1, '请填写未通过原因');
            }
        }
        if ($video == 2) {
            if (empty($param['video_des'])) {
                SetReturn(-1, '请填写未通过原因');
            }
        }
        if ($diy == 2) {
            if (empty($param['diy_des'])) {
                SetReturn(-1, '请填写未通过原因');
            }
        }
        if ($voice == 2) {
            if (empty($param['voice_des'])) {
                SetReturn(-1, '请填写未通过原因');
            }
        }
        if ($built == 2) {
            if (empty($param['built_des'])) {
                SetReturn(-1, '请填写未通过原因');
            }
        }
        $db = dbMysql::getInstance();
        $db->beginTransaction();
        try {
            //保存测试数据数据 + 日志记录
            goodsAppFunctionTestFrom::saveAppTest($param);
            $db->commit();
            returnSuccess('','保存成功');
        } catch (ExceptionError $error) {
            $db->commit();
            throw new ExceptionError($error->getMessage());

        }
    }
    //APP功能验证 查询
    public function getDetail() {
        $paras_list = ['project_id','node_index','event_index'];
        $request_list = ['project_id' => '项目ID', 'node_index' => '节点角标', 'event_index' => '事件角标'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $db = dbMysql::getInstance();
        $data = $db->table('goods_app_function_test')
            ->where('where project_id=:project_id and node_index=:node_index and event_index=:event_index',['project_id'=>$param['project_id'],'node_index'=>$param['node_index'],'event_index'=>$param['event_index']])
            ->one();
        returnSuccess($data);
    }

}