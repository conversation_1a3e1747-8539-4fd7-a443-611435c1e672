<?php
/**
 * @author: zhangguoming
 * @Time: 2025/4/15 16:28
 */

namespace plugins\goods\form;

use admin\common\upLoadBase;
use core\lib\config;
use core\lib\db\dbMysql;
use Rap2hpoutre\FastExcel\FastExcel;

class imgsRequestCollectionForm
{
    //列表获取
    public static function getList($param) {
        $db = dbMysql::getInstance();
        $db->table('imgs_request_collection','a')
            ->where('where a.is_delete = 0 and a.is_confirm = 1')
            ->leftJoin('goods_new','b','b.id = a.goods_id')
            ->leftJoin('qwuser','c','c.id = a.user_id')
            ->leftJoin('imgs_request','d','d.id = a.request_id');
        if (!empty($param['request_name'])) {
            $db->andWhere(' d.request_name like :request_name',['request_name'=>"%{$param['request_name']}%"]);
        }
        if (!empty($param['goods_name'])) {
            $db->andWhere(' b.goods_name like :goods_name',['goods_name'=>"%{$param['goods_name']}%"]);
        }
        if (!empty($param['goods_ids']) && $param['goods_ids'] != '[]') {
            $db->whereIn('a.goods_id',json_decode($param['goods_ids']));
        }
        if (!empty($param['type']) && $param['type'] != '[]') {
            $db->whereIn('d.type',json_decode($param['type']));
        }
        if (!empty($param['platform_id']) && $param['platform_id'] != '[]') {
            $db->whereIn('a.platform_id',json_decode($param['platform_id']));
        }
        if (!empty($param['second_types']) && $param['second_types'] != '[]') {
            $db->whereIn('a.second_type',json_decode($param['second_types']));
        }
        if (!empty($param['user_ids']) && $param['user_ids'] != '[]') {
            $db->whereIn('a.user_id',json_decode($param['user_ids']));
        }
        if (!empty($param['cate_ids']) && $param['cate_ids'] != '[]') {
            $db->andWhere('and JSON_OVERLAPS(b.cat_id, :cat_id)', ['cat_id' => $param['cate_ids']]);
        }
        if (!empty($param['color_ids']) && $param['color_ids'] != '[]') {
            $db->whereIn('a.color_id', json_decode($param['color_ids']));
        }
        if (!empty($param['language_ids']) && $param['language_ids'] != '[]') {
            $db->whereIn('a.language_id', json_decode($param['language_ids']));
        }

//        if (!empty($param['request_created_time']) && $param['request_created_time'] != '[]') {
//            $times = json_decode($param['request_created_time']);
//            $time_array = [
//                'created_time1'=>$times[0],
//                'created_time2'=>$times[1],
//            ];
//            $db->whereIn('d.created_time >= :created_time1 and d.created_time <= :created_time2', $time_array);
//        }
//        if (!empty($param['latest_expect_time']) && $param['latest_expect_time'] != '[]') {
//            $times = json_decode($param['latest_expect_time']);
//            $time_array = [
//                'latest_expect_time1'=>strtotime($times[0]),
//                'latest_expect_time2'=>strtotime($times[1]),
//            ];
//            $db->whereIn('d.latest_expect_time >= :latest_expect_time1 and d.latest_expect_time <= :latest_expect_time2', $time_array);
//        }
//        if (!empty($param['expected_time']) && $param['expected_time'] != '[]') {
//            $times = json_decode($param['expected_time']);
//            $time_array = [
//                'expected_time1'=>strtotime($times[0]),
//                'expected_time2'=>strtotime($times[1]),
//            ];
//            $db->whereIn('d.expected_time >= :expected_time1 and d.expected_time <= :expected_time2', $time_array);
//        }
//        if (!empty($param['real_expected_time']) && $param['real_expected_time'] != '[]') {
//            $times = json_decode($param['real_expected_time']);
//            $time_array = [
//                'real_expected_time1'=>strtotime($times[0]),
//                'real_expected_time2'=>strtotime($times[1]),
//            ];
//            $db->whereIn('d.real_expected_time >= :real_expected_time1 and d.real_expected_time <= :real_expected_time2', $time_array);
//        }
//        if (!empty($param['begin_time']) && $param['begin_time'] != '[]') {
//            $times = json_decode($param['begin_time']);
//            $time_array = [
//                'begin_time1'=>strtotime($times[0]),
//                'begin_time2'=>strtotime($times[1]),
//            ];
//            $db->whereIn('d.begin_time >= :begin_time1 and d.begin_time <= :begin_time2', $time_array);
//        }
//        if (!empty($param['task_reviewed_time']) && $param['task_reviewed_time'] != '[]') {
//            $times = json_decode($param['task_reviewed_time']);
//            $time_array = [
//                'task_reviewed_time1'=>strtotime($times[0]),
//                'task_reviewed_time2'=>strtotime($times[1]),
//            ];
//            $db->whereIn('d.task_reviewed_time >= :task_reviewed_time1 and d.task_reviewed_time <= :task_reviewed_time2', $time_array);
//        }
//        if (!empty($param['completion_time']) && $param['completion_time'] != '[]') {
//            $times = json_decode($param['completion_time']);
//            $time_array = [
//                'completion_time1'=>strtotime($times[0]),
//                'completion_time2'=>strtotime($times[1]),
//            ];
//            $db->whereIn('d.completion_time >= :completion_time1 and d.completion_time <= :completion_time2', $time_array);
//        }
//        if (!empty($param['update_time']) && $param['update_time'] != '[]') {
//            $times = json_decode($param['update_time']);
//            $time_array = [
//                'update_time1'=>strtotime($times[0]),
//                'update_time2'=>strtotime($times[1]),
//            ];
//            $db->whereIn('d.update_time >= :update_time1 and d.update_time <= :update_time2', $time_array);
//        }
//        if (!empty($param['distributor_ids'])) {
//            $db->andWhere('and a.file_type = :file_type',['file_type'=>$param['file_type']]);
//        }
        $order_str = '';
        if (!empty($param['order_by'])) {
            $order_by = json_decode($param['order_by'],true);
            foreach ($order_by as $k=>$ord){
                $order_str .= 'a.'.$k.' '.$ord.',';
            }
        }
        if (empty($order_str)) {
            $db->order('a.id desc');
        } else {
            $db->order(trim($order_str,','));
        }
        $db->field('a.id,a.source_type,a.file_name,a.extension,a.user_id,a.platform_id,b.goods_name,a.type,b.cat_id,c.wname as upload_wname,a.created_time,a.file_type,a.url,a.second_type,a.language_id,a.share_path,d.request_name,d.type as r_type');
        $list = $db->pages($param['page'],$param['page_size']);
        //语言
        $language_list = config::get('language_list','data');
        $language_ = array_column($language_list,'name','id');
        //业务平台
        $platform_list = config::get('imgs_platform','data');
        $platform_ = array_column($platform_list,'name','id');
        foreach ($list['list'] as &$v) {
            $v['language'] = $language_[$v['language_id']]??'';
            $v['platform_name'] = $platform_[$v['platform_id']]??'';
            $v['type_name'] = imgsRequestFrom::getTypeName($v['source_type'],$v['type'],$v['r_type']);
            $v['second_type_name'] = $v['second_type']==0?'':config::getDataName('imgs_second_type',$v['second_type']);
            $v['file_type_name'] = imgsRequestFrom::getFileTypeName($v['file_type']);
            $v['cat_data'] = goodsCateFrom::getGoodsCate(json_decode($v['cat_id']));
        }
        returnSuccess($list);
    }
    //导入共享盘文件
    public static function importShareField($data) {
        ini_set('memory_limit', '1024M');
        $uesful_data = [];
        $error_list = [];
        //产品查询
        $skus = array_unique(array_column($data,'产品SKU'));
        $skus = array_map('trim', $skus);
        $db = dbMysql::getInstance();
        $goods_list = $db->table('goods_color_relation')
            ->whereIn('sku',$skus)
            ->field('goods_id,color_id,sku')
            ->list();
        if (!count($goods_list)) {
            returnError('未找到任何产品数据');
        }
        $goods_ = [];
        foreach ($goods_list as $v) {
            $goods_[$v['sku']] = $v;
        }
        //语言
        $language_list = config::get('language_list','data');
        $language_ = array_column($language_list,'id','name');
        //业务平台
        $platform_list = config::get('imgs_platform','data');
        $platform_ = array_column($platform_list,'id','name');
        //已经上传的数据
        foreach ($data as $v) {
            $goods_info = $goods_[trim($v['产品SKU'])]??'';
            if (empty($goods_info)) {
                $v['失败原因'] = '产品不存在';
                $error_list[] = $v;
                continue;
            }
            if (empty($v['文件名称'])) {
                $v['失败原因'] = '缺少文件名称';
                $error_list[] = $v;
                continue;
            }
            $field_name_split = explode('.',$v['文件名称']);
            $extension = end($field_name_split);
            if (count($field_name_split) <= 1 || !in_array($extension,upLoadBase::$allow_suffix)) {
                $v['失败原因'] = '文件后缀错误';
                $error_list[] = $v;
                continue;
            }
            $file_type = (int)$v['文件类型：1源文件，2渲染文件，3结果文件'];
            if (!in_array($file_type,[1,2,3])) {
                $v['失败原因'] = '文件类型错误';
                $error_list[] = $v;
                continue;
            }
            $share_url = str_replace('\\\\192.168.10.116\\','',$v['文件地址']);
            if (empty($share_url)) {
                $v['失败原因'] = '缺少文件地址';
                $error_list[] = $v;
                continue;
            }
            $share_path = $share_url.'/'.$v['文件名称'];
            $share_path_bendi = '/data1/file-share/'.$share_url;
            $share_path = str_replace($share_path,'\\','/');
            if (is_file($share_path_bendi)) {
                $v['失败原因'] = '文件不存在';
                $error_list[] = $v;
                continue;
            }
            $language_id = $language_[$v['语言']]??0;
            if (!$language_id) {
                $v['失败原因'] = '语言不存在';
                $error_list[] = $v;
                continue;
            }
            $platform_id = $platform_[$v['业务平台']]??0;
            if (!$platform_id) {
                $v['失败原因'] = '业务平台不存在';
                $error_list[] = $v;
                continue;
            }
            $item = [
                'user_id'=>0,
                'is_confirm'=>1,
                'goods_id'=>$goods_info['goods_id'],
                'color_id'=>$goods_info['color_id'],
                'file_name'=>$v['文件名称'],
                'url'=>'/public/file-share/'.$share_path,
                'thumb_src'=>'/public/file-share/'.$share_path,
                'file_type'=>$file_type,
                'platform_id'=>$platform_id,
                'language_id'=>$language_id,
                'extension'=>$extension,
                'share_url'=>$share_path,
                'share_path'=>$v['文件地址'],
                'created_time'=>date('Y-m-d H:i:s')
            ];
            $uesful_data[] = $item;
        }
        if (count($uesful_data)) {
            //删除上传导入的
            $import_path_s = array_column($uesful_data,'share_path');
            $db->table('imgs_request_collection')
                ->whereIn('share_path',$import_path_s)
                ->update(['is_delete'=>1]);
            //新增本次导入的
            $keys = array_keys($uesful_data[0]);
            $insert_list = array_chunk($uesful_data, 3000);
            foreach ($insert_list as $v) {
                $db->table('imgs_request_collection')
                    ->insertBatch($keys,$v);
            }
        }
        //将失败的导出
        $error_path = '';
        if (count($error_list)) {
            // 生成这一批次的Excel文件名
            $error_path = '/public/temp/file_share/import_error/'. date('YmdHis'). uniqid(). '.xlsx';
            $url = SELF_FK. '/public/temp/file_share/import_error';
            if (!file_exists($url)) {
                mkdir($url, 0777, true);
            }
            $batchUrl = SELF_FK. $error_path;
            if (!file_exists($batchUrl)) {
                touch($batchUrl);
            }
            // 使用FastExcel导出这一批次的Excel文件
            (new FastExcel($error_list))->export($batchUrl);
        }
        return [
            'success_count'=>count($uesful_data),
            'error_count'=>count($error_list),
            'error_path'=>$error_path
        ];
    }
}