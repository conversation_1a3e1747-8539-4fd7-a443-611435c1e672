<?php

/**
 * @author: zhangguoming
 * @Time: 2024/3/27 9:02
 */

namespace plugins\assessment\models;


use core\lib\db\dbAMysql;

class assessmentModel
{
    public static int $id;
    public static string $assessment_name; // 考核名称
    public static string $user_id; // 发起人 qwuser_id
    public static string $a_s_id; // 考核方案id
    public static string $status; // 考核状态 -1未开始 0进行中 1已完成 2暂停 3终止
    public static string $assessment_cycle; // 考核周期
    public static string $assessment_desc; // 考核描述
    public static string $apply_range; // 考核范围
    public static string $result_view; // 结果可见范围
    public static string $attach; // 考核信息，发起时将方案、指标等，全部快照存储

    // 获取考核状态（方案）
    public static function getAssessmentStatus($id, $status_map = []) :string
    {
        // 考核内全部终止，是已终止；//考核内有进行中，是进行中；//考核内全部暂停，是已暂停
        if (empty($status_map)) {
            $adb = dbAMysql::getInstance();
            $adb->table('assessment_users', 'au')->field('au.status, count(au.id) as count');
            $adb->where('where a_id = :a_id', ['a_id' => $id]);
            $adb->groupBy(['au.status']);
            $status = $adb->list();
            $status_map = array_column($status, 'count', 'status');
        }

        if (isset($status_map[0]) && $status_map[0] > 0) { // 考核中
            return '0';
        } elseif (isset($status_map[2]) && $status_map[2] > 0) { // 已暂停
            return '2';
        } elseif (isset($status_map[1]) && $status_map[1] > 0) { // 已完成
            return '1';
        } else { // 终止
            return '3';
        }
    }

    //  获取考核中的所有涉及的指标、提成比例、等级
    public static function getRunningAssessmentDetail($type): array
    {
        if (!in_array($type, ['scheme', 'target', 'commission', 'level'])) return [];
        // 获取非考核完成、终止的考核
        $adb = dbAMysql::getInstance();
        $adb->table('assessment')->field('id, a_s_id');
        $adb->where('where is_delete = 0 and status != 1 and status != 3');
        $assessment = $adb->list();
        $scheme_ids = array_values(array_unique(array_column($assessment, 'a_s_id')));

        if ($type == 'scheme') return $scheme_ids;
        if (empty($scheme_ids)) return [];

        // 查询所有考核方案
        $adb->table('assessment_schemes');
        $adb->where('where is_delete = 0');
        $adb->whereIn('id', $scheme_ids);
        $schemes = $adb->list();

        $target_ids = [];
        $commission_ids = [];
        $level_ids = [];
        foreach ($schemes as $scheme) {
            $template = $scheme['assessment_template'] ? json_decode($scheme['assessment_template'], true) : null;
            $process = $scheme['assessment_scheme_process'] ? json_decode($scheme['assessment_scheme_process'], true) : null;
            if (empty($template['list'])) continue;
            foreach ($template['list'] as $item) {
                $target_ids[] = $item['id'];
            }
            if ($process[4]['performance_calculation_method'] == 1) { // 按公式核算
                foreach ($process[4]['formula'] as $formula) {
                    if ($formula['type'] == 1) { // 条件
                        if ($formula['value'][0] == 2) { // 提成比例
                            $commission_ids[] = $formula['value'][1];
                        }
                    } elseif ($formula['type'] == 2) { // 条件组
                        foreach ($formula['list'] as $sub_formula) {
                            if ($sub_formula['value'][0] == 2) { // 提成比例
                                $commission_ids[] = $sub_formula['value'][1];
                            }
                        }
                    }
                }
            }
            if ($process[4]['performance_calculation_method'] == 3) { // 按等级核算
                $level_ids[] = $process[4]['level_rule_id'];
            }
        }

        // 指标校验
        if ($type == 'target') return array_values(array_unique($target_ids));
        if ($type == 'commission') return array_values(array_unique($commission_ids));
        if ($type == 'level') return array_values(array_unique($level_ids));

        return [];
    }
}