openapi: 3.0.0
info:
  title: 店铺申请API
  version: 1.0.0
  description: 提供店铺申请的相关接口

paths:
  /shop/shopApply/getList:
    get:
      tags:
        - 店铺申请管理
      summary: 获取申请列表
      description: 根据条件筛选获取店铺申请列表
      parameters:
        - name: department_id
          in: query
          description: 部门id
          required: false
          schema:
            type: integer
        - name: site
          in: query
          description: 站点
          required: false
          schema:
            type: string
        - name: shop_type
          in: query
          description: 店铺类型
          required: false
          schema:
            type: string
        - name: status
          in: query
          description: 状态(0待审核,1审核通过,2审核不通过,3已取消,4已接收,5已分配)
          required: false
          schema:
            type: integer
        - name: created_by
          in: query
          description: 创建人id
          required: false
          schema:
            type: integer
        - name: date_start
          in: query
          description: 创建开始日期
          required: false
          schema:
            type: string
            format: date-time
        - name: date_end
          in: query
          description: 创建结束日期
          required: false
          schema:
            type: string
            format: date-time
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页条数
          required: false
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/ShopApply'
                      total:
                        type: integer
                        description: 总记录数

  /shop/shopApply/apply:
    post:
      tags:
        - 店铺申请管理
      summary: 提交店铺申请
      description: 创建新的店铺申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ShopApplyCreate'
      responses:
        '200':
          description: 申请成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonResponse'

  /shop/shopApply/cancel:
    post:
      tags:
        - 店铺申请管理
      summary: 取消申请
      description: 取消已提交的店铺申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 申请ID
              required:
                - id
      responses:
        '200':
          description: 取消成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonResponse'

  /shop/shopApply/audit:
    post:
      tags:
        - 店铺申请管理
      summary: 审核申请
      description: 审核店铺申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 申请ID
                audit_result:
                  type: boolean
                  description: 审核结果（true通过，false不通过）
                audit_remark:
                  type: string
                  description: 审核意见
              required:
                - id
                - audit_result
                - audit_remark
      responses:
        '200':
          description: 审核成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonResponse'

  /shop/shopApply/edit:
    post:
      tags:
        - 店铺申请管理
      summary: 编辑申请
      description: 编辑已驳回的申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ShopApplyEdit'
      responses:
        '200':
          description: 编辑成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonResponse'

  /shop/shopApply/receive:
    post:
      tags:
        - 店铺申请管理
      summary: 接收申请
      description: 接收已审核通过的申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 申请ID
              required:
                - id
      responses:
        '200':
          description: 接收成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonResponse'

  /shop/shopApply/assign:
    post:
      tags:
        - 店铺申请管理
      summary: 分配店铺
      description: 为已接收的申请分配店铺
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 申请ID
                shop_id:
                  type: integer
                  description: 店铺ID
                remark:
                  type: string
                  description: 备注
              required:
                - id
                - shop_id
      responses:
        '200':
          description: 分配成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonResponse'

  /shop/shopApply/cancelAssign:
    post:
      tags:
        - 店铺申请管理
      summary: 撤回分配
      description: 撤回已分配的店铺
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 申请ID
              required:
                - id
      responses:
        '200':
          description: 撤回成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonResponse'

  /shop/shopApply/detail:
    get:
      tags:
        - 店铺申请管理
      summary: 获取详情
      description: 获取申请详细信息
      parameters:
        - name: id
          in: query
          description: 申请ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    $ref: '#/components/schemas/ShopApply'

  /shop/shopApply/urge:
    post:
      tags:
        - 店铺申请管理
      summary: 催办
      description: 对申请进行催办
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 申请ID
              required:
                - id
      responses:
        '200':
          description: 催办成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonResponse'

components:
  schemas:
    ShopApply:
      type: object
      properties:
        id:
          type: integer
          description: 申请ID
        department_id:
          type: integer
          description: 部门ID
        site:
          type: string
          description: 站点
        shop_type:
          type: string
          description: 店铺类型
        expect_date:
          type: string
          format: date
          description: 期望下达日期
        remind_interval:
          type: integer
          description: 间隔提醒(天)
        apply_reason:
          type: string
          description: 申请原因
        remark:
          type: string
          description: 备注
        status:
          type: integer
          description: 状态(0待审核,1审核通过,2审核不通过,3已取消,4已接收,5已分配)
        shop_id:
          type: integer
          description: 店铺ID
        created_at:
          type: string
          format: date-time
          description: 创建时间
        updated_at:
          type: string
          format: date-time
          description: 更新时间

    ShopApplyCreate:
      type: object
      properties:
        department_id:
          type: integer
          description: 部门ID
        site:
          type: string
          description: 站点
        shop_type:
          type: string
          description: 店铺类型
        expect_date:
          type: string
          format: date
          description: 期望下达日期
        remind_interval:
          type: integer
          description: 间隔提醒(天)
        apply_reason:
          type: string
          description: 申请原因
        remark:
          type: string
          description: 备注
      required:
        - department_id
        - site
        - shop_type
        - expect_date
        - remind_interval
        - apply_reason

    ShopApplyEdit:
      type: object
      properties:
        id:
          type: integer
          description: 申请ID
        department_id:
          type: integer
          description: 部门ID
        site:
          type: string
          description: 站点
        shop_type:
          type: string
          description: 店铺类型
        expect_date:
          type: string
          format: date
          description: 期望下达日期
        remind_interval:
          type: integer
          description: 间隔提醒(天)
        apply_reason:
          type: string
          description: 申请原因
        remark:
          type: string
          description: 备注
      required:
        - id

    CommonResponse:
      type: object
      properties:
        code:
          type: integer
          example: 0
        message:
          type: string
          example: 成功
        data:
          type: object
