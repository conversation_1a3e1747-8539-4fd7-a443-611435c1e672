<?php
/**
 * @author: zhangguoming
 * @Time: 2025/3/25 15:31
 */

namespace admin\form;

use admin\models\userModel;
use core\lib\db\dbMysql;

class applicationForm
{
    //用户可用应用列表获取
    public static function getList(string $app_name) {
        $db = dbMysql::getInstance();
        //查询plugin
        $plugin_list = $db->table('plugins')
            ->where('status = 1')
            ->order('sort asc')
            ->field('id,key_name,name')
            ->list();
        $plugin_ = [];
        foreach ($plugin_list as $v) {
            $plugin_[$v['key_name']] = $v;
        }
        //获取用户权限
        //1、总系统,根据权限获取插件
        if (!userModel::$is_super) {
            $auth = json_decode(userModel::$auth);
            $plugin_ = array_intersect_key($plugin_,array_flip($auth));
        }
        $plugin_ids = array_column($plugin_,'id');
        //查询plugin的功能模块
        $db->table('application')
            ->where('is_delete = 0')
            ->whereIn('plugin_id',$plugin_ids);
        if (!empty($app_name)) {
            $db->andWhere('name like :name',['name'=>'%'.$app_name.'%']);
        }
        $application_list = $db
            ->field('key_name,name,plugin_id,icon,path')
            ->order('sort asc')
            ->list();
        $res_application = [];
        if (count($application_list)) {
            $application_ = [];
            foreach ($application_list as $v) {
                $application_[$v['plugin_id']][$v['key_name']] = $v;
            }
            foreach ($plugin_ids as $plugin_id) {
                $this_app = $application_[$plugin_id]??[];
                if (!userModel::$is_super) {
                    switch ($plugin_id) {
                        case 1:
                            $all_auth = \plugins\goods\form\qwuserFrom::getUserAuth();
                            $this_auth = $all_auth['auth'];
                            break;
                        case 4:
                            $all_auth = \plugins\assessment\models\userRolesModel::getAuthByQuserId(userModel::$qwuser_id);
                            $this_auth = json_decode($all_auth['auth']);
                            break;
                        case 5:
                            $all_auth = \plugins\checkin\models\userRolesModel::getAuthByQuserId(userModel::$qwuser_id);
                            $this_auth = json_decode($all_auth['auth']);
                            break;
                        case 6:
                            $all_auth = \plugins\salary\models\userRolesModel::getAuthByQuserId(userModel::$qwuser_id);
                            $this_auth = json_decode($all_auth['auth']);
                            break;
                        default:
                            $this_auth = [];
                            break;
                    }
                    if (count($this_auth)) {
                        $this_app = array_intersect_key($this_app,array_flip($this_auth));
                    } else {
                        $this_app = [];
                    }
                }
                $res_application[$plugin_id] = array_values($this_app);
            }
        }
        //最终数据处理
        $res_data = [];
        foreach ($plugin_ as $v) {
            $v['child'] = $res_application[$v['id']]??[];
            $res_data[] = $v;
        }
        return $res_data;
    }
    //常用应用设置
    public static function setUserApp(string $keys) {
        $db = dbMysql::getInstance();
        $user_quick_app = $db->table('user_quick_app')
            ->where('qwuser_id = :user_id',['user_id'=>userModel::$qwuser_id])
            ->one();
        if ($user_quick_app) {
            $db->table('user_quick_app')
                ->where('id = :id',['id'=>$user_quick_app['id']])
                ->update(['app_keys'=>$keys]);
        } else {
            $db->table('user_quick_app')
                ->insert(['qwuser_id'=>userModel::$qwuser_id,'app_keys'=>$keys]);
        }
    }
    //获取常用快捷应用
    public static function getUserApp() {
        $db = dbMysql::getInstance();
        $quick_app = $db->table('user_quick_app')
            ->where('qwuser_id = :qwuser_id',['qwuser_id'=>userModel::$qwuser_id])
            ->one();
        if (!$quick_app) {
            return [];
        }
        $app_keys = $quick_app['app_keys'];
        if (empty($app_keys)) {
            return [];
        }
        $app_keys = json_decode($app_keys,true);
        $plugins_keys = array_keys($app_keys);
        $application_keys =  array_merge(...array_values($app_keys));
        $plugin_list = $db->table('plugins')
            ->where('status = 1')
            ->whereIn('key_name',$plugins_keys)
            ->order('sort asc')
            ->field('id,key_name,name')
            ->list();
        $application_list = $db->table('application')
            ->where('is_delete = 0')
            ->whereIn('key_name',$application_keys)
            ->field('key_name,name,plugin_id,icon,path')
            ->order('sort asc')
            ->list();
        if (count($plugin_list)) {
            $application_list_ = [];
            foreach ($application_list as $v) {
                $application_list_[$v['plugin_id']][] = $v;
            }
            foreach ($plugin_list as $k=>$v) {
                $plugin_list[$k]['child'] = $application_list_[$v['id']]??[];
            }
        }
        return $plugin_list;
    }
}