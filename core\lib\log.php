<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/8 9:03
 */

namespace core\lib;

use admin\models\userModel;
use core\lib\db\dbMysql;
use plugins\goods\form\goodsNewFrom;


class log
{
    private static object|null $o = null;
    private static string $current_path = '';

    //任务日志（3点的员工同步记录）
    public static function taskLog(){
        self::$current_path = LOG_PATH.'/task/';
        if (self::$o != null) {
            return self::$o;
        } else {
            self::$o = new self;
            return self::$o;
        }
    }
    //项目操作日志
    public static function goodspProjectLog()
    {
        self::$current_path = LOG_PATH.'/goodsProjectLog/';
        if (self::$o != null) {
            return self::$o;
        } else {
            self::$o = new self;
            return self::$o;
        }
    }
    //商品功能操作日志
    public static function goodspFuntionLog()
    {
        self::$current_path = LOG_PATH.'/goodsFunctionLog/';
        if (self::$o != null) {
            return self::$o;
        } else {
            self::$o = new self;
            return self::$o;
        }
    }
    //商品颜色操作日志
    public static function goodsColorLog()
    {
        self::$current_path = LOG_PATH.'/goodsColorLog/';
        if (self::$o != null) {
            return self::$o;
        } else {
            self::$o = new self;
            return self::$o;
        }
    }
    public static function goodsCateLog()
    {
        self::$current_path = LOG_PATH.'/goodsCateLog/';
        if (self::$o != null) {
            return self::$o;
        } else {
            self::$o = new self;
            return self::$o;
        }
    }

    public function info($msg){
        self::$current_path .= 'info/';
        if (!file_exists(self::$current_path)) {
            mkdir(self::$current_path, 0777, true);
        }
        $file = self::$current_path . date('Ymd').'.log';
        $str = date('Y-m-d H:i:s').': '.$msg;
        if (!file_exists($file)) {
            file_put_contents($file, $str.PHP_EOL);
        } else {
            file_put_contents($file, $str.PHP_EOL, FILE_APPEND);
        }
    }
    public function error($msg){
        self::$current_path .= 'error/';
        if (!file_exists(self::$current_path)) {
            mkdir(self::$current_path, 0777, true);
        }
        $file = self::$current_path . date('Ymd').'.log';
        $str = date('Y-m-d H:i:s').': '.$msg;
        if (!file_exists($file)) {
            file_put_contents($file, $str.PHP_EOL);
        } else {
            file_put_contents($file, $str.PHP_EOL, FILE_APPEND);
        }
    }

    /**
     * @param $goods_id
     * @param $project_id
     * @param $describe
     * @param $type  类型，0创建，1修改；设置产信息修改的日志
     */
    public static function setGoodsInfoLog($goods_id, $type):void
    {
        $describe = '';
        if ($type == 0) {
            $describe = '新增产品';
        } else {
            $goods_update_log_data = goodsNewFrom::$update_log_data;
            foreach ($goods_update_log_data as $v) {
                if (count($v)) {
                    foreach ($v as $v1) {
                        $describe .= $v1['key_name'].'：【'.$v1['data'].'】，';
                    }
                }
            }
            if ($describe != '') {
                $describe = '修改产品信息，'.trim($describe,',');
            }
        }
        if (!empty($describe)) {
            $describe = trim($describe,'，');
            $data = [
                'qwuser_id'=>userModel::$qwuser_id,
                'wname'=>userModel::$wname,
                'goods_id'=>$goods_id,
                'api'=>API_ROUTE,
                'remark'=>$describe,
                'type'=>$type,
                'created_at'=>date('Y-m-d H:i:s'),
            ];
            $db = dbMysql::getInstance();
            $db->table('goods_new_log');
            $db->insert($data);
        }
    }

    /**
     * @param $goods_id
     * @param $project_id
     * @param $describe
     * @param $dmatter_name 流程名称
     * @param $type  -1创建，0修改，-2异常 ,其他为事件类型
     * 设置项目流程中操作日志
     */
    public static function setGoodsProjectLog($goods_id, $project_id, $describe,$matter_name, $type = 0):void
    {
        $data = [
            'qwuser_id'=>userModel::$wid,
            'qw_name'=>userModel::$wname,
            'goods_id'=>$goods_id,
            'goods_project_id'=>$project_id,
            'matter_name'=>$matter_name,
            'api'=>API_ROUTE,
            'remark'=>$describe,
            'type'=>$type,
            'created_at'=>date('Y-m-d H:i:s'),
        ];
        $db = dbMysql::getInstance();
        $db->table('goods_project_log');
        $db->insert($data);
    }

    /**
     * 下载文件记录
     * @param $file_id 文件id
     * @param $source_type  1文件库，2产品附件, 3图片需求池，4，说明说，5图片管理
     */
    public static function downLoadLog(string $file_id, int $source_type){
        $data = [
            'qw_userid'=>userModel::$wid,
            'qw_name'=>userModel::$wname,
            'file_id'=>$file_id,
            'type'=>$source_type,
            'api'=>API_ROUTE,
            'created_at'=>date('Y-m-d H:i:s'),
        ];
        $db = dbMysql::getInstance();
        $db->table('file_down_log');
        $db->insert($data);
    }

    /**
     * @return log|object|null  获取领星数据日志
     */
    public static function lingXingApi($model)
    {
        self::$current_path = LOG_PATH.'/lingXingApi/'.$model.'/';
        if (self::$o != null) {
            return self::$o;
        } else {
            self::$o = new self;
            return self::$o;
        }
    }

    /**
     * @return log|object|null 延时队列
     */
    public static function delayQueue()
    {
        self::$current_path = LOG_PATH.'/delayQueue/';
        if (self::$o != null) {
            return self::$o;
        } else {
            self::$o = new self;
            return self::$o;
        }
    }
}