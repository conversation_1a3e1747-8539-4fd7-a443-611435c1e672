<?php

namespace plugins\checkin\models;

use core\lib\db\dbCMysql;

class holidayModel
{
    public static int $id;
    public static int $user_id;
    public static string $holiday_name;
    public static string $workday; // 调班日期，json格式，日期数组
    public static string $nonworkday; // 休息日期，json格式，日期范围数组

    public static int $status;

    // 获取节假日列表
    public static function getHolidayByTimeRange($start_time, $end_time)
    {
        if(empty($start_time) || empty($end_time)) return [];
        $cdb = dbCMysql::getInstance();
        $cdb->table('holiday');
        $cdb->where('where is_delete = 0 and status = 1');
        $cdb->andwhere("(EXISTS (SELECT 1 FROM JSON_TABLE(workday, '$[*]' COLUMNS(date_value DATE PATH '$')) AS jt WHERE jt.date_value BETWEEN :start_time AND :end_time) or (JSON_EXTRACT(nonworkday, '$[0]') <= :end_time AND JSON_EXTRACT(nonworkday, '$[1]') >= :start_time))", ['start_time' => $start_time, 'end_time' => $end_time]);
        $list = $cdb->list();

        $workday = [];
        $nonworkday = [];
        $holiday = [];
        foreach ($list as $item) {
            $item['workday'] = json_decode($item['workday'], true);
            foreach ($item['workday'] as $d) {
                if (strtotime($d) >= strtotime($start_time) && strtotime($d) <= strtotime($end_time)) {
                    $workday[] = $d;
                }
            }
            $item['holiday'] = json_decode($item['holiday'], true);
            foreach ($item['holiday'] as $d) {
                if (strtotime($d) >= strtotime($start_time) && strtotime($d) <= strtotime($end_time)) {
                    $holiday[] = $d;
                }
            }
            // 连续天，需要逐日添加
            $item['nonworkday'] = json_decode($item['nonworkday'], true);
            for ($current = strtotime($item['nonworkday'][0]); $current <= strtotime($item['nonworkday'][1]); $current += 86400) {
                if ($current >= strtotime($start_time) && $current <= strtotime($end_time)) {
                    $nonworkday[] = date('Y-m-d', $current);
                }
            }
        }
        $workday = array_values(array_unique($workday));
        $nonworkday = array_values(array_unique($nonworkday));
        $holiday = array_values(array_unique($holiday));
        return ['workday' => $workday, 'nonworkday' => $nonworkday, 'holiday' => $holiday];
    }

}