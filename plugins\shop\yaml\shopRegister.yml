openapi: 3.0.0
info:
  title: 店铺注册API
  version: 1.0.0
  description: 提供店铺注册相关接口

paths:
  /shop/shopRegister/add:
    post:
      tags:
        - 店铺注册管理
      summary: 新增注册任务
      description: 创建新的店铺注册任务
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ShopRegisterCreate'
      responses:
        '200':
          $ref: '#/components/responses/Success'

  /shop/shopRegister/follow:
    post:
      tags:
        - 店铺注册管理
      summary: 跟进注册任务
      description: 更新注册任务进展
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 注册任务ID
                is_end:
                  type: boolean
                  description: 是否结束跟进
                register_result:
                  type: boolean
                  description: 注册结果(true成功,false失败)
                register_date:
                  type: string
                  format: date
                  description: 店铺注册日期
                apply_id:
                  type: integer
                  description: 关联店铺申请id
                follow_progress:
                  type: string
                  description: 注册进展
              required:
                - id
                - is_end
      responses:
        '200':
          $ref: '#/components/responses/Success'

  /shop/shopRegister/reRegister:
    post:
      tags:
        - 店铺注册管理
      summary: 重新注册
      description: 针对注册失败的任务创建新的注册任务
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 失败任务的ID
              required:
                - id
      responses:
        '200':
          $ref: '#/components/responses/Success'

  /shop/shopRegister/pause:
    post:
      tags:
        - 店铺注册管理
      summary: 暂停任务
      description: 暂停注册任务
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 注册任务ID
              required:
                - id
      responses:
        '200':
          $ref: '#/components/responses/Success'

  /shop/shopRegister/cancelPause:
    post:
      tags:
        - 店铺注册管理
      summary: 取消暂停
      description: 取消暂停状态，恢复注册任务
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 注册任务ID
              required:
                - id
      responses:
        '200':
          $ref: '#/components/responses/Success'

  /shop/shopRegister/urge:
    post:
      tags:
        - 店铺注册管理
      summary: 催办
      description: 催办注册任务
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 注册任务ID
              required:
                - id
      responses:
        '200':
          $ref: '#/components/responses/Success'

  /shop/shopRegister/getList:
    get:
      tags:
        - 店铺注册管理
      summary: 获取列表
      description: 获取注册任务列表
      parameters:
        - name: register_type
          in: query
          description: 注册类型
          schema:
            type: string
        - name: shop_code
          in: query
          description: 店铺编号
          schema:
            type: string
        - name: company_id
          in: query
          description: 公司id
          schema:
            type: integer
        - name: account_type
          in: query
          description: 账号类型
          schema:
            type: string
        - name: shop_site
          in: query
          description: 店铺站点
          schema:
            type: string
        - name: contact_person
          in: query
          description: 对接人
          schema:
            type: string
        - name: status
          in: query
          description: 状态(0待处理,1处理中,2注册成功,3注册失败,4已暂停)
          schema:
            type: integer
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页条数
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/ShopRegister'
                      total:
                        type: integer
                        description: 总记录数

  /shop/shopRegister/detail:
    get:
      tags:
        - 店铺注册管理
      summary: 获取详情
      description: 获取注册任务详情
      parameters:
        - name: id
          in: query
          description: 注册任务ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回详情数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    $ref: '#/components/schemas/ShopRegister'

components:
  schemas:
    ShopRegister:
      type: object
      properties:
        id:
          type: integer
          description: 任务ID
        register_type:
          type: string
          description: 注册类型
        shop_code:
          type: string
          description: 店铺编号
        company_id:
          type: integer
          description: 公司id
        account_type:
          type: string
          description: 账号类型
        contact_person:
          type: string
          description: 对接人
        shop_site:
          type: string
          description: 店铺站点
        business_manager:
          type: string
          description: 招商经理
        receive_card_id:
          type: integer
          description: 收款卡号id
        contact_group:
          type: string
          description: 对接群
        register_device:
          type: string
          description: 注册设备
        register_phone_id:
          type: integer
          description: 注册手机号id
        register_email_id:
          type: integer
          description: 注册邮箱id
        credit_card_id:
          type: integer
          description: 信用卡id
        shop_password:
          type: string
          description: 店铺密码
        vat_status:
          type: string
          description: VAT注册情况
        erp_status:
          type: string
          description: ERP注册情况
        status:
          type: integer
          description: 状态(0待处理,1处理中,2注册成功,3注册失败,4已暂停)
        is_end:
          type: boolean
          description: 是否结束跟进
        register_result:
          type: boolean
          description: 注册结果
        register_date:
          type: string
          format: date
          description: 店铺注册日期
        apply_id:
          type: integer
          description: 关联店铺申请id
        follow_progress:
          type: string
          description: 注册进展
        remark:
          type: string
          description: 备注
        created_at:
          type: string
          format: date-time
          description: 创建时间

    ShopRegisterCreate:
      type: object
      properties:
        register_type:
          type: string
          description: 注册类型
        shop_code:
          type: string
          description: 店铺编号
        company_id:
          type: integer
          description: 公司id
        account_type:
          type: string
          description: 账号类型
        contact_person:
          type: string
          description: 对接人
        shop_site:
          type: string
          description: 店铺站点
        business_manager:
          type: string
          description: 招商经理
        receive_card_id:
          type: integer
          description: 收款卡号id
        contact_group:
          type: string
          description: 对接群
        register_device:
          type: string
          description: 注册设备
        register_phone_id:
          type: integer
          description: 注册手机号id
        register_email_id:
          type: integer
          description: 注册邮箱id
        credit_card_id:
          type: integer
          description: 信用卡id
        shop_password:
          type: string
          description: 店铺密码
        vat_status:
          type: string
          description: VAT注册情况
        erp_status:
          type: string
          description: ERP注册情况
        remark:
          type: string
          description: 备注
      required:
        - register_type
        - shop_code
        - company_id
        - account_type
        - contact_person
        - shop_site

  responses:
    Success:
      description: 成功响应
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                example: 0
              message:
                type: string
                example: 成功
              data:
                type: object
