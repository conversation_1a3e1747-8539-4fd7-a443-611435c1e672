<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/13 9:10
 */

namespace plugins\salary\Controller;

use plugins\assessment\models\customCrontabModel;
use core\lib\config;
use core\lib\db\dbAMysql;
use core\lib\redisCached;
use DateTime;
use Rap2hpoutre\FastExcel\FastExcel;
use plugins\salary\form\auditForm;
use plugins\salary\form\messagesFrom;
use plugins\salary\models\userModel;
use core\lib\db\dbMysql;
use core\lib\db\dbSMysql;
use plugins\salary\models\userInfoModel;
use plugins\salary\models\userSalaryModel;
use Throwable;

class userInfoController
{
    // 列表
    public function getList()
    {
        $paras_list = array('page', 'page_size', 'corp_id', 'dep_id', 'user_id', 'user_status', 'user_type', 'salary_status', 'work_place');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();

        $db->table('qwuser', 'u')
            ->field('u.id as user_id,u.wid as user_wid, u.wname as user_name, u.wphone as user_wphone, u.wdepartment_ids as user_wdepartment_ids,u.wmain_department, u.position as user_position, ui.*')
            ->leftJoin('user_info', 'ui', 'u.id=ui.qwuser_id');
        $db->where('1=1');

        if (userModel::getUserListAuth('userInfoAll')) {
            // 全部权限
        } elseif (userModel::getUserListAuth('userInfoWorkPlace')) {
            $auth_work_place_id = [];
            $gid = ["1", "2"];
            foreach ($gid as $item) {
                if (userModel::getUserListAuth('userInfoWorkPlace_' . $item)) {
                    $auth_work_place_id[] = $item;
                }
            }
            if (empty($auth_work_place_id)) {
                returnError('无权限查看');
            }
            $db->whereIn('ui.work_place', $auth_work_place_id);
        }

        // 员工
        if (!empty($param['user_id'])) {
            $users = json_decode($param['user_id'], true);
            if (!empty($users)) {
                $db->whereIn('u.id', $users);
            }
        }
        // 公司
        if (!empty($param['corp_id'])) {
            $corps = json_decode($param['corp_id'], true);
            if (!empty($corps)) {
                $db->whereIn('ui.corp_id', $corps);
            }
        }
        // 部门
        if (!empty($param['dep_id'])) {
            $departments = json_decode($param['dep_id'], true);
            if (!empty($departments)) {
                $db->whereIn('wmain_department', $departments);
            }
        }
        // 员工状态
        if (!empty($param['user_status'])) {
            $user_status = json_decode($param['user_status'], true);
            if (!empty($user_status)) {
                $db->whereIn('ui.user_status', $user_status);
            }
        }
        // 员工类型
        if (!empty($param['user_type'])) {
            $user_type = json_decode($param['user_type'], true);
            if (!empty($user_type)) {
                $db->whereIn('ui.user_type', $user_type);
            }
        }
        // 工资状态
        if (!empty($param['salary_status'])) {
            $salary_status = json_decode($param['salary_status'], true);
            if (!empty($salary_status)) {
                if (in_array('0', $salary_status)) {
                    $db->andWhere('and (ui.salary_status is null or ui.salary_status in (:salary_status))', ['salary_status' => implode(',', $salary_status)]);
                } else {
                    $db->whereIn('ui.salary_status', $salary_status);
                }
            }
        }

        // 归属地
        if (!empty($param['work_place'])) {
            $work_place = json_decode($param['work_place'], true);
            if (!empty($work_place)) {
                $db->whereIn('ui.work_place', $work_place);
            }
        }

        $data = $db->pages($page, $limit);
        if (empty($data['list'])) returnSuccess($data);

        $user_ids = array_column($data['list'], 'qwuser_id');

        // 用户薪资信息
        $user_salary = $sdb->table('user_salary')
            ->where('where is_delete = 0')
            ->whereIn('qwuser_id', $user_ids)
            ->order('effective_date DESC')
            ->list();
        $user_salary_map = [];
        $trial_period_salary = []; // 试用期薪资
        $regularization_salary = []; // 转正薪资

        // 如果员工没有转正，使用定薪时转正后工资
        $salary_change_record = $sdb->table('user_salary_change_record')
            ->where('where is_delete = 0 and type = 1')
            ->whereIn('qwuser_id', $user_ids)
            ->order('effective_date DESC')
            ->list();
        foreach ($salary_change_record as $item) {
            $detail = json_decode($item['detail'], true);
            $salary = $detail['salary'];
            $total_salary = 0;
            foreach ($salary as $key => $value) {
                $total_salary += $value;
            }
            $salary['total_salary'] = $total_salary;
            if (isset($regularization_salary[$item['qwuser_id']])) continue;
            $regularization_salary[$item['qwuser_id']] = $salary;
        }
        // 如果员工已经转正，使用转正类型的薪资
        foreach ($user_salary as $item) {
            $salary = json_decode($item['salary'], true);
            $total_salary = 0;
            foreach ($salary as $key => $value) {
                $total_salary += $value;
            }
            $salary['total_salary'] = $total_salary;
            if ($item['type'] == 1) { // 试用期定薪
                if (!isset($trial_period_salary[$item['qwuser_id']])) {
                    $trial_period_salary[$item['qwuser_id']] = $salary;
                }
            } elseif ($item['type'] == 2) { // 转正
                if (!isset($regularization_salary[$item['qwuser_id']])) {
                    $regularization_salary[$item['qwuser_id']] = $salary;
                }
            }

            if (strtotime($item['effective_date']) > strtotime(date('Y-m-d'))) continue;
            if (isset($user_salary_map[$item['qwuser_id']])) continue;
            $user_salary_map[$item['qwuser_id']] = $salary;
        }

        // 用户待审批的薪资审批记录
        $salary_change_record = $sdb->table('user_salary_change_record')
            ->where('where is_delete = 0 and audit_status = 0 and type = 3')
            ->whereIn('qwuser_id', $user_ids)
            ->order('effective_date DESC')
            ->list();
        foreach ($salary_change_record as &$item) {
            $item['salary'] = json_decode($item['salary'], true);
            $item['audit_attach'] = json_decode($item['audit_attach'], true);
        }
        $user_salary_change_record = array_column($salary_change_record, 'id', 'qwuser_id');

        // 部门信息
        $department = redisCached::getDepartment();
        $department = array_column($department, 'name', 'wp_id');

        // 公司信息
        $corps = redisCached::getCorp();
        $corps = array_column($corps, null, 'id');

        foreach ($data['list'] as &$item) {
            $item['social_insurance'] = json_decode($item['social_insurance'], true);
            $item['housing_fund'] = json_decode($item['housing_fund'], true);

            $item['user_salary'] = $user_salary_map[$item['user_id']] ?? null;
            $item['trial_period_salary'] = $trial_period_salary[$item['user_id']] ?? null;
            $item['regularization_salary'] = $regularization_salary[$item['user_id']] ?? null;
            $item['corp_name'] = $corps[$item['corp_id']]['name'] ?? '';
            $start = new DateTime($item['hire_date']);
            $end = new DateTime(date('Y-m-d'));
            $diff = $start->diff($end);
            $item['corp_in_age'] = [
                'year'  => $diff->y,
                'month' => $diff->m,
                'day'   => $diff->d,
            ];
            $item['user_main_department_name'] = $department[$item['wmain_department']] ?? '';

            // 薪资审批记录
            $item['change_id'] = $user_salary_change_record[$item['user_id']] ?? null;
        }
        returnSuccess($data);
    }

    // 详情
    public function getDetail()
    {
        $paras_list = array('id', 'user_id');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $id = $param['id'];
        $user_id = $param['user_id'];
        if (empty($id) || empty($user_id)) {
            returnError('参数错误');
        }
        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();

        $user = $db->table('qwuser', 'u')
            ->field('u.id as user_id,u.wid as user_wid, u.wname as user_name, u.wphone as user_wphone, u.wdepartment_ids as user_wdepartment_ids,u.wmain_department, u.position as user_position, ui.*')
            ->leftJoin('user_info', 'ui', 'u.id=ui.qwuser_id')
            ->where('where ui.id = :id and u.id = :user_id', ['id' => $id, 'user_id' => $user_id])
            ->one();

        $user['social_insurance'] = json_decode($user['social_insurance'], true);
        $user['housing_fund'] = json_decode($user['housing_fund'], true);

        // 公司信息
        $corps = $sdb->table('corp')->list();
        $corps = array_column($corps, null, 'id');

        // 部门信息
        $department = $db->table('qwdepartment')->field('wp_id, name')->list();
        $department = array_column($department, 'name', 'wp_id');

        $user['corp_name'] = $corps[$user['corp_id']]['name'] ?? '';
        $start = new DateTime($user['hire_date']);
        $end = new DateTime(date('Y-m-d'));
        $diff = $start->diff($end);
        $user['corp_in_age'] = [
            'year'  => $diff->y,
            'month' => $diff->m,
            'day'   => $diff->d,
        ];

        // 用户薪资
        // 如果员工没有转正，使用定薪时转正后工资
        $salary_change_record = $sdb->table('user_salary_change_record')
            ->where('where is_delete = 0 and type = 1 and qwuser_id = :qwuser_id', ['qwuser_id' => $user_id])
            ->order('effective_date DESC')
            ->list();
        foreach ($salary_change_record as $item) {
            $detail = json_decode($item['detail'], true);
            $salary = $detail['salary'];
            $total_salary = 0;
            foreach ($salary as $key => $value) {
                $total_salary += $value;
            }
            $salary['total_salary'] = $total_salary;
            $user['regularization_salary'] = $salary;
        }
        // 用户薪资信息
        $salary = $sdb->table('user_salary')
            ->where('where is_delete = 0 and qwuser_id = :qwuser_id', ['qwuser_id' => $user_id])
            ->field('salary, effective_date, type')
            ->order('effective_date DESC')
            ->list();

        foreach ($salary as &$item) {
            $item['salary'] = json_decode($item['salary'], true);
            $total_salary = 0;
            foreach ($item['salary'] as $key => $value) {
                $total_salary += $value;
            }
            $item['salary']['total_salary'] = $total_salary;
            if ($item['type'] == 1) { // 试用期定薪
                $user['trial_period_salary'] = $item['salary'];
            } elseif ($item['type'] == 2) { // 转正
                $user['regularization_salary'] = $item['salary'];
            }
        }

        $user['user_salary'] = $salary ?: [];
        $user['today'] = date('Y-m-d');

        $audit_config = auditForm::getAuditConfig('adjust');
        $user['audit_config'] = $audit_config;
        $user['user_main_department_name'] = $department[$user['wmain_department']] ?? '';

        if (empty($user)) {
            returnError('用户不存在');
        }
        returnSuccess($user);
    }


    // 定薪
    public function init_salary()
    {
        $paras_list = array('user_id', 'work_place', 'project', 'rd_project', 'corp_id', 'name', 'id_number', 'user_type', 'hire_date', 'probation', 'probation_unit', 'regularization_date',
            'bank_card_owner', 'bank_card', 'bank_name', 'trial_period_salary', 'salary', 'social_insurance', 'housing_fund', 'remark');
        $request_list = ['user_id' => 'ID'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $id = $param['user_id'];
        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();
        $user = $db->table('qwuser')->where('where id = :id', ['id' => $id])->one();
        if (empty($user)) {
            returnError('用户不存在');
        }

        // 查询用户是否已经存在信息
        $user_info = $db->table('user_info')->where('where qwuser_id = :qwuser_id', ['qwuser_id' => $param['user_id']])->one();
        if ($user_info && $user_info['salary_status']) {
            returnError('用户已有薪资信息');
        }

        userInfoModel::checkInitSalary($param);

        $user_status = 1;//在职
        if ($param['regularization_date'] && strtotime($param['regularization_date']) > strtotime(date('Y-m-d'))) {
            $user_status = 2;//试用期
        }

        $data = [
            'qwuser_id'        => $param['user_id'],
            'wid'              => $user['wid'],
            'id_number'        => $param['id_number'],
            'name'             => $param['name'],
            'wname'            => $user['wname'],
            'salary_status'    => 1,
            'work_place'       => $param['work_place'],
            'project'          => $param['project'],
            'rd_project'       => $param['rd_project'],
            'corp_id'          => $param['corp_id'],
            'user_type'        => $param['user_type'],
            'user_status'      => $user_status,
            'probation'        => $param['probation'] ?? '',
            'probation_unit'   => $param['probation_unit'] ?? '',
            'hire_date'        => $param['hire_date'],
            'bank_card_owner'  => $param['bank_card_owner'],
            'bank_card'        => $param['bank_card'],
            'bank_name'        => $param['bank_name'],
            'social_insurance' => $param['social_insurance'],
            'housing_fund'     => $param['housing_fund'],
            'remark'           => $param['remark'],
            'operator'         => userModel::$qwuser_id,
        ];

        if ($param['regularization_date']) {
            $data['regularization_date'] = $param['regularization_date'];
        }

        if ($user_info) {
            $db->table('user_info')->where('where id = :id', ['id' => $user_info['id']])->update($data);
        } else {
            $db->table('user_info')->insert($data);
        }

        // 插入薪资信息
        $salary_id = $sdb->table('user_salary')->insert([
            'qwuser_id'      => $param['user_id'],
            'salary'         => $param['trial_period_salary'],
            'effective_date' => $param['hire_date'],
            'operator'       => userModel::$qwuser_id,
            'type'           => 1, // 定薪
        ]);

        if ($param['regularization_date'] && strtotime($param['regularization_date']) < strtotime(date('Y-m-d'))) {
            // 插入薪资信息
            $salary_id = $sdb->table('user_salary')->insert([
                'qwuser_id'      => $param['user_id'],
                'salary'         => $param['salary'],
                'effective_date' => $param['regularization_date'],
                'operator'       => userModel::$qwuser_id,
                'type'           => 2, // 转正
            ]);
        }

        // 插入薪资调整记录
        $trial_period_salary = json_decode($param['trial_period_salary'], true) ?? null;
        $salary = json_decode($param['salary'], true) ?? null;
        $sdb->table('user_salary_change_record')->insert([
            'qwuser_id'      => $param['user_id'],
            'salary_id'      => $salary_id,
            'salary'         => $param['trial_period_salary'],
            'detail'         => json_encode([
                'trial_period_salary' => $trial_period_salary,
                'salary'              => $salary,
            ], JSON_UNESCAPED_UNICODE),
            'effective_date' => $param['hire_date'],
            'audit_status'   => 1,
            'operator'       => userModel::$qwuser_id,
            'type'           => 1, // 定薪
        ]);

        returnSuccess([], '定薪成功');
    }

    // 编辑
    public function edit()
    {
        $paras_list = array('id', 'user_id', 'id_number', 'name', 'user_type', 'work_place', 'project', 'rd_project', 'leave_date', 'dimission_allowance',
            'bank_card_owner', 'bank_card', 'bank_name', 'social_insurance', 'housing_fund');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        $id = $param['id'];
        if (empty($id)) returnError('参数错误');
        $user_id = $param['user_id'];
        $db = dbMysql::getInstance();
        $user = $db->table('qwuser')->where('where id = :id', ['id' => $user_id])->one();
        empty($user) && returnError('用户不存在');
        $user_info = $db->table('user_info')->where('where id = :id', ['id' => $id])->one();
        empty($user_info) && returnError('用户信息不存在');

        if (isset($param['leave_date']) && !empty($param['leave_date'])) {
            (strtotime($param['leave_date']) < strtotime($user_info['hire_date'])) && returnError('离职日期不能小于入职日期');
        }

        $data = [
            'id_number'        => $param['id_number'],
            'name'             => $param['name'],
            'user_type'        => $param['user_type'],
            'work_place'       => $param['work_place'],
            'project'          => $param['project'],
            'rd_project'       => $param['rd_project'],
//            'regularization_date' => $param['regularization_date'],
            'bank_card_owner'  => $param['bank_card_owner'],
            'bank_card'        => $param['bank_card'],
            'bank_name'        => $param['bank_name'],
            'social_insurance' => $param['social_insurance'],
            'housing_fund'     => $param['housing_fund'],
        ];
        if (isset($param['leave_date']) && !empty($param['leave_date'])) {
            $data['leave_date'] = $param['leave_date'];
            $data['dimission_allowance'] = $param['dimission_allowance'] ?? 0;
        }
        $db->table('user_info')->where('where id = :id', ['id' => $id])->update($data);
        returnSuccess([], '编辑成功');
    }

    // 调薪
    public function change_salary_check()
    {
        $paras_list = array('user_id', 'change_reason', 'effective_date', 'change_desc', 'salary');
        $request_list = ['user_id' => '用户ID'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $user_id = $param['user_id'];
        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();
        $user = $db->table('qwuser')->where('where id = :id', ['id' => $user_id])->one();
        empty($user) && returnError('用户不存在');
        $user_info = $db->table('user_info')->where('where qwuser_id = :qwuser_id', ['qwuser_id' => $user_id])->one();
        empty($user_info) && returnError('用户信息不存在');

        $user_info['salary_status'] == 0 && returnError('待定薪员工不能调薪');
        $user_info['salary_status'] == 2 && returnError('已有在审批中的调薪记录');

        $audit_config = auditForm::getAuditConfig('adjust');
        if (empty($audit_config) || empty($audit_config['rule'])) {
            returnError('调薪审批流程未配置');
        }

        // 查询未生效但审批通过的调薪记录
        $salary_change_record = $sdb->table('user_salary_change_record')
            ->where('where is_delete = 0 and audit_status = 1 and type = 3 and qwuser_id = :qwuser_id and effective_date > :date', ['qwuser_id' => $user_id, 'date' => date('Y-m-d')])
            ->order('effective_date DESC')
            ->one();
        returnSuccess(['status' => empty($salary_change_record) ? 1 : 0]);
    }

    // 调薪
    public function change_salary()
    {
        $paras_list = array('user_id', 'change_reason', 'effective_date', 'change_desc', 'salary');
        $request_list = ['user_id' => '用户ID'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $user_id = $param['user_id'];
        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();
        $user = $db->table('qwuser')->where('where id = :id', ['id' => $user_id])->one();
        empty($user) && returnError('用户不存在');
        $user_info = $db->table('user_info')->where('where qwuser_id = :qwuser_id', ['qwuser_id' => $user_id])->one();
        empty($user_info) && returnError('用户信息不存在');

        $user_info['salary_status'] == 0 && returnError('待定薪员工不能调薪');
        $user_info['salary_status'] == 2 && returnError('已有在审批中的调薪记录');

        $audit_config = auditForm::getAuditConfig('adjust');
        if (empty($audit_config) || empty($audit_config['rule'])) {
            returnError('调薪审批流程未配置');
        }

        // 查询未生效但审批通过的调薪记录
        $salary_change_record = $sdb->table('user_salary_change_record')
            ->where('where is_delete = 0 and audit_status = 1 and type = 3 and qwuser_id = :qwuser_id and effective_date > :date', ['qwuser_id' => $user_id, 'date' => date('Y-m-d')])
            ->order('effective_date DESC')
            ->one();

        // 薪资删除
        $sdb->table('user_salary')->where('where id = :id', ['id' => $salary_change_record['salary_id']])->update([
            'is_delete' => 1,
            'operator'  => userModel::$qwuser_id,
        ]);
        // 薪资记录作废标记
        $sdb->table('user_salary_change_record')->where('where id = :id', ['id' => $salary_change_record['id']])->update([
            'is_cancel' => 1,
            'operator'  => userModel::$qwuser_id,
        ]);

        $next_node = auditForm::getNextNode($audit_config['rule']);

        $record = [
            'qwuser_id'      => $user_id,
            'change_reason'  => $param['change_reason'],
            'effective_date' => $param['effective_date'],
            'change_desc'    => $param['change_desc'],
            'salary'         => $param['salary'],
            'detail'         => json_encode([
                'now_salary'   => userSalaryModel::getUserSalary($user_id),
                'after_salary' => json_decode($param['salary'], true),
            ], JSON_UNESCAPED_UNICODE),
            'operator'       => userModel::$qwuser_id,
            'type'           => 3, // 调薪
            'audit_status'   => 0,
            'audit_attach'   => json_encode([
                'config'     => $audit_config,
                'node'       => $next_node['node'],
                'audit_user' => $next_node['audit_user'],
            ], JSON_UNESCAPED_UNICODE)
        ];
        $id = $sdb->table('user_salary_change_record')->insert($record);

        // 用户状态改为待审批
        $db->table('user_info')->where('where qwuser_id = :qwuser_id', ['qwuser_id' => $user_id])->update(['salary_status' => 2]);

        if (!empty($audit_config['start_cc'])) {
            $user_ids = array_column($audit_config['start_cc'], 'wid');
            messagesFrom::senMeg($user_ids, 1, "调薪审批", $id, '用户调薪');
        }

        returnSuccess([], '调薪成功');
    }

    // 薪资导入
    public function import_salary()
    {
        $paras_list = array('excel_src');
        $request_list = ['excel_src' => '表格链接'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $excel_url = SELF_FK . $param['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格不存在');
        }

        $excel_data = (new FastExcel)->import($excel_url);
        $data = $excel_data->toArray();
        if (!count($data)) {
            returnError('表格内不存在数据');
        }

        // 表头
        $first_user = $data[0];
        if (empty($first_user['姓名']) || empty($first_user['调薪日期']) || !isset($first_user['基本工资']) || !isset($first_user['岗位工资']) || !isset($first_user['保密工资']) ||
            !isset($first_user['福利工资']) || !isset($first_user['基准绩效']) || !isset($first_user['日薪']) || !isset($first_user['备注'])) {
            returnError('表头错误');
        }

        $user_names = array_column($data, '姓名');
        $user_names = array_values(array_unique(array_filter($user_names)));
        $users = dbMysql::getInstance()->table('qwuser')->field('id,wname')->whereIn('wname', $user_names)->list();
        $users = array_column($users, 'id', 'wname');

        $error_data = [];
        $res_data = [];
        $user_salary = [];
        $salary_log = [];

        foreach ($data as $item) {
            $salary = [];
            $is_error = false;
            $error_msg = [];
            if (empty($item['姓名'])) {
                $error_msg [] = '用户姓名不能为空';
                $is_error = true;
            }
            if (!isset($users[$item['姓名']])) {
                $error_msg [] = '用户不存在';
                $is_error = true;
            }
            try {
                $item['调薪日期'] = $item['调薪日期']->format('Y-m-d');
                if (empty($item['调薪日期']) || strtotime($item['调薪日期']) === false) {
                    $error_msg [] = '调薪日期格式错误';
                    $is_error = true;
                }
            } catch (Throwable $e) {
                $error_msg [] = '调薪日期格式错误';
                $is_error = true;
            }

            // 日薪
            if (!empty($item['日薪'])) {
                if (!is_numeric($item['日薪'])) {
                    $error_msg [] = '日薪格式错误';
                    $is_error = true;
                }
                $salary['day_salary'] = $item['日薪'] ?? 0;
            } else { // 普通员工，基本工资必填
                if (empty($item['基本工资']) || !is_numeric($item['基本工资'])) {
                    $error_msg [] = '基本工资格式错误';
                    $is_error = true;
                }
                $salary['base_salary'] = $item['基本工资'] ?? 0;
                $salary['position_salary'] = $item['岗位工资'] ?? 0;
                $salary['secret_salary'] = $item['保密工资'] ?? 0;
                $salary['welfare_salary'] = $item['福利工资'] ?? 0;
                $salary['performance_salary'] = $item['基准绩效'] ?? 0;
            }

            if ($is_error) {
                $error_data[] = array_merge($item, ['失败原因' => implode(',', $error_msg)]);
                continue;
            }

            $user_salary[] = [
                'qwuser_id'      => $users[$item['姓名']],
                'salary'         => $salary,
                'effective_date' => $item['调薪日期'],
                'operator'       => userModel::$qwuser_id,
                'type'           => 4, // 导入
                'remark'         => $item['备注'] ?? '',
            ];
            $res_data[] = $item;
        }

        if ($user_salary) {
            $sdb = dbSMysql::getInstance();
            foreach ($user_salary as $item) {
                $now_salary = userSalaryModel::getUserSalary($item['qwuser_id']);
                $remark = $item['remark'];
                unset($item['remark']);
                $salary = $item['salary'];
                $item['salary'] = json_encode($salary, JSON_UNESCAPED_UNICODE);
                $salary_id = $sdb->table('user_salary')->insert($item);
                $salary_log = [
                    'salary_id'      => $salary_id,
                    'qwuser_id'      => $item['qwuser_id'],
                    'salary'         => $item['salary'],
                    'detail'         => json_encode([
                        'now_salary'   => $now_salary,
                        'after_salary' => $salary,
                    ], JSON_UNESCAPED_UNICODE),
                    'effective_date' => $item['effective_date'],
                    'type'           => 4, // 导入
                    'change_desc'    => $remark,
                    'operator'       => userModel::$qwuser_id,
                ];
                $sdb->table('user_salary_change_record')->insert($salary_log);
            }
        }

        if (count($error_data) > 0) {
            // 使用项目根目录动态构建导出文件路径
            $src = '/public/salary/temp/error/user_salary_import_errors' . time() . rand(100, 1000) . '.xlsx';
            $exportPath = SELF_FK . $src;

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径

            returnSuccess(['error_file' => $src, 'error_count' => count($error_data), 'success_count' => count($res_data), 'data' => $error_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => count($error_data), 'success_count' => count($res_data), 'data' => $res_data], '导入成功');
        }

    }

    // 调薪记录
    public function get_salary_log()
    {
        $paras_list = array('user_id');
        $request_list = ['user_id' => '用户ID'];
        $param = arrangeParam($_GET, $paras_list, $request_list);
        $user_id = $param['user_id'];
        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();
        $user = $db->table('qwuser')->where('where id = :id', ['id' => $user_id])->one();
        empty($user) && returnError('用户不存在');

        // 用户薪资信息
        $salary = $sdb->table('user_salary_change_record')
            ->where('where is_delete = 0 and qwuser_id = :qwuser_id', ['qwuser_id' => $user_id])
            ->order('create_time DESC')
            ->list();

        $user_ids = array_column($salary, 'operator');
        $users = $db->table('qwuser')->field('id,wname')->whereIn('id', $user_ids)->list();
        $users = array_column($users, 'wname', 'id');

        foreach ($salary as &$item) {
            $item['salary'] = json_decode($item['salary'], true);
            $item['audit_attach'] = json_decode($item['audit_attach'], true);
            $item['detail'] = json_decode($item['detail'], true);
            $item['operator_name'] = $users[$item['operator']];
        }

        returnSuccess(['user_salary' => $salary ?: []]);
    }

    // 薪资作废
    public function cancel_salary()
    {
        $paras_list = array('id');
        $param = arrangeParam($_POST, $paras_list);
        $id = $param['id'];
        if (!$id) returnError('id不能为空');

        $sdb = dbSMysql::getInstance();
        $sdb->table('user_salary_change_record');
        $sdb->where('where id = :id', ['id' => $id]);
        $detail = $sdb->one();
        returnError($detail);
        if (!$detail) returnError('薪资记录不存在');

        if (!in_array($detail['type'], [3, 4])) returnError('该条薪资不能作废');
        if (!$detail['salary_id']) returnError('薪资记录不存在');

        $sdb->table('user_salary');
        $sdb->where('where id = :id', ['id' => $detail['salary_id']]);
        $salary = $sdb->one();
        if (!$salary) returnError('薪资记录不存在');

        // 薪资删除
        $sdb->table('user_salary')->where('where id = :id', ['id' => $detail['salary_id']])->update([
            'is_delete' => 1,
            'operator'  => userModel::$qwuser_id,
        ]);
        // 薪资记录作废标记
        $sdb->table('user_salary_change_record')->where('where id = :id', ['id' => $id])->update([
            'is_cancel' => 1,
            'operator'  => userModel::$qwuser_id,
        ]);
        returnSuccess([], '作废成功');
    }

    // 待审批列表
    public function audit_list()
    {
        $paras_list = array('ids');
        $param = arrangeParam($_POST, $paras_list);
        $ids = json_decode($param['ids'], true);
        if (empty($ids)) {
            returnError('参数错误');
        }

        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();
        // 调薪任务
        $sdb->table('user_salary_change_record');
        $records = $sdb->whereIn('id', $ids)->list();

        $user_ids = array_column($records, 'qwuser_id');
        // 获取用户薪资
        $salary = $sdb->table('user_salary')
            ->where('where is_delete = 0')
            ->whereIn('qwuser_id', $user_ids)
            ->order('effective_date DESC')
            ->list();
        $user_salary = [];
        foreach ($salary as $item) {
            $salary = json_decode($item['salary'], true);
            $user_salary[$item['qwuser_id']][] = $salary;
        }

        // 用户信息
        $user = $db->table('qwuser')->field('id, wname')->list();
        $user = array_column($user, null, 'id');

        foreach ($records as &$item) {
            $item['user_name'] = $user[$item['qwuser_id']]['wname'] ?? '';
            $item['detail'] = json_decode($item['detail'], true);
            $item['salary'] = json_decode($item['salary'], true);
            $item['audit_attach'] = json_decode($item['audit_attach'], true);
            $effective_date = $item['effective_date'];
            // 找出该条调薪审批前最近的一条调薪
            $item['last_salary'] = null;
            foreach ($user_salary[$item['qwuser_id']] as $salary) {
                if (strtotime($salary['effective_date']) > strtotime($effective_date)) continue;
                if ($item['last_salary'] == null || strtotime($salary['effective_date']) > strtotime($item['last_salary']['effective_date'])) {
                    $item['last_salary'] = $salary;
                }
            }
        }

        returnSuccess($records);

    }

    // 审批
    public function approval()
    {
        $paras_list = array('data');
        $param = arrangeParam($_POST, $paras_list);
        $param['data'] = json_decode($param['data'], true);
        if (empty($param['data'])) {
            returnError('参数错误');
        }

        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();
        // 调薪任务
        $sdb->table('user_salary_change_record');
        $records = $sdb->whereIn('id', array_column($param['data'], 'id'))->list();
        $records = array_column($records, null, 'id');
        if (empty($records)) {
            returnError('调薪记录不存在');
        }

        $salary_change_reason = config::get('salary_change_reason', 'data_salary');
        $salary_change_reason_map = array_column($salary_change_reason, 'type', 'id');

        foreach ($param['data'] as $item) {
            $need_change = false;
            $update_data = [];
            $record = $records[$item['id']];
            $record['audit_attach'] = json_decode($record['audit_attach'], true);
            if ($record['audit_status'] != 0) continue; // 非待审批状态
            if ($item['audit'] == 0) {// 审批拒绝
                $need_change = true;
                $record['audit_status'] = 2;
                $node = $record['audit_attach']['node'];
                $record['audit_attach']['config']['rule'][$node - 1]['audit_list'][] = [
                    'user_id'    => userModel::$qwuser_id,
                    'audit_desc' => $item['audit_desc'],
                ];
                $update_data = [
                    'audit_status' => 2,
                    'audit_attach' => json_encode($record['audit_attach'], JSON_UNESCAPED_UNICODE),
                ];
            } else { // 审批通过
                $node = $record['audit_attach']['node'];
                $record['audit_attach']['config']['rule'][$node - 1]['audit_list'][] = [
                    'user_id'    => userModel::$qwuser_id,
                    'audit_desc' => $item['audit_desc'],
                ];
                $next_node = auditForm::getNextNode($record['audit_attach']['config']['rule'], $record['audit_attach']['node'], $record['audit_attach']['audit_user']);

                if (empty($next_node)) {
                    $need_change = true;
                    $record['audit_status'] = 1;
                    $update_data = [
                        'audit_status' => 1,
                        'audit_attach' => json_encode($record['audit_attach'], JSON_UNESCAPED_UNICODE),
                    ];
                } else {
                    $record['audit_attach']['node'] = $next_node['node'];
                    $record['audit_attach']['audit_user'] = $next_node['audit_user'];
                    $update_data = [
                        'audit_attach' => json_encode($record['audit_attach'], JSON_UNESCAPED_UNICODE),
                    ];
                }
            }
            // 审批通过后更新薪资
            if ($need_change) {
                $salary_id = $sdb->table('user_salary')->insert([
                    'qwuser_id'      => $record['qwuser_id'],
                    'salary'         => $record['salary'],
                    'effective_date' => $record['effective_date'],
                    'operator'       => userModel::$qwuser_id,
                    'type'           => 3, // 调薪
                ]);
                $update_data['salary_id'] = $salary_id;
                $db->table('user_info')->where('where qwuser_id = :qwuser_id', ['qwuser_id' => $record['qwuser_id']])->update(['salary_status' => 1]);

                if (!empty($record['audit_attach']['config']['rule']['end_cc'])) {
                    $user_ids = array_column($record['audit_attach']['config']['rule']['start_cc'], 'wid');
                    messagesFrom::senMeg($user_ids, 1, "调薪审批通过", $record['id'], '用户调薪通过');
                }

            }
            if (!empty($update_data)) {
                $sdb->table('user_salary_change_record')->where('where id = :id', ['id' => $item['id']])->update($update_data);
            }

            // 需要改变员工状态
            if ($salary_change_reason_map[$record['change_reason']] == 1) {
                $adb = dbAMysql::getInstance();
                $adb->table('custom_crontab');
                $adb->insert([
                    'link_type'   => 4,
                    'link_id'     => $record['id'],
                    'link_module' => customCrontabModel::MODULE_SALARY,
                    'runtime'     => $param['effective_date'],
                    'status'      => -1
                ]);
            }

        }
        returnSuccess([], '审批成功');
    }

    // 用户信息导出
    public function export_user_info()
    {
        $excel_data = [];

        $db = dbMysql::getInstance();
        $users = $db->table('qwuser', 'u')
            ->field('u.id as user_id,u.wid as user_wid, u.wname as user_name, u.wphone as user_wphone, u.wdepartment_ids as user_wdepartment_ids,u.wmain_department, u.position as user_position, ui.*')
            ->leftJoin('user_info', 'ui', 'u.id=ui.qwuser_id')
            ->list();

        $db = dbMysql::getInstance();
        $department = $db->table('qwdepartment')->list();
        $department = array_column($department, 'name', 'id');

        foreach ($users as $user) {
            if ($user['id']) continue;//已定薪
            $excel_data[] = [
                'ID'               => $user['user_id'],
                '姓名'             => $user['user_name'],
                '部门'             => $department[$user['wmain_department']] ?? '',
                '岗位'             => $user['user_position'],
                '归属地'           => '',
                '公司'             => '',
                '身份证姓名'       => '',
                '证件号码'         => '',
                '员工类型'         => '',
                '项目'             => '',
                '研发项目'         => '',
                '入职时间'         => '',
                '试用期'           => '',
                '试用期时间单位'   => '',
                '转正时间'         => '',
                '开户人姓名'       => '',
                '银行卡号'         => '',
                '开户行'         => '',
                '基本工资(试用期）' => '',
                '岗位工资(试用期）' => '',
                '保密工资(试用期）' => '',
                '福利工资(试用期）' => '',
                '基准绩效(试用期）' => '',
                '基本工资(转正）'   => '',
                '岗位工资(转正）'   => '',
                '保密工资(转正）'   => '',
                '福利工资(转正）'   => '',
                '基准绩效(转正）'   => '',
                '日薪'             => '',
                '社保'             => '',
                '发放社保补贴'     => '',
                '公积金'           => '',
                '发放公积金补贴'   => '',
                '备注'             => '',
            ];
        }
        // 准备导出数据
        // 使用项目根目录动态构建导出文件路径

        $path = '/public/salary/temp/user_info_data' . date('YmdHis') . rand(1, 1000) . '.xlsx';
        $exportPath = SELF_FK . $path;

        // 检查并创建文件夹
        $exportDir = dirname($exportPath);
        if (!file_exists($exportDir)) {
            mkdir($exportDir, 0777, true);
        }

        // 使用 FastExcel 导出数据
        (new FastExcel($excel_data))->export($exportPath);

        // 返回导出文件路径
        returnSuccess(['path' => $path]);


    }

    // 用户信息导入
    public function import_user_info()
    {
        $paras_list = array('excel_src');
        $request_list = ['excel_src' => '表格链接'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $excel_url = SELF_FK . $param['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格不存在');
        }

        $excel_data = (new FastExcel)->import($excel_url);
        $data = $excel_data->toArray();
        if (!count($data)) {
            returnError('表格内不存在数据');
        }

        $db = dbMysql::getInstance();
        $users = $db->table('qwuser', 'u')
            ->field('u.id as user_id,u.wid as user_wid, u.wname as user_name, u.wphone as user_wphone, u.wdepartment_ids as user_wdepartment_ids,u.wmain_department, u.position as user_position, ui.*')
            ->leftJoin('user_info', 'ui', 'u.id=ui.qwuser_id')
            ->list();
        $users = array_column($users, null, 'user_id');

        $work_place = config::get('work_place', 'data_salary');
        $work_place = array_column($work_place, 'id', 'name');

        $user_type = config::get('user_type', 'data_salary');
        $user_type = array_column($user_type, 'id', 'name');

        // 公司信息
        $sdb = dbSMysql::getInstance();
        $corps = $sdb->table('corp')->list();
        $corps = array_column($corps, 'id', 'name');

        $error_data = [];
        $res_data = [];

        foreach ($data as $item) {
            $error_msg = [];
            if (empty($item['ID'])) {
                $error_msg[] = '用户ID不能为空';
            }
            $user = $users[$item['ID']] ?? [];
            if (empty($user)) {
                $error_msg[] = '找不到用户';
            }

            if (isset($user['id'])) {
                $error_msg[] = '该用户已定薪';
            }

            foreach (['ID', '归属地', '公司', '身份证姓名', '证件号码', '员工类型', '项目', '入职时间', '开户人姓名', '银行卡号', '开户行', '社保', '公积金'] as $field) {
                if (empty($item[$field])) {
                    $error_msg[] = "{$field}缺失";
                }
            }

            try {
                $hire_date = $item['入职时间']->format('Y-m-d');
                if (empty($item['入职时间']) || strtotime($hire_date) === false) {
                    $error_msg[] = '入职时间格式错误';
                }
            } catch (Throwable $e) {
                $error_msg[] = '入职时间格式错误';
            }

            $user_status = 1;//在职

            $trial_period_salary = [];
            $salary = [];
            if (in_array($item['员工类型'], [2, 3])) {
                if (!empty($item['日薪']) || !is_numeric($item['日薪'])) {
                    $error_msg [] = '日薪格式错误';
                }
                $trial_period_salary['day_salary'] = $item['实习日薪'] ?? 0;
            } else { // 普通员工，基本工资必填
                foreach (['试用期', '试用期时间单位', '转正时间'] as $field) {
                    if (empty($item[$field])) {
                        $error_msg[] = "{$field}缺失";
                    }
                }

                try {
                    $regularization_date = $item['转正时间']->format('Y-m-d');
                    if (empty($item['转正时间']) || strtotime($regularization_date) === false) {
                        $error_msg[] = '转正时间格式错误';
                    }
                    if ($regularization_date && strtotime($regularization_date) > strtotime(date('Y-m-d'))) {
                        $user_status = 2;//试用期
                    }
                } catch (Throwable $e) {
                    $error_msg[] = '转正时间格式错误';
                }

                if (empty($item['基本工资(试用期）']) || !is_numeric($item['基本工资(试用期）'])) {
                    $error_msg [] = '基本工资(试用期）必填';
                }
                $trial_period_salary['base_salary'] = $item['基本工资(试用期）'] ?? 0;
                $trial_period_salary['position_salary'] = $item['岗位工资(试用期）'] ?? 0;
                $trial_period_salary['secret_salary'] = $item['保密工资(试用期）'] ?? 0;
                $trial_period_salary['welfare_salary'] = $item['福利工资(试用期）'] ?? 0;
                $trial_period_salary['performance_salary'] = $item['基准绩效(试用期）'] ?? 0;

                if (empty($item['基本工资(转正）']) || !is_numeric($item['基本工资(转正）'])) {
                    $error_msg [] = '基本工资(转正）必填';
                }
                $salary['base_salary'] = $item['基本工资(转正）'] ?? 0;
                $salary['position_salary'] = $item['岗位工资(转正）'] ?? 0;
                $salary['secret_salary'] = $item['保密工资(转正）'] ?? 0;
                $salary['welfare_salary'] = $item['福利工资(转正）'] ?? 0;
                $salary['performance_salary'] = $item['基准绩效(转正）'] ?? 0;
            }

            if (!empty($error_msg)) {
                $error_data[] = array_merge($item, ['失败原因' => implode(',', $error_msg)]);
                continue;
            }
            $social_insurance = [
                'status' => $item['社保'] == '买' ? 1 : 0
            ];
            $social_insurance['status'] == 0 && $social_insurance['is_allowance'] = $item['发放社保补贴'] == '是' ? 1 : 0;

            $housing_fund = [
                'status' => $item['公积金'] == '买' ? 1 : 0
            ];
            $housing_fund['status'] == 0 && $housing_fund['is_allowance'] = $item['发放公积金补贴'] == '是' ? 1 : 0;


            $data = [
                'qwuser_id'           => $item['ID'],
                'wid'                 => $user['user_wid'],
                'id_number'           => $item['证件号码'],
                'name'                => $item['身份证姓名'],
                'wname'               => $user['user_name'],
                'salary_status'       => 1,
                'work_place'          => $work_place[$item['归属地']],
                'project'             => $item['项目'],
                'rd_project'          => $item['研发项目'] ?? '',
                'corp_id'             => $corps[$item['公司']],
                'user_type'           => $user_type[$item['员工类型']],
                'user_status'         => $user_status,
                'probation'           => $item['试用期'] ?? '',
                'probation_unit'      => $item['试用期时间单位'] == '天' ? 1 : 2,
                'regularization_date' => $regularization_date ?? null,
                'hire_date'           => $hire_date ?? null,
                'bank_card_owner'     => $item['开户人姓名'],
                'bank_card'           => $item['银行卡号'],
                'bank_name'           => $item['开户行'],
                'social_insurance'    => json_encode($social_insurance, JSON_UNESCAPED_UNICODE),
                'housing_fund'        => json_encode($housing_fund, JSON_UNESCAPED_UNICODE),
                'remark'              => $item['备注'],
                'operator'            => userModel::$qwuser_id,
            ];
            $db->table('user_info')->insert($data);

            // 插入薪资信息
            $salary_id = $sdb->table('user_salary')->insert([
                'qwuser_id'      => $item['ID'],
                'salary'         => json_encode($trial_period_salary, JSON_UNESCAPED_UNICODE),
                'effective_date' => $hire_date,
                'operator'       => userModel::$qwuser_id,
                'type'           => 1, // 定薪
            ]);

            if ($regularization_date && strtotime($regularization_date) < strtotime(date('Y-m-d'))) {
                // 插入薪资信息
                $salary_id = $sdb->table('user_salary')->insert([
                    'qwuser_id'      => $item['ID'],
                    'salary'         => json_encode($salary, JSON_UNESCAPED_UNICODE),
                    'effective_date' => $regularization_date,
                    'operator'       => userModel::$qwuser_id,
                    'type'           => 2, // 转正
                ]);
            }

            // 插入薪资调整记录
            $sdb->table('user_salary_change_record')->insert([
                'qwuser_id'      => $item['ID'],
                'salary_id'      => $salary_id,
                'salary'         => json_encode($trial_period_salary, JSON_UNESCAPED_UNICODE),
                'detail'         => json_encode([
                    'trial_period_salary' => $trial_period_salary,
                    'salary'              => $salary,
                ], JSON_UNESCAPED_UNICODE),
                'effective_date' => $hire_date,
                'audit_status'   => 1,
                'operator'       => userModel::$qwuser_id,
                'type'           => 1, // 定薪
            ]);

            $res_data[] = $item;
        }

        if (count($error_data) > 0) {
            // 使用项目根目录动态构建导出文件路径
            $src = '/public/salary/temp/error/user_salary_import_errors' . time() . rand(100, 1000) . '.xlsx';
            $exportPath = SELF_FK . $src;

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径

            returnSuccess(['error_file' => $src, 'error_count' => count($error_data), 'success_count' => count($res_data), 'data' => $error_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => count($error_data), 'success_count' => count($res_data), 'data' => $res_data], '导入成功');
        }

    }


}