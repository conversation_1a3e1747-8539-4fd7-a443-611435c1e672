openapi: 3.0.0
info:
  title: 用户管理API
  version: 1.0.0
  description: 提供用户管理的相关接口
paths:
  /shop/user/getUserInfo:
    get:
      tags:
        - User
      summary: 获取当前用户信息
      description: 获取当前登录用户的详细信息
      responses:
        '200':
          description: 成功返回用户信息
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    $ref: '#/components/schemas/UserInfo'

  /shop/user/getList:
    post:
      tags:
        - User
      summary: 获取用户列表
      description: 根据条件筛选获取用户列表
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                wname:
                  type: string
                  description: 用户姓名
                qw_partment_id:
                  type: string
                  description: 部门ID(多个用逗号分隔)
                wstatus:
                  type: integer
                  description: 用户状态
                roles_id:
                  type: string
                  description: 角色ID列表(JSON格式)
                order_by:
                  type: string
                  description: 排序字段(JSON格式)
                page:
                  type: integer
                  description: 页码
                  default: 1
                page_size:
                  type: integer
                  description: 每页数量
                  default: 10
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/UserList'
                      total:
                        type: integer
                        description: 总记录数
                      page:
                        type: integer
                        description: 当前页码
                      page_size:
                        type: integer
                        description: 每页条数

  /shop/user/updateUserPwd:
    post:
      tags:
        - User
      summary: 修改用户密码
      description: 修改当前用户的登录密码
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - password
              properties:
                password:
                  type: string
                  description: 新密码
      responses:
        '200':
          description: 修改成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 修改成功

  /shop/user/getOftenUsedUser:
    get:
      tags:
        - User
      summary: 获取常用用户列表
      description: 获取当前用户设置的常用联系人列表
      responses:
        '200':
          description: 成功返回常用用户列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/OftenUsedUser'

  /shop/user/setOftenUsedUser:
    post:
      tags:
        - User
      summary: 设置常用用户
      description: 更新当前用户的常用联系人列表
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - qwuser_ids
              properties:
                qwuser_ids:
                  type: string
                  description: 用户ID列表(JSON格式)
      responses:
        '200':
          description: 设置成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 设置成功

components:
  schemas:
    UserInfo:
      type: object
      properties:
        id:
          type: integer
          description: ID
        wid:
          type: string
          description: 企业微信ID
        wname:
          type: string
          description: 用户姓名
        avatar:
          type: string
          description: 头像URL
        is_super:
          type: integer
          description: 是否超级管理员
        auth:
          type: array
          description: 权限列表
        role_type:
          type: integer
          description: 角色类型

    UserList:
      type: object
      properties:
        id:
          type: integer
          description: ID
        wid:
          type: string
          description: 企业微信ID
        wname:
          type: string
          description: 用户姓名
        wphone:
          type: string
          description: 手机号
        wdepartment_ids:
          type: string
          description: 部门ID列表
        wstatus:
          type: integer
          description: 状态
        updated_at:
          type: string
          description: 更新时间
          format: date-time
        is_manage:
          type: integer
          description: 是否管理员
        avatar:
          type: string
          description: 头像URL
        position:
          type: string
          description: 职位

    OftenUsedUser:
      type: object
      properties:
        id:
          type: integer
          description: ID
        wid:
          type: string
          description: 企业微信ID
        wname:
          type: string
          description: 用户姓名
        avatar:
          type: string
          description: 头像URL
        position:
          type: string
          description: 职位
