<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/12 17:47
 */
namespace  plugins\goods\controller;

use core\lib\db\dbMysql;

class formController{
    public function getList() {
        $paras_list = array('form_name', 'page', 'page_size');
        $param = arrangeParam($_GET, $paras_list);

        $db = dbMysql::getInstance();
        $db->table('form');
        $sql_string = 'where is_delete = 0 and type = 0';
        $where_data = [];
        if ($param['form_name']) {
            $sql_string .= ' and form_name like :form_name';
            $where_data['form_name'] = '%'.$param['form_name'].'%';
        }
        $db->where($sql_string, $where_data);
        $db->field('id,form_name,table_name,created_at');
        $list = $db->pages($param['page'], $param['page_size']);
        returnSuccess($list);
    }

    /*
     * 表单内容：
     * title：名称；
     * type: 类型； 0-input
     * is_request: 是否必填：0否，1是
     * 样式
     * {
     *     //第一行
     *     {
     *          {"title":"规格名称","is_request":"1","type":0},
     *          {"title":"KUS","is_request":"1","type":0}
     *     },
     *     //第二行
     *     {
     *          {"title":"规格名称","is_request":"1","type":0},
     *          {"title":"KUS","is_request":"1","type":0}
     *     }
     * }
     *
     * */
    public function addFrom(){
        echo 'wei';die;
        $param = $this->verifyFromData();
        $db = dbMysql::getInstance();
        $db->table('form');
        //表单名称重复验证（只有新增的）
//        $form = $db->query('select id from oa_form where form_name = :form_name and is_delete = 0 limit 1', ['form_name'=>$param['form_name']]);
//        if ($form) {
//            SetReturn(-1,'该表单名称已存在');
//        }
        $data_ = [
            'form_name'=>$param['form_name'],
            'description'=>$param['description'],
            'data'=>json_encode($param['from_data']),
        ];
        if (!$param['id']) {
            //新增
            $data_['created_at'] = date('Y-m-d H:i:s');
            if($db->insert($data_)) {
                SetReturn(0,'保存成功');
            } else {
                SetReturn(-1,'保存失败');
            }
        } else {
            $db->where(' where id = :id', ['id'=>$param['id']]);
            //修改
            $data_['updated_at'] = date('Y-m-d H:i:s');
            if($db->update($data_)) {
                SetReturn(0,'修改成功');
            } else {
                SetReturn(-1,'修改失败');
            }
        }

    }

    public function verifyFromData(){
        $paras_list = array('form_name', 'description', 'data', 'id');
        $param = arrangeParam($_POST, $paras_list);

        if (empty($param['form_name'])) {
            SetReturn(-1,'表单名必填');
        }
        $form_data = json_decode($param['data'], true);
        if (empty($form_data) || !is_array($form_data)){
            SetReturn(-1,'请设置表单内容');
        }
        $new_from_d = [];
        foreach ($form_data as $k1=>$list) {
            $from_item = [];
            foreach ($list as $k2=>$v) {
                if(empty($v['title'])){
                    SetReturn(-1,'选项名称必填');
                }
                $from_item[$k1]['title'] = (string)$v['title'];
                $from_item[$k1]['is_request'] = (int)$v['is_request'];
                $from_item[$k1]['type'] = (int)$v['type'];
            }
            $new_from_d[] = $from_item;
        }
        $param['from_data'] = $new_from_d;
        return $param;
    }
}