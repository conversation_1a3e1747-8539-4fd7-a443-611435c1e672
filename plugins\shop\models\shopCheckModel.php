<?php

namespace plugins\shop\models;


use core\lib\redisCached;

class shopCheckModel extends baseModel
{
    public string $table = 'shop_check';

    public static array $paras_list = [
        "shop_id"      => "店铺ID",
//        "type"         => "类型",
        "check_detail" => "详情",
    ];

    public static array $json_keys = [
        'check_detail',
    ];

    public function formatItem($item, $maps = [])
    {
        $users = $maps['users'] ?? [];
        if (empty($maps['users'])) {
            $users = redisCached::getUserInfo();
            $users = array_column($users, 'user_name', 'user_id');
        }

        $maps = [
            ['name' => 'operator_name', 'maps' => $users, 'key' => 'operator'],
        ];
        return parent::formatItem($item, $maps);
    }

    public static function getMaps()
    {
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        return [
            'users' => $users
        ];
    }

    /**
     * 获取列表
     */
    public function getList($param, $order = 'id desc')
    {
        $db = $this->db->table($this->table)->where('1=1');

        $db->order($order);

        if (isset($param['shop_id'])) {
            $db->andWhere('shop_id = :shop_id', ['shop_id' => $param['shop_id']]);
        }
        if (isset($param['check_type'])) {
            $db->andWhere('check_type = :check_type', ['check_type' => $param['check_type']]);
        }

        if (!empty($param['page']) && !empty($param['page_size'])) {
            $list = $db->pages($param['page'], $param['page_size']);

            $maps = $this->getMaps();
            foreach ($list['list'] as &$item) {
                $item = $this->formatItem($item, $maps);
            }
            return $list;
        } else {
            $list = $db->list();

            $result = [];
            foreach ($list as $item) {
                $formatted = $this->formatItem($item);
                $result[] = $formatted;
            }

            return $result;
        }
    }

    /**
     * 获取店铺详情
     */
    public function getDetail($id)
    {
        return $this->db->table($this->table)
            ->where('id = :id', ['id' => $id])
            ->one();
    }

    public function getByCheckDate($shop_id, $check_date, $type = '1')
    {
        return $this->db->table($this->table)
            ->where('shop_id = :shop_id and check_date = :check_date and check_type = :check_type', [
                'shop_id' => $shop_id,
                'check_date' => $check_date,
                'check_type' => $type
            ])
            ->one();
    }
    // 新增
    public function add($data, $type = '新增')
    {
        $db = $this->db->table($this->table);
        $data = $this->jsonEncodeFormat($data, [
            'check_detail'
        ]);
        $data['operator'] = userModel::$qwuser_id ?? 0;
        $data = $this->validateData($data);
        $id = $db->insert($data);
    }

    // 新增
    public function edit($data, $id, $old_data = [], $type = '编辑', $remark = '', $result = '', $other_attach = [])
    {
        $detail = $this->getById($id);
        if (!$detail) {
            throw new \Exception('数据不存在');
        }
        $data = $this->jsonEncodeFormat($data);
        $data['operator'] = userModel::$qwuser_id ?? 0;
        $data = $this->validateData($data);

        parent::edit($data, $id, $detail, $type, $remark, $result, $other_attach);
    }
}
