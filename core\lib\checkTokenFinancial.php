<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/7 15:22
 */

namespace core\lib;

use admin\form\qwuserFrom;
use financial\models\userModel;

class checkTokenFinancial
{
    public string $ip;
    public function __construct($_url, $prefix)
    {
        $headerData = getallheaders();
        $SkipToken = array(
            "financial/login/login",
            "financial/login/logOut",
            "financial/login/codeLogin",
        );
        $this->ip = GetIP();
        define('USER_TOKEN', $headerData['Authorization']??''); //调试模式设置
        if (!in_array($_url, $SkipToken)) {
            //验证是否带了token
            if (!isset($headerData['Authorization']) || $headerData['Authorization'] == '') {
                SetReturn(1, '请先登录');
            }
            if (!in_array($_url,['admin/login/logOut'])) {
                //验证登录是否过期
                $redis = (new predisV())::$client;
                $key = $prefix.$headerData['Authorization'];

                $data = $redis->hmget($key,['id','name','wid','avatar','is_super','wname','auth', 'list_auth','role_type','user_mac'])??'';
                if (!$data['id']) {
                    SetReturn(1, '请先登录');
                } else {
                    $user_mac = json_decode($data['user_mac'],true);
                    if (!in_array(MAC,$user_mac)) {
                        SetReturn(-1, 'mac地址错误');
                    }
                    userModel::$wid = $data['wid'];
                    userModel::$qwuser_id = $data['id'];
                    userModel::$wname = $data['wname'];
                    userModel::$is_super = $data['is_super'];
                    $role_type = json_decode($data['role_type'],true);
                    userModel::$role_type = $role_type;
                    userModel::$avatar = $data['avatar']??'';
                    userModel::$auth = $data['auth'];
                    userModel::$list_auth = $data['list_auth'];
                    //todo 之后看到 USER_INFO 就将其替换掉
                    define('USER_INFO',json_encode($data));
                    //登录信息更新和时间缓存时间重置
                    $redis->expire($key,4*60*60);
                    //更新所有token时效
                    qwuserFrom::updateTokenTime($data['id']);
                }
            }
        }

    }


}