<?php
/**
 * 调试API测试脚本
 * @author: AI Assistant
 * @Time: 2025/5/29
 */

echo "开始调试API测试...\n\n";

// 第一步：测试登录
echo "=== 第一步：测试登录 ===\n";

$loginData = [
    'account' => '魏雪韵',
    'password' => '123456'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://oa.ywx.com/admin/login/login');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($loginData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
curl_setopt($ch, CURLOPT_VERBOSE, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: $httpCode\n";
echo "响应内容: $response\n";

if ($error) {
    echo "CURL错误: $error\n";
    exit(1);
}

$responseData = json_decode($response, true);
$token = '';

if ($responseData && isset($responseData['data']['token'])) {
    $token = $responseData['data']['token'];
    echo "✓ 登录成功，获取到token: " . substr($token, 0, 20) . "...\n\n";
} else {
    // 尝试其他可能的token字段位置
    if ($responseData && isset($responseData['token'])) {
        $token = $responseData['token'];
        echo "✓ 登录成功，获取到token: " . substr($token, 0, 20) . "...\n\n";
    } elseif ($responseData && isset($responseData['data']) && is_string($responseData['data'])) {
        $token = $responseData['data'];
        echo "✓ 登录成功，获取到token: " . substr($token, 0, 20) . "...\n\n";
    } else {
        echo "✗ 登录失败或未找到token\n";
        echo "完整响应: " . print_r($responseData, true) . "\n";
        exit(1);
    }
}

// 第二步：测试基础配置接口
echo "=== 第二步：测试基础配置接口 ===\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://oa.ywx.com/logistics/festivalActivity/getBaseConfig');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: ' . $token
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: $httpCode\n";
echo "响应内容: $response\n";

if ($error) {
    echo "CURL错误: $error\n";
}

$responseData = json_decode($response, true);
if ($responseData) {
    echo "✓ 接口调用成功\n";
    if (isset($responseData['data']['festival_types'])) {
        echo "✓ 数据结构正确，节日类型数量: " . count($responseData['data']['festival_types']) . "\n";
    }
} else {
    echo "✗ 接口响应格式错误\n";
}

echo "\n=== 第三步：测试创建活动接口 ===\n";

$createData = [
    'name' => '调试测试活动_' . time(),
    'festival_type' => '春节',
    'start_date' => '2025-02-01',
    'end_date' => '2025-02-15',
    'description' => '调试用测试活动',
    'status' => 1
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://oa.ywx.com/logistics/festivalActivity/create');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($createData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded',
    'Authorization: ' . $token
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: $httpCode\n";
echo "响应内容: $response\n";

if ($error) {
    echo "CURL错误: $error\n";
}

$responseData = json_decode($response, true);
if ($responseData && isset($responseData['data']['id'])) {
    $createdId = $responseData['data']['id'];
    echo "✓ 创建活动成功，ID: $createdId\n";
    
    // 第四步：测试获取详情
    echo "\n=== 第四步：测试获取详情接口 ===\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://oa.ywx.com/logistics/festivalActivity/getDetail?id=' . $createdId);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: ' . $token
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP状态码: $httpCode\n";
    echo "响应内容: $response\n";
    
    // 第五步：清理测试数据
    echo "\n=== 第五步：清理测试数据 ===\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://oa.ywx.com/logistics/festivalActivity/delete');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query(['id' => $createdId]));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
        'Authorization: ' . $token
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "删除操作 - HTTP状态码: $httpCode\n";
    echo "删除操作 - 响应内容: $response\n";
    
} else {
    echo "✗ 创建活动失败\n";
    if ($responseData) {
        echo "错误信息: " . ($responseData['msg'] ?? '未知错误') . "\n";
    }
}

echo "\n调试测试完成！\n";
