<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/3 10:07
 */

namespace  plugins\goods\form;

use core\lib\db\dbMysql;

class goodsCateFrom
{
    public static array $goods_cate_list = [];

    /**
     * @param array $cate_ids
     * @return array
     * @throws \core\lib\ExceptionError  不查表获取分类的全面
     * $type  0递归查，1不递归
     */
    public static function getGoodsCate(array $cate_ids,$type=0) {
        if (!count($cate_ids)) {
            return [];
        }
        if (count(self::$goods_cate_list) == 0) {
            $db = dbMysql::getInstance();
            $all_cate = $db->queryAll('select * from oa_goods_cate where is_delete = 0');
        } else {
            $all_cate = self::$goods_cate_list;
        }
        $data = [];
        foreach ($cate_ids as $v) {
            foreach ($all_cate as $v1) {
                if ($v1['id'] == $v) {
                    $data[] = $v1;
                }
            }
        }
        $result = [];
        foreach ($data as $v) {
            //递归获取名称
            if (!$type) {
                $name_array = self::getCateAllName($all_cate,$v['p_id'],[$v['cate_name']]);
            } else {
                $name_array = [$v['cate_name']];
            }
            $result[] = [
                'id'=>$v['id'],
                'p_id'=>$v['p_id'],
                'name'=>implode('/',array_reverse($name_array))
            ];
        }
        return $result;
    }
    //获取完整名
    private static function getCateAllName($all_cate,$pid,$name_list){
        if ($pid>0) {
            foreach ($all_cate as $cate) {
                if ($cate['id'] == $pid) {
                    $name_list[] = $cate['cate_name'];
                    if ($cate['p_id'] > 0) {
                        $name_list = self::getCateAllName($all_cate,$cate['p_id'],$name_list);
                    }
                }
            }
        }
        return $name_list;
    }
}