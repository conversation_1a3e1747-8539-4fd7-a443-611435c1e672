<?php
/**
 * @author: zhangguoming
 * @Time: 2025/3/28 16:04
 */

namespace admin\form;

use admin\models\userModel;
use core\lib\db\dbMysql;

class pluginsForm
{
    public static function getPlugins() {
        $db = dbMysql::getInstance();
        $plugins = $db->table('plugins')
            ->where('status = 1')
            ->field('key_name,name,path,icon')
            ->order('sort asc')
            ->list();
        if (userModel::$is_super) {
            return $plugins;
        } else {
            $user_auth = json_decode(userModel::$auth);
            $plugins_ = [];
            if (count($user_auth)) {
                foreach ($plugins as $v) {
                    if (in_array($v['key_name'],$user_auth)) {
                        $plugins_[] = $v;
                    }
                }
            }
            return  $plugins_;
        }
    }
}