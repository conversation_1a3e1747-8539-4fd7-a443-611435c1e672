<?php

namespace plugins\shop\models;

use core\lib\redisCached;
use Exception;

class trademarkModel extends baseModel
{
    public string $table = 'trademark';

    // 商标类型字段定义
    const TYPE_SELF = '自注册';   // 自注册
    const TYPE_PURCHASE = '外购'; // 外购

    public static array $paras_list = [
        // 基本信息
//        "dep_id"                    => "部门",
        "trademark_type"            => "商标类型|required",
        "brand_name"                => "品牌名|required",
        "country"                   => "国家|required",
        "category"                  => "类目|required",
        // 自注册信息
        "domain_id"                 => "域名ID|required",
        "email_id"                  => "邮箱ID|required",
        "service_provider"          => "服务商|required",
        // 外购信息
        "transfer_lawyer"           => "商标转让律师|required",
        "trademark_holder"          => "商标持有人|required",
        "trademark_holder_pre"      => "商标转让前持有人|required",
        "transfer_date"             => "商标转让日期|required",
        "transfer_provider"         => "商标转让服务商|required",
        "price"                     => "价格|required",
        "currency"                  => "币种|required",
        // 共用信息
        "register_date"             => "注册 / 购买日期|required",
        "certificate_date"          => "下证日期|required",
        "certificate_number"        => "证书号 / 注册号|required",
        "original_storage"          => "原件保管地|required",
        "validity_period"           => "商标有效期|required",
        "earliest_declaration_date" => "最早宣誓日期|required",
        "shop_id"                   => "备案店铺ID",
        "record_date"               => "备案日期|required",
        "use_status"                => "使用状态|required",
        "remark"                    => "备注",
    ];

    public static array $json_keys = [
        'validity_period',
        'domain_id',
        'email_id',
        'shop_id'
    ];

    /**
     * 获取商标列表
     */
    public function getList($param, $order = 'id desc', $is_export = false)
    {
        $db = $this->db->table($this->table);

        // 固定筛选
        if (!empty($param['ids'])) {
            $this->db->whereIn('id', $param['ids']);
        }

        // 申请人
        if (!empty($param['user_id'])) {
            $db->andWhere('user_id = :user_id', ['user_id' => $param['user_id']]);
        }

        // 部门ID
        if (!empty($param['dep_id']) && is_array($param['dep_id'])) {
            $db->whereIn('dep_id', $param['dep_id']);
        }

        // 商标类型
        if (!empty($param['trademark_type'])) {
            $db->andWhere('trademark_type = :trademark_type', ['trademark_type' => $param['trademark_type']]);
        }

        // 状态
        if (!empty($param['status'])) {
            $db->andWhere('status = :status', ['status' => $param['status']]);
        }

        // 品牌名
        if (!empty($param['brand_name'])) {
            $db->andWhere('brand_name LIKE :brand_name', ['brand_name' => '%' . $param['brand_name'] . '%']);
        }

        // 商标持有人
        if (!empty($param['trademark_holder'])) {
            $db->andWhere('trademark_holder LIKE :trademark_holder', ['trademark_holder' => '%' . $param['trademark_holder'] . '%']);
        }

        // 转让律师
        if (!empty($param['transfer_lawyer'])) {
            $db->andWhere('transfer_lawyer LIKE :transfer_lawyer', ['transfer_lawyer' => '%' . $param['transfer_lawyer'] . '%']);
        }

        // 域名
        if (!empty($param['domain'])) {
            $db->andWhere('JSON_CONTAINS(domain_id, :domain)', ['domain' => json_encode([$param['domain']])]);
        }

        // 邮箱
        if (!empty($param['email'])) {
            $db->andWhere('JSON_CONTAINS(email_id, :email)', ['email' => json_encode([$param['email']])]);
        }

        // 商标原持有人
        if (!empty($param['trademark_holder_pre'])) {
            $db->andWhere('trademark_holder_pre LIKE :trademark_holder_pre', ['trademark_holder_pre' => '%' . $param['trademark_holder_pre'] . '%']);
        }

        // 转让服务商
        if (!empty($param['transfer_provider'])) {
            $db->andWhere('transfer_provider LIKE :transfer_provider', ['transfer_provider' => '%' . $param['transfer_provider'] . '%']);
        }

        // 服务商
        if (!empty($param['service_provider'])) {
            $db->andWhere('service_provider LIKE :service_provider', ['service_provider' => '%' . $param['service_provider'] . '%']);
        }

        // 证书号
        if (!empty($param['certificate_number'])) {
            $db->andWhere('certificate_number = :certificate_number', ['certificate_number' => $param['certificate_number']]);
        }

        // 原件保管地
        if (!empty($param['original_storage'])) {
            $db->andWhere('original_storage LIKE :original_storage', ['original_storage' => '%' . $param['original_storage'] . '%']);
        }

        // 国家
        if (!empty($param['country']) && is_array($param['country'])) {
            $db->whereIn('country', $param['country']);
        }

        // 类目
        if (!empty($param['category']) && is_array($param['category'])) {
            $db->whereIn('category', $param['category']);
        }

        // 使用状态
        if (isset($param['use_status'])) {
            $db->andWhere('use_status = :use_status', ['use_status' => $param['use_status']]);
        }

        // 注册日期
        if (!empty($param['register_date']) && is_array($param['register_date']) && count($param['register_date']) == 2) {
            $db->andWhere("register_date >= :register_date_start AND register_date <= :register_date_end", [
                'register_date_start' => $param['register_date'][0],
                'register_date_end'   => $param['register_date'][1]
            ]);
        }

        // 商标转让日期
        if (!empty($param['transfer_date']) && is_array($param['transfer_date']) && count($param['transfer_date']) == 2) {
            $db->andWhere("transfer_date >= :transfer_date_start AND transfer_date <= :transfer_date_end", [
                'transfer_date_start' => $param['transfer_date'][0],
                'transfer_date_end'   => $param['transfer_date'][1]
            ]);
        }

        // 下证日期
        if (!empty($param['certificate_date']) && is_array($param['certificate_date']) && count($param['certificate_date']) == 2) {
            $db->andWhere("certificate_date >= :certificate_date_start AND certificate_date <= :certificate_date_end", [
                'certificate_date_start' => $param['certificate_date'][0],
                'certificate_date_end'   => $param['certificate_date'][1]
            ]);
        }

        // 最早宣誓日期
        if (!empty($param['earliest_declaration_date']) && is_array($param['earliest_declaration_date']) && count($param['earliest_declaration_date']) == 2) {
            $db->andWhere("earliest_declaration_date >= :earliest_declaration_date_start AND earliest_declaration_date <= :earliest_declaration_date_end", [
                'earliest_declaration_date_start' => $param['earliest_declaration_date'][0],
                'earliest_declaration_date_end'   => $param['earliest_declaration_date'][1]
            ]);
        }

        // 分配日期
        if (!empty($param['bind_date']) && is_array($param['bind_date']) && count($param['bind_date']) == 2) {
            $db->andWhere("bind_time >= :bind_time_start AND bind_time <= :bind_time_end", [
                'bind_time_start' => $param['bind_date'][0],
                'bind_time_end'   => $param['bind_date'][1] . ' 23:59:59'
            ]);
        }

        // 备案日期
        if (!empty($param['record_date']) && is_array($param['record_date']) && count($param['record_date']) == 2) {
            $db->andWhere("record_date >= :record_date_start AND record_date <= :record_date_end", [
                'record_date_start' => $param['record_date'][0],
                'record_date_end'   => $param['record_date'][1]
            ]);
        }

        // 有效期
        if (!empty($param['validity_period'])) {
            $this->db->andWhere('JSON_EXTRACT(validity_period, "$[1]") >= :validity_period_start
                                and JSON_EXTRACT(validity_period, "$[0]") <= :validity_period_end',
                [
                    'validity_period_start' => $param['validity_period'][0],
                    'validity_period_end'   => $param['validity_period'][1]
                ]);
        }

        $db->order($order);

        if (!$is_export && !empty($param['page']) && !empty($param['page_size'])) {
            $list = $db->pages($param['page'], $param['page_size']);
            $maps = self::getMaps();
            foreach ($list['list'] as &$item) {
                $item = $this->formatItem($item, $maps);
            }
            return $list;
        } else {
            $list = $this->db->list();
            if ($is_export) {
                $export_data = [];
                $paras_list = static::$paras_list;
                $paras_list['domain'] = '域名';
                $paras_list['shop'] = '备案店铺';
                $maps = self::getMaps();
                foreach ($list as $item) {
                    $item = $this->formatItem($item, $maps);
                    // 使用参数对应的中文作为键名返回
                    $export_data[] = self::changeToCnKey($item, $paras_list);
                }
                return $export_data;
            }

            return $list;
        }
    }

    public static function getMaps()
    {
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        $deps = redisCached::getDepartment();
        $deps = array_column($deps, 'dep_name', 'id');

        $domains = redisCached::getDomain();
        $emails = redisCached::getEmail();
        $shops = redisCached::getShop();

        return [
            'users'   => $users,
            'deps'    => $deps,
            'domains' => $domains,
            'emails'  => $emails,
            'shops'   => $shops,
        ];

    }

    public function dataValidCheck(array $data, array $param_list = [], $is_throw = true, $error = []): array
    {
        if ($data['trademark_type'] == self::TYPE_SELF) {
            unset($param_list['transfer_lawyer']);
            unset($param_list['trademark_holder']);
            unset($param_list['trademark_holder_pre']);
            unset($param_list['transfer_date']);
            unset($param_list['transfer_provider']);
            unset($param_list['price']);
            unset($param_list['currency']);
        } elseif ($data['trademark_type'] == self::TYPE_PURCHASE) {
            unset($param_list['domain_id']);
            unset($param_list['service_provider']);
        }

        return parent::dataValidCheck($data, $param_list, $is_throw, $error);
    }

    /**
     * 获取商标详情
     */
    public function getDetail($id)
    {
        if (empty($id)) {
            return null;
        }

        $item = $this->db->table($this->table)
            ->where('id = :id', ['id' => $id])
            ->one();

        return $this->formatItem($item);
    }

    /**
     * 新增商标
     */
    public function add($data, $type = '新增')
    {
        if (empty($data)) {
            throw new Exception('数据不能为空');
        }
        $domain_id = $data['domain_id'] ?? null;
        $email_id = $data['email_id'] ?? null;
        $shop_id = $data['shop_id'] ?? null;
        $id = parent::add($data, $type);

        $relationModel = new relationModel();
        $relationModel->deleteRelation('trademark', $id, 'domain');
        if ($domain_id) {
            // 使用updateRelation方法,会自动处理创建或更新
            foreach ($domain_id as $d) {
                $relationModel->createRelation($this->table, $id, 'domain', $d);
            }
        }
        $relationModel->deleteRelation('trademark', $id, 'email');
        if ($email_id) {
            // 使用updateRelation方法,会自动处理创建或更新
            foreach ($email_id as $d) {
                $relationModel->createRelation($this->table, $id, 'email', $d);
            }
        }
        $relationModel->deleteRelation('trademark', $id, 'shop');
        if ($shop_id) {
            // 使用updateRelation方法,会自动处理创建或更新
            foreach ($shop_id as $d) {
                $relationModel->createRelation($this->table, $id, 'shop', $d);
            }
        }

        return $id;
    }

    public function edit($data, $id, $old_data = [], $type = '编辑', $remark = '', $result = '', $other_attach = [])
    {
        $domain_id = $data['domain_id'] ?? null;
        $email_id = $data['email_id'] ?? null;
        $shop_id = $data['shop_id'] ?? null;
        $relationModel = new relationModel();
        $relationModel->deleteRelation('trademark', $id, 'domain');
        if ($domain_id) {
            // 使用updateRelation方法,会自动处理创建或更新
            foreach ($domain_id as $d) {
                $relationModel->createRelation($this->table, $id, 'domain', $d);
            }
        }
        $relationModel->deleteRelation('trademark', $id, 'email');
        if ($email_id) {
            // 使用updateRelation方法,会自动处理创建或更新
            foreach ($email_id as $d) {
                $relationModel->createRelation($this->table, $id, 'email', $d);
            }
        }
        $relationModel->deleteRelation('trademark', $id, 'shop');
        if ($shop_id) {
            // 使用updateRelation方法,会自动处理创建或更新
            foreach ($shop_id as $d) {
                $relationModel->createRelation($this->table, $id, 'shop', $d);
            }
        }
        parent::edit($data, $id, $old_data, $type, $remark, $result, $other_attach);
    }

    /**
     * 格式化输出项
     */
    public function formatItem($item, $maps = [])
    {
        $users = $maps['users'] ?? [];
        if (empty($users)) {
            $users = redisCached::getUserInfo();
            $users = array_column($users, 'user_name', 'user_id');
        }
        $deps = $maps['deps'] ?? [];
        if (empty($deps)) {
            $deps = redisCached::getDepartment();
            $deps = array_column($deps, 'name', 'id');
        }
        $domains = $maps['domains'] ?? [];
        if (empty($domains)) {
            $domains = redisCached::getDomain();
        }
        $emails = $maps['emails'] ?? [];
        if (empty($emails)) {
            $emails = redisCached::getEmail();
        }
        $shops = $maps['shops'] ?? [];
        if (empty($shops)) {
            $shops = redisCached::getShop();
            $shops = array_column($shops, 'shop_number', 'id');
        }
        $maps = [
            ['name' => 'operator_name', 'maps' => $users, 'key' => 'operator'],
            ['name' => 'user_name', 'maps' => $users, 'key' => 'user_id'],
            ['name' => 'dep_name', 'maps' => $deps, 'key' => 'dep_id'],
            ['name' => 'domain', 'maps' => $domains, 'key' => 'domain_id', 'is_array' => 1, 'keys' => ['domain']],
            ['name' => 'email', 'maps' => $emails, 'key' => 'email_id', 'is_array' => 1, 'keys' => ['email_account']],
            ['name' => 'shop', 'maps' => $shops, 'key' => 'shop_id', 'is_array' => 1, 'keys' => ['shop_number']],
        ];
        return parent::formatItem($item, $maps);
    }

    public function getByBrandName($brand_name, $id = null)
    {
        $db = $this->db->table($this->table);
        $db->where('brand_name = :brand_name', ['brand_name' => $brand_name]);
        if ($id) {
            $db->andWhere('id != :id', ['id' => $id]);
        }
        return $db->one();
    }

    // 批量编辑
    public function editBatch($data)
    {
        $db = $this->db->table($this->table);
        $ids = array_column($data, 'id');
        $db->whereIn('id', $ids);
        $list = $db->list();
        $list = array_column($list, null, 'id');

        $error_ids = [];
        foreach ($data as $item) {
            try {
                $item_id = $item['id'];
                unset($item['id']);
                // 唯一性校验
                $this->dataValidCheck($item, self::$paras_list);
                $detail = $this->getByBrandName($item['brand_name'], $item_id);
                if ($detail) {
                    throw new Exception('品牌名已存在');
                }
                self::edit($item, $item_id, $list[$item_id], '批量编辑');
            } catch (Exception $e) {
                $error_ids[] = [
                    'id' => $item_id,
                    'error' => $e->getMessage(),
                ];
            }
        }
        return $error_ids;
    }
}
