<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/1 9:18
 */

namespace  plugins\goods\form;

use plugins\goods\models\userModel;
use core\lib\config;
use core\lib\db\dbMysql;

class goodsNewFrom
{
    //修改需要记录的字段
    public static array $field_log_list = [
        'goods_name'=>'产品名称',
        'is_app'=>'是否APP产品',
        'is_electriferous'=>'是否带电',
        'goods_img'=>'产品图片',
        'cat_id'=>'分类ID',
        'color_id'=>'颜色ID',
        'function_id'=>'功能ID',
        'manage_info'=>'负责人',
        'operator_info'=>'运营人员',
        'supplier_id'=>'供应商id',
    ];
    /**
     * 修改商品信息的数据   内容$item = ['key_name'=>'规格','data'=>'1red']
     * attachment 附件；purchase产品采购信息;goods_info产品基础信息
     * @var array
     */
    public static array $update_log_data = ['attachment'=>[],'purchase'=>[],'goods_info'=>[]];
    /**
     * @param $goods_attachment
     * @return void
     * 添加货修改商品信息时，对商品商品附件的验证
     */
    public static function varifyAttachmentSubmit($goods_attachment_data,$is_electriferous, $cat_ids) {
        $attachment_data = json_decode($goods_attachment_data, true);
        $file_list_for_type = [];
        $types = [];
        $cat_ids = json_decode($cat_ids);
        foreach ($attachment_data as $file_) {
            $file_list_for_type[$file_['type']][] = $file_['url'];
            $types[] = $file_['type'];
            $file_array = explode('.',$file_['url']);
            $extension = end($file_array);
            switch ($file_['type']) {
                case 1;
                    if (empty($file_['url'])) {
                        SetReturn(-1,'请上传3D模型图');
                    }
                    break;
                case 2;
                    if (empty($file_['url'])) {
                        SetReturn(-1,'请上传规格书');
                    }
                    if (!in_array($extension,['xls','xlsx'])) {
                        SetReturn(-1,'规格书格式只能为xls或xlsx');
                    }
                    break;
                case 3;
                    if ($is_electriferous && empty($file_['url'])) {
                        SetReturn(-1,'请上传电池报告');
                    }
                    break;
                case 4;
                    break;
                case 5;
                    break;
                case 6;
                    break;
                default;
                    SetReturn(-1,'没有该类型附件！');
            }
        }
        if (!in_array('1',$types)) {
            $db = dbMysql::getInstance();
            $cat_list = $db->table('goods_cate')
                ->where('where has_3D_model=0')
                ->whereIn('id',$cat_ids)
                ->count();
            if (!$cat_list) {
                SetReturn(-1,'请上传3D模型图');
            }
        }
        if (!in_array('2',$types)) {
            SetReturn(-1,'请上传规格书');
        } else {
            if (count($file_list_for_type[2]) > 1) {
                SetReturn(-1,'规格书只能上传一个');
            }
        }
        if ($is_electriferous && !in_array('3',$types)) {
            SetReturn(-1,'请上传电池报告');
        }

        return $attachment_data;
    }

    public static function getGoodsPurchaseUpdate($purchase,$new_data) {
        $update_data = [];
        $purchase_field = [
            'outer_box_info'=>'外箱规格',
            'outer_box_unit'=>'外箱规格单位',
            'pack_info'=>'产品包装规格',
            'pack_unit'=>'产品包装规格单位',
            'single_weight'=>'单个重量(g)',
            'number'=>'单箱数量(个)',
            'weight'=>'单箱重量(kg)',
        ];
        foreach ($purchase as $key=>$v) {
            if (isset($purchase_field[$key])) {
                $item = [];
                if ($new_data[$key] != $v) {
                    if ($key == 'outer_box_info' || $key == 'outer_box_unit') {
                        $item = [
                            'key_name'=>$purchase_field['outer_box_info'],
                            'data'=>$purchase['outer_box_info'].config::getDataName('length_unit',(int)$purchase['outer_box_unit']).'->'.$new_data['outer_box_info'].config::getDataName('length_unit',(int)$new_data['outer_box_unit']),
                        ];
                    } elseif ($key == 'pack_info' || $key == 'pack_unit') {
                        $item = [
                            'key_name'=>$purchase_field['pack_info'],
                            'data'=>$purchase['pack_info'].config::getDataName('length_unit',$purchase['pack_unit']).'->'.$new_data['pack_info'].config::getDataName('length_unit',$new_data['pack_unit']),
                        ];
                    } else {
                        $item = [
                            'key_name'=>$purchase_field[$key],
                            'data'=>$new_data[$key]
                        ];
                    }
                    $update_data[] = $item;
                }

            }
        }
        goodsNewFrom::$update_log_data['purchase'] = $update_data;
    }

    /**
     * @param $goods_data
     * @return void 验证新品是否可操作
     */
    static public function varifyAllowOperateGoods($goods_data) {
        if (in_array($goods_data['status'],[2,4])) {
            switch ($goods_data['status']) {
                case 2:
                    SetReturn(-1,'该新品已出货，不可操作');
                case 4:
                    SetReturn(-1,'该新品已暂停，不可操作');
            }
        }
    }

    /**
     * @param $goods_id
     * @param $lod_data
     * @param $data
     * @return void 设置修改日志信息
     */
    public static function setUpdateLog($lod_data=[],$data = [],) :void
    {
        if (count($data)) {
            $update_data = [];
            foreach ($data as $k=>$v) {
                if (isset($lod_data[$k]) && ($lod_data[$k] != $v) && isset(self::$field_log_list[$k])) {
                    if ($k == 'is_app' || $k == 'is_electriferous') {
                        $mgs = ($lod_data[$k]==1?'是':'否').'->'.($data[$k]==1?'是':'否');
                    } else if ($k == 'manage_info' || $k == 'operator_info') {
                        $manage_info = json_decode($data[$k], true);
                        $old_manage_info = json_decode($lod_data[$k], true);
                        $wnames = array_column($manage_info,'wname');
                        $old_wnames = array_column($old_manage_info,'wname');
                        $mgs = implode(',',$old_wnames).'->'.implode(',',$wnames);
                    } else {
                        $mgs = $lod_data[$k].'->'.$data[$k];
                    }
                    $item = [
                        'key_name'=>self::$field_log_list[$k],
                        'data'=>$mgs,
                    ];
                    $update_data[] = $item;
                }
            }
            self::$update_log_data['goods_info'] = $update_data;
        }

    }


    /**
     * @param $goods_id
     * @return void
     * @throws \core\lib\ExceptionError  获取产品寄出信息 切记在循环中调用
     */
    public static function getGoodsBaseInfo($goods_id) {
        $db = dbMysql::getInstance();
        $db->table('goods_new');
        $db->where('where id=:goods_id',['goods_id'=>$goods_id]);
        $db->field('id,is_app,goods_name,e_name,manage_info,operator_info,goods_img,bluetooth,function_id,cat_id,manage_info');
        $goods_info = $db->one();
        $code_info = $db->query('select code from oa_goods_code where goods_id=:goods_id',['goods_id'=>$goods_id]);
        //蓝牙编号
        $goods_info['code'] = $code_info['code'];
        //分类
        $goods_info['cate_list'] = goodsCateFrom::getGoodsCate(json_decode($goods_info['cat_id'],true));
        //功能
        $goods_info['function_list'] = goodsFunctionFrom::getGoodsFunction(json_decode($goods_info['function_id'],true));
        //颜色
        $goods_info['color_list'] = goodsColorRelationFrom::getGoodsColor($goods_id);
        return $goods_info;
    }

    public static function  getMatterName1($goods_name,$tpl_name,$sample_batch,$batch_count) {
        $matter_name = $goods_name.'_'.$tpl_name;
        if ($sample_batch == 1) {
            $matter_name.='_首批';
        } else {
            $matter_name.='_第'.($batch_count+1).'批';
        }
        return $matter_name;
    }

    //出货样称获取
    public static function  getMatterName2($goods_name,$tpl_name,$sample_batch,$batch_count) {
        $matter_name = $goods_name.$tpl_name;
        if ($sample_batch == 1) {
            if ($batch_count > 1) {
                $matter_name.="_首货".$batch_count."批次";
            } else {
                $matter_name.='_首批';
            }
        } else {
            $matter_name.='_第'.($batch_count+1).'批';
        }
        return $matter_name;
    }
    //抽货样称获取
    public static function  getMatterName3($goods_name,$tpl_name,$sample_batch,$batch_count) {
        $matter_name = $goods_name.$tpl_name;
        if ($sample_batch == 1) {
            $matter_name.='_首批';
        } else {
            $matter_name.='_第'.($batch_count+1).'批';
        }
        return $matter_name;
    }

    /**
     * @param $goods_id
     * @param $is_goods_manage
     * @param $is_goods_operator
     * @return array
     * @throws \core\lib\ExceptionError 获取产品附件  不可用于循环调用
     */
    public static  function getGoodsAttachmentInfo($goods_id, $is_goods_manage, $is_goods_operator, $is_project_participant) {
        //注意  userModel::isZhuanLiMan() 的顺序
        if (!(userModel::isZhuanLiMan() || userModel::$is_super || userModel::isManage() || $is_goods_manage || $is_goods_operator || $is_project_participant)) {
            return [];
        }
        $new_list = [];
        //根据权限来返回数据
        $db = dbMysql::getInstance();
        $db->table('goods_attachment')->where('where goods_id=:goods_id and is_delete=0',['goods_id'=>$goods_id]);
        if (!(userModel::$is_super || userModel::isManage() || $is_goods_manage)) {
            if (!($is_project_participant || $is_goods_operator)) {
                if (userModel::isZhuanLiMan()) {
                    $db->andWhere('and `type` = 4');
                }
            } else {
                $db->andWhere('and `type` <> 4');
            }
        }
        $db->field('id,goods_id,type,file_name,created_time');
        $goods_attachment = $db->list();
        foreach ($goods_attachment as $v) {
            $new_list[] = [
                'id'=>$v['id'],
                'file_type'=>config::getDataName('goods_attachment_type',$v['type']),
                'file_name'=>$v['file_name'],
                'created_time'=>$v['created_time'],
            ];
        }
        return $new_list;
    }

    /**
     * @param $project_file
     * @return array
     * @throws \core\lib\ExceptionError  不可用于循环调用
     */
    public static  function getProjectFileInfo($goods_id) {
        $db = dbMysql::getInstance();
        $project_file = $db->queryAll('select id,event_type,filename,source_type,created_at as created_time from oa_goods_project_file where goods_id=:goods_id and is_delete = 0',['goods_id'=>$goods_id]);
        $new_list = [];
        foreach ($project_file as $v) {
            if ($v['source_type'] == 0) {
                $file_name = config::getDataName('event_type',$v['event_type']);
            } else {
                $file_name = $v['source_type']==1?"说明书":"检测标准书";
            }
            $new_list[] = [
                'id'=>$v['id'],
                'file_type'=>$file_name,
                'file_name'=>$v['filename'],
                'created_time'=>$v['created_time'],
            ];
        }
        return $new_list;
    }

    /**
     * @param $manage_info
     * @return int|void  是否为产品开发，运维
     */
    public static function isGoodsMnage($manage_info){
        $goods_manage_info = json_decode($manage_info, true);
        $goods_manage_wid = array_column($goods_manage_info,'wid');
        if (in_array(userModel::$wid,$goods_manage_wid)) {
            return 1;
        }else {
            return 0;
        }
    }

}