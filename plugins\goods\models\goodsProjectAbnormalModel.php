<?php
/**
 * @author: zhangguoming
 * @Time: 2024/1/5 11:20
 */

namespace  plugins\goods\models;

use core\lib\db\dbMysql;

class goodsProjectAbnormalModel
{
    /**
     * @param $type 异常类型，0商品，1项目，2，节点，3事件
     * @param $goods_id
     * @param $project_id
     * @param $node_index
     * @param $event_index
     * @return mixed
     * @throws \core\lib\ExceptionError
     */
    public static function getAbnormalCount($type, $project_id, $node_index = '', $event_index = ''){
        $db = dbMysql::getInstance();
        $db->table('goods_project_abnormal');
        $where_sql = 'where is_handled = 0';
        $where_data = [];
        if ($type == 1) {
            $where_sql .= ' and project_id = :project_id';
            $where_data = [
                'project_id'=>$project_id,
            ];
        }  elseif ($type == 2) {
            $where_sql .= ' and project_id = :project_id';
            $where_sql .= ' and node_index = :node_index';
            $where_data = [
                'project_id'=>$project_id,
                'node_index'=>$node_index
            ];
        } elseif ($type == 3) {
            $where_sql .= ' and project_id = :project_id';
            $where_sql .= ' and node_index = :node_index';
            $where_sql .= ' and event_index = :event_index';
            $where_data = [
                'project_id'=>$project_id,
                'node_index'=>$node_index,
                'event_index'=>$event_index,
            ];
        } else {
            SetReturn(-1, 'type不存在');
        }
        $db->where($where_sql, $where_data);
        $count = $db->count();
        return $count;
    }
}