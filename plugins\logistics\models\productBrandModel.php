<?php
/**
 * 产品品牌模型
 * @purpose 处理领星产品品牌数据的存储和查询
 * @Author: System
 * @Time: 2025/07/04
 */

namespace plugins\logistics\models;

use core\lib\db\dbErpMysql;

class productBrandModel
{
    private $db;
    
    public function __construct()
    {
        $this->db = dbErpMysql::getInstance();
    }
    
    /**
     * 批量保存品牌数据
     * @param array $brandList 品牌数据列表
     * @param string $syncDate 同步日期
     * @return int 成功保存的数量
     */
    public function batchSaveBrands($brandList, $syncDate = '')
    {
        if (empty($brandList)) {
            return 0;
        }
        
        if (empty($syncDate)) {
            $syncDate = date('Y-m-d');
        }
        
        $successCount = 0;
        
        try {
            $this->db->beginTransaction();
            
            foreach ($brandList as $brand) {
                if ($this->saveSingleBrand($brand, $syncDate)) {
                    $successCount++;
                }
            }
            
            $this->db->commit();
            return $successCount;
            
        } catch (\Exception $e) {
            $this->db->rollBack();
            error_log("批量保存品牌数据失败: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * 保存单个品牌数据
     * @param array $brand 品牌数据
     * @param string $syncDate 同步日期
     * @return bool
     */
    private function saveSingleBrand($brand, $syncDate)
    {
        if (empty($brand['bid'])) {
            return false;
        }
        
        // 检查是否已存在
        $existing = $this->getBrandByBid($brand['bid']);
        
        $data = [
            'bid' => (int)$brand['bid'],
            'title' => $brand['title'] ?? '',
            'brand_code' => $brand['brand_code'] ?? '',
            'sync_date' => $syncDate,
            'is_deleted' => 0
        ];
        
        if ($existing) {
            // 更新现有记录
            return $this->db->table('lingxing_product_brand')
                ->where('bid = :bid', ['bid' => $brand['bid']])
                ->update($data);
        } else {
            // 插入新记录
            return $this->db->table('lingxing_product_brand')->insert($data);
        }
    }
    
    /**
     * 根据品牌ID获取品牌信息
     * @param int $bid 品牌ID
     * @return array|false
     */
    public function getBrandByBid($bid)
    {
        return $this->db->table('lingxing_product_brand')
            ->where('bid = :bid AND is_deleted = 0', ['bid' => $bid])
            ->one();
    }
    
    /**
     * 获取品牌列表
     * @param array $params 查询参数
     * @return array
     */
    public function getBrandList($params = [])
    {
        $page = (int)($params['page'] ?? 1);
        $pageSize = (int)($params['page_size'] ?? 20);
        
        $this->db->table('lingxing_product_brand')->where('where is_deleted = 0');
        
        // 搜索条件
        if (!empty($params['title'])) {
            $this->db->where('and title LIKE :title', ['title' => '%' . $params['title'] . '%']);
        }
        
        if (!empty($params['brand_code'])) {
            $this->db->where('and brand_code LIKE :brand_code', ['brand_code' => '%' . $params['brand_code'] . '%']);
        }
        
        if (!empty($params['sync_date'])) {
            $this->db->where('and sync_date = :sync_date', ['sync_date' => $params['sync_date']]);
        }
        
        return $this->db->field('*')->order('updated_at DESC')->pages($page, $pageSize);
    }
    
    /**
     * 获取品牌统计信息
     * @param string $syncDate 同步日期
     * @return array
     */
    public function getBrandStats($syncDate = '')
    {
        if (empty($syncDate)) {
            $syncDate = date('Y-m-d');
        }
        
        // 总数统计
        $totalCount = $this->db->table('lingxing_product_brand')
            ->where('is_deleted = 0')
            ->count();
        
        // 今日同步数量
        $todayCount = $this->db->table('lingxing_product_brand')
            ->where('sync_date = :sync_date AND is_deleted = 0', ['sync_date' => $syncDate])
            ->count();
        
        // 有品牌简码的数量
        $withCodeCount = $this->db->table('lingxing_product_brand')
            ->where('brand_code != "" AND is_deleted = 0')
            ->count();
        
        return [
            'total_count' => $totalCount,
            'today_count' => $todayCount,
            'with_code_count' => $withCodeCount,
            'sync_date' => $syncDate
        ];
    }
    
    /**
     * 清理指定日期的品牌数据
     * @param string $syncDate 同步日期
     * @return bool
     */
    public function clearBrandsByDate($syncDate)
    {
        return $this->db->table('lingxing_product_brand')
            ->where('sync_date = :sync_date', ['sync_date' => $syncDate])
            ->update(['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * 根据品牌名称搜索
     * @param string $keyword 关键词
     * @param int $limit 限制数量
     * @return array
     */
    public function searchBrandsByTitle($keyword, $limit = 10)
    {
        $sql = "SELECT bid, title, brand_code FROM lingxing_product_brand
                WHERE title LIKE ? AND is_deleted = 0
                ORDER BY title ASC
                LIMIT " . (int)$limit;

        return $this->db->queryAll($sql, ['%' . $keyword . '%']);
    }
}
