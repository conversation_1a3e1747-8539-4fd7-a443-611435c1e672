<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/16 15:56
 */

namespace financial\models;

use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use financial\common\boardBase;
use financial\form\coustomColumnJobForm;
use financial\form\customColumnForm;
use financial\form\goodsInformationForm;
use financial\form\listingFirstTimeFrom;
use financial\form\sellerForm;
use financial\form\tableDataForm;

class boardTableModels extends boardBase
{
    public static string $auth_key_ = '';
    public static array $param;
    public static array $locked_month = [];//已结账的月份
    public static array $all_show_keys = [];
    public static bool $show_all_data = false;//查看所有数据
    public static array $yunying_ids = [];//要查询的运营id，为[],标识查询所有人的数据。
    public static array $project_ids = [];//要查询的运营id对应的项目id。
    public static array $function_keys = [];//方案显示的字段
    public static int $show_rate_column = 1;
    public static string $real_table = '';//真实的表类型（有效表用的其他数据，表类型就是其他类型）
    public static array $month_qoq_key = ['oa_key_1','oa_key_3','oa_key_4'];//月汇总要计算环比的数据
    public static array $country_table = ['asin','p_asin','sid'];//以国家为维度的表格
    public static array $profit_table = ['goods_sku','supplier_sku'];//查利润贡献表
    public static string $param_md5;//搜索的md5
    public static array $years; //查询年数据
    public static array $search_month; //搜索时间段
    public static array $aggregation_keys = []; //去除合并字段(搜索传的字段)
    public static array $lx_keys = []; //要查的领星字段
    public static array $oa_column_ids; //要查的自定义字段id
    public static array $oa_keys=[]; //要查的自定义字段
    public static int $need_last_data = 0; //是否需要上月数据
    public static array $last_years; //上月查询年数据
    public static array $last_all_keys = []; //要查的领星上月字段
    public static array $lx_last_keys = []; //要查的领星上月字段
    public static array $oa_last_ids; //要查的上月自定义字段id
    public static string $table; //当前查询的报表表单 **
    public static array $currency_keys;//货币字段（计算时要按汇率转）
    public static array $column_list;//要显示的列
    public static array $all_keys=[];//所有字段
    public static int $page = 1;
    public static int $page_size = 10;
    public static array  $need_again_oa_keys2 = [];//需要单独拿出来计算的自定义字段(百分比字段);
    public static array  $need_again_oa_keys1 = [];//需要单独拿出来计算的自定义字段(跟毛利润相关的数字，其他跟毛利润不相关且不为百分比的完全不用重新计算，统计就行);
    public static array $sql_sku_list = [];//根据产品名称，分类新品，查出来的sku集合
    public static int $sql_use_sku = 0;//是否用sku
    public static array $sql_new_goods_data = [];//根据新品筛选获取的条件数据
    public static int $sql_use_new_goods = 0;//是否存在新品筛选，0不存在，1新品筛选，2非新品筛选
    //导出用到的字段
    public static array $export_keys = [];


    public function __construct($table)
    {
        self::$table = $table;
        $paras_list = array('country_code','currency_code','project_ids','yunying_ids','date_time','category_ids',"search_type","search_value",'aggregation_keys','order_by_key','order_by_type','is_new','page','page_size','hidden_detail','waring_id','waring_status','supplier_name','function_keys');
        $request_data = ['date_time'=>'时间区间'];
        $param = arrangeParam($_POST, $paras_list, $request_data);
        if (empty($param['search_type']) || empty($param['search_value'])) {
            $param['search_value'] = '';
            $param['search_type'] = '';
        }
        $param['date_time'] = json_decode($param['date_time']);
        //合并字段获取
        $param['aggregation_keys'] = empty($param['aggregation_keys'])?'[]':$param['aggregation_keys'];
        $aggregation_keys_list = json_decode($param['aggregation_keys']);
        self::$aggregation_keys = columnModel::getTableSearchKeys($aggregation_keys_list,self::$auth_key_);
        self::$page = empty($param['page'])?1:$param['page'];
        self::$page_size = empty($param['page_size'])?1:$param['page_size'];
        if (!count($param['date_time'])) {
            returnError('请选择时间区间');
        }
        //获取结账月份
        $db = dbFMysql::getInstance();
        self::$locked_month = tableDataForm::getDataMonth($db);
        //月份
        $search_month = [];
        //毛利润报表数据要获取近三年数据
        if (in_array(self::$real_table,self::$profit_table)) {
            $end_month = date('Y').'-12';
            $month = date("Y", strtotime("-2 years")).'-01';
            while (1) {
                if (strtotime($month) <= strtotime($param['date_time'][1])) {
                    $search_month[] = $month;
                    $month = date('Y-m',strtotime($month.' +1 month'));
                } else {
                    break;
                }
            }while (1) {
                if (strtotime($month) <= strtotime($end_month)) {
                    $search_month[] = $month;
                    $month = date('Y-m',strtotime($month.' +1 month'));
                } else {
                    break;
                }
            }
        } else {
            $month = $param['date_time'][0];
            if (self::$real_table == 'month_total') {
                //因为要计算环比
                $month = date('Y-m',strtotime($month.'-01 -1 month'));
                $param['date_time'][0] = $month;
            }
            while (1) {
                if (strtotime($month) <= strtotime($param['date_time'][1])) {
                    $search_month[] = $month;
                    $month = date('Y-m',strtotime($month.' +1 month'));
                } else {
                    break;
                }
            }
        }
        self::$search_month = $search_month;
        $all_years = [];
        //获取时间
        $years = [];
        $last_years = [];
        $month_count = count($search_month);
        foreach ($search_month as $m_date) {
            $year = date('Y',strtotime($m_date));
            $years[$year][] = $m_date;
            $all_years[] = $year;
            $last_year_month = date('Y-m',strtotime($m_date.'-01 -'.$month_count.' month'));
            $last_year =  date('Y',strtotime($last_year_month));
            $last_years[$last_year][] = $last_year_month;
            $all_years[] = $last_year;
        }
        //月度汇总去掉影藏标签搜索字段
        if (self::$real_table == 'month_total') {
            $param['hidden_detail'] = (int)$param['hidden_detail'];
        }
        //供应商利润统计，去掉影藏标签搜索字段和供应商名称数据
        if (self::$real_table == 'month_total') {
            $param['hidden_detail'] = (int)$param['hidden_detail'];
        }
        self::$years = $years;
        self::$last_years = $last_years;
        self::$param = $param;
        //储存查询结果的MD5
        if (self::$table == $param['search_type']) {
            $param['search_type'] = '';
            $param['search_value'] = '';
        }
        if (in_array(self::$table,self::$country_table)) {
            unset($param['country_code']);
        }
        $param['real_table'] = self::$real_table;
        unset($param['page']);
        unset($param['page_size']);
        unset($param['order_by_type']);
        unset($param['order_by_key']);
        if (isset($param['hidden_detail'])) {
            unset($param['hidden_detail']);
        }
        self::$param_md5 = MD5(json_encode($param));
        //建表
        $all_years = array_unique($all_years);
        foreach ($all_years as $year) {
            tableDataModel::creatTableMonth($year);
        }
        //获取要查的列（自定义和自定义计算需要的列）
        if (!(self::$table == 'waring_goods' || in_array(self::$real_table,self::$profit_table))) {
            self::getColumnAuth();
        }
        if (in_array(self::$real_table,self::$profit_table)) {
            self::$oa_keys = ['oa_key_4'];
            self::$oa_column_ids = [4];
            self::$lx_keys = customColumnForm::$aggregation_keys;
        }
        //获取可查询的数据的运营id
        list($yunying_ids,$project_ids,$show_all_data) = self::getYunyingIds();
        self::$yunying_ids = $yunying_ids;
        self::$project_ids = $project_ids;
        self::$show_all_data = $show_all_data;
    }
    //获取可查看数据（主管可以看所属项目组所有数据；组长可以看自己和组员的数据；组员只能看自己的数据）
    public static function getYunyingIds() {
        $project_ids = [];
        $yunying_ids = [];
        $show_all_data = false;
        /*
         * 1、项目负责人可以看项目下所有的数据，和自己所在的运营数据
         * 2、运营只能看自己的数据
         * */
        if (!(userModel::$is_super || in_array(1,userModel::$role_type))) {
            $db = dbFMysql::getInstance();
            //组长和负责人查询
            //user_id负责人，leader组长，user_ids组员，
            $project_list = $db->table('project')
                ->where('where user_id=:user_id or Leader like :leader or user_ids like :user_ids or JSON_OVERLAPS(other_user_ids, :json_user_id) ',['user_id'=>userModel::$qwuser_id,'leader'=>'%"'.userModel::$qwuser_id.'":%','user_ids'=>'%,'.userModel::$qwuser_id.',%','json_user_id'=>json_encode([userModel::$qwuser_id])])
                ->list();
            if (count($project_list)) {
                foreach ($project_list as $v) {
                    $leader = json_decode($v['Leader'],true);
                    //负责人（主管）
                    $fz_user_ids = json_decode($v['other_user_ids']);
                    $fz_user_ids[] = $v['user_id'];
                    if (in_array(userModel::$qwuser_id,$fz_user_ids)) {
                        //负责人可以看整个项目的
                        $project_ids[] = $v['id'];
                    }  elseif(isset($leader[userModel::$qwuser_id]))  {
                        //组长可以看组员的
                        $user_ids = $leader[userModel::$qwuser_id];
                        $yunying_ids[$v['id']] = $user_ids;
                    } else {
                        $yunying_ids[$v['id']] = [userModel::$qwuser_id];
                    }
                }
            }
        } else {
            $show_all_data = true;
        }
        return [$yunying_ids,$project_ids,$show_all_data];
    }
    //获取要查询的数据
    public static function getColumnAuth() {
        $db = dbFMysql::getInstance();
        //获取权限字段
        $list_auth = json_decode(userModel::$list_auth,true);
        $list_auth_ = array_column($list_auth,'key','name');
        $auth_keys = $list_auth_[self::$auth_key_]??[];
        //看看是否为导出
        if (count(self::$export_keys)) {
            $auth_keys = array_intersect($auth_keys,self::$export_keys);
        } else {
            //方案的使用
            $fang_an = $db->table('table_custom_setting')
                ->where('where user_id=:user_id and is_default = 1 and table_type=:table_type and is_delete = 0', ['user_id' => userModel::$qwuser_id, 'table_type' => self::$auth_key_])
                ->field('custom_keys')
                ->one();
            if ($fang_an) {
                $custom_keys = json_decode($fang_an['custom_keys']);
                $auth_keys = array_intersect($auth_keys, $custom_keys);
            }
        }
        //加上合并计算字段
        if (count(self::$aggregation_keys)) {
            $auth_keys = array_merge($auth_keys,self::$aggregation_keys);
        }
        $db->table('column')
            ->where('where is_delete = 0 and table_index <> 0')
            ->field('key_name,column_name,table_index,show_type,custom_id,data_type')
            ->whereIn('key_name',$auth_keys)
            ->order('data_type');
        //月度汇总只计算金额字段
        if (self::$real_table == 'month_total') {
            $db->andWhere('show_type <> 2');
        } else {
            //汇总不要百分比字段
            if (!self::$show_rate_column) {
                $db->andWhere('show_type <> 2');
            }
        }
        $list = $db->list();
        //要显示的字段
        self::$function_keys = array_column($list,'key_name');
        $lx_keys = [];
        $oa_column_ids = [];
        $currency_keys = [];
        $oa_keys = [];
        $column_list = [];
        $all_keys = [];
        $column_type = self::getColumnTypeList();
        foreach ($list as $v) {
            $column_list[$v['key_name']] = [
                'key_name'=>$v['key_name'],
                'column_name'=>$v['column_name'],
                'show_type'=>$v['show_type'],
                'data_type'=>$v['data_type'],
                'type_name'=>$column_type[$v['show_type']]??'',
                'val'=>0,
            ];
            $all_keys[] = $v['key_name'];
            if ($v['custom_id'] > 0) {
                $oa_column_ids[] = $v['custom_id'];
                $oa_keys[] = $v['key_name'];
            } else {
                $lx_keys[] = $v['key_name'];
            }
            if ($v['show_type'] == 1) {
                $currency_keys[] = $v['key_name'];
            }
        }
        //查询自定义字段需要的列（只用找到百分比字段的自定义列，自定义的列都是通过领星列算好的）
        if (count($oa_column_ids)) {
            $custom_column = $db->table('custom_column')
                ->whereIn('id',$oa_column_ids)
                ->field('id,relation_column,last_relation_column,show_type,rules,sort')
                ->list();
            //本月字段(只看百分比的字段，其余字段都是提前算好的)
            $relation_column = [];
            $last_relation_column = [];
            if ($custom_column) {
                foreach ($custom_column as $v) {
                    if ($v['show_type'] == 2 || $v['id'] == 5 || in_array($v['sort'],[5,6])) {
                        self::$need_again_oa_keys2['oa_key_'.$v['id']] = $v;
                    }
                    if ($v['last_relation_column'] != '[]') {
                        self::$need_last_data = 1;
                        $last_relation_column = array_unique(array_merge($last_relation_column,json_decode($v['last_relation_column'])));
                    }
                    if ($v['relation_column'] != '[]') {
                        $relation_column = array_unique(array_merge($relation_column,json_decode($v['relation_column'])));
                    }
                }
            }
            if (count($relation_column)) {
                //oa字段
                $oa_relation = array_filter($relation_column, function($item) {
                    return strpos($item, 'oa_key_') !== false;
                });
                //领星字段
                $lx_relation = array_filter($relation_column, function($item) {
                    return strpos($item, 'oa_key_') === false;
                });
                if (count($lx_relation)) {
                    $lx_keys = array_unique(array_merge($lx_keys,$lx_relation));
                }
                if (count($oa_relation)) {
                    $oa_keys = array_unique(array_merge($oa_keys,$oa_relation));
                    foreach ($oa_relation as $v) {
                        $v_arry = explode('_',$v);
                        $oa_column_ids[] = end($v_arry);
                    }
                    $oa_column_ids = array_unique($oa_column_ids);
                }
            }
            if (count($last_relation_column)) {
                //oa字段
                $lx_last_keys = [];
                $oa_last_ids = [];
                foreach ($last_relation_column as $kk) {
                    if (strpos($kk, 'oa_key_') === false) {
                        $lx_last_keys[] = $kk;
                    } else {
                        $v_arry = explode('_',$v);
                        $oa_last_ids[] = end($v_arry);
                    }
                }
                self::$lx_last_keys = array_unique($lx_last_keys);
                self::$oa_last_ids = array_unique($oa_last_ids);
                self::$last_all_keys = array_unique($last_relation_column);
            }
        }
        self::$column_list = $column_list;
        self::$oa_keys = $oa_keys;
        self::$all_keys = $all_keys;
        self::$lx_keys = $lx_keys;
        self::$currency_keys = $currency_keys;
        self::$oa_column_ids = $oa_column_ids;
    }

    /**
     * @param $field
     * @param $field_u
     * @param $group_by
     * @param $waring_array
     * @param $is_last_month 0查筛选数据，1查筛选的上月数据
     * @return array
     * @throws \core\lib\ExceptionError  获取查询条件(领星原表,所有数据)
     */
    public static function getSqlWhereList($field,$field_u,$group_by,$waring_array = [],$is_last_month = 0) {
        $group_by_1 = $field;
        if (!self::$show_all_data) {
            if (count(self::$yunying_ids) == 0 && count(self::$project_ids) == 0) {
                return [];
            }
        }
        if (!count(self::$locked_month)) {
            return [];
        }
        $db = dbFMysql::getInstance();
        $param = self::$param;
        if (self::$real_table != 'goods_sku') {
            self::getGoodsSku($db);
        }
        $sku_list = self::$sql_sku_list;
        $user_sku = self::$sql_use_sku;
        //新品查询
        if (self::$table == 'asin') {
            self::getNewGoodsWhere($db);
        }
        //搜索的字段
        if ($is_last_month) {
            if (count(self::$lx_last_keys)) {
                foreach (self::$lx_last_keys as $w_k) {
                    $field_u[] = "sum($w_k) as $w_k";
                    $field[] = "sum($w_k) as $w_k";
                }
            } else {
                return [];
            }
        } else {
            if (count(self::$lx_keys)) {
                foreach (self::$lx_keys as $w_k) {
                    $field_u[] = "sum($w_k) as $w_k";
                    $field[] = "sum($w_k) as $w_k";
                }
            }
        }
        $field = array_unique($field);
        $field_u = array_unique($field_u);
        $field_u_string = implode(',',$field_u);
        $field_string = implode(',',$field);
        //获取数据
        $sql_list = [];
        $years = $is_last_month?self::$last_years:self::$years;
        foreach ($years as $year=>$m_date_list) {
            $m_date_list = array_intersect(self::$locked_month,$m_date_list);
            $db->table('table_month_count_'.$year)
                ->where('where is_delete=0')
                ->whereIn('reportDateMonth',$m_date_list);
            if (self::$table == 'waring_goods') {
                $db->whereIn('localSku',$waring_array['sku']);
                $country_codes = $waring_array['country_code'];
                //国家
                if (!empty($param['country_code']) && $param['country_code'] != '[]') {
                    $country_codes_ = json_decode($param['country_code']);
                    $country_codes = array_intersect($country_codes,$country_codes_);
                }
                $db->whereIn('countryCode',$country_codes);
            } else {
                //国家
                if (!empty($param['country_code']) && $param['country_code'] != '[]') {
                    $country_codes = json_decode($param['country_code']);
                    $db->whereIn('countryCode',$country_codes);
                }
            }
            //项目
            if (!empty($param['project_ids']) && $param['project_ids']!='[]') {
                $project_ids = json_decode($param['project_ids']);
                if (!self::$show_all_data) {
                    $project_ids = array_intersect(self::$project_ids,$project_ids);
                }
                $db->whereIn('project_id',$project_ids);
            }
            //权限运营+搜索
            $user_id = userModel::$qwuser_id;
            if (!empty($param['yunying_ids']) && $param['yunying_ids']!='[]') {
                if (!self::$show_all_data) {
                    $yunying_ids = array_intersect(self::$yunying_ids,json_decode($param['yunying_ids']));
                    $yunying_str = getYunyingSql($yunying_ids,self::$project_ids,$user_id);
                    $db->andWhere($yunying_str);
                } else {
                    $db->whereIn('yunying_id',json_decode($param['yunying_ids']));
                }
            } else {
                if (!self::$show_all_data) {
                    $yunying_str = getYunyingSql(self::$yunying_ids,self::$project_ids,$user_id);
                    $db->andWhere($yunying_str);
                }
            }
            //自定义搜索
            if (!empty($param['search_type']) && !empty($param['search_value'])) {
                if ($param['search_type'] == 'asin') {
                    $db->andWhere('asin like :asin',['asin'=>"%{$param['search_value']}%"]);
                }elseif ($param['search_type'] == 'sku') {
                    $db->andWhere('localSku like :sku',['sku'=>"%{$param['search_value']}%"]);
                } elseif ($param['search_type'] == 'p_asin') {
                    $db->andWhere('parentAsin like :parentAsin',['parentAsin'=>"%{$param['search_value']}%"]);
                }
            }
            if ($user_sku) {
                if ($user_sku == 1) {
                    $db->whereIn('localSku',$sku_list);
                } else {
                    $db->whereIn('localSku not',$sku_list);
                }
            }
            if (self::$sql_use_new_goods) {
                $array_ = self::$sql_new_goods_data;
                if (self::$sql_use_new_goods == 2) {
                    //非新品查询
                    $db->whereInMuch(['asin','countryCode'],$array_);
                } else {
                    $db->whereInMuch(['asin','countryCode'],$array_,'not');
                }
            }
//            dd($db->getSql());
            $db->field($field_string)->groupBy($group_by_1);
            $sql_list[] = $db->getSql();
        }
        $mew_tabel = implode(' UNION ALL ',$sql_list);
        $db->tablep($mew_tabel,'un_table')
            ->field($field_u_string)
            ->groupBy($group_by);
        if (self::$real_table == 'supplier_sku') {
//            $db->andWhere('supplier_id > 0')
            $db->order('supplier_id asc');
        }
        $report_data = $db->list();
        return $report_data;
    }

    /**
     * @param $field
     * @param $field_u
     * @param $group_by
     * @param $table_list
     * @param $is_last_month 0查筛选数据，1查筛选的上月数据
     * @return array
     * @throws \core\lib\ExceptionError
     */
    public static function getSqlWhereForOa($field,$field_u,$group_by,$table_list,$is_last_month = 0) {
        if (!self::$show_all_data) {
            if (count(self::$yunying_ids) == 0 && count(self::$project_ids) == 0) {
                return [];
            }
        }
        if ($is_last_month && count(self::$oa_last_ids) == 0) {
            return [];
        }
        if (!count(self::$locked_month)) {
            return [];
        }
        $param = self::$param;
        $db = dbFMysql::getInstance();
        //查询可用的sku
        $sku_list = self::$sql_sku_list;
        $user_sku = self::$sql_use_sku;
        //获取数据
        $sql_list = [];
        $field_string = implode(',',$field);
        $field_u_string = implode(',',$field_u);
        $years = $is_last_month?self::$last_years:self::$years;
        $oa_column_ids = $is_last_month?self::$oa_last_ids:self::$oa_column_ids;
        foreach ($years as $year=>$m_date_list) {
//            if (!in_array(self::$real_table,['goods_sku'])) {
                $m_date_list = array_intersect(self::$locked_month,$m_date_list);
//            }
            if (!count($m_date_list)) {
                continue;
            }
            //获取数据
            $db->table("custom_val_$year")
                ->whereIn('m_date',$m_date_list)
                ->whereIn('custom_id',$oa_column_ids);
            if ($table_list !== false) {
                $db->whereIn(self::$table,$table_list);
            }
            //国家
            if (!empty($param['country_code']) && $param['country_code'] != '[]') {
                $country_codes = json_decode($param['country_code']);
                $db->whereIn('country_code',$country_codes);
            }
            //项目
            if (!empty($param['project_ids']) && $param['project_ids']!='[]') {
                $project_ids = json_decode($param['project_ids']);
                if (!self::$show_all_data) {
                    $project_ids = array_intersect(self::$project_ids,$project_ids);
                }
                $db->whereIn('project_id',$project_ids);
            }
            //权限运营+搜索
            $user_id = userModel::$qwuser_id;
            if (!empty($param['yunying_ids']) && $param['yunying_ids']!='[]') {
                if (!self::$show_all_data) {
                    $yunying_ids = array_intersect(self::$yunying_ids,json_decode($param['yunying_ids']));
                    $yunying_str = getYunyingSql($yunying_ids,self::$project_ids,$user_id);
                    $db->andWhere($yunying_str);
                } else {
                    $db->whereIn('yunying_id',json_decode($param['yunying_ids']));
                }
            } else {
                if (!self::$show_all_data) {
                    $yunying_str = getYunyingSql(self::$yunying_ids,self::$project_ids,$user_id);
                    $db->andWhere($yunying_str);
                }
            }
            //自定义搜索
            if (!empty($param['search_type']) && !empty($param['search_value'])) {
                if ($param['search_type'] == 'asin') {
                    $db->andWhere('asin like :asin',['asin'=>"%{$param['search_value']}%"]);
                }elseif ($param['search_type'] == 'sku') {
                    $db->andWhere('sku like :sku',['sku'=>"%{$param['search_value']}%"]);
                } elseif ($param['search_type'] == 'p_asin') {
                    $db->andWhere('p_asin like :p_asin',['p_asin'=>"%{$param['search_value']}%"]);
                }
            }
            if ($user_sku) {
                if ($user_sku == 1) {
                    $db->whereIn('sku',$sku_list);
                } else {
                    $db->whereIn('sku not',$sku_list);
                }
            }
            if (self::$sql_use_new_goods) {
                $array_ = self::$sql_new_goods_data;
                if (self::$sql_use_new_goods == 2) {
                    //非新品查询
                    $db->whereInMuch(['asin','country_code'],$array_);
                } else {
                    $db->whereInMuch(['asin','country_code'],$array_,'not');
                }
            }
            $db->field($field_string)
                ->groupBy($group_by);
            $sql_list[] = $db->getSql();
        }
        $mew_tabel = implode(' UNION ALL ',$sql_list);
        $db->tablep($mew_tabel,'oa_un_table')
            ->field($field_u_string)
            ->groupBy($group_by);
        $oa_data = $db->list();

        return $oa_data;
    }
    //获取预警产品的信息
    public static function getWaringGoodsSku() {
        $db = dbFMysql::getInstance();
        $db->table('goods_waring')
            ->whereIn('m_date',self::$search_month)
            ->where('where is_delete = 0');
        if (self::$param['waring_id']) {
            $db->andWhere('waring_id = :waring_id',['waring_id'=>self::$param['waring_id']]);
        }
        $list = $db->field('id,sku,waring_id,waring_name,status,created_time,m_date,country_code,rules,reason_txt')
            ->list();
        $sku_array = array_unique(array_column($list,'sku'));
        $country_code_array = array_unique(array_column($list,'country_code'));
        return ['sku'=>array_values($sku_array),'country_code'=>array_values($country_code_array),'list'=>$list];
    }

    /**
     * @param $list
     * @param $type 2导出，1页面table
     * @param $last_list 上月数据
     * @return array 排序，分页，合并计算，币种整理
     */
    public static function getPageList($list, $type = 1,$last_list =[])
    {
        $count = count($list);
        //合并数据加入
        if (count(self::$aggregation_keys)) {
            foreach ($list as $k => $v) {
                $total = array_sum(array_intersect_key($v,array_flip(self::$aggregation_keys)));
                if (isset($v['oa_key_4'])) {
                    $list[$k]['oa_key_4'] = roundToString($total+$list[$k]['oa_key_4']);
                }
            }
        }
        //计算比例字段
        if (count(self::$need_again_oa_keys2)) {
            foreach ($list as $k => $row_) {
                $last_row = $last_list[$k]??[];
                foreach ($row_ as $key_ => $val) {
                    if (isset(self::$need_again_oa_keys2[$key_])) {
                        $rule = self::$need_again_oa_keys2[$key_];
                        //计算数据
                        $rule_data = json_decode($rule['rules'], true)[0]['rules'];
                        $show_type = $rule['show_type'];
                        foreach ($rule_data as &$rule_) {
                            if ($rule_['group_type'] == 1) {
                                $rule_['val'] = self::getValRow($rule_, $row_,$last_row);
                            } else {
                                foreach ($rule_['list'] as &$rule_l) {
                                    $rule_l['val'] = self::getValRow($rule_l, $row_,$last_row);
                                }
                            }
                        }
                        if ($show_type == 2) {
                            $list[$k][$key_] = roundToString(coustomColumnJobForm::getValue($rule_data) * 100);
                        } else {
                            $list[$k][$key_] = roundToString(coustomColumnJobForm::getValue($rule_data));
                        }
                    }
                }
            }
        }

        //排序
        $order_by_key = self::$param['order_by_key'] ?? '';
        if (in_array($order_by_key,self::$function_keys)){
            if (self::$param['order_by_type'] == 1) {
                //正序
                usort($list, fn($a, $b) => $a[$order_by_key] <=> $b[$order_by_key]);
            } else {
                usort($list, fn($a, $b) => $b[$order_by_key] <=> $a[$order_by_key]);
            }
        }
        //分页
        if ($type != 2) { // 当 type 不为 2 时执行分页逻辑
            $offset = (self::$param['page'] - 1) * self::$param['page_size'];
            $list_ = array_slice($list, $offset, self::$param['page_size']);
        } else {
            $list_ = $list; // 当 type 为 2 时保留完整列表
        }
        //获取基础字段
        $list_ = self::getListBascData($list_);
        //整理只显示的字段
        $new_list = [];
        $keys = array_column(self::$column_list, 'key_name');
        foreach ($list_ as $v) {
            $new_row = [];
            foreach (authListModel::$base_auth as $vv) {
                $new_row[$vv['key']] = $v[$vv['key']];
            }
            $new_row['country'] = $v['country'];
            if (self::$table == 'p_asin') {
                $new_row['asin_list'] = $v['asin_list'] ?? [];
            }
            if (self::$table == 'yunying_id') {
                $new_row['project'] = $v['project'] ?? [];
            }
            foreach ($v as $key => $val) {
                if (in_array($key, $keys)) {
                    $new_row[$key] = $val;
                }
            }
            $new_list[] = $new_row;
        }
        //整理总计数据
        $total_list = [];
        $last_total_list = [];
        $all_keys = array_merge(self::$lx_keys,self::$oa_keys);
        foreach ($all_keys as $key) {
            $total_list[$key] = roundToString(array_sum(array_column($list, $key)));
        }
        if (count($last_list)) {
            foreach (self::$last_all_keys as $key) {
                $last_total_list[$key] = roundToString(array_sum(array_column($last_list, $key)));
            }
        }
        //一些需要重新计算的字段就重新算
        if (count(self::$need_again_oa_keys2)) {
            foreach (self::$need_again_oa_keys2 as $key_=>$rule) {
                if (isset($total_list[$key_])) {
                    $rule_data = json_decode($rule['rules'], true)[0]['rules'];
                    $show_type = $rule['show_type'];
                    foreach ($rule_data as &$rule_) {
                        if ($rule_['group_type'] == 1) {
                            $rule_['val'] = self::getValRow($rule_, $total_list,$last_total_list);
                        } else {
                            foreach ($rule_['list'] as &$rule_l) {
                                $rule_l['val'] = self::getValRow($rule_l, $total_list,$last_total_list);
                            }
                        }
                    }
                    if ($show_type == 2) {
                        $total_list[$key_] = roundToString(coustomColumnJobForm::getValue($rule_data) * 100);
                    } else {
                        $total_list[$key_] = roundToString(coustomColumnJobForm::getValue($rule_data));
                    }
                }
            }
        }
        list($total_list) = self::getPriceByNewRoute([$total_list],self::$param['currency_code'],self::$all_keys,self::$currency_keys,self::$search_month);

        $total_list = array_intersect_key($total_list,array_flip($keys));
        return [
            'count' => $count,
            'page_count' => count($new_list),
            'page' => self::$param['page'],
            'page_size' => self::$param['page_size'],
            'list' => $new_list,
            'total_data' => $total_list,
        ];
    }
    //利率贡献汇总
    private static function getSupplierAmount($data) {
        // 初始化一个新数组来存储累加的结果
        $result = [];
        $year_data = [];
        $month_data = [];

        // 遍历列表中的每个元素
        foreach ($data as $item) {
            // 累加 year_data
            if (isset($item['year_data'])) {
                foreach ($item['year_data'] as $key => $value) {
                    if (!isset($year_data[$key])) {
                        $year_data[$key] = 0.00; // 初始化键为0.00
                    }
                    $year_data[$key] = round($year_data[$key] + $value, 2); // 四舍五入到小数点后两位
                }
            }
            // 累加 month_data
            if (isset($item['month_data'])) {
                foreach ($item['month_data'] as $key => $value) {
                    if (!isset($month_data[$key])) {
                        $month_data[$key] = 0.00; // 初始化键为0.00
                    }
                    $month_data[$key] = round($month_data[$key] + $value, 2); // 四舍五入到小数点后两位
                }
            }
        }
        $result['year_data'] = $year_data;
        $result['month_data'] = $month_data;

        // 返回结果
        return $result;
    }
    //国家筛选，运营，项目，排序，分页，合并计算，币种整理(预警)
    public static function getWaringPageList($list,$waring_list,$type) {
        //1已处理，2未处理筛选
        $waring_status = (int)self::$param['waring_status'];
        if ($waring_status) {
            $waring_list = array_filter($waring_list, function ($row) use($waring_status) {
                return $row['status'] == $waring_status;
            });
        }
        //按照asin+国家+预警类型分组+月份
        $new_list = [];
        foreach ($waring_list as $v) {
            $sku = $v['sku'];
            $m_data = $v['m_date'];
            $country_code = $v['country_code'];
            $waring_id = $v['waring_id'];
            $item = [
                'goods_waring_id'=>$v['id'],
                'm_date'=>$v['m_date'],
                'waring_name'=>$v['waring_name'],
                'waring_time'=>$v['created_time'],
                'waring_status'=>$v['status'],
            ];
            if ($type == 2) {
                $item['waring_rules'] = $v['rules'];
                $item['waring_reason_txt'] = $v['reason_txt'];
            }
            foreach ($list as $v1) {
                $key_ = $m_data.'_'.$v1['asin'].'_'.$country_code.'_'.$waring_id;
                if ($v1['country_code'] == $country_code && $sku == $v1['sku']) {
                    $item = array_merge($item,$v1);
                    $new_list[$key_][] = $item;
                    break;
                }
            }
        }
        $new_list = array_values($new_list);
        //分页
        if ($type != 2) { // 当 type 不为 2 时执行分页逻辑
            $offset = (self::$param['page'] - 1)*self::$param['page_size'];
            $list_ = array_slice($new_list,$offset,self::$param['page_size']);
        } else {
            $list_ = $new_list; // 当 type 为 2 时保留完整列表
        }
        return [
            'count'=>count($new_list),
            'page_count'=>count($list_),
            'page'=>self::$param['page'],
            'page_size'=>self::$param['page_size'],
            'list'=>$list_,
        ];
    }
    //供应商利润贡献汇总(详情)
    public static function getSupplierGoodsList($db,$list) {
        if (!empty(self::$param['supplier_name'])) {
            $db->table('supplier')
                ->where('where supplier_name like :supplier_name',['supplier_name'=>self::$param['supplier_name']])
                ->field('id,supplier_name');
            $supplier_list = $db->list();
            if (count($supplier_list)) {
                $supplier_ids = array_column($supplier_list,'id');
                $supplier_ids[] = 0;
                $list = array_filter($list, function ($row) use ($supplier_ids) {
                    return in_array($row['supplier_id'],$supplier_ids);
                });
            } else {
                $list = [];
            }
        }
        //按照供应商排序
        usort($list, fn($a, $b) => $a['supplier_id'] <=> $b['supplier_id']);
        //按照供应商和sku整理数据
        $new_list = [];
        foreach ($list as $v) {
            $key = $v['supplier_id']."_".$v['sku'];
            if (!isset($new_list[$key])) {
                $new_list[$key]['supplier_id'] = $v['supplier_id'];
                $new_list[$key]['sku'] = $v['sku'];
            }
            $new_list[$key][$v['m_date']] = $v;
        }
        $s_list = $new_list;
        $sku_list = [];
        foreach ($s_list as $v) {
            $sku_list = array_merge($sku_list,array_column($v,'sku'));
        }
        $sku_list = array_unique($sku_list);
        //整理供应商，产品信息
        $goods_list = $db->table('goods')
            ->whereIn('sku',$sku_list)
            ->field('sku,supplier_name,product_name,category_name')
            ->list();
        $goods_ = [];
        foreach ($goods_list as $v) {
            $goods_[$v['sku']] = $v;
        }
        //显示的月份
        $search_month = [];
        $month = self::$param['date_time'][0];
        while (1) {
            if (strtotime($month) <= strtotime(self::$param['date_time'][1])) {
                $search_month[] = $month;
                $month = date('Y-m',strtotime($month.' +1 month'));
            } else {
                break;
            }
        }
        //显示的年份
        $years = array_keys(self::$years);
        arsort($years);
        arsort($search_month);
        //先获取年数据
        $new_list1 = [];
        foreach ($s_list as $k=>$v) {
            $supplier_id = $v['supplier_id'];
            $sku = $v['sku'];
            $new_item = [
                'supplier_id'=>$supplier_id,
                'sku'=>$sku,
                'supplier_name'=>isset($goods_[$sku])?$goods_[$sku]['supplier_name']:'',
                'product_name'=>isset($goods_[$sku])?$goods_[$sku]['product_name']:'',
                'category_name'=>isset($goods_[$sku])?$goods_[$sku]['category_name']:'',
            ];
            $year_amount = 0;
            $year_data = [];
            foreach ($years as $year) {
                //获取符合年限的数据
                $year_data_list = array_filter($list, function ($row) use($year,$supplier_id,$sku) {
                    return $row['supplier_id']==$supplier_id && $row['year'] == $year && $row['sku'] == $sku;
                });
                //毛利润获取
                $year_total = array_sum(array_column($year_data_list,'oa_key_4'));
                foreach (self::$aggregation_keys as $kes_) {
                    $key_total = array_sum(array_column($year_data_list,$kes_));
                    $year_total += $key_total;
                }
                $year_data[$year] = roundToString($year_total);
                $year_amount += $year_total;
            }
            $year_data['year_amount'] = roundToString($year_amount);
            $new_item['year_data'] = $year_data;
            $new_list1[$k] = $new_item;
        }
        //月数据获取
        foreach ($s_list as $k=>$v) {
            $new_item = [];
            $month_amount = 0;
            foreach ($search_month as $month) {
                $month_total = 0;
                if (isset($v[$month])) {
                    //毛利润获取
                    $month_total = $v[$month]['oa_key_4'];
                    foreach (self::$aggregation_keys as $kes_) {
                        $month_total += $v[$month][$kes_];
                    }
                }
                $new_item[$month] = roundToString($month_total);
                $month_amount += $month_total;
            }
            $new_item['month_amount'] = roundToString($month_amount);
            $new_list1[$k]['month_data'] = $new_item;
        }
        $new_list1 = array_values($new_list1);
        $result = self::getSupplierAmount($new_list1);
        return $result;
    }
    //供应商利润贡献,分页(详情)
    public static function getSupplierGoodsPageList($list,$type) {
        $db = dbFMysql::getInstance();
        //获取汇总数
//        $rulst = self::getSupplierGoodsList($db,$list);
        $rulst = self::getSupplierAggregation($db,$list);
//        if (userModel::$qwuser_id == 212) {
//            dd(99);
//        }
        if (!empty(self::$param['supplier_name'])) {
            $db->table('supplier')
                ->where('where supplier_name like :supplier_name',['supplier_name'=>self::$param['supplier_name']])
                ->field('id,supplier_name');
            $supplier_list = $db->list();
            if (count($supplier_list)) {
                $supplier_ids = array_column($supplier_list,'id');
                $list = array_filter($list, function ($row) use ($supplier_ids) {
                    return in_array($row['supplier_id'],$supplier_ids);
                });
            } else {
                $list = [];
            }
        }
        //按照供应商排序
        usort($list, fn($a, $b) => $a['supplier_id'] <=> $b['supplier_id']);
        //按照供应商和sku整理数据
        $new_list = [];
        foreach ($list as $v) {
            $key = $v['supplier_id']."_".$v['sku'];
            if (!isset($new_list[$key])) {
                $new_list[$key]['supplier_id'] = $v['supplier_id'];
                $new_list[$key]['sku'] = $v['sku'];
            }
            $new_list[$key][$v['m_date']] = $v;
        }
        $offset = (self::$param['page'] - 1)*self::$param['page_size'];
        //分页
        if ($type != 2) { // 当 type 不为 2 时执行分页逻辑
            $s_list = array_slice($new_list,$offset,self::$param['page_size']);
            $sku_list = [];
        } else {
            $s_list = $new_list;
            $sku_list = [];
        }
        foreach ($s_list as $v) {
            $sku_list = array_merge($sku_list,array_column($v,'sku'));
        }
        $sku_list = array_unique($sku_list);
        //整理供应商，产品信息
        $goods_list = $db->table('goods')
            ->whereIn('sku',$sku_list)
            ->field('sku,supplier_name,product_name,category_name')
            ->list();
        $goods_ = [];
        foreach ($goods_list as $v) {
            $goods_[$v['sku']] = $v;
        }
        //显示的月份
        $search_month = [];
        $month = self::$param['date_time'][0];
        while (1) {
            if (strtotime($month) <= strtotime(self::$param['date_time'][1])) {
                $search_month[] = $month;
                $month = date('Y-m',strtotime($month.' +1 month'));
            } else {
                break;
            }
        }
        //显示的年份
        $years = array_keys(self::$years);
        arsort($years);
        arsort($search_month);
        //先获取年数据
        $new_list1 = [];
        foreach ($s_list as $k=>$v) {
            $supplier_id = $v['supplier_id'];
            $sku = $v['sku'];
            $new_item = [
                'supplier_id'=>$supplier_id,
                'sku'=>$sku,
                'supplier_name'=>isset($goods_[$sku])?$goods_[$sku]['supplier_name']:'',
                'product_name'=>isset($goods_[$sku])?$goods_[$sku]['product_name']:'',
                'category_name'=>isset($goods_[$sku])?$goods_[$sku]['category_name']:'',
            ];
            $year_amount = 0;
            $year_data = [];
            foreach ($years as $year) {
                //获取符合年限的数据
                $year_data_list = array_filter($list, function ($row) use($year,$supplier_id,$sku) {
                    return $row['supplier_id']==$supplier_id && $row['year'] == $year && $row['sku'] == $sku;
                });
                //毛利润获取
                $year_total = array_sum(array_column($year_data_list,'oa_key_4'));
                foreach (self::$aggregation_keys as $kes_) {
                    $key_total = array_sum(array_column($year_data_list,$kes_));
                    $year_total += $key_total;
                }
                $year_data[$year] = roundToString($year_total);
                $year_amount += $year_total;
            }
            $year_data['year_amount'] = roundToString($year_amount);
            $new_item['year_data'] = $year_data;
            $new_list1[$k] = $new_item;
        }
        //月数据获取
        foreach ($s_list as $k=>$v) {
            $new_item = [];
            $month_amount = 0;
            foreach ($search_month as $month) {
                $month_total = 0;
                if (isset($v[$month])) {
                    //毛利润获取
                    $month_total = $v[$month]['oa_key_4'];
                    foreach (self::$aggregation_keys as $kes_) {
                        $month_total += $v[$month][$kes_];
                    }
                }
                $new_item[$month] = roundToString($month_total);
                $month_amount += $month_total;
            }
            $new_item['month_amount'] = roundToString($month_amount);
            $new_list1[$k]['month_data'] = $new_item;
        }
        $new_list1 = array_values($new_list1);

        return [
            'count'=>count($new_list),
            'page_count'=>count($s_list),
            'page'=>self::$param['page'],
            'page_size'=>self::$param['page_size'],
            'list'=>$new_list1,
            'result'=>$rulst,
        ];
    }
    //获取汇总数
    public static function getSupplierAggregation($db,$list) {
        $db->table('supplier')
            ->field('id,supplier_name');
        if (!empty(self::$param['supplier_name'])) {
            $db->where('where supplier_name like :supplier_name',['supplier_name'=>self::$param['supplier_name']]);
        }
        $supplier_list = $db->list();
        $s_list = $supplier_list;
//        $supplier_ids = array_column($s_list,'id');
        //跟上空的
        //筛选出能用的数据
//        $list = array_filter($list, function ($row) use($supplier_ids) {
//            return in_array($row['supplier_id'],$supplier_ids);
//        });
        //显示的月份
        $search_month = [];
        $month = self::$param['date_time'][0];
        while (1) {
            if (strtotime($month) <= strtotime(self::$param['date_time'][1])) {
                $search_month[] = $month;
                $month = date('Y-m',strtotime($month.' +1 month'));
            } else {
                break;
            }
        }
        //显示的年份
        $years = array_keys(self::$years);
        arsort($years);
        arsort($search_month);
        //先获取年数据
        foreach ($s_list as $k=>$v) {
            $supplier_id = $v['id'];
            $year_amount = 0;
            $year_itme = [];
            foreach ($years as $year) {
                //获取符合年限的数据
                $year_data_list = array_filter($list, function ($row) use($year,$supplier_id) {
                    return $row['supplier_id']==$supplier_id && $row['year'] == $year;
                });
                //毛利润获取
                $year_total = array_sum(array_column($year_data_list,'oa_key_4'));
                foreach (self::$aggregation_keys as $kes_) {
                    $key_total = array_sum(array_column($year_data_list,$kes_));
                    $year_total += $key_total;
                }
                $year_itme[$year] = roundToString($year_total);
                $year_amount += $year_total;
            }
            $year_itme['year_amount'] = roundToString($year_amount);
            $s_list[$k]['year_data'] = $year_itme;
        }
        //月数据获取
        foreach ($s_list as $k=>$v) {
            $supplier_id = $v['id'];
            $month_amount = 0;
            $month_itme = [];
            foreach ($search_month as $month) {
                //获取符合年限的数据
                $month_data_list = array_filter($list, function ($row) use($month,$supplier_id) {
                    return $row['supplier_id']==$supplier_id && $row['m_date'] == $month;
                });
                //毛利润获取
                $month_total = array_sum(array_column($month_data_list,'oa_key_4'));
                foreach (self::$aggregation_keys as $kes_) {
                    $key_total = array_sum(array_column($month_data_list,$kes_));
                    $month_total += $key_total;
                }
                $month_itme[$month] = roundToString($month_total);
                $month_amount += $month_total;
            }
            $month_itme['month_amount'] = roundToString($month_amount);
            $s_list[$k]['month_data'] = $month_itme;
        }
        $result = self::getSupplierAmount($s_list);
        return $result;
    }
    //供应商利润贡献,分页(汇总)
    public static function getSupplierPageList($list,$type) {
        $db = dbFMysql::getInstance();
        //获取汇总数
        $rulst = self::getSupplierAggregation($db,$list);
        $db->table('supplier')
            ->field('id,supplier_name');
        if (!empty(self::$param['supplier_name'])) {
            $db->where('where supplier_name like :supplier_name',['supplier_name'=>self::$param['supplier_name']]);
        }
        //分页
        if ($type != 2) { // 当 type 不为 2 时执行分页逻辑
            $supplier_list = $db->pages(self::$param['page'],self::$param['page_size']);
            $supplier_list[] = ['id'=>0,'supplier_name'=>'无'];
            $s_list = $supplier_list['list'];
        } else {
            $supplier_list = $db->list();
            $s_list = $supplier_list;
        }
        $supplier_ids = array_column($s_list,'id');
        //筛选出能用的数据
        $list = array_filter($list, function ($row) use($supplier_ids) {
            return in_array($row['supplier_id'],$supplier_ids);
        });
//        if (userModel::$qwuser_id == 212) {
//            dd($list);
//        }
        //显示的月份
        $search_month = [];
        $month = self::$param['date_time'][0];
        while (1) {
            if (strtotime($month) <= strtotime(self::$param['date_time'][1])) {
                $search_month[] = $month;
                $month = date('Y-m',strtotime($month.' +1 month'));
            } else {
                break;
            }
        }
        //显示的年份
        $years = array_keys(self::$years);
        arsort($years);
        arsort($search_month);
        //先获取年数据
        foreach ($s_list as $k=>$v) {
            $supplier_id = $v['id'];
            $year_amount = 0;
            $year_itme = [];
            foreach ($years as $year) {
                //获取符合年限的数据
                $year_data_list = array_filter($list, function ($row) use($year,$supplier_id) {
                    return $row['supplier_id']==$supplier_id && $row['year'] == $year;
                });
                //毛利润获取
                $year_total = array_sum(array_column($year_data_list,'oa_key_4'));
                foreach (self::$aggregation_keys as $kes_) {
                    $key_total = array_sum(array_column($year_data_list,$kes_));
                    $year_total += $key_total;
                }
                $year_itme[$year] = roundToString($year_total);
                $year_amount += $year_total;
            }
            $year_itme['year_amount'] = roundToString($year_amount);
            $s_list[$k]['year_data'] = $year_itme;
        }
        //月数据获取
        foreach ($s_list as $k=>$v) {
            $supplier_id = $v['id'];
            $month_amount = 0;
            $month_itme = [];
            foreach ($search_month as $month) {
                //获取符合年限的数据
                $month_data_list = array_filter($list, function ($row) use($month,$supplier_id) {
                    return $row['supplier_id']==$supplier_id && $row['m_date'] == $month;
                });
                //毛利润获取
                $month_total = array_sum(array_column($month_data_list,'oa_key_4'));
                foreach (self::$aggregation_keys as $kes_) {
                    $key_total = array_sum(array_column($month_data_list,$kes_));
                    $month_total += $key_total;
                }
                $month_itme[$month] = roundToString($month_total);
                $month_amount += $month_total;
            }
            $month_itme['month_amount'] = roundToString($month_amount);
            $s_list[$k]['month_data'] = $month_itme;
        }
        return [
            'count'=>$supplier_list['total'],
            'page_count'=>count($s_list),
            'page'=>self::$param['page'],
            'page_size'=>self::$param['page_size'],
            'list'=>$s_list,
            'result'=>$rulst,
        ];
    }
    //商品利润贡献,分页
    public static function getGoodsPageList($list) {
        if (!count($list)) {
            return [];
        }
        $db = dbFMysql::getInstance();
        $goods_list = $db->table('goods')
            ->field('id,category_name,cid,sku,product_name')
            ->pages(self::$param['page'],self::$param['page_size']);
        $s_list = $goods_list['list'];
        $category_list = goodsInformationForm::getGoodsCate(array_column($s_list,'cid'));
//        dd($category_list);
        $sku_ids = array_column($s_list,'sku');
        //筛选出能用的数据
        $list = array_filter($list, function ($row) use($sku_ids) {
            return in_array($row['sku'],$sku_ids);
        });
        //显示的月份
        $search_month = [];
        $month = self::$param['date_time'][0];
        while (1) {
            if (strtotime($month) <= strtotime(self::$param['date_time'][1])) {
                $search_month[] = $month;
                $month = date('Y-m',strtotime($month.' +1 month'));
            } else {
                break;
            }
        }
        //显示的年份
        $years = array_keys(self::$years);
        arsort($years);
        arsort($search_month);
        //计算获取数据(整理筛选出来的数据)
        foreach ($s_list as $k=>$v) {
            $sku = $v['sku'];
            $year_data = [];
            $year_amount = 0;
            //分类
            if (isset($category_list[$v['cid']])) {
                $s_list[$k]['category_name'] = $category_list[$v['cid']]['name'];
            }
            foreach ($years as $year) {
                //获取符合年限的数据
                $year_data_list = array_filter($list, function ($row) use($year,$sku) {
                    return $row['sku']==$sku && $row['year'] == $year;
                });
//                dd($year_data_list);
                //毛利润获取
                $year_total = array_sum(array_column($year_data_list,'oa_key_4'));
                foreach (self::$aggregation_keys as $kes_) {
                    $key_total = array_sum(array_column($year_data_list,$kes_));
                    $year_total += $key_total;
                }
                $year_data[$year] = roundToString($year_total);
                $year_amount += $year_total;
            }
            $year_data['year_amount'] = roundToString($year_amount);
            $s_list[$k]['year_data'] = $year_data;
        }

        //月数据获取
        foreach ($s_list as $k=>$v) {
            $sku = $v['sku'];
            $new_item = [];
            $month_amount = 0;
            foreach ($search_month as $month) {
                $month_total = 0;
                //获取符合年限的数据
                $month_data_list = array_filter($list, function ($row) use($month,$sku) {
                    return $row['sku']==$sku && $row['m_date'] == $month;
                });
                $month_data_list = array_values($month_data_list);
                if (count($month_data_list)) {
                    $month_data_ = $month_data_list[0];
                    $month_total = $month_data_['oa_key_4'];
                    foreach (self::$aggregation_keys as $kes_) {
                        $month_total += $month_data_[$kes_];
                    }
                }
                $new_item[$month] = roundToString($month_total);
                $month_amount += $month_total;
            }
            $new_item['month_amount'] = roundToString($month_amount);
            $s_list[$k]['month_data'] = $new_item;
        }
        return [
            'count'=>$goods_list['total'],
            'page_count'=>count($s_list),
            'page'=>self::$param['page'],
            'page_size'=>self::$param['page_size'],
            'list'=>$s_list,
        ];
    }
    public static function goodsGrossTotalPageList($s_list,$list,$category_list) {
        if (!count($s_list)) {
            return [];
        }
        //显示的月份
        $search_month = [];
        $month = self::$param['date_time'][0];
        while (1) {
            if (strtotime($month) <= strtotime(self::$param['date_time'][1])) {
                $search_month[] = $month;
                $month = date('Y-m',strtotime($month.' +1 month'));
            } else {
                break;
            }
        }
        //显示的年份
        $years = array_keys(self::$years);
        arsort($years);
        arsort($search_month);
        //计算获取数据(整理筛选出来的数据)
        foreach ($s_list as $k=>$v) {
            $sku = $v['sku'];
            $year_data = [];
            $year_amount = 0;
            //分类
            if (isset($category_list[$v['cid']])) {
                $s_list[$k]['category_name'] = $category_list[$v['cid']]['name'];
            }
            foreach ($years as $year) {
                //获取符合年限的数据
                $year_data_list = array_filter($list, function ($row) use($year,$sku) {
                    return $row['sku']==$sku && $row['year'] == $year;
                });
//                dd($year_data_list);
                //毛利润获取
                $year_total = array_sum(array_column($year_data_list,'oa_key_4'));
                foreach (self::$aggregation_keys as $kes_) {
                    $key_total = array_sum(array_column($year_data_list,$kes_));
                    $year_total += $key_total;
                }
                $year_data[$year] = roundToString($year_total);
                $year_amount += $year_total;
            }
            $year_data['year_amount'] = roundToString($year_amount);
            $s_list[$k]['year_data'] = $year_data;
        }
        //月数据获取
        foreach ($s_list as $k=>$v) {
            $sku = $v['sku'];
            $new_item = [];
            $month_amount = 0;
            foreach ($search_month as $month) {
                $month_total = 0;
                //获取符合年限的数据
                $month_data_list = array_filter($list, function ($row) use($month,$sku) {
                    return $row['sku']==$sku && $row['m_date'] == $month;
                });
                $month_data_list = array_values($month_data_list);
                if (count($month_data_list)) {
                    foreach ($month_data_list as $month_data_) {
                        unset($month_data_['m_date']);
                        unset($month_data_['year']);
                        $month_total += array_sum($month_data_);
                    }
//                    $month_data_ = $month_data_list[0];
//                    $month_total = $month_data_['oa_key_4'];
//                    foreach (self::$aggregation_keys as $kes_) {
//                        $month_total += $month_data_[$kes_];
//                    }
                }
                $new_item[$month] = roundToString($month_total);
                $month_amount += $month_total;
            }
            $new_item['month_amount'] = roundToString($month_amount);
            $s_list[$k]['month_data'] = $new_item;
        }
        return $s_list;
    }
    //商品利润共享，统计
    public static function getGoodsCount($list) {
        //显示的月份
        $search_month = [];
        $month = self::$param['date_time'][0];
        while (1) {
            if (strtotime($month) <= strtotime(self::$param['date_time'][1])) {
                $search_month[] = $month;
                $month = date('Y-m',strtotime($month.' +1 month'));
            } else {
                break;
            }
        }
        //显示的年份
        $years = array_keys(self::$years);
        arsort($years);
        arsort($search_month);

        //年数据计算
        $year_data = [];
        $year_amount = 0;
        foreach ($years as $year) {
            //获取符合年限的数据
            $year_data_list = array_filter($list, function ($row) use($year) {
                return $row['year'] == $year;
            });
            //毛利润获取
            $year_total = array_sum(array_column($year_data_list,'oa_key_4'));
            foreach (self::$aggregation_keys as $kes_) {
                $key_total = array_sum(array_column($year_data_list,$kes_));
                $year_total += $key_total;
            }
            $year_data[$year] = roundToString($year_total);
            $year_amount += $year_total;
        }
        $year_data['year_amount'] = roundToString($year_amount);
        //月与统计
        $month_data = [];
        $month_amount = 0;
        foreach ($search_month as $month) {
            $month_total = 0;
            //获取符合年限的数据
            $month_data_list = array_filter($list, function ($row) use($month) {
                return $row['m_date'] == $month;
            });
            $month_data_list = array_values($month_data_list);
            if (count($month_data_list)) {
                foreach ($month_data_list as $month_data_) {
                    unset($month_data_['m_date']);
                    unset($month_data_['year']);
                    $month_total += array_sum($month_data_);
//
//                    $month_total += $month_data_['oa_key_4'];
//                    foreach (self::$aggregation_keys as $kes_) {
//                        $month_total += $month_data_[$kes_];
//                    }
                }
            }
            $month_data[$month] = roundToString($month_total);
            $month_amount += $month_total;
        }
        $month_data['month_amount'] = roundToString($month_amount);
        return [
            'year_data'=>$year_data,
            'month_data'=>$month_data,
        ];
    }
    public static function getValRow($rule_,$row,$last_row) {
        $row_val = 0;
        if ($rule_['type'] == 1) {
            if (isset($row[$rule_['coulmn_key']])) {
                return $row[$rule_['coulmn_key']];
            }
        } elseif ($rule_['type'] == 2){
            if (isset($last_row[$rule_['coulmn_key']])) {
                return $last_row[$rule_['coulmn_key']];
            }
        } else if ($rule_['type'] == 3){
            $row_val = round($rule_['val'],4);
        }
        return $row_val;
    }
    //获取产品相关查询条件按
    public static function getGoodsSku($db) {
        $param = self::$param;
        //查询可用的sku
        //产品名
        $goods_name = $param['search_type'] == 'goods_name'?$param['search_value']:'';
        //分类
        $category_ids = [];
        if (!empty($param['category_ids']) && $param['category_ids']!='[]') {
            $category_ids = json_decode($param['category_ids']);
        }
        $sku_list = [];
        $user_sku = 0;
        //产品名和分类查询
        if (!empty($goods_name) || count($category_ids)) {
            $user_sku = 1;
            $db->table('goods');
            if (!empty($goods_name)) {
                $db->where('where product_name like :product_name',['product_name'=>"%$goods_name%"]);
            }
            if (count($category_ids)) {
                $db->whereIn('cid',$category_ids);
            }
            $goods_list = $db->field('sku')->list();
            $sku_list = array_column($goods_list,'sku');
        }
        //新品查询
//        if (!empty($goods_name) || count($category_ids) || ($param['is_new'] != '' && $param['is_new'] != -1)) {
//            $user_sku = 1;
//            $db->table('goods');
//            if (!empty($goods_name)) {
//                $db->where('where product_name like :product_name',['product_name'=>"%$goods_name%"]);
//            }
//            if (count($category_ids)) {
//                $db->whereIn('cid',$category_ids);
//            }
//            if ($param['is_new'] != '' && $param['is_new'] != -1) {
//                $db->andWhere('is_new=:is_new',['is_new'=>(int)$param['is_new']]);
//            }
//            $goods_list = $db->field('sku')->list();
//            $sku_list = array_column($goods_list,'sku');
//        }
        self::$sql_sku_list = $sku_list;
        self::$sql_use_sku = $user_sku;
    }
    //获取新品查询条件
    public static function getNewGoodsWhere($db) {
        $param = self::$param;
        if (in_array($param['is_new'],[0,1])) {
            self::$sql_use_new_goods = $param['is_new'] == 1?1:2;
            $first_time_list = listingFirstTimeFrom::getNotNewList($db);
            $list_new = [];
            foreach ($first_time_list as $v) {
                $list_new[] = array_values($v);
            }
            self::$sql_new_goods_data = $list_new;
        }
    }
    //整合领星数据和oa数据（不用重新计算）
    public static function arrangeList($lx_list,$oa_list,$is_last_month = 0) {
        //整理数据(只能是维度+国家使用)
        $oa_data = [];
        $oa_ids = $is_last_month?self::$oa_last_ids:self::$oa_column_ids;
        foreach ($oa_list as $v1) {
            //每个维度的数据
            if (!in_array(self::$table,self::$country_table)) {
                $key = $v1[self::$table];
            } elseif (self::$real_table == 'month_total') {
                $key = $v1['asin'].'_'.$v1['country_code'].'_'.$v1['m_date'];
            } else {
                $key = $v1[self::$table].'_'.$v1['country_code'];
            }
            $oa_data[$key]['oa_key_'.$v1['custom_id']] = $v1['total'];
        }
        //每条领星数据需要拼接的字段
        $def_oa_data = [];
        foreach ($oa_ids as $oa_id) {
            $oa_key = 'oa_key_'.$oa_id;
            $def_oa_data[$oa_key] = '0.00';
        }
        $new_list = [];
        foreach ($lx_list as $v) {
            //将自定义字段的值放入行中
            $v = array_merge($v,$def_oa_data);
            if (!in_array(self::$table,self::$country_table)) {
                $key = $v[self::$table];
            } elseif (self::$real_table == 'month_total') {
                $key = $v['asin'].'_'.$v['country_code'].'_'.$v['m_date'];
            } else {
                $key = $v[self::$table].'_'.$v['country_code'];
            }
            if (isset($oa_data[$key])) {
                $v = array_replace($v,$oa_data[$key]);
                unset($oa_data[$key]);
            }
            $new_list[$key] = $v;
        }
        return $new_list;
    }
    //获取数据基础像
    public static function getListBascData($list) {
        $table_list = array_column($list,self::$table);
        $country_data = self::getCountryData();
        //商品信息，国家信息数据（按照asin整理好需要的数据）
        if (self::$table == 'asin') {
            $base_data = self::getAsinNeed($table_list);
        } elseif (self::$table == 'p_asin') {
            $base_data = self::getPasinNeed($table_list);
        } elseif (self::$table == 'sku') {
            $base_data = self::getSkuNeed($table_list,$country_data);
        } elseif (self::$table == 'yunying_id') {
            $base_data = self::getYunyingNeed($table_list,$country_data);
        } elseif (self::$table == 'sid') {
            $base_data = self::getSidNeed($table_list,$country_data);
        } else {
            dd(520);
        }
        $new_list = [];
        foreach ($list as $v) {
            if (in_array(self::$table,self::$country_table)) {
                $v['country'] = $country_data[$v['country_code']]??'空';
            }
            //基础字段整理
            $table_data = $v[self::$table];
            if (in_array(self::$table,['asin','p_asin'])) {
                $table_data = $table_data.'_'.$v['country_code'];
            }
            if (isset($base_data[$table_data])) {
                foreach (authListModel::$base_auth as $key_list){
                    $key_ = $key_list['key'];
                    if (self::$table == 'asin' && in_array($key_,['asin','country'])){
                        //数组和字符串
                        if ($key_ == 'p_asin') {
                            $v[$key_] = $base_data[$table_data][$key_];
                        }
                        continue;
                    }
                    if (self::$table == 'p_asin' && in_array($key_,['p_asin','country'])){
                        continue;
                    }
                    if (self::$table == 'sku' && in_array($key_,['product_name','sku','category_name','product_developer','is_new'])){
                        if (!in_array($key_,['sku'])) {
                            $v[$key_] = $base_data[$table_data][$key_];
                        }
                        continue;
                    }
                    if (self::$table == 'yunying_id') {
                        if ($key_ == 'yunying') {
                            $v[$key_] = $base_data[$table_data][$key_];
                            continue;
                        }
                    }
                    if (self::$table == 'sid') {
                        if ($key_ == 'store_name') {
                            $v[$key_] = $base_data[$table_data][$key_];
                            continue;
                        }
                    }
                    if (!is_array($base_data[$table_data][$key_])) {
                        echo $key_.PHP_EOL;
                        dd($base_data[$table_data]);
                    }
                    $v[$key_] = array_values(array_unique($base_data[$table_data][$key_]));
                }
            } else {
                if (self::$table == 'asin'){
                    foreach (authListModel::$base_auth as $key_list){
                        $key_ = $key_list['key'];
                        if (in_array($key_,['asin','country'])) {
                            continue;
                        } else {
                            if ($key_ == 'p_asin') {
                                $v[$key_] = '';
                            } else {
                                $v[$key_] = [];
                            }
                        }
                    }
                }
                if (self::$table == 'p_asin'){
                    foreach (authListModel::$base_auth as $key_list){
                        $key_ = $key_list['key'];
                        if (in_array($key_,['country','p_asin'])) {
                            continue;
                        } else {
                            $v[$key_] = [];
                        }
                    }
                }
                if (self::$table == 'sku'){
                    foreach (authListModel::$base_auth as $key_list){
                        $key_ = $key_list['key'];
                        if (in_array($key_,['sku'])) {
                            continue;
                        } else {
                            if (in_array($key_,['product_name','project','yunying','category_name','product_developer','is_new'])) {
                                $v[$key_] = '';
                            } else {
                                $v[$key_] = [];
                            }
                        }
                    }
                }
                if (self::$table == 'yunying_id') {
                    if ($key_ == 'yunying') {
                        $v[$key_] = '';
                        continue;
                    } else {
                        $v[$key_] = [];
                    }
                }
                if (self::$table == 'sid') {
                    if ($key_ == 'store_name') {
                        $v[$key_] = '';
                        continue;
                    } else {
                        $v[$key_] = [];
                    }
                }
            }
            $new_list[] = $v;
        }
        return $new_list;
    }
    private static function getSidNeed($sids,$country_data) {
        $base_data = self::getBaseDataSku('sid',$sids);
        $store_ = $base_data['store_data'];
        $goods_list = $base_data['goods_data'];
        $yunying_data = $base_data['yunying_data'];
        $project_data = $base_data['project_data'];
        //数据整理
        $res_data = [];
        foreach ($base_data['msku_data'] as $v) {
            if (!isset($res_data[$v['sid']])) {
                $res_data[$v['sid']] =  [
                    'store_name'=>'',
                    'asin'=>[],
                    'yunying'=>[],
                    'country'=>[],
                    'p_asin'=>[],
                    'product_name'=>[],
                    'pic_url'=>[],
                    'level_name'=>[],
                    'is_new'=>[],
                    'category_name'=>[],
                    'product_developer'=>[],
                    'project'=>[],
                    'project1'=>[],
                    'sku'=>[],
                ];
            }
            if (!in_array($v['localSku'],$res_data[$v['sid']]['sku'])) {
                $res_data[$v['sid']]['sku'][] = $v['localSku'];
            }
            if (!in_array($v['parentAsin'],$res_data[$v['sid']]['p_asin'])) {
                $res_data[$v['sid']]['p_asin'][] = $v['parentAsin'];
            }
            if (!in_array($v['asin'],$res_data[$v['sid']]['asin'])) {
                $res_data[$v['sid']]['asin'][] = $v['asin'];
            }
            //国家
            $country_name = $country_data[$v['countryCode']]??'空';
            if (!in_array($country_name,$res_data[$v['sid']]['country'])) {
                $res_data[$v['sid']]['country'][] = $country_name;
            }
            //运营
            $yunying_name = $yunying_data[$v['yunying_id']]??'-';
            if (!in_array($yunying_name,$res_data[$v['sid']]['yunying'])) {
                $res_data[$v['sid']]['yunying'][] = $yunying_name;
            }
            //项目
            if (isset($project_data[$v['project_id']])) {
                $res_data[$v['sid']]['project'][] = $project_data[$v['project_id']]['name3'];
                $res_data[$v['sid']]['project1'][] = $project_data[$v['project_id']]['name1'];
            }
            $res_data[$v['sid']]['store_name'] = $store_[$v['sid']]??'-';
            $goods_info = $goods_list[$v['localSku']]??'';
            if ($goods_info) {
                $res_data[$v['sid']]['product_name'][] = $goods_info['product_name'];
                $res_data[$v['sid']]['level_name'] = array_merge($res_data[$v['sid']]['level_name'],$goods_info['level_name']);
                $res_data[$v['sid']]['category_name'][] = $goods_info['category_name'];
//                $res_data[$v['sid']]['is_new'][] = $goods_info['is_new'];
                if (!is_null($goods_info['pic_url'])) {
                    $res_data[$v['sid']]['product_developer'][] = $goods_info['product_developer'];
                    $res_data[$v['sid']]['pic_url'][] = $goods_info['pic_url'];
                }
            } else {
                if (!in_array('-',$res_data[$v['sid']]['product_name'])) {
                    $res_data[$v['sid']]['product_name'][] = '-';
                }
            }
        }
        return $res_data;
    }
    //运营
    private static function getYunyingNeed($yunying_ids,$country_data) {
        $base_data = self::getBaseDataSku('yunying_id',$yunying_ids);
        $store_ = $base_data['store_data'];
        $goods_list = $base_data['goods_data'];
        $yunying_data = $base_data['yunying_data'];
        $project_data = $base_data['project_data'];
        //数据整理
        $res_data = [];
        foreach ($base_data['msku_data'] as $v) {
            if (!isset($res_data[$v['yunying_id']])) {
                $res_data[$v['yunying_id']] =  [
                    'store_name'=>[],
                    'asin'=>[],
                    'country'=>[],
                    'p_asin'=>[],
                    'product_name'=>[],
                    'pic_url'=>[],
                    'level_name'=>[],
                    'is_new'=>[],
                    'category_name'=>[],
                    'product_developer'=>[],
                    'yunying'=>$yunying_data[$v['yunying_id']]??'',
                    'project'=>[],
                    'project1'=>[],
                    'sku'=>[],
                ];
            }
            if (!in_array($v['localSku'],$res_data[$v['yunying_id']]['sku'])) {
                $res_data[$v['yunying_id']]['sku'][] = $v['localSku'];
            }
            if (!in_array($v['parentAsin'],$res_data[$v['yunying_id']]['p_asin'])) {
                $res_data[$v['yunying_id']]['p_asin'][] = $v['parentAsin'];
            }
            if (!in_array($v['asin'],$res_data[$v['yunying_id']]['asin'])) {
                $res_data[$v['yunying_id']]['asin'][] = $v['asin'];
            }
            //国家
            $country_name = $country_data[$v['countryCode']]??'空';
            if (!in_array($country_name,$res_data[$v['yunying_id']]['country'])) {
                $res_data[$v['yunying_id']]['country'][] = $country_name;
            }
            //店铺
            $store_name = isset($store_[$v['sid']])?$store_[$v['sid']]:'-';
            if (!in_array($store_name,$res_data[$v['yunying_id']]['store_name'])) {
                $res_data[$v['yunying_id']]['store_name'][] = $store_name;
            }
            //项目
            if (isset($project_data[$v['project_id']])) {
                $res_data[$v['yunying_id']]['project'][] = $project_data[$v['project_id']]['name3'];
                $res_data[$v['yunying_id']]['project1'][] = $project_data[$v['project_id']]['name1'];
            }
            $goods_info = $goods_list[$v['localSku']]??'';
            if ($goods_info) {
                $res_data[$v['yunying_id']]['product_name'][] = $goods_info['product_name'];
                $res_data[$v['yunying_id']]['level_name'] = array_merge($res_data[$v['yunying_id']]['level_name'],$goods_info['level_name']);
                $res_data[$v['yunying_id']]['category_name'][] = $goods_info['category_name'];
//                $res_data[$v['yunying_id']]['is_new'][] = $goods_info['is_new'];
                if (!is_null($goods_info['pic_url'])) {
                    $res_data[$v['yunying_id']]['product_developer'][] = $goods_info['product_developer'];
                    $res_data[$v['yunying_id']]['pic_url'][] = $goods_info['pic_url'];
                }
            } else {
                //产品名称
                if (!in_array('-',$res_data[$v['yunying_id']]['product_name'])) {
                    $res_data[$v['yunying_id']]['product_name'][] = '-';
                }
            }
        }
        return $res_data;
    }
    //sku信息
    private static function getSkuNeed($sku_list,$country_data) {
        $base_data = self::getBaseDataSku('localSku',$sku_list);
        $store_ = $base_data['store_data'];
        $goods_list = $base_data['goods_data'];
        $yunying_data = $base_data['yunying_data'];
        $project_data = $base_data['project_data'];
        //按sku 基础数据
        $res_data = [];
        foreach ($base_data['msku_data'] as $v) {
            if (!isset($res_data[$v['localSku']])) {
                $res_data[$v['localSku']] =  [
                    'store_name'=>[],
                    'asin'=>[],
                    'country'=>[],
                    'p_asin'=>[],
                    'product_name'=>'-',
                    'pic_url'=>[],
                    'level_name'=>[],
                    'is_new'=>'',
                    'category_name'=>'',
                    'product_developer'=>'',
                    'yunying'=>[],
                    'project'=>[],
                    'project1'=>[],
                ];
            }
            if (!in_array($v['parentAsin'],$res_data[$v['localSku']]['p_asin'])) {
                $res_data[$v['localSku']]['p_asin'][] = $v['parentAsin'];
            }
            if (!in_array($v['asin'],$res_data[$v['localSku']]['asin'])) {
                $res_data[$v['localSku']]['asin'][] = $v['asin'];
            }
            //项目
            if (!empty($project_data[$v['project_id']])) {
                $res_data[$v['localSku']]['project'][] = $project_data[$v['project_id']]['name3'];
                $res_data[$v['localSku']]['project1'][] = $project_data[$v['project_id']]['name1'];
            }
            //运营
            $yunying_name = $yunying_data[$v['yunying_id']]??'-';
            if (!in_array($yunying_name,$res_data[$v['localSku']]['yunying'])) {
                $res_data[$v['localSku']]['yunying'][] = $yunying_name;
            }
            //国家
            $country_name = $country_data[$v['countryCode']]??'空';
            if (!in_array($country_name,$res_data[$v['localSku']]['country'])) {
                $res_data[$v['localSku']]['country'][] = $country_name;
            }
            //店铺名
            $store_name = isset($store_[$v['sid']])?$store_[$v['sid']]:'-';
            if (!in_array($store_name,$res_data[$v['localSku']]['store_name'])) {
                $res_data[$v['localSku']]['store_name'][] = $store_name;
            }
            $goods_info = $goods_list[$v['localSku']]??'';
            if ($goods_info) {
                $res_data[$v['localSku']]['product_name'] = $goods_info['product_name'];
                $res_data[$v['localSku']]['level_name'] = $goods_info['level_name'];
                $res_data[$v['localSku']]['category_name'] = $goods_info['category_name'];
//                $res_data[$v['localSku']]['is_new'] = $goods_info['is_new'];
                if (!is_null($goods_info['pic_url'])) {
                    $res_data[$v['localSku']]['product_developer'] = $goods_info['product_developer'];
                    $res_data[$v['localSku']]['pic_url'][] = $goods_info['pic_url'];
                }
            }
        }
        return $res_data;
    }
    //pasin基础信息获取
    private static function getPasinNeed($p_asin_list) {
        $base_data = self::getBaseDataSku('parentAsin',$p_asin_list);
        $msku_data = $base_data['msku_data'];
        $store_ = $base_data['store_data'];
        $goods_list = $base_data['goods_data'];
        $project_data = $base_data['project_data'];
        $yunying_data = $base_data['yunying_data'];
        //按p_asin 基础数据
        $res_data = [];
        foreach ($msku_data as $v) {
            $key_ = $v['parentAsin'].'_'.$v['countryCode'];
            if (!isset($res_data[$key_])) {
                $res_data[$key_] =  [
                    'store_name'=>[],
                    'product_name'=>[],
                    'sku'=>[],
                    'pic_url'=>[],
                    'level_name'=>[],
                    'is_new'=>[],
                    'category_name'=>[],
                    'product_developer'=>[],
                    'asin'=>[],
                    'project'=>[],
                    'project1'=>[],
                    'yunying'=>[],
                ];
            }
            //sku
            if (!in_array($v['localSku'],$res_data[$key_]['sku'])) {
                $res_data[$key_]['sku'][] = $v['localSku'];
            }
            //asin
            if (!in_array($v['asin'],$res_data[$key_]['asin'])) {
                $res_data[$key_]['asin'][] = $v['asin'];
            }
            //运营
            $yunying_name = $yunying_data[$v['yunying_id']]??'-';
            if (!in_array($yunying_name,$res_data[$key_]['yunying'])) {
                $res_data[$key_]['yunying'][] = $yunying_name;
            }
            if (isset($project_data[$v['project_id']])) {
                if (!in_array($project_data[$v['project_id']]['name3'],$res_data[$key_]['project'])) {
                    $res_data[$key_]['project'][] = $project_data[$v['project_id']]['name3'];
                }
                if (!in_array($project_data[$v['project_id']]['name1'],$res_data[$key_]['project1'])){
                    $res_data[$key_]['project1'][] = $project_data[$v['project_id']]['name1'];
                }
            }
            //店铺名
            $store_name = isset($store_[$v['sid']])?$store_[$v['sid']]:'-';
            if (!in_array($store_name,$res_data[$key_]['store_name'])) {
                $res_data[$key_]['store_name'][] = $store_[$v['sid']]??'';
            }
            if (isset($goods_list[$v['localSku']])) {
                $v1 = $goods_list[$v['localSku']];
                //店铺名称
                if (!in_array($v1['product_name'],$res_data[$key_]['product_name'])) {
                    $res_data[$key_]['product_name'][] = $v1['product_name'];
                }
                //产品等级
                $res_data[$key_]['level_name'] = array_values(array_unique(array_merge($res_data[$key_]['level_name'],$v1['level_name'])));
                //分类名称
                if (!in_array($v1['category_name'],$res_data[$key_]['category_name'])) {
                    $res_data[$key_]['category_name'][] = $v1['category_name'];
                }
                //新品
//                if (!in_array($v1['is_new'],$res_data[$key_]['is_new'])) {
//                    $res_data[$key_]['is_new'][] = $v1['is_new'];
//                }
                if (!is_null($v1['pic_url'])) {
                    if (!in_array($v1['product_developer'],$res_data[$key_]['product_developer'])) {
                        $res_data[$key_]['product_developer'][] = $v1['product_developer'];
                    }
                    if (!in_array($v1['pic_url'], $res_data[$key_]['pic_url'])) {
                        $res_data[$key_]['pic_url'][] = $v1['pic_url'];
                    }
                }
            } else {
                //产品名称
                if (!in_array('-',$res_data[$key_]['product_name'])) {
                    $res_data[$key_]['product_name'][] = '-';
                }
            }
        }
        return $res_data;
    }
    //asin基础信息获取
    private static function getAsinNeed($asin_list) {
        $base_data = self::getBaseDataSku('asin',$asin_list);
        $msku_data = $base_data['msku_data'];
        $store_ = $base_data['store_data'];
        $goods_list = $base_data['goods_data'];
        $yunying_data = $base_data['yunying_data'];
        $project_data = $base_data['project_data'];
        $no_new_goods = $base_data['no_new_goods'];
        //按asin 基础数据
        $sku_asin_data = [];
        foreach ($msku_data as $v) {
            $key_ = $v['asin'].'_'.$v['countryCode'];
            if (!isset($sku_asin_data[$key_])) {
                $sku_asin_data[$key_] =  [
                    'store_name'=>[],
                    'p_asin'=>[],
                    'product_name'=>[],
                    'sku'=>[],
                    'pic_url'=>[],
                    'level_name'=>[],
                    'is_new'=>[],
                    'category_name'=>[],
                    'product_developer'=>[],
                    'yunying'=>[],
                    'project'=>[],
                    'project1'=>[],
                ];
            }
            if (!in_array($v['localSku'],$sku_asin_data[$key_]['sku'])) {
                $sku_asin_data[$key_]['sku'][] = $v['localSku'];
            }
            if (!in_array($v['parentAsin'],$sku_asin_data[$key_]['p_asin'])) {
                $sku_asin_data[$key_]['p_asin'][] = $v['parentAsin'];
            }
            $yunying_name = $yunying_data[$v['yunying_id']]??'-';
            if ($yunying_name && !in_array($yunying_name,$sku_asin_data[$key_]['yunying'])) {
                $sku_asin_data[$key_]['yunying'][] = $yunying_name;
            }

            if (isset($project_data[$v['project_id']])) {
                $name3 = $project_data[$v['project_id']]['name3'];
                $name1 = $project_data[$v['project_id']]['name1'];
                if (!in_array($name3,$sku_asin_data[$key_]['project'])) {
                    $sku_asin_data[$key_]['project'][] = $project_data[$v['project_id']]['name3'];
                }
                if (!in_array($name1,$sku_asin_data[$key_]['project1'])) {
                    $sku_asin_data[$key_]['project1'][] = $project_data[$v['project_id']]['name1'];
                }
            }
            $store_name = isset($store_[$v['sid']])?$store_[$v['sid']]:'-';
            if ($store_name && !in_array($store_name,$sku_asin_data[$key_]['store_name']) ) {
                $sku_asin_data[$key_]['store_name'][] = $store_name;
            }
            if (isset($goods_list[$v['localSku']])) {
                $v1 = $goods_list[$v['localSku']];
                //店铺名称
                if (!in_array($v1['product_name'],$sku_asin_data[$key_]['product_name'])) {
                    $sku_asin_data[$key_]['product_name'][] = $v1['product_name'];
                }
                //产品等级
                $sku_asin_data[$key_]['level_name'] = array_values(array_unique(array_merge($sku_asin_data[$key_]['level_name'],$v1['level_name'])));

                //分类名称
                if (!in_array($v1['category_name'],$sku_asin_data[$key_]['category_name'])) {
                    $sku_asin_data[$key_]['category_name'][] = $v1['category_name'];
                }
                //新品
//                if (!in_array($v1['is_new'],$sku_asin_data[$key_]['is_new'])) {
//                    $sku_asin_data[$key_]['is_new'][] = $v1['is_new'];
//                }
                if (!is_null($v1['pic_url'])) {
                    if (!in_array($v1['product_developer'],$sku_asin_data[$key_]['product_developer'])) {
                        $sku_asin_data[$key_]['product_developer'][] = $v1['product_developer'];
                    }
                    if (!in_array($v1['pic_url'], $sku_asin_data[$key_]['pic_url'])) {
                        $sku_asin_data[$key_]['pic_url'][] = $v1['pic_url'];
                    }
                }
            } else {
                //产品名称
                if (!in_array('-',$sku_asin_data[$key_]['product_name'])) {
                    $sku_asin_data[$key_]['product_name'][] = '-';
                }
            }
            //新品判断
            if (in_array($key_,$no_new_goods)) {
                $sku_asin_data[$key_]['is_new'] = ['0'];
            } else {
                $sku_asin_data[$key_]['is_new'] = ['1'];
            }
        }
        return $sku_asin_data;
    }
    //获取记录数据
    public static function getSearchList() {
        return false;
        $db = dbFMysql::getInstance();
        $param_md5 = self::$param_md5;
        if (!$param_md5) {
           return false;
        }
        $data = $db->table('table_search_data')
            ->where('where type=:type and param_md5=:param_md5',
                ['type'=>self::$real_table,'param_md5'=>$param_md5]
            )->one();
        if (!$data) {
            return false;
        } else {
            return json_decode($data['data_list'],true);
        }
    }
    public static function setSearchList($list) {
        $db = dbFMysql::getInstance();
        $param_md5 = self::$param_md5;
        if (empty($param_md5)) {
            return ;
        }
        $db->table('table_search_data')
            ->insert([
                'type'=>self::$real_table,
                'param_md5'=>$param_md5,
                'user_id'=>userModel::$qwuser_id,
                'data_list'=>json_encode($list,JSON_UNESCAPED_UNICODE)
            ]);
    }

    //国家数据获取
    public static function getCountryData() {
        $dbF = dbFMysql::getInstance();
        //国家
        $country_data = [];
        $country_list = $dbF->table('market')
            ->field('code,country')
            ->list();
        foreach ($country_list as $v) {
            $country_data[$v['code']] = $v['country'];
        }
        return $country_data;
    }
    //月汇总
    public static function getMonthAmount($lx_list,$oa_list,$no_need_rate) {
        //自定义字段合并
        $oa_data = [];
        foreach ($oa_list as $v1) {
            //每个维度的数据
            $key = $v1['m_date'];
            $oa_data[$key]['oa_key_'.$v1['custom_id']] = $v1['total'];
        }
        //每条领星数据需要拼接的字段
        $def_oa_data = [];
        foreach (self::$oa_keys as $oa_key) {
            $def_oa_data[$oa_key] = '0.00';
        }
        $list = [];
        $column_list = array_flip(self::$function_keys);
        //合并msku数据和自定义数据
        $oa_key_3_array = [];
        foreach ($lx_list as $v) {
            //将自定义字段的值放入行中
            $v = array_merge($v,$def_oa_data);
            $key = $v['m_date'];
            if (isset($oa_data[$key])) {
                $oa_data_row = $oa_data[$key];
                if (isset($oa_data_row['oa_key_4'])) {
                    if (count(self::$aggregation_keys)) {
                        foreach (self::$aggregation_keys as $a_k) {
                            $oa_data_row['oa_key_4'] += $v[$a_k]??0;
                        }
                    }
                }
                $v = array_replace($v,$oa_data_row);
                unset($oa_data[$key]);
            }
            $oa_key_3_array[$v['m_date']] = $v['oa_key_3'];
            unset($v['m_date']);
            $list[$key] = $v;
        }
        //字段数据月汇总
        $month_data = [];
        $custom_total_list = [];
        foreach (self::$search_month as $k=>$month) {
            $month_data[$month] = $list[$month]??[];
            if(count($month_data[$month]) && $k>0) {
                $custom_total_list[] = $month_data[$month];
            }
        }
        //字段数据月汇总
        $custom_total = [];
        if (count($custom_total_list)) {
            foreach ($custom_total_list[0] as $key_=>$val_) {
                $custom_total[$key_] = roundToString(array_sum(array_column($custom_total_list,$key_)));
            }
        }
        /**月数据计算**/
        //获取字段类型
        $column_type = config::get('column_type','data_financial');
        $column_type_list = [];
        foreach ($column_type as $v) {
            $column_type_list[$v['id']] = $v['name'];
        }
        //计算比例字段
        if (count(self::$need_again_oa_keys2)) {
            //月数据
            foreach ($month_data as $k => $row_) {
                $last_row = [];
                foreach ($row_ as $key_ => $val) {
                    if (isset(self::$need_again_oa_keys2[$key_])) {
                        $rule = self::$need_again_oa_keys2[$key_];
                        //计算数据
                        $rule_data = json_decode($rule['rules'], true)[0]['rules'];
                        $show_type = $rule['show_type'];
                        foreach ($rule_data as &$rule_) {
                            if ($rule_['group_type'] == 1) {
                                $rule_['val'] = self::getValRow($rule_, $row_,$last_row);
                            } else {
                                foreach ($rule_['list'] as &$rule_l) {
                                    $rule_l['val'] = self::getValRow($rule_l, $row_,$last_row);
                                }
                            }
                        }
                        if ($show_type == 2) {
                            $month_data[$k][$key_] = roundToString(coustomColumnJobForm::getValue($rule_data) * 100);
                        } else {
                            $month_data[$k][$key_] = roundToString(coustomColumnJobForm::getValue($rule_data));
                        }
                    }
                }
            }
            //字段月总计
            $last_row = [];
            $row_ = $custom_total;
            foreach ($row_ as $key_ => $val) {
                if (isset(self::$need_again_oa_keys2[$key_])) {
                    $rule = self::$need_again_oa_keys2[$key_];
                    //计算数据
                    $rule_data = json_decode($rule['rules'], true)[0]['rules'];
                    $show_type = $rule['show_type'];
                    foreach ($rule_data as &$rule_) {
                        if ($rule_['group_type'] == 1) {
                            $rule_['val'] = self::getValRow($rule_, $row_,$last_row);
                        } else {
                            foreach ($rule_['list'] as &$rule_l) {
                                $rule_l['val'] = self::getValRow($rule_l, $row_,$last_row);
                            }
                        }
                    }
                    if ($show_type == 2) {
                        $custom_total[$key_] = roundToString(coustomColumnJobForm::getValue($rule_data) * 100);
                    } else {
                        $custom_total[$key_] = roundToString(coustomColumnJobForm::getValue($rule_data));
                    }
                }
            }
        }
        //转币种
        $month_data = self::getPriceByNewRoute($month_data,self::$param['currency_code'],self::$all_keys,self::$currency_keys);
        $custom_total = self::getPriceByNewRoute([$custom_total],self::$param['currency_code'],self::$all_keys,self::$currency_keys)[0];
        //按字段类型整理数据
        $new_list = [];
        foreach (self::$function_keys as $key_) {
            $type = self::$column_list[$key_]['data_type'];
            $key_name = self::$column_list[$key_]['key_name'];
            foreach ($month_data as $month=>$v) {
                if (!isset($new_list[$type]['data'][$month])) {
                    $new_list[$type]['data'][$month] = '-';
                }
                if (count($v)) {
                    foreach ($v as $key=>$val){
                        if (empty($val)) {
                            $val = 0;
                        }
                        if ($key == $key_name) {
                            $new_list[$type][$key_name][$month] = $val;
                        }
                    }
                } else {
                    $new_list[$type][$key_name][$month] = 0;
                }
            }
        }
        //合并类型数据-总计
        $min_month = self::$search_month[0];
        $res_data = [];
        foreach ($new_list as $k=>$v) {
            //当月的订单总售价
            $item = [
                'data_type'=>$k,
                'key'=>'type_'.$k,
                'key_name'=>$column_type_list[$k],
            ];
            //类型总数居计算
            $type_list = $v['data'];
            unset($type_list[$min_month]);
            asort($type_list);//顺序排
            $item['total'] = '';
            foreach ($type_list as $month=>$v1) {
                $item[$month] = ['val'=>'','rate'=>''];
            }
            unset($v['data']);
            //类型中数据计算
            $item['child'] = [];
            $v = array_intersect_key($v,$column_list);
            foreach ($v as $key_name=>$list_m) {
                $item_['key_name'] = self::$column_list[$key_name]['column_name'];
                $item_['key'] = self::$column_list[$key_name]['key_name'];
                if (in_array($item_['key'],$no_need_rate)) {
                    $item_['need_rate'] = 0;
                } else {
                    $item_['need_rate'] = 1;
                }
                //排序的作用是最后一个数据为0 ，但是有比例
                $type_list = $list_m;
                unset($type_list[$min_month]);
                asort($type_list);//顺序排
                $item_['total'] = $custom_total[$key_name]??0;
                $all_total_key_3 = 0;
                foreach ($type_list as $month=>$v1) {
                    $total_key_3 = $oa_key_3_array[$month]??0;
                    $all_total_key_3 += $total_key_3;
                    $rate = $total_key_3 == 0?'-':roundToString($v1*100/$total_key_3);
                    $v1 = roundToString($v1);
                    $item_[$month] = ['val'=>$v1,'rate'=>$rate];
                    //环比计算
                    if (in_array($item_['key'],self::$month_qoq_key)) {
                        $last_month = date('Y-m',strtotime($month.'-01 -1 month'));
                        $qoq_rate = $list_m[$last_month]==0?'-':roundToString(($v1-$list_m[$last_month])*100/$list_m[$last_month]);
                        $item_[$month]['qoq_rate'] = $qoq_rate;
                    }
                }
                $item_['rate'] = $all_total_key_3 == 0?'-':roundToString($item_['total']*100/$all_total_key_3);
                $item['child'][] = $item_;
            }
            $res_data[] = $item;
        }
        return $res_data;
    }
    //月sin汇总
    public static function getMonthAsinAmount($list,$no_need_rate) {
        $country_data = self::getCountryData();
        $min_month = self::$search_month[0];
        //获取字段类型
        $column_type = config::get('column_type','data_financial');
        $column_type_list = [];
        foreach ($column_type as $v) {
            $column_type_list[$v['id']] = $v['name'];
        }
        //asin 先获取asin + 国家，已asin+国家分页 得出产品的所有月的汇总
        $asin_contry = [];
        $month_oa_key_3_list = [];
        foreach ($list as $v) {
            if ($v['m_date'] != $min_month) {
                $asin_contry[] = $v['asin']."_".$v['country_code'];
                $month_oa_key_3_list[$v['m_date']][] = $v['oa_key_3']??0;
            }
        }
        //订单售价总计
        $month_oa_key_3_data = [];
        if (count($month_oa_key_3_list)) {
            foreach ($month_oa_key_3_list as $k=>$v) {
                $month_oa_key_3_data[$k] = array_sum($v);
            }
        }
        $asin_contry = array_values(array_unique($asin_contry));
        $offset = (self::$param['page'] - 1)*self::$param['page_size'];
        $asins = array_slice($asin_contry,$offset,self::$param['page_size']);
        //筛选出asin相关数据
        $list = array_filter($list, function ($row) use($asins) {
            return in_array($row['asin']."_".$row['country_code'],$asins);
        });
        //合并计算
        $custom_list = [];
        foreach ($list as $k=>$v) {
            //毛利润
            if (count(self::$aggregation_keys)) {
                $total = 0;
                foreach (self::$aggregation_keys as $va) {
                    $total += $v[$va];
                }
                //计算需要加入合并计算字段的自定义字段
                if (isset($v['oa_key_4'])) {
                    $v['oa_key_4'] += $total;
                }
                $list[$k] = $v;
            }
            if ($v['m_date'] != $min_month) {
                $key_ = $v['asin']."_".$v['country_code'];
                $custom_list[$key_][] = $v;
            }
        }
        $custom_total = [];
        if (count($custom_list)) {
            foreach ($custom_list as $k=>$v) {
                foreach ($v[0] as $kk=>$vv) {
                    if (!in_array($kk,['asin','country_code','m_date'])) {
                        $custom_total[$k][$kk] = roundToString(array_sum(array_column($v,$kk)));
                    }
                }
            }
        }

        //计算比例字段
        if (count(self::$need_again_oa_keys2)) {
            //没有比例字段，所以就不考虑上月数据
            foreach ($list as $k => $row_) {
                $last_row = [];
                foreach ($row_ as $key_ => $val) {
                    if (isset(self::$need_again_oa_keys2[$key_])) {
                        $rule = self::$need_again_oa_keys2[$key_];
                        //计算数据
                        $rule_data = json_decode($rule['rules'], true)[0]['rules'];
                        foreach ($rule_data as &$rule_) {
                            if ($rule_['group_type'] == 1) {
                                $rule_['val'] = self::getValRow($rule_, $row_,$last_row);
                            } else {
                                foreach ($rule_['list'] as &$rule_l) {
                                    $rule_l['val'] = self::getValRow($rule_l, $row_,$last_row);
                                }
                            }
                        }
                        $list[$k][$key_] = roundToString(coustomColumnJobForm::getValue($rule_data));
                    }
                }
            }
            //字段月合并数据
            foreach ($custom_total as $k => $row_) {
                $last_row = [];
                foreach ($row_ as $key_ => $val) {
                    if (isset(self::$need_again_oa_keys2[$key_])) {
                        $rule = self::$need_again_oa_keys2[$key_];
                        //计算数据
                        $rule_data = json_decode($rule['rules'], true)[0]['rules'];
                        foreach ($rule_data as &$rule_) {
                            if ($rule_['group_type'] == 1) {
                                $rule_['val'] = self::getValRow($rule_, $row_,$last_row);
                            } else {
                                foreach ($rule_['list'] as &$rule_l) {
                                    $rule_l['val'] = self::getValRow($rule_l, $row_,$last_row);
                                }
                            }
                        }
                        $custom_total[$k][$key_] = roundToString(coustomColumnJobForm::getValue($rule_data));
                    }
                }
            }
        }
        //转币种
        $list = self::getPriceByNewRoute($list,self::$param['currency_code'],self::$all_keys,self::$currency_keys);
        $custom_total = self::getPriceByNewRoute($custom_total,self::$param['currency_code'],self::$all_keys,self::$currency_keys);
        //获取字段类型
        $column_type = config::get('column_type','data_financial');
        $column_type_list = [];
        foreach ($column_type as $v) {
            $column_type_list[$v['id']] = $v['name'];
        }
        //按asin和月整理数据,(字段+字段对应时间数据)
        $column_list = self::$function_keys;
        $asin_data = [];
        foreach ($list as $v) {
            $key_a = $v['asin'].'_'.$v['country_code'];
            if(!isset($asin_data[$key_a])) {
                $asin_data[$key_a]['base']['asin'] = $v['asin'];
                $asin_data[$key_a]['base']['country'] = $country_data[$v['country_code']]??'';
                $asin_data[$key_a]['base']['country_code'] = $v['country_code'];
                foreach (authListModel::$base_auth as $vv) {
                    $asin_data[$key_a]['base'][$vv['key']] = $v[$vv['key']]??[];
                }
            }
            foreach (self::$search_month as $month) {
                if ($v['m_date'] == $month) {
                    foreach ($column_list as $key_) {
                        $asin_data[$key_a]['data_list'][$key_][$month] = $v[$key_]??'0.00';
                    }
                }
            }
        }
        //按字段类型整理数据、求和
        $new_list = [];
        foreach ($asin_data as $k_=>$v) {
            $item_ = $v['base'];
            $child_list = [];
            //字段统计
            foreach ($v['data_list'] as $key_=>$v1) {
                if (in_array($key_,$column_list)) {
                    $column_data = self::$column_list[$key_];
                    $data_type = $column_data['data_type'];
                    if (!isset($child_list[$column_data['data_type']])) {
                        $child_list[$data_type]['key'] = 'type_'.$column_data['data_type'];
                        $child_list[$data_type]['data_type'] = $column_data['data_type'];
                        $child_list[$data_type]['key_name'] = $column_data['type_name'];
                        $child_list[$data_type]['total'] = 0;
//                        $child_list[$data_type]['data'] = [];
                    }
                    $child['key_name'] = $column_data['column_name'];
                    $child['key'] = $key_;
                    if (in_array($key_,$no_need_rate)) {
                        $child['need_rate'] = 0;
                    } else {
                        $child['need_rate'] = 1;
                    }
                    //去除多查的月
                    $type_list = $v1;
                    unset($type_list[$min_month]);
                    asort($type_list);//顺序排
                    //字段行总计
                    $child['total'] = $custom_total[$k_][$key_]??'0.00';
                    $all_total_key_3 = 0;
                    foreach ($type_list as $month=>$val) {
                        $total_key_3 = $month_oa_key_3_data[$month]??0;
                        $all_total_key_3 += $total_key_3;
                        $rate = $total_key_3==0?'-':roundToString($val*100/$total_key_3);
                        $child[$month] = ['val'=>$val,'rate'=>$rate];
                        //环比计算
                        if (in_array($key_,self::$month_qoq_key)) {
                            $last_month = date('Y-m',strtotime($month.'-01 -1 month'));
                            if (!isset($v1[$last_month])) {
                                $qoq_rate = '-';
                            } else {
                                $qoq_rate = $v1[$last_month]==0?'-':roundToString(($val-$v1[$last_month])*100/$v1[$last_month]);
                            }
                            $child[$month]['qoq_rate'] = $qoq_rate;
                        }
//                        if (!isset($child_list[$data_type]['data'][$month])) {
//                            $child_list[$data_type]['data'][$month] = $val;
//                        } else {
//                            $child_list[$data_type]['data'][$month] += $val;
//                        }
                    }
                    $child['rate'] = $all_total_key_3==0?'-':roundToString($child['total']*100/$all_total_key_3);
                    $child_list[$data_type]['column_data'][] = $child;
                }
            }
            //按字段类型统计，并计算类型合计数据
//            foreach ($child_list as &$c_data) {
//                $data_list = $c_data['data'];
//                $c_data['total'] = roundToString(array_sum(array_values($data_list)));
//                asort($data_list);
//                $keys = array_keys($data_list);
//                foreach ($data_list as $month=>$val) {
//                    if (!isset($c_data[$month])) {
//                        $c_data[$month] = [
//                            'val'=>roundToString($val),
//                            'rate'=>$rate,
//                        ];
//                    }
//                    $total_key_3 = $month_oa_key_3_data[$month]??0;
//                    $rate = $total_key_3 == 0?'0.00':roundToString($val/$total_key_3);
//                    $c_data[$month] = [
//                        'val'=>roundToString($val),
//                        'rate'=>$rate,
//                    ];
//                }
//                unset($c_data['data']);
//            }
            $item_['data_type_list'] = array_values($child_list);
            $new_list[] = $item_;
        }
        $new_list = self::getListBascData($new_list);
        return [
            'count'=>count($asin_contry),
            'page_count'=>count($new_list),
            'page'=>self::$param['page'],
            'page_size'=>self::$param['page_size'],
            'list'=>$new_list,
        ];
    }
    //获取基础信息
    private static function getBaseDataSku($key_,$c_list) {
        $dbF = dbFMysql::getInstance();
        //获取pasin对应的asin和sku
        $param = self::$param;
        $sql_list = [];
        foreach (self::$years as $year=>$month_list) {
            $dbF->table('table_month_count_'.$year)
                ->where("where is_delete=0")
                ->whereIn('reportDateMonth',self::$search_month);
            //国家
            if (!empty($param['country_code']) && $param['country_code'] != '[]') {
                $country_codes = json_decode($param['country_code']);
                $dbF->whereIn('countryCode',$country_codes);
            }
            //项目
            if (!empty($param['project_ids']) && $param['project_ids']!='[]') {
                $project_ids = json_decode($param['project_ids']);
                if (!self::$show_all_data) {
                    $project_ids = array_intersect(self::$project_ids,$project_ids);
                }
                $dbF->whereIn('project_id',$project_ids);
            }
            //权限运营+搜索
            $user_id = userModel::$qwuser_id;
            if (!empty($param['yunying_ids']) && $param['yunying_ids']!='[]') {
                if (!self::$show_all_data) {
                    $yunying_ids = array_intersect(self::$yunying_ids,json_decode($param['yunying_ids']));
                    $yunying_str = getYunyingSql($yunying_ids,self::$project_ids,$user_id);
                    $dbF->andWhere($yunying_str);
                } else {
                    $dbF->whereIn('yunying_id',json_decode($param['yunying_ids']));
                }
            } else {
                if (!self::$show_all_data) {
                    $yunying_str = getYunyingSql(self::$yunying_ids,self::$project_ids,$user_id);
                    $dbF->andWhere($yunying_str);
                }
            }
            //自定义搜索
            if (!empty($param['search_type']) && !empty($param['search_value'])) {
                if ($param['search_type'] == 'asin') {
                    $dbF->andWhere('asin like :asin',['asin'=>"%{$param['search_value']}%"]);
                }elseif ($param['search_type'] == 'sku') {
                    $dbF->andWhere('localSku like :sku',['sku'=>"%{$param['search_value']}%"]);
                } elseif ($param['search_type'] == 'p_asin') {
                    $dbF->andWhere('parentAsin like :parentAsin',['parentAsin'=>"%{$param['search_value']}%"]);
                }
            }
            if (self::$sql_use_sku) {
                $dbF->whereIn('localSku',self::$sql_sku_list);
            }
            $dbF->whereIn($key_,$c_list)
                ->field('parentAsin,asin,localSku,project_id,yunying_id,sid,countryCode,sum(totalSalesQuantity) as totalSalesQuantity')
                ->groupBy(['parentAsin','asin','localSku','project_id','yunying_id','sid','countryCode']);
            $sql_list[] = $dbF->getSql();
        }
        $mew_tabel = implode(' UNION ALL ',$sql_list);
        $dbF->tablep($mew_tabel,'un_table')
            ->field('parentAsin,asin,localSku,project_id,yunying_id,sid,countryCode,sum(totalSalesQuantity) as totalSalesQuantity')
            ->order('totalSalesQuantity desc')
            ->groupBy(['parentAsin','asin','localSku','project_id','yunying_id','sid','countryCode']);
        $msku_list = $dbF->list();

        //根据sku查询商品数据
        $sku_array = array_unique(array_column($msku_list,'localSku'));
        $goods_list = [];
        if (count($sku_array)) {
            $goods_list = $dbF->table('goods','a')
                ->leftJoin('goods_detail','b','b.sku = a.sku')
                ->whereIn('a.sku',$sku_array)
                ->orderByField('a.sku',$sku_array)
                ->field('a.sku,a.product_name,a.cid,b.pic_url,b.product_developer')
                ->list();
            //查询产品的等级
            $goods_level = $dbF->table('goods_level_relation','a')
                ->leftJoin('goods_level','b','b.id = a.level_id')
                ->where('where a.is_delete = 0')
                ->whereIn('m_date',self::$search_month)
                ->field('a.sku,b.level_name')
                ->list();
            //获取产品上两级分类
            if ($goods_list) {
                $cids = array_column($goods_list,'cid');
                $category_list = goodsInformationForm::getGoodsCate($cids);
            }
            foreach ($goods_list as $g_k=>$goods_) {
                $level_name_array = [];
                foreach ($goods_level as $level_) {
                    if ($goods_['sku'] == $level_['sku']) {
                        $level_name_array[] = $level_['level_name'];
                    }
                }
                $goods_list[$g_k]['level_name'] = $level_name_array;
                $goods_list[$g_k]['category_name'] = isset($category_list[$goods_['cid']])?$category_list[$goods_['cid']]['name']:'';
            }
        }
        $goods_ = [];
        foreach ($goods_list as $v) {
            $goods_[$v['sku']] = $v;
        }
        //运营
        $yunying_ids = array_unique(array_column($msku_list,'yunying_id'));
        $user_ = [];
        if (count($yunying_ids)) {
            $db = dbMysql::getInstance();
            $user_list = $db->table('qwuser')
                ->whereIn('id',$yunying_ids)
                ->field('id,wname')
                ->list();
            foreach ($user_list as $v) {
                $user_[$v['id']] = $v['wname'];
            }
        }
        //项目查询
        $project_ids = array_unique(array_column($msku_list,'project_id'));
        $project_ = [];
        if (count($project_ids)) {
            $project_list = $dbF->table('project')
                ->whereIn('id',$project_ids)
                ->field('id,p_id,project_name')
                ->list();
            //对应的上级查询
            $p_dis = array_unique(array_column($project_list,'p_id'));
            $list_project = $dbF->table('project','a')
                ->leftJoin('project','b','b.id =a.p_id')
                ->whereIn('a.id',$p_dis)
                ->field('a.id,a.project_name,b.project_name as p_name')
                ->list();
            foreach ($project_list as $k=>$v) {
                $project_[$v['id']]['name3'] = $v['project_name'];
                foreach ($list_project as $v1) {
                    if ($v['p_id'] == $v1['id']) {
                        $project_[$v['id']]['name1'] = $v1['p_name'];
                        $project_[$v['id']]['name2'] = $v1['project_name'];
                    }
                }
            }
        }
        //店铺
        $ids = array_unique(array_column($msku_list,'sid'));
        $store_list = $dbF->table('seller')
            ->whereIn('sid',$ids)
            ->field('sid,real_name')
            ->list();
        $store_ = [];
        foreach ($store_list as $v) {
            $store_[$v['sid']] = $v['real_name'];
        }
        //非新品新品查询
        $asins = array_column($msku_list,'asin');
        $first_time_list = listingFirstTimeFrom::getNotNewList($dbF,$asins);
        $first_time_ = [];
        foreach ($first_time_list as $v) {
            $first_time_[] = $v['asin'].'_'.$v['country_code'];
        }
        return [
            'msku_data'=>$msku_list,
            'goods_data'=>$goods_,
            'yunying_data'=>$user_,
            'store_data'=>$store_,
            'project_data'=>$project_,
            'no_new_goods'=>$first_time_
        ];
    }
    //获取基础信息
    private static function getGoodsDataWaring($lx_list) {
        $sku_array = array_column($lx_list,'sku');
        $dbF = dbFMysql::getInstance();
        //获取pasin对应的asin和sku
        //根据sku查询商品数据
        $goods_list = [];
        if (count($sku_array)) {
            $goods_list = $dbF->table('goods','a')
                ->leftJoin('goods_detail','b','b.sku = a.sku')
                ->whereIn('a.sku',$sku_array)
                ->field('a.sku,a.product_name,a.cid,a.category_name,a.is_new,b.pic_url,b.product_developer')
                ->list();
            //查询产品的等级
            $goods_level = $dbF->table('goods_level_relation','a')
                ->leftJoin('goods_level','b','b.id = a.level_id')
                ->where('where a.is_delete = 0')
                ->whereIn('a.m_date',self::$search_month)
                ->field('a.sku,b.level_name')
                ->list();
            //获取产品上两级分类
            $category_list = [];
            if ($goods_list) {
                $cids = array_column($goods_list,'cid');
                $category_list = goodsInformationForm::getGoodsCate($cids);
            }
            foreach ($goods_list as $g_k=>$goods_) {
                $level_name_array = [];
                foreach ($goods_level as $level_) {
                    if ($goods_['sku'] == $level_['sku']) {
                        $level_name_array[] = $level_['level_name'];
                    }
                }
                $goods_list[$g_k]['level_name'] = $level_name_array;
                $goods_list[$g_k]['category_name'] = isset($category_list[$goods_['cid']])?$category_list[$goods_['cid']]['name']:'';
            }
        }
        $goods_ = [];
        foreach ($goods_list as $v) {
            $goods_[$v['sku']] = $v;
        }
        //运营
        $yunying_ids = array_unique(array_column($lx_list,'yunying_id'));
        $user_ = [];
        if (count($yunying_ids)) {
            $db = dbMysql::getInstance();
            $user_list = $db->table('qwuser')
                ->whereIn('id',$yunying_ids)
                ->field('id,wname')
                ->list();
            foreach ($user_list as $v) {
                $user_[$v['id']] = $v['wname'];
            }
        }
        //项目查询
        $project_ids = array_unique(array_column($lx_list,'project_id'));
        $project_ = [];
        if (count($project_ids)) {
            $project_list = $dbF->table('project')
                ->whereIn('id',$project_ids)
                ->field('id,p_id,project_name')
                ->list();
            //对应的上级查询
            $p_dis = array_unique(array_column($project_list,'p_id'));
            $list_project = $dbF->table('project','a')
                ->leftJoin('project','b','b.id =a.p_id')
                ->whereIn('a.id',$p_dis)
                ->field('a.id,a.project_name,b.project_name as p_name')
                ->list();
            foreach ($project_list as $k=>$v) {
                $project_[$v['id']]['name3'] = $v['project_name'];
                foreach ($list_project as $v1) {
                    if ($v['p_id'] == $v1['id']) {
                        $project_[$v['id']]['name1'] = $v1['p_name'];
                        $project_[$v['id']]['name2'] = $v1['project_name'];
                    }
                }
            }
        }
        //店铺
        $ids = array_unique(array_column($lx_list,'sid'));
        $store_list = $dbF->table('seller')
            ->whereIn('sid',$ids)
            ->field('sid,real_name')
            ->list();
        $store_ = [];
        foreach ($store_list as $v) {
            $store_[$v['sid']] = $v['real_name'];
        }
        return [
            'goods_data'=>$goods_,
            'yunying_data'=>$user_,
            'store_data'=>$store_,
            'project_data'=>$project_,
        ];
    }
    //整理预警需要的数据
    public static function arrangeWaringList($lx_list) {
        $country_data = self::getCountryData();
        //商品信息，国家信息数据（按照asin整理好需要的数据）
        $base_data = self::getGoodsDataWaring($lx_list);
        $store_ = $base_data['store_data'];
        $goods_list = $base_data['goods_data'];
        $yunying_data = $base_data['yunying_data'];
        $project_data = $base_data['project_data'];
        //整理数据
        $new_list = [];
        foreach ($lx_list as $v) {
            $sku = $v['sku'];
            //商品信息
            if (isset($goods_list[$sku])) {
                foreach (authListModel::$base_auth as $key_list){
                    $key_ = $key_list['key'];
                    if (in_array($key_,['asin','p_asin','sku'])) {
                        continue;
                    }
                    if (in_array($key_,['product_name','category_name','product_developer','is_new','pic_url','level_name'])){
                        if (!in_array($key_,['sku'])) {
                            $v[$key_] = $goods_list[$sku][$key_];
                        }
                    } else {
                        if ($key_ == 'country') {
                            $v[$key_] = $country_data[$v['country_code']]??'';
                        } elseif ($key_ == 'store_name') {
                            $v[$key_] = $store_[$v['sid']]??'';
                        } elseif ($key_ == 'project') {
                            if ($v['project_id']) {
                                $v[$key_] = $project_data[$v['project_id']]['name3']??'';
                            }
                        } elseif ($key_ == 'yunying') {
                            if ($v['project_id']) {
                                $v[$key_] = $yunying_data[$v['yunying_id']]??'';
                            }
                        } elseif ($key_ == 'project1') {
                            if ($v['project_id']) {
                                $v[$key_] = $project_data[$v['project_id']]['name1']??'';
                            }
                        } else {
                            dd($key_);
                        }
                    }
                }
            } else {
                foreach (authListModel::$base_auth as $key_list){
                    $key_ = $key_list['key'];
                    if (in_array($key_,['asin','p_asin','sku'])) {
                        continue;
                    } else {
                        if (in_array($key_,['product_name','country','pic_url','store_name','project','yunying','category_name','product_developer','is_new'])) {
                            if ($key_ == 'country') {
                                $v[$key_] = $country_data[$v['country_code']]??'';
                            } else {
                                $v[$key_] = '';
                            }
                        } else {
                            $v[$key_] = [];
                        }
                    }
                }
            }
            $new_list[] = $v;
        }
        return $new_list;
    }
    //利润表整理数据（利润贡献数据整理）
    public static function arrangeSupplierList($lx_list,$oa_list) {
        //整理数据
        $new_list = [];
        //将数据整理为横向
        $oa_list_ = [];
        foreach ($oa_list as $v) {
            $key_ = $v['m_date'].'_'.$v['sku'].'_'.$v['supplier_id'];
            $oa_list_[$key_] = $v['total'];
        }
        foreach ($lx_list as $v) {
            $item = $v;
            $item['year'] = date('Y',strtotime($v['m_date']));
            $item['oa_key_4'] = 0;
            $key_ = $v['m_date'].'_'.$v['sku'].'_'.$v['supplier_id'];
            $item['oa_key_4'] = $oa_list_[$key_]??0;
            $new_list[] = $item;
        }
        return array_values($new_list);
    }
    public static function arrangeGoodsList($lx_list,$oa_list) {
        //整理数据
        $new_list = [];
        //将数据整理为横向
        foreach ($lx_list as $v) {
            $item = $v;
            $item['year'] = date('Y',strtotime($v['m_date']));
            $item['oa_key_4'] = 0;
            foreach ($oa_list as $k1=>$v1) {
                if ($v1['m_date'] == $v['m_date'] && $v1['sku'] == $v['sku']) {
                    $item['oa_key_4'] = $v1['total'];
                    unset($oa_list[$k1]);
                }
            }
            $new_list[] = $item;
        }
        return array_values($new_list);
    }

    //获取字段类型
    private static function getColumnTypeList(){
        //获取字段类型
        $column_type = config::get('column_type','data_financial');
        $column_type_list = [];
        foreach ($column_type as $v) {
            $column_type_list[$v['id']] = $v['name'];
        }
        return $column_type_list;
    }

    //商品利润贡献,分页
    public static function getGoodsPageLists($list) {
        $db = dbFMysql::getInstance();

        // 分页获取 goods 数据
        $page = 1;
        $pageSize = 100; // 每页处理 100 条 SKU
        $totalRecords = 0; // 初始化总记录数
        $sku_ids = []; // 保存所有 SKU ID

        $result_list = []; // 保存最终处理结果

        do {
            // 获取分批次 goods 数据
            $goods_list = $db->table('goods')
                ->field('id,category_name,sku,product_name')
                ->pages($page, $pageSize);

            if (empty($goods_list['list'])) {
                break;
            }

            $s_list = $goods_list['list'];
            $sku_ids = array_column($s_list, 'sku');

            // 筛选出当前批次相关数据
            $batch_list = array_filter($list, function ($row) use ($sku_ids) {
                return in_array($row['sku'], $sku_ids);
            });

            // 显示的月份
            $search_month = [];
            $month = self::$param['date_time'][0];
            while (strtotime($month) <= strtotime(self::$param['date_time'][1])) {
                $search_month[] = $month;
                $month = date('Y-m', strtotime($month . ' +1 month'));
            }

            // 显示的年份
            $years = array_keys(self::$years);
            arsort($years);
            arsort($search_month);

            // 处理年度数据
            foreach ($s_list as $k => $v) {
                $sku = $v['sku'];
                $year_data = [];
                $year_amount = 0;

                foreach ($years as $year) {
                    $year_data_list = array_filter($batch_list, function ($row) use ($year, $sku) {
                        return $row['sku'] == $sku && $row['year'] == $year;
                    });

                    $year_total = array_sum(array_column($year_data_list, 'oa_key_4'));
                    foreach (self::$aggregation_keys as $key) {
                        $year_total += array_sum(array_column($year_data_list, $key));
                    }

                    $year_data[$year] = roundToString($year_total);
                    $year_amount += $year_total;
                }

                $year_data['year_amount'] = roundToString($year_amount);
                $s_list[$k]['year_data'] = $year_data;
            }

            // 处理月度数据
            foreach ($s_list as $k => $v) {
                $sku = $v['sku'];
                $new_item = [];
                $month_amount = 0;

                foreach ($search_month as $month) {
                    $month_total = 0;

                    $month_data_list = array_filter($batch_list, function ($row) use ($month, $sku) {
                        return $row['sku'] == $sku && $row['m_date'] == $month;
                    });

                    if (!empty($month_data_list)) {
                        $month_data = array_values($month_data_list)[0];
                        $month_total = $month_data['oa_key_4'];

                        foreach (self::$aggregation_keys as $key) {
                            $month_total += $month_data[$key];
                        }
                    }

                    $new_item[$month] = roundToString($month_total);
                    $month_amount += $month_total;
                }

                $new_item['month_amount'] = roundToString($month_amount);
                $s_list[$k]['month_data'] = $new_item;
            }

            // 合并当前批次的结果
            $result_list = array_merge($result_list, $s_list);

            // 保存总记录数
            $totalRecords = $goods_list['total'];
            $page++;
        } while (($page - 1) * $pageSize < $totalRecords);
        return [
            'count' => $totalRecords,
            'page_count' => count($result_list),
            'page' => self::$param['page'],
            'page_size' => self::$param['page_size'],
            'list' => $result_list,
        ];
    }




























}