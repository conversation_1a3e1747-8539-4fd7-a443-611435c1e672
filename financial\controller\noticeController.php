<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/12 18:00
 */

namespace financial\controller;

use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use financial\form\noticeForm;

class noticeController
{
    //列表
    public function getList(){
        $paras_list = array('page_size','page','id');
        $param = arrangeParam($_POST, $paras_list);
        $db = dbFMysql::getInstance();
        if ($param['id']){

            $list = $db->table('notice')
                ->where('where is_delete = 0 and type = :type', ['type' => $param['id']] )
                ->order('id desc')
                ->pages($param['page'], $param['page_size']);

            if (count($list['list'])) {
                //获取通知人信息
                $list['list'] = noticeForm::getNoticeUser( $list['list']);
            }
        }else{
            $list = $db->table('notice')
                ->where('where is_delete = 0')
                ->order('id desc')
                ->pages($param['page'], $param['page_size']);
            if (count($list['list'])) {
                //获取通知人信息
                $list['list'] = noticeForm::getNoticeUser( $list['list']);
            }

        }


        returnSuccess($list);
    }
    //新增,编辑通知
    public function editNotice() {
        $paras_list = array('id','notice_type','user_ids1');
        $request_list = ['notice_type'=>'消息类型','user_ids1'=>'通知人'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $id = (int)$param['id'];
        $type = (int)$param['notice_type'];
        $user_ids1 = json_decode($param['user_ids1']);
        if (!count($user_ids1)) {
            returnError('请选择通知人');
        }
//        if ($type == 9 && !count($user_ids2)) {
//            returnError('请选择第二次同步通知人');
//        }
//        if (count($user_ids1) > 20) {
//            returnError('通知人');
//        }
        $db = dbFMysql::getInstance();
        if ($id) {
            $notice = $db->table('notice')
                ->where('where id=:id and is_delete=0',['id'=>$id])->one();
            if (!$notice) {
                returnError('未查询到该通知');
            }
            //消息类型可修改
            $db->table('notice')->where('where id=:id',['id'=>$id])
                ->update([
                    'user_ids1'=>json_encode($user_ids1),
                    'updated_time'=>date('Y-m-d H:i:s'),
                ]);
            returnSuccess('','修改成功');
        } else {
            $notice_count = $db->table('notice')
                ->where('where type=:type and is_delete=0',['type'=>$type])
                ->one();
            if ($notice_count) {
                returnError('【'.config::getDataName1('data_financial','notice_type',$type).'】通知已存在，不可重复添加');
            }
            $db->table('notice')->insert([
                'type'=>$type,
                'user_ids1'=>json_encode($user_ids1),
                'created_time'=>date('Y-m-d H:i:s'),
            ]);
            returnSuccess('','添加成功');
        }
    }
    //删除通知
    public function del() {
        $paras_list = array('id');
        $request_list = ['id'=>'通知ID'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $id = (int)$param['id'];
        $db = dbFMysql::getInstance();
        $notice = $db->table('notice')
            ->where('where id=:id and is_delete=0',['id'=>$id])->one();
        if (!$notice) {
            returnError('未查询到该通知');
        }
        $db->table('notice')
            ->where('where id=:id',['id'=>$id])
            ->update(['is_delete'=>1,'updated_time'=>date('Y-m-d H:i:s')]);
        returnSuccess('','删除成功');
    }

}