openapi: 3.0.0
info:
  title: 店铺申诉接口
  version: 1.0.0
  description: 店铺申诉系统相关接口

paths:
  /shop/shopAppeal/getList:
    get:
      summary: 获取申诉列表
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
          description: 页码
        - name: page_size
          in: query
          schema:
            type: integer
            default: 20
          description: 每页数量
        - name: shop_id
          in: query
          schema:
            type: integer
          description: 店铺ID
        - name: status
          in: query
          schema:
            type: string
          description: 状态
        - name: follower_id
          in: query
          schema:
            type: integer
          description: 跟进人ID
        - name: issue_type
          in: query
          schema:
            type: string
          description: 问题类型
        - name: complaint_type
          in: query
          schema:
            type: string
          description: 投诉类型
        - name: date_start
          in: query
          schema:
            type: string
            format: date
          description: 开始日期
        - name: date_end
          in: query
          schema:
            type: string
            format: date
          description: 结束日期
      responses:
        '200':
          description: 成功返回申诉列表

  /shop/shopAppeal/create:
    post:
      summary: 创建申诉
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              required:
                - shop_id
                - issue_type
              properties:
                shop_id:
                  type: integer
                  description: 店铺ID
                issue_type:
                  type: string
                  description: 问题类型
                warning_screenshot:
                  type: string
                  description: 警告截图
                performance_screenshot:
                  type: string
                  description: 表现截图
                reason_analysis:
                  type: string
                  description: 原因分析
                shop_status:
                  type: string
                  description: 店铺状态
                frozen_funds:
                  type: number
                  description: 冻结资金
                currency:
                  type: string
                  description: 货币类型
                inventory:
                  type: integer
                  description: 库存数量
                complaint_type:
                  type: string
                  description: 投诉类型
                reminder_interval:
                  type: integer
                  description: 提醒间隔
      responses:
        '200':
          description: 成功创建申诉

  /shop/shopAppeal/cancel:
    post:
      summary: 撤销申请
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              required:
                - id
              properties:
                id:
                  type: integer
                  description: 申诉ID
      responses:
        '200':
          description: 成功撤销申请

  /shop/shopAppeal/assignFollower:
    post:
      summary: 配置跟进人
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              required:
                - id
                - follower_id
              properties:
                id:
                  type: integer
                  description: 申诉ID
                follower_id:
                  type: integer
                  description: 跟进人ID
      responses:
        '200':
          description: 成功配置跟进人

  /shop/shopAppeal/follow:
    post:
      summary: 跟进申诉
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              required:
                - id
                - reason_analysis
              properties:
                id:
                  type: integer
                  description: 申诉ID
                reason_analysis:
                  type: string
                  description: 跟进内容
      responses:
        '200':
          description: 成功跟进申诉

  /shop/shopAppeal/process:
    post:
      summary: 处理申诉
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              required:
                - id
                - review_result
                - status
              properties:
                id:
                  type: integer
                  description: 申诉ID
                review_result:
                  type: string
                  description: 审核结果
                status:
                  type: string
                  description: 状态(success/failed)
      responses:
        '200':
          description: 成功处理申诉

  /shop/shopAppeal/remind:
    post:
      summary: 提醒功能
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              required:
                - id
              properties:
                id:
                  type: integer
                  description: 申诉ID
      responses:
        '200':
          description: 成功发送提醒

  /shop/shopAppeal/getDetail:
    get:
      summary: 获取申诉详情
      parameters:
        - name: id
          in: query
          required: true
          schema:
            type: integer
          description: 申诉ID
      responses:
        '200':
          description: 成功返回申诉详情
