# !/bin/bash
# 领星listing店铺更新
token='01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0'
qwUrl='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=03a8032d-65c0-4390-a588-9a6b2cf11336'

warehouseType=(
  1
  3
  4
  6
)

for type in "${warehouseType[@]}"; do
  while true; do
    # 发送POST请求，这里假设URL为http://example.com/api，根据实际情况替换
    # response=$(curl -s -X POST -d "token=$token" 'http://171.223.214.187:8901/task/lingXingApi/synWarehouse'
    # response=$(curl -s -X POST -d "token=$token" 'http://39.101.133.112:8082/task/lingXingApi/synWarehouse')
    response=$(curl -s -X POST -d "token=$token&type=$type" 'http://oa.ywx.com/task/lingXingApi/synWarehouse')
    echo "$response"
    # 从响应中提取code字段的值
    code=$(echo "$response" | grep -oP '"code":\K-?\d+')
    echo "code=$code"
    # 从响应中提取msg字段的值
    msg=$(echo "$response" | grep -oP '"message"\s*:\s*"\K[^"]+')
    echo "msg=$msg"
    # 检查code字段的值
    current_time=$(date +"%Y-%m-%d %H:%M:%S")
    if [ "$code" -ne 2 ]; then
        echo "code=$code，继续请求$current_time"
    else
       break ;
    fi
  done
  echo "领星仓库同步完成，$current_time"
  # 发送企微机器人
  curl "$qwUrl" -H "Content-Type: application/json" -d "{\"msgtype\":\"text\",\"text\":{\"content\":\"领星仓库同步完成，code=$code，msg=$msg\"}}"
done;