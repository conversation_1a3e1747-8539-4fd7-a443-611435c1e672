<?php

namespace plugins\shop\models;

class creditCardFeeModel extends baseModel 
{
    public string $table = 'credit_card_fee';


    public static array $paras_list = [
        'pid' => 'pid',
        'credit_card_id' => '信用卡ID',
        'fee_year' => '费用年份',
        'amount' => '费用金额',
        'pay_file' => '付款文件',
        'pay_date' => '付款日期',
        'type' => '类型',
        'remark' => '备注'
    ];

    const TYPE_APPLY = 1; // 申请
    const TYPE_CONFIRM = 2; // 付款
    const TYPE_CANCEL = 3; // 撤销

    // 获取列表
    public function getList($param)
    {
        $this->db->table($this->table, 'f')
            ->where('1=1');

        if (!empty($param['credit_card_id'])) {
            $this->db->whereIn('f.credit_card_id', $param['credit_card_id']);
        }
        if (isset($param['status'])) {
            $this->db->andWhere('f.status = :status', ['status' => $param['status']]);
        }

        $this->db->order('f.id desc');

        return $this->db->list();
    }

    // 新增费用申请
    public function add($data, $type = '新增')
    {
        $data['operator'] = userModel::$qwuser_id ?? 0;
        return $this->db->table($this->table)->insert($data);
    }

    public function edit($data, $id, $old_data = [], $type = '', $remark = '', $result = '', $other_attach = [])
    {
        $data['operator'] = userModel::$qwuser_id ?? 0;
        $this->db->table($this->table)->where('id = :id', ['id' => $id])->update($data);
    }
}
