<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/22 10:58
 */

namespace  plugins\goods\form;

use plugins\goods\models\goodsAppFunctionTestModel;
use plugins\goods\models\userModel;
use core\lib\db\dbMysql;
use core\lib\log;

class goodsAppFunctionTestFrom
{
    //APP功能验证 保存
    public static function saveAppTest($param) {
        $project_id = $param['project_id'];
        $type_des_kes = goodsAppFunctionTestModel::$type_des_kes;
        foreach ($type_des_kes as $v) {
            if (mb_strlen($param[$v], 'UTF-8')  > 500) {
                SetReturn(-1,'测试失败原因不得超过500个字符');
            }
        }
        //事件数据保存验证
        $data = goodsProjectFrom::verifyEventSave($project_id,$param['node_index'],$param['event_index'],3);
        $project = $data['project'];
        $db = dbMysql::getInstance();
        $app_test = $db->table('goods_app_function_test')
            ->where('where project_id=:project_id and node_index=:node_index and event_index=:event_index',['project_id'=>$project_id,'node_index'=>$param['node_index'],'event_index'=>$param['event_index']])
            ->one();
        $insert_data = [
            'noise'=>(int)$param['noise'],
            'stall'=>(int)$param['stall'],
            'stall_not_link'=>(int)$param['stall_not_link'],
            'button_lights'=>(int)$param['button_lights'],
            'bt_switch'=>(int)$param['bt_switch'],
            'recommendation'=>(int)$param['recommendation'],
            'video'=>(int)$param['video'],
            'diy'=>(int)$param['diy'],
            'voice'=>(int)$param['voice'],
            'built'=>(int)$param['built'],
            'noise_des'=>$param['noise_des'],
            'stall_des'=>$param['stall_des'],
            'stall_not_link_des'=>$param['stall_not_link_des'],
            'button_lights_des'=>$param['button_lights_des'],
            'bt_switch_des'=>$param['bt_switch_des'],
            'recommendation_des'=>$param['recommendation_des'],
            'video_des'=>$param['video_des'],
            'diy_des'=>$param['diy_des'],
            'voice_des'=>$param['voice_des'],
            'built_des'=>$param['built_des'],
        ];
        if ($app_test) {
            $insert_data['update_time'] = date('Y-m-d H:i:s');
            $db->table('goods_app_function_test');
            $db->where('where id=:id',['id'=>$app_test['id']])
                ->update($insert_data);
        } else {
            $insert_data['created_time'] = date('Y-m-d H:i:s');
            $insert_data['user_id'] = userModel::$qwuser_id;
            $insert_data['goods_id'] = $project['goods_id'];
            $insert_data['project_id'] = $project_id;
            $insert_data['node_index'] = $param['node_index'];
            $insert_data['event_index'] = $param['event_index'];
            $db->insert($insert_data);
            $app_test = [];
        }
        //修改日志
        self::setAppTestLog($app_test,$insert_data,$project);
    }
    //修改记录
    public static function setAppTestLog($old_data, $update_data,$project)
    {
        $describe = '';
        $types = ['待测试','通过','未通过'];
        $type_kes = goodsAppFunctionTestModel::$type_kes;
        if ($old_data == []) {
            //新增
            $describe = 'APP功能测试数据新增';
        } else {
            //修改
            $varify_key = goodsAppFunctionTestModel::$field_log_list;
            foreach ($update_data as $k=>$v) {
                if (isset($varify_key[$k])) {
                    if ($old_data[$k] != $v) {
                        if (in_array($k,$type_kes)) {
                            $describe .= $varify_key[$k].'：【'.$types[$old_data[$k]].'->'.$types[$v].'】；';
                        } else {
                            $describe .= $varify_key[$k].'：【'.$old_data[$k].'->'.$v.'】;';
                        }
                    }
                }
            }
            $describe = trim($describe,',');
        }
        log::setGoodsProjectLog($project['goods_id'],$project['id'], $describe, $project['matter_name']);
    }

    /**
     * @param $project_id
     * @param $node_index
     * @param $event_index
     * @return void
     * @throws \core\lib\ExceptionError app功能测试提交验证
     */
    public static function varifyAppFunctionTestSubmit($project_id,$node_index,$event_index)
    {
        $db = dbMysql::getInstance();
        $data = $db->table('goods_app_function_test')
            ->where('where project_id=:project_id and node_index=:node_index and event_index=:event_index',['project_id'=>$project_id,'node_index'=>$node_index,'event_index'=>$event_index])
            ->one();
        if (!$data) {
            SetReturn(-1,'请先完成【APP功能验证】');
        }
        $type_kes = goodsAppFunctionTestModel::$type_kes;
        $field_log_list = goodsAppFunctionTestModel::$field_log_list;
        foreach ($type_kes as $v) {
            if ($data[$v] == 0) {
                SetReturn(-1,'【APP功能验证】的【'.$field_log_list[$v].'】功能未测试。');
            }
        }
    }
}