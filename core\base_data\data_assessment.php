<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/14 9:20
 */
return array(
    // 条件类型
    'rule_type' => [
        ['id' => '1', 'name' => '条件'],
        ['id' => '2', 'name' => '条件组'],
    ],
    // 多个条件的连接符
    'link_symbol' => [
        ['id' => '1', 'name' => '或', 'value' => '||'],
        ['id' => '2', 'name' => '且', 'value' => '&&'],
    ],

    // 提成比例规则符号
    'commission_rules_symbol' => [
        ['id' => '1', 'name' => '>', 'value' => '>'],
        ['id' => '2', 'name' => '≥', 'value' => '>='],
        ['id' => '3', 'name' => '<', 'value' => '<'],
        ['id' => '4', 'name' => '≤', 'value' => '<='],
        ['id' => '5', 'name' => '=', 'value' => '=='],
    ],

    // 等级规则的月份选择
    'level_rules_month' => [
        ['id' => '1', 'name' => '本次考核周期'],
        ['id' => '2', 'name' => '上次考核周期'],
        ['id' => '3', 'name' => '连续3次考核周期（包含本次）'],
        ['id' => '4', 'name' => '连续6次考核周期（包含本次）']
    ],

    // 等级规则的计算符号
    'level_rules_symbol' => [
        ['id' => '1', 'name' => '>', 'value' => '>'],
        ['id' => '2', 'name' => '≥', 'value' => '>='],
        ['id' => '3', 'name' => '<', 'value' => '<'],
        ['id' => '4', 'name' => '≤', 'value' => '<='],
        ['id' => '5', 'name' => '=', 'value' => '=='],
        ['id' => '6', 'name' => '≠', 'value' => '!='],
    ],

    // 考核指标类型
    'assessment_targets_type' => [
        ['id' => '1', 'name' => '定量'],
        ['id' => '2', 'name' => '定性'],
    ],
    // 考核指标类型
    'assessment_targets_source' => [
        ['id' => '1', 'name' => '财务管理', 'list' => [
            ['id' => "1", 'column_name' => '销售额','custom_id' => '3'],
            ['id' => "2", 'column_name' => '毛利润','custom_id' => '4'],
            ['id' => "3", 'column_name' => '毛利率','custom_id' => '6'],
            ['id' => "4", 'column_name' => '销售额环比','custom_id' => ''],
            ['id' => "5", 'column_name' => '毛利率达成率','custom_id' => ''],
        ]],
        ['id' => '2', 'name' => '备货', 'list' => [
            ['id' => "1", 'column_name' => '周转天数'],
            ['id' => "2", 'column_name' => '运输成本'],
            ['id' => "3", 'column_name' => '专线'],
            ['id' => "4", 'column_name' => '快线'],
        ]],
//        ['id' => '2', 'name' => '产品管理'],
//        ['id' => '3','name'=>'易威行ERP'],
//        ['id' => '4','name'=>'库存管理'],
        ['id' => '-1','name'=>'暂不可拉取数据'],
    ],

    'assessment_targets_source_custom' => [
        ['custom_id' => '3', 'name' => '销售额'],   
        ['custom_id' => '4', 'name' => '毛利润'],
        // ['custom_id' => '6', 'name' => '毛利率'], // 不能直接使用
        ['custom_id' => '59', 'name' => '福利政策'],
        ['custom_id' => '76', 'name' => 'VAT'],
        // ['custom_id' => 'key6', 'name' => '预留资金'],
        // ['custom_id' => 'key14', 'name' => '海外推广'],
    ],

    // 考核指标符号(用于阶梯)
    'assessment_targets_symbol' => [
        ['id' => '1', 'name' => '小于', 'value' => '<'],
        ['id' => '2', 'name' => '小于等于', 'value' => '<='],
        ['id' => '3', 'name' => '大于', 'value' => '>'],
        ['id' => '4', 'name' => '大于等于', 'value' => '>='],
        ['id' => '5', 'name' => '等于', 'value' => '='],
        ['id' => '6', 'name' => '在范围内', 'value' => 'between'],
    ],

    // 指标考核阶梯值类型
    'assessment_targets_stage_value_type' => [
        ['id' => '1', 'name' => '指标值'],
        ['id' => '2', 'name' => '指标分数'],
        ['id' => '3', 'name' => '自定义'],
    ],

    // 指标考核方式，用于定量指标
    'assessment_targets_method' => [
        ['id' => '1', 'name' => '通过公式计算'],
        ['id' => '2', 'name' => '按阶梯划分'],
    ],

    // 指标考核公式值类型
    'assessment_targets_value_type' => [
        ['id' => '1', 'name' => '个人', 'list' => [
            ['id' => '1', 'name' => '指标考核值'],
            ['id' => '2', 'name' => '指标实际值'],
            ['id' => '3', 'name' => '自评分数'],
            ['id' => '4', 'name' => '上级评分']
        ]],
        ['id' => '2', 'name' => '部门', 'list' => [
            ['id' => '1', 'name' => '指标考核值'],
            ['id' => '2', 'name' => '指标实际值'],
        ]],
        ['id' => '3', 'name' => '自定义'],
    ],

    // 个人
    'assessment_targets_value_type_1' => [
        ['id' => '1', 'name' => '指标考核值'],
        ['id' => '2', 'name' => '指标实际值'],
        ['id' => '3', 'name' => '自评分数'],
        ['id' => '4', 'name' => '上级评分']
    ],

    // 部门
    'assessment_targets_value_type_2' => [
        ['id' => '1', 'name' => '指标考核值'],
        ['id' => '2', 'name' => '指标实际值'],
        ['id' => '3', 'name' => '自定义值'],
    ],

    // 取数规则，用于定性指标
    'assessment_targets_calc_type' => [
        ['id' => '1', 'name' => '原始数据', 'method' => ''],
        ['id' => '2', 'name' => '四舍五入', 'method' => 'round'],
        ['id' => '3', 'name' => '向上取整', 'method' => 'ceil'],
        ['id' => '4', 'name' => '向下取整', 'method' => 'floor'],
    ],

    // 公式计算符号
    'formula_symbol' => [
        ['id' => '1', 'name' => '+'],
        ['id' => '2', 'name' => '-'],
        ['id' => '3', 'name' => '*'],
        ['id' => '4', 'name' => '/'],
    ],

    // 考核周期
    'assessment_cycle' => [
        ['id' => '1', 'name' => '月度'],
        ['id' => '2', 'name' => '季度'],
        ['id' => '3', 'name' => '半年度'],
        ['id' => '4', 'name' => '年度'],
        ['id' => '5', 'name' => '试用期'],
    ],

    // 考核类型
    'assessment_type' => [
        ['id' => '1', 'name' => '绩效'],
        ['id' => '2', 'name' => '提成'],
        ['id' => '3', 'name' => '薪资'],
    ],

    // 计分方式
    'score_type' => [
        ['id' => '1', 'name' => '加权计算'],
        ['id' => '2', 'name' => '加和计算'],
    ],

    // 分制
    'score_system' => [
        ['id' => '1', 'name' => '百分制'],
        ['id' => '2', 'name' => '十分制'],
        ['id' => '3', 'name' => '自定义分制'],
    ],

    // 无需考核对象
    'no_need_assessment_type' => [
        ['id' => '1', 'name' => '指定人员'],
        ['id' => '2', 'name' => '试用期员工'],
        ['id' => '2', 'name' => '正式员工'],
    ],

    // 考核模版
    'assessment_template' => [
        ['id' => '1', 'name' => '系统模版'],
        ['id' => '2', 'name' => '设计组考核模板'],
    ],

    // 考核发起类型
    'assessment_launch_type' => [
        ['id' => '1', 'name' => '自动发起'],
        ['id' => '2', 'name' => '手动发起'],
    ],

    // 绩效核算方式
    'performance_calculation_method' => [
        ['id' => '1', 'name' => '按比例核算'],
        ['id' => '2', 'name' => '按阶梯奖励'],
        ['id' => '3', 'name' => '按等级核算'],
        ['id' => '4', 'name' => '手动录入'],
    ],

    // 绩效核算值类型
    'assessment_calc_value_type' => [
        ['id' => '1', 'name' => '个人', 'list' => [
            ['id' => '1', 'name' => '财务-业绩指标'],// 还有第三层
            ['id' => '2', 'name' => '提成比例'],// 还有第三层
            ['id' => '3', 'name' => '绩效', 'list' => [
                ['id' => '1', 'name' => '绩效考核结果'],
            ]],
        ]],
        ['id' => '2', 'name' => '部门', 'list' => [
            ['id' => '1', 'name' => '财务-业绩指标'],
            ['id' => '2', 'name' => '提成比例'],// 还有第三层
            ['id' => '3', 'name' => '绩效', 'list' => [
                ['id' => '1', 'name' => '员工绩效'],
            ]],
            ['id' => '4', 'name' => '员工业绩']
        ]],
        ['id' => '3', 'name' => '自定义'],

    ],
    // 个人
    'assessment_calc_value_type_1' => [
        ['id' => '1', 'name' => '财务-业绩指标'],
        ['id' => '2', 'name' => '提成比例'],
        ['id' => '3', 'name' => '绩效考核结果']
    ],

    // 部门
    'assessment_calc_value_type_2' => [
        ['id' => '1', 'name' => '财务-业绩指标'],
        ['id' => '2', 'name' => '提成比例'],
        ['id' => '3', 'name' => '员工绩效'],
        ['id' => '4', 'name' => '员工业绩']
    ],

    // 绩效核算值类型（用于阶梯）
    'assessment_calc_symbol' => [
        ['id' => '1', 'name' => '小于', 'value' => '<'],
        ['id' => '2', 'name' => '小于等于', 'value' => '<='],
        ['id' => '3', 'name' => '大于', 'value' => '>'],
        ['id' => '4', 'name' => '大于等于', 'value' => '>='],
        ['id' => '5', 'name' => '等于', 'value' => '='],
    ],

    // 审批人
    'approver' => [
        ['id' => '1', 'name' => '指定人员'],
        ['id' => '2', 'name' => '直属上级'],
    ],

    // 发布通知人
    'notice_person' => [
        ['id' => '1', 'name' => '方案中的所有人员'],
        ['id' => '2', 'name' => '指定人员'],
    ],

    // 考核方案流程
    'assessment_scheme_process' => [
        ['id' => '1', 'name' => '发起考核'],
        ['id' => '2', 'name' => '工作简述'],
        ['id' => '3', 'name' => '自评'],
        ['id' => '4', 'name' => '上级评分'],
        ['id' => '5', 'name' => '绩效核算'],
        ['id' => '6', 'name' => '审批'],
//        ['id' => '7', 'name' => '绩效确认'],
    ],

    'assessment_status' => [
        ['id' => '-1', 'name' => '任务生成中'],
        ['id' => '0', 'name' => '考核中'],
        ['id' => '1', 'name' => '考核完成'],
        ['id' => '2', 'name' => '已暂停'],
        ['id' => '3', 'name' => '已终止'],
    ],

    'assessment_stage' => [
        ['id' => '97', 'name' => '待审批'],
        ['id' => '98', 'name' => '审批通过'],
        ['id' => '99', 'name' => '审批曾通过且待审批'],
    ],

    'result_view' => [
        ['id' => '1', 'name' => '上级评分'],
        ['id' => '2', 'name' => '评分意见'],
        ['id' => '3', 'name' => '指标考核结果'],
        ['id' => '4', 'name' => '绩效考核结果'],
        ['id' => '5', 'name' => '绩效/提成/薪资'],
    ],


);