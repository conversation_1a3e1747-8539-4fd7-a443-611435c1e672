<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/13 9:10
 */

namespace plugins\checkin\controller;

use plugins\checkin\models\userModel;
use core\lib\db\dbMysql;
use Rap2hpoutre\FastExcel\FastExcel;

class userInfoController
{
    private static $paras_list = [
        'wname'        => '姓名',
        'qwdepartment' => '部门',
        'base_salary'  => '基本工资',
        'total_salary' => '综合工资',
        'hire_date'    => '入职时间',
        'remark'       => '备注',
    ];

    // 列表
    public function getList()
    {
        returnError('功能已废弃');
        $paras_list = array('page', 'page_size', 'users', 'keywords');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        $db = dbMysql::getInstance();
        $db->table('user_info');
        if (!empty($param['users'])) {
            $users = json_decode($param['users'], true);
            if (!empty($users)) {
                $db->whereIn('wid', $users);
            }
        }
        $param['keywords'] = json_decode($param['keywords'], true);
        if (!empty($param['keywords'])) {
            $str = [];
            $str_arr = [];
            $idx = 0;
            foreach ($param['keywords'] as $keyword) {
                $str[] = 'wname like :keyword'.$idx;
                $str_arr['keyword'.$idx] = '%'.$keyword.'%';
                $idx++;
            }

            $db->where('where ('.implode(' or ', $str).')', $str_arr);
        }


        $data = $db->pages($page, $limit);
        if (empty($data['list'])) returnSuccess($data);


        $user_ids = array_column($data['list'], 'qwuser_id');
        $userMap = $db->table('qwuser')->whereIn('id', $user_ids)->field('id, wname, wdepartment_ids')->list();
        $userMap = array_column($userMap, null, 'id');

        $department = $db->table('qwdepartment')->field('wp_id, name')->list();
        $department = array_column($department, 'name', 'wp_id');

        foreach ($data['list'] as &$item) {
            $item['user_name'] = $userMap[$item['qwuser_id']]['wname'] ?? $item['wname'];
            $item['user_department'] = [];
            if (isset($userMap[$item['qwuser_id']])) {
                $wdepartment_ids = json_decode($userMap[$item['qwuser_id']]['wdepartment_ids'], true);
                foreach ($wdepartment_ids as $id) {
                    $item['user_department'][] = $department[$id];
                }
                $item['user_department'] = implode(',', $item['user_department']);
            } else {
                $item['user_department'] = $item['departs_name'];
            }

            if (!userModel::getUserListAuth('userSalary')) {
                $item['base_salary'] = '****';
                $item['total_salary'] = '****';
            }
        }
        returnSuccess($data);
    }

    // 编辑
    public function edit()
    {
        returnError('功能已废弃');
        $paras_list = array('id', 'base_salary', 'total_salary', 'hire_date', 'remark');
        $request_list = ['id' => 'ID', 'base_salary' => '基本工资', 'total_salary' => '综合工资', 'hire_date' => '入职时间'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $id = $param['id'];
        $db = dbMysql::getInstance();
        $user_info = $db->table('user_info')->where('where id = :id', ['id' => $id])->one();
        if (empty($user_info)) {
            returnError('用户信息不存在');
        }
        $data = [
            'hire_date' => $param['hire_date'],
            'remark' => $param['remark'],
        ];
        // 有数据权限的用户才能编辑工资
        if (userModel::getUserListAuth('userSalary')) {
            $data['base_salary'] = $param['base_salary'];
            $data['total_salary'] = $param['total_salary'];
        }
        $db->table('user_info')->where('where id = :id', ['id' => $id])->update($data);
        returnSuccess([], '编辑成功');
    }

    // 导出模版
    public function exportTemplate()
    {
        returnError('功能已废弃');
        $users = dbMysql::getInstance()->table('qwuser')->field('id, wid, wname, wdepartment_ids')->list();
        $department = dbMysql::getInstance()->table('qwdepartment')->field('wp_id, name')->list();
        $department = array_column($department, 'name', 'wp_id');

        $new_data = [];

        // 查询已有的用户信息
        $user_info_data = dbMysql::getInstance()->table('user_info')->list();
        $user_info = array_column($user_info_data, null, 'wid');

        foreach ($users as $user) {
            $wdepartment_ids = json_decode($user['wdepartment_ids'], true);
            $wdepartment = [];
            foreach ($wdepartment_ids as $id) {
                $wdepartment[] = $department[$id];
            }
            $item = [
                '姓名'     => $user['wname'],
                '账号'     => $user['wid'],
                '部门'     => implode(',', $wdepartment),
                '基本工资' => $user_info[$user['wid']]['base_salary'] ?? '',
                '综合工资' => $user_info[$user['wid']]['total_salary'] ?? '',
                '入职时间' => $user_info[$user['wid']]['hire_date'] ?? '',
                '备注'     => $user_info[$user['wid']]['remark'] ?? '',
            ];
            $new_data[$user['wid']] = $item;
        }

        // 非user表数据
        foreach ($user_info as $info) {
            if (!array_key_exists($info['wid'], $new_data)) {
                $new_data[$info['wid']] = [
                    '姓名'     => $info['wname'],
                    '账号'     => $info['wid'],
                    '部门'     => $info['departs_name'],
                    '基本工资' => $info['base_salary'],
                    '综合工资' => $info['total_salary'],
                    '入职时间' => $info['hire_date'],
                    '备注'     => $info['remark'],
                ];
            }
        }
        $new_data = array_values($new_data);

        //保存
        $save_path = "/public/checkin/temp/user";
        $url = SELF_FK . $save_path;

        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path . "/" . date('YmdHis') . uniqid() . '.xlsx';
        $url = SELF_FK . $path;
        if (!file_exists($url)) {
            touch($url);
        }
        (new FastExcel($new_data))->export($url);
        returnSuccess(['path' => $path]);
    }

    // 导入用户信息
    public function import()
    {
        returnError('功能已废弃');
        $paras_list = array('excel_src');
        $request_list = ['excel_src' => '表格链接'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $excel_url = SELF_FK . $param['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格不存在');
        }

        $excel_data = (new FastExcel)->import($excel_url);
        $data = $excel_data->toArray();
        if (!count($data)) {
            returnError('表格内不存在数据');
        }

        // 表头
        $first_user = $data[0];
        if (empty($first_user['姓名']) || empty($first_user['部门']) || !isset($first_user['基本工资']) ||
            !isset($first_user['综合工资']) || !isset($first_user['入职时间']) || !isset($first_user['账号'])) {
            returnError('表头错误');
        }

        $db = dbMysql::getInstance();
        $users = $db->table('qwuser')->field('id, wname, wdepartment_ids')->list();

        $department = $db->table('qwdepartment')->field('wp_id, name')->list();
        $department = array_column($department, 'name', 'wp_id');
        foreach ($users as &$user) {
            $wdepartment_ids = json_decode($user['wdepartment_ids'], true);
            $wdepartment = [];
            foreach ($wdepartment_ids as $id) {
                $wdepartment[] = $department[$id];
            }
            $user['department'] = implode(',', $wdepartment);
        }
        $users = array_column($users, null, 'wid');

        // 用户基本信息
        $user_info_data = $db->table('user_info')->list();
        $user_info = array_column($user_info_data, null, 'qwuser_id');

        $error_data = [];
        $res_data = [];
        foreach ($data as $item) {
            $error_msg = [];
            // if (!array_key_exists($item['姓名'], $users)) {
            //     $error_msg[] = '用户不存在';
            // }
            // if ($item['部门'] != $users[$item['姓名']]['department']) {
            //     $error_msg[] = $item['姓名'].'部门不匹配';
            // }
            if (empty($item['基本工资'])) {
                $error_msg[] = '基本工资不能为空';
            }
            if (empty($item['综合工资'])) {
                $error_msg[] = '综合工资不能为空';
            }
            try {
                $hire_date = $item['入职时间']->format('Y-m-d');
                if (empty($item['入职时间']) || strtotime($hire_date) === false) {
                    $error_msg[] = '入职时间格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '入职时间格式错误';
            }
            if (!empty($error_msg)) {
                $error_data[] = array_merge($item, ['失败原因' => implode('，', $error_msg)]);
                continue;
            }

            // 保存用户基本信息
            $user_id = $users[$item['账号']]['id'] ?? 0;
            $user_info_data = [
                'qwuser_id'   => $user_id,
                'wid'         => $item['账号'],
                'wname'       => $item['姓名'],
                'base_salary' => $item['基本工资'],
                'total_salary' => $item['综合工资'],
                'departs_name' => $item['部门'],
                'hire_date'   => $hire_date,
                'remark'      => $item['备注'] ?? '',
            ];
            if (array_key_exists($user_id, $user_info)) {
                $db->table('user_info')->where('qwuser_id = :user_id', ['user_id' => $user_id])->update($user_info_data);
            } else {
                $db->table('user_info')->insert($user_info_data);
            }
            $res_data[] = $item;
        }


        if (count($error_data) > 0) {
            // 使用项目根目录动态构建导出文件路径
            $src = '/public/checkin/temp/error/user_import_errors'.time().rand(100,1000).'.xlsx';
            $exportPath = SELF_FK . $src;

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径

            returnSuccess(['error_file' => $src, 'error_count' => count($error_data), 'success_count' => count($res_data), 'data' => $error_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => count($error_data), 'success_count' => count($res_data), 'data' => $res_data], '导入成功');
        }
    }
}