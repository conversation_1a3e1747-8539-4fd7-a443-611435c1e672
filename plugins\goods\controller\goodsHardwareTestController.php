<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/23 10:23
 */

namespace  plugins\goods\controller;

use plugins\goods\form\goodsHardwareApprovalFrom;
use plugins\goods\form\goodsHardwareTestFrom;
use plugins\goods\form\goodsMattersFrom;
use plugins\goods\form\goodsNewFrom;
use plugins\goods\form\goodsProjectAbnormalFrom;
use plugins\goods\form\goodsProjectFrom;
use plugins\goods\form\messagesFrom;
use plugins\goods\models\userModel;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;
use core\lib\log;

class goodsHardwareTestController
{
    //获取所有行
    public function getHardwareRow(){
        $db = dbMysql::getInstance();
        $list = $db->table('goods_hardware')
            ->where('where is_delete = 0')
            ->field("id,row_name,description,standard_basis,prototype_allocation,tools,0 as is_pass,'' as test_log,'' as test_res,'' as images")
            ->list();
        returnSuccess($list);
    }
    //获取产品硬件详情
    public function getHardwareFrom(){
        $paras_list = array('project_id','user_id','node_index');
        $request_list = ['project_id' => '项目ID','node_index'=>'节点角标'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $project_id = (int)$param['project_id'];
        $user_id = (int)$param['user_id']; //查看对应的人
        $node_index = $param['node_index'];
        $db = dbMysql::getInstance();
        $project = $db->table('goods_project')
            ->where('where id=:project_id',['project_id'=>$project_id])
            ->field('id,goods_id,flow_path_id')
            ->one();
        if (!$project) {
            SetReturn(-1,'未找到对应项目流程');
        }
        if ($user_id) {
            $test_data = $db->table('goods_hardware_test')
                ->where('where project_id=:project_id and qwuser_id=:qwuser_id and is_submit=1 and node_index=:node_index',['project_id'=>$project_id,'qwuser_id'=>$user_id,'node_index'=>$node_index])
                ->field('id,test_num,constituent_part,prototype,from_data,abnormal_data,goods_type,from_data,updated_at,conclusion,is_submit,is_filished,submit_time,qwuser_id')
                ->one();
            if (!$test_data) {
                SetReturn(-1,'未找找到此人提交的检测报告');
            }
        } else {
            $test_data = $db->table('goods_hardware_test')
                ->where('where project_id=:project_id and qwuser_id=:qwuser_id and node_index=:node_index',['project_id'=>$project_id,'qwuser_id'=>userModel::$qwuser_id,'node_index'=>$node_index])
                ->field('id,test_num,constituent_part,prototype,from_data,abnormal_data,goods_type,from_data,updated_at,conclusion,is_submit,is_filished,submit_time,qwuser_id')
                ->one();
        }
        if ($test_data) {
            $from_data = json_decode($test_data['from_data'],true);
            $list = $db->table('goods_hardware')
                ->where('where is_delete = 0')
                ->field('id,row_name,description,standard_basis,prototype_allocation,tools')
                ->list();
            $new_list = [];
            foreach ($from_data as $v1) {
                    foreach ($list as $v2) {
                       if ($v1['row_name'] == $v2['row_name']) {
                           $item = $v2;
                           $item['is_pass'] = $v1['is_pass']??0;
                           $item['test_log'] = $v1['test_log']??'';
                           $item['test_res'] = $v1['test_res']??"";
                           $item['images'] = $v1['images']??"";
                           $new_list[] = $item;
                       }
                    }
            }
            $test_data['from_data'] = $new_list;
            //硬件问题查询
            $abnormal_data = json_decode($test_data['abnormal_data'],true);
            $test_data['abnormal_data'] = $abnormal_data;
            $no_list = $db->table('goods_hardware_no')
                ->where('where project_id=:project_id and qwuser_id=:qwuser_id and is_delete = 0 and node_index=:node_index',['project_id'=>$project_id,'qwuser_id'=>$test_data['qwuser_id'],'node_index'=>$node_index])
                ->field('model_id as id,type,row_name,description,reason,suggestion')
                ->list();
            $test_data['no_list'] = $no_list;
        } else {
            //硬件问题查询
            $project_abnormal = $db->table('goods_abnormal','a')
                ->leftJoin('goods_project_abnormal','b','b.id = a.project_abnormal_id')
                ->where('where a.project_id=:project_id and a.is_handled <> 1 and a.project_abnormal_id>0 and a.abnormal_type=2',['project_id'=>$project_id])
                ->field('b.id,b.txt_id,b.title')
                ->list();
            $abnormal_list = [];
            if (count($project_abnormal)) {
                foreach ($project_abnormal as $abl) {
                    $item = [
                        'id'=>$abl['id'],
                        'abnormal_text'=>$abl['title'],
                        'txt'=>goodsProjectAbnormalFrom::getAbnormallTxt(json_decode($abl['txt_id']),2),
                    ];
                    $abnormal_list[] = $item;
                }
            }
            $new_list = $db->table('goods_hardware')
                ->where('where is_delete = 0 and user_id=0')
                ->field("id,row_name,description,standard_basis,prototype_allocation,tools,0 as is_pass,'' as test_log,'' as test_res,'' as images")
                ->list();
            //硬件问题查询
            $test_data = [
                'id'=>0,
                'test_num'=>0,
                'constituent_part'=>'本体，USB充电线，说明书',
                'from_data'=>$new_list,
                'abnormal_data'=>$abnormal_list,
                'goods_type'=>1,
                'prototype'=>1,
                'no_list'=>[],
                'updated_at'=>'',
                'submit_time'=>'',
                'conclusion'=>'',
                'is_submit'=>'0',
                'is_filished'=>'0'
            ];

        }
        //获取商品基础信息
        $goods_receiving =  $db->table('goods_receiving')
            ->where('where project_id=:project_id and is_delete = 0',['project_id'=>$project_id])
            ->one();
        $test_data['flow_path_id'] = $project['flow_path_id'];
        $goods_info = goodsNewFrom::getGoodsBaseInfo($project['goods_id']);
        if ($goods_receiving) {
            $goods_info['code'] = $goods_receiving['sample_no'];
            $goods_info['receiving_time'] = date('Y-m-d H:i:s',$goods_receiving['created_time']);
            $color_info = [];
            $color_ids = !empty($goods_receiving['color_id'])?json_decode($goods_receiving['color_id']):[];
            foreach ($color_ids as $color_id) {
                foreach ($goods_info['color_list'] as $color) {
                    if ($color['id'] == $color_id) {
                        $color_info[] = $color;
                        break;
                    }
                }
            }
            $goods_info['color_list'] = $color_info;
        } else {
            $goods_info['receiving_time'] = '';
            $goods_info['color_list'] = [];
            $goods_info['code'] = '-';
        }
        $test_data['goods_info']=$goods_info;
        //如果生产了pdf获取pdf地址
        $db = dbMysql::getInstance();
        if ($user_id) {
            $file = $db->table('goods_project_file')
                ->where('where user_id=:user_id and project_id=:project_id and event_type=4',['user_id'=>$user_id,'project_id'=>$project_id])
                ->one();
        } else {
            $file = $db->table('goods_project_file')
                ->where('where user_id=:user_id and project_id=:project_id and event_type=4',['user_id'=>userModel::$qwuser_id,'project_id'=>$project_id])
                ->one();
        }

        $test_data['pdf']=$file?['file_name'=>$file['filename'],'path'=>$file['src']]:'';
        returnSuccess($test_data);
    }
    //规格书参数确认(硬件功能测试)保存参数
    public function saveHardware() {
        $paras_list = array('project_id','node_index','event_index','test_num','constituent_part','from_data','conclusion','abnormal_data','no_list','prototype','goods_type');
        $request_list = ['project_id' => '项目ID', 'node_index' => '节点角标', 'event_index' => '事件角标','test_num' => '测试数量','constituent_part' => '组成部分','prototype' => '样机描述','goods_type' => '产品类别'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        //项目的数据验证
        $db = dbMysql::getInstance();
        $db->beginTransaction();
        try {
            //保存测试数据数据 + 日志记录 +未通过记录保存
            goodsHardwareTestFrom::saveHardwareTest($param);
            $db->commit();
            returnSuccess('','保存成功');
        } catch (ExceptionError $error) {
            $db->commit();
            throw new ExceptionError($error->getMessage());

        }
    }
    //添加测试列
    public function addTestRow() {
        $paras_list = array('row_name','description','standard_basis','prototype_allocation','tools');
        $request_list = ['row_name' => '项目名', 'description' => '标准概述', 'standard_basis' => '标准依据','prototype_allocation' => '样机分配','tools' => '检查方法、仪器/工具'];
        $param = arrangeParam($_POST, $paras_list,$request_list);

        $db = dbMysql::getInstance();
        $other_row = $db->table('goods_hardware')
            ->where('where is_delete = 0 and row_name=:row_name',['row_name'=>$param['row_name']])
            ->count();
        if ($other_row) {
            SetReturn(-1,'该项目名已存在');
        }
        $id = $db->table('goods_hardware')
            ->insert([
                'user_id'=>userModel::$qwuser_id,
                'row_name'=>$param['row_name'],
                'description'=>$param['description'],
                'standard_basis'=>$param['standard_basis'],
                'tools'=>$param['tools'],
                'prototype_allocation'=>$param['prototype_allocation'],
                'created_time'=>date('Y-m-d H:i:s'),
            ]);
        if ($id) {
           returnSuccess(['id'=>$id],'添加成功');
        } else {
            SetReturn(-1,'添加失败');
        }

    }
    //异常审核-节点负责人（产品硬件检测异常审核）
    public function checkHardwareEvent(){
        $paras_list = array('project_id','is_pass','reason_ids','send_copy_user','remarks','node_index');
        $request_list = ['reason_ids' => '不合格原因','node_index'=>'节点角标'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        //事件基础验证
        $request_data = goodsProjectFrom::verifyNodeRequestData($param);
        $project = $request_data['project'];
        $project_id = (int)$param['project_id'];
        $is_pass = (int)$param['is_pass'];
        if ($is_pass) {
            SetReturn(-1,'审核通过走审核流程');
        }
        //获取商品信息
        $db = dbMysql::getInstance();
        $goods_info = $db->table('goods_new')
            ->where('where id=:goods_id',['goods_id'=>$project['goods_id']])
            ->field('manage_info')
            ->one();
        if (!$project) {
            SetReturn(-1,'未查询到该项目流程');
        }
        //查询默认推送人
        $goods_manage = json_decode($goods_info['manage_info'],true);
        $wids[] = $goods_manage[0]['wid'];
        $hardware_test = $db->table('goods_hardware_test','a')
            ->leftJoin('qwuser','b','b.id=a.qwuser_id')
            ->where('where a.project_id=:project_id and node_index=:node_index',['project_id'=>$project_id,'node_index'=>$param['node_index']])
            ->field('b.wid')
            ->list();
        $wids2 = array_column($hardware_test,'wid');
        $wids = array_merge($wids,$wids2);
        $db->beginTransaction();
        try {
            goodsHardwareApprovalFrom::hardeareTestNotPass($project,$param,$wids,$param['node_index']);
            goodsMattersFrom::setProjectMatterStatus1(0,$project_id,$param['node_index']);
            $db->commit();
            returnSuccess('','操作成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //审批（产品硬件检测审批）
    public function checkApproval(){
        $paras_list = array('is_pass','remarks','project_id','approval_id','node_index');
        $request_list = ['project_id' => '项目ID','node_index'=>'节点角标','approval_id'=>'审批ID'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $is_pass = (int)$param['is_pass'];
        $project_id = (int)$param['project_id'];
        $approval_id = (int)$param['approval_id'];
        $db = dbMysql::getInstance();
        $hardware_approval = $db->table('goods_hardware_approval')
            ->where('where id=:id',['id'=>$approval_id])
            ->one();
        if (!$project_id) {
            SetReturn(-1,'未找硬件检测审批数据');
        }
        if (userModel::$qwuser_id != $hardware_approval['qwuser_id']) {
            SetReturn(-1,'硬件检测审批未交与您');
        }
        if ($hardware_approval['is_pass'] > 0) {
            SetReturn(-1,'已审批，切勿重复操作');
        }
        if (!$is_pass) {
            $project = $db->table('goods_project')
                ->where('where id=:project_id',['project_id'=>$project_id])
                ->field('id,goods_id,matter_name')
                ->one();
            if (!$project) {
                SetReturn(-1,'未找到对应项目流程');
            }
            $goods_info = $db->table('goods_new')
                ->where('where id =:goods_id',['goods_id'=>$project['goods_id']])
                ->field('manage_info')
                ->one();
            $db->beginTransaction();
            try {
                //修改状态
                $db->table('goods_hardware_approval')
                    ->where('where id=:id',['id'=>$approval_id])
                    ->update(['is_pass'=>2,'approval'=>$param['remarks']]);
                //改变待办事件状态
                $db->table('goods_matters')
                    ->where('where goods_project_id=:project_id and qwuser_id=:qwuser_id and create_type=2 and status=0 and event_type = 4 and node_index=:node_index',['project_id'=>$project_id,'qwuser_id'=>userModel::$qwuser_id,'node_index'=>$param['node_index']])
                    ->update([
                        'status'=>1,
                        'expected_time'=>time()
                    ]);
                //废除该流程
                $db->table('goods_project')
                    ->where('where id=:project_id',['project_id'=>$project_id])
                    ->update([
                        'status'=>4,
                        'updated_time'=>date('Y-m-d H:i:s')
                    ]);
                //通知相关人员
                $msg = messagesFrom::getMsgTxt(11,$project['matter_name'],'产品硬件测试','');
                $wids = goodsHardwareApprovalFrom::getHardwareApproval($is_pass,$project_id);
                $wids[] = json_decode($goods_info['manage_info'],true)[0]['wid'];
                $other_data = [
                    'user_id'=>userModel::$qwuser_id,
                    'model_id'=>$project_id,
                    'msg_type'=>8
                ];
                messagesFrom::senMeg($wids,$msg,$other_data,$param['remarks']);
                $db->commit();
                returnSuccess('','审批成功');
            } catch (ExceptionError $error) {
                $db->rollBack();
                throw new ExceptionError($error->getMessage());
            }
        }
        else
        {
            //节点基础验证
            $request_data = goodsProjectFrom::verifyNodeRequestData($param,0);
            $tpl_data = $request_data['tpl_data'];
            $project = $request_data['project'];
            $current_node = $request_data['current_node'];
            //产品信息
            $goods_info = $db->query('select id,goods_name,manage_info from oa_goods_new where id=:goods_id',['goods_id'=>$project['goods_id']]);
            $goods_info['matter_name'] = $project['matter_name'];
            $db->beginTransaction();
            try {
                //修改状态
                $db->table('goods_hardware_approval')
                    ->where('where id=:id',['id'=>$approval_id])
                    ->update(['is_pass'=>1]);
                //更新当前节点为已完成
                goodsProjectFrom::updateTplDataForNodeStatus1($tpl_data,$project_id,$project['current_index'],$goods_info,$param['node_index'],0);

                //操作记录
                $matter_name = $project['matter_name'];
                $goods_info['matter_name'] = $matter_name;
                $msg = "审批了【{$current_node['node_name']}】不合格项，并通过审核。";
                log::setGoodsProjectLog($project['goods_id'],$project_id,$msg, $matter_name);
                //修改待办事件
                $db->table('goods_matters')
                    ->where('where goods_project_id=:project_id and qwuser_id=:qwuser_id and create_type=2 and status=0 and event_type = 4 and node_index=:node_index',['project_id'=>$project_id,'qwuser_id'=>userModel::$qwuser_id,'node_index'=>$param['node_index']])
                    ->update([
                        'status'=>1,
                        'expected_time'=>time()
                    ]);
                //通知
                $msg = messagesFrom::getMsgTxt(12,$project['matter_name'],'产品硬件测试','');
                $wids = goodsHardwareApprovalFrom::getHardwareApproval($is_pass,$project_id);
                $wids[] = json_decode($goods_info['manage_info'],true)[0]['wid'];
                $other_data = [
                    'user_id'=>userModel::$qwuser_id,
                    'model_id'=>$project_id,
                    'msg_type'=>8
                ];
                messagesFrom::senMeg($wids,$msg,$other_data,$param['remarks']);
                //本地记录数据
                log::goodspProjectLog()->info('['.userModel::$wid.']将'.$project['id'].'修改为：'.json_encode($tpl_data,JSON_UNESCAPED_UNICODE));
                $db->commit();
                SetReturn(0,'审核成功');
            } catch (ExceptionError $error) {
                $db->rollBack();
                throw new ExceptionError($error->getMessage());
            }
        }
    }
    //下载检测报告
    public function downloadHardwarePdf() {
        $paras_list = array('project_id','pfd_html','user_id','node_index');
        $request_list = ['project_id' => '项目ID','user_id'=>'用户ID','node_index'=>'节点角标'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $project_id = (int)$param['project_id'];
        $user_id = (int)$param['user_id'];
        $pfd_html = $param['pfd_html'];
        //如果生产了pdf获取pdf地址
        $db = dbMysql::getInstance();
        $file = $db->table('goods_project_file')
            ->where('where user_id=:user_id and project_id=:project_id and event_type=4 and nodex_index=:nodex_index',['user_id'=>$user_id,'project_id'=>$project_id,"nodex_index"=>$param['nodex_index']])
            ->one();
        if ($file) {
            returnSuccess(['pdf_url'=>$file['src']]);
        } else {
            $project = $db->table('goods_project')
                ->where('where id=:id',['id'=>$project_id])
                ->one();
            if (!$project) {
                SetReturn(-1,'未查询到该流程');
            }
            $path = goodsHardwareTestFrom::setPdf($pfd_html,$project_id,$project['goods_id'],$project['flow_path_id'],$param['nodex_index']);
            returnSuccess($path);
        }
    }

    //补充检测报告查询(先在流程中提交后，再补充提交)
    public function submitAfterProject() {
        $paras_list = array('project_id','pfd_html','node_index');
        $request_list = ['project_id' => '项目ID','node_index'=>'节点角标','pfd_html'=>'pdf_html'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $project_id = $param['project_id'];
        $pfd_html = $param['pfd_html'];
        $db = dbMysql::getInstance();
        $project = $db->table('goods_project')
            ->where('where id =:project_id',['project_id'=>$project_id])
            ->one();
        if (!$project) {
            SetReturn(-1,'未找到流程信息');
        }
        $hard_test = $db->table('goods_hardware_test')
            ->where('where qwuser_id=:qwuser_id and project_id=:project_id and node_index=:node_index',['qwuser_id'=>userModel::$qwuser_id,'project_id'=>$project_id,'node_index'=>$param['node_index']])
            ->one();
        if ($hard_test['is_filished'] == 1) {
            SetReturn(-1,'已完成，切勿重复操作');
        }
        if (!$hard_test) {
            SetReturn(-1,'请先完成产品硬件检测');
        } else {
            $db->beginTransaction();
            try {
                $from_data = json_decode($hard_test['from_data'],true);
                foreach ($from_data as $v) {
                    if ($v['is_pass'] == 0) {
                        $all_test = 0;
                        SetReturn(-1,"【{$v['row_name']}】还没测试结果");
                    }
                }
                if ($hard_test['abnormal_data']) {
                    $abnormal_data = json_decode($hard_test['abnormal_data'],true);
                    foreach ($abnormal_data as $v) {
                        if ($v['is_pass'] == 0) {
                            $all_test = 0;
                            SetReturn(-1,"【{$v['txt']}】问题还没测试结果");
                        }
                    }
                }
                //pdf保存
                goodsHardwareTestFrom::setPdf($pfd_html,$project_id,$hard_test['goods_id'],$project['flow_path_id'],$param['node_index']);
                //修改提交状态
                $db->table('goods_hardware_test')
                    ->where('where id=:id',['id'=>$hard_test['id']])
                    ->update(['submit_time'=>date('Y-m-d H:i:s'),'is_submit'=>1,'is_filished'=>1]);
                $db->commit();
                //修改待办事项为完成
                $db->table('goods_matters')
                    ->where('where goods_project_id=:project_id and status = 1 and is_advance_submit=1 and qwuser_id=:qwuser_id and node_index=:node_index',['qwuser_id'=>userModel::$qwuser_id,'project_id'=>$project_id,'node_index'=>$param['node_index']])
                    ->update(['is_advance_submit'=>2]);
                returnSuccess('','提交成功');
            } catch (ExceptionError $error) {
                $db->rollBack();
                throw new ExceptionError($error->getMessage());
            }

        }

    }
}