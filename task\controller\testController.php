<?php
/**
 * @author: zhangguoming
 * @Time: 2025/4/8 13:52
 */

namespace task\controller;

use core\lib\db\dbMysql;
use core\lib\predisV;

class testController
{
    public function ddJson() {
        $principal = '[[{"principal_uid":208,"principal_name":"\u7cbe\u54c1"},{"principal_uid":10445635,"principal_name":"\u82f1\u56fd\u56db\u7ec4"},{"principal_uid":10353132,"principal_name":"\u90ed\u5b87"},{"principal_uid":48,"principal_name":"\u4f55\u5174"}],[{"principal_uid":208,"principal_name":"\u7cbe\u54c1"},{"principal_uid":10132161,"principal_name":"\u5fb7\u56fd\u4e03\u7ec4"},{"principal_uid":10401041,"principal_name":"\u5218\u9759"},{"principal_uid":48,"principal_name":"\u4f55\u5174"},{"principal_uid":49,"principal_name":"Terisa"}],[{"principal_uid":208,"principal_name":"\u7cbe\u54c1"},{"principal_uid":10445635,"principal_name":"\u82f1\u56fd\u56db\u7ec4"},{"principal_uid":10353132,"principal_name":"\u90ed\u5b87"},{"principal_uid":48,"principal_name":"\u4f55\u5174"}],[{"principal_uid":208,"principal_name":"\u7cbe\u54c1"},{"principal_uid":10120966,"principal_name":"\u7f8e\u56fd\u4e5d\u7ec4"},{"principal_uid":98,"principal_name":"summer"},{"principal_uid":10325476,"principal_name":"\u80e1\u8776"},{"principal_uid":80,"principal_name":"\u9093\u660e\u6b23"},{"principal_uid":49,"principal_name":"Terisa"}],[{"principal_uid":208,"principal_name":"\u7cbe\u54c1"},{"principal_uid":10120966,"principal_name":"\u7f8e\u56fd\u4e5d\u7ec4"},{"principal_uid":98,"principal_name":"summer"},{"principal_uid":10325476,"principal_name":"\u80e1\u8776"},{"principal_uid":80,"principal_name":"\u9093\u660e\u6b23"},{"principal_uid":49,"principal_name":"Terisa"}],[{"principal_uid":208,"principal_name":"\u7cbe\u54c1"},{"principal_uid":10120966,"principal_name":"\u7f8e\u56fd\u4e5d\u7ec4"},{"principal_uid":98,"principal_name":"summer"},{"principal_uid":10325476,"principal_name":"\u80e1\u8776"},{"principal_uid":80,"principal_name":"\u9093\u660e\u6b23"},{"principal_uid":49,"principal_name":"Terisa"}],[{"principal_uid":208,"principal_name":"\u7cbe\u54c1"},{"principal_uid":10120966,"principal_name":"\u7f8e\u56fd\u4e5d\u7ec4"},{"principal_uid":98,"principal_name":"summer"},{"principal_uid":10325476,"principal_name":"\u80e1\u8776"},{"principal_uid":80,"principal_name":"\u9093\u660e\u6b23"},{"principal_uid":49,"principal_name":"Terisa"}],[{"principal_uid":208,"principal_name":"\u7cbe\u54c1"},{"principal_uid":10120966,"principal_name":"\u7f8e\u56fd\u4e5d\u7ec4"},{"principal_uid":98,"principal_name":"summer"},{"principal_uid":10325476,"principal_name":"\u80e1\u8776"},{"principal_uid":80,"principal_name":"\u9093\u660e\u6b23"},{"principal_uid":49,"principal_name":"Terisa"}],[{"principal_uid":208,"principal_name":"\u7cbe\u54c1"},{"principal_uid":10445635,"principal_name":"\u82f1\u56fd\u56db\u7ec4"},{"principal_uid":10353132,"principal_name":"\u90ed\u5b87"},{"principal_uid":48,"principal_name":"\u4f55\u5174"}],[{"principal_uid":208,"principal_name":"\u7cbe\u54c1"},{"principal_uid":10445635,"principal_name":"\u82f1\u56fd\u56db\u7ec4"},{"principal_uid":10353132,"principal_name":"\u90ed\u5b87"},{"principal_uid":48,"principal_name":"\u4f55\u5174"}],[{"principal_uid":208,"principal_name":"\u7cbe\u54c1"},{"principal_uid":10120966,"principal_name":"\u7f8e\u56fd\u4e5d\u7ec4"},{"principal_uid":98,"principal_name":"summer"},{"principal_uid":10325476,"principal_name":"\u80e1\u8776"},{"principal_uid":80,"principal_name":"\u9093\u660e\u6b23"},{"principal_uid":49,"principal_name":"Terisa"}],[{"principal_uid":208,"principal_name":"\u7cbe\u54c1"},{"principal_uid":10120966,"principal_name":"\u7f8e\u56fd\u4e5d\u7ec4"},{"principal_uid":98,"principal_name":"summer"},{"principal_uid":10325476,"principal_name":"\u80e1\u8776"},{"principal_uid":80,"principal_name":"\u9093\u660e\u6b23"},{"principal_uid":49,"principal_name":"Terisa"}],[{"principal_uid":208,"principal_name":"\u7cbe\u54c1"},{"principal_uid":10120966,"principal_name":"\u7f8e\u56fd\u4e5d\u7ec4"},{"principal_uid":10497042,"principal_name":"\u989c\u96c5\u840d"},{"principal_uid":10325476,"principal_name":"\u80e1\u8776"},{"principal_uid":80,"principal_name":"\u9093\u660e\u6b23"},{"principal_uid":98,"principal_name":"summer"}],[{"principal_uid":208,"principal_name":"\u7cbe\u54c1"},{"principal_uid":10445635,"principal_name":"\u82f1\u56fd\u56db\u7ec4"},{"principal_uid":10353132,"principal_name":"\u90ed\u5b87"},{"principal_uid":48,"principal_name":"\u4f55\u5174"}],[{"principal_uid":208,"principal_name":"\u7cbe\u54c1"},{"principal_uid":10445635,"principal_name":"\u82f1\u56fd\u56db\u7ec4"},{"principal_uid":10353132,"principal_name":"\u90ed\u5b87"},{"principal_uid":48,"principal_name":"\u4f55\u5174"}]]';
        $mm_ = json_decode($principal,true);
        $names = [];
        foreach ($mm_ as $v) {
            $mm_1 = array_column($v,'principal_name');
            $mm = array_slice($mm_1,2,3);
            $names = array_merge($names,$mm);
        }
        $names = array_unique($names);
        dd($names);
    }
    //需求相关人员历史记录处理
    public function setRequest() {
        $db = dbMysql::getInstance();
        $request_list = $db->table('imgs_request','a')
            ->leftJoin('imgs_request_participant','b','b.request_id = a.id')
            ->where('b.request_id is null and a.is_delete = 0')
            ->field('a.id')
            ->groupBy(['a.id'])
            ->order('a.id asc')
            ->list(1000);
        if (count($request_list)) {
            $ids = array_column($request_list,'id');
            //需求
            $requests = $db->table('imgs_request')
                ->whereIn('id',$ids)
                ->field('id,goods_id,user_id,allocation_user_id,qwuser_id')
                ->list();
            //产品开发运营
            $goods_ids = array_column($requests,'goods_id');
            $goods_list = $db->table('goods_new')
                ->whereIn('id',$goods_ids)
                ->field('id,operator_info,manage_info')
                ->list();
            $goods_ = [];
            foreach ($goods_list as $v) {
                $operator_info = json_decode($v['operator_info'],true);
                $manage_info = json_decode($v['manage_info'],true);
                $goods_[$v['id']] = [
                    'operator_ids'=>array_column($operator_info,'id'),
                    'manage_ids'=>array_column($manage_info,'id')
                ];
            }
            //需求审核人
            $request_checks = $db->table('imgs_request_check_log')
                ->where('is_check = 1')
                ->whereIn('request_id',$ids)
                ->field('request_id,check_user_id')
                ->list();
            $check_ = array_column($request_checks,'check_user_id','request_id');
            $save_list = [];
            $ksys = ['request_id','user_id','type'];
            foreach ($requests as $v) {
                //创建者
                $save_list[] = [$v['id'],$v['user_id'],1];
                //分配美工人
                if ($v['allocation_user_id']) {
                    $save_list[] = [$v['id'],$v['allocation_user_id'],3];
                }
                //美工
                if ($v['qwuser_id']) {
                    $save_list[] = [$v['id'],$v['qwuser_id'],4];
                }
                //图片审核人
                if (isset($check_[$v['id']])) {
                    $save_list[] = [$v['id'],$check_[$v['id']],10];
                }
                //开发+运营
                if (isset($goods_[$v['goods_id']])) {
                    $goods = $goods_[$v['goods_id']];
                    if (count($goods['manage_ids'])) {
                        //开发
                        $save_list[] = [$v['id'],$goods['manage_ids'][0],5];
                    }
                    if (count($goods['operator_ids'])) {
                        //运营
                        foreach ($goods['operator_ids'] as $yunying_id) {
                            $save_list[] = [$v['id'],$yunying_id,6];
                        }

                    }
                }
            }
            $chunkedArray = array_chunk($save_list, 500);
            foreach ($chunkedArray as $v) {
                $db->table('imgs_request_participant')
                    ->insertBatch($ksys,$v);
            }
            returnSuccess('','本次完成，继续');
        } else {
            returnSuccess('','没了');
        }

        dd($request_list);
    }
    //消息测试
    public function sendQwMessage() {
        $data = [
            'system_type' => 1,
            'type' => 'textcard',
            'msg' => '消息测试内容',
            'title' => '消息测试',
            'qw_userid' => 'ZhangGuoMing',
            'data' => '{"id":50}'
        ];
        $redis = (new \core\lib\predisV())::$client;
        predisV::redisQueue($data);
    }
}