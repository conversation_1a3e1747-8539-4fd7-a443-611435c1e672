<?php
/**
 * @author: zhangguoming
 * @Time: 2024/8/23 9:14
 */

namespace financial\form;

use core\lib\db\dbFMysql;

class marketForm
{
    public static function getCountryList() {
        $db = dbFMysql::getInstance();
        $list = $db->table('market')
            ->field('country,code')
            ->list();
        $country_list = [];
        foreach ($list as $v) {
            $country_list[$v['code']] = $v;
        }
        return $country_list;
    }
}