<?php
/**
 * Created by 漠山.
 * @purpose 描述
 * @Author: 漠山
 * @Time: 2023/12/5 21:43
 */
namespace core\lib;
class config
{
    static public $conf = [];

    //配置参数获取
    static public function get($name, $file)
    {
        /*
         * 判断文件是否存在
         * 判断配置是否存在
         * 缓存配置
         * */
        if (isset(self::$conf[$file])) {
            return self::$conf[$file][$name];
        } else {
            if (SYS_ENV == 'produce') {
                //生产环境
                $file_path = SELF_FK.'/core/config/'.$file.'.php';
            } else {
                $file_path = SELF_FK.'/core/config_develop/'.$file.'.php';
            }
            //查询base_data配置
            if (!is_file($file_path)) {
                $file_path = SELF_FK.'/core/base_data/'.$file.'.php';
            }
            if (is_file($file_path)) {
                $conf = include $file_path;
                if (isset($conf[$name])) {
                    self::$conf[$file] = $conf;
                    return $conf[$name];
                } else {
                    throw new \Exception('没用有这个参数'.$name);
                }
            } else {
                throw new \Exception('找不到配置文件'.$file);
            }
        }

    }
    static public function all($file)
    {
        /*
         * 判断文件是否存在
         * 判断配置是否存在
         * 缓存配置
         * */
        if (isset(self::$conf[$file])) {
            return self::$conf[$file];
        } else {
            if (SYS_ENV == 'produce') {
                //生产环境
                $file_path = SELF_FK.'/core/config/'.$file.'.php';
            } else {
                $file_path = SELF_FK.'/core/config_develop/'.$file.'.php';
            }
            //查询base_data配置
            if (!is_file($file_path)) {
                $file_path = SELF_FK.'/core/base_data/'.$file.'.php';
            }
            if (is_file($file_path)) {
                $conf = include $file_path;
                self::$conf[$file] = $conf;
                return $conf;
            } else {
                throw new \Exception('找不到配置文件'.$file);
            }
        }
    }
    //基础参数获取
    public static function getDataName($key,$id){
        $list = [];
        if (isset(self::$conf['data'])) {
            $list = self::$conf['data'][$key];
        } else {
            $conf = include SELF_FK.'/core/base_data/data.php';
            self::$conf['data'] = $conf;
            $list = $conf[$key];
        }
        foreach ($list as $value) {
            if ($value['id'] == $id) {
                return $value['name'];
            }
        }
        return '';
    }
    //财务系统参数获取
    public static function getDataName1($file,$key,$id){
        $list = [];
        if (isset(self::$conf[$file])) {
            $list = self::$conf[$file][$key];
        } else {
            $conf = include SELF_FK.'/core/base_data/'.$file.'.php';
            self::$conf[$file] = $conf;
            $list = $conf[$key];
        }

        foreach ($list as $value) {
            if ($value['id'] == $id) {
                return $value['name'];
            }
        }
        return '';
    }
}