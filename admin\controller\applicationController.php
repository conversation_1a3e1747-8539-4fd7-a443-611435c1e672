<?php
/**
 * @author: zhangguoming
 * @Time: 2024/3/28 16:46
 */

namespace  admin\controller;

use admin\form\applicationForm;
use admin\models\userModel;
use core\lib\config;
use core\lib\db\dbMysql;

class applicationController
{
    public function getList() {
        $app_name = $_GET['app_name'];
        $list = applicationForm::getList($app_name);
        returnSuccess($list);
    }

    public function setQuickApp() {
        $keys = $_POST['keys']??'';
        if (!$keys) {
            returnError('参数错误');
        }
        applicationForm::setUserApp($keys);
        returnSuccess([],'保存成功');
    }

    public function getQuickApp() {
        $list = applicationForm::getUserApp();
        returnSuccess($list);

    }
}