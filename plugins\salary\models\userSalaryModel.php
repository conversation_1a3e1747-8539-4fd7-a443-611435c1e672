<?php

namespace plugins\salary\models;

use core\lib\db\dbMysql;
use core\lib\db\dbSMysql;

class userSalaryModel
{

    public static function getUserSalary($user_id, $date = null)
    {
        $date = $date ?? date('Y-m-d');
        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();
        $salary = $sdb->table('user_salary')
            ->where('where is_delete = 0 and qwuser_id = :qwuser_id', ['qwuser_id' => $user_id])
            ->field('salary, effective_date, type')
            ->order('effective_date DESC')
            ->list();

        foreach ($salary as &$item) {
            $item['salary'] = json_decode($item['salary'], true);
        }
        unset($item);

        foreach ($salary as $item) {
            if ($item['effective_date'] <= $date) {
                return $item['salary'];
            }
        }
        return null;
    }

}