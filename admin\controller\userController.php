<?php

namespace admin\controller;

use admin\form\userForm;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;
use admin\models\userModel;

class userController
{
    //拉取用户列表
    public function getUserList()
    {
        $paras_list = array('wname', 'qw_partment_id', 'wstatus', 'page', 'page_size','status','roles_id','order_by');
        $param = arrangeParam($_POST, $paras_list);
        $list = userForm::getUserList($param);
        returnSuccess($list);
    }
    //激活用户
    public function activeUser()
    {
        $paras_list = array('qw_userid','password','role_ids');
        $param = arrangeParam($_POST, $paras_list);
        userForm::activeUser($param);
        returnSuccess('', '激活成功');
    }
    //启用禁用用户
    public function changeUserStatus()
    {
        $paras_list = array('qw_userid');
        $param = arrangeParam($_POST, $paras_list);
        userForm::changeUserStatus($param);
        returnSuccess('', '操作成功');
    }
    //修改密码
    public function changePwd()
    {
        $paras_list = array('qw_userid','password');
        $param = arrangeParam($_POST, $paras_list);
        userForm::changePwd($param);
        returnSuccess('', '操作成功');
    }
    //个人修改密码
    public function updateUserPwd()
    {
        $paras_list = array('qw_userid','password');
        $param = arrangeParam($_POST, $paras_list);
        userForm::updateUserPwd($param);
        returnSuccess('', '修改成功');
    }
    //管理mac地址
    public function manageMac()
    {
        $paras_list = array('qw_id','mac_ids');
        $param = arrangeParam($_POST, $paras_list);
        userForm::manageMac($param);
        returnSuccess('', '操作成功');
    }

    //详情
    public function getUserdetails()
    {
        $paras_list = array('qw_userid');
        $param = arrangeParam($_POST, $paras_list);
        $data = userForm::getUserdetails($param);
        returnSuccess($data);
    }


    //通过token获取用户信息
    public function getUserInfo(){
        $data = [
            'id'=>userModel::$qwuser_id,
            'wid'=>userModel::$wid,
            'wname'=>userModel::$wname,
            'avatar'=>userModel::$avatar,
            'is_super'=>userModel::$is_super,
            'auth'=>userModel::$auth,
        ];
        returnSuccess(['data'=>$data]);
    }
    /**
     * @return void   常用用户查询
     * @throws ExceptionError
     */
    public function getOftenUsedUser()
    {
        $db = dbMysql::getInstance();
        $db->table('user_often_used', 'a');
        $db->leftJoin('qwuser', 'b', 'b.id = a.used_qwuser_id');
        $db->where('b.id=:id', ['id' => userModel::$qwuser_id]);
        $db->order('a.updated_at desc');
        $db->field('b.id,b.wid,b.wname,b.avatar,b.position');
        $list = $db->list();
        returnSuccess(['data' => $list]);
    }
    //常用用户列表
    public function setOftenUsedUser() {
        $paras_list = array('qwuser_ids');
        $param = arrangeParam($_POST, $paras_list);
        if (!empty($param['qwuser_ids'])) {
            $qwuser_ids = $need_add_ids = json_decode($param['qwuser_ids']);
            $db = dbMysql::getInstance();
            //查询这些用户是否在数据中
            $db->table('user_often_used');
            $db->where('where qwuser_id=:qwuser_id',['qwuser_id'=>userModel::$qwuser_id]);
            $db->field('used_qwuser_id');
            $db->whereIn('used_qwuser_id',$qwuser_ids);
            $old_user = $db->list();
            //修改
            $db->update(['updated_at'=>time()]);

            if (count($old_user)) {
                $old_used_qwuser_ids = array_column($old_user,'used_qwuser_id');
                $need_add_ids = array_diff($qwuser_ids,$old_used_qwuser_ids);
            }
            if (count($need_add_ids)) {
                $insert_sql = 'insert into oa_user_often_used (qwuser_id,used_qwuser_id,updated_at) values ';
                $insert_data = ['qwuser_id'=>userModel::$qwuser_id,'updated_at'=>time()];
                foreach ($need_add_ids as $k=>$v) {
                    $insert_sql .= "(:qwuser_id,:used_qwuser_id_$k,:updated_at),";
                    $insert_data["used_qwuser_id_$k"] = $v;
                }
                $insert_sql = trim($insert_sql,',');
                $db->query($insert_sql,$insert_data);
            }
            returnSuccess('','');
        }
    }
    //获取免登录code
    public function getNoLoginCode() {
        $ip_ = $_GET['u_ip']??'-';
        $wid = userModel::$wid;
        $user_id = userModel::$qwuser_id;
        $code = uniqid();
        $db  = dbMysql::getInstance();
        $code_data = $db->table('login_code')
            ->where('user_id = :user_id',['user_id'=>$user_id])
            ->one();
        if ($code_data) {
            $db->table('login_code')
                ->where('code = :code_',['code_'=>$code_data['code']])
                ->update([
                    'code'=>$code,
                    'wid'=>$wid,
                    'user_ip'=>$ip_,
                    'created_time'=>time(),
                    'status'=>1,
                    'token1'=>USER_TOKEN
                ]);
        } else {
            $db->table('login_code')
                ->insert([
                    'code'=>$code,
                    'user_id'=>$user_id,
                    'wid'=>$wid,
                    'user_ip'=>$ip_,
                    'created_time'=>time(),
                    'status'=>1,
                    'token1'=>USER_TOKEN
                ]);
        }
        returnSuccess(['code'=>$code]);
    }
}