-- 节日活动表
CREATE TABLE IF NOT EXISTS `oa_l_festival_activities` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '活动名称',
  `festival_type` varchar(50) NOT NULL COMMENT '节日类型（如：春节、圣诞节、黑五等）',
  `start_date` date NOT NULL COMMENT '活动开始日期',
  `end_date` date NOT NULL COMMENT '活动结束日期',
  `description` text COMMENT '活动描述',
  `sites_config` json COMMENT '站点配置JSON（包含各站点的配置信息）',
  `stock_rules` json COMMENT '备货规则JSON（包含备货策略和规则）',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_by` int(11) NOT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_festival_type` (`festival_type`),
  KEY `idx_start_date` (`start_date`),
  KEY `idx_end_date` (`end_date`),
  KEY `idx_status` (`status`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='节日活动表';

-- 插入示例数据
INSERT INTO `oa_l_festival_activities` (`name`, `festival_type`, `start_date`, `end_date`, `description`, `sites_config`, `stock_rules`, `status`, `created_by`) VALUES
('2025春节活动', '春节', '2025-02-01', '2025-02-15', '2025年春节促销活动', 
 JSON_OBJECT(
   'US', JSON_OBJECT('discount_rate', 0.15, 'free_shipping_threshold', 50, 'promotion_banner', '春节特惠'),
   'UK', JSON_OBJECT('discount_rate', 0.12, 'free_shipping_threshold', 40, 'promotion_banner', 'Chinese New Year Sale'),
   'DE', JSON_OBJECT('discount_rate', 0.10, 'free_shipping_threshold', 35, 'promotion_banner', 'Chinesisches Neujahr')
 ),
 JSON_OBJECT(
   'lead_time_days', 30,
   'safety_stock_multiplier', 1.5,
   'max_stock_limit', 10000,
   'categories', JSON_ARRAY('electronics', 'home_garden', 'fashion'),
   'priority_products', JSON_ARRAY('hot_seller', 'new_arrival')
 ),
 1, 1),

('黑色星期五2025', '黑五', '2025-11-24', '2025-11-30', '2025年黑色星期五大促',
 JSON_OBJECT(
   'US', JSON_OBJECT('discount_rate', 0.30, 'free_shipping_threshold', 25, 'promotion_banner', 'Black Friday Mega Sale'),
   'CA', JSON_OBJECT('discount_rate', 0.25, 'free_shipping_threshold', 30, 'promotion_banner', 'Black Friday Canada'),
   'UK', JSON_OBJECT('discount_rate', 0.28, 'free_shipping_threshold', 20, 'promotion_banner', 'Black Friday UK')
 ),
 JSON_OBJECT(
   'lead_time_days', 45,
   'safety_stock_multiplier', 2.0,
   'max_stock_limit', 50000,
   'categories', JSON_ARRAY('electronics', 'fashion', 'sports', 'beauty'),
   'priority_products', JSON_ARRAY('trending', 'high_margin')
 ),
 1, 1);
