<?php
/**
 * @author: zhangguoming
 * @Time: 2024/2/26 17:34
 */

namespace core\jobs;

use admin\form\messagesFrom;

class sendMessageJobs
{
    public string $unqueid = '';
    public array $w_userids = [];
    public string $text = '';
    public array $other_data = [];
    public function __construct($w_userids, $text, $other_data){
        $this->unqueid = uniqid();
        $this->w_userids = $w_userids;
        $this->other_data = $other_data;
        $this->text = $text;
    }

    public function getJobUid(){
        return $this->unqueid;
    }
    //消息发送
    public function task(){
        messagesFrom::senMeg($this->w_userids, $this->text, $this->other_data);
    }
}