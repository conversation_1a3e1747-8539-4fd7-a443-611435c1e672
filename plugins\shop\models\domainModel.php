<?php

namespace plugins\shop\models;

use core\lib\redisCached;
use Exception;
use financial\controller\goodsInformationController;

class domainModel extends baseModel
{
    public string $table = 'domain';

    public static array $paras_list = [
        "domain"          => "域名|required",
        "domain_platform" => "域名平台|required",
        "email_id"        => "域名邮箱ID|required",
        "purchase_date"   => "购买时间|required",
        "expire_date"     => "到期时间|required",
        "valid_days"      => "有效天数|required",
        "remark"          => "备注",
        "status"          => "状态",
    ];

    public static  array $json_keys = [];

    const STATUS_APPLY = 0;  // 申请中
    const STATUS_NORMAL = 1;  // 正常
    const STATUS_RENEWING = 2;  // 续费申请中
    const STATUS_WAIT_RENEWAL = 3;  // 待续费
    const STATUS_EXPIRED = 4;  // 已过期

    public function __construct()
    {
        parent::__construct();
    }

    public function formatItem($item, $maps = [])
    {
        $users = $maps['users'] ?? [];
        if (empty($maps['users'])) {
            $users = redisCached::getUserInfo();
            $users = array_column($users, 'user_name', 'user_id');
        }
        $email = $maps['email'] ?? [];
        if (empty($maps['email'])) {
            $email = redisCached::getEmail();
            $email = array_column($email, 'email_account', 'id');
        }
        $deps = $maps['deps'] ?? [];
        if (empty($deps)) {
            $deps = redisCached::getDepartment();
            $deps = array_column($deps, 'name', 'id');
        }

        $maps = [
            ['name' => 'apply_user_name', 'maps' => $users, 'key' => 'apply_user_id'],
            ['name' => 'operator_name', 'maps' => $users, 'key' => 'operator'],
            ['name' => 'email_account', 'maps' => $email, 'key' => 'email_id'],
            ['name' => 'dep_name', 'maps' => $deps, 'key' => 'dep_id'],
        ];

        return parent::formatItem($item, $maps);

    }

    public function getByDomain($domain, $id = null)
    {
        $this->db->table($this->table)->where('where domain = :domain', ['domain' => $domain]);
        if ($id) {
            $this->db->andWhere('id != :id', ['id' => $id]);
        }
        return $this->db->one();
    }

    public function getList($param, $order = 'id desc', $is_export = false)
    {

        $domain_id = [];
        if (!empty($param['brand_name'])) {
            $list = $this->db->table('trademark')->where('where brand_name like :brand_name', ['brand_name' => '%' . $param['brand_name'] . '%'])->list();
            foreach ($list as $item) {
                $item['domain_id'] = json_decode($item['domain_id'], true);
                foreach ($item['domain_id'] as $value) {
                    if (!in_array($value, $domain_id)) {
                        $domain_id[] = $value;
                    }
                }
            }
        }

        $this->db->table($this->table, 'd')->where('where 1=1');
        // 固定筛选
        if (!empty($param['ids'])) {
            $this->db->whereIn('d.id', $param['ids']);
        }

        if (!empty($domain_id)) {
            $this->db->whereIn('d.id', $domain_id);
        }

        if (!empty($param['domain'])) {
            $this->db->andWhere('domain like :domain', ['domain' => '%' . $param['domain'] . '%']);
        }
        if (!empty($param['domain_platform'])) {
            $this->db->andWhere('domain_platform like :domain_platform', ['domain_platform' => '%' . $param['domain_platform'] . '%']);
        }
        if (!empty($param['purchase_date'])) {
            $this->db->andWhere('purchase_date >= :purchase_date_start and purchase_date <= :purchase_date_end', [
                'purchase_date_start' => $param['purchase_date'][0],
                'purchase_date_end'   => $param['purchase_date'][1],
            ]);
        }
        if (!empty($param['expire_date'])) {
            $this->db->andWhere('expire_date >= :expire_date_start and expire_date <= :expire_date_end', [
                'expire_date_start' => $param['expire_date'][0],
                'expire_date_end'   => $param['expire_date'][1],
            ]);
        }
        if (!empty($param['apply_date'])) {
            $this->db->andWhere('created_at >= :create_time_start and d.created_at <= :create_time_end', [
                'create_time_start' => $param['apply_date'][0],
                'create_time_end'   => $param['apply_date'][1].' 23:59:59',
            ]);
        }
        if (!empty($param['update_time'])) {
            $this->db->andWhere('updated_at >= :update_time_start and d.updated_at <= :update_time_end', [
                'update_time_start' => $param['update_time'][0],
                'update_time_end'   => $param['update_time'][1],
            ]);
        }
        if (isset($param['status'])) {
            $this->db->andWhere('status = :status', ['status' => $param['status']]);
        }
        if (!empty($param['user_id'])) {
            $this->db->andWhere('apply_user_id = :user_id', ['user_id' => $param['user_id']]);
        }
        if (!empty($param['dep_id'])) {
            $this->db->whereIn('dep_id', $param['dep_id']);
        }

        $this->db->order($order);

        if (!empty($param['page']) && !empty($param['page_size'])) {
            $list = $this->db->pages($param['page'], $param['page_size']);

            $maps = self::getMaps();
            foreach ($list['list'] as &$item) {
                $item = $this->formatItem($item, $maps);
            }
            return $list;
        } else {
            $list = $this->db->list();
            if ($is_export) {
                $export_data = [];
                $maps = self::getMaps();
                $paras_list = self::$paras_list;
                $paras_list['dep_name'] = '使用部门';
                $paras_list['email_account'] = '域名邮箱';
                foreach ($list as $item) {
                    $item = $this->formatItem($item, $maps);
                    // 使用参数对应的中文作为键名返回
                    $export_data[] = self::changeToCnKey($item, $paras_list);
                }

                return $export_data;
            }
            return $list;
        }
    }

    public static function getMaps () {
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        $email = redisCached::getEmail();
        $email = array_column($email, 'email_account', 'id');

        $deps = redisCached::getDepartment();
        $deps = array_column($deps, 'name', 'id');

        return ['users' => $users, 'email' => $email, 'deps' => $deps];
    }

    public function edit($data, $id, $old_data = [], $type = '编辑', $remark = '', $result = '', $other_attach = [])
    {
        $email_id = $data['email_id'] ?? null;
        if ($email_id) {
            $relationModel = new relationModel();
            // 使用updateRelation方法,会自动处理创建或更新
            $relationModel->updateRelation($this->table, $id, 'email', $email_id);
        }

        parent::edit($data, $id, $old_data, $type, $remark, $result, $other_attach);
    }

    // 批量编辑
    public function editBatch($data)
    {
        $db = $this->db->table($this->table);
        $ids = array_column($data, 'id');
        $db->whereIn('id', $ids);
        $list = $db->list();
        $list = array_column($list, null, 'id');

        $error_ids = [];
        foreach ($data as $item) {
            try {
                $item_id = $item['id'];
                unset($item['id']);
                // 唯一性校验
                $this->dataValidCheck($item, self::$paras_list);
                $detail = $this->getByDomain($item['domain'], $item_id);
                if ($detail) {
                    throw new Exception('域名已存在');
                }
                self::edit($item, $item_id, $list[$item_id], '批量编辑');
            } catch (Exception $e) {
                $error_ids[] = [
                    'id' => $item_id,
                    'error' => $e->getMessage(),
                ];
            }
        }
        return $error_ids;
    }

    // 数据校验
    public function dataValidCheck(array $data, array $param_list = [], $is_throw = true, array $error = []): array
    {
        if (empty($param_list)) $param_list = self::$paras_list;
        return parent::dataValidCheck($data, $param_list, $is_throw);
    }
}
