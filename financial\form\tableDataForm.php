<?php
/**
 * @author: zhangguoming
 * @Time: 2024/8/14 9:53
 */

namespace financial\form;

use core\lib\db\dbFMysql;
use core\lib\ExceptionError;
use financial\models\mskuReportModel;
use financial\models\tableDataModel;
use financial\models\userModel;

class tableDataForm
{
    //获取领星对接过来的数据
    public static function getLxData($field,$group,$m_date,$year) {
        mskuReportModel::creatMskuReportTable($year);
        tableDataModel::creatTableMonth($year);
        $db = dbFMysql::getInstance();
        $lx_keys = $db->table('column')
            ->where('where custom_id = 0 and show_type > 0')
            ->andWhere('show_type <> 2')
            ->field('key_name')
            ->list();
        $keys = array_column($lx_keys,'key_name');
        //搜索的字段
        foreach ($keys as $w_k) {
            $field[] = "sum($w_k) as $w_k";
        }
        $field_string = implode(',',$field);
        $db->table('msku_report_data_'.$year)
            ->where('where is_delete=0 and reportDateMonth=:m_date',['m_date'=>$m_date]);
        $db->field($field_string)
            ->groupBy($group);
        $list = $db->list();
        return $list;
    }
    public static function arrangeList($lx_list,$m_date,$year) {
        //整理数据
        $table_name = 'table_month_count_'.$year;
        //删除本月数据
        $db = dbFMysql::getInstance();
        $db->beginTransaction();
        try {
            //删除之前的数据
            $db->table($table_name)
                ->where('where reportDateMonth = :m_date',['m_date'=>$m_date])
                ->delete();
            //保存现在的数据
            $keys = array_merge(array_keys($lx_list[0]),['reportDateMonth','created_time']);
            $date_ = [$m_date,date('Y-m-d H:i:s')];
            $list = array_map(function($item) use ($date_) {
                $item = array_values($item);
                return array_merge($item,$date_);
            }, $lx_list);
            $chunkedArray = array_chunk($list, 500);
            foreach ($chunkedArray as $v) {
                $db->table($table_name)
                    ->insertBatch($keys,$v);
            }
            $db->commit();
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }

    //原始数据统计
    public static function oldGetLxData($m_date,$year) {
        $field = ['sid','asin','msku','parentAsin','localSku','countryCode','project_id','yunying_id','supplier_id','is_delete','itemName','brandName','principalRealname','productDeveloperRealname','categoryName','currencyCode','listingTagIds'];
        $group = ['sid','asin','msku','parentAsin','localSku','countryCode','project_id','yunying_id','supplier_id','is_delete','itemName','brandName','principalRealname','productDeveloperRealname','categoryName','currencyCode','listingTagIds'];
        mskuReportModel::creatMskuReportOriginalTable($year);
        $db = dbFMysql::getInstance();
        $lx_keys = $db->table('column')
            ->where('where custom_id = 0 and show_type > 0')
            ->andWhere('show_type <> 2')
            ->field('key_name')
            ->list();
        $keys = array_column($lx_keys,'key_name');
        //搜索的字段
        foreach ($keys as $w_k) {
            $field[] = "sum($w_k) as $w_k";
        }
        $field_string = implode(',',$field);
        $db->table('msku_report_data_'.$year)
            ->where('where reportDateMonth=:m_date and import_id = 0',['m_date'=>$m_date]);
        $list = $db->field($field_string)
            ->groupBy($group)
            ->list();
        $db->beginTransaction();
        try {
            //删除
            $db->table('msku_original_data_'.$year)
                ->where('where reportDateMonth=:m_date',['m_date'=>$m_date])
                ->delete();
            //保存
            if (count($list)) {
                $chunkedArray = array_chunk($list, 1000);
                foreach ($chunkedArray as $list) {
                    $insert_array = [];
                    $insert_str_array = [];
                    foreach ($list as $k=>$v) {
                        $v['reportDateMonth'] = $m_date;
                        $str_one_array = [];
                        foreach ($v as $key_=>$val_) {
                            $k_k = $key_.'_'.$k;
                            $str_one_array[] = ':'.$k_k;
                            $insert_array[$k_k] = $val_;
                        }
                        $str_one = implode(',',$str_one_array);
                        $insert_str_array[] = '('.$str_one.')';
                    }
                    $insert_kesy = implode(',',array_keys($v));
                    $insert_str = implode(',',$insert_str_array);
                    $sql = "INSERT INTO oa_f_msku_original_data_$year ($insert_kesy) VALUES $insert_str";
                    $db->query($sql,$insert_array);
                }
            }
            $db->commit();
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }

    }
    //获取该用户可查询月份
    public static function getDataMonth($db) {
        //结账月份
        $checkout_list = $db->table('checkout')
            ->field('id,m_date')
            ->where('where is_lock=1')
            ->order('m_date asc')
            ->list();
        $locked_month = array_column($checkout_list,'m_date');
        //权限月份
        $data_auth = userForm::getUserInfo();
        if (!userModel::$is_super) {
            if (count($data_auth)) {
                if (!$data_auth['is_all_data']) {
                    if (!empty($data_auth['begin_time'])) {
                        $begin_time = strtotime($data_auth['begin_time']);
                        $locked_month = array_filter($locked_month,function ($m_date) use ($begin_time){
                            $time = strtotime($m_date);
                            return $time >= $begin_time;
                        });
                    }
                    if (!empty($data_auth['end_time'])) {
                        $end_time = strtotime($data_auth['end_time']);
                        $locked_month = array_filter($locked_month,function ($m_date) use ($end_time){
                            $time = strtotime($m_date);
                            return $time<=$end_time;
                        });
                    }
                }
                
            } else {
                returnError('您还未被设置“数据时限“');
            }
        }
        return $locked_month;
    }
}