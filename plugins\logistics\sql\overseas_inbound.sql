-- 海外仓备货单表
CREATE TABLE `oa_l_overseas_inbound` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `overseas_order_no` varchar(100) NOT NULL DEFAULT '' COMMENT '备货单号',
  `inbound_order_no` varchar(100) NOT NULL DEFAULT '' COMMENT '三方入库单号',
  `customer_reference_no` varchar(100) NOT NULL DEFAULT '' COMMENT '客户提交参考号',
  `s_wid` int(11) NOT NULL DEFAULT 0 COMMENT '发货仓id',
  `s_wname` varchar(200) NOT NULL DEFAULT '' COMMENT '发货仓名称',
  `r_wid` int(11) NOT NULL DEFAULT 0 COMMENT '收货仓id',
  `r_wname` varchar(200) NOT NULL DEFAULT '' COMMENT '收货仓名称',
  `logistics_id` int(11) NOT NULL DEFAULT 0 COMMENT '物流方式id',
  `logistics_name` varchar(200) NOT NULL DEFAULT '' COMMENT '物流方式名称',
  `remark` text COMMENT '备注',
  `status` tinyint(3) NOT NULL DEFAULT 0 COMMENT '状态：10待审核 20已驳回 30待配货 40待发货 50待收货 51已撤销 60已完成',
  `sub_status` tinyint(3) NOT NULL DEFAULT 0 COMMENT '子状态：0全部 1未收货 2部分收货',
  `rollback_remark` text COMMENT '驳回备注',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已删除：0正常 1已删除',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '创建用户id',
  `create_user` varchar(100) NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(100) NOT NULL DEFAULT '' COMMENT '最后更新人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `estimated_time` datetime DEFAULT NULL COMMENT '预计到货时间',
  `audit_handle_time` datetime DEFAULT NULL COMMENT '审核时间',
  `send_good_handle_time` datetime DEFAULT NULL COMMENT '发货时间',
  `receive_good_handle_time` datetime DEFAULT NULL COMMENT '收货时间',
  `real_delivery_time` date DEFAULT NULL COMMENT '实际发货时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `products` text COMMENT '商品信息JSON',
  `logistics` text COMMENT '物流数据JSON',
  `logistics_list_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '物流信息版本：0旧版 1新版',
  `head_logistics_list` text COMMENT '新版头程物流信息JSON',
  
  -- 可编辑字段
  `transparent_label` varchar(200) NOT NULL DEFAULT '' COMMENT '透明标',
  `warehouse_code` varchar(100) NOT NULL DEFAULT '' COMMENT '入仓编码（默认备货单号）',
  `shop_code` varchar(100) NOT NULL DEFAULT '' COMMENT '店铺代码',
  `fnsku` varchar(200) NOT NULL DEFAULT '' COMMENT 'FNSKU',
  `remaining_available` int(11) NOT NULL DEFAULT 0 COMMENT '剩余可用',
  `shipping_remark` text COMMENT '发货备注',
  `other_remark` text COMMENT '其他备注',
  
  `sync_date` date NOT NULL COMMENT '同步日期',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_overseas_order_no` (`overseas_order_no`),
  KEY `idx_status` (`status`),
  KEY `idx_s_wid` (`s_wid`),
  KEY `idx_r_wid` (`r_wid`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_sync_date` (`sync_date`),
  KEY `idx_is_delete` (`is_delete`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海外仓备货单表';
