<?php
/**
 * @author: zhangguoming
 * @Time: 2025/3/24 16:35
 */

namespace admin\form;

use admin\models\userModel;
use core\lib\db\dbMysql;

class statisticsForm
{
    //待办事项统计
    public static function getWaitAgentNum() {
        //产品
        $db = dbMysql::getInstance();
        //1、待办+计时任务
        $wait_agent_goods = $db->table('goods_matters')
            ->where('qwuser_id = :qwuser_id',['qwuser_id'=>userModel::$qwuser_id])
            ->andWhere('((status=0 and is_advance_submit=0) or (status=1 and is_advance_submit=1))')
            ->count();
        return [
            'goods'=>$wait_agent_goods
        ];
    }
    //已办时间统计
    public static function getHadAgentNum() {
        //产品
        $db = dbMysql::getInstance();
        //1、待办类型
        //2、已完成，即使任务已完成或者非计时任务；(所有类型的事项)
        $had_agent_goods = $db->table('goods_matters')
            ->where('qwuser_id = :qwuser_id',['qwuser_id'=>userModel::$qwuser_id])
            ->andWhere('status = 1')
            ->count();
        return [
            'goods'=>$had_agent_goods
        ];
    }
    //我的申请
    public static function getApplayNum() {
        //产品
        $db = dbMysql::getInstance();
        $applay_goods = $db->table('goods_project')
            ->where('user_id = :user_id and flow_path_id<>1 and is_delete = 0',['user_id'=>userModel::$qwuser_id])
            ->count();
        return [
            'goods'=>$applay_goods
        ];
    }
    //我的申请-已完成
    public static function getApplaycompleteNum() {
        //产品
        $db = dbMysql::getInstance();
        $applay_goods = $db->table('goods_project')
            ->where('user_id = :user_id and flow_path_id<>1 and is_delete = 0 and status in(0,1,3)',['user_id'=>userModel::$qwuser_id])
            ->count();
        return [
            'goods'=>$applay_goods
        ];
    }
}