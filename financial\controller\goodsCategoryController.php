<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/19 16:55
 */

namespace financial\controller;

use core\lib\db\dbFMysql;

class goodsCategoryController
{
    public function getList() {
        $db = dbFMysql::getInstance();
        $data = $db->table('goods_category')
            ->where('where is_delete = 0')
            ->field('id,cid,parent_cid,title')
            ->order('sort asc')
            ->list();
        $new_list = [];
        if (count($data)) {
            function child($list, $pid) {
                $new_list = [];
                $i = 0;
                foreach ($list as $k=>$cate) {
                    if ($cate['parent_cid'] == $pid) {
                        $new_list[$i] = $cate;
                        unset($list[$k]);
                        $new_list[$i]['child'] = child($list,$cate['cid']);
                        $i++;
                    }
                }
                return $new_list;
            }
            $new_list = child($data,0);
        }
        returnSuccess($new_list);
    }
}