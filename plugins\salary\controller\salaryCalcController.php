<?php

namespace plugins\salary\Controller;

use plugins\assessment\models\customCrontabModel;
use core\lib\config;
use core\lib\db\dbAMysql;
use core\lib\db\dbCMysql;
use plugins\salary\form\auditForm;
use plugins\salary\models\salaryCalculationModel;
use plugins\salary\models\salarySchemeModel;
use core\lib\db\dbMysql;
use core\lib\db\dbSMysql;
use plugins\salary\models\userModel;

class salaryCalcController
{
    public function getConfig()
    {
        $adb = dbAMysql::getInstance();
        $adb->table('custom_crontab');
        $config = $adb->where('where link_module = 2 and link_type = 1')->one();
        $cron_expression = $config['cron_expression'] ?? '';
        $cron_expression = explode(' ', $cron_expression);
        $date = $cron_expression[2] ?? '';

        returnSuccess([
            'status' => $config ? 1 : 0,
            'date'   => $date ?? ''
        ]);
    }

    public function setConfig()
    {
        $paras_list = array('status', 'date');
        $param = arrangeParam($_POST, $paras_list);
        if (!isset($param['status'])) returnError('开启状态不能为空');
        if (!$param['date']) returnError('日期不能为空');

        $adb = dbAMysql::getInstance();
        $adb->table('custom_crontab');
        $config = $adb->where('where link_module = 2 and link_type = 1 and status = -1')->one();

        if ($config && $config['status'] != -1) returnError('正在生成算薪任务，请稍后');

        $date = date('Y-m-' . sprintf('%02d', $param['date']));
        if (date('d') > $param['date']) {
            $date = date('Y-m-' . sprintf('%02d', $param['date']), strtotime('+1 month'));
        }
        if (!$param['status']) {
            if ($config) {
                $adb->table('custom_crontab');
                $adb->where('where id = :id', ['id' => $config['id']]);
                $adb->delete();
            }
            returnSuccess([], '设置成功');
        }

        if (!$config) {
            $adb->table('custom_crontab');
            $adb->insert([
                'is_crontab_task' => 1,
                'link_type'       => 1,
                'link_id'         => 0,
                'link_module'     => customCrontabModel::MODULE_SALARY,
                'runtime'         => $date,
                'cron_expression' => '0 0 ' . $param['date'] . ' * ?',
                'status'          => -1
            ]);
        } else {
            $adb->table('custom_crontab');
            $adb->where('where id = :id', ['id' => $config['id']]);
            $adb->update([
                'runtime'         => $date,
                'cron_expression' => '0 0 ' . $param['date'] . ' * ?',
                'status'          => -1
            ]);
        }
        returnSuccess([], '设置成功');
    }

    // 获取薪资计算列表
    public function getList()
    {
        $paras_list = array('calc_name', 'scheme', 'month', 'status', 'page', 'page_size');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_calculation');
        $sdb->where('where is_delete = 0');

        if (userModel::getUserListAuth('salaryCalcAll')) {
            // 全部权限
        } elseif (userModel::getUserListAuth('salaryCalcRelated')) {
            $sdb->andWhere('operator = :operator', ['operator' => userModel::$qwuser_id]);
        }

        if ($param['calc_name']) {
            $sdb->andWhere('calc_name like :calc_name', ['calc_name' => '%' . $param['calc_name'] . '%']);
        }
        if ($param['scheme']) {
            $scheme = json_decode($param['scheme'], true);
            $sdb->whereIn('scheme_id', $scheme);
        }
        if ($param['month']) {
            $month = json_decode($param['month'], true);
            $sdb->andWhere('month >= :start and month <= :end', ['start' => $month[0], 'end' => $month[1]]);
        }
        if ($param['status']) {
            $status = json_decode($param['status'], true);
            $sdb->whereIn('status', $status);
        }
        $sdb->order('id desc');
        $data = $sdb->pages($page, $limit);

        if (empty($data['list'])) returnSuccess($data);

        $calc_ids = array_column($data['list'], 'id');
        $user_count = $sdb->table('salary_calculation_user')
            ->field('count(id) as user_count, calc_id')
            ->where('where is_delete = 0')
            ->whereIn('calc_id', $calc_ids)
            ->groupBy(['calc_id'])
            ->list();
        $user_count = array_column($user_count, 'user_count', 'calc_id');


        foreach ($data['list'] as &$item) {
            $item['attach'] = json_decode($item['attach'], true);
            $item['user_count'] = $user_count[$item['id']] ?? 0;
        }


        returnSuccess($data);
    }

    // 新增/编辑薪资计算
    public function add()
    {
        $paras_list = array('calc_name', 'scheme_id', 'month');
        $length_data = ['calc_name' => ['name' => '算薪表名称', 'length' => 50]];
        $request_list = ['calc_name' => '算薪表名称', 'scheme_id' => '算薪方案', 'month' => '算薪月份'];
        $param = arrangeParam($_POST, $paras_list, $request_list, $length_data);

        $sdb = dbSMysql::getInstance();

        $scheme_id = $param['scheme_id'];
        $sdb->table('salary_scheme')->where('where id = :id and is_delete = 0', ['id' => $scheme_id]);
        $scheme = $sdb->one();
        if (!$scheme) {
            returnError('算薪方案不存在');
        }
        $scheme['attach'] = $scheme['attach'] ? json_decode($scheme['attach'], true) : null;
        $scheme['config'] = $scheme['config'] ? json_decode($scheme['config'], true) : null;

        // 找出浮动薪资项
        $item_ids = []; // 需要计算的浮动薪资项
        foreach ($scheme['config'] as $item) {
            if ($item['module'] == 5) { // 浮动薪资项
                $item_ids = $item['list'];
            }
        }
        $items = $sdb->table('salary_item')
            ->where('where is_delete = 0')
            ->whereIn('id', $item_ids)
            ->list();

        $items = array_map(function ($item) {
            $item['item_detail'] = json_decode($item['item_detail'], true);
            return $item;
        }, $items);

        $status_map = config::get('salary_calculation_status', 'data_salary');
        $status_map = array_column($status_map, 'name', 'id');

        // 查询当前方案是否有未完成的算薪任务
        $calc = $sdb->table('salary_calculation')
            ->where('where scheme_id = :scheme_id and month = :month and is_delete = 0', ['scheme_id' => $scheme_id, 'month' => $param['month']])
            ->whereIn('status', [
                salaryCalculationModel::STATUS_WAIT_CALC,
                salaryCalculationModel::STATUS_CALCULATING,
                salaryCalculationModel::STATUS_WAIT_CHECK,
                salaryCalculationModel::STATUS_WAIT_APPROVE,
                salaryCalculationModel::STATUS_FINISHED
            ])
            ->one();
        if ($calc) returnError('已有'.$status_map[$calc['status']].'状态的薪资计算，不可再次发起');

        $scheme_users = salarySchemeModel::getSchemeUsers($scheme['attach']);
        if (empty($scheme_users)) {
            returnError('算薪方案没有算薪对象');
        }

        // 考勤数据验证
        $cdb = dbCMysql::getInstance();
        $detail = $cdb->table('user_checkin_month')
            ->where('where month = :month', ['month' => $param['month']])
            ->one();
        if (!$detail) returnError('考勤数据未提交，不能算薪');

        $data = [
            'calc_name' => $param['calc_name'],
            'scheme_id' => $param['scheme_id'],
            'month'     => $param['month'],
            'attach'    => json_encode([
                'scheme' => $scheme,
                'items'  => $items,
            ], JSON_UNESCAPED_UNICODE),
            'operator'  => userModel::$qwuser_id,
            'status'    => salaryCalculationModel::STATUS_WAIT_CALC,
        ];
        $id = $sdb->table('salary_calculation')->insert($data);
        $data['id'] = $id;
        if (!$id) returnError('发起失败');

        // 生成薪资计算用户
        salaryCalculationModel::createCalcUser($data, $scheme_users);

        returnSuccess(['id' => $id], '发起成功');
    }

    // 获取算薪下员工
    public function getUserList()
    {
        $paras_list = array('id', 'user_id', 'page', 'page_size');
        $param = arrangeParam($_GET, $paras_list);
        if (!$param['id']) returnError('id不能为空');
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        $sdb = dbSMysql::getInstance();
        $calc = $sdb->table('salary_calculation')->where('where id = :id and is_delete = 0', ['id' => $param['id']])->one();
        if (!$calc) returnError('算薪表不存在');

        $sdb->table('salary_calculation_user')->where('where calc_id = :calc_id', ['calc_id' => $param['id']]);
        if ($param['user_id']) {
            $user_id = json_decode($param['user_id'], true);
            if (!empty($user_id)) {
                $sdb->whereIn('user_id', $user_id);
            }
        }
        $data = $sdb->pages($page, $limit);
        if (empty($data['list'])) returnSuccess($data);

        foreach ($data['list'] as &$item) {
            $item['result'] = json_decode($item['result'], true);
            $item['attach'] = json_decode($item['attach'], true) ?? null;
        }

        returnSuccess($data);
    }

    // 添加算薪员工
    public function addUser()
    {
        $paras_list = array('id', 'users');
        $param = arrangeParam($_POST, $paras_list);
        $id = $param['id'];
        $users = json_decode($param['users'], true);
        if (!$id) returnError('id不能为空');
        if (!$users) returnError('users不能为空');

        $sdb = dbSMysql::getInstance();
        $calc = $sdb->table('salary_calculation')->where('where id = :id and is_delete = 0', ['id' => $id])->one();
        if (!$calc) returnError('算薪表不存在');
        if ($calc['status'] != salaryCalculationModel::STATUS_WAIT_CALC) {
            returnError('只有待核算状态才能添加员工');
        }

        $sdb->table('salary_calculation_user')->where('where calc_id = :calc_id', ['calc_id' => $id]);
        $check = $sdb->whereIn('user_id', $users)->one();

        if ($check) returnError('不能重复添加员工');

        // 查询当前方案是否有未完成的算薪任务
        $calc_user = $sdb->table('salary_calculation_user', 'u')
            ->leftJoin('salary_calculation', 'sc', 'sc.id=u.calc_id')
            ->where('where sc.is_delete = 0 and u.is_delete = 0 and sc.month = :month', ['month' => $calc['month']])
            ->whereIn('u.status', [
                salaryCalculationModel::STATUS_WAIT_CALC,
                salaryCalculationModel::STATUS_CALCULATING,
                salaryCalculationModel::STATUS_WAIT_CHECK,
                salaryCalculationModel::STATUS_WAIT_APPROVE,
                salaryCalculationModel::STATUS_FINISHED
            ])
            ->whereIn('u.user_id', $users)
            ->one();
        if ($calc_user) returnError('已有存在已有薪资计算的用户，不可添加');

        $db = dbMysql::getInstance();
        // 用户基本信息
        $scheme_users = $db->table('qwuser', 'u')
            ->field('u.id as user_id,u.wid, u.wname, ui.user_status')
            ->leftJoin('user_info', 'ui', 'u.id=ui.qwuser_id')
            ->whereIn('u.id', $users)
            ->list();
        $scheme_users = array_column($scheme_users, null, 'user_id');

        // 生成薪资计算用户
        salaryCalculationModel::createCalcUser($calc, $scheme_users);

        returnSuccess([], '添加用户成功');
    }

    // 删除算薪员工
    public function deleteUser()
    {
        $paras_list = array('id', 'ids');
        $param = arrangeParam($_POST, $paras_list);
        $id = $param['id'];
        $ids = json_decode($param['ids'], true);
        if (!$id) returnError('id不能为空');
        if (!$ids) returnError('ids不能为空');

        $sdb = dbSMysql::getInstance();
        $calc = $sdb->table('salary_calculation')->where('where id = :id and is_delete = 0', ['id' => $id])->one();
        if (!$calc) returnError('算薪表不存在');
        if ($calc['status'] != salaryCalculationModel::STATUS_WAIT_CALC) {
            returnError('只有待核算状态才能删除员工');
        }

        $sdb->table('salary_calculation_user');
        $sdb->where('where calc_id = :calc_id and is_delete = 0', ['calc_id' => $id]);
        $sdb->whereIn('id', $ids);
        $res = $sdb->list();

        if (!$res) returnError('员工不存在');
        if (count($res) != count($ids)) returnError('员工不存在');

        $sdb->table('salary_calculation_user');
        $sdb->where('where is_delete = 0');
        $sdb->whereIn('id', $ids);
        $res = $sdb->delete();

        returnSuccess([], '删除用户成功');
    }

    // 开始核算
    public function calculate()
    {
        $paras_list = array('id');
        $param = arrangeParam($_POST, $paras_list);
        $id = $param['id'];
        if (!$id) returnError('id不能为空');
        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_calculation');
        $sdb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $detail = $sdb->one();
        if (empty($detail)) returnError('薪资计算不存在');
        if ($detail['status'] != salaryCalculationModel::STATUS_WAIT_CALC) {
            returnError('只有待核算状态才能核算');
        }

        // 是否存在算薪用户
        $calc_user = $sdb->table('salary_calculation_user')->where('where calc_id = :calc_id', ['calc_id' => $param['id']])->one();
        if (!$calc_user) returnError('当前算薪计算中没有用户, 不能发起核算');

        // 算薪员工状态变更
        $update_data = [
            'status' => salaryCalculationModel::STATUS_CALCULATING,
        ];
        $sdb->table('salary_calculation_user')
            ->where('where calc_id = :calc_id', ['calc_id' => $param['id']])
            ->update($update_data);

        // 算薪任务状态变更
        $update_data['operator'] = userModel::$qwuser_id;
        $sdb->table('salary_calculation')
            ->where('where id = :id', ['id' => $id])
            ->update($update_data);

        $adb = dbAMysql::getInstance();
        // 增加定时任务进行绩效核算
        $crontab_data = [
            'is_crontab_task' => 0, // 非周期任务
            'link_id'         => $detail['id'],
            'link_type'       => 2,
            'link_module'     => customCrontabModel::MODULE_SALARY,
            'runtime'         => date('Y-m-d H:i:s'),
        ];
        $adb->table('custom_crontab');
        $adb->insert($crontab_data);

        // 10 min 增加定时任务进行算薪失败
        $crontab_data = [
            'is_crontab_task' => 0, // 非周期任务
            'link_id'         => $detail['id'],
            'link_type'       => 3,
            'link_module'     => customCrontabModel::MODULE_SALARY,
            'runtime'         => date('Y-m-d H:i:s', strtotime('+10 minutes')),
        ];
        $adb->table('custom_crontab');
        $adb->insert($crontab_data);
        returnSuccess([], '已开始核算');
    }

    // 取消
    public function cancel()
    {
        $paras_list = array('id');
        $param = arrangeParam($_POST, $paras_list);
        $id = $param['id'];
        if (!$id) returnError('id不能为空');
        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_calculation');
        $sdb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $detail = $sdb->one();
        if (empty($detail)) returnError('薪资计算不存在');

        if ($detail['status'] != salaryCalculationModel::STATUS_WAIT_CALC) {
            returnError('只有待核算状态才能取消');
        }

        $data = [
            'status'   => salaryCalculationModel::STATUS_CANCEL,
            'operator' => userModel::$qwuser_id
        ];
        $sdb->table('salary_calculation');
        $sdb->where('where id = :id', ['id' => $id]);
        $sdb->update($data);
        returnSuccess([], '取消成功');
    }

    // 作废
    public function invalid()
    {
        $paras_list = array('id');
        $param = arrangeParam($_POST, $paras_list);
        $id = $param['id'];
        if (!$id) returnError('id不能为空');
        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_calculation');
        $sdb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $detail = $sdb->one();
        if (empty($detail)) returnError('薪资计算不存在');

        // 待核算、已完成、已取消、已作废 ，不能作废
        if (in_array($detail['status'], [
            salaryCalculationModel::STATUS_WAIT_CALC,
            salaryCalculationModel::STATUS_FINISHED,
            salaryCalculationModel::STATUS_CANCEL,
            salaryCalculationModel::STATUS_INVALID
        ])) {
            returnError('当前状态不能作废');
        }

        $data = [
            'status'   => salaryCalculationModel::STATUS_INVALID,
            'operator' => userModel::$qwuser_id
        ];
        $sdb->table('salary_calculation');
        $sdb->where('where id = :id', ['id' => $id]);
        $sdb->update($data);
        returnSuccess([], '作废成功');
    }

    // 审核
    public function check()
    {
        $paras_list = array('id', 'ids', 'is_all', 'audit_status', 'audit_desc');
        $param = arrangeParam($_POST, $paras_list);
        $id = $param['id'];
        $ids = json_decode($param['ids'], true) ?? [];
        $is_all = $param['is_all'] ?? 0;
        if (!$id) returnError('id不能为空');
        if (!$ids && !$is_all) {
            returnError('至少选择一项才能审核');
        }
        if (!isset($param['audit_status'])) returnError('审核状态不能为空');
        if (empty($param['audit_desc'])) returnError('审核意见不能为空');

        $sdb = dbSMysql::getInstance();
        $calc = $sdb->table('salary_calculation')->where('where id = :id and is_delete = 0', ['id' => $id])->one();
        if (!$calc) returnError('算薪表不存在');

        $sdb->table('salary_calculation_user')
            ->where('where is_delete = 0 and status = :status', ['status' => salaryCalculationModel::STATUS_WAIT_CHECK]);

        if (!$is_all) {
            $sdb->whereIn('id', $ids);
        }
        $records = $sdb->list();
        if (empty($records)) returnError('数据不存在');

        $update_data = [];
        foreach ($records as $record) {
            $record['audit_attach'] = json_decode($record['audit_attach'], true);
            $node = $record['audit_attach']['check']['node'];
            if (!isset($record['audit_attach']['check']['rule'][$node - 1]['audit_list'])) {
                $record['audit_attach']['check']['rule'][$node - 1]['audit_list'] = [];
            }

            $update_item = [];
            if ($param['audit_status'] == 0) {// 审批拒绝
                $record['audit_attach']['check']['rule'][$node - 1]['audit_list'][] = [
                    'user_id'    => userModel::$qwuser_id,
                    'audit_desc' => $param['audit_desc'],
                ];
                $update_item = [
                    'status'       => salaryCalculationModel::STATUS_CHECK_FAIL,
                    'audit_attach' => json_encode($record['audit_attach'], JSON_UNESCAPED_UNICODE),
                ];
            } else { // 审批通过
                $record['audit_attach']['check']['rule'][$node - 1]['audit_list'][] = [
                    'user_id'    => userModel::$qwuser_id,
                    'audit_desc' => $param['audit_desc'],
                ];
                $next_node = auditForm::getNextNode($record['audit_attach']['check']['rule'], $record['audit_attach']['check']['node'], $record['audit_attach']['check']['audit_user']);

                if (empty($next_node)) {
                    $record['audit_status'] = salaryCalculationModel::STATUS_FINISHED;
                    if (isset($record['audit_attach']['approve'])) {
                        $record['audit_status'] = salaryCalculationModel::STATUS_WAIT_APPROVE;
                        $next_node = auditForm::getNextNode($record['audit_attach']['approve']['rule']);
                        // 下一个节点
                        if (!empty($next_node)) {
                            $audit_user = $next_node['audit_user'];
                            $record['audit_attach']['approve']['node'] = $next_node['node'];
                            $record['audit_attach']['approve']['audit_user'] = $audit_user;
                        }
                    }
                    $update_item = [
                        'status'       => $record['audit_status'],
                        'audit_attach' => json_encode($record['audit_attach'], JSON_UNESCAPED_UNICODE),
                    ];
                } else {
                    $record['audit_attach']['check']['node'] = $next_node['node'];
                    $record['audit_attach']['check']['audit_user'] = $next_node['audit_user'];
                    $update_item = [
                        'audit_attach' => json_encode($record['audit_attach'], JSON_UNESCAPED_UNICODE),
                    ];
                }
            }
            if (!empty($update_item)) {
                $update_item['id'] = $record['id'];
                $update_data[] = $update_item;
            }
        }
        if (!empty($update_data)) {
            $sdb->table('salary_calculation_user')->updateBatch($update_data);
        }
        salaryCalculationModel::changeCalculationStatus($id);
        returnSuccess([], '审核成功');

    }

    // 审批
    public function approve()
    {
        $paras_list = array('id', 'ids', 'is_all', 'audit_status', 'audit_desc');
        $param = arrangeParam($_POST, $paras_list);
        $id = $param['id'];
        $ids = json_decode($param['ids'], true) ?? [];
        $is_all = $param['is_all'] ?? 0;
        if (!$id) returnError('id不能为空');
        if (!$ids && !$is_all) {
            returnError('至少选择一项才能审核');
        }
        if (!isset($param['audit_status'])) returnError('审核状态不能为空');
        if (empty($param['audit_desc'])) returnError('审核意见不能为空');

        $sdb = dbSMysql::getInstance();
        $calc = $sdb->table('salary_calculation')->where('where id = :id and is_delete = 0', ['id' => $id])->one();
        if (!$calc) returnError('算薪表不存在');

        $sdb->table('salary_calculation_user')
            ->where('where is_delete = 0 and status = :status', ['status' => salaryCalculationModel::STATUS_WAIT_APPROVE]);

        if (!$is_all) {
            $sdb->whereIn('id', $ids);
        }

        $records = $sdb->list();
        if (empty($records)) returnError('数据不存在');

        $update_data = [];
        foreach ($records as $record) {
            $record['audit_attach'] = json_decode($record['audit_attach'], true);
            $node = $record['audit_attach']['approve']['node'];
            if (!isset($record['audit_attach']['approve']['rule'][$node - 1]['audit_list'])) {
                $record['audit_attach']['approve']['rule'][$node - 1]['audit_list'] = [];
            }

            $update_item = [];
            if ($param['audit_status'] == 0) {// 审批拒绝
                $record['audit_attach']['approve']['rule'][$node - 1]['audit_list'][] = [
                    'user_id'    => userModel::$qwuser_id,
                    'audit_desc' => $param['audit_desc'],
                ];
                $update_item = [
                    'status'       => salaryCalculationModel::STATUS_APPROVE_FAIL,
                    'audit_attach' => json_encode($record['audit_attach'], JSON_UNESCAPED_UNICODE),
                ];
            } else { // 审批通过
                $record['audit_attach']['approve']['rule'][$node - 1]['audit_list'][] = [
                    'user_id'    => userModel::$qwuser_id,
                    'audit_desc' => $param['audit_desc'],
                ];
                $next_node = auditForm::getNextNode($record['audit_attach']['approve']['rule'], $record['audit_attach']['approve']['node'], $record['audit_attach']['approve']['audit_user']);

                if (empty($next_node)) {
                    $update_item = [
                        'status'       => salaryCalculationModel::STATUS_FINISHED,
                        'audit_attach' => json_encode($record['audit_attach'], JSON_UNESCAPED_UNICODE),
                    ];
                } else {
                    $record['audit_attach']['approve']['node'] = $next_node['node'];
                    $record['audit_attach']['approve']['audit_user'] = $next_node['audit_user'];
                    $update_item = [
                        'audit_attach' => json_encode($record['audit_attach'], JSON_UNESCAPED_UNICODE),
                    ];
                }
            }
            if (!empty($update_item)) {
                $update_item['id'] = $record['id'];
                $update_data[] = $update_item;
            }
        }
        if (!empty($update_data)) {
            $sdb->table('salary_calculation_user')->updateBatch($update_data);
        }
        salaryCalculationModel::changeCalculationStatus($id);
        returnSuccess([], '审批成功');
    }

    // 删除薪资计算
    public function delete()
    {
        $paras_list = array('id');
        $param = arrangeParam($_POST, $paras_list);
        $id = $param['id'];
        if (!$id) returnError('id不能为空');
        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_calculation');
        $sdb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $detail = $sdb->one();
        if (empty($detail)) returnError('薪资计算不存在');

        // 待核算、已完成、已取消、已作废 ，不能作废
        if ($detail['status'] != salaryCalculationModel::STATUS_CANCEL) {
            returnError('只有取消状态才能删除');
        }

        $data = [
            'is_delete' => 1,
            'operator'  => userModel::$qwuser_id
        ];
        $sdb->table('salary_calculation');
        $sdb->where('where id = :id', ['id' => $id]);
        $sdb->update($data);
        returnSuccess([], '删除成功');
    }

    // 获取薪资计算详情
    public function getDetail()
    {
        $paras_list = array('id');
        $param = arrangeParam($_GET, $paras_list);
        $id = $param['id'];
        if (!$id) returnError('id不能为空');

        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_calculation');
        $sdb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $detail = $sdb->one();
        if (empty($detail)) returnError('薪资计算不存在');
        $detail['attach'] = $detail['attach'] ? json_decode($detail['attach'], true) : null;

        $statistic = [
            'no_salary'       => [],
            'no_insurance'    => [],
            'no_housing_fund' => [],
            'no_calculation'  => [],
        ];

        // 用户基本信息
        $userMap = $db->table('qwuser', 'u')
            ->field('u.id as user_id,u.wid, u.wname, ui.user_status, ui.salary_status')
            ->leftJoin('user_info', 'ui', 'u.id=ui.qwuser_id')
            ->list();
        $userMap = array_column($userMap, null, 'user_id');

        // 检查当前算薪周期，未加入算薪的用户
        $month_users = $sdb->table('salary_calculation_user', 'u')
            ->leftJoin('salary_calculation', 'sc', 'sc.id=u.calc_id')
            ->field('u.user_id')
            ->where('where sc.month = :month and sc.is_delete = 0', ['month' => $detail['month']])
            ->list();
        $month_users = array_column($month_users, 'user_id');

        // 查询当前算薪用户有无【未定薪】、【未缴社保】、【未缴公积金的人】，并显示人数，点击显示人名
        $users = $sdb->table('salary_calculation_user')
            ->field('user_id')
            ->where('where calc_id = :calc_id and is_delete = 0', ['calc_id' => $id])
            ->list();
        $user_ids = array_column($users, 'user_id');

        $user_insurance = [];
        $user_housing_fund = [];
        // 查询当前算薪用户的社保公积金
        if (!empty($user_ids)) {
            $social_security = $sdb->table('user_insurance_fund_tax', 'a')
                ->where('where a.month = :month and a.type in (1,2) and a.is_delete = 0', ['month' => $detail['month']])
                ->whereIn('a.qwuser_id', $user_ids)
                ->list();
            foreach ($social_security as $item) {
                if ($item['type'] == 1) {
                    $user_insurance[] = $item['qwuser_id'];
                } else {
                    $user_housing_fund[] = $item['qwuser_id'];
                }
            }
        }

        foreach ($userMap as $user_id => $user) {
            if (!empty($month_users) && !in_array($user_id, $month_users)) {
                $statistic['no_calculation'][] = $user;
            }
            if (!empty($user_ids)) {
                if (in_array($user_id, $user_ids)) {
                    if ($user['salary_status'] == 0) {
                        $statistic['no_salary'][] = $user;
                    }
                    if (!in_array($user_id, $user_insurance)) {
                        $statistic['no_insurance'][] = $user;
                    }
                    if (!in_array($user_id, $user_housing_fund)) {
                        $statistic['no_housing_fund'][] = $user;
                    }
                }
            }
        }

        $detail['statistic'] = $statistic;

        returnSuccess($detail);
    }


}