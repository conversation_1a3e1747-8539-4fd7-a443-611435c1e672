<?php

namespace plugins\checkin\controller;

use admin\models\qwdepartmentModel;
use admin\models\qwModel;
use plugins\checkin\form\checkinForm;
use plugins\checkin\form\messagesFrom;
use plugins\checkin\models\approvelModel;
use plugins\checkin\models\holidayModel;
use plugins\checkin\models\userModel;
use core\lib\db\dbCMysql;
use core\lib\db\dbMysql;
use core\lib\db\dbSMysql;
use Rap2hpoutre\FastExcel\FastExcel;
use plugins\salary\models\salaryCalculationModel;

class userCheckinDataController
{
    // 获取考勤月的用户（包括导入的离职用户）
    public function getUserList()
    {
        $paras_list = array('month');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        if (!$param['month']) returnError('考勤月份必填');

        $user_list = [];
        $cdb = dbCMysql::getInstance();
        $users = $cdb->table('checkin_user')->where('where month = :month', ['month' => $param['month']])->list();
        foreach ($users as $user) {
            $user['users'] = json_decode($user['users'], true);
            foreach ($user['users'] as $user_item) {
                $user_list[$user_item['wid']] = [
                    'wid' => $user_item['wid'],
                    'wname' => $user_item['name'],
                    'id' => $user_item['id'] ?? 0,
                ];
            }
        }
        $user_list = array_values($user_list);
        
        // 获取所有用户
        if (empty($user_list)) {
            $db = dbMysql::getInstance();
            $users = $db->table('qwuser')->field('id, wid, wname, position')->list();
            $user_list = array_merge($user_list, $users);
        }

        returnSuccess($user_list);

    }


    // 获取规则列表
    public function getList()
    {
        $paras_list = array('corp_id', 'month', 'day', 'corp_rule_id', 'users', 'page', 'page_size', 'corp_group_id', 'day');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        if (!$param['month']) returnError('考勤月份必填');
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        $cdb = dbCMysql::getInstance();
        $checkin_table = 'user_checkin_data_'.date('Y', strtotime($param['month']));
        $cdb->table($checkin_table);
        $cdb->where('where checkin_date >= :start_time and checkin_date <= :end_time', ['start_time' => date('Y-m-01', strtotime($param['month'])), 'end_time' => date('Y-m-t', strtotime($param['month']))]);
        $cdb->order('user_id asc, checkin_date asc');

        // 权限控制
        $user_id = userModel::$qwuser_id;
        $all_department_users = qwdepartmentModel::getUserDepartment(userModel::$wid)['all_department_users'] ?: []; // 当前用户管理的部门及其子部门下的用户
        $user_ids = array_column($all_department_users, 'wid');

        if (userModel::getUserListAuth('checkinDataAll')) {
            // 全部权限
        } elseif (userModel::getUserListAuth('checkinDataDepartment')) {
            if (!empty($all_department_users)) {
                $cdb->whereIn('user_id', $user_ids);
            }
        } elseif (userModel::getUserListAuth('checkinDataRelated')) {
            $cdb->andWhere('user_id = :user_id', ['user_id' => $user_id]);
        } elseif (userModel::getUserListAuth('checkinDataCorpRule')) {
            $auth_corp_group_id = [];
            $gid = [1, 2, 3, 4, 10, 11];
            foreach ($gid as $item) {
                if (userModel::getUserListAuth('checkinData_'.$item)) {
                    $auth_corp_group_id[] = $item;
                }
            }
            if (empty($auth_corp_group_id)) {
                returnError('无权限查看');
            }
            $auth_corp_group_id = implode(',', $auth_corp_group_id);
            $cdb->andWhere("JSON_EXTRACT(base_info, '$.rule_info.groupid') in ($auth_corp_group_id)");
        }

        // 打卡规则权限

        if (!empty($param['users'])) {
            $users = json_decode($param['users'], true);
            $cdb->whereIn('user_id', $users);
        }
        if (!empty($param['corp_group_id'])) {
            $corp_group_id = json_decode($param['corp_group_id'], true);
            $corp_group_id = array_map('intval', $corp_group_id);
            $corp_group_id_str = implode(',', $corp_group_id);
            $cdb->andWhere("JSON_EXTRACT(base_info, '$.rule_info.groupid') in ($corp_group_id_str)");
        }


        if (!empty($param['day'])) {
            $cdb->andWhere('checkin_date = :day', ['day' => date('Y-m-'.sprintf('%02d', $param['day']), strtotime($param['month']))]);
        }

        $list = $cdb->pages($page,$limit);
        
        $user_ids = array_column($list['list'], 'user_id');
        $db = dbMysql::getInstance();
        $users = $db->table('qwuser')->field('id,wid,wname,position')->whereIn('wid', $user_ids)->list();
        $userMap = array_column($users, null, 'wid');

        // 查询请假类型的审批单(查询3个月前的数据)
        $last_3_month = strtotime('-3 month', strtotime(date('Y-m-01', strtotime($param['month']))));
        $cdb->table('approvel')
            ->field('sp_no,qw_user_id,template_id, apply_data, apply_time')
            ->where('where apply_time >= :start_time and apply_time <= :end_time and sp_status = 2', ['start_time' => $last_3_month, 'end_time' => strtotime($param['month'].'-'.date('t', strtotime($param['month'])).' 23:59:59')]);
        $cdb->whereIn('qw_user_id', $user_ids);
        $approvel = $cdb->list();
        $format_data = approvelModel::formatApprovel($approvel);
        $user_approvel = $format_data['user_approvel'];
        $user_absence = $format_data['user_absence'];
        $approvel = $format_data['list'];
        $approvel = array_column($approvel, null, 'sp_no');

        // 格式化数据
        $ret_list = [];

        foreach ($list['list'] as $item) {
            $base_info = json_decode($item['base_info'], true);
            $holiday_infos = json_decode($item['holiday_infos'], true);
            // 把审批单的数据加到假勤相关信息里
            $user_day_absence = $user_absence[$item['user_id'].'-'.$item['checkin_date']] ?? [];
            $user_day_sp_absence = array_column($user_day_absence, null, 'sp_no');
            foreach ($holiday_infos as &$holiday) {
                if (isset($user_day_sp_absence[$holiday['sp_number']])) {
                    $holiday['sp_item'] = $user_day_sp_absence[$holiday['sp_number']];
                }
            }
            $ret_list[] = [
                'id' => $item['id'],
                'checkin_date' => $item['checkin_date'],
                'user_id' => $item['user_id'],
                'user_name' => $base_info['name'] ??  '',
                'departs_name' => $base_info['departs_name'] ?? '',
                'position' => $base_info['position'] ?? $userMap[$item['user_id']]['position'] ?? '',
                'rule_info' => $base_info['rule_info'] ?? [],
                'base_info' => $base_info,
                'summary_info' => json_decode($item['summary_info'], true),
                'holiday_infos' => $holiday_infos,
                'holiday_infos_new' => json_decode($item['holiday_infos_new'], true) ?? [],
                'exception_infos' => json_decode($item['exception_infos'], true),
                'ot_info' => json_decode($item['ot_info'], true),
                'ot_info_new' => json_decode($item['ot_info_new'], true) ?? [],
                'sp_items' => json_decode($item['sp_items'], true),
                'approvel' => $user_approvel[$item['user_id'].'-'.$item['checkin_date']] ?? null,
                'absence' => $user_day_absence,
            ];
        }
        $list['list'] = $ret_list;



        $last_sync = $cdb->table('sync_time')->where('where type = 3 and status = 1 and month=:month', ['month' => $param['month']])->order('finish_time desc')->one();
        $list['last_update_time'] = $last_sync['finish_time'] ?? '';
        
        returnSuccess($list);
    }

    // 校准
    public function calibrate()
    {
        $paras_list = array('id', 'month', 'earliest_time', 'lastest_time', 'exception_infos', 'vacation', 'ot_info_new');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        if (!$param['id']) returnError('ID必填');
        if (!isset($param['month'])) returnError('考勤周期必填');
        if (!isset($param['earliest_time'])) returnError('最早时间必填');
        if (!isset($param['lastest_time'])) returnError('最晚时间必填');
        $param['earliest_time'] = $param['earliest_time'] ? intval($param['earliest_time']) : 0;
        $param['lastest_time'] = $param['lastest_time'] ? intval($param['lastest_time']) : 0;
        if ($param['lastest_time'] != 0 && $param['earliest_time'] != 0 && $param['lastest_time'] < $param['earliest_time']) returnError('最晚时间不能小于最早时间');
        $checkin_table = 'user_checkin_data_'.date('Y', strtotime($param['month']));
        $checkin_data = dbCMysql::getInstance()->table($checkin_table)->where('where id = :id', ['id' => $param['id']])->one();
        if (empty($checkin_data)) returnError('数据不存在');


        $param['exception_infos'] = json_decode($param['exception_infos'], true) ?: [];
        $param['vacation'] = json_decode($param['vacation'], true) ?: [];
        $param['ot_info_new'] = json_decode($param['ot_info_new'], true) ?: [];

        // 修正用户考勤数据
        try {
            self::checkinDataCalibrate($param['month'], $checkin_data, $param['earliest_time'], $param['lastest_time'], $param['exception_infos'], $param['vacation'], $param['ot_info_new']);
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }


        returnSuccess([], '校准成功');

    }

    // 校准
    public function calibrateBatch()
    {
        $paras_list = array('ids', 'month', 'earliest_time', 'lastest_time', 'exception_infos');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        if (!$param['ids']) returnError('ID必填');
        if (!isset($param['month'])) returnError('考勤周期必填');
        if (!isset($param['earliest_time'])) returnError('最早时间必填');
        if (!isset($param['lastest_time'])) returnError('最晚时间必填');
        $param['earliest_time'] = $param['earliest_time'] ? intval($param['earliest_time']) : 0;
        $param['lastest_time'] = $param['lastest_time'] ? intval($param['lastest_time']) : 0;
        if ($param['lastest_time'] != 0 && $param['earliest_time'] != 0 && $param['lastest_time'] < $param['earliest_time']) returnError('最晚时间不能小于最早时间');
        $ids = json_decode($param['ids'], true);
        $checkin_table = 'user_checkin_data_'.date('Y', strtotime($param['month']));
        $checkin_data = dbCMysql::getInstance()->table($checkin_table)->whereIn('id', $ids)->list();

        if (empty($checkin_data) || count($checkin_data) != count($ids)) returnError('数据不存在');

        $param['exception_infos'] = json_decode($param['exception_infos'], true) ?: [];

        foreach ($checkin_data as $item) {
            // 修正用户考勤数据
            try {
                self::checkinDataCalibrate($param['month'], $item, $param['earliest_time'], $param['lastest_time'], $param['exception_infos']);
            } catch (\Exception $e) {
                returnError($e->getMessage());
            }
        }

        returnSuccess([], '批量校准成功');

    }

    
    // 修正用户考勤数据
    private static function checkinDataCalibrate($month, $checkin_data, $earliest_time, $lastest_time, $exception = [], $vacation = [], $ot_info_new = [])
    {
        // base_info 基本信息
        $base_info = json_decode($checkin_data['base_info'], true);
        // summary_info 汇总信息
        $summary_info = json_decode($checkin_data['summary_info'], true);
        // earliest_time 当日最早打卡时间
        $summary_info['earliest_time'] = $earliest_time;
        // lastest_time 当日最晚打卡时间
        $summary_info['lastest_time'] = $lastest_time;
        // checkin_count 当日打卡次数
        $summary_info['checkin_count'] = 0;
        if (!empty($earliest_time)) $summary_info['checkin_count'] = $summary_info['checkin_count'] + 1;
        if (!empty($lastest_time)) $summary_info['checkin_count'] = $summary_info['checkin_count'] + 1;
        // regular_work_sec 当日实际工作时长，单位：秒
        if (!empty($earliest_time) && !empty($lastest_time)) {
            $summary_info['regular_work_sec'] = self::calculate_work_time($base_info['rule_info']['checkintime'], $earliest_time, $lastest_time);
        } else {
            $summary_info['regular_work_sec'] = 0;
        }

        // holiday_infos 假勤相关信息

        // exception_infos 校准状态信息
        $calibrate_exception = [];
        $exception_infos = json_decode($checkin_data['exception_infos'], true);
        $is_absent = false;
        foreach ($exception as $item) {
            if (in_array($item['exception'], [1, 2])) { // 迟到早退
                $calibrate_exception[] = [
                    'exception' => $item['exception'],
                    'count' => 1,
                    'duration' => $item['duration'] ?? 0,
                ];
            } elseif ($item['exception'] == 3) { // 缺卡
                $calibrate_exception[] = [
                    'exception' => $item['exception'],
                    'count' => 1,
                ];
            } elseif ($item['exception'] == 4) { // 旷工
                $is_absent = true;
            }
        }
        if ($is_absent) {
            $calibrate_exception[] = [
                'exception' => 4,
                'count' => 1,
                'duration' => $summary_info['standard_work_sec'],
            ];
        }

        // 原本的异常信息
        foreach ($exception_infos as $item) {
            if ($item['exception'] > 4) $calibrate_exception[] = $item;
        }

        $calibrate_vacation = [];// 校准后假期的时长
        $total_calibrate_duration = 0;
        foreach ($vacation as $item) {
            if (!isset($calibrate_vacation[$item['vacation_id']])) $calibrate_vacation[$item['vacation_id']] = ['vacation_id' => $item['vacation_id'], 'duration' => 0, 'count' => 0];
            if ($item['duration'] == 0) continue; // 时长为0的不计数
            $calibrate_vacation[$item['vacation_id']]['duration'] += $item['duration'];
            $calibrate_vacation[$item['vacation_id']]['count'] += 1;
            $total_calibrate_duration += $item['duration'];
        }
        // sp_items 可能只处理请假类型的数据
        // type 类型：1-请假；2-补卡；3-出差；4-外出；15-审批打卡；100-外勤
        $sp_items = json_decode($checkin_data['sp_items'], true);
        // 校验后的假期总时长 不能 大于原时长
        $total_duration = 0;
        foreach ($sp_items as &$item) {
            if ($item['type'] == 1) {
                $total_duration += $item['duration'];
            }
        }
        if ($total_calibrate_duration > $total_duration) {
            throw new \Exception('校准后请假时长不能大于原时长');
        }

        foreach ($sp_items as &$item) {
            if ($item['type'] == 1) { // 因为不会提上来被删除的请假数据，所以不用考虑删除的情况
                $item['duration'] = $calibrate_vacation[$item['vacation_id']]['duration'] ?? 0;
                $item['count'] = $calibrate_vacation[$item['vacation_id']]['count'] ?? 0;
            }
        }

        $cdb = dbCMysql::getInstance();
        $checkin_table = 'user_checkin_data_'.date('Y', strtotime($month));
        $cdb->table($checkin_table)->where('id = :id', ['id' => $checkin_data['id']])->update([
            'summary_info' => json_encode($summary_info, JSON_UNESCAPED_UNICODE),
            'exception_infos' => json_encode($calibrate_exception, JSON_UNESCAPED_UNICODE),
            'sp_items' => json_encode($sp_items, JSON_UNESCAPED_UNICODE),
            'holiday_infos_new' => json_encode($vacation, JSON_UNESCAPED_UNICODE),
            'ot_info_new' => json_encode($ot_info_new, JSON_UNESCAPED_UNICODE),
        ]);

    }

    public function import()
    {
        $paras_list = array('excel_src', 'month');
        $request_list = ['excel_src' => '表格链接', 'month' => '考勤月份'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $excel_url = SELF_FK . $param['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格不存在');
        }

        $excel_data = (new FastExcel)->import($excel_url);
        $data = $excel_data->toArray();
        if (!count($data)) {
            returnError('表格内不存在数据');
        }

        // 表头
        $first_user = $data[0];
        if (empty($first_user['时间']) || empty($first_user['姓名']) || empty($first_user['账号']) || empty($first_user['部门']) || empty($first_user['职务']) ||
            empty($first_user['所属规则']) || empty($first_user['班次']) || empty($first_user['最早']) || empty($first_user['最晚']) || empty($first_user['打卡次数(次)']) ||
            empty($first_user['假勤申请']) || !isset($first_user['校准状态']))
        {
            returnError('表头错误');
        }

        $error_data = [];
        $res_data = [];
        $check_users = [];

        $db = dbMysql::getInstance();
        $db->table('qwdepartment')
            ->field('id,wp_id,name,department_leader,qw_parentid,sort');
        $deps = $db->list();
        $department_map = array_column($deps, null, 'name');

        $cdb = dbCMysql::getInstance();
        // 获取公司规则
        $corpRules = $cdb->table('corp_checkin_option')->list();
        $corpRules = array_column($corpRules, null, 'groupname');

        // 假期类型
        $vacation = $cdb->table('vacation')->field('id, name, time_attr')->list();
        $vacation = array_column($vacation, null, 'id');

        // 所有账号
        $users = array_column($data, '账号');

        // 已有考勤数据
        $start_time = date('Y-m-01', strtotime($param['month']));
        $end_time = date('Y-m-t', strtotime($param['month']));
        $checkin_table = 'user_checkin_data_'.date('Y', strtotime($param['month']));
        $checkin_data = $cdb->table($checkin_table)
        ->where('where checkin_date >= :start_time and checkin_date <= :end_time', ['start_time' => $start_time, 'end_time' => $end_time])
        ->whereIn('user_id', $users)->list();
        $user_checkin_data = [];

        foreach ($checkin_data as $item) {
            $user_checkin_data[$item['user_id']][$item['checkin_date']] = $item['id'];
        } 

        foreach ($data as $item) {
            $item['is_import'] = 1;
            $date = strtotime(explode(' ', $item['时间'])[0]);
            if ($date < strtotime($param['month'].'-01') || $date > strtotime($param['month'].'-'.date('t', strtotime($param['month'])))) {
                $item['失败原因'] = '考勤日期超出考勤月份';
                $error_data[] = $item;
                continue;
            }

            // 已存在考勤数据
            if (isset($user_checkin_data[$item['账号']][date('Y-m-d', $date)])) {
                $item['失败原因'] = '已存在考勤数据';
                $error_data[] = $item;
                continue;
            }

            $checkintime = [];
            $standard_work_sec = 0;
            if ($item['班次'] == '休息') {
                $earliest_time = 0;
                $lastest_time = 0;
            } else {
                $str_checkintime = explode('、', $item['班次']);
                foreach ($str_checkintime as $str) {
                    $item_time = explode('-', $str);
                    $checkintime[] = [
                        'work_sec' => strtotime($item_time[0]) - strtotime("00:00"),
                        'off_work_sec' => strtotime($item_time[1]) - strtotime("00:00"),
                    ];
                    $standard_work_sec += strtotime($item_time[1]) - strtotime($item_time[0]);
                }
                try {
                    $earliest_time = ($item['最早'] == '未打卡' || $item['最早'] == '--') ? 0 : strtotime($item['最早']) - strtotime("00:00");
                } catch (\Throwable $e) {
                    $item['失败原因'] = '最早打卡数据格式错误';
                    $error_data[] = $item;
                    continue;
                }
                try {
                    $lastest_time = ($item['最晚'] == '未打卡' || $item['最晚'] == '--') ? 0 : strtotime($item['最晚']) - strtotime("00:00");
                } catch (\Throwable $e) {
                    $item['失败原因'] = '最晚打卡数据格式错误';
                    $error_data[] = $item;
                    continue;
                }
            }
            $exception_infos = [];
            //校准状态
            if (strpos($item['校准状态'], '正常') !== false) { // 包含正常、休息、请假、补卡
                $exception_infos = [];
            }
            else {
                $exceptions = explode('、', $item['校准状态']);
                $exception_map = ['1' => '迟到', '早退', '缺卡', '旷工'];
                $exception_map = array_flip($exception_map);
                foreach ($exceptions as $exception) {
                    $exception_type = null;
                    foreach ($exception_map as $key => $value) {
                        if (strpos($exception, $key) !== false) {
                            $exception_type = $value;
                            break;
                        }
                    }
                    $duration = 0;
                    if (strpos($exception, '迟到') !== false || strpos($exception, '早退') !== false || strpos($exception, '旷工') !== false) {
                        preg_match('/\d+/', $exception, $matches);
                        $duration = $matches[0] * 60 ?? 0;
                    }
                    $exception_infos[] = [
                        'exception' => $exception_type,
                        'count' => 1,
                        'duration' => $duration,
                    ];
                    unset($matches);
                }
            }
            // 假勤申请
            $holiday_infos = [];
            $sp_items = [];
            $vacation_item = [];
            foreach ($vacation as $vac) {
                $vacation_item[$vac['id']] = [
                    'type' => 1,
                    'vacation_id' => $vac['id'],
                    'duration' => 0,
                    'count' => 0,
                    'name' => $vac['name'],
                    'time_type' => $vac['time_attr'],
                ];
            }
            if ($item['假勤申请'] != '--') {
                $str_holiday_infos = explode('、', $item['假勤申请']);
                foreach ($str_holiday_infos as $str) {
                    preg_match('/（([^（）]+)）$/u', $str, $matches);
                    $title = preg_replace('/（[^（）]+）$/u', '', $str);
                    preg_match('/\d+(\.\d+)?/', $title, $matches_duration);
                    $number = $matches_duration[0];
                    $duration = 0;
                    $vacation_id = 0;
                    $vacation_name = '';
                    if (strpos($title, '天') !== false) {
                        $duration = $number * 24 * 60 * 60;
                    } elseif (strpos($title, '小时') !== false) {
                        $duration = $number * 60 * 60;
                    }
                    // 匹配假期
                    foreach ($vacation as $vac) {
                        if (strpos($title, $vac['name']) !== false) {
                            if (!isset($vacation_item[$vac['id']])) {
                                $vacation_item[$vac['id']] = [
                                    'duration' => 0,
                                    'count' => 0,
                                ];
                            }
                            $vacation_item[$vac['id']]['duration'] += $duration;
                            $vacation_item[$vac['id']]['count'] ++;
                            $vacation_id = $vac['id'];
                            $vacation_name = $vac['name'];
                        }
                    }


                    // 构造虚拟审批单
                    $sp_item = [
                        'absence_duration' => $duration,
                        'vacation_id' => $vacation_id,
                        'vacation_name' => $vacation_name,
                    ];


                    $holiday_infos[] = [
                        'sp_number' => 'import',
                        'sp_title' => ['data' => [['text' => $title, 'lang' => 'zh_CN']]],
                        'sp_description' => ['data' => [['text' => $matches[1], 'lang' => 'zh_CN']]],
                        'sp_item' => $sp_item,
                    ];
                    unset($matches);
                }

                foreach ($vacation_item as $key => $value) {
                    $sp_items[] = [
                        'type' => 1,
                        'vacation_id' => $key,
                        'duration' => $value['duration'],
                        'count' => $value['count'],
                        'name' => $vacation[$key]['name'],
                        'time_type' => $vacation[$key]['time_attr'],
                    ];
                }
            }

            $base_info = [
                'date' => $date,
                'name' => $item['姓名'],
                'departs_name' => $item['部门'],
                'position' => $item['职务'],
                'acctid' => $item['账号'],
                'rule_info' => [
                    'groupid' => $corpRules[$item['所属规则']]['groupid'],
                    'groupname' => $item['所属规则'],
                    'checkintime' => $checkintime,
                ]
            ];
            $summary_info = [
                'checkin_count' => $item['打卡次数(次)'] == '--' ? 0 : $item['打卡次数(次)'],
                'earliest_time' => $earliest_time,
                'lastest_time' => $lastest_time,
                'regular_work_sec' => self::calculate_work_time($checkintime, $earliest_time, $lastest_time),
                'standard_work_sec' => $standard_work_sec,
            ];

            $insert_data = [
                'checkin_date' => date('Y-m-d', $date),
                'user_id' => $item['账号'],
                'base_info' => json_encode($base_info, JSON_UNESCAPED_UNICODE),
                'summary_info' => json_encode($summary_info, JSON_UNESCAPED_UNICODE),
                'holiday_infos' => $holiday_infos ? json_encode($holiday_infos, JSON_UNESCAPED_UNICODE) : '[]',
                'exception_infos' => $exception_infos ? json_encode($exception_infos, JSON_UNESCAPED_UNICODE) : '[]',
                'ot_info' => '{"ot_status":0,"ot_duration":0,"exception_duration":[]}',
                'sp_items' => $sp_items ? json_encode($sp_items, JSON_UNESCAPED_UNICODE) : '[]',
            ];
            // 记录原始数据
            $ori_data = $insert_data;
            $ori_data['import_data'] = $item;
            $insert_data['ori_data'] = json_encode([
                'checkin_date' => date('Y-m-d', $date),
                'user_id' => $item['账号'],
                'base_info' => $base_info,
                'summary_info' => $summary_info,
                'holiday_infos' => $holiday_infos ?: [],
                'exception_infos' => $exception_infos ?: [],
                'ot_info' => ["ot_status" => 0, "ot_duration" => 0, "exception_duration" => []],
                'sp_items' => $sp_items ?: [],
            ], JSON_UNESCAPED_UNICODE);
            $cdb->table($checkin_table)->insert($insert_data);
            if (!isset($check_users[$item['账号']])) {
                $departments = explode(';', $item['部门']);
                $department_ids = [];
                if (!empty($departments)) {
                    foreach ($departments as $department) {
                        $department_arr = explode('/', $department);
                        $department_name = end($department_arr);
                        $department_ids[] = $department_map[$department_name]['id'] ?? 0;
                    }
                }
                $check_users[$item['账号']] = [
                    'wid' => $item['账号'],
                    'name' => $item['姓名'],
                    'departs_name' => $item['部门'],
                    'departs_ids' => json_encode($department_ids),
                    'position' => $item['职务'],
                    'id' => $ywx_users[$item['账号']]['id'] ?? 0,
                ];
            }
        }

        // 记录考勤导入的用户
        if (!empty($check_users)) {
            $check_users = array_values($check_users);
            $cdb->table('checkin_user')->insert([
                'users' => json_encode($check_users, JSON_UNESCAPED_UNICODE),
                'month' => $param['month'],
                'create_time' => date('Y-m-d H:i:s'),
            ]);
        }


        if (count($error_data) > 0) {
            // 使用项目根目录动态构建导出文件路径
            $src = '/public/checkin/temp/error/user_checkin_import_errors'.time().rand(100,1000).'.xlsx';
            $exportPath = SELF_FK . $src;

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径

            returnSuccess(['error_file' => $src, 'error_count' => count($error_data), 'success_count' => count($res_data), 'data' => $error_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => count($error_data), 'success_count' => count($res_data), 'data' => $res_data], '导入成功');
        }


    }

    public function revertData()
    {
        $paras_list = array('id', 'month');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        empty($param['id']) && returnError('id必填');
        empty($param['month']) && returnError('考勤周期必填');

        $cdb = dbCMysql::getInstance();
        $checkin_table = 'user_checkin_data_'.date('Y', strtotime($param['month']));
        $checkin_data = $cdb->table($checkin_table)->where('where id = :id', ['id' => $param['id']])->one();
        if (empty($checkin_data)) returnError('数据不存在');


        $ori_data = json_decode($checkin_data['ori_data'], true);
        
        $checkin_table = 'user_checkin_data_'.date('Y', strtotime($param['month']));
        $cdb->table($checkin_table)->where('id = :id', ['id' => $param['id']])->update([
            'base_info' => json_encode($ori_data['base_info'], JSON_UNESCAPED_UNICODE),
            'summary_info' => json_encode($ori_data['summary_info'], JSON_UNESCAPED_UNICODE),
            'holiday_infos' => json_encode($ori_data['holiday_infos'], JSON_UNESCAPED_UNICODE),
            'holiday_infos_new' => null,
            'exception_infos' => json_encode($ori_data['exception_infos'], JSON_UNESCAPED_UNICODE),
            'ot_info' => json_encode($ori_data['ot_info'], JSON_UNESCAPED_UNICODE),
            'ot_info_new' => null,
            'sp_items' => json_encode($ori_data['sp_items'], JSON_UNESCAPED_UNICODE),
        ]);
        returnSuccess([], '还原成功');
    }

    public function syncUserDayData()
    {
        $paras_list = array('month');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        empty($param['month']) && returnError('考勤周期必填');
        $start_time = date('Y-m-01', strtotime($param['month']));
        $end_time = date('Y-m-t', strtotime($param['month']));

        // 查询是否有当月的算薪
        $calculation = salaryCalculationModel::getCalculationByMonth($param['month']);
        if ($calculation) returnError('已进行算薪的不可拉取');

        $cdb = dbCMysql::getInstance();
        $page = 1;
        $checkin_table = 'user_checkin_data_'.date('Y', strtotime($param['month']));
        $check = $cdb->table($checkin_table)->where('where checkin_date >= :start_time and checkin_date <= :end_time',
            ['start_time' => date('Y-m-01', strtotime($param['month'])), 'end_time' => date('Y-m-t', strtotime($param['month']))])
            ->one();
        if (!$check) self::syncUserDayDataOld();

        while (true) {
            $cdb->table($checkin_table);
            $cdb->where('where checkin_date >= :start_time and checkin_date <= :end_time', ['start_time' => date('Y-m-01', strtotime($param['month'])), 'end_time' => date('Y-m-t', strtotime($param['month']))]);
            $list = $cdb->pages($page, 100);
            $update_data = [];
            foreach ($list['list'] as $item) {
                if (!$item) continue;
                $ori_data = json_decode($item['ori_data'], true);
                $update_data[] = [
                    'id' => $item['id'],
                    'base_info' => json_encode($ori_data['base_info'], JSON_UNESCAPED_UNICODE),
                    'summary_info' => json_encode($ori_data['summary_info'], JSON_UNESCAPED_UNICODE),
                    'holiday_infos' => json_encode($ori_data['holiday_infos'], JSON_UNESCAPED_UNICODE),
                    'holiday_infos_new' => '',
                    'exception_infos' => json_encode($ori_data['exception_infos'], JSON_UNESCAPED_UNICODE),
                    'ot_info' => json_encode($ori_data['ot_info'], JSON_UNESCAPED_UNICODE),
                    'ot_info_new' => '',
                    'sp_items' => json_encode($ori_data['sp_items'], JSON_UNESCAPED_UNICODE),
                ];
            }
            if ($update_data) $cdb->table($checkin_table)->updateBatch($update_data);
            if (intval($page) * 500 >= intval($list['total'])) {
                break;
            }
            $page++;
        }
        // 更新同步时间
        $cdb->table('sync_time')->insert([
            'type' => 3,
            'status' => 1,
            'month' => $param['month'],
            'finish_time' => date('Y-m-d H:i:s'),
        ]);
        returnSuccess([], '同步成功');


    }


    public static function syncUserDayDataOld()
    {
        $paras_list = array('month');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        empty($param['month']) && returnError('考勤周期必填');
        $start_time = date('Y-m-01', strtotime($param['month']));
        $end_time = date('Y-m-t', strtotime($param['month']));
        $qwModel = new qwModel();
        $cdb = dbCMysql::getInstance();
        $users = corpCheckinOptionController::getAllUserInRule();
        $checkin_table = 'user_checkin_data_'.date('Y', strtotime($param['month']));

        $error_data = [];
        foreach ($users as $user) {
            try {
                $user_day_data = $qwModel->getUserCheckinDayData([$user['wid']], strtotime($start_time), strtotime($end_time));
            } catch (\Exception $e) {
                $error_data = [
                    'wid' => $user['wid'],
                    'msg' => $e->getMessage(),
                ];
                continue;
            }
            if (!$user_day_data) {
                $error_data[] = $user['wid'];
                continue;
            }
            // 查询用户考勤周期内的数据
            $user_data = $cdb->table($checkin_table)->where('checkin_date >= :start_time and checkin_date <= :end_time and user_id = :user_id', ['start_time' => $start_time, 'end_time' => $end_time, 'user_id' => $user['id']])->list();
            $user_data = array_column($user_data, null, 'checkin_date');

            foreach ($user_day_data as $item) {
                $checkin_date = date('Y-m-d', $item['base_info']['date']);
                if (array_key_exists($checkin_date, $user_data)) {
                    $cdb->table($checkin_table)->where('id = :id', ['id' => $user_data[$checkin_date]['id']])->update([
                        'base_info' => json_encode($item['base_info'], JSON_UNESCAPED_UNICODE),
                        'summary_info' => json_encode($item['summary_info'], JSON_UNESCAPED_UNICODE),
                        'holiday_infos' => json_encode($item['holiday_infos'], JSON_UNESCAPED_UNICODE),
                        'exception_infos' => json_encode($item['exception_infos'], JSON_UNESCAPED_UNICODE),
                        'ot_info' => json_encode($item['ot_info'], JSON_UNESCAPED_UNICODE),
                        'sp_items' => json_encode($item['sp_items'], JSON_UNESCAPED_UNICODE),
                        'ori_data' => json_encode($item, JSON_UNESCAPED_UNICODE),
                        'update_time' => date('Y-m-d H:i:s'),
                    ]);
                } else {
                    $cdb->table($checkin_table)->insert([
                        'checkin_date' => date('Y-m-d', $item['base_info']['date']),
                        'user_id' => $item['base_info']['acctid'],
                        'base_info' => json_encode($item['base_info'], JSON_UNESCAPED_UNICODE),
                        'summary_info' => json_encode($item['summary_info'], JSON_UNESCAPED_UNICODE),
                        'holiday_infos' => json_encode($item['holiday_infos'], JSON_UNESCAPED_UNICODE),
                        'exception_infos' => json_encode($item['exception_infos'], JSON_UNESCAPED_UNICODE),
                        'ot_info' => json_encode($item['ot_info'], JSON_UNESCAPED_UNICODE),
                        'sp_items' => json_encode($item['sp_items'], JSON_UNESCAPED_UNICODE),
                        'ori_data' => json_encode($item, JSON_UNESCAPED_UNICODE),
                    ]);
                }
            }
        }
        returnSuccess(['err' => $error_data], '同步成功');
    }

    // 考勤汇总
    public function statistic()
    {
        $paras_list = array('corp_id', 'month', 'day', 'corp_rule_id', 'users', 'page', 'page_size', 'corp_group_id', 'day');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        if (!$param['month']) returnError('考勤月份必填');
        self::summary($param['month']);
        returnSuccess([], '统计成功');

    }


    // 按人汇总单月考勤数据
    public function summary($month)
    {
        if (!$month) returnError('考勤月份必填');
        $start_time = date('Y-m-01', strtotime($month));
        $end_time = date('Y-m-t', strtotime($month));
        $db = dbMysql::getInstance();
        $cdb = dbCMysql::getInstance();
        $sdb = dbSMysql::getInstance();

        // 查询是否有当月的算薪
        $calculation = salaryCalculationModel::getCalculationByMonth($month);
        if ($calculation) returnError('已进行算薪的不可汇总');

        // 获取所有用户
        $users = $db->table('qwuser')->field('id, wname, wdepartment_ids,wid, position')->list();
        $userMap = array_column($users, null, 'wid');
        $user_ids = array_column($users, 'id');

        // 获取节假日配置
        $holidayOption = holidayModel::getHolidayByTimeRange($start_time, $end_time);
        
        // 获取假期数据
        $vacation = $cdb->table('vacation')->field('id,name,time_attr,duration_type,perday_duration')->list();
        $vacation = array_column($vacation, null, 'id');
        
        // 所有的企业打卡规则
        $corpRules = $cdb->table('corp_checkin_option')->field('id, groupid,groupname, spe_workdays, spe_offdays, rule, range_info, white_users')->list();
        $corpRules = array_column($corpRules, null, 'groupid');

        $rule_id = [];
        foreach ($corpRules as &$corp_rule) {
            $corp_rule['rule'] = json_decode($corp_rule['rule'], true);
            $corp_rule['full_days'] = 0;
            // 构造当月的日期数组
            $days = [];
            for ($i = 1; $i <= date('t', strtotime($month)); $i++) {
                $checkin_date = date('Y-m-'.sprintf('%02d', $i), strtotime($month));
                $is_work = checkinForm::isWorkDay($checkin_date, $corp_rule, $holidayOption); // 0 休息 1 工作日 2 节假日
                $days[$checkin_date] = ['is_work' => $is_work];
                if ($is_work == 1) {
                    $corp_rule['full_days']++;
                }
            }
            $corp_rule['days'] = $days;

            // 获取扣款规则
            if (isset($corp_rule['rule']['rule_1'])) $rule_id[] = $corp_rule['rule']['rule_1'];
            if (isset($corp_rule['rule']['rule_2'])) $rule_id[] = $corp_rule['rule']['rule_2'];
            if (isset($corp_rule['rule']['rule_3'])) $rule_id[] = $corp_rule['rule']['rule_3'];
            if (isset($corp_rule['rule']['rule_4'])) $rule_id[] = $corp_rule['rule']['rule_4'];
            if (isset($corp_rule['rule']['rule_5'])) $rule_id[] = $corp_rule['rule']['rule_5'];
            if (isset($corp_rule['rule']['rule_6'])) $rule_id[] = $corp_rule['rule']['rule_6'];

        }
        $ruleMap = $cdb->table('rule')->field('id,rule_name, rule_type,attach')->whereIn('id', $rule_id)->where('where is_delete = 0 and status = 1')->list();
        foreach ($ruleMap as &$r) {
            $r['attach'] = json_decode($r['attach'], true);
        }
        $ruleMap = array_column($ruleMap, null, 'id');


        // 取出当月的所有用户
        $user_list = [];
        $users = $cdb->table('checkin_user')->where('where month = :month', ['month' => $month])->list();
        foreach ($users as $user) {
            $user['users'] = json_decode($user['users'], true);
            $user_list = array_merge($user_list, $user['users']);
        }
        $wids = array_values(array_unique(array_column($user_list, 'wid')));
        $user_list = array_column($user_list, null, 'wid');
       
        // 用户基本信息
        $user_info_data = $db->table('user_info')->whereIn('wid', $wids)->list();
        $user_info = array_column($user_info_data, null, 'wid');

        // 用户薪资信息
        $salary_list = $sdb->table('user_salary')
            ->where('where is_delete = 0 and effective_date < :effective_date', ['effective_date' => $month.'-25'])
            ->whereIn('qwuser_id', $user_ids)
            ->order('effective_date DESC')
            ->list();
        $user_salary = [];
        foreach ($salary_list as $item) {
            $salary = json_decode($item['salary'], true);
            $total_salary = 0;
            foreach ($salary as $key => $value) {
                $total_salary += $value;
            }
            $salary['total_salary'] = $total_salary;
            if (isset($user_salary[$item['qwuser_id']])) continue;
            $user_salary[$item['qwuser_id']] = $salary;
        }

        // 用户汇总数据
        $user_summary_data = $cdb->table('user_checkin_summary')->where('where summary_month = :summary_month', ['summary_month' => $month])->list();
        $user_summary = array_column($user_summary_data, null, 'user_id');

        foreach ($wids as $wid) {
            $user = $user_list[$wid];
            $user_id = $userMap[$wid]['id'] ?? 0;
            $user['wdepartment_ids'] = $user['departs_ids'] ? : $userMap[$wid]['wdepartment_ids'] ?? '';
            $user['wname'] = $user_list[$wid]['name'] ?? '';
            $user['position'] = $user_list[$wid]['position'] ?? '';
            $user['base_salary'] = $user_salary[$user_id]['base_salary'] ?? 0;
            $user['total_salary'] = $user_salary[$user_id]['total_salary'] ?? 0;
            $user['day_salary'] = $user_salary[$user_id]['day_salary'] ?? 0; // 这个可能会有，但也有可能会没有
            $user['hire_date'] = $user_info[$wid]['hire_date'] ?? '';

            $userSummary = checkinForm::userCheckSummary($user, $start_time, $end_time, $corpRules, $ruleMap, $vacation, $userMap);
            if (empty($userSummary)) continue;
            $userSummary['summary_month'] = $month;
            if (isset($user_summary[$user['wid']])) {
                // 将已确认、已拒绝的数据更新为未确认
                if ($user_summary[$user['wid']]['is_confirm'] == 1 || $user_summary[$user['wid']]['is_confirm'] == 2) {
                    $userSummary['is_confirm'] = null;
                    $userSummary['confirm_create_user_id'] = userModel::$wid;
                    $userSummary['confirm_time'] = null;
                    $company_name = '深圳易威行科技创新有限公司';
                    $user_name = userModel::$wname;
                    messagesFrom::senMeg([$user['wid']], 1, "{$user_name}已将{$company_name}_{$month}考勤】重新汇总，请您重新确认考勤", $user_summary[$user['wid']]['id']);
                }
                $cdb->table('user_checkin_summary')->where('id = :id', ['id' => $user_summary[$user['wid']]['id']])->update($userSummary);
            } else {
                $cdb->table('user_checkin_summary')->insert($userSummary);
            }
        }


    }

    // 计算实际工作时长
    private static function calculate_work_time($work_periods, $start_time, $end_time) {
        $total_work_time = 0;

        foreach ($work_periods as $period) {
            $work_start = $period['work_sec'];
            $work_end = $period['off_work_sec'];

            // 检查重叠
            if ($start_time < $work_end && $end_time > $work_start) {
                $overlap_start = max($start_time, $work_start);
                $overlap_end = min($end_time, $work_end);
                $total_work_time += ($overlap_end - $overlap_start);
            }
        }

        return $total_work_time;
    }

}