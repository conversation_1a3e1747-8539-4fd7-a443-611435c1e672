<?php

namespace plugins\assessment\controller;

use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\db\dbAMysql;
use plugins\assessment\models\assessmentModel;
use plugins\assessment\models\assessmentSchemesModel;
use plugins\assessment\models\assessmentTargetsModel;

class assessmentTargetsController
{
    // 获取考核指标列表
    public function getAssessmentTargetsList()
    {
        $paras_list = array('target_name', 'target_type', 'page', 'page_size');
        $param = arrangeParam($_POST, $paras_list);
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        if (isset($_POST['status'])) $param['status'] = $_POST['status'];
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_targets');
        $adb->where('where is_delete = 0');
        if ($param['target_name']) {
            $adb->andWhere('target_name like :target_name', ['target_name' => '%' . $param['target_name'] . '%']);
        }
        if ($param['target_type']) {
            $adb->andWhere('target_type = :target_type', ['target_type' => $param['target_type']]);
        }
        if (isset($param['status'])) {
            $adb->andWhere('status = :status', ['status' => $param['status']]);
        }
        $adb->order('id desc');
        $data = $adb->pages($page, $limit);

        // 业绩指标
        $assessment_targets_source = config::get('assessment_targets_source','data_assessment');
        $assessment_targets_source = array_column($assessment_targets_source, 'list', 'id');
        $columns = $assessment_targets_source['1'];
        $columns_map = array_column($columns, 'column_name', 'id');

        foreach ($data['list'] as &$item) {
            $item['target_detail'] = json_decode($item['target_detail'], true);
            $item['target_text'] = assessmentTargetsModel::getTargetText($item['target_detail'], $item['target_type'], $columns_map);
        }

        returnSuccess($data);
    }

    // 新增/编辑考核指标
    public function addAssessmentTargets()
    {
        $paras_list = array('id', 'target_name', 'status', 'target_type', 'assessment_standard', 'target_detail');
        $length_data = ['target_name' => ['name' => '指标名称', 'length' => 20], 'assessment_standard' => ['name' => '考核标准', 'length' => 1000]];
        $request_list = ['target_name' => '指标名称', 'status' => '是否启用', 'target_type' => '指标类型', 'assessment_standard' => '考核标准', 'target_detail' => '指标明细'];
        $param = arrangeParam($_POST, $paras_list, $request_list, $length_data);

        $adb = dbAMysql::getInstance();
        $adb->table('assessment_targets');

        // 判断考核指标是否存在
        if ($param['id']) {
            $adb->where('where id = :id and is_delete = 0', ['id' => $param['id']]);
            $target = $adb->one();
            if (!$target) returnError('指标不存在');

            // 是否有方案引用该指标
            if ($target['status'] != $param['status'] && in_array($param['id'], assessmentSchemesModel::getSchemeDetail('target'))) {
                returnError('有方案在使用该指标，不允许修改启用状态');
            }

            // 判断是否有进行中的绩效考核在使用该指标
            if (in_array($param['id'], assessmentModel::getRunningAssessmentDetail('target'))) {
                returnError('有进行中的绩效考核在使用该指标，不允许修改');
            }
        }

        // 判断考核指标名称是否重复
        $adb->table('assessment_targets');
        $adb->where('where target_name = :target_name and is_delete = 0', ['target_name' => $param['target_name']]);
        if ($param['id']) {
            $adb->andWhere('id != :id', ['id' => $param['id']]);
        }
        $is_exist = $adb->one();
        if ($is_exist) returnError('指标名称已存在');

        // 指标内容校验
        $target_detail = json_decode($param['target_detail'], true);
        try {
            assessmentTargetsModel::checkTargetDetail($target_detail, $param['target_type']);
        } catch (\Throwable $error) {
            returnError($error->getMessage());
        }

        $data = [
            'target_name'         => $param['target_name'],
            'status'              => $param['status'],
            'target_type'         => $param['target_type'],
            'target_detail'       => $param['target_detail'],
            'assessment_standard' => $param['assessment_standard']
        ];
        if ($param['id']) {
            $adb->table('assessment_targets');
            $adb->where('where id = :id and is_delete = 0', ['id' => $param['id']]);
            $res = $adb->update($data);
        } else {
            $res = $adb->insert($data);
            if (!$res) {
                returnError('fail');
            }
        }
        returnSuccess([], 'success');
    }

    // 删除考核指标
    public function delAssessmentTargets()
    {
        $id = $_POST['id'];
        if (!$id) returnError('id不能为空');
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_targets');
        $adb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $is_exist = $adb->one();
        if (!$is_exist) returnError('指标不存在');

        // 是否有方案引用该指标
        if (in_array($id, assessmentSchemesModel::getSchemeDetail('target'))) {
            returnError('有方案在使用该指标，不允许删除');
        }

        $data = [
            'is_delete' => 1,
        ];
        // 判断是否有进行中的绩效考核在使用该指标
        if (in_array($id, assessmentModel::getRunningAssessmentDetail('target'))) {
            returnError('有进行中的绩效考核在使用该指标，不允许删除');
        }
        $adb->table('assessment_targets');
        $adb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $res = $adb->update($data);
        if ($res) {
            returnSuccess([], 'success');
        } else {
            returnError('fail');
        }
    }

    // 获取考核指标详情
    public function getAssessmentTargetsDetail()
    {
        $id = $_POST['id'];
        if (!$id) returnError('id不能为空');
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_targets');
        $adb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $detail = $adb->one();
        if (!$detail) returnError('指标不存在');

        $detail['target_detail'] = $detail['target_detail'] ? json_decode($detail['target_detail'], true) : null;
        $detail['target_text'] = assessmentTargetsModel::getTargetText($detail['target_detail'], $detail['target_type']);

        returnSuccess($detail);
    }

}