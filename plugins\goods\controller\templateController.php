<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/14 16:10
 */

namespace  plugins\goods\controller;

use plugins\goods\form\templateFrom;
use plugins\goods\models\userModel;
use core\lib\db\dbMysql;
use function Symfony\Component\Translation\t;

class templateController
{
    public function getList(){
        $paras_list = array('flow_path_id', 'tpl_name','page','page_size', 'status', 'order_by');
        $param = arrangeParam($_GET, $paras_list);
        $where_sql = 'where is_delete = 0';
        $where_array = [];
        $flow_path_id = (int)$param['flow_path_id'];
        if ($flow_path_id > 0) {
            $where_sql .= ' and flow_path_id=:flow_path_id';
            $where_array['flow_path_id'] = $param['flow_path_id'];
        }
        if (!empty($param['tpl_name'])) {
            $where_sql .= ' and tpl_name like :tpl_name';
            $where_array['tpl_name'] = '%'.$param['tpl_name'].'%';
        }
        if ((int)$param['status'] > -1) {
            $where_sql .= ' and status = :status';
            $where_array['status'] = $param['status'];
        }
        $db = dbMysql::getInstance();
        $db->table('template');
        $db->field('id,flow_path_id,tpl_name,created_at,updated_at,status,expected_day,description');

        $order_str = '';
        if (!empty($param['order_by'])) {
            $order_by = json_decode($param['order_by'],true);
            foreach ($order_by as $k=>$ord){
                $order_str .= ''.$k.' '.$ord.',';
            }
        }
        if (empty($order_str)) {
            $db->order('id desc');
        } else {
            $db->order(trim($order_str,','));
        }
        $db->where($where_sql, $where_array);
        $list = $db->pages($param['page'], $param['page_size']);
        //整理模板信息
        returnSuccess($list);
    }

    public function editTemplate(){
        $paras_list = array('id', 'flow_path_id', 'tpl_name', 'node_list', 'status', 'expected_day', 'batch', 'description');
        $length_data = ['tpl_name'=>['name'=>'模板名称','length'=>20],'description'=>['name'=>'描述','length'=>225]];
        $param = arrangeParam($_POST, $paras_list,[],$length_data);
        $flow_path_id = (int)$param['flow_path_id'];
        //整理获取节点数据
        $node_list = json_decode($param['node_list'],true);
        if (!count($node_list)) {
            SetReturn(-1,'请选择节点');
        }
        $node_ids = [];
        foreach ($node_list as $v1) {
            if (!count($v1)) {
                SetReturn(-1,'模板数据有误，请重新设置节点');
            }
            foreach ($v1 as $v2) {
                $node_ids[] = $v2['id'];
            }
        }
        $tpl_data = templateFrom::getTplData($node_list,$node_ids);
        $db = dbMysql::getInstance();
        $db->table('template');
        $data = [
            'flow_path_id'=>$flow_path_id,
            'expected_day'=>(int)$param['expected_day'],
            'batch'=>$flow_path_id==1?(int)$param['batch']:1,
            'tpl_name'=>$param['tpl_name'],
            'tpl_data'=>json_encode($tpl_data,JSON_UNESCAPED_UNICODE),
            'status'=>(int)$param['status'],
            'description'=>$param['description'],
        ];

        $id = (int)$param['id'];
        $tpl = $db->query('select id from oa_template where id=:id',['id'=>$id]);
        if ($tpl) {
            $data['updated_at'] = date('Y-m-d H:i:s');
            $db->where('where id='.$id, []);
            if($db->update($data)) {
                if ($data['status'] == 1) {
                    templateFrom::setTplStatus($id,$param['flow_path_id'],(int)$param['batch']);
                }
                SetReturn(0, '修改成功');
            } else {
                SetReturn(0, '修改失败');
            }
        } else {
            $data['user_id'] = userModel::$qwuser_id;
            $data['created_at'] = date('Y-m-d H:i:s');
            $db->where('', []);
            $id = $db->insert($data);
            if($id) {
                if ($data['status'] == 1) {
                    templateFrom::setTplStatus($id,$param['flow_path_id'],(int)$param['batch']);
                }
                SetReturn(0, '添加成功');
            } else {
                SetReturn(0, '添加失败');
            }
        }
    }

    public function getDtail() {
        $id = (int)$_GET['id'];
        if (!$id) {
            SetReturn(-1, '参数有有误');
        }
        $db = dbMysql::getInstance();
        $db->table('template');
        $db->field('id,flow_path_id,batch,description,tpl_name,expected_day,status,tpl_data');
        $tpl = $db->where('where id = :id', ['id'=>$id])->one();

        $tpl_data = json_decode($tpl['tpl_data'],true);
        $new_tpl_data = [];
        foreach ($tpl_data as $node_) {
            $event_item = [];
            foreach ($node_ as $node) {
                if ($node['id'] == 0) {
                    continue;
                }
                $event_item[] = [
                    'id'=>$node['id'],
                    'node_name'=>$node['node_name']
                ];
            }
            if (count($event_item)) {
                $new_tpl_data[] = $event_item;
            }
        }
        unset($tpl['tpl_data']);
        $tpl['node_list'] = $new_tpl_data;
        returnSuccess(['data'=>$tpl]);
    }

    public function setStatus() {
        $paras_list = array('ids','status');
        $param = arrangeParam($_POST, $paras_list);
        $ids = json_decode($param['ids']);
        if (count($ids) > 1) {
            SetReturn(-1,'批量操作暂不可用');
        }
        $status = (int)$param['status'];
        if (empty($param['ids'])) {
            SetReturn(-1,'请选择要操作的数据');
        }
//        if (empty($param['status'])) {
//            SetReturn(-1,'暂不可直接关闭模板');
//        }
        //查询需要绑定的模板
        $db = dbMysql::getInstance();
        //查询模板
        $tpl = $db->table('template')
            ->where('where id =:id',['id'=>$ids[0]])
            ->one();
        if ($status == 0) {
            //抽货样可直接关闭关闭
            if ($tpl['flow_path_id'] == 3) {
                $db->table('template')
                    ->where('where id=:id',['id'=>$tpl['id']])
                    ->update(['status'=>0]);
            } else {
                //测试样和出货样不可直接关闭
                SetReturn(-1,'测试样和出货样模板不可直接关闭');
            }
        } else {
            if ($tpl['flow_path_id'] != 3) {
                //测试样和出货样   -关闭其他
                $db->table('template')
                    ->where('where flow_path_id=:flow_path_id and batch=:batch',['batch'=>$tpl['batch'],'flow_path_id'=>$tpl['flow_path_id']])
                    ->update(['status'=>0]);

            }
            //开启当前
            $db->table('template')
                ->where('where id=:id',['id'=>$tpl['id']])
                ->update(['status'=>1]);
        }
        //启用它
        returnSuccess(0,'设置成功');
    }


}