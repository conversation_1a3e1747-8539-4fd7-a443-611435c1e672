openapi: 3.0.0
info:
  title: 收款账户管理API
  version: 1.0.0
  description: 提供收款账户管理的相关接口
paths:
  /shop/receiveAccount/getList:
    get:
      tags:
        - ReceiveAccount
      summary: 获取收款账户列表
      description: 根据条件筛选获取收款账户列表
      parameters:
        - name: platform
          in: query
          description: 使用平台
          required: false
          schema:
            type: string
        - name: receiving_platform
          in: query
          description: 收款平台
          required: false
          schema:
            type: string
        - name: customer_manager
          in: query
          description: 客户经理
          required: false
          schema:
            type: string
        - name: user_id
          in: query
          description: 用户编号
          required: false
          schema:
            type: string
        - name: phone_number
          in: query
          description: 手机号
          required: false
          schema:
            type: string
        - name: account_holder
          in: query
          description: 账户主体
          required: false
          schema:
            type: string
        - name: account_type
          in: query
          description: 账户类型
          required: false
          schema:
            type: string
        - name: registration_date
          in: query
          description: 注册日期范围
          required: false
          schema:
            type: array
            items:
              type: string
              format: date
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/ReceiveAccount'
                      total:
                        type: integer
                        description: 总记录数
                      page:
                        type: integer
                        description: 当前页码
                      page_size:
                        type: integer
                        description: 每页条数

  /shop/receiveAccount/add:
    post:
      tags:
        - ReceiveAccount
      summary: 添加收款账户
      description: 新增收款账户信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReceiveAccountCreate'
      responses:
        '200':
          description: 添加成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 添加成功
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 新增记录ID

  /shop/receiveAccount/edit:
    post:
      tags:
        - ReceiveAccount
      summary: 编辑收款账户
      description: 修改收款账户信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReceiveAccountEdit'
      responses:
        '200':
          description: 编辑成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 编辑成功

  /shop/receiveAccount/detail:
    get:
      tags:
        - ReceiveAccount
      summary: 获取收款账户详情
      description: 根据ID获取收款账户详细信息
      parameters:
        - name: id
          in: query
          description: 收款账户ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回详情数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    $ref: '#/components/schemas/ReceiveAccount'

  /shop/receiveAccount/import:
    post:
      tags:
        - ReceiveAccount
      summary: 批量导入收款账户
      description: 通过Excel文件批量导入收款账户数据
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                excel_src:
                  type: string
                  description: Excel文件链接
              required:
                - excel_src
      responses:
        '200':
          description: 导入成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 导入成功
                  data:
                    type: object
                    properties:
                      error_file:
                        type: string
                        description: 错误数据文件路径
                      error_count:
                        type: integer
                        description: 错误数量
                      success_count:
                        type: integer
                        description: 成功数量

  /shop/receiveAccount/export:
    get:
      tags:
        - ReceiveAccount
      summary: 导出收款账户数据
      description: 根据筛选条件导出收款账户数据
      parameters:
        - name: platform
          in: query
          description: 使用平台
          required: false
          schema:
            type: string
        - name: receiving_platform
          in: query
          description: 收款平台
          required: false
          schema:
            type: string
        - name: customer_manager
          in: query
          description: 客户经理
          required: false
          schema:
            type: string
        - name: account_holder
          in: query
          description: 账户主体
          required: false
          schema:
            type: string
      responses:
        '200':
          description: 导出成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 导出成功
                  data:
                    type: object
                    properties:
                      download_url:
                        type: string
                        description: 下载链接

  /shop/receiveAccount/getLog:
    get:
      tags:
        - ReceiveAccount
      summary: 获取操作日志
      description: 获取收款账户的操作日志记录
      parameters:
        - name: id
          in: query
          description: 收款账户ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回日志数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          description: 日志ID
                        table_name:
                          type: string
                          description: 表名
                        table_id:
                          type: integer
                          description: 记录ID
                        before_data:
                          type: object
                          description: 修改前数据
                        after_data:
                          type: object
                          description: 修改后数据
                        operator:
                          type: integer
                          description: 操作人ID
                        created_at:
                          type: string
                          format: date-time
                          description: 创建时间
                        update_time:
                          type: string
                          format: date-time
                          description: 更新时间

  /shop/receiveAccount/editBatch:
    post:
      tags:
        - ReceiveAccount
      summary: 批量编辑收款账户
      description: 批量修改收款账户信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: array
                  items:
                    $ref: '#/components/schemas/ReceiveAccountEdit'
                  description: 编辑数据数组
              required:
                - data
      responses:
        '200':
          description: 批量编辑成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 批量编辑成功
                  data:
                    type: object
                    properties:
                      success_count:
                        type: integer
                        description: 成功数量
                      error_count:
                        type: integer
                        description: 错误数量

components:
  schemas:
    ReceiveAccount:
      type: object
      properties:
        id:
          type: integer
          description: ID
        account_type:
          type: string
          description: 账户类型
        account_holder:
          type: string
          description: 账户主体
        platform:
          type: string
          description: 使用平台
        registration_date:
          type: string
          description: 注册日期
          format: date
        receiving_platform:
          type: string
          description: 收款平台
        customer_manager:
          type: string
          description: 客户经理
        user_id:
          type: string
          description: 用户编号
        phone_number:
          type: string
          description: 手机号
        phone_owner:
          type: string
          description: 手机号持有人
        phone_contact:
          type: string
          description: 手机号对接人
        login_password:
          type: string
          description: 登录密码
        transaction_password:
          type: string
          description: 交易密码
        bank_card:
          type: string
          description: 银行卡
        account_position:
          type: string
          description: 账户定位
        remark:
          type: string
          description: 备注

    ReceiveAccountCreate:
      type: object
      required:
        - account_type
        - account_holder
        - platform
        - receiving_platform
        - customer_manager
        - user_id
      properties:
        account_type:
          type: string
          description: 账户类型
        account_holder:
          type: string
          description: 账户主体
        platform:
          type: string
          description: 使用平台
        receiving_platform:
          type: string
          description: 收款平台
        customer_manager:
          type: string
          description: 客户经理
        user_id:
          type: string
          description: 用户编号
        login_method:
          type: string
          description: 登录方式
        phone_number:
          type: string
          description: 手机号
        phone_owner:
          type: string
          description: 手机号持有人
        phone_contact:
          type: string
          description: 手机号对接人
        bank_card:
          type: string
          description: 银行卡
        account_position:
          type: string
          description: 账户定位
        remark:
          type: string
          description: 备注

    ReceiveAccountEdit:
      type: object
      required:
        - id
      properties:
        id:
          type: integer
          description: 收款账户ID
        account_type:
          type: string
          description: 账户类型
        account_holder:
          type: string
          description: 账户主体
        platform:
          type: string
          description: 使用平台
        receiving_platform:
          type: string
          description: 收款平台
