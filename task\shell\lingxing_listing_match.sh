# !/bin/bash
# 领星listing获取，更新oa系统项目
token='01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0'
date=$1
while true; do
    # 发送POST请求，这里假设URL为http://example.com/api，根据实际情况替换
#    response=$(curl -s -X POST -d "token=$token&date=$date" 'http://171.223.214.187:8901/task/lingXingApi/updateProjectOfListing')
    response=$(curl -s -X POST -d "token=$token&date=$date" 'http://39.101.133.112:8082/task/lingXingApi/updateProjectOfListing')
     echo "$response"
    # 从响应中提取code字段的值
    code=$(echo "$response" | grep -oP '"code":\K\d+')
    # 检查code字段的值
    current_time=$(date +"%Y-%m-%d %H:%M:%S")
    if [ "$code" -ne 2 ]; then
        echo "code=$code，继续请求$current_time"
    else
       break ;
    fi
done
current_time=$(date +"%Y-%m-%d %H:%M:%S")
echo "同步完成，退出。 $current_time"