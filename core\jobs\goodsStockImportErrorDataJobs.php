<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/24 9:57
 */

namespace core\jobs;

use core\lib\config;
use core\lib\db\dbFMysql;
use financial\form\goodsStorkForm;

class goodsStockImportErrorDataJobs
{
    public string $unqueid = '';
    public string $key = '';//到表得redis_key
    public int $import_id;
    public int $page;
    public int $page_size = 1000;
    public function __construct($key,$import_id,$page){
        $this->unqueid = uniqid();
        $this->key = $key;
        $this->import_id = $import_id;
        $this->page = $page;
    }
    public function task(){
        $redis = (new \core\lib\predisV())::$client;
        $export_data = json_decode($redis->get($this->key),true);
        $db = dbFMysql::getInstance();
        $res = goodsStorkForm::exportImportErrorData($this->import_id,$this->page,$this->page_size);
        if ($res['count'] > 0) {
            $url = $res['excel_path'];
            $export_data['success_count'] = $export_data['success_count']+$res['count'];
            $export_data['excel_url'][] = $url;
            if ($export_data['success_count'] < $export_data['total']) {
                $queue_key = config::get('delay_queue_key', 'app');
                $page = $this->page+1;
                $task = new goodsStockImportErrorDataJobs($this->key,$this->import_id,$page); // 创建任务类实例
                $redis->zAdd($queue_key, [], 0, serialize($task));
                $redis->set($this->key,json_encode($export_data));
                $redis->expire($this->key,60*60);
            } else {
                //压缩包生成
                $save_path = '/public_financial/downLoad/goods_stock/import_error';
                if (!file_exists(SELF_FK.$save_path)) {
                    mkdir(SELF_FK.$save_path, 0777, true);
                }
                $zip_url = $save_path ."/".date('YmdHis').'.zip';
                //生成压缩包
                if (setZipByUrl($export_data['excel_url'],$zip_url)) {
                    //保存压缩包地址
                    $db->table('goods_stock_import')
                        ->where('where id=:id',['id'=>$this->import_id])
                        ->update(['error_zip_url'=>$zip_url]);
                }
                $export_data['zip_url'] = $zip_url;
            }
            $redis->set($this->key,json_encode($export_data));
            $redis->expire($this->key,60*60);
        }
    }
}