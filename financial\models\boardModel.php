<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/8 14:51
 */

namespace financial\models;

use core\lib\db\dbFMysql;
use financial\common\boardBase;
use financial\common\mskuReportBase;
use financial\form\coustomColumnJobForm;
use financial\form\tableDataForm;

class boardModel extends boardBase
{
    public static array $years; //本时间段年数据
    public static array $qoq_years;//同比年
    public static array $yoy_years;//环比年
    public static array $param;
    public static array $search_month; //搜索时间段
    public static array $qoq_month;//同比月份
    public static array $yoy_month;//环比月份
    public static array $yoy_qoq_month;//同比数据对应的环比时间
    public static array $yoy_qoq_years;//同比数据对应的环比年份
    public static array $qoq_last_years;//同比上一个时间段 年
    public static array $qoq_last_month;//同比上一个时间段 月
    public static array $aggregation_keys = []; //合并计算字段（毛利润）
    public static int $need_add_aggregation_val = 0;//是否需要计算合并字段 1是 0否
    public static array $yunying_ids = [];//要查询的运营id，为[],标识查询所有人的数据。
    public static array $project_ids = [];//要查询的运营id对应的项目id。
    public static bool $show_all_data = false;//查看所有数据
    public function __construct()
    {
        $paras_list = array('country_code','currency_code','project_ids','yunying_ids','date_time','category_ids',"search_type","search_value",'aggregation_keys','order_by_key','order_by_type');
        $request_data = ['date_time'=>'时间区间'];
        $param = arrangeParam($_POST, $paras_list, $request_data);
        $param['date_time'] = json_decode($param['date_time']);
        $aggregation_keys_list = json_decode($param['aggregation_keys']);
        self::$aggregation_keys = columnModel::getTableSearchKeys($aggregation_keys_list,'board');
        if (!count($param['date_time'])) {
            returnError('请选择时间区间');
        }
        //获取结账月份+权限月份
        $db = dbFMysql::getInstance();
        $data_month = tableDataForm::getDataMonth($db);
        //月份
        $search_month = [];
        $month = $param['date_time'][0];
        while (1) {
            if (strtotime($month) <= strtotime($param['date_time'][1])) {
                if (in_array($month,$data_month)) {
                    $search_month[] = $month;
                }
                $month = date('Y-m',strtotime($month.' +1 month'));
            } else {
                break;
            }
        }
        self::$search_month = $search_month;
        $all_years = [];
        //获取时间
        $years = [];
        foreach ($search_month as $m_date) {
            $year = date('Y',strtotime($m_date));
            $years[$year][] = $m_date;
            $all_years[] = $year;
        }
        //环比时间
        $m_date_count = count($search_month);
        $qoq_month = [];
        $yoy_month = [];
        $yoy_qoq_month = [];
        $qoq_last_month = [];
        foreach ($search_month as $m_date) {
            $qoq_month[] = date('Y-m',strtotime($m_date.' -'.$m_date_count.' month'));
            $qoq_last_month[] = date('Y-m',strtotime($m_date.' -'.($m_date_count*2).' month'));
            $yoy_month[] = date('Y-m',strtotime($m_date.' -12 month'));
            $yoy_qoq_month[] = date('Y-m',strtotime($m_date.' -13 month'));
        }
        self::$qoq_month = $qoq_month;
        self::$qoq_last_month = $qoq_last_month;
        self::$yoy_month = $yoy_month;
        self::$yoy_qoq_month = $yoy_qoq_month;
        $qoq_years = [];
        foreach ($qoq_month as $m_date) {
            $year = date('Y',strtotime($m_date));
            $qoq_years[$year][] = date('Y-m',strtotime($m_date.' -'.$m_date_count.' ,month'));
            $all_years[] = $year;
        }
        //同比时间
        $yoy_years = [];
        foreach ($yoy_month as $m_date) {
            $year = date('Y',strtotime($m_date));
            $yoy_years[$year][] = date('Y-m',strtotime($m_date.' -'.$m_date_count.' ,month'));
            $all_years[] = $year;
        }
        //同比上一个时间段时间
        $qoq_last_years = [];
        foreach ($qoq_last_month as $m_date) {
            $year = date('Y',strtotime($m_date));
            $qoq_last_years[$year][] = date('Y-m',strtotime($m_date.' -'.$m_date_count.' ,month'));
            $all_years[] = $year;
        }
        //同比数据对应的环比年份
        $yoy_qoq_years = [];
        foreach ($yoy_qoq_month as $m_date) {
            $year = date('Y',strtotime($m_date));
            $yoy_qoq_years[$year][] = date('Y-m',strtotime($m_date.' -'.$m_date_count.' ,month'));
            $all_years[] = $year;
        }
        self::$years = $years;
        self::$yoy_years = $yoy_years;
        self::$qoq_last_years = $qoq_last_years;
        self::$qoq_years = $qoq_years;
        self::$yoy_qoq_years = $yoy_qoq_years;
        self::$param = $param;
        //建表
        $all_years = array_unique($all_years);
        foreach ($all_years as $year) {
            tableDataModel::creatTableMonth($year);
        }
        //获取可查询的数据的运营id
        list($yunying_ids,$project_ids,$show_all_data) = boardTableModels::getYunyingIds();
        self::$yunying_ids = $yunying_ids;
        self::$project_ids = $project_ids;
        self::$show_all_data = $show_all_data;
    }
    //获取查询条件(领星原表)
    public static function getSqlWhereList($db,$year,$m_date_list,$group_by,$fields,$key_list) {
        if (!self::$show_all_data) {
            if (count(self::$yunying_ids) == 0 && count(self::$project_ids) == 0) {
                return [];
            }
        }
        $param = self::$param;
        //查询可用的sku
        //产品名
        $goods_name = $param['search_type'] == 'goods_name'?$param['search_value']:'';
        //分类
        $category_ids = [];
        if (!empty($param['category_ids']) && $param['category_ids']!='[]') {
            $category_ids = json_decode($param['category_ids']);
        }
        $sku_list = [];
        if (!empty($goods_name) || count($category_ids)) {
            $db->table('goods');
            if (!empty($goods_name)) {
                $db->where('where product_name=:product_name',['product_name'=>"%$goods_name%"]);
            }
            if (count($category_ids)) {
                $db->whereIn('cid',$category_ids);
            }
            $goods_list = $db->field('sku')->list();
            $sku_list = array_column($goods_list,'sku');
        }
        //获取数据
        $db->table('table_month_count_'.$year)
            ->where('where is_delete=0')
            ->whereIn('reportDateMonth',$m_date_list);
        //国家
        if (!empty($param['country_code']) && $param['country_code'] != '[]') {
            $country_codes = json_decode($param['country_code']);
            $db->whereIn('countryCode',$country_codes);
        }
        //项目
        if (!empty($param['project_ids']) && $param['project_ids']!='[]') {
            $project_ids = json_decode($param['project_ids']);
            if (!self::$show_all_data) {
                $project_ids = array_intersect(self::$project_ids,$project_ids);
            }
            $db->whereIn('project_id',$project_ids);
        }
        //权限运营+搜索
        $user_id = userModel::$qwuser_id;
        if (!empty($param['yunying_ids']) && $param['yunying_ids']!='[]') {
            if (!self::$show_all_data) {
                $yunying_ids = array_intersect(self::$yunying_ids,json_decode($param['yunying_ids']));
                $yunying_str = getYunyingSql($yunying_ids,self::$project_ids,$user_id);
                $db->andWhere($yunying_str);
            } else {
                $db->whereIn('yunying_id',json_decode($param['yunying_ids']));
            }
        } else {
            if (!self::$show_all_data) {
                $yunying_str = getYunyingSql(self::$yunying_ids,self::$project_ids,$user_id);
                $db->andWhere($yunying_str);
            }
        }
        //自定义搜索
        if (!empty($param['search_type']) && !empty($param['search_value'])) {
            if ($param['search_type'] == 'asin') {
                $db->andWhere('asin like :asin',['asin'=>"%{$param['search_value']}%"]);
            } elseif ($param['search_type'] == 'sku') {
                $db->andWhere('localSku like :sku',['sku'=>"%{$param['search_value']}%"]);
            } elseif ($param['search_type'] == 'parentAsin') {
                $db->andWhere('parentAsin like :parentAsin',['parentAsin'=>"%{$param['search_value']}%"]);
            }
        }
        if (!empty($goods_name) || count($category_ids)) {
            $db->whereIn('localSku',$sku_list);
        }
        if (!count($key_list)) {
            $db->field($fields);
        } else {
            //获取要查询的字段
            $where_array = [];
            //规则跟毛利润相关且
            if (self::$need_add_aggregation_val) {
                $string_ = implode('+',self::$aggregation_keys);
                $where_array[] = "sum($string_) as aggregation_val";
            }
            //字段
            foreach ($key_list as $w_k) {
                if ($w_k == 'aggregation_val') {
                    continue;
                }
                if (in_array($w_k,self::$aggregation_keys)) {
                    $where_array[] = "0 as $w_k";
                    continue;
                }
                $where_array[] = "sum($w_k) as $w_k";
            }
            $fields .= ','.implode(',',$where_array);
            $db->field($fields);
        }
        $db->groupBy($group_by);
        $report_data = $db->list();
        return $report_data;
    }
    //自定义表
    public static function getSqlWhereForOa($db,$year,$m_date_list) {
        if (!self::$show_all_data) {
            if (count(self::$yunying_ids) == 0 && count(self::$project_ids) == 0) {
                //获取数据
                $db->table("custom_val_$year")
                    ->where('1=0');
                return $db;
            }
        }
        $param = self::$param;
        //查询可用的sku
        //产品名
        $goods_name = $param['search_type'] == 'goods_name'?$param['search_value']:'';
        //分类
        $category_ids = [];
        if (!empty($param['category_ids']) && $param['category_ids']!='[]') {
            $category_ids = json_decode($param['category_ids']);
        }
        $sku_list = [];
        if (!empty($goods_name) || count($category_ids)) {
            $db->table('goods');
            if (!empty($goods_name)) {
                $db->where('where product_name=:product_name',['product_name'=>"%$goods_name%"]);
            }
            if (count($category_ids)) {
                $db->whereIn('cid',$category_ids);
            }
            $goods_list = $db->field('sku')->list();
            $sku_list = array_column($goods_list,'sku');
        }
        //获取数据
        $db->table("custom_val_$year")
            ->whereIn('m_date',$m_date_list);
        //国家
        if (!empty($param['country_code']) && $param['country_code'] != '[]') {
            $country_codes = json_decode($param['country_code']);
            $db->whereIn('country_code',$country_codes);
        }
        //项目
        if (!empty($param['project_ids']) && $param['project_ids']!='[]') {
            $project_ids = json_decode($param['project_ids']);
            if (!self::$show_all_data) {
                $project_ids = array_intersect(self::$project_ids,$project_ids);
            }
            $db->whereIn('project_id',$project_ids);
        }
        //权限运营+搜索
        $user_id = userModel::$qwuser_id;
        if (!empty($param['yunying_ids']) && $param['yunying_ids']!='[]') {
            if (!self::$show_all_data) {
                $yunying_ids = array_intersect(self::$yunying_ids,json_decode($param['yunying_ids']));
                $yunying_str = getYunyingSql($yunying_ids,self::$project_ids,$user_id);
                $db->andWhere($yunying_str);
            } else {
                $db->whereIn('yunying_id',json_decode($param['yunying_ids']));
            }
        } else {
            if (!self::$show_all_data) {
                $yunying_str = getYunyingSql(self::$yunying_ids,self::$project_ids,$user_id);
                $db->andWhere($yunying_str);
            }
        }
        //自定义搜索
        if (!empty($param['search_type']) && !empty($param['search_value'])) {
            if ($param['search_type'] == 'asin') {
                $db->andWhere('asin like :asin',['asin'=>"%{$param['search_value']}%"]);
            } elseif ($param['search_type'] == 'sku') {
                $db->andWhere('sku like :sku',['sku'=>"%{$param['search_value']}%"]);
            } elseif ($param['search_type'] == 'parentAsin') {
                $db->andWhere('p_asin like :p_asin',['p_asin'=>"%{$param['search_value']}%"]);
            }
        }
        if (!empty($goods_name) || count($category_ids)) {
            $db->whereIn('sku',$sku_list);
        }
        return $db;
    }
    //库存表（库存末期情况）
    public static function getSqlWhereForStock($db,$year,$m_date_list) {
        if (!self::$show_all_data) {
            if (count(self::$yunying_ids) == 0 && count(self::$project_ids) == 0) {
                //获取数据
                $db->table("goods_stock_$year")
                    ->where('1=0');
                return $db;
            }
        }
        $param = self::$param;
        //查询可用的sku
        //产品名
        $goods_name = $param['search_type'] == 'goods_name'?$param['search_value']:'';
        //分类
        $category_ids = [];
        if (!empty($param['category_ids']) && $param['category_ids']!='[]') {
            $category_ids = json_decode($param['category_ids']);
        }
        $sku_list = [];
        if (!empty($goods_name) || count($category_ids)) {
            $db->table('goods');
            if (!empty($goods_name)) {
                $db->where('where product_name=:product_name',['product_name'=>"%$goods_name%"]);
            }
            if (count($category_ids)) {
                $db->whereIn('cid',$category_ids);
            }
            $goods_list = $db->field('sku')->list();
            $sku_list = array_column($goods_list,'sku');
        }
        //获取数据
        $db->table("goods_stock_$year")
            ->whereIn('m_date',$m_date_list);
        //国家
        if (!empty($param['country_code']) && $param['country_code'] != '[]') {
            $country_codes = json_decode($param['country_code']);
            $db->whereIn('country_code',$country_codes);
        }
        //项目
        if (!empty($param['project_ids']) && $param['project_ids']!='[]') {
            $project_ids = json_decode($param['project_ids']);
            if (!self::$show_all_data) {
                $project_ids = array_intersect(self::$project_ids,$project_ids);
            }
            $db->whereIn('project_id',$project_ids);
        }
        //权限运营+搜索
        $user_id = userModel::$qwuser_id;
        if (!empty($param['yunying_ids']) && $param['yunying_ids']!='[]') {
            if (!self::$show_all_data) {
                $yunying_ids = array_intersect(self::$yunying_ids,json_decode($param['yunying_ids']));
                $yunying_str = getYunyingSql($yunying_ids,self::$project_ids,$user_id);
                $db->andWhere($yunying_str);
            } else {
                $db->whereIn('yunying_id',json_decode($param['yunying_ids']));
            }
        } else {
            if (!self::$show_all_data) {
                $yunying_str = getYunyingSql(self::$yunying_ids,self::$project_ids,$user_id);
                $db->andWhere($yunying_str);
            }
        }
        //自定义搜索
        if (!empty($param['search_type']) && !empty($param['search_value'])) {
            if ($param['search_type'] == 'asin') {
                $db->andWhere('asin like :asin',['asin'=>"%{$param['search_value']}%"]);
            } elseif ($param['search_type'] == 'sku') {
                $db->andWhere('sku like :sku',['sku'=>"%{$param['search_value']}%"]);
            } elseif ($param['search_type'] == 'parentAsin') {
                $db->andWhere('p_asin like :parentAsin',['parentAsin'=>"%{$param['search_value']}%"]);
            }
        }
        if (!empty($goods_name) || count($category_ids)) {
            $db->whereIn('sku',$sku_list);
        }
        return $db;
    }

    //不用根据规则时使用（直接根据汇率转，费用是total）
    protected static function getPriceByRoute1($res_list,$month_list = []) {
        //获取这几月的汇率
        $currency_code = self::$param['currency_code'];
        if (!count($month_list)) {
            $db = dbFMysql::getInstance();
            $routing_ = $db->table('routing')
                ->where('where code=:code',['code'=>$currency_code])
                ->field('code,date,code,my_rate')
                ->order('id desc')
                ->one();
            if ($routing_) {
                //计算每条金额数据换算
                foreach ($res_list as &$item) {
                    $val_total = $item['total'];
                    $val_total = $val_total/$routing_['my_rate'];
                    $item['total'] = roundToString($val_total);
                }
            }
        } else {
            if (!empty($currency_code)) {
                $db = dbFMysql::getInstance();
                $routing_list = $db->table('routing')
                    ->where('where code=:code',['code'=>$currency_code])
                    ->whereIn('date',$month_list)
                    ->field('code,date,code,my_rate')
                    ->list();
                foreach ($routing_list as $routing) {
                    $routing_[$routing['date'].'_'.$routing['code']] = $routing['my_rate'];
                }
            }
            //计算每条金额数据换算
            foreach ($res_list as &$item) {
                $val_total = $item['total'];
                if (!empty($currency_code)) {
                    if (isset($item['m_date'])) {
                        $routing_key = $item['m_date'].'_'.$currency_code;
                    } else {
                        $routing_key = $item['month'].'_'.$currency_code;
                    }

                    if (isset($routing_[$routing_key])) {
                        $val_total = $val_total/$routing_[$routing_key];
                    }
                }
                $item['total'] = roundToString($val_total);
            }
        }
        return $res_list;
    }

    /**
     * @param array $oa_list
     * @param array $m_date
     * @param $keys
     * @param int $type 0按月整理，1按项目整理
     * @return array
     * @throws \core\lib\ExceptionError 整理月数据，将每个月的数据统计为人民币(按规则计算需要)
     */
    public static function mergeMdataOa(array $oa_list,array $m_date,$keys,$type=0) {
        $oa_data = $oa_list;
        if (count($keys)) {
//            $oa_data = self::getPriceByRoute($oa_list,$m_date,self::$param['currency_code'],coustomColumnJobForm::$currency_keys);
            $new_data = [];
            switch ($type) {
                case 0:
                    foreach ($m_date as $month) {
                        if (!isset($new_data[$month])) {
                            $new_data[$month]['m_date'] = $month;
                            foreach ($keys as $id_) {
                                $key_1 = 'oa_key_' . $id_;
                                $new_data[$month][$key_1] = 0;
                            }
                        }
                        foreach ($oa_data as $data_) {
                            if ($month == $data_['m_date']) {
                                $key_ = 'oa_key_' . $data_['custom_id'];
                                $new_data[$month][$key_] += $data_['total'];
                            }
                        }
                    }
                    break;
                case 1:
                    $project_ids = array_unique(array_column($oa_list,'project_id'));
                    foreach ($project_ids as $project_id) {
                        if (!isset($new_data[$project_id])) {
                            foreach ($keys as $id_) {
                                $key_1 = 'oa_key_' . $id_;
                                $new_data[$project_id][$key_1] = 0;
                            }
                        }
                        foreach ($oa_data as $data_) {
                            if ($project_id == $data_['project_id']) {
                                $key_ = 'oa_key_' . $data_['custom_id'];
                                $new_data[$project_id][$key_] += $data_['total'];
                            }
                        }
                    }
                    break;
                case -1:
                    foreach ($oa_data as &$data_) {
                        foreach ($keys as $id_) {
                            $key_ = 'oa_key_' . $id_;
                            $data_[$key_] = $data_['total'];
                        }
                    }
                    $new_data = $oa_data;
                    break;
            }
            return $new_data;
        } else {
            return [];
        }

    }
    //整理数据(跟金额相关, 单个字段统计，且字段转换为total)
    public static function getMPricelist($month_list,$res_list) {
        //获取这几月的汇率
        $res_list = self::getPriceByRoute1($res_list,$month_list);
        $new_list = [];
        foreach ($res_list as $item) {
            $new_list[$item['m_date']][] = $item['total'];
        }
        //获取最终值
        $res_data = [];
        foreach ($month_list as $month) {
            $val_total = 0;
            if (isset($new_list[$month])) {
                $val_total = array_sum($new_list[$month]);
            }
            $res_data[] = [
                'm_date'=>$month,
                'total'=>roundToString($val_total),
            ];

        }
        return $res_data;
    }
}