<?php

namespace plugins\shop\models;

use core\lib\db\dbAfMysql;
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use core\lib\db\dbShopMysql;
use core\lib\redisCached;

class shopPlanModel extends baseModel
{
    public string $table = 'shop_plan';

    const STATUS_OPEN = 1; // 开启
    const STATUS_CLOSE_WAITING = 2; // 关闭待审核
    const STATUS_CLOSE_FAIL = 3; // 关闭失败
    const STATUS_CLOSE = 4; // 关闭

    public function getList($param){

        // 查询OA店铺信息 品牌(多选)、品牌持有人、卖家ID、店铺邮箱、部门（多选）
        $shopDb = dbShopMysql::getInstance();
        $afdb = dbAfMysql::getInstance();
        $dbF = dbFMysql::getInstance();

        // 搜索商标
        $trademark_ids = [];
        if (isset($param['trademark_holder'])) {
            $shopDb->table('trademark');
            $shopDb->andWhere('trademark_holder like :trademark_holder', ['trademark_holder' => '%' . $param['trademark_holder'] . '%']);
            $list = $shopDb->list();
            $trademark_ids = array_column($list, 'id');
            if (empty($list)) {
                returnSuccess(['list' => [], 'page' => $param['page'], 'page_size' => $param['page_size'], 'total' => 0]);
            }
        }

        $shopDb->table('shop', 's')
            ->leftJoin('email', 'e', 's.email_id = e.id')
            ->field('s.lx_shop_id as sid,s.shop_number, s.lx_shop_id,s.trademark_id,s.email_id,s.dep_id,e.email_account')
            ->where('1=1');
        $is_oa_shop = false;
        if (!empty($trademark_ids)) {
            $or_arr = [];
            $or_data_arr = [];
            $k = 0;
            foreach ($trademark_ids as $trademark_id) {
                $or_arr[] = "JSON_CONTAINS(s.trademark_id, :trademark_id_{$k})";
                $or_data_arr["trademark_id_{$k}"] = json_encode([strval($trademark_id)]);
                $k++;
            }
            $this->db->andWhere('('.implode(' or ', $or_arr).')', $or_data_arr);
            $is_oa_shop = true;
        }
        if (isset($param['trademark_id']) && !empty($param['trademark_id']) && is_array($param['trademark_id'])) {
            $or_arr = [];
            $or_data_arr = [];
            $k = 0;
            foreach ($param['trademark_id'] as $trademark_id) {
                $or_arr[] = "JSON_CONTAINS(s.trademark_id, :trademark_id_{$k})";
                $or_data_arr["trademark_id_{$k}"] = json_encode([$trademark_id]);
                $k++;
            }
            $this->db->andWhere('('.implode(' or ', $or_arr).')', $or_data_arr);
            $is_oa_shop = true;
        }
        if (isset($param['shop_email']) && !empty($param['shop_email'])) {
            $shopDb->andWhere('e.email_account like :shop_email', ['shop_email' => '%' . $param['shop_email'] . '%']);
            $is_oa_shop = true;

        }
        if (isset($param['dep_id']) && !empty($param['dep_id']) && is_array($param['dep_id'])) {
            $shopDb->whereIn('s.dep_id', $param['dep_id']);
            $is_oa_shop = true;
        }
        $sid = [];
        if ($is_oa_shop) {
            $shopList = $shopDb->list();
            $sid = array_column($shopList, 'lx_shop_id');
            if (empty($sid)) {
                returnSuccess(['list' => [], 'page' => $param['page'], 'page_size' => $param['page_size'], 'total' => 0]);
            }
        }
        // 领星查询 asin、 品名、运营人员、国家、店铺（通过OA查出来的）
        // 搜索店铺、产品名
        $listing = [];
        //店铺 sid
        if (!empty($sid)) {
            $listing = $afdb->table('lingxing_listing','a')
                ->where('a.status = 1')
                ->whereIn('a.sid', $sid)
                ->field('a.asin,a.marketplace')
                ->groupBy(['asin','marketplace'])
                ->list();
        }

        //品名
        if (isset($param['product_name']) && !empty($param['product_name'])) {
            $goods_list = $dbF->table('goods')
                ->where('product_name like :product_name',['product_name'=>'%'.$param['product_name'].'%'])
                ->field('sku')
                ->list();
            if (count($goods_list)) {
                $skus = array_column($goods_list,'sku');
                $listing_ = $afdb->table('lingxing_listing')
                    ->where('status = 1')
                    ->whereIn('local_sku',$skus)
                    ->field('asin,marketplace')
                    ->groupBy(['asin','marketplace'])
                    ->list();
                if (count($listing)) {
                    $last_listing = array_column($listing,'marketplace','asin');
                    foreach ($listing_ as $v) {
                        if (!isset($last_listing[$v['asin']])) {
                            $listing[] = $v;
                        }
                    }
                } else {
                    $listing = $listing_;
                }
            }
        }

        $afdb->table('listing_data','a')
            ->leftJoin('market','b','b.code = a.country_code')
            ->leftJoinOut('shop', 'shop_plan', 'sp', 'sp.listing_id = a.id');

        // 查询listing
        if (count($listing)) {
            list($sql_str,$sql_data) = self::getSqlInDataClon($listing);
            $afdb->where("(a.asin,b.country) in ($sql_str)",$sql_data);
        }
        if (isset($param['asin']) && !empty($param['asin'])) {
            $afdb->andWhere('a.asin = :asin',['asin'=>$param['asin']]);
        }
        //运营
        if (isset($param['yunying_id']) && !empty($param['yunying_id']) && is_array($param['yunying_id'])) {
            $afdb->andWhere('JSON_OVERLAPS(a.yunying_ids,:yunying_ids)',['yunying_ids'=>json_encode($param['yunying_id'])]);
        }
        //国家
        if (isset($param['country_code']) && !empty($param['country_code'])) {
            $afdb->whereIn('a.country_code',$param['country_code']);
        }
        if (isset($param['seller_id']) && !empty($param['seller_id'])) {
            $afdb->andWhere('sp.seller_id like :seller_id', ['seller_id' => '%' . $param['seller_id'] . '%']);
        }

        // 查询透明计划 upc ipp opr 状态
        if (isset($param['upc']) && !empty($param['upc'])) {
            $afdb->andWhere('sp.upc = :upc',['upc'=>$param['upc']]);
        }
        if (isset($param['ipp']) && !empty($param['ipp'])) {
            $afdb->andWhere('sp.ipp = :ipp',['ipp'=>$param['ipp']]);
        }
        if (isset($param['opr']) && !empty($param['opr'])) {
            $afdb->andWhere('sp.opr = :opr',['opr'=>$param['opr']]);
        }
        if (isset($param['status'])) {
            if ($param['status'] == 1) {
                $afdb->whereIn('sp.status', ['1', '3']);
            } else {
                $afdb->whereIn('sp.status', ['2', '4']);
            }
        }

        $list = $afdb->field('a.*,b.country_e as country_e, sp.id as shop_plan_id, sp.seller_id, sp.upc, sp.ipp, sp.opr, sp.status as shop_plan_status,sp.contract_manager, sp.contract_group, sp.remark')
            ->pages($param['page'],$param['page_size']);

        foreach ($list['list'] as $k=>$v) {
            $v['yunying_ids'] = json_decode($v['yunying_ids']);
            $v['leader_ids'] = json_decode($v['leader_ids']);
            $list['list'][$k] = $v;
        }
        $list['list'] = self::arrangeList($list['list']);
        return $list;
    }

    public static function getDetail($param) {
        // 查询透明计划 upc ipp opr 状态

        $afdb = dbAfMysql::getInstance();
        $afdb->table('listing_data','a')
            ->leftJoin('market','b','b.code = a.country_code')
            ->leftJoinOut('shop', 'shop_plan', 'sp', 'sp.listing_id = a.id');
        $detail = $afdb->field('a.*,b.country_e as country_e, sp.id as shop_plan_id, sp.seller_id,sp.upc, sp.ipp, sp.opr, sp.status as shop_plan_status,sp.contract_manager, sp.contract_group, sp.remark')
            ->where("a.id = :id",['id'=>$param['id']])
            ->one();

        $detail['yunying_ids'] = json_decode($detail['yunying_ids']);
        $detail['leader_ids'] = json_decode($detail['leader_ids']);
        $detail = self::arrangeList([$detail]);
        return $detail[0];
    }

    public function getByListingId($listing_id) {
        $this->db->table($this->table)->where('where listing_id = :listing_id', ['listing_id' => $listing_id]);
        return $this->db->one();
    }


    //根据分类id获取产品asin
    public static function getAsinDataByCateIds($cate_cids) {
        $dbF = dbFMysql::getInstance();
        //查询分类及其子分类
        $cate_cids = self::getChildsId($cate_cids);
        $goods_list = $dbF->table('goods')
            ->whereIn('cid',$cate_cids)
            ->field('cid,sku')
            ->list();
        $listing = [];
        if (count($goods_list)) {
            $skus = array_column($goods_list,'sku');
            $afdb = dbAfMysql::getInstance();
            $listing = $afdb->table('lingxing_listing')
                ->where('status = 1 and asin <> \'\' and marketplace <> \'\'')
                ->whereIn('local_sku',$skus)
                ->field('asin,marketplace')
                ->groupBy(['asin','marketplace'])
                ->list();
        }
        return $listing;
    }

    public static function arrangeList($list) {
        if (!count($list)) {
            return [];
        }
        $asin_s = array_column($list,'asin');
        $countrys = array_column($list,'country');
        $afdb = dbAfMysql::getInstance();
        /**  sku，品名，店铺  */
        $lingxing_listing = $afdb->table('lingxing_listing')
            ->where('status = 1')
            ->whereIn('asin',$asin_s)
            ->whereIn('marketplace',$countrys)
            ->field('asin,marketplace,local_sku,parent_asin,sid')
            ->groupBy(['asin','parent_asin','marketplace','local_sku','sid'])
            ->list();
        $listing_ = [];
        foreach ($lingxing_listing as $v) {
            $key_ = $v['asin'].'_'.$v['marketplace'];
            $listing_[$key_][] = $v;
        }
        //产品查询
        $dbF = dbFMysql::getInstance();
        $skus = array_column($lingxing_listing,'local_sku');
        $goods_list = $dbF->table('goods')
            ->whereIn('sku',$skus)
            ->field('sku,product_name,cid')
            ->list();
        $goods_ = [];
        foreach ($goods_list as $v) {
            $goods_[$v['sku']] = $v;
        }
        //产品分类
        $cids = array_column($goods_list,'cid');
        $goods_categoty_ = [];
        if ($cids) {
            $goods_categoty_list = self::getGoodsCate(array_unique($cids));
            foreach ($goods_list as $k=>$v) {
                if (isset($goods_categoty_list[$v['cid']])) {
                    $goods_categoty_[$v['sku']] = $goods_categoty_list[$v['cid']];
                }
            }
        }
        //店铺查询
        $sids = array_column($lingxing_listing,'sid');

        // 转换到OA店铺
        $shopDb = dbShopMysql::getInstance();
        $oa_shop_list = $shopDb->table('shop')->whereIn('lx_shop_id',$sids)
            ->field('id,shop_number,lx_shop_id,email_id, trademark_id')
            ->list();
        $emails = redisCached::getEmail();
        $emails = array_column($emails,'email_account', 'id');
        $trademarks = redisCached::getTrademark();
        $trademarks = array_column($trademarks,null,'id');

        $lx_shop = [];
        foreach ($oa_shop_list as &$oa_shop) {
            $oa_shop['email_account'] = $emails[$oa_shop['email_id']] ?? '';
            $oa_shop['trademark_id'] = json_decode($oa_shop['trademark_id'],true);
            $trademark_list = [];
            foreach ($oa_shop['trademark_id'] as $trademark_id) {
                $trademark_list[$trademark_id] = $trademarks[$trademark_id];
            }
            $oa_shop['trademark'] = array_values($trademark_list);
            if ($oa_shop['lx_shop_id']) {
                !isset($lx_shop[$oa_shop['lx_shop_id']]) && $lx_shop[$oa_shop['lx_shop_id']] = [];
                $lx_shop[$oa_shop['lx_shop_id']][] = $oa_shop;
            }
        }

        $shop_list = $afdb->table('shop_list')
            ->whereIn('sid',$sids)
            ->field('sid,lx_name,nick_name')
            ->list();
        $shop_ = [];
        foreach ($shop_list as $v) {
            $v['oa_shop'] = $lx_shop[$v['sid']] ?? [];
            $shop_[$v['sid']] = $v;
        }

        /** 用户负责人查询 内部成员*/
        $qw_user_ids = [];
        foreach ($list as $v) {
            $qw_user_ids = array_merge($qw_user_ids,$v['yunying_ids'],$v['leader_ids']);
        }
        $db = dbMysql::getInstance();
        $qw_user_list = $db->table('qwuser')
            ->whereIn('id',array_unique($qw_user_ids))
            ->field('id,wname')
            ->list();
        $qw_user_ = [];
        foreach ($qw_user_list as $v) {
            $qw_user_[$v['id']] = $v;
        }
        //整理数据
        foreach ($list as $k=>$v) {
            $key_ = $v['asin'].'_'.$v['country'];
            if (isset($listing_[$key_])) {
                //sku，品名
                $skus = array_column($listing_[$key_],'local_sku');
                $skus = array_flip($skus);
                $v['goods_list'] = array_values(array_intersect_key($goods_,$skus));
                $v['goods_category_list'] = array_values(array_intersect_key($goods_categoty_,$skus));
                //parent_asin
                $v['parent_asin'] = array_values(array_column($listing_[$key_],'parent_asin'));
                //店铺
                $sids = array_flip(array_column($listing_[$key_],'sid'));
                $v['store_list'] = array_values(array_intersect_key($shop_,$sids));
            } else {
                $v['goods_list'] = [];
                $v['store_list'] = [];
                $v['parent_asin'] = [];
                $v['goods_category_list'] = [];
            }
            //内部负责人-运营
            $yunying_ids = array_flip($v['yunying_ids']);
            $v['yunying'] = array_values(array_intersect_key($qw_user_,$yunying_ids));
            //内部负责人-主管
            $leader_ids = array_flip($v['leader_ids']);
            $v['leader'] = array_values(array_intersect_key($qw_user_,$leader_ids));
            //类别

            unset($v['yunying_ids']);
            unset($v['leader_ids']);
            unset($v['stock_ids']);
            unset($v['inner_user_ids']);
            unset($v['outer_user_ids']);
            $list[$k] = $v;
        }
        return $list;
    }

    public static function getChildsId($cate_ids) {
        $afdb = dbFMysql::getInstance();
        //第二层
        $child = $afdb->table('goods_category')
            ->where('is_delete = 0')
            ->whereIn('parent_cid',$cate_ids)
            ->field('cid')
            ->list();
        if (count($child)) {
            $cate_ids_ = array_column($child,'cid');
            //第三次
            $child1 = $afdb->table('goods_category')
                ->where('is_delete = 0')
                ->whereIn('parent_cid',$cate_ids_)
                ->field('cid')
                ->list();
            $child = array_merge($child,$child1);
        }
        $new_cate_ids = array_merge($cate_ids,array_column($child,'cid'));
        return $new_cate_ids;
    }

    public static function getSqlInDataClon(array $array) {
        $key_ = uniqid();
        $sql_str = [];
        $sql_data = [];
        $i = 0;
        foreach ($array as $v) {
            $key_item = [];
            $j = 0;
            foreach ($v as $value) {
                $key_v = $key_.$i.'_'.$j;
                $key_item[] = ':'.$key_v;
                $sql_data[$key_v] = $value;
                $j++;
            }
            $sql_str_item = '('.implode(',',$key_item).')';
            $sql_str[] = $sql_str_item;
            $i++;
        }
        return [implode(',',$sql_str),$sql_data];
    }

    public static function getGoodsCate(array $cate_ids, $type = 0) {
        if (!count($cate_ids)) {
            return [];
        }
        $afdb = dbFMysql::getInstance();
        $all_cate = $afdb->table('goods_category')
            ->where('is_delete = 0')
            ->list();

        $data = [];
        foreach ($cate_ids as $v) {
            foreach ($all_cate as $v1) {
                if ($v1['cid'] == $v) {
                    $data[] = $v1;
                }
            }
        }

        $result = [];
        foreach ($data as $v) {
            // 递归获取分类名称
            if (!$type) {
                $name_array = self::getCateAllName($all_cate, $v['parent_cid'], [$v['title']]);
            } else {
                $name_array = [$v['title']];
            }
            $result[$v['cid']] = [
                'id' => $v['id'],
                'cid' => $v['cid'],
                'parent_cid' => $v['parent_cid'],
                'name' => implode('/', array_reverse($name_array))
            ];
        }
        return $result;
    }

    private static function getCateAllName($all_cate, $pid, $name_list) {
        if ($pid > 0) {
            foreach ($all_cate as $cate) {
                if ($cate['cid'] == $pid) {
                    $name_list[] = $cate['title'];
                    if ($cate['parent_cid'] > 0) {
                        $name_list = self::getCateAllName($all_cate, $cate['parent_cid'], $name_list);
                    }
                }
            }
        }
        return $name_list;
    }


}
