<?php
/**
 * @author: zhangguoming
 * @Time: 2024/3/27 11:25
 */

namespace  plugins\goods\models;

use core\lib\db\dbMysql;
use plugins\goods\form\configFrom;

class userModel extends \admin\models\userModel
{
    //需要写的字段
    public static string $auth;
    public static array  $role_type;//0其他,1表示开发,2系统管理员，3软件问题，4硬件问题处理人员，5英文名,6亚马逊运营
    public static int $is_zhuanli_man = -1;//-1未使用，0否，1是

    //是否为开发
    public static function isDeveloper() {
        if(in_array(1,self::$role_type)) {
            return true;
        } else {
            return false;
        }
    }
    //是否为管理员
    public static function isManage() {
        if(in_array(2,self::$role_type)) {
            return true;
        } else {
            return false;
        }
    }
    //获取当前登录人角色类型
    public static function getRoleType() {
        $config_data = configFrom::getConfigData(['goods_ename_manage','goods_soft_manage']);
        foreach ($config_data as $k=>$v) {
            if (empty($v)) {
                continue;
            }
            $data = json_decode($v,true);
            $ids = array_column($data,'id');
            if (in_array(self::$qwuser_id,$ids)) {
                //商品取英文名
                if ($k == 'goods_ename_manage') {
                    self::$role_type[] = 5;
                }
                //软件问题处理人
                if ($k == 'goods_soft_manage') {
                    self::$role_type[] = 3;
                }
            }
        }
        self::$role_type = getArryForType(self::$role_type,'string');
        return self::$role_type;
    }

    //是否为管理员或开发或超级管理员
    public static function isManageDeveloper() {
        if(in_array(2,self::$role_type) || in_array(1,self::$role_type) || self::$is_super) {
            return true;
        } else {
            return false;
        }
    }
    //是否为管理员+超管
    public static function isSupreOrManage() {
        if(in_array(2,self::$role_type) || self::$is_super) {
            return true;
        } else {
            return false;
        }
    }
    //是否为超管
    public static function isSuper() {
        if(self::$is_super) {
            return true;
        } else {
            return false;
        }
    }
    //验证角色是否可看产品问题
    public static function allowGoodsAbnormal() {
        $array_ = array_intersect([1,2,3,4],self::$role_type);
        if (count($array_) || self::$is_super) {
            return true;
        } else {
            return false;
        }
    }
    //验证是否配置专利的专利查看着
    public static function isZhuanLiMan() {
        if (self::$is_zhuanli_man == -1) {
            $zhuanli_view = configFrom::getConfigByName('zhuanli_view_user');
            $zhuanli_user = empty($zhuanli_view)?[]:array_column(json_decode($zhuanli_view,true),'id');
            if (in_array(userModel::$qwuser_id,$zhuanli_user)) {
                self::$is_zhuanli_man = 1;
            } else {
                self::$is_zhuanli_man = 0;
            }
        }
        return  self::$is_zhuanli_man;
    }
    //验证是开发或者亚马逊运营
    public static function isKaifaOrYunying() {
        if(in_array(6,self::$role_type) || in_array(1,self::$role_type)) {
            return true;
        } else {
            return false;
        }
    }

}