<?php
/**
 * Created by 漠山.
 * @purpose 描述
 * @Author: 漠山
 * @Time: 2023/12/5 20:15
 */
namespace  plugins\goods\controller;
use plugins\goods\form\goodsProjectFrom;
use plugins\goods\form\goodsProjectParticipantFrom;
use plugins\goods\models\goodsNewModel;
use core\jobs\sendMessageJobs;
use core\lib\ExceptionError;
use core\lib\log;
use core\lib\db\dbMysql;
use core\lib\predisV;
use core\lib\swoole\webSocketSwoole;
use Psr\Log\LoggerInterface;
use Rap2hpoutre\FastExcel\FastExcel;

class indexController
{
    public function index(){
//        dd(uniqid());
//        $time = strtotime('2024-03-18 14:50:00');
//        $text = '测试延时队列：新品。';
//        $w_userid = ['ZhangGuoMing'];
//        $user = json_decode(USER_INFO, true);
//        $other_data = [
//            'user_id'=>$user['id'],
//            'model_id'=>1,
//            'node_index'=>0,
//            'event_index'=>0,
//            'msg_type'=>1
//        ];
//        $job = new sendMessageJobs($w_userid, $text, $other_data);
//        predisV::setDelayQueue($job,$time);
        $data = [
            'system_type' => 3,
            'type' => 'textcard',
            'msg' => 'Order 026-1393415-0246735 to uploa, please upload the replenishment order number in time.',
            'title' => 'Replenishment order',
            'qw_userid' => 'ZhangGuoMing',
            'data' => '{"id":50}'
        ];
        predisV::redisQueue($data);
        dd($data);
        //企微应用，卡片发送消息，参数编码
//        predisV::redisQueue([
//            'system_type'=>1,//系统1产品系统，2财务系统
//            'type'=>'textcard',
//            'qw_userid'=>'ZhangYunYing',
//            'msg'=>'测试',
//            'title'=>'测试',
//            'data'=> json_encode(['id'=>1])
//        ]);
//        dd(655);
//        $data = qwMsgDecryption('8wPWAeCa44+qp8QtgjPTaabQNGWwAubAanRFQgINTAKoGokCfRUyWA==');
//        dd($data);
//       webSocketSwoole::sendMsg('你是9999');
        //更新流程参与人关系表
//        $db = dbMysql::getInstance();
//        $data = $db->query('select * from oa_goods_project where id = 1');
//        goodsProjectParticipantFrom::setParticipant(json_decode($data['tpl_data'],true),$data['goods_id'],1);
//        $data = strtotime("+1.5 days", time());
//        dd($data);

        // 打印输出结果
    }

    //运行redis stream 异步发送消息监听
    public function openRedisConsumer() {
        $targetFile = SELF_FK.'/task/redis/redisConsumer.php';
        // 执行命令行
        $command = "php $targetFile > /dev/null 2>&1 &";
//        exec($command,$output);
        dd($command);
    }

    //运行websoket 消息推送
    public function openSwooleWs() {
        $targetFile = SELF_FK.'/task/swoole/webSocket.php';
        // 执行命令行
        $command = "php $targetFile > /dev/null 2>&1 &";
        exec($command,$output);
        dd($output);
    }

    //运行延时队列监听脚本运行
    public function openQueue(){
        $targetFile = SELF_FK.'/queue.sh > /dev/null 2>&1 &';
        // 执行命令行
//        $output = shell_exec($targetFile);
        dd($targetFile);
    }

    public function setCat(){
        die;
        $db = dbMysql::getInstance();
        $data = $db->queryAll('select * from lingxing_category');

        function ss($list,$pid,$new_pid) {
            $res_data = [];
            foreach ($list as $k=>$mm){
                if($mm['parent_cid'] == $pid) {
                    $res_data[$k] = $mm;
                    $db = dbMysql::getInstance();
                    $db->table('goods_cate');
                    $pppid = $db->insert(['p_id'=>$new_pid,'cate_name'=>$mm['title']]);
                    unset($list[$k]);
                    $res_data[$k]['child'] = ss($list,$mm['cid'],$pppid);
                }
            }
            return $res_data;
        }
        $kk = ss($data,0, 0);
    }

    public function setColor() {
        die;
        $file = $_FILES['s'];
        //获取文件临时路径
        $temp_name = $file['tmp_name'];

        $excel_data = (new FastExcel)->import($temp_name);
        $data = $excel_data->toArray();
        if (!count($data)) {
            echo '没数量';die;
        }
        $db = dbMysql::getInstance();
        $sql = 'insert into oa_goods_color (`color_name`,`color_name_en`) values ';
        foreach ($data as $v) {
            $sql .= "('{$v['name']}','{$v['name_en']}'),";
        }
        $db->query(trim($sql,','));
        dd($data);
    }
    public function setVideo() {
        dd(22);
        $file = $_FILES['s'];
        //获取文件临时路径
        $temp_name = $file['tmp_name'];

        $excel_data = (new FastExcel)->import($temp_name);
        $data = $excel_data->toArray();
        if (!count($data)) {
            echo '没数量';die;
        }
        $db = dbMysql::getInstance();
        $sql = 'insert into oa_goods_color (`color_name`,`color_name_en`) values ';
        foreach ($data as $v) {
            $sql .= "('{$v['name']}','{$v['name_en']}'),";
        }
        $db->query(trim($sql,','));
        dd($data);
    }
    public function setGoodsCode(){
        dd(12);
        $next_code = 'a0';
        $sql = 'insert into oa_goods_code (`code`) values ';
        while (1) {
            $sql.=("('$next_code'),");
            $next_code = dechex(hexdec($next_code) + 1);
            if (hexdec($next_code) > 255) {
                break;
            }
        }
        $db = dbMysql::getInstance();
        $db->query(trim($sql,','));
    }

    //硬件测试模板导入
    public function setGoodsHardware() {
        dd(522);
        $file = $_FILES['s'];
        //获取文件临时路径
        $temp_name = $file['tmp_name'];

        $excel_data = (new FastExcel)->import($temp_name);
        $data = $excel_data->toArray();
        if (!count($data)) {
            echo '没数量';die;
        }
        $db = dbMysql::getInstance();
        $db->table('goods_hardware');
        foreach ($data as $v) {
            $db->insert([
                'row_name'=>$v['row_name'],
                'description'=>str_replace('/\n','',str_replace(' ','',$v['description'])),
                'standard_basis'=>str_replace(' ','',$v['standard_basis']),
                'prototype_allocation'=>str_replace(' ','',$v['prototype_allocation']),
                'tools'=>str_replace(' ','',$v['tools']),
            ]);
        }

        dd($data);
    }
    //产品供应商导入
    public function setGoodsSupplier() {
        dd(456);
        $file = $_FILES['s'];
        //获取文件临时路径
        $temp_name = $file['tmp_name'];

        $excel_data = (new FastExcel)->import($temp_name);
        $data = $excel_data->toArray();
        if (!count($data)) {
            echo '没数量';die;
        }
        $db = dbMysql::getInstance();
        foreach ($data as $v) {
            $supplier = $db->table('goods_supplier')
                ->where('where supplier_name=:supplier_name',['supplier_name'=>$v['供应商']])
                ->one();
            if ($supplier) {
                if ($supplier['is_delete'] == 1) {
                    $db->table('goods_supplier')
                        ->where('where id=:id',['id'=>$supplier['id']])
                        ->update();
                }
                continue;
            } else {
                $db->table('goods_supplier')
                    ->insert([
                    'supplier_name'=>$v['供应商'],
                    'level'=>$v['等级'],
                ]);
            }
        }

        dd($data);
    }

    //批量设置用户未后台管理员
    public function setManageMuch() {
        dd('52616');
        $db = dbMysql::getInstance();
        //只能是二级或者三级
        $qw_partment_ids = [114,115,116,175,190,209,210,211];
        $qw_partment1 =  $db->table('qwdepartment')
            ->whereIn('qw_parentid',$qw_partment_ids)->list();
        $qw_partment2 =  $db->table('qwdepartment')
            ->whereIn('wp_id',$qw_partment_ids)->list();
        $qw_partment = array_merge($qw_partment1,$qw_partment2);
//        dd($qw_partment);
        $partment_ids = array_column($qw_partment,'wp_id');
        $user_list = [];
        foreach($partment_ids as $id) {
            $user_list_ = $db->table('qwuser','a')
                ->leftJoin('user','b','b.qw_userid = a.wid')
                ->where('where JSON_CONTAINS(a.wdepartment_ids,:qw_partment_id)',['qw_partment_id'=>json_encode([$id],JSON_NUMERIC_CHECK)])
                ->field('a.*,b.qw_userid')->list();
            $user_list = array_merge($user_list,$user_list_);
        }
        //去重
        $user_list = arrayUnique2($user_list,'id');
        $wids = [];
        foreach ($user_list as $user) {
            if ($user['qw_userid'] == 'null' || $user['qw_userid'] == '') {
                $wids[] = $user['wid'];
                $db->table('user');
                $unique_id = getuniqId('oa');
                $pwd = getPwdMd5('123456', $unique_id);
                $in_data = ['name'=>$user['wname'],'pwd'=>$pwd,'created_at'=>time(),'uniqueid'=>$unique_id,'qw_userid'=>$user['wid']];
                $db->insert($in_data);
            }
        }
        dd($wids);
    }

    //导入产品信息(app产品)
    public function exelGoodsInfo() {
        //对应的协议编码导入未开发
        returnSuccess('暂时不可用');
        $file = $_FILES['s'];
        //获取文件临时路径
        $temp_name = $file['tmp_name'];
        $excel_data = (new FastExcel)->import($temp_name);
        $data = $excel_data->toArray();
        if (!count($data)) {
            echo '没数量';die;
        }
//        dd($data);
        $db = dbMysql::getInstance();
        //功能
        $function_list = $db->table('goods_function')->list();
        //颜色
        $color_list = $db->table('goods_color')->list();
        //供应商
        $supplier_list = $db->table('goods_supplier')->list();
        //已有商品
        $goods_code_list = $db->table('goods_code')->list();
        $code_array_used = [];
        $code_array_not_used = [];
        foreach ($goods_code_list as $code_) {
            if ($code_['goods_id'] > 0) {
                $code_array_used[] = $code_['code'];
            } else {
                $code_array_not_used[] = $code_['code'];
            }
        }
        //已有产品
        $goods_list = $db->table('goods_new')
            ->where('where is_app = 1')
            ->field('e_name')
            ->list();
        $goods_names = array_column($goods_list,'e_name');
        $excel_manage_arry = [];
        $excel_op_arry = [];
        foreach ($data as &$vv) {
            $vv['开发'] = trim($vv['开发']);
            $vv['运营人员'] = explode(',',str_replace('，',',',trim($vv['运营人员'])));
            $excel_manage_arry[] = $vv['开发'];
            $excel_op_arry = array_merge($excel_op_arry,$vv['运营人员']);
            $vv['功能名称(英文名)'] = explode(',',str_replace('，',',',trim($vv['功能名称(英文名)'])));
            $vv['颜色名称（英文）'] = explode(',',str_replace('，',',',trim($vv['颜色名称（英文）'])));
        }
        $excel_manage_arry = array_unique($excel_manage_arry);
        $excel_op_arry = array_unique($excel_op_arry);
        //开发，运维
        $kaifa_list = $db->table('qwuser')->whereIn('wname',$excel_manage_arry)
            ->field('id,wname,wid')->list();
        $yunyin_list = $db->table('qwuser')->whereIn('wname',$excel_op_arry)
            ->field('id,wname,wid')->list();
        $kifa_name_array = array_column($kaifa_list,'wname');
        $error_lsit = [];
        foreach ($data as $v) {
            if (in_array($v['英文名'],$goods_names)) {
                continue;
            }
            if (!in_array($v['开发'],$kifa_name_array)) {
                $error_lsit[] = ['error'=>'开发未找到','data'=>$v];
                continue;
            }
            if (in_array(trim($v['蓝牙编号']),$code_array_used)) {
                $error_lsit[] = ['error'=>'产品已存在,蓝牙已被用','data'=>$v];
                continue;
            }
            $insert_data = [
                'user_id'=>0,
                'alias_name'=>'',
                'goods_name'=>trim($v['中文名']),
                'e_name'=>trim($v['英文名']),
                'bluetooth'=>trim($v['蓝牙名']),
                'is_electriferous'=>(int)$v['是否带电(0否,1是)'],
                'description'=>trim($v['描述']),
                'is_app'=>1,
                'created_at'=>date('Y-m-d H:i:s'),
                'cat_id'=>'[]',
            ];
            //开发运维
            foreach ($kaifa_list as $v1) {
                if ($v1['wname'] == trim($v['开发'])) {
                    $insert_data['manage_info'] = json_encode([$v1],JSON_UNESCAPED_UNICODE);
                    break;
                }
            }
            $insert_data['operator_info'] = [];
            if (count($v['运营人员'])) {
                foreach ($v['运营人员'] as $o_name) {
                    foreach ($yunyin_list as $o) {
                        if ($o['wname'] == $o_name) {
                            $insert_data['operator_info'][] = $o;
                            break;
                        }
                    }
                }
            }
            $insert_data['operator_info'] = json_encode($insert_data['operator_info'],JSON_UNESCAPED_UNICODE);
            //颜色
            $v['insert_color'] = [];
            if (count($v['颜色名称（英文）'])) {
                foreach ($v['颜色名称（英文）'] as $c) {
                    $c_array = explode(':',str_replace('：',':',$c));
                    if (count($c_array) == 2) {
                        foreach ($color_list as $color) {
                            if ($color['color_name_en'] == $c_array[0]) {
                                $v['insert_color'][] = [
                                    'color_id'=>$color['id'],
                                    'sku'=>$c_array[1],
                                    'e_sku'=>$c_array[1],
                                    'created_time'=>date('Y-m-d H:i:s')
                                ];
                                break;
                            }
                        }
                    }
                }
            }
            if (!count($v['insert_color'])) {
                $error_lsit[] = ['error'=>'颜色sku没匹配到','data'=>$v];
                continue;
            }

            //功能
            $insert_data['function_id'] = [];
            if (count($v['功能名称(英文名)'])) {
                foreach ($v['功能名称(英文名)'] as $f) {
                    foreach ($function_list as $func) {
                        if ($func['fc_name_en'] == $f) {
                            $insert_data['function_id'][] = $func['id'];
                            break;
                        }
                    }
                }
            }
            $insert_data['function_id'] = json_encode($insert_data['function_id']);
            //供应商
            if (!empty($v['供应商名称'])) {
                foreach ($supplier_list as $sur) {
                    if (trim($v['供应商名称']) == $sur['supplier_name']) {
                        $insert_data['supplier_id'] = $sur['id'];
                        break;
                    }
                }
            }
            $db->beginTransaction();
            try {
                $goods_id = $db->table('goods_new')->insert($insert_data);
                if (in_array(trim($v['蓝牙编号']),$code_array_not_used)) {
                    $db->table('goods_code')
                        ->where('where code=:code',['code'=>trim($v['蓝牙编号'])])
                        ->update(['goods_id'=>$goods_id]);
                } else {
                    $db->table('goods_code')->insert(['code'=>trim($v['蓝牙编号']),'goods_id'=>$goods_id,'protocol_code'=>'']);
                }

                //颜色
                foreach ($v['insert_color'] as $c) {
                    $c['goods_id'] = $goods_id;
                    $db->table('goods_color_relation')
                        ->insert($c);
                }

                //商品采购信息
                $db->table('goods_purchase')
                    ->insert([
                        'user_id'=>0,
                        'goods_id'=>$goods_id,
                        'outer_box_info'=>trim($v['外箱规格(长*宽*高)']),
                        'outer_box_unit'=>(int)$v['外箱规格单位(1:cm,2:inch)']??1,
                        'pack_info'=>trim($v['产品包装规格(长*宽*高)']),
                        'pack_unit'=>(int)$v['产品包装规格单位(1:cm,2:inch)']??1,
                        'single_weight'=>(int)$v['单品毛重(g)']??1,
                        'number'=>(int)$v['单箱数量(个)']??1,
                        'weight'=>$v['单箱重量(kg)']??1,
                        'created_time'=>date('Y-m-d H:i:s'),
                    ]);
                $db->commit();
                $code_array_used[] = trim($v['蓝牙编号']);
            } catch (ExceptionError $error) {
                $error_lsit[] = ['error'=>$error->getMessage(),'data'=>$v];
                $db->rollBack();
            }

        }
        dd($error_lsit);
    }
    //导入产品信息(非app产品)
    public function exelNoAppGoodsInfo() {
        returnSuccess('暂时不可用');
        $file = $_FILES['s'];
        //获取文件临时路径
        $temp_name = $file['tmp_name'];
        $excel_data = (new FastExcel)->import($temp_name);
        $data = $excel_data->toArray();
//        dd($data);
        if (!count($data)) {
            echo '没数量';die;
        }
        $db = dbMysql::getInstance();
        //功能
        $function_list = $db->table('goods_function')->list();
        //颜色
        $color_list = $db->table('goods_color')->list();
        //供应商
        $supplier_list = $db->table('goods_supplier')->list();
        //已有产品
        $goods_list = $db->table('goods_new')
            ->where('where is_app = 0')
            ->field('goods_name')
            ->list();
        $goods_names = array_column($goods_list,'goods_name');
        $excel_manage_arry = [];
        $excel_op_arry = [];
        foreach ($data as &$vv) {
            $vv['产品开发'] = trim($vv['产品开发']);
            $vv['运营人员'] = explode(',',str_replace('，',',',trim($vv['运营人员'])));
            $excel_manage_arry[] = $vv['产品开发'];
            $excel_op_arry = array_merge($excel_op_arry,$vv['运营人员']);
            $vv['功能'] = explode(',',str_replace('，',',',trim($vv['功能'])));
            $vv['颜色及SKU（不带遥控）'] = explode(',',str_replace('，',',',trim($vv['颜色及SKU（不带遥控）'])));
        }
        $excel_manage_arry = array_unique($excel_manage_arry);
        $excel_op_arry = array_unique($excel_op_arry);
        //开发，运维
        $kaifa_list = $db->table('qwuser')->whereIn('wname',$excel_manage_arry)
            ->field('id,wname,wid')->list();
        $yunyin_list = $db->table('qwuser')->whereIn('wname',$excel_op_arry)
            ->field('id,wname,wid')->list();
        $kifa_name_array = array_column($kaifa_list,'wname');
        $error_lsit = [];
        foreach ($data as $v) {
            if (in_array($v['产品名'],$goods_names)) {
//                $error_lsit[] = ['error'=>'产品已存在','data'=>$v];
                continue;
            }
            if (!in_array($v['产品开发'],$kifa_name_array)) {
                $error_lsit[] = ['error'=>'开发未找到','data'=>$v];
                continue;
            }
            $insert_data = [
                'user_id'=>0,
                'alias_name'=>'',
                'goods_name'=>trim($v['产品名']),
                'e_name'=>'',
                'bluetooth'=>'',
                'is_electriferous'=>(int)$v['1是带电；0是不带电'],
                'description'=>'',
                'is_app'=>0,
                'created_at'=>date('Y-m-d H:i:s'),
                'cat_id'=>'[]',
            ];
            //开发运维
            foreach ($kaifa_list as $v1) {
                if ($v1['wname'] == trim($v['产品开发'])) {
                    $insert_data['manage_info'] = json_encode([$v1],JSON_UNESCAPED_UNICODE);
                    break;
                }
            }
            $insert_data['operator_info'] = [];
            if (count($v['运营人员'])) {
                foreach ($v['运营人员'] as $o_name) {
                    foreach ($yunyin_list as $o) {
                        if ($o['wname'] == $o_name) {
                            $insert_data['operator_info'][] = $o;
                            break;
                        }
                    }
                }
            }
            $insert_data['operator_info'] = json_encode($insert_data['operator_info'],JSON_UNESCAPED_UNICODE);
            //颜色
            $v['insert_color'] = [];
            if (count($v['颜色及SKU（不带遥控）'])) {
                foreach ($v['颜色及SKU（不带遥控）'] as $c) {
                    $c_array = explode(':',str_replace('：',':',$c));
                    if (count($c_array) == 2) {
                        foreach ($color_list as $color) {
                            if ($color['color_name_en'] == $c_array[0]) {
                                $v['insert_color'][] = [
                                    'color_id'=>$color['id'],
                                    'sku'=>$c_array[1],
                                    'e_sku'=>$c_array[1],
                                    'created_time'=>date('Y-m-d H:i:s')
                                ];
                                break;
                            }
                        }
                    }
                }
            }
            if (!count($v['insert_color'])) {
                $error_lsit[] = ['error'=>'颜色sku没匹配到','data'=>$v];
                continue;
            }

            //功能
            $insert_data['function_id'] = [];
            if (count($v['功能'])) {
                foreach ($v['功能'] as $f) {
                    foreach ($function_list as $func) {
                        if ($func['fc_name_en'] == $f) {
                            $insert_data['function_id'][] = $func['id'];
                            break;
                        }
                    }
                }
            }
            $insert_data['function_id'] = json_encode($insert_data['function_id']);
            //供应商
            if (!empty($v['供应商'])) {
                foreach ($supplier_list as $sur) {
                    if (trim($v['供应商']) == $sur['supplier_name']) {
                        $insert_data['supplier_id'] = $sur['id'];
                        break;
                    }
                }
            }
            $db->beginTransaction();
            try {
                $goods_id = $db->table('goods_new')->insert($insert_data);
                //颜色
                foreach ($v['insert_color'] as $c) {
                    $c['goods_id'] = $goods_id;
                    $db->table('goods_color_relation')
                        ->insert($c);
                }
                //商品采购信息
                $db->table('goods_purchase')
                    ->insert([
                        'user_id'=>0,
                        'goods_id'=>$goods_id,
                        'outer_box_info'=>trim($v['外箱规格（长*宽*高）']),
                        'outer_box_unit'=>(int)$v['外箱规格单位(1:cm,2:inch)']??1,
                        'pack_info'=>trim($v['产品包装规格（长*宽*高）']),
                        'pack_unit'=>(int)$v['外箱规格单位(1:cm,2:inch)']??1,
                        'single_weight'=>(int)$v['单品毛重（g）']??1,
                        'number'=>(int)$v['单箱数量（个）']??1,
                        'weight'=>$v['单箱重量（kg）']??1,
                        'created_time'=>date('Y-m-d H:i:s'),
                    ]);
                $db->commit();
            } catch (ExceptionError $error) {
                $error_lsit[] = ['error'=>$error->getMessage(),'data'=>$v];
                $db->rollBack();
            }

        }
        dd($error_lsit);
    }
}