-- FBA库存明细表
CREATE TABLE IF NOT EXISTS `lingxing_fba_storage_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '仓库名',
  `sid` int(11) NOT NULL DEFAULT 0 COMMENT '店铺id',
  `asin` varchar(50) NOT NULL DEFAULT '' COMMENT 'ASIN',
  `product_name` varchar(500) NOT NULL DEFAULT '' COMMENT '品名',
  `small_image_url` text COMMENT '预览图链接',
  `seller_sku` varchar(100) NOT NULL DEFAULT '' COMMENT 'MSKU',
  `fnsku` varchar(50) NOT NULL DEFAULT '' COMMENT 'FNSKU',
  `sku` varchar(100) NOT NULL DEFAULT '' COMMENT 'SKU',
  `category_text` varchar(255) NOT NULL DEFAULT '' COMMENT '分类文本',
  `cid` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `product_brand_text` varchar(255) NOT NULL DEFAULT '' COMMENT '品牌文本',
  `bid` int(11) NOT NULL DEFAULT 0 COMMENT '品牌ID',
  `share_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '共享类型:0非共享,1北美共享,2欧洲共享',
  
  -- 库存数量相关字段
  `total` int(11) NOT NULL DEFAULT 0 COMMENT '总数',
  `total_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '总价',
  `available_total` int(11) NOT NULL DEFAULT 0 COMMENT '可用总数',
  `available_total_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '可用总数成本价',
  `afn_fulfillable_quantity` int(11) NOT NULL DEFAULT 0 COMMENT 'FBA可售',
  `afn_fulfillable_quantity_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT 'FBA可售成本价',
  `reserved_fc_transfers` int(11) NOT NULL DEFAULT 0 COMMENT '待调仓',
  `reserved_fc_transfers_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '待调仓成本价',
  `reserved_fc_processing` int(11) NOT NULL DEFAULT 0 COMMENT '调仓中',
  `reserved_fc_processing_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '调仓中成本价',
  `reserved_customerorders` int(11) NOT NULL DEFAULT 0 COMMENT '待发货',
  `reserved_customerorders_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '待发货成本价',
  `quantity` int(11) NOT NULL DEFAULT 0 COMMENT 'FBM可售',
  `quantity_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT 'FBM可售成本价',
  `afn_unsellable_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '不可售',
  `afn_unsellable_quantity_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '不可售成本价',
  `afn_inbound_working_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '计划入库',
  `afn_inbound_working_quantity_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '计划入库成本价',
  `afn_inbound_shipped_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '在途',
  `afn_inbound_shipped_quantity_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '在途成本价',
  `afn_inbound_receiving_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '入库中',
  `afn_inbound_receiving_quantity_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '入库中成本价',
  `stock_up_num` int(11) NOT NULL DEFAULT 0 COMMENT '实际在途',
  `stock_up_num_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '实际在途成本价',
  `afn_researching_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '调查中数量',
  `afn_researching_quantity_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '调查中数量成本价',
  `total_fulfillable_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '总可用库存',
  
  -- 库龄统计字段
  `inv_age_0_to_30_days` int(11) NOT NULL DEFAULT 0 COMMENT '0-1个月库龄',
  `inv_age_0_to_30_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '0-1个月库龄成本价',
  `inv_age_31_to_60_days` int(11) NOT NULL DEFAULT 0 COMMENT '1-2个月库龄',
  `inv_age_31_to_60_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '1-2个月库龄成本价',
  `inv_age_61_to_90_days` int(11) NOT NULL DEFAULT 0 COMMENT '2-3个月库龄',
  `inv_age_61_to_90_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '2-3个月库龄成本价',
  `inv_age_0_to_90_days` int(11) NOT NULL DEFAULT 0 COMMENT '0-3个月库龄',
  `inv_age_0_to_90_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '0-3个月库龄成本价',
  `inv_age_91_to_180_days` int(11) NOT NULL DEFAULT 0 COMMENT '3-6个月库龄',
  `inv_age_91_to_180_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '3-6个月库龄成本价',
  `inv_age_181_to_270_days` int(11) NOT NULL DEFAULT 0 COMMENT '6-9个月库龄',
  `inv_age_181_to_270_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '6-9个月库龄成本价',
  `inv_age_271_to_330_days` int(11) NOT NULL DEFAULT 0 COMMENT '9-11个月库龄',
  `inv_age_271_to_330_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '9-11个月库龄成本价',
  `inv_age_271_to_365_days` int(11) NOT NULL DEFAULT 0 COMMENT '9-12个月库龄',
  `inv_age_271_to_365_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '9-12个月库龄成本价',
  `inv_age_331_to_365_days` int(11) NOT NULL DEFAULT 0 COMMENT '11-12个月库龄',
  `inv_age_331_to_365_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '11-12个月库龄成本价',
  `inv_age_365_plus_days` int(11) NOT NULL DEFAULT 0 COMMENT '12个月以上库龄',
  `inv_age_365_plus_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '12个月以上库龄成本价',
  
  -- 其他业务字段
  `recommended_action` varchar(255) NOT NULL DEFAULT '' COMMENT '推荐操作',
  `sell_through` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT '售出率',
  `estimated_excess_quantity` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '预计冗余数量',
  `estimated_storage_cost_next_month` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '预计30天仓储费用',
  `fba_minimum_inventory_level` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '最低库存水平',
  `fba_inventory_level_health_status` varchar(100) NOT NULL DEFAULT '' COMMENT '库存水平健康度',
  `historical_days_of_supply` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT '历史供货天数',
  `historical_days_of_supply_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '历史供货天数成本价',
  `low_inventory_level_fee_applied` varchar(100) NOT NULL DEFAULT '' COMMENT '低库存水平费收取情况',
  `fulfillment_channel` varchar(50) NOT NULL DEFAULT '' COMMENT '配送方式',
  `fba_storage_quantity_list` text COMMENT 'FBA可售信息列表JSON',
  
  -- 系统字段
  `sync_date` date NOT NULL COMMENT '同步日期',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0否，1是',
  
  PRIMARY KEY (`id`),
  KEY `idx_sync_date` (`sync_date`),
  KEY `idx_asin` (`asin`),
  KEY `idx_seller_sku` (`seller_sku`),
  KEY `idx_fnsku` (`fnsku`),
  KEY `idx_sku` (`sku`),
  KEY `idx_sid` (`sid`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='FBA库存明细表';
