<?php
namespace plugins\logistics\form;

use core\lib\db\dbMysql;
use core\lib\ExceptionError;
use plugins\logistics\models\userModel;

class PrepareAuditForm
{
    /**
     * 提交审核
     * @param array $param 参数
     * @return void
     * @throws ExceptionError
     */
    public static function submitAudit(array $param): void
    {
        $db = dbMysql::getInstance();
        $db->beginTransaction();
        
        try {
            // 验证参数
            if (empty($param['title']) || empty($param['content'])) {
                throw new ExceptionError("标题和内容不能为空");
            }
            
            // 创建备货审核记录
            $auditData = [
                'title' => $param['title'],
                'content' => $param['content'],
                'amount' => $param['amount'] ?? 0,
                'creator_id' => userModel::$qwuser_id,
                'creator_name' => userModel::$wname,
                'approval_status' => 0, // 待审批
                'created_time' => date('Y-m-d H:i:s')
            ];
            
            $auditId = $db->table('prepare_audit')->insert($auditData);
            
            // 启动审批流程
            $businessData = [
                'amount' => $param['amount'] ?? 0,
            ];
            
            ApprovalForm::startApproval('prepare_audit', $auditId, $param['title'], $businessData);
            
            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            throw new ExceptionError("提交审核失败: " . $e->getMessage());
        }
    }
    
    /**
     * 获取审核列表
     * @param array $param 查询参数
     * @return array
     */
    public static function getAuditList(array $param): array
    {
        $db = dbMysql::getInstance();
        $page = $param['page'] ?? 1;
        $pageSize = $param['page_size'] ?? 20;
        $offset = ($page - 1) * $pageSize;
        
        $where = "WHERE 1=1";
        $params = [];
        
        // 根据状态筛选
        if (isset($param['status']) && $param['status'] !== '') {
            $where .= " AND approval_status = :status";
            $params['status'] = $param['status'];
        }
        
        // 根据创建人筛选
        if (isset($param['creator_id']) && $param['creator_id']) {
            $where .= " AND creator_id = :creator_id";
            $params['creator_id'] = $param['creator_id'];
        }
        
        // 根据日期范围筛选
        if (!empty($param['start_date'])) {
            $where .= " AND created_time >= :start_date";
            $params['start_date'] = $param['start_date'] . ' 00:00:00';
        }
        
        if (!empty($param['end_date'])) {
            $where .= " AND created_time <= :end_date";
            $params['end_date'] = $param['end_date'] . ' 23:59:59';
        }
        
        // 获取总数
        $countSql = "SELECT COUNT(*) AS total FROM prepare_audit $where";
        $totalCount = $db->query($countSql, $params)['total'] ?? 0;
        
        // 获取审核列表
        $sql = "SELECT
            id,
            title,
            content,
            amount,
            creator_id,
            creator_name,
            approval_status,
            created_time
        FROM
            prepare_audit
        $where
        ORDER BY
            created_time DESC
        LIMIT
            $offset, $pageSize";
        
        $auditList = $db->query($sql, $params);
        
        // 获取审批流程状态
        foreach ($auditList as &$audit) {
            $approvalStatus = ApprovalForm::getApprovalStatus('prepare_audit', $audit['id']);
            $audit['approval_info'] = $approvalStatus;
        }
        
        return [
            'total' => $totalCount,
            'list' => $auditList
        ];
    }
    
    /**
     * 获取审核详情
     * @param int $id 审核ID
     * @return array
     * @throws ExceptionError
     */
    public static function getAuditDetail(int $id): array
    {
        $db = dbMysql::getInstance();
        
        $audit = $db->table('prepare_audit')
            ->where('id = :id', ['id' => $id])
            ->one();
            
        if (!$audit) {
            throw new ExceptionError("审核记录不存在");
        }
        
        // 获取审批流程状态
        $approvalStatus = ApprovalForm::getApprovalStatus('prepare_audit', $id);
        $audit['approval_info'] = $approvalStatus;
        
        return $audit;
    }
    
    /**
     * 处理审批
     * @param array $param 参数
     * @return void
     * @throws ExceptionError
     */
    public static function processAudit(array $param): void
    {
        if (empty($param['flow_id'])) {
            throw new ExceptionError("缺少流程ID");
        }
        
        $action = isset($param['is_pass']) && $param['is_pass'] ? 1 : 2;
        $comment = $param['comment'] ?? '';
        
        ApprovalForm::processApproval($param['flow_id'], $action, $comment);
    }
    
    /**
     * 撤销审批
     * @param int $instanceId 审批实例ID
     * @return void
     * @throws ExceptionError
     */
    public static function cancelAudit(int $instanceId): void
    {
        ApprovalForm::cancelApproval($instanceId);
    }
}

