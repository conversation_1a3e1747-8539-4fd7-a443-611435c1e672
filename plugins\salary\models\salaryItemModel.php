<?php

namespace plugins\salary\models;

use core\lib\config;
use core\lib\db\dbMysql;
use core\lib\redisCached;

class salaryItemModel
{
    // 获取薪资项文本
    public static function check($detail)
    {
        if (!is_array($detail) && empty($detail)) returnError('薪资项详情不能为空');
        empty($detail['item_calc_method']) && returnError('计算方式不能为空');
        !in_array($detail['item_calc_method'], [1, 2]) && returnError('计算方式有误');
        empty($detail['range_type']) && returnError('适用范围不能为空');
        !in_array($detail['range_type'], [1, 2]) && returnError('适用范围有误');
        $detail['range_type'] == 2 && empty($detail['range_month']) && returnError('适用月份不能为空');

        empty($detail['rules']) && returnError('规则不能为空');

        foreach ($detail['rules'] as $rule) {
            empty($rule['department']) && returnError('适用部门不能为空');
            empty($rule['formula']) && returnError('薪资项公式不能为空');
            $idx = 0;
            foreach ($rule['formula'] as $item) {
                if ($detail['item_calc_method'] == 1) { // 公式
                    empty($item['type']) && returnError('公式类型不能为空');
                    !in_array($item['type'], [1, 2]) && returnError('公式类型有误');
                    $idx && empty($item['formula_symbol']) && returnError('公式符号不能为空');
                    if ($item['type'] == 1) { // 条件
                        empty($item['value_type']) && returnError('值类型不能为空');
                        !in_array($item['value_type'], [1, 2, 3]) && returnError('值类型有误');
                        $item['value_type'] == 3 && empty($item['value']) && returnError('自定义值不能为空');
                    } elseif ($item['type'] == 2) { // 条件组
                        empty($item['list']) && returnError('公式列表不能为空');
                        $subIdx = 0;
                        foreach ($item['list'] as $subItem) {
                            $subIdx && empty($subItem['formula_symbol']) && returnError('公式符号不能为空');
                            empty($subItem['value_type']) && returnError('值类型不能为空');
                            !in_array($subItem['value_type'], [1, 2, 3]) && returnError('值类型有误');
                            $subItem['value_type'] == 3 && empty($subItem['value']) && returnError('自定义值不能为空');
                            $subIdx++;
                        }
                    }
                }
                elseif ($detail['item_calc_method'] == 2) { // 阶梯
                    empty($item['rules']) && returnError('阶梯规则不能为空');
                    $subIdx = 0;
                    foreach ($item['rules'] as $rule) {
                        $subIdx && empty($rule['link_symbol']) && returnError('连接符号不能为空');
                        empty($rule['value_type']) && returnError('值类型不能为空');
                        !in_array($rule['value_type'], [1, 2]) && returnError('值类型有误');
                        empty($rule['stage_symbol']) && returnError('阶梯符号不能为空');
                        !in_array($rule['stage_symbol'], [1, 2, 3, 4, 5, 6]) && returnError('阶梯符号有误');
                        if ($rule['stage_symbol'] == 6) {
                            count($rule['value']) != 2 && returnError('阶梯值有误');
                        } else {
                            count($rule['value']) != 1 && returnError('阶梯值有误');
                        }
                        $subIdx++;
                    }
                    empty($item['result']) && returnError('阶梯结果不能为空');
                    $subIdx = 0;
                    foreach ($item['result'] as $result) {
                        $subIdx && empty($result['formula_symbol']) && returnError('公式符号不能为空');
                        empty($result['value_type']) && returnError('取数规则不能为空');
                        $subIdx++;
                    }
                    if (isset($item['highest_result'])) {
                        $item['highest_result'] < 0 && returnError('最高值不能为负数');
                    }
                }
                $idx++;
            }
        }


        empty($detail['calc_type']) && returnError('取数规则不能为空');
        in_array($detail['calc_type'], [1, 2]) && empty($detail['after_point']) && returnError('保留小数位数有误');

        return true;
    }

    // 获取薪资项文本
    public static function getText($detail, $departments = [])
    {
        if (empty($departments)) {
            // 部门信息
            $departments = redisCached::getDepartment();
            $departments = array_column($departments, null, 'wp_id');
        }
        if (!is_array($detail) && empty($detail)) return '';
        // 连接符号(用于阶梯)
        $link_symbol = config::get('link_symbol', 'data_salary');
        $link_symbol_map = array_column($link_symbol, 'name', 'id');
        // 薪资项符号(用于阶梯)
        $salary_item_stage_symbol = config::get('salary_item_stage_symbol', 'data_salary');
        $salary_item_stage_symbol_map = array_column($salary_item_stage_symbol, 'name', 'id');
        // 公式符号(+-*/)
        $formula_symbol = config::get('formula_symbol', 'data_salary');
        $formula_symbol_map = array_column($formula_symbol, 'name', 'id');
        // 值类型（用于公式）1考勤2删差评3自定义
        $salary_item_value_type = config::get('salary_item_value_type', 'data_salary');
        $salary_item_value_type_map = array_column($salary_item_value_type, 'name', 'id');
        $salary_item_value_type_1 = config::get('salary_item_value_type_1', 'data_salary');
        $salary_item_value_type_1_map = array_column($salary_item_value_type_1, 'name', 'id');
        $salary_item_value_type_2 = config::get('salary_item_value_type_2', 'data_salary');
        $salary_item_value_type_2_map = array_column($salary_item_value_type_2, 'name', 'id');

        // 阶梯值类型
        $salary_item_stage_rule_value_type = config::get('salary_item_stage_rule_value_type', 'data_salary');
        $salary_item_stage_rule_value_type_map = array_column($salary_item_stage_rule_value_type, 'name', 'id');

        // 阶梯结果值类型
        $salary_item_stage_result_value_type = config::get('salary_item_stage_result_value_type', 'data_salary');
        $salary_item_stage_result_value_type_map = array_column($salary_item_stage_result_value_type, 'name', 'id');

        $text = '';

        foreach ($detail['rules'] as $rule) {
            $department_name = [];
            foreach ($rule['department'] as $department_id) {
                $department_name[] = $departments[$department_id]['name'] ?? '';
            }
            $text = "适用部门：".implode(',', $department_name).", 规则：";
            // 按公式计算
            if ($detail['item_calc_method'] == 1) {
                $idx = 0;
                foreach ($rule['formula'] as $item) {
                    if ($idx) $text .= $formula_symbol_map[$item['formula_symbol']];
                    // 条件
                    if ($item['type'] == 1) {
                        if ($item['value_type'] == 3) { // 自定义值
                            $text .= $item['value'];
                        } else {
                            $value_map = $item['value_type'] == 1 ? $salary_item_value_type_1_map : $salary_item_value_type_2_map;
                            $is_abs = $item['is_abs'] ? '的绝对值' : '';
                            $text .= "{$salary_item_value_type_map[$item['value_type']]}{$value_map[$item['value']]}{$is_abs}";
                        }
                    } // 条件组
                    elseif ($item['type'] == 2) {
                        $text .= '(';
                        $sub_idx = 0;
                        foreach ($item['list'] as $subItem) {
                            $sub_idx && $text .= $formula_symbol_map[$subItem['formula_symbol']];
                            if ($subItem['value_type'] == 3) { // 自定义值
                                $text .= $subItem['value'];
                            } else {
                                $value_map = $subItem['value_type'] == 1 ? $salary_item_value_type_1_map : $salary_item_value_type_2_map;
                                $is_abs = $subItem['is_abs'] ? '的绝对值' : '';
                                $text .= "{$salary_item_value_type_map[$subItem['value_type']]}{$value_map[$subItem['value']]}{$is_abs}";
                            }
                            $sub_idx++;
                        }
                        $text .= ")";
                    }
                    $idx++;
                }
            } // 按阶段划分
            elseif ($detail['item_calc_method'] == 2) {
                foreach ($rule['formula'] as $item) {
                    $idx = 0;
                    foreach ($item['rules'] as $r) {
                        $idx && $text .= $link_symbol_map[$r['link_symbol']];
                        $column_name = $salary_item_stage_rule_value_type_map[$r['value_type']];
                        if ($item['stage_symbol'] == 6) {
                            $text .= "{$column_name}{$salary_item_stage_symbol_map[$r['stage_symbol']]}[{$r['value'][0]},{$r['value'][1]}] ";
                        } else {
                            $text .= "{$column_name}{$salary_item_stage_symbol_map[$r['stage_symbol']]}{$r['value'][0]} ";
                        }
                        $idx++;
                    }

                    $idx = 0;
                    $text .= ", 则结果=";
                    foreach ($item['result'] as $result) {
                        $idx && $text .= $formula_symbol_map[$result['formula_symbol']];
                        if ($result['value_type'] == 3) { // 自定义值
                            $text .= $result['value'];
                        } else {
                            $text .= $salary_item_stage_result_value_type_map[$result['value_type']];
                        }
                        $idx++;
                    }
                }
                $text .= ";";
            }
        }

        return $text;
    }

    public static function getResult($detail, $user_attach)
    {
        // 连接符号(用于阶梯)
        $link_symbol = config::get('link_symbol', 'data_salary');
        $link_symbol_map = array_column($link_symbol, 'value', 'id');
        // 薪资项符号(用于阶梯)
        $salary_item_stage_symbol = config::get('salary_item_stage_symbol', 'data_salary');
        $salary_item_stage_symbol_map = array_column($salary_item_stage_symbol, 'value', 'id');
        // 公式符号(+-*/)
        $formula_symbol = config::get('formula_symbol', 'data_salary');
        $formula_symbol_map = array_column($formula_symbol, 'name', 'id');

        $result = 0;
        foreach ($detail['rules'] as $rule) {
            $user_department = $user_attach['user_info']['user_wmain_department'] ?? 0;
            if (!in_array($user_department, $rule['department'])) {
                continue;
            }
            // 按公式计算
            if ($detail['item_calc_method'] == 1) {
                $idx = 0;
                $expression = '';
                foreach ($rule['formula'] as $item) {
                    if ($idx) $expression .= $formula_symbol_map[$item['formula_symbol']];
                    // 条件
                    if ($item['type'] == 1) {
                        $expression .= self::getItemValue($item['value_type'], $item['value'], $item['is_abs'], $user_attach);
                    } // 条件组
                    elseif ($item['type'] == 2) {
                        $expression .= '(';
                        $sub_idx = 0;
                        foreach ($item['list'] as $subItem) {
                            $sub_idx && $expression .= $formula_symbol_map[$subItem['formula_symbol']];
                            $expression .= self::getItemValue($subItem['value_type'], $subItem['value'], $subItem['is_abs'], $user_attach);
                            $sub_idx++;
                        }
                        $expression .= ")";
                    }
                    $idx++;
                }
                $result = eval("return $expression;");
            } // 按阶段划分
            elseif ($detail['item_calc_method'] == 2) {
                foreach ($rule['formula'] as $item) {
                    $idx = 0;
                    $expression = '';
                    foreach ($item['rules'] as $r) {
                        $idx && $expression .= $link_symbol_map[$r['link_symbol']];
                        $item_value = self::getStageItemValue($r['value_type'], $r['value'], $user_attach);

                        if ($r['stage_symbol'] == 6) {
                            $expression .= "({$item_value} >= {$r['value'][0]} && {$item_value} <= {$r['value'][1]})";
                        } else {
                            $expression .= $item_value . $salary_item_stage_symbol_map[$r['stage_symbol']] . $r['value'][0];
                        }
                        $idx++;
                    }

                    $flag = eval("return $expression;");
                    if ($flag) {
                        $rule_result = '';
                        $idx_result = 0;
                        $rule_result_expression = '';

                        foreach ($item['result'] as $result_item) {
                            $idx_result && $rule_result_expression .= $formula_symbol_map[$result_item['formula_symbol']];
                            $rule_result_expression .= self::getStageResultItemValue($result_item['value_type'], $result_item['value'], $user_attach);
                            $idx_result++;
                        }
                        eval("\$rule_result = $rule_result_expression;");
                        if (!empty($rule_result)) {
                            if (!empty($item['highest_result'])) {
                                $rule_result = min($rule_result, $item['highest_result']);
                            }
                        }
                        $result = max($result, $rule_result);
                    }

                }
            }
        }

        // 取数规则
        switch ($detail['calc_type']) {
            case 1: // 原始数据
                for ($i = 0; $i < $detail['after_point']; $i++) {
                    $result = $result * 10;
                }
                $result = floor($result);
                for ($i = 0; $i < $detail['after_point']; $i++) {
                    $result = $result / 10;
                }
                $result = number_format($result, $detail['after_point'],'.','');
                break;
            case 2: // 四舍五入
                $result = round($result, $detail['after_point']);
                break;
            case 3: // 向上取整
                $result = ceil($result);
                break;
            case 4: // 向下取整
                $result = floor($result);
                break;
        }

        return $result;
    }

    // 公式值
    public static function getItemValue($value_type, $value, $is_abs, $data)
    {
        $ret_value = null;
        switch ($value_type) {
            case 1: // 考勤
                switch ($value) {
                    case 1: // 实际出勤天数
                        $ret_value =  $data['checkin']['summary_info']['actual_attendance_day'] ?? 0;
                        break;
                    case 2: // 应出勤天数
                        $ret_value = $data['checkin']['summary_info']['should_attendance_day'] ?? 0;
                        break;
                    case 3: // 满勤天数
                        $ret_value = $data['checkin']['summary_info']['full_attendance_day'] ?? 0;
                        break;
                }
                break;
            case 2: // 删差评
                switch ($value) {
                    case 1: // 删差评数
                        $ret_value = $data['comment_num'] ?? 0;
                        break;
                }
                break;
            case 3: // 自定义
                $ret_value = $value;
                break;
        }
        $is_abs && $ret_value = abs($ret_value);
        return $ret_value;
    }

    public static function getStageItemValue($value_type, $value, $data)
    {
        $ret_value = null;
        switch ($value_type) {
            case 1:
                $ret_value = $data['user_info']['corp_in_age']['year'] ?? 0;
                break;
        }
        return $ret_value;
    }

    public static function getStageResultItemValue($value_type, $value, $data)
    {
        $ret_value = null;
        switch ($value_type) {
            case 1: // 工龄
                $ret_value = $data['user_info']['corp_in_age']['year'] ?? 0;
                break;
            case 11: // 实际出勤天数
                $ret_value = $data['checkin']['summary_info']['actual_attendance_day'] ?? 0;
                break;
            case 12: // 应出勤天数
                $ret_value = $data['checkin']['summary_info']['should_attendance_day'] ?? 0;
                break;
            case 13: // 满勤天数
                $ret_value = $data['checkin']['summary_info']['full_attendance_day'] ?? 0;
                break;
            case 21: // 基本工资
                $ret_value = $data['salary']['base_salary'] ?? 0;
                break;
            case 22: // 综合工资
                $ret_value = $data['salary']['total_salary'] ?? 0;
                break;
            case 3: // 自定义
                $ret_value = $value ?? 0;
                break;
        }
        return $ret_value;
    }

}