<?php
/**
 * @author: zhangguoming
 * @Time: 2024/3/28 11:32
 */

namespace  plugins\goods\form;

use core\lib\db\dbMysql;

class nodeFrom
{
    public static function getEventDetail($event_ids,$event_list) {
        $db = dbMysql::getInstance();
        $db->table('event');
        $db->field('id,event_name,event_type,manage_info,description,need_check,check_user_info,send_copy_user,expected_day,is_advance_submit,delay_hour,is_developer');
        $db->whereIn('id',$event_ids);
        $event_data = $db->list();
        $event_new_list = [];
        $hardware_count = 0;
        foreach ($event_data as $event) {
            //默认值添加
            $event['begin_time'] = '';
            $event['status'] = 0;
            $event['complete_time'] = '';
            $event_new_list[$event['id']] = $event;
            if ($event['event_type'] == 4) {
                $hardware_count++;
            }
        }
        if ($hardware_count > 1) {
            SetReturn(-1,'一个节点下面不能存在多个硬件检测事件');
        }
        $event_detail = [];
        foreach ($event_list as $v1) {
            $event_item = [];
            foreach ($v1 as $v2) {
                $event_item[] = $event_new_list[$v2['id']];
            }
            $event_detail[] = $event_item;
        }
        return $event_detail;
    }
}