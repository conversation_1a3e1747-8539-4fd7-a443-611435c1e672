<?php
/**
 * Created by 漠山.
 * @purpose 描述
 * @Author: 漠山
 * @Time: 2023/12/3 16:19
 */

if (!function_exists("dd")) {
    function dd($var){
        //ob_end_clean();
        header('Content-Type:application/json; charset=utf-8');
        print_r($var);die;
    }
}
if (!function_exists("publish")) {
    function publish($url){
        if (!SYS_PUBLISH) {
            return;
        } else {
            if ($url == 'admin/login/login') {
                $user_name = $_POST['account']??'';
                if (!in_array($user_name,['张国明','张耘荧','廖亮晶'])) {
                    SetReturn(10,'系统升级中');
                }
            }
        }
    }
}
if (!function_exists('getuniqId')) {
    function getuniqId($prefix = ''){
        return uniqid($prefix);
    }
}


if (!function_exists('getallheaders')) {
    function getallheaders() {
        foreach ($_SERVER as $name => $value) {
            if (substr($name, 0, 5) == 'HTTP_') {
                $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
            }
        }
        return $headers;
    }
}

//将数组内的数据全转未自己想要的类型(一维数组)
function getArryForType($data, $type = 'int') {
    switch ($type){
        case 'int';
            foreach ($data as &$v) {
                $v = (int)$v;
            }
            break;
        case 'string';
            foreach ($data as &$v) {
                $v = (string)$v;
            }
            break;
    }
    return $data;
}

// 获取IP
function GetIP(){
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        $IP = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $IP = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        $IP = $_SERVER['REMOTE_ADDR'];
    }

    return $IP;
}

function getPwdMd5($password, $uni_code){
    return substr(md5($password.$uni_code), 0,20);
}

function getUrlBase64($data){
    $path = SELF_FK.$data['src'];
    $data = file_get_contents($path);
    $base64Data = base64_encode($data);
    return $base64Data;
}

function getFileType($extension) {
    if (in_array($extension ,['jpg', 'png'])) {
        return 3;
    } elseif (in_array($extension ,['mp4'])) {
        return 2;
    } elseif (in_array($extension ,['mp3'])){
        return 4;
    } else {
        return 1;
    }
}

if (!function_exists('requestHttp')) {
    function requestHttp($url, $type="get", $data='{}'){
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        if ($type == 'post') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        } else {
            curl_setopt($ch, CURLOPT_POST, false);
        }
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);//设置为FALSE 禁止 cURL 验证对等证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT_MS, 5000);
        $response = curl_exec($ch);
        $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($code) {
            return json_decode($response,true);
        } else {
            return '';
        }
    }
}

if (!function_exists('requestLingXing')) {
    function requestLingXing($url, $data='{}', $type="POST"){
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        if ($type == 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Content-Type: application/json'
                )
            );
        } else {
            curl_setopt($ch, CURLOPT_POST, false);
        }
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);//设置为FALSE 禁止 cURL 验证对等证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_TIMEOUT, 20);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT_MS, 60000);
        $response = curl_exec($ch);
        $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($code) {
            return json_decode($response,true);
        } else {
            return '';
        }
    }
}

//openssl_encrypt 加密
function getSecretCode($data){
    $method = "AES-256-CBC"; // 加密算法和模式
    $key = "redis_qw_message_url_data"; // 加密密钥
    $salt = openssl_random_pseudo_bytes(8); // 随机生成盐值
    $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length($method)); // 生成初始化向量
    $encrypted = openssl_encrypt($data, $method, $key, OPENSSL_RAW_DATA, $iv);
    // 将盐值、初始化向量和加密数据拼接在一起，以便后续解密时使用
    $encryptedData = base64_encode($salt . $iv . $encrypted);
    return $encryptedData;
}
//解码
if (!function_exists('qwMsgDecryption')) {
    function qwMsgDecryption($encryptedData){
        $method = "AES-256-CBC"; // 加密算法和模式
        $key = "redis_qw_message_url_data"; // 加密密钥
        $data = base64_decode($encryptedData);
        $iv = substr($data, 8, openssl_cipher_iv_length($method));
        $encrypted = substr($data, 8 + openssl_cipher_iv_length($method));
        $decrypted = openssl_decrypt($encrypted, $method, $key, OPENSSL_RAW_DATA, $iv);
        return $decrypted;
    }
}

//二维数据去重
function arrayUnique2($array,$key) {
    // 去重逻辑
    $deduplicatedArray = array();
    foreach ($array as $item) {
        // 假设每个子数组都有一个唯一的标识符，这里使用 'id' 键作为示例
        $id = $item[$key];
        // 检查标识符是否已存在于去重后的数组中
        if (!in_array($id, array_column($deduplicatedArray, $key))) {
            $deduplicatedArray[] = $item;
        }
    }
    return $deduplicatedArray;
}

//去除某一列
function removeColumn(&$array, $column_key) {
    return array_map(function ($element) use ($column_key) {
        unset($element[$column_key]);
        return $element;
    }, $array);
}

//根据地址生成压缩包
function setZipByUrl($array, $path) {
    $zipFile = SELF_FK . $path;
    $zip = new \ZipArchive();
    if ($zip->open($zipFile, \ZipArchive::CREATE) !== TRUE) {
        exit("Cannot open <$zipFile>\n");
    }
    foreach ($array as $v) {
        $file_dir = SELF_FK.$v;
        $zip->addFile($file_dir, basename($file_dir));
    }
    $zip->close();
    return true;
}


//图片植入内容
function embedTextInImage($inputImagePath, $outputImagePath, $hiddenText) {
    $imageType = mime_content_type($inputImagePath);
    $real_suffix = explode('/',$imageType)[1];
    // 加载原始图像
    switch ($real_suffix) {
        case 'png':
            $image = imagecreatefrompng($inputImagePath);
            break;
        case 'gif':
            $image = imagecreatefromgif($inputImagePath);
            break;
        case 'jpg':
        case 'jpeg':
            $image = imagecreatefromjpeg($inputImagePath);
            break;
        default:
            return false;
    }
    // 获取图片的宽度和高度
    $width = imagesx($image);
    $height = imagesy($image);

    // 将要植入的内容转换为二进制
    $binaryText = '';
    for ($i = 0; $i < strlen($hiddenText); $i++) {
        $binaryText .= sprintf("%08b", ord($hiddenText[$i]));
    }

    // 检查植入的内容是否过长
    if (strlen($binaryText) > $width * $height) {
        die("Hidden text is too long to be embedded in the image.");
    }

    // 遍历每个像素点，植入内容
    $index = 0;
    for ($x = 0; $x < $width; $x++) {
        for ($y = 0; $y < $height; $y++) {
            $rgb = imagecolorat($image, $x, $y);
            $r = ($rgb >> 16) & 0xFF;
            $g = ($rgb >> 8) & 0xFF;
            $b = $rgb & 0xFF;

            // 获取要植入的二进制位
            $bit = isset($binaryText[$index]) ? $binaryText[$index] : 0;

            // 植入内容到 RGB 值的最低有效位
            $r = ($r & 0xFE) | $bit;
            $g = ($g & 0xFE) | $bit;
            $b = ($b & 0xFE) | $bit;

            // 更新像素点的 RGB 值
            $newRgb = ($r << 16) | ($g << 8) | $b;
            imagesetpixel($image, $x, $y, $newRgb);

            // 更新植入的内容的索引
            $index++;

            // 如果已经植入完整个内容，跳出循环
            if ($index >= strlen($binaryText)) {
                break 2;
            }
        }
    }
    // 保存修改后的图片
    switch ($real_suffix) {
        case 'png':
            imagepng($image, $outputImagePath);
            break;
        case 'gif':
            imagegif($image, $outputImagePath);
            break;
        case 'jpg':
        case 'jpeg':
            imagejpeg($image, $outputImagePath);
            break;
        default:
            return false;
    }

    // 销毁图片资源
    imagedestroy($image);
    return true;
}

//解析图片
function extractTextFromImage($imagePath,$hiddenTextLength) {
    // 加载图片
    $imageType = mime_content_type($imagePath);
    $real_suffix = explode('/',$imageType)[1];
    // 加载原始图像
    switch ($real_suffix) {
        case 'png':
            $image = imagecreatefrompng($imagePath);
            break;
        case 'gif':
            $image = imagecreatefromgif($imagePath);
            break;
        case 'jpg':
        case 'jpeg':
            $image = imagecreatefromjpeg($imagePath);
            break;
        default:
            return false;
    }
    // 获取图片的宽度和高度
    $width = imagesx($image);
    $height = imagesy($image);

    // 解析图片植入的内容
    $extractedText = '';

    // 遍历每个像素点，提取植入的内容
    // 将要植入的内容转换为二进制
    $index = 0;
    for ($x = 0; $x < $width; $x++) {
        for ($y = 0; $y < $height; $y++) {
            $rgb = imagecolorat($image, $x, $y);
            $r = ($rgb >> 16) & 0xFF;
            $g = ($rgb >> 8) & 0xFF;
            $b = $rgb & 0xFF;

            // 提取 RGB 值的最低有效位
            $bit = $r & 1;

            // 将二进制位转换为字符
            $extractedText .= $bit;

            // 更新植入的内容的索引
            $index++;
            if ($index >= $hiddenTextLength*8) {
                break 2;
            }
        }
    }


    //二进制文本转换为可见文本
    $extractedText = str_split($extractedText, 8);
    $extractedText = array_map('bindec', $extractedText);
    $extractedText = array_map('chr', $extractedText);
    $extractedText = implode('', $extractedText);

    // 返回提取的文本
    return $extractedText;
}
//财务系统生成费用单上传的单号
function getFinancialShareNo($id) {
    // 设置前缀
    $prefix = 'OAFY';
    $date = date('ymd');
    $padding = 6;
    $sequenceNumber = str_pad($id, $padding, '0', STR_PAD_LEFT);
    $code = $prefix . $date . $sequenceNumber;
    return $code;

}
//四舍五入保留两位数字，并且转为字符串
function roundToString(float $num,int $decimals=2,string $decimals_sep='.') {
    $res_num = round($num,$decimals);
    $res_num = number_format($res_num, $decimals, '.','');
    if ($num < 0 && $res_num =='0' ) {
        $res_num = '-'.$res_num;
    }
    return $res_num;
}
//向上取单位转换为万切保留两位有效数字
//function numToTenThousand(float $num,int $decimals=2,string $decimals_sep='.') {
//    $res_num = round($num/10000,2);
//    $res_num = number_format($res_num, 2, '.','');
//    return $res_num;
//}
//获取字符串中的数字
function extractNumberFromString($str) {
    // 使用正则表达式匹配数字部分
    if (preg_match('/(-?\d+(\.\d+)?)/', $str, $matches)) {
        // 返回匹配到的数字部分（不包括%）
        return $matches[0];
    }
    // 如果没有匹配到，返回null或其他适当的值
    return 0;
}


function deleteFolder($folderPath) {
    if (!is_dir($folderPath)) {
        return false;
    }
    // 打开文件夹
    $dirHandle = opendir($folderPath);
    if (!$dirHandle) {
        return false;
    }

    // 遍历文件夹中的所有文件和子文件夹
    while ($file = readdir($dirHandle)) {
        if ($file !== '.' && $file !== '..') {
            $filePath = $folderPath . DIRECTORY_SEPARATOR . $file;
            if (is_dir($filePath)) {
                // 如果是子文件夹，递归删除
                deleteFolder($filePath);
            } else {
                // 如果是文件，直接删除
                unlink($filePath);
            }
        }
    }

    // 关闭文件夹句柄
    closedir($dirHandle);

    // 最后删除空文件夹本身
    rmdir($folderPath);

    return true;
}

function getTableSearchAggregationKeys($aggregation_keys_list) {
    $aggregation_keys = [];
    foreach ($aggregation_keys_list as $v) {
        if ($v == 'key8') {//vat
            $aggregation_keys[] = 'key8';
        } elseif($v == 'key14') {//海外推广费
            $aggregation_keys[] = 'key19';
        } elseif($v == 'key13') {//福利政策
            $aggregation_keys = array_merge($aggregation_keys,['key13','key14','key18']);
        } elseif($v == 'key6') {//预留资金
            $aggregation_keys[] = 'key6';
        }
    }
    return $aggregation_keys;
}

//表格查询获取运营权限对应人员和项目sql
function getYunyingSql($yunying_ids,$project_ids,$user_id,$table = '') {
    $yunying_sql = '';
    $project_sql = '';
    if (count($yunying_ids)) {
        $yunying_sql_data = [];
        foreach ($yunying_ids as $project_id=>$yunying_ids) {
            if (count($yunying_ids)) {
                foreach ($yunying_ids as $yunying_id) {
                    $yunying_sql_data[] = '('.(int)$project_id.','.(int)$yunying_id.')';
                }
            }
        }
        if (!empty($table)) {
            $yunying_sql = "($table.project_id,$table.yunying_id) in (".implode(',',$yunying_sql_data).')';
        } else {
            $yunying_sql = '(project_id,yunying_id) in ('.implode(',',$yunying_sql_data).')';
        }
    }
    if (count($project_ids)) {
        if (!empty($table)) {
            $project_sql = "$table.project_id in (".implode(',',getArryForType($project_ids)).')';
        } else {
            $project_sql = 'project_id in ('.implode(',',getArryForType($project_ids)).')';
        }
    }
    if (empty($project_sql) && empty($yunying_sql)) {
        return 'yunying_id = '.(int)$user_id;
    } else {
        $sql = 'yunying_id = '.(int)$user_id;
        if (!empty($yunying_sql)) {
            $sql .= ' or '.$yunying_sql;
        }
        if (!empty($project_sql)) {
            $sql .= ' or '.$project_sql;
        }
        return '('.$sql.')';
    }

}


// 构建树
function buildTree(array &$projects, $parentId = 0, $id = 'id', $p_id = 'p_id') : array {
    $tree = [];

    foreach ($projects as $key => &$project) {
        if ($project[$p_id] == $parentId) {
            // 递归查找子项目
            $children = buildTree($projects, $project[$id], $id, $p_id);
            if ($children) {
                $project['children'] = $children;
            }
            $tree[] = $project;
            // 从原始数组中移除已经处理的项目
            unset($projects[$key]);
        }
    }

    return $tree;
}

// 校验时间段是否有交集
function checkTimeHasIntersect($start_time1, $end_time1, $start_time2, $end_time2) :bool
{
    // 俩区间相交的情况：max(A.start, B.start) <= min(A.end, B.end)
    // 俩区间不相交的情况：A.end < B.start || A.start > B.end
    $min_end_time = min($end_time1, $end_time2);
    $max_start_time = max($start_time1, $start_time2);
    if ($max_start_time <= $min_end_time) {
        // 有交集
        return true;
    }
    return false;
}

// 从根节点开始组装部门路径
function getDepartmentRoute($departments, $departmentId, &$departmentRoute = []) : bool
{
    foreach ($departments as $department) {
        if ($department['wp_id'] == $departmentId) {
            $departmentRoute[] = $department;
            getDepartmentRoute($departments, $department['qw_parentid'], $departmentRoute);
        }
    }
    return true;
}

// 递归获取所有子节点 ID
function getAllChildIds($node, $id = 'id', $children = 'children') {
    // 初始化结果数组，包含当前节点的 ID
    $result = [$node[$id]];

    // 如果有子节点，递归处理每个子节点
    if (isset($node[$children]) && is_array($node[$children])) {
        foreach ($node[$children] as $child) {
            // 将子节点的所有 ID 合并到结果中
            $result = array_merge($result, getAllChildIds($child, $id, $children));
        }
    }

    return $result;
}

// 递归查找所有子节点
function findAllChildren($data, $targetId, $id = 'id', $children = 'children') {
    foreach ($data as $node) {
        // 检查当前节点是否为目标节点
        if ($node[$id] == $targetId) {
            // 找到目标节点，获取其所有子节点 ID
            return getAllChildIds($node, $id, $children);
        }

        // 如果有子节点，递归搜索子节点
        if (isset($node[$children]) && is_array($node[$children])) {
            $result = findAllChildren($node[$children], $targetId, $id, $children);
            if (!empty($result)) {
                return $result;
            }
        }
    }
    return [];
}

























