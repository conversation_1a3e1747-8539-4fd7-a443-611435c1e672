<?php
/**
 * 海外仓备货单模型单元测试
 * @purpose 测试海外仓备货单模型功能
 * @Author: System
 * @Time: 2025/06/24
 */

require_once __DIR__ . '/../../../core/fk.php';

use plugins\logistics\models\overseasInboundModel;
use PHPUnit\Framework\TestCase;

class overseasInboundModelTest extends TestCase
{
    private $model;
    private $testData;

    public function setUp(): void
    {
        $this->model = new overseasInboundModel();
        
        // 测试数据
        $this->testData = [
            [
                'overseas_order_no' => 'TEST_OWS_001',
                'inbound_order_no' => 'TEST_IC_001',
                'customer_reference_no' => 'TEST_REF_001',
                's_wid' => 1,
                's_wname' => '测试发货仓',
                'r_wid' => 2,
                'r_wname' => '测试收货仓',
                'logistics_id' => 100,
                'logistics_name' => '测试物流',
                'remark' => '测试备注',
                'status' => 10,
                'rollback_remark' => '',
                'is_delete' => 0,
                'uid' => 1,
                'create_user' => '测试用户',
                'update_user' => '测试用户',
                'create_time' => '2025-06-24 10:00:00',
                'estimated_time' => '2025-06-30 10:00:00',
                'audit_handle_time' => null,
                'send_good_handle_time' => null,
                'receive_good_handle_time' => null,
                'real_delivery_time' => null,
                'update_time' => '2025-06-24 10:00:00',
                'products' => [
                    [
                        'product_id' => 1001,
                        'sku' => 'TEST-SKU-001',
                        'product_name' => '测试商品1',
                        'fnsku' => 'TEST-FNSKU-001',
                        'stock_num' => 100,
                        'receive_num' => 0
                    ]
                ],
                'logistics' => [
                    [
                        'logistics_order_no' => 'TEST-LOG-001',
                        'track_order_no' => 'TEST-TRACK-001',
                        'logistics_money' => 100.00,
                        'logistics_money_unit' => 'CNY'
                    ]
                ],
                'logistics_list_type' => 0,
                'head_logistics_list' => [
                    'tracking_list' => [],
                    'estimate_expenses_list' => null,
                    'actual_expenses_list' => null
                ]
            ]
        ];
    }

    /**
     * 测试保存备货单数据
     */
    public function testSaveInboundData()
    {
        try {
            $result = $this->model->saveInboundData($this->testData);
            $this->assertGreaterThan(0, $result, '保存备货单数据应该返回成功数量');
            echo "✓ 保存备货单数据测试通过\n";
        } catch (Exception $e) {
            $this->fail('保存备货单数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试根据备货单号获取详情
     */
    public function testGetInboundByOrderNo()
    {
        try {
            // 先保存测试数据
            $this->model->saveInboundData($this->testData);
            
            $result = $this->model->getInboundByOrderNo('TEST_OWS_001');
            $this->assertNotEmpty($result, '应该能根据备货单号查询到数据');
            $this->assertEquals('TEST_OWS_001', $result['overseas_order_no'], '备货单号应该匹配');
            $this->assertIsArray($result['products'], '商品信息应该是数组');
            $this->assertIsArray($result['logistics'], '物流信息应该是数组');
            echo "✓ 根据备货单号获取详情测试通过\n";
        } catch (Exception $e) {
            $this->fail('根据备货单号获取详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试获取备货单列表
     */
    public function testGetInboundList()
    {
        try {
            // 先保存测试数据
            $this->model->saveInboundData($this->testData);
            
            $params = [
                'page' => 1,
                'page_size' => 10,
                'status' => 10
            ];
            
            $result = $this->model->getInboundList($params);
            $this->assertIsArray($result, '返回结果应该是数组');
            $this->assertArrayHasKey('list', $result, '结果应该包含list字段');
            $this->assertArrayHasKey('total', $result, '结果应该包含total字段');
            $this->assertGreaterThanOrEqual(0, $result['total'], '总数应该大于等于0');
            echo "✓ 获取备货单列表测试通过\n";
        } catch (Exception $e) {
            $this->fail('获取备货单列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试更新可编辑字段
     */
    public function testUpdateEditableFields()
    {
        try {
            // 先保存测试数据
            $this->model->saveInboundData($this->testData);
            
            // 获取刚保存的记录
            $inbound = $this->model->getInboundByOrderNo('TEST_OWS_001');
            $this->assertNotEmpty($inbound, '应该能查询到刚保存的记录');
            
            $updateData = [
                'transparent_label' => '测试透明标',
                'warehouse_code' => 'TEST_WH_CODE_001',
                'shop_code' => 'TEST_SHOP_001',
                'fnsku' => 'TEST_FNSKU_UPDATE',
                'remaining_available' => 50,
                'shipping_remark' => '测试发货备注',
                'other_remark' => '测试其他备注'
            ];
            
            $result = $this->model->updateEditableFields($inbound['id'], $updateData);
            $this->assertGreaterThan(0, $result, '更新应该影响至少1行');
            
            // 验证更新结果
            $updatedInbound = $this->model->getInboundDetail($inbound['id']);
            $this->assertEquals('测试透明标', $updatedInbound['transparent_label'], '透明标应该更新成功');
            $this->assertEquals('TEST_WH_CODE_001', $updatedInbound['warehouse_code'], '入仓编码应该更新成功');
            $this->assertEquals(50, $updatedInbound['remaining_available'], '剩余可用应该更新成功');
            echo "✓ 更新可编辑字段测试通过\n";
        } catch (Exception $e) {
            $this->fail('更新可编辑字段失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试删除备货单
     */
    public function testDeleteInbound()
    {
        try {
            // 先保存测试数据
            $this->model->saveInboundData($this->testData);
            
            // 获取刚保存的记录
            $inbound = $this->model->getInboundByOrderNo('TEST_OWS_001');
            $this->assertNotEmpty($inbound, '应该能查询到刚保存的记录');
            
            $result = $this->model->deleteInbound($inbound['id']);
            $this->assertGreaterThan(0, $result, '删除应该影响至少1行');
            
            // 验证删除结果 - 应该查询不到未删除的记录
            $params = ['overseas_order_no' => 'TEST_OWS_001', 'is_delete' => 0];
            $listResult = $this->model->getInboundList($params);
            $this->assertEquals(0, $listResult['total'], '删除后应该查询不到未删除的记录');
            echo "✓ 删除备货单测试通过\n";
        } catch (Exception $e) {
            $this->fail('删除备货单失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试参数验证
     */
    public function testValidateParams()
    {
        try {
            // 测试有效参数
            $validParams = [
                'status' => 10,
                'sub_status' => 1,
                'page_size' => 20,
                'create_time_from' => '2025-06-01',
                'create_time_to' => '2025-06-30',
                'date_type' => 'create_time',
                'is_delete' => 0
            ];
            
            $errors = $this->model->validateParams($validParams);
            $this->assertEmpty($errors, '有效参数不应该有错误');
            
            // 测试无效参数
            $invalidParams = [
                'status' => 999,
                'sub_status' => 99,
                'page_size' => 100,
                'create_time_from' => 'invalid-date',
                'date_type' => 'invalid_type'
            ];
            
            $errors = $this->model->validateParams($invalidParams);
            $this->assertNotEmpty($errors, '无效参数应该有错误');
            echo "✓ 参数验证测试通过\n";
        } catch (Exception $e) {
            $this->fail('参数验证测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试状态映射
     */
    public function testStatusMaps()
    {
        try {
            $statusMap = $this->model->getStatusMap();
            $this->assertIsArray($statusMap, '状态映射应该是数组');
            $this->assertArrayHasKey(10, $statusMap, '状态映射应该包含状态10');
            $this->assertArrayHasKey(60, $statusMap, '状态映射应该包含状态60');
            
            $subStatusMap = $this->model->getSubStatusMap();
            $this->assertIsArray($subStatusMap, '子状态映射应该是数组');
            $this->assertArrayHasKey(0, $subStatusMap, '子状态映射应该包含状态0');
            
            $dateTypeMap = $this->model->getDateTypeMap();
            $this->assertIsArray($dateTypeMap, '时间类型映射应该是数组');
            $this->assertArrayHasKey('create_time', $dateTypeMap, '时间类型映射应该包含create_time');
            echo "✓ 状态映射测试通过\n";
        } catch (Exception $e) {
            $this->fail('状态映射测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试边界条件
     */
    public function testBoundaryConditions()
    {
        try {
            // 测试空数据保存
            $emptyResult = $this->model->saveInboundData([]);
            $this->assertEquals(0, $emptyResult, '空数据保存应该返回0');
            
            // 测试不存在的ID查询
            $nonExistentDetail = $this->model->getInboundDetail(999999);
            $this->assertFalse($nonExistentDetail, '不存在的ID应该返回false');
            
            // 测试不存在的备货单号查询
            $nonExistentOrder = $this->model->getInboundByOrderNo('NON_EXISTENT_ORDER');
            $this->assertFalse($nonExistentOrder, '不存在的备货单号应该返回false');
            
            // 测试更新不存在的记录
            $updateResult = $this->model->updateEditableFields(999999, ['transparent_label' => 'test']);
            $this->assertEquals(0, $updateResult, '更新不存在的记录应该返回0');
            
            echo "✓ 边界条件测试通过\n";
        } catch (Exception $e) {
            $this->fail('边界条件测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 清理测试数据
     */
    public function tearDown(): void
    {
        try {
            // 清理测试数据
            $this->cleanupTestData();
        } catch (Exception $e) {
            // 忽略清理错误
        }
    }

    /**
     * 清理测试数据
     */
    private function cleanupTestData()
    {
        // 这里可以添加清理测试数据的逻辑
        // 由于使用的是逻辑删除，可以直接删除测试备货单号的记录
    }

    /**
     * 运行所有测试
     */
    public static function runAllTests()
    {
        echo "\n=== 海外仓备货单模型测试开始 ===\n";
        
        $test = new self();
        $test->setUp();
        
        try {
            $test->testSaveInboundData();
            $test->testGetInboundByOrderNo();
            $test->testGetInboundList();
            $test->testUpdateEditableFields();
            $test->testDeleteInbound();
            $test->testValidateParams();
            $test->testStatusMaps();
            $test->testBoundaryConditions();
            
            echo "\n✅ 所有测试通过！\n";
        } catch (Exception $e) {
            echo "\n❌ 测试失败: " . $e->getMessage() . "\n";
        } finally {
            $test->tearDown();
        }
        
        echo "=== 海外仓备货单模型测试结束 ===\n\n";
    }
}

// 如果直接运行此文件，执行所有测试
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    overseasInboundModelTest::runAllTests();
}
