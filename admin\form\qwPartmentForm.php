<?php
/**
 * @author: zhangguoming
 * @Time: 2025/3/25 17:16
 */

namespace admin\form;

use core\lib\db\dbMysql;

class qwPartmentForm
{
    public static function getList($param){
        $db = dbMysql::getInstance();
        $db->table('qwdepartment')
            ->where('where 1=1');
        if (!empty($param['name'])) {
            $db->andWhere('and name like :wp_name',['wp_name'=>'%'.$param['name'].'%']);
        }
        if (!empty($param['department_leader'])) {
            $db->andWhere('and department_leader like :lName',['lName'=>'%'.$param['department_leader'].'%']);
        }
        $db->field('id,wp_id,name,department_leader,qw_parentid,sort');
        $db->order('qw_parentid asc,`sort` desc');
        $partments = $db->list();
        $list = [];
        if (count($partments)) {
            $list = self::arrangeList($partments,$partments[0]['qw_parentid']);
        }
        return $list;
    }
    //递归整理数据
    private static function arrangeList($list, $pid){
        $res_data = [];
        if(count($list) > 0) {
            foreach ($list as $val) {
                if ($val['qw_parentid'] == $pid) {
                    $val['department_leader'] = json_decode($val['department_leader']);
                    $val['child'] = self::arrangeList($list,$val['wp_id']);
                    $res_data[] = $val;
                }
            }
        }
        return $res_data;
    }

    public static function getTree(){
        $db = dbMysql::getInstance();
        $db->table('qwdepartment');
        $db->field('id,wp_id,name,department_leader,qw_parentid,sort');
        $db->order('qw_parentid asc,`sort` desc');
        $deps = $db->list();
        $departmentTree = [];
        if (count($deps)) {
            $departmentTree = buildTree($deps, $deps[0]['qw_parentid'], 'wp_id', 'qw_parentid');
        }
        return $departmentTree;
    }
}