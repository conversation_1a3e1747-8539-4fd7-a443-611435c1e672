<?php

namespace plugins\checkin\models;

/**
 * CREATE TABLE `oa_c_corp_checkin_option` (
 * `id` int NOT NULL COMMENT '打卡规则id',
 * `groupname` varchar(255) DEFAULT NULL COMMENT '打卡规则名称',
 * `grouptype` tinyint DEFAULT NULL COMMENT '打卡规则类型，1：固定时间上下班；2：按班次上下班；3：自由上下班',
 * `checkindate` text COMMENT '打卡时间，当规则类型为排班时没有意义\r\n',
 * `spe_workdays` text COMMENT '特殊日期-必须打卡日期信息',
 * `spe_offdays` text COMMENT '特殊日期-不用打卡日期信息',
 * `range_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '打卡人员信息',
 * `white_users` text COMMENT '打卡人员白名单，即不需要打卡人员，需要有设置白名单才能查看',
 * `ot_info` text COMMENT '加班信息，相关信息需要设置后才能显示',
 * `ori_group` text COMMENT '原始数据',
 * `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
 * `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
 * PRIMARY KEY (`id`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
 */


class corpCheckinOptionModel
{
    public static int $id;
    public static int $user_id;
    public static string $rule_name;
    public static int $rule_type;
    public static int $status;
    public static string $remark;
    public static string $attach_item;
    /**
     * 请假扣款 （type=1）
     *
     *
     */


    /**
     * @param $rule
     * @return void
     * @throws \Exception
     */
    public static function checkRule($rule)
    {
        if (!in_array($rule['rule_type'], [1, 2, 3, 4, 5, 6])) throw new \Exception('规则类型错误');
        if (!in_array($rule['status'], [0, 1])) throw new \Exception('是否启用错误');
        $attach = json_decode($rule['attach'], true);
        switch ($rule['rule_type']) {
            case 1: // 请假扣款
                self::checkVacation($attach);
                break;
            case 2: // 旷工扣款
                self::checkAbsenteeism($attach);
                break;
            case 3: // 缺卡扣款
                self::checkNotCheckin($attach);
                break;
            case 4: // 迟到早退扣款
                self::checkLate($attach);
                break;
            case 5: // 加班计薪
                self::checkOvertime($attach);
                break;
            case 6: // 全勤奖
                self::checkFullAttendance($attach);
                break;

        }

        return;

    }

    // 请假扣款
    private static function checkVacation($attach) :void
    {
        empty($attach['casual_leave']) && throw new \Exception('事假规则错误');
        $casual_leave = $attach['casual_leave'];
        if (!isset($casual_leave['status']) || !in_array($casual_leave['status'], [0, 1])) throw new \Exception('事假是否启用错误');
        if ($casual_leave['status']) {
            if (!isset($casual_leave['safe_limit'])) throw new \Exception('不扣款事假最小时间错误');
            if (!$casual_leave['calc_type'] || !in_array($casual_leave['calc_type'], [1, 2, 3, 4])) throw new \Exception('事假取数规则错误');
            if (in_array($casual_leave['calc_type'], [1,2]) && empty($casual_leave['after_point'])) throw new \Exception('事假保留小数错误');
            if (!is_array($casual_leave['formula']) || empty($casual_leave['formula'])) throw new \Exception('事假扣款公式错误');
            $idx = 0;
            foreach ($casual_leave['formula'] as $item) {
                if (!in_array($item['type'], [1, 2])) throw new \Exception("事假扣款公式条件/条件组{$idx}错误");
                if ($idx && !in_array($item['symbol'], [1, 2, 3, 4])) throw new \Exception("事假扣款公式条件/条件组{$idx}计算符号错误");
                if ($item['type'] == 1) {
                    if (!is_numeric($item['value'])) throw new \Exception("条件{$idx}值错误");
                    if (!is_numeric($item['value_type'])) throw new \Exception("条件{$idx}值类型错误");
                } elseif ($item['type'] == 2) {
                    $sub_idx = 0;
                    if (!is_array($item['list']) || empty($item['list'])) throw new \Exception("事假扣款公式条件组{$idx}规则错误");
                    foreach ($item['list'] as $list_item) {
                        if ($sub_idx && !in_array($list_item['symbol'], [1, 2, 3, 4])) throw new \Exception("事假扣款公式条件组{$idx}子条件{$sub_idx}计算符号错误");
                        if (!is_numeric($list_item['value'])) throw new \Exception("事假扣款公式条件组{$idx}子条件{$sub_idx}值错误");
                        if (!is_numeric($list_item['value_type'])) throw new \Exception("事假扣款公式条件组{$idx}子条件{$sub_idx}值类型错误");
                        $sub_idx++;
                    }
                }
                $idx++;
            }
        }

        empty($attach['sick_leave']) && throw new \Exception('病假规则错误');
        $sick_leave = $attach['sick_leave'];
        if (!isset($sick_leave['status']) || !in_array($sick_leave['status'], [0, 1])) throw new \Exception('病假是否启用错误');
        if ($sick_leave['status']) {
            if (!$sick_leave['calc_type'] || !in_array($sick_leave['calc_type'], [1, 2, 3, 4])) throw new \Exception('病假取数规则错误');
            if (in_array($sick_leave['calc_type'], [1,2]) && empty($sick_leave['after_point'])) throw new \Exception('病假保留小数错误');
            if (!is_array($sick_leave['formula']) || empty($sick_leave['formula'])) throw new \Exception('病假扣款公式错误');
            $formula_idx = 0;
            $max_idx = count($sick_leave['formula']) - 1;
            foreach ($sick_leave['formula'] as $item) {
                if ($formula_idx < $max_idx) {
                    if (!in_array($item['condition'], [1, 2, 3, 4, 5])) throw new \Exception("病假扣款公式判断条件{$formula_idx}符号错误");
                    if (!isset($item['value'])) throw new \Exception("病假扣款公式判断条件{$formula_idx}值错误");
                }
                $idx = 0;
                foreach ($item['formula'] as $formula_item) {
                    if ($idx && !in_array($formula_item['symbol'], [1, 2, 3, 4])) throw new \Exception("病假扣款公式判断条件{$formula_idx}条件/条件组{$idx}计算符号错误");
                    if (!in_array($formula_item['type'], [1, 2])) throw new \Exception("病假扣款公式判断条件{$formula_idx}条件/条件组{$idx}错误");
                    if ($formula_item['type'] == 1) { // 条件
                        if (!is_numeric($formula_item['value'])) throw new \Exception("病假扣款公式判断条件{$formula_idx}条件{$idx}值错误");
                        if (!is_numeric($formula_item['value_type'])) throw new \Exception("病假扣款公式判断条件{$formula_idx}条件{$idx}值类型错误");
                    } elseif ($formula_item['type'] == 2) { // 条件组
                        $sub_idx = 0;
                        if (!is_array($formula_item['list']) || empty($formula_item['list'])) throw new \Exception("病假扣款公式判断条件{$formula_idx}条件组{$idx}规则错误");
                        foreach ($formula_item['list'] as $list_item) {
                            if ($sub_idx && !in_array($list_item['symbol'], [1, 2, 3, 4])) throw new \Exception("病假扣款公式判断条件{$formula_idx}条件组{$idx}子条件{$sub_idx}计算符号错误");
                            if (!is_numeric($list_item['value'])) throw new \Exception("病假扣款公式判断条件{$formula_idx}条件组{$idx}子条件{$sub_idx}值错误");
                            if (!is_numeric($list_item['value_type'])) throw new \Exception("病假扣款公式判断条件{$formula_idx}条件组{$idx}子条件{$sub_idx}值类型错误");
                            $sub_idx++;
                        }
                    }
                }
                $formula_idx++;
            }
        }

        empty($attach['year_leave']) && throw new \Exception('年假规则错误');
        $year_leave = $attach['year_leave'];
        if (!isset($year_leave['status']) || !in_array($year_leave['status'], [0, 1])) throw new \Exception('年假是否启用错误');
        if (!isset($year_leave['year_lowest'])) throw new \Exception('年假最低起算时间错误');
        if (!isset($year_leave['year_leave'])) throw new \Exception('年假长度错误');

        empty($attach['examination_leave']) && throw new \Exception('体检假规则错误');
        $examination_leave = $attach['examination_leave'];
        if (!isset($examination_leave['status']) || !in_array($examination_leave['status'], [0, 1])) throw new \Exception('体检假是否启用错误');
        if ($examination_leave['status'] && !isset($examination_leave['examination_leave'])) throw new \Exception('体检假长度错误');
    }

    // 旷工扣款
    private static function checkAbsenteeism($attach)
    {
        if ($attach['calc_method'] == 1) { // 固定金额扣款
            if (!is_numeric($attach['value'])) throw new \Exception('扣款金额错误');
        } elseif ($attach['calc_method'] == 2) { // 自定义公式
            if (!$attach['calc_type'] || !in_array($attach['calc_type'], [1, 2, 3, 4])) throw new \Exception('旷工扣款取数规则错误');
            if (in_array($attach['calc_type'], [1,2]) && empty($attach['after_point'])) throw new \Exception('旷工扣款保留小数错误');
            if (!is_array($attach['formula']) || empty($attach['formula'])) throw new \Exception('扣款公式错误');
            $idx = 0;
            foreach ($attach['formula'] as $item) {
                if (!in_array($item['type'], [1, 2])) throw new \Exception("条件/条件组条件类型{$idx}错误");
                if ($idx && !in_array($item['symbol'], [1, 2, 3, 4])) throw new \Exception("条件/条件组{$idx}计算符号错误");
                if ($item['type'] == 1) { // 条件
                    if (!is_numeric($item['value_type'])) throw new \Exception("条件{$idx}值类型错误");
                    if (!is_numeric($item['value'])) throw new \Exception("条件{$idx}值错误");
                } elseif ($item['type'] == 2) { // 条件组
                    $sub_idx = 0;
                    if (!is_array($item['list']) || empty($item['list'])) throw new \Exception("条件组{$idx}规则错误");
                    foreach ($item['list'] as $list_item) {
                        if ($sub_idx && !in_array($list_item['symbol'], [1, 2, 3, 4])) throw new \Exception("条件组{$idx}子条件{$sub_idx}计算符号错误");
                        if (!is_numeric($list_item['value'])) throw new \Exception("条件组{$idx}子条件{$sub_idx}值错误");
                        if (!is_numeric($list_item['value_type'])) throw new \Exception("条件组{$idx}子条件{$sub_idx}值类型错误");
                        $sub_idx++;
                    }
                }
                $idx++;
            }
        }
    }

    // 缺卡扣款
    private static function checkNotCheckin($attach)
    {
        empty($attach['start_or_end']) && throw new \Exception('月累计上班/下班缺卡错误');
        empty($attach['start_and_end']) && throw new \Exception('月累计上班和下班都缺卡错误');
        foreach ($attach['start_or_end'] as $item) {
            if (empty($item['range_times']) || !isset($item['range_times'][0]) || !isset($item['range_times'][1])) throw new \Exception('次数范围错误');
            if (!is_numeric($item['value_type'])) throw new \Exception('缺卡值类型错误');
            if (!is_numeric($item['value'])) throw new \Exception('缺卡值错误');
        }
        foreach ($attach['start_and_end'] as $item) {
            if (empty($item['range_times']) || !isset($item['range_times'][0]) || !isset($item['range_times'][1])) throw new \Exception('缺卡次数范围错误');
            if (!is_numeric($item['value_type'])) throw new \Exception('缺卡值类型错误');
            if (!is_numeric($item['value'])) throw new \Exception('缺卡值错误');
        }
    }

    // 迟到早退扣款
    private static function checkLate($attach)
    {
        empty($attach['come_late']) && throw new \Exception('迟到规则错误');
        empty($attach['come_late']['setting']) && throw new \Exception('迟到规则设置错误');
        empty($attach['come_late']['rule']) && throw new \Exception('迟到规则错误');
        !isset($attach['come_late']['setting']['times']) && throw new \Exception('迟到规则设置每月次数错误');
        !isset($attach['come_late']['setting']['minutes']) && throw new \Exception('迟到规则设置单次分钟错误');
        foreach ($attach['come_late']['rule'] as $item) {
            if (empty($item['range_minute']) || !isset($item['range_minute'][0]) || !isset($item['range_minute'][1])) throw new \Exception('迟到分钟范围错误');
            if (!is_numeric($item['value'])) throw new \Exception('迟到规则值错误');
            if (!is_numeric($item['value_type'])) throw new \Exception('迟到规则值类型错误');
        }

        empty($attach['leave_early']) && throw new \Exception('早退规则错误');
        empty($attach['leave_early']['setting']) && throw new \Exception('早退规则设置错误');
        empty($attach['leave_early']['rule']) && throw new \Exception('早退规则错误');
        !isset($attach['leave_early']['setting']['times']) && throw new \Exception('早退规则设置每月次数错误');
        !isset($attach['leave_early']['setting']['minutes']) && throw new \Exception('早退规则设置单次分钟错误');
        foreach ($attach['leave_early']['rule'] as $item) {
            if (empty($item['range_minute']) || !isset($item['range_minute'][0]) || !isset($item['range_minute'][1])) throw new \Exception('早退分钟范围错误');
            if (!is_numeric($item['value'])) throw new \Exception('早退规则值错误');
            if (!is_numeric($item['value_type'])) throw new \Exception('早退规则值类型错误');
        }
    }

    // 加班计薪
    private static function checkOvertime($attach)
    {
        empty($attach['workday']) && throw new \Exception('工作日加班规则错误');
        !isset($attach['workday']['hour']) || !isset($attach['workday']['minute']) && throw new \Exception('工作日加班判定错误');
        !isset($attach['workday']['value']) && throw new \Exception('工作日加班补贴错误');

        empty($attach['nonworkingday']) && throw new \Exception('休息日加班规则错误');
        foreach ($attach['nonworkingday'] as $item) {
            if (empty($item['department'])) throw new \Exception('休息日适用范围错误');
            if (!is_numeric($item['value_type'])) throw new \Exception('休息日计薪规则错误');
            if ($item['value_type'] == 1 && !is_numeric($item['value'])) throw new \Exception('休息日加班补贴错误');
        }

        empty($attach['holiday']) && throw new \Exception('节假日加班规则错误');
        foreach ($attach['holiday'] as $item) {
            if (empty($item['department'])) throw new \Exception('节假日适用范围错误');
            if (!is_numeric($item['value_type'])) throw new \Exception('节假日计薪规则错误');
            if ($item['value_type'] == 1 && !is_numeric($item['value'])) throw new \Exception('节假日加班补贴错误');
        }


    }

    // 全勤奖
    private static function checkFullAttendance($attach)
    {
        empty($attach['rule']) && throw new \Exception('全勤奖扣款规则错误');
        empty($attach['value']) && throw new \Exception('全勤奖金额错误');

    }

}