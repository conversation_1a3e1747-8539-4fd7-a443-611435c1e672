<?php

namespace core\lib;

class validator
{
    /**
     * 校验数据
     *
     * @param array $data 要校验的数据，例如 ['name' => '张三', 'age' => 20]
     * @param array $rules 校验规则，例如：
     * [
     *     'start_date' => '开始日期|required|date',
     *     'name'       => '名称|required|string|gt:20|lt:100',
     *     'desc'       => '描述|string|lt:1000',
     *     'age'        => '年龄|required|integer|gt:18|lt:60',
     *     'email'      => '邮箱|required|email',
     *     'status'     => '状态|required|in:0,1,2', // 0-待处理, 1-处理中, 2-已处理
     *     'ids'        => 'ID列表|required|array',
     *     'ids.*'      => 'ID|integer|gt:0' // 校验数组中的每一项
     * ]
     * @return array 错误信息，如果为空数组则表示校验通过
     */
    public static function validate(array $data, array $rules): array
    {
        $errors = [];

        foreach ($rules as $field => $ruleString) {
            $ruleParts = explode('|', $ruleString);
            $fieldNameAlias = array_shift($ruleParts); // 第一个元素是字段别名

            // 处理嵌套数组校验，例如 'ids.*'
            if (strpos($field, '.*') !== false) {
                $arrayField = str_replace('.*', '', $field);
                if (!isset($data[$arrayField]) || !is_array($data[$arrayField])) {
                    if (in_array('required', $ruleParts)) {
                        $errors[$field][] = '【'.$fieldNameAlias . '】是必填的数组';
                    }
                    continue;
                }

                foreach ($data[$arrayField] as $key => $value) {
                    $currentFieldPath = $arrayField . '[' . $key . ']';
                    foreach ($ruleParts as $rule) {
                        $errorMessage = self::applyRule($fieldNameAlias . '(第'. ($key + 1) .'项)', $value, $rule, $data);
                        if ($errorMessage) {
                            $errors[$currentFieldPath][] = $errorMessage;
                        }
                    }
                }
                continue;
            }

            $value = $data[$field] ?? null;

            // 检查 'required' 规则
            if (in_array('required', $ruleParts)) {
                $is_also_array_rule = in_array('array', $ruleParts);

                // 情况1: 必填项为空 (null, 空字符串, 空数组)
                if ($value === null || (is_string($value) && trim($value) === '') || (is_array($value) && empty($value))) {
                    if ($is_also_array_rule) {
                        $errors[$field][] = '【'.$fieldNameAlias . '】是必填的数组';
                    } else {
                        $errors[$field][] = '【'.$fieldNameAlias . '】是必填项';
                    }
                    continue; // 如果是必填项但为空，则无需进行后续校验
                }

                // 情况2: 必填数组规则存在，但值存在却不是数组类型
                if ($is_also_array_rule && !is_array($value)) {
                    $errors[$field][] = '【'.$fieldNameAlias . '】是必填的数组';
                    continue; // 值不是数组类型，无需进行后续校验
                }
            } else {
                // 如果不是必填项且值为空 (null 或空字符串)，则跳过其他校验
                if ($value === null || (is_string($value) && trim($value) === '')) {
                    continue;
                }
            }

            foreach ($ruleParts as $rule) {
                if ($rule === 'required') {
                    continue; // 'required' 规则已在前面处理
                }
                $errorMessage = self::applyRule($fieldNameAlias, $value, $rule, $data);
                if ($errorMessage) {
                    $errors[$field][] = $errorMessage;
                }
            }
        }

        // 合并错误信息
        if (!empty($errors)) {
            foreach ($errors as $field => $fieldErrors) {
                $errors[$field] = implode('、', $fieldErrors);
            }
        }

        return [
            'errors' => $errors,
            'error_msg' => implode(';', $errors)
        ];
    }

    /**
     * 应用单条校验规则
     *
     * @param string $fieldNameAlias 字段别名
     * @param mixed $value 字段值
     * @param string $rule 规则，例如 'string', 'gt:10', 'date'
     * @param array $data 完整数据，用于某些跨字段校验（本例中未使用）
     * @return string|null 错误信息，如果校验通过则返回 null
     */
    private static function applyRule(string $fieldNameAlias, $value, string $rule, array $data): ?string
    {
        $ruleName = $rule;
        $ruleParam = null;

        if (strpos($rule, ':') !== false) {
            list($ruleName, $ruleParam) = explode(':', $rule, 2);
        }

        switch ($ruleName) {
            case 'string':
                if (!is_string($value)) {
                    return '【'.$fieldNameAlias . '】必须是字符串';
                }
                break;
            case 'integer':
                if (!is_numeric($value) || filter_var($value, FILTER_VALIDATE_INT) === false) {
                     // 允许字符串形式的数字，例如 "123"
                    if (is_string($value) && ctype_digit($value)) {
                        // no error
                    } else {
                        return '【'.$fieldNameAlias . '】必须是整数';
                    }
                }
                break;
            case 'numeric':
                if (!is_numeric($value)) {
                    return '【'.$fieldNameAlias . '】必须是数字';
                }
                break;
            case 'array':
                if (!is_array($value)) {
                    return '【'.$fieldNameAlias . '】必须是数组';
                }
                break;
            case 'date':
                if (!self::isValidDate($value)) {
                    return '【'.$fieldNameAlias . '】必须是有效的日期格式 (例如 YYYY-MM-DD)';
                }
                break;
            case 'datetime':
                if (!self::isValidDateTime($value)) {
                    return '【'.$fieldNameAlias . '】必须是有效的日期时间格式 (例如 YYYY-MM-DD HH:MM:SS)';
                }
                break;
            case 'email':
                if (filter_var($value, FILTER_VALIDATE_EMAIL) === false) {
                    return '【'.$fieldNameAlias . '】必须是有效的邮箱地址';
                }
                break;
            case 'max': // 最大值/最大长度
                if ($ruleParam === null) break;
                if (is_string($value)) {
                    if (mb_strlen($value, 'UTF-8') > (int)$ruleParam) {
                        return '【'.$fieldNameAlias . '】长度不能超过 ' . $ruleParam . ' 个字符';
                    }
                } elseif (is_numeric($value)) {
                    if ($value > (float)$ruleParam) {
                        return '【'.$fieldNameAlias . '】不能超过 ' . $ruleParam;
                    }
                } elseif (is_array($value)) {
                    if (count($value) > (int)$ruleParam) {
                        return '【'.$fieldNameAlias . '】的元素数量不能超过 ' . $ruleParam;
                    }
                }
                break;
            case 'min': // 最小值/最小长度
                if ($ruleParam === null) break;
                if (is_string($value)) {
                    if (mb_strlen($value, 'UTF-8') < (int)$ruleParam) {
                        return '【'.$fieldNameAlias . '】长度不能少于 ' . $ruleParam . ' 个字符';
                    }
                } elseif (is_numeric($value)) {
                    if ($value < (float)$ruleParam) {
                        return '【'.$fieldNameAlias . '】不能小于 ' . $ruleParam;
                    }
                } elseif (is_array($value)) {
                    if (count($value) < (int)$ruleParam) {
                        return '【'.$fieldNameAlias . '】的元素数量不能少于 ' . $ruleParam;
                    }
                }
                break;
            case 'gt': // greater than
                if ($ruleParam === null) break;
                if (is_string($value)) {
                    if (mb_strlen($value, 'UTF-8') <= (int)$ruleParam) {
                        return '【'.$fieldNameAlias . '】长度必须大于 ' . $ruleParam . ' 个字符';
                    }
                } elseif (is_numeric($value)) {
                    if ($value <= (float)$ruleParam) {
                        return '【'.$fieldNameAlias . '】必须大于 ' . $ruleParam;
                    }
                } elseif (is_array($value)) {
                     if (count($value) <= (int)$ruleParam) {
                        return '【'.$fieldNameAlias . '】的元素数量必须大于 ' . $ruleParam;
                    }
                }
                break;
            case 'lt': // less than
                if ($ruleParam === null) break;
                if (is_string($value)) {
                    if (mb_strlen($value, 'UTF-8') >= (int)$ruleParam) {
                        return '【'.$fieldNameAlias . '】长度必须小于 ' . $ruleParam . ' 个字符';
                    }
                } elseif (is_numeric($value)) {
                    if ($value >= (float)$ruleParam) {
                        return '【'.$fieldNameAlias . '】必须小于 ' . $ruleParam;
                    }
                } elseif (is_array($value)) {
                    if (count($value) >= (int)$ruleParam) {
                       return '【'.$fieldNameAlias . '】的元素数量必须小于 ' . $ruleParam;
                   }
               }
                break;
            case 'gte': // greater than or equal
                if ($ruleParam === null) break;
                if (is_string($value)) {
                    if (mb_strlen($value, 'UTF-8') < (int)$ruleParam) {
                        return '【'.$fieldNameAlias . '】长度必须大于等于 ' . $ruleParam . ' 个字符';
                    }
                } elseif (is_numeric($value)) {
                    if ($value < (float)$ruleParam) {
                        return '【'.$fieldNameAlias . '】必须大于等于 ' . $ruleParam;
                    }
                } elseif (is_array($value)) {
                    if (count($value) < (int)$ruleParam) {
                       return '【'.$fieldNameAlias . '】的元素数量必须大于等于 ' . $ruleParam;
                   }
               }
                break;
            case 'lte': // less than or equal
                if ($ruleParam === null) break;
                if (is_string($value)) {
                    if (mb_strlen($value, 'UTF-8') > (int)$ruleParam) {
                        return '【'.$fieldNameAlias . '】长度必须小于等于 ' . $ruleParam . ' 个字符';
                    }
                } elseif (is_numeric($value)) {
                    if ($value > (float)$ruleParam) {
                        return '【'.$fieldNameAlias . '】必须小于等于 ' . $ruleParam;
                    }
                } elseif (is_array($value)) {
                    if (count($value) > (int)$ruleParam) {
                       return '【'.$fieldNameAlias . '】的元素数量必须小于等于 ' . $ruleParam;
                   }
               }
                break;
            case 'len': // length equal
                if ($ruleParam === null) break;
                 if (is_string($value)) {
                    if (mb_strlen($value, 'UTF-8') != (int)$ruleParam) {
                        return '【'.$fieldNameAlias . '】长度必须等于 ' . $ruleParam . ' 个字符';
                    }
                } elseif (is_array($value)) {
                    if (count($value) != (int)$ruleParam) {
                        return '【'.$fieldNameAlias . '】的元素数量必须等于 ' . $ruleParam;
                    }
                } else {
                    return '【'.$fieldNameAlias . '】无法应用长度校验';
                }
                break;
            case 'after': // 日期必须晚于指定日期
                if ($ruleParam === null) break;
                if (!self::isValidDate($value)) {
                    return '【'.$fieldNameAlias . '】必须是有效的日期格式';
                }
                $compareDate = $ruleParam;
                // 如果参数是字段名，从数据中获取值
                if (isset($data[$ruleParam])) {
                    $compareDate = $data[$ruleParam];
                }
                if (!self::isValidDate($compareDate)) {
                    return '【'.$fieldNameAlias . '】比较的日期格式无效';
                }
                if (strtotime($value) <= strtotime($compareDate)) {
                    return '【'.$fieldNameAlias . '】必须晚于 ' . $compareDate;
                }
                break;
            case 'after_or_equal': // 日期必须晚于或等于指定日期
                if ($ruleParam === null) break;
                if (!self::isValidDate($value)) {
                    return '【'.$fieldNameAlias . '】必须是有效的日期格式';
                }
                $compareDate = $ruleParam;
                // 如果参数是字段名，从数据中获取值
                if (isset($data[$ruleParam])) {
                    $compareDate = $data[$ruleParam];
                }
                if (!self::isValidDate($compareDate)) {
                    return '【'.$fieldNameAlias . '】比较的日期格式无效';
                }
                if (strtotime($value) < strtotime($compareDate)) {
                    return '【'.$fieldNameAlias . '】必须晚于或等于 ' . $compareDate;
                }
                break;
            case 'before': // 日期必须早于指定日期
                if ($ruleParam === null) break;
                if (!self::isValidDate($value)) {
                    return '【'.$fieldNameAlias . '】必须是有效的日期格式';
                }
                $compareDate = $ruleParam;
                // 如果参数是字段名，从数据中获取值
                if (isset($data[$ruleParam])) {
                    $compareDate = $data[$ruleParam];
                }
                if (!self::isValidDate($compareDate)) {
                    return '【'.$fieldNameAlias . '】比较的日期格式无效';
                }
                if (strtotime($value) >= strtotime($compareDate)) {
                    return '【'.$fieldNameAlias . '】必须早于 ' . $compareDate;
                }
                break;
            case 'before_or_equal': // 日期必须早于或等于指定日期
                if ($ruleParam === null) break;
                if (!self::isValidDate($value)) {
                    return '【'.$fieldNameAlias . '】必须是有效的日期格式';
                }
                $compareDate = $ruleParam;
                // 如果参数是字段名，从数据中获取值
                if (isset($data[$ruleParam])) {
                    $compareDate = $data[$ruleParam];
                }
                if (!self::isValidDate($compareDate)) {
                    return '【'.$fieldNameAlias . '】比较的日期格式无效';
                }
                if (strtotime($value) > strtotime($compareDate)) {
                    return '【'.$fieldNameAlias . '】必须早于或等于 ' . $compareDate;
                }
                break;
            case 'in': // in a list of values
                if ($ruleParam === null) break;
                $allowedValues = explode(',', $ruleParam);
                // Trim whitespace from allowed values
                $allowedValues = array_map('trim', $allowedValues);
                if (!in_array((string)$value, $allowedValues, true)) { // Strict comparison for type
                    return '【'.$fieldNameAlias . '】的值必须是 [' . implode(', ', $allowedValues) . '] 中的一个';
                }
                break;
            case 'not_in': // not in a list of values
                 if ($ruleParam === null) break;
                 $disallowedValues = explode(',', $ruleParam);
                 $disallowedValues = array_map('trim', $disallowedValues);
                 if (in_array((string)$value, $disallowedValues, true)) {
                     return '【'.$fieldNameAlias . '】的值不能是 [' . implode(', ', $disallowedValues) . '] 中的任何一个';
                 }
                 break;
            case 'regex': // regular expression
                if ($ruleParam === null) break;
                if (!preg_match($ruleParam, $value)) {
                    return '【'.$fieldNameAlias . '】格式不正确';
                }
                break;
            // 可以根据需要添加更多规则，例如 'url', 'ip', 'min', 'max' 等
        }
        return null;
    }

    /**
     * 校验是否为有效的日期格式 (YYYY-MM-DD)
     * @param string $dateString
     * @return bool
     */
    private static function isValidDate(string $dateString): bool
    {
        $d = \DateTime::createFromFormat('Y-m-d', $dateString);
        return $d && $d->format('Y-m-d') === $dateString;
    }

    /**
     * 校验是否为有效的日期时间格式 (YYYY-MM-DD HH:MM:SS)
     * @param string $dateTimeString
     * @return bool
     */
    private static function isValidDateTime(string $dateTimeString): bool
    {
        $d = \DateTime::createFromFormat('Y-m-d H:i:s', $dateTimeString);
        return $d && $d->format('Y-m-d H:i:s') === $dateTimeString;
    }
}
