<?php
/**
 * @author: zhangguoming
 * @Time: 2024/5/31 9:24
 */

namespace  plugins\goods\controller;

use plugins\goods\common\publicMethod;
use plugins\goods\form\downLoadFrom;
use plugins\goods\form\messagesFrom;
use plugins\goods\form\takePictureRequestFrom;
use plugins\goods\models\userModel;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;

class takePictureRequestController
{
    //列表
    public function getList() {
        $paras_list = array('request_name','goods_name','qwuser_id','user_id','order_by', 'status', 'page','page_size','type');
        $param = arrangeParam($_POST, $paras_list);
        $db = dbMysql::getInstance();
        $db->table('take_picture_request','a')
            ->leftJoin('goods_new','b','b.id = a.goods_id')
            ->leftJoin('qwuser','q','q.id = a.qwuser_id')
            ->leftJoin('qwuser','q1','q1.id = a.user_id')
            ->where('where a.is_delete = 0');
        if (!empty($param['goods_name'])) {
            $db->andWhere('and b.goods_name like :goods_name',['goods_name'=>'%'.$param['goods_name'].'%']);
        }
        if (!empty($param['request_name'])) {
            $db->andWhere('and a.request_name like :request_name',['request_name'=>'%'.$param['request_name'].'%']);
        }
        if ((int)$param['qwuser_id']) {
            $db->andWhere('and a.qwuser_id=:qwuser_id',['qwuser_id'=>$param['qwuser_id']]);
        }
        if ((int)$param['status'] > -1) {
            $db->andWhere('and a.status=:status',['status'=>(int)$param['status']]);
        }
        if ((int)$param['type'] > 0) {
            $db->andWhere('and a.type=:type',['type'=>(int)$param['type']]);
        }
        if (!empty($param['user_id'])) {
            $db->andWhere('and a.user_id=:user_id',['user_id'=>$param['user_id']]);
        }
        $order_str = '';
        if (!empty($param['order_by'])) {
            $order_by = json_decode($param['order_by'],true);
            foreach ($order_by as $k=>$ord){
                $order_str .= 'a.'.$k.' '.$ord.',';
            }
        }
        if (empty($order_str)) {
            $db->order('a.id desc');
        } else {
            $db->order(trim($order_str,','));
        }
        $db->field('a.*,b.goods_name,q.wname as agent_wname,q1.wname as created_wname');
        $list = $db->pages($param['page'],$param['page_size']);
        foreach ($list['list'] as &$v) {
            $v['expected_time'] = date('Y-m-d H:i:s',$v['expected_time']);
            $v['begin_time'] = date('Y-m-d H:i:s',$v['begin_time']);
        }
        //获取列表商品颜色
        returnSuccess($list);
    }

    //申请
    public function editRequest() {
        $paras_list = array('goods_id','type','expected_time','description','id');
        $request_list = ['type'=>'图片类型','goods_id'=>'商品ID','expected_time'=>'预计完成时间'];
        $length_data = ['description'=>['name'=>'描述','length'=>225]];
        $param = arrangeParam($_POST, $paras_list, $request_list, $length_data);
        $id = (int)$param['id'];
        $db = dbMysql::getInstance();
        $db->beginTransaction();
        try {
            //保存信息
            takePictureRequestFrom::editRes($param);
            //消息推送
            takePictureRequestFrom::sendQwMsg();
            $db->commit();
            returnSuccess([],'申请成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }

    }

    //上传
    public function uploadFile() {
        $paras_list = array('request_id','images','remarks','is_edit');
        $request_list = ['request_id'=>'需求ID','images'=>'拍摄图片'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $request_id = (int)$param['request_id'];
        $is_edit = (int)$param['is_edit'];
        $db = dbMysql::getInstance();
        $requrst = $db->table('take_picture_request')
            ->where('where id=:request_id and is_delete = 0',['request_id'=>$request_id])
            ->one();
        if (!$requrst) {
            SetReturn(-1,'未找到该需求');
        }
        if ($requrst['qwuser_id'] != userModel::$qwuser_id) {
            SetReturn(-1,'非需求拍摄员不可上传文件');
        }
        $images = json_decode($param['images'],true);
        if (!count($images)) {
            SetReturn(-1,'拍摄图片必传');
        }
        foreach ($images as $v) {
            if (!isset($v['url']) || !isset($v['thumb_src'])) {
                SetReturn(-1,'图片压缩路径必传');
            }
        }
        //获取文件列表
        $collection_list = $db->table('imgs_request_collection')
            ->where('where request_id=:request_id and is_delete = 0 and source_type=2',['request_id'=>$request_id])
            ->field('id,url')
            ->list();
        $old_imgs_url = array_column($collection_list,'url');
        $db->beginTransaction();
        try {
            //需求修改
            $db->table('take_picture_request')
                ->where('where id=:id',['id'=>$requrst['id']])
                ->update([
                    'remarks'=>$param['remarks'],
                ]);
            //文件处理
            $new_imgs_url = array_column($images,'url');
            $del_url = array_diff($old_imgs_url,$new_imgs_url);
            $add_url = array_diff($new_imgs_url,$old_imgs_url);
            //删除
            if (count($del_url)) {
                $del_ids = [];
                foreach ($del_url as $url) {
                    foreach ($collection_list as $imgs) {
                        if ($imgs['url'] == $url) {
                            $del_ids[] = $imgs['id'];
                            break;
                        }
                    }
                }
                $db->table('imgs_request_collection')
                    ->whereIn('id',$del_ids)
                    ->update(['is_delete'=>1]);
            }
            if (count($add_url)) {
                $db->table('imgs_request_collection');
                foreach ($add_url as $url) {
                    foreach ($images as $v) {
                        if ($url == $v['url']) {
                            $name_array = explode('.',$v['file_name']);
                            $insert_data = [
                                'user_id' => userModel::$qwuser_id,
                                'goods_id'=>$requrst['goods_id'],
                                'request_id'=>$request_id,
                                'file_name'=>$v['file_name'],
                                'url'=>$v['url'],
                                'thumb_src'=>$v['thumb_src'],
                                'created_time'=>date('Y-m-d H:i:s'),
                                'file_type'=>3,
                                'source_type'=>2,
                                'extension'=>end($name_array),
                                'is_confirm'=>(!$is_edit?0:1),
                            ];
                            $db->insert($insert_data);
                        }
                    }
                }
            }
            $db->commit();
            returnSuccess('','保存成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }

    //提交
    public function submitTmg() {
        $paras_list = array('request_id');
        $param = arrangeParam($_POST, $paras_list);
        $request_id = (int)$param['request_id'];
        $db = dbMysql::getInstance();
        $collection_list = $db->table('imgs_request_collection')
            ->where('where request_id=:request_id and is_delete = 0 and is_confirm = 0 and source_type=2',['request_id'=>$request_id])
            ->field('id,url')
            ->list();
        if (!count($collection_list)) {
            SetReturn(-1,'请先上传文件');
        }
        $reques = $db->table('take_picture_request')
            ->where('where id=:id',['id'=>$request_id])
            ->one();
        if ($reques['type'] != 1) {
            SetReturn(-1,'当前需求不可提交');
        }
        if ($reques['qwuser_id'] != userModel::$qwuser_id) {
            SetReturn(-1,'非需求拍摄员不可上传文件');
        }
        $db->beginTransaction();
        try {
            //修改需求状态
            $db->table('take_picture_request')
                ->where('where id=:id',['id'=>$request_id])
                ->update(['status'=>2,'completion_time'=>time()]);
            //图片完成
            $db->table('imgs_request_collection')
                ->where('where request_id=:request_id and is_delete = 0 and is_confirm = 0 and source_type = 2',['request_id'=>$request_id])
                ->update(['is_confirm'=>1]);
            //消息推送到美工
            $user_info = $db->table('qwuser')
                ->where('where id=:id',['id'=>$reques['user_id']])
                ->one();
            $wids = [$user_info['wid']];
            $msg = messagesFrom::getMsgTxtForImgRequest(12,'',$reques['request_name']);
            $other_data = [
                'user_id'=>userModel::$qwuser_id,
                'model_id'=>$request_id,
                'msg_type'=>5
            ];
            messagesFrom::senMeg($wids, $msg, $other_data,$reques['remarks']);
            $db->commit();
            returnSuccess('','提交成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }

    }

    //获取需求图
    public function getImages(){
        $id = (int)$_GET['request_id'];
        if (!$id) {
            SetReturn(-1,'参数有误');
        }
        $db = dbMysql::getInstance();
        $list = $db->table('imgs_request_collection')
            ->where('where request_id=:request_id and is_delete = 0 and source_type=2',['request_id'=>$id])
            ->list();
        returnSuccess(['list'=>$list]);
    }

    //催办
    public function imgRemind() {
        $paras_list = array('request_id','remarks');
        $length_data = ['remarks'=>['name'=>'备注','length'=>225]];
        $param = arrangeParam($_POST, $paras_list,[],$length_data);
        $request_id = (int)$param['request_id'];
        $db = dbMysql::getInstance();
        $imgs_reques = $db->table('take_picture_request')
            ->where('where id=:id',['id'=>$request_id])
            ->field('id,qwuser_id,request_name,status,goods_id')
            ->one();
        if ($imgs_reques['status'] != 1) {
            SetReturn(-1,'暂时不可催办');
        }
        $user_info = $db->table('qwuser')
            ->where('where id=:id',['id'=>$imgs_reques['qwuser_id']])
            ->field('wid')
            ->one();
        $remind_msg = [
            'wids'=>[$user_info['wid']],
            'msg'=>messagesFrom::getMsgTxtForImgRequest(13,'',$imgs_reques['request_name']),
            'other_data'=>[
                'user_id'=>userModel::$qwuser_id,
                'model_id'=>$request_id,
                'msg_type'=>4
            ],
        ];
        messagesFrom::senMeg($remind_msg['wids'],$remind_msg['msg'],$remind_msg['other_data'],$param['remarks']??'');
        returnSuccess('','已提醒');
    }

    //批量下载图片
    public function getZipUrl() {
        $ids = $_POST['ids'];
        $ids = json_decode($ids);
        if (!count($ids)) {
            SetReturn(-1,'请存在下载文件');
        }
        $db = dbMysql::getInstance();
        $img_list = $db->table('imgs_request_collection')
            ->field('id,url,goods_id')
            ->whereIn('id',$ids)
            ->list();
        //生成压缩包
        $save_path = "/public/downLoad/take_pic/temp/".userModel::$wid;
        if (!file_exists(SELF_FK.$save_path)) {
            mkdir(SELF_FK.$save_path, 0777, true);
        }
        $zip_url = $save_path ."/".date('YmdHis').'.zip';
        $count = publicMethod::setZip($img_list,$zip_url,$img_list[0]['goods_id'],1);
        if ($count) {
            returnSuccess(['url'=>$zip_url]);
        } else {
            SetReturn(-1,'未找到本地图片');
        }

    }

    //单个下载、预览
    public function downLoad() {
        $id = $_POST['id'];
        if (!$id) {
            SetReturn(-1,'参数有误');
        }
        $db = dbMysql::getInstance();
        $img = $db->table('imgs_request_collection')
            ->field('id,url,goods_id')
            ->where('where id=:id',['id'=>$id])
            ->one();
        $base64Data = downLoadFrom::getdownLoadBase64Encode($img['url'],$img['goods_id'],1);
        returnSuccess(['data'=>$base64Data]);
        returnSuccess(['data'=>$base64Data]);
    }















}