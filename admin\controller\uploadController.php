<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/20 14:01
 */

namespace  admin\controller;

use admin\common\upLoadBase;
use admin\models\userModel;
use core\lib\db\dbMysql;
use Rap2hpoutre\FastExcel\FastExcel;

class uploadController extends upLoadBase
{
    //上传文件
    public function upload()
    {
        $file = $_FILES;
        if (!count($file)) {
            returnError('文件上传失败，请压缩文件大小');
        }
        //获取文件临时路径
        $temp_name = $file['file']['tmp_name'];
        //获取文件名
        $filename = $file['file']['name'];
        //dd($filename);
        //获取文件的后缀名
        $arr = pathinfo($filename);
        $ext_suffix = $arr['extension'];
        if (!$ext_suffix) {
            returnError('未读取到文件后缀');
        }
        //限制上传类型
        if (!in_array($ext_suffix, self::$allow_suffix)) {
            SetReturn(-1, '暂不支持' . $ext_suffix . '文件类型的上传');
        }
        if (mb_strlen($filename) > 180) {
            SetReturn(-1, '文件名称过长');
        }
        //判断真实的格式是否同后缀一样
        $fileType = mime_content_type($temp_name);
        $real_suffix = explode('/',$fileType)[1];
        if (in_array($ext_suffix, self::$img_suffix)) {
            if (($real_suffix == 'jpg' || $real_suffix == 'jpeg') && ($ext_suffix == 'jpg' || $ext_suffix == 'jpeg')) {
                goto next;
            }
            if ($real_suffix != $ext_suffix) {
                SetReturn(-1, '文件格式有误，文件实际格式为'.$real_suffix);
            }
        }
        next:
        //获取大小
        $size = $file['file']['size'];
        self::varifyFileSize($ext_suffix, $size);

        //创建没有的文件夹
        $url = '/public/upload/' . $ext_suffix . '/' . date('Ymd');
        $save_path = SELF_FK . $url;
        if (!file_exists($save_path)) {
            mkdir($save_path, 0777, true);
        }
        //保存图片
        $file_name = $url . '/' . date('YmdHis', time()) . rand(100, 1000) . '.' . $ext_suffix;
        $new_filename = SELF_FK . $file_name;
        if (move_uploaded_file($temp_name, $new_filename)) {
            //生成压缩图片
            $thumb_data = $this->getThumb($new_filename, $ext_suffix);
            //保存图片到数据库，以便之后清理
            $db = dbMysql::getInstance();
            $into_data = [
                'user_id'=>userModel::$qwuser_id,
                'filename'=>$filename,
                'extension'=>$ext_suffix,
                'src'=>$file_name,
                'thumb_src'=>$thumb_data['thumb_url']??$file_name,
                'cover_image'=>$thumb_data['cover_image']??'',//视频的封面截图
                'size'=>$size.'B',
                'created_at'=>date('Y-m-d H:i:s')
            ];
            $db->query('insert into oa_source_material (user_id,filename,extension,src,thumb_src,size,created_at,cover_image) values (:user_id,:filename,:extension,:src,:thumb_src,:size,:created_at,:cover_image)', $into_data);
            $res = [
                'extension' => $ext_suffix,
                'filename' => $filename,
                "src" => $file_name,
                'thumb_src'=>$thumb_data['thumb_url']??'',
                'cover_image'=>$thumb_data['cover_image']??'',
                'url' => HTTP_HOST . $file_name
            ];
            returnSuccess($res,'');
        } else {
            SetReturn(-1, '上传失败');
        }
    }


    public function getThumb($absolute_path, $ext_suffix){
        if (in_array($ext_suffix, self::$img_suffix)) {
            return self::getThumbImg($absolute_path, $ext_suffix);
        } elseif (in_array($ext_suffix, self::$video_suffix)) {
            return [
                'cover_image'=>'',
                'thumb_url'=>'',
            ];
//            return self::getThumbVideo($absolute_path,$ext_suffix);
        } else {
            return [
                'cover_image'=>'',
                'thumb_url'=>'',
            ];
        }

    }

    //下载excel并获取其中数据
    public function getExcelData()
    {
        $file = $_FILES;
        //获取文件临时路径
        $temp_name = $file['file']['tmp_name'];

        //获取大小
        $size = $file['file']['size'];
        if ($size > 50 * 1024 * 1024) {
            SetReturn(-1, '文件大小不能超过50M');
        }
        $excel_data = (new FastExcel)->import($temp_name);
        $data = $excel_data->toArray();

        $res_list = [];
        if (count($data)) {
            $field_list = formGoodsAttrModel::$field_lsit;
            foreach ($field_list as $k => $v) {
                $res_list[$k] = '';
                foreach ($data as $v1) {
                    if ($v == trim($v1['字段'])) {
                        $res_list[$k] = $v1['内容'];
                    }
                }
            }
        }
        SetReturn(0, '', $res_list);
    }



//    public function gethHtmldata($filled_from_id, $table_id)
//    {
//        $data = [];
//        switch ($table_id) {
//            case 1:
//                $data = $this->getFormGoodsAttr($filled_from_id);
//                break;
//            default:
//                SetReturn(-1, '表单不存在');
//        }
//        return $data;
//    }
//
//    public function getFormGoodsAttr($filled_from_id)
//    {
//        $db = dbMysql::getInstance();
//        $filled_form = $db->query('select * from oa_form_goods_attr where id = ' . $filled_from_id . ' limit 1');
//        if (!$filled_form) {
//            SetReturn(-1, '未找到数据');
//        } else {
//            //内容
//            $form = $db->query('select form_name from oa_form where id = 1 limit 1');
//            $goods = $db->query('select * from oa_goods_new where id = ' . $filled_form['goods_id'] . ' limit 1');
//            //流程和新品名称
//            $flow_path = config::getDataName('flow_path', $filled_form['flow_path_id']);
//            $title = $goods['goods_name'] . $flow_path . $form['form_name'];
//            //填写的内容
//            $html = '';
//            $key = formGoodsAttrModel::$field_lsit;
//            $achieve_key = !empty($data['achieve_key'])?json_decode($data['achieve_key']):'';
//            foreach ($filled_form as $k => $v) {
//                if (isset($key[$k])) {
//                    $html .= '<tr>';
//                    $html .= "<td>$key[$k]</td>";
//                    $html .= "<td>$v</td>";
//                    if ($achieve_key) {
//                        $html .= '<td class="gou">'.(in_array($k,$achieve_key)?'✔':'').'</td>';
//                    } else {
//                        $html .= '<td class="gou"></td>';
//                    }
//                    $html .= '</tr>';
//                }
//            }
//            return ['title' => $title, 'html' => $html];
//        }
//    }
}