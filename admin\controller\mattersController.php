<?php
/**
 * @author: zhangguoming
 * @Time: 2025/3/26 15:24
 */

namespace admin\controller;

use admin\models\userModel;
use plugins\goods\form\mattersNewForm;
use plugins\goods\models\combinedProjectModel;
use plugins\goods\models\goodsMattersModel;

class mattersController
{
    //待办列表获取-我的待办
    public function getWatingList(){
        mattersNewForm::$qwuser_ids = [userModel::$qwuser_id];
        //统计+列表
        $data = [
            'list'=>mattersNewForm::getList(),
            'count_data'=>mattersNewForm::getWatingCountdata(),
        ];
        returnSuccess($data);
    }
    //已办列表-我的已完成
    public function getCompletedList(){
        mattersNewForm::$qwuser_ids = [userModel::$qwuser_id];
        //统计+列表
        $data = [
            'list'=>mattersNewForm::getList()
        ];
        returnSuccess($data);
    }
    //待办，已办左侧导航
    public function getCount() {
        if ($_POST['status'] == -1) {
            //全部待办
            $qwuser_ids = userModel::getIdsByAuth('all_wating_matters');
            mattersNewForm::$qwuser_ids = $qwuser_ids;
            $_POST['status'] = 0;
        } else {
            mattersNewForm::$qwuser_ids = [userModel::$qwuser_id];
        }
        //产品管理
        $flow1_count = mattersNewForm::countData(1);
        //测试样
        $flow2_count = mattersNewForm::countData(2);
        //出货样
        $flow3_count = mattersNewForm::countData(3);
        //抽货样
        $flow4_count = mattersNewForm::countData(4);
        //图片需求
        $flow5_count = mattersNewForm::countData(5);
        //摄影需求
        $flow6_count = mattersNewForm::countData(6);
        //说明书需求
        $flow7_count = mattersNewForm::countData(7);
        //异常问题
        $data = [
            [
                'nav_type'=>'goods',
                'name'=>'产品管理',
                'total_data'=>[
                    ['name'=>'产品管理','nav_id'=>1,'total'=>$flow1_count],
                    ['name'=>'测试样','nav_id'=>2,'total'=>$flow2_count],
                    ['name'=>'出货样','nav_id'=>3,'total'=>$flow3_count],
                    ['name'=>'抽货样','nav_id'=>4,'total'=>$flow4_count],
                    ['name'=>'图片需求','nav_id'=>5,'total'=>$flow5_count],
                    ['name'=>'摄影需求','nav_id'=>6,'total'=>$flow6_count],
                    ['name'=>'说明书需求','nav_id'=>7,'total'=>$flow7_count],
                ]
            ],
        ];
        returnSuccess([
            'data'=>$data,
            'export_key'=>goodsMattersModel::$export_key
        ]);
    }
    //获取全部流程
    public function getAllProcess() {
        $qwuser_ids = userModel::getIdsByAuth('process');
        mattersNewForm::$qwuser_ids = $qwuser_ids;
        $list = mattersNewForm::getProjectList();
        $list['export_key'] = combinedProjectModel::$export_key;
        returnSuccess($list);
    }
    //全部流程左侧导航
    public static function getCountForProcess() {
        $qwuser_ids = userModel::getIdsByAuth('process');
        mattersNewForm::$qwuser_ids = $qwuser_ids;
        //测试样
        $flow1_count = mattersNewForm::countDataProcess(1);
        //出货样
        $flow2_count = mattersNewForm::countDataProcess(2);
        //抽货样
        $flow3_count = mattersNewForm::countDataProcess(3);
        $data = [
            [
                'nav_type'=>'goods',
                'name'=>'产品管理',
                'total_data'=>[
                    ['name'=>'测试样','nav_id'=>1,'total'=>$flow1_count],
                    ['name'=>'出货样','nav_id'=>2,'total'=>$flow2_count],
                    ['name'=>'抽货样','nav_id'=>3,'total'=>$flow3_count],
                ]
            ],
        ];
        returnSuccess([
            'data'=>$data,
            'export_key'=>goodsMattersModel::$export_key
        ]);
        returnSuccess($data);
    }
    //提交
    public function submitMatters() {
        mattersNewForm::submitMuchMatters();
    }
    //交办
    public function changeMatterAgent() {
        mattersNewForm::changeMatterAgent();
    }
    //待办导出
    public function exportWaitingList() {
        mattersNewForm::$qwuser_ids = [userModel::$qwuser_id];
        mattersNewForm::exportList();
    }
    //已办导出
    public function exportCompletedList() {
        mattersNewForm::$qwuser_ids = [userModel::$qwuser_id];
        mattersNewForm::exportList();
    }
    //批量抄送
    public function sendToUser() {
        mattersNewForm::sendToUserMuch();
    }
    //批量催办
    public function nodeAgentRemind() {
        mattersNewForm::nodeAgentRemindMuch();
    }
    //批量暂停
    public function stopProcess() {
        mattersNewForm::stopProjectMuch(1);
    }
    //批量取消暂停
    public function startProcess() {
        mattersNewForm::stopProjectMuch(0);
    }
    //流程导出
    public function exportAllProcess() {
        $qwuser_ids = userModel::getIdsByAuth('process');
        mattersNewForm::$qwuser_ids = $qwuser_ids;
        $url = mattersNewForm::exportAllProject();
        returnSuccess(['url'=>$url]);
    }
    //全部待办
    public function getWatingAllList(){
        $qwuser_ids = userModel::getIdsByAuth('all_wating_matters');
        mattersNewForm::$qwuser_ids = $qwuser_ids;
        //统计+列表
        $data = [
            'list'=>mattersNewForm::getList(),
            'count_data'=>mattersNewForm::getWatingCountdata(),
        ];
        returnSuccess($data);
    }
    //全部待办导出
    public function exportWaitingAllList() {
        $qwuser_ids = userModel::getIdsByAuth('all_wating_matters');
        mattersNewForm::$qwuser_ids = $qwuser_ids;
        mattersNewForm::exportList();
    }
    //全部待办-交办
    public function changeMatterAgentAll() {
        mattersNewForm::changeMatterAgent();
    }
    //全部待办-催办
    public function matterAgentRemind() {
        mattersNewForm::matterAgentRemind();
    }
}