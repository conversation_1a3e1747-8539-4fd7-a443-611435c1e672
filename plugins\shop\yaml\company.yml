openapi: 3.0.0
info:
  title: 公司管理API
  version: 1.0.0
  description: 提供公司管理的相关接口

paths:
  /shop/company/getList:
    get:
      tags:
        - 公司管理
      summary: 获取公司列表
      description: 根据条件筛选获取公司列表
      parameters:
        - name: name
          in: query
          description: 公司名称
          required: false
          schema:
            type: string
        - name: legal_person_id
          in: query
          description: 法人ID
          required: false
          schema:
            type: integer
        - name: status
          in: query
          description: 公司状态
          required: false
          schema:
            type: integer
        - name: city
          in: query
          description: 城市
          required: false
          schema:
            type: string
        - name: tax_number
          in: query
          description: 纳税人识别号
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页条数
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListResponse'

  /shop/company/accept:
    post:
      tags:
        - 公司管理
      summary: 接收公司申请
      description: 接收法人管理发起的公司申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
              properties:
                id:
                  type: integer
                  description: 公司ID
      responses:
        '200':
          description: 接收成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'

  /shop/company/register:
    post:
      tags:
        - 公司管理
      summary: 注册登记
      description: 登记公司注册信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyRegister'
      responses:
        '200':
          description: 注册登记成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'

  /shop/company/edit:
    post:
      tags:
        - 公司管理
      summary: 编辑公司
      description: 编辑公司信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyEdit'
      responses:
        '200':
          description: 编辑成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'

  /shop/company/detail:
    get:
      tags:
        - 公司管理
      summary: 获取公司详情
      description: 根据ID获取公司详细信息
      parameters:
        - name: id
          in: query
          description: 公司ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回详情数据
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailResponse'

  /shop/company/applyCancel:
    post:
      tags:
        - 公司管理
      summary: 申请注销
      description: 提交公司注销申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - reason
              properties:
                id:
                  type: integer
                  description: 公司ID
                reason:
                  type: string
                  description: 注销原因
      responses:
        '200':
          description: 申请成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'

  /shop/company/handleCancel:
    post:
      tags:
        - 公司管理
      summary: 处理注销申请
      description: 处理公司注销申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - is_cancel
                - result
              properties:
                id:
                  type: integer
                  description: 公司ID
                is_cancel:
                  type: boolean
                  description: 是否同意注销
                result:
                  type: string
                  description: 处理结果
      responses:
        '200':
          description: 处理成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'

  /shop/company/changeLegalPerson:
    post:
      tags:
        - 公司管理
      summary: 变更法人
      description: 变更公司法人
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - legal_person_id
              properties:
                id:
                  type: integer
                  description: 公司ID
                legal_person_id:
                  type: integer
                  description: 新法人ID
      responses:
        '200':
          description: 变更成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'

  /shop/company/updateStatus:
    post:
      tags:
        - 公司管理
      summary: 状态维护
      description: 更新公司状态
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - status
              properties:
                id:
                  type: integer
                  description: 公司ID
                status:
                  type: integer
                  description: 公司状态
                remark:
                  type: string
                  description: 状态变更备注
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'

components:
  schemas:
    CompanyRegister:
      type: object
      required:
        - id
        - name
        - city
        - register_type
      properties:
        id:
          type: integer
          description: 公司ID
        name:
          type: string
          description: 公司名称
        city:
          type: string
          description: 注册城市
        register_type:
          type: integer
          description: 注册类型:1-法人自注册,2-公司代注册
        tax_number:
          type: string
          description: 纳税人识别号
        register_coordinator:
          type: string
          description: 注册对接人
        register_date:
          type: string
          format: date-time
          description: 注册时间
        register_address:
          type: string
          description: 注册地址
        house_number:
          type: string
          description: 房屋编号
        license_keeper:
          type: string
          description: 营业执照保管方
        e_license:
          type: string
          description: 电子营业执照
        license_download_date:
          type: string
          format: date
          description: 执照下载日期
        bank_account:
          type: string
          description: 银行公户账号
        accountants:
          type: array
          description: 记账报税人(多选)
          items:
            type: string
        remark:
          type: string
          description: 备注

    CompanyEdit:
      type: object
      required:
        - id
        - name
        - city
        - register_type
      properties:
        id:
          type: integer
          description: 公司ID
        name:
          type: string
          description: 公司名称
        city:
          type: string
          description: 注册城市
        register_type:
          type: integer
          description: 注册类型:1-法人自注册,2-公司代注册
        tax_number:
          type: string
          description: 纳税人识别号
        register_coordinator:
          type: string
          description: 注册对接人
        register_date:
          type: string
          format: date-time
          description: 注册时间
        register_address:
          type: string
          description: 注册地址
        house_number:
          type: string
          description: 房屋编号
        license_keeper:
          type: string
          description: 营业执照保管方
        e_license:
          type: string
          description: 电子营业执照
        license_download_date:
          type: string
          format: date
          description: 执照下载日期
        bank_account:
          type: string
          description: 银行公户账号
        accountants:
          type: array
          description: 记账报税人(多选)
          items:
            type: string
        remark:
          type: string
          description: 备注

    BaseResponse:
      type: object
      properties:
        code:
          type: integer
          example: 0
        message:
          type: string
          example: 操作成功
        data:
          type: object

    ListResponse:
      type: object
      properties:
        code:
          type: integer
          example: 0
        message:
          type: string
          example: 操作成功
        data:
          type: object
          properties:
            list:
              type: array
              items:
                $ref: '#/components/schemas/CompanyEdit'
            total:
              type: integer
              description: 总记录数
            page:
              type: integer
              description: 当前页码
            page_size:
              type: integer
              description: 每页条数

    DetailResponse:
      type: object
      properties:
        code:
          type: integer
          example: 0
        message:
          type: string
          example: 操作成功
        data:
          $ref: '#/components/schemas/CompanyEdit'
