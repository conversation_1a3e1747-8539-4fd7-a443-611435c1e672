<?php
/**
 * @author: zhangguoming
 * @Time: 2024/1/5 9:52
 */

namespace  plugins\goods\controller;

use plugins\goods\form\configFrom;
use plugins\goods\form\goodsAbnormalFrom;
use plugins\goods\form\goodsMattersFrom;
use plugins\goods\form\messagesFrom;
use plugins\goods\models\userModel;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;

class abnormalController
{
    //流程异常获取
    public function getGoodsProjectAbnormal() {
        $paras_list = array('goods_id', 'project_id', 'level', 'is_handled' ,'page', 'page_size','abnormal_type');
        $param = arrangeParam($_POST, $paras_list);
        $goods_id = (int)$param['goods_id'];
        $project_id = (int)$param['project_id'];
        $is_handled = (int)$param['is_handled'];
        $level = (int)$param['level'];
        $abnormal_type = (int)$param['abnormal_type'];
        $sql = 'where 1=1';
        $where_data = [];
        if ($goods_id > 0) {
            $sql .= ' and a.goods_id = :goods_id';
            $where_data['goods_id'] = $goods_id;
        }
        if ($project_id > 0) {
            $sql .= ' and a.project_id = :project_id';
            $where_data['project_id'] = $project_id;
        }
        if ($is_handled > -1) {
            $sql .= ' and a.is_handled = :is_handled';
            $where_data['is_handled'] = $is_handled;
        }
        if ($level > 0) {
            $sql .= ' and a.level = :level';
            $where_data['level'] = $level;
        }

        $db = dbMysql::getInstance();
        $db->table('goods_project_abnormal','a');
        $db->leftJoin('qwuser','b','b.id = a.qw_userid');
        $db->where($sql,$where_data);
        $db->field('a.id,a.responsible_person,a.level,a.abnormal_name,a.title,a.remark,a.is_handled,a.created_at,a.updated_at,a.abnormal_type,a.txt_id,a.images,a.is_remake,a.is_return,a.handled_remark,a.copy_user,a.abnormal_text,b.wname as created_wname');
        $db->order('a.is_handled asc,a.id desc');
        $data = $db->pages($param['page'], $param['page_size']);
        returnSuccess($data);
    }
    //流程异常问题处理
    public function setHandledForProjectAbnormal(){
        $paras_list = array('id','is_solve','is_abolish_project','send_copy_user','remarks','abnormal_text','abnormal_type');
        $param = arrangeParam($_POST, $paras_list);
        $id = (int)$param['id'];
        if (!$id) {
            SetReturn(-1,'参数有误');
        }
        $db = dbMysql::getInstance();
        $db->table('goods_project_abnormal');
        $db->where('where id = '.$id);
        $abnormal = $db->one();
        if ($abnormal['is_handled'] != 0) {
            SetReturn(-1,'该异常已处理，不可重复操作');
        }
        $project = $db->query('select id,matter_name,goods_id from oa_goods_project where id=:project_id',['project_id'=>$abnormal['project_id']]);
        if (!$abnormal) {
            SetReturn(-1,'流程数据不存在');
        }
        $responsible_person = json_decode($abnormal['responsible_person'],true);
        if (userModel::$wid != $responsible_person[0]['wid']) {
            SetReturn(-1,'该异常未交办于您，不可处理');
        }
        $is_solve = (int)$param['is_solve'];
        $is_abolish_project = (int)$param['is_abolish_project'];
        $abnormal_type = $abnormal['abnormal_type'];
        //默认抄送人
        $copy_user = configFrom::getAbnormalCopyUser($abnormal_type,$is_solve==0?1:2,0);
        $copy_user = json_decode($copy_user,true);
        if (!empty($param['send_copy_user'])) {
            $send_copy_user = json_decode($param['send_copy_user'],true);
            $copy_user = array_merge($copy_user,$send_copy_user);
        }
        //提问题人员
        $user_ = $db->table('qwuser')
            ->where('where id=:id',['id'=>$abnormal['qw_userid']])
            ->field('id,wid,wname')
            ->one();
        $copy_user = array_merge($copy_user,[$user_]);
        $copy_user = arrayUnique2($copy_user,'wid');
        $copy_user = removeColumn($copy_user,'avatar');
        $node_name = $param['abnormal_type'] == 1?'软件异常':'硬件异常';
        //获取消息推送人信息
        $db->beginTransaction();
        try {
            if ($is_solve > 0) {
                //已解决
                //修改异常为已解决
                $db->table('goods_project_abnormal');
                $db->where('where id = '.$id);
                $db->update([
                    'is_handled'=>1,'updated_at'=>date('Y-m-d H:i:s'),
                    'copy_user'=>json_encode($copy_user,JSON_UNESCAPED_UNICODE),
                    'handled_remark'=>$param['remarks']
                ]);
                $msg = "流程【{$project['matter_name']}】的【{$abnormal['abnormal_name']}】异常已解决。";
            } else {
                if (!$is_abolish_project) {
                    //未解决
                    $goods_info = $db->table('goods_new')
                        ->where('where id=:id',['id'=>$project['goods_id']])
                        ->field('id,manage_info,goods_name')
                        ->one();
                    if ($abnormal_type == 1) {
                        $manage_info = configFrom::getConfigByName('goods_soft_manage');
                    } else {
                        $manage_info = $goods_info['manage_info'];
                    }
                    //问题记录到产品
                    $abnormal_id = goodsAbnormalFrom::setGoodsAbnormal($project['goods_id'],$project['id'],1,$abnormal_type,$param['abnormal_text'],$manage_info,$id);
                    //添加异常待办
                    $agent_manage = json_decode($manage_info,true);
                    goodsMattersFrom::addCreateMatter($goods_info['goods_name'].'_问题反馈',$project['goods_id'],$node_name,3,6,$agent_manage[0]['id'],$abnormal_id,0);

                    //项目问题和产品问题负责人是同一个就不通知了
                    $db->table('goods_project_abnormal');
                    $db->where('where id = '.$id);
                    $db->update([
                        'is_handled'=>2,'updated_at'=>date('Y-m-d H:i:s'),
                        'copy_user'=>json_encode($copy_user,JSON_UNESCAPED_UNICODE),
                        'abnormal_text'=>$param['abnormal_text']
                    ]);
                    $msg = "流程【{$project['matter_name']}】的【{$abnormal['abnormal_name']}】异常未解决，已提交到产品的问题反馈。";
                } else {
                    if ($abnormal_type == 1) {
                        //软件回厂
                        $db->table('goods_project_abnormal');
                        $db->where('where id = '.$id);
                        $db->update([
                            'is_handled'=>2,'is_return'=>1,'updated_at'=>date('Y-m-d H:i:s'),
                            'copy_user'=>json_encode($copy_user,JSON_UNESCAPED_UNICODE),
                            'handled_remark'=>$param['remarks']
                        ]);
                        $msg = "流程【{$project['matter_name']}】的【{$abnormal['abnormal_name']}】异常未解决，需返厂。";
                    } else {
                        //硬件重新打样
                        $db->table('goods_project_abnormal');
                        $db->where('where id = '.$id);
                        $db->update([
                            'is_handled'=>2,'is_remake'=>1,'updated_at'=>date('Y-m-d H:i:s'),
                            'copy_user'=>json_encode($copy_user,JSON_UNESCAPED_UNICODE),
                            'handled_remark'=>$param['remarks']
                        ]);
                        //直接废除该流程
                        $db->table('goods_project');
                        $db->where('where id=:project_id',['project_id'=>$abnormal['project_id']]);
                        $db->update([
                            'status'=>4,'updated_time'=>date('Y-m-d H:i:s'),
                        ]);
                        $msg = "流程【{$project['matter_name']}】的【{$abnormal['abnormal_name']}】异常未解决，需重新打样。";
                    }
                }
            }
            $wids = array_column($copy_user,'wid');
            $other_data = [
                'user_id'=>userModel::$qwuser_id,
                'model_id'=>$project['id'],
                'node_index'=>$abnormal['node_index'],
                'event_index'=>$abnormal['event_index'],
                'msg_type'=>6
            ];
            messagesFrom::senMeg($wids,$msg,$other_data,$param['remarks']);
            //修改待办事件已完成
            $db->table('goods_matters')
                ->where('where create_type=3 and type=3 and goods_project_id=:project_id and model_id=:model_id',['project_id'=>$project['id'],'model_id'=>$id])
                ->update(['status'=>1]);

            //修改流程状态
            $count = $db->table('goods_project_abnormal')
                ->where('where is_handled = 0 and project_id = '.$abnormal['project_id'])
                ->count();
            if ($count == 0) {
                $db->table('goods_project')
                    ->where('where status = 3 and id = '.$abnormal['project_id'])
                    ->update(['status'=>1]);
            }
            $db->commit();
            SetReturn(0,'处理成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
}