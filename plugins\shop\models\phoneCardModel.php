<?php

namespace plugins\shop\models;

use core\lib\config;
use core\lib\redisCached;
use Exception;

class phoneCardModel extends baseModel
{
    public string $table = 'phone_card';

    public static array $paras_list = [
        'phone_number'  => '电话号码|required',
        'register_date' => '登记时间|required',
        'user_id'       => '电话卡归属人id|required',
        'user_name'     => '电话卡归属人|required',
        'phone_type'    => '电话卡类型|required',
        'phone_combo'   => '套餐情况|required',
        'is_use_self'   => '是否自用|required',
        'phone_usage'   => '用途',
        'use_status'    => '使用状态|required',
        'phone_manager' => '电话卡保管人|required',
        'charge_manage' => '公司充值管理|required',
        'card_status'   => '电话卡状态|required',
        'remark'        => '备注',
    ];

    public static array $json_keys = [
        'phone_usage',
    ];

    public static array $use_status = ['使用中', '未使用'];
    public static array $card_status = ['正常', '注销', '停机', '限制使用'];



    public function __construct()
    {
        parent::__construct();
    }

    public function add($data, $type = '新增')
    {
        return parent::add($data, $type);
    }

    public function edit($data, $id, $old_data = [], $type = '编辑', $remark = '', $result = '', $other_attach = [])
    {
        parent::edit($data, $id, $old_data, $type, $remark, $result, $other_attach);
    }

    public function formatItem($item, $maps = [])
    {
        $user_status = $maps['user_status'] ?? [];
        if (empty($maps['user_status'])) {
            $user_status = config::get('user_status', 'data_salary');
            $user_status = array_column($user_status, 'name', 'id');
        }

        $users = $maps['users'] ?? [];
        if (empty($maps['users'])) {
            $users = redisCached::getUserInfo();
            $users = array_column($users, 'user_name', 'user_id');
        }

        $maps = [
            ['name' => 'operator_name', 'maps' => $users, 'key' => 'operator'],
            ['name' => 'user_name', 'maps' => $users, 'key' => 'user_id'],
            ['name' => 'user_status', 'maps' => $user_status, 'key' => 'user_status']
        ];
        return parent::formatItem($item, $maps);
    }

    // 通过手机号获取信息
    public function getByPhoneNumber($phone_number, $id = null)
    {
        $this->db->table($this->table)->where('where phone_number = :phone_number', ['phone_number' => $phone_number]);
        if ($id) {
            $this->db->andWhere('id != :id', ['id' => $id]);
        }
        return $this->db->one();
    }

    // 数据校验
    public function dataValidCheck(array $data, array $param_list = [], $is_throw = true, array $error = []): array
    {
        if (empty($param_list)) $param_list = self::$paras_list;
        if (empty($data)) {
            if (!$is_throw) {
                $error[] = '用户id和用户姓名不能同时为空';
            } else {
                throw new Exception('电话卡数据不能为空');
            }
        }
        // 内部用户和外部用户只需要一个即可
        if (!isset($data['user_id']) && !isset($data['user_name'])) {
            if (!$is_throw) {
                $error[] = '用户id和用户姓名不能同时为空';
            } else {
                throw new Exception('用户id和用户姓名不能同时为空');
            }
        }
        unset($param_list['user_name']);
        unset($param_list['user_id']);
        return parent::dataValidCheck($data, $param_list, $is_throw, $error);
    }

    public function getList($param, $order = 'id desc', $is_export = false)
    {

        if (isset($param['relations']) && !empty($param['relations'])) {
            $ids = (new relationModel)->getIdsByRelation('phone_card', $param['relations']);
        }

        $this->db->table($this->table, 'pc')
            ->leftJoinOut('db', 'user_info', 'ui', 'ui.qwuser_id = pc.user_id')
            ->field('pc.*, ui.user_status')
            ->where('where 1=1');
        // 固定筛选
        if (!empty($param['ids'])) {
            $this->db->whereIn('pc.id', $param['ids']);
        }
        // 根据电话号码精确查询
        if (!empty($param['phone_number'])) {
            $this->db->andWhere('phone_number = :phone_number', ['phone_number' => $param['phone_number']]);
        }
        // 根据用户ID和姓名组合查询（支持姓名模糊搜索）
        if (!empty($param['user_id']) && !empty($param['user_name'])) {
            $user_id = array_map('intval', $param['user_id']);
            $and_sal = '(';
            $and_arr = [];

            $user_name_str = [];
            foreach ($param['user_name'] as $k => $user_name) {
                $user_name_str[] = ' user_name like :user_name_' . $k . ' or phone_manager like :user_name_' . $k;
                $and_arr['user_name_'.$k] = '%' . $user_name . '%';
            }
            $and_sal .= implode(' or ', $user_name_str) .' or user_id in (';
            $in_str = [];
            foreach ($user_id as $k => $v) {
                $in_str[] = ':user_id_' . $k;
                $and_arr['user_id_' . $k] = $v;
            }
            $and_sal .= implode(', ', $in_str) . '))';
            $this->db->andWhere($and_sal, $and_arr);
        }
        // 仅根据用户ID数组查询
        elseif (!empty($param['user_id'])) {
            $this->db->whereIn('user_id', $param['user_id']);
        } 
        // 仅根据用户姓名或保管人模糊查询
        elseif (!empty($param['user_name'])) {
            $user_name_str = [];
            $and_arr = [];
            foreach ($param['user_name'] as $k => $user_name) {
                $user_name_str[] = ' user_name like :user_name_' . $k . ' or phone_manager like :user_name_' . $k;
                $and_arr['user_name_'.$k] = '%' . $user_name . '%';
            }
            $this->db->andWhere('('.implode(', ', $user_name_str).')', $and_arr);
        }

        // 根据公司充值管理状态筛选
        if (!empty($param['charge_manage'])) {
            $this->db->andWhere('charge_manage = :charge_manage', ['charge_manage' => $param['charge_manage']]);
        }
        // 根据用户状态筛选
        if (!empty($param['user_status'])) {
            if ($param['user_status'] == 1) {
                $this->db->whereIn('user_status', [1,2]);
            } else {
                $this->db->andWhere('ui.user_status = :user_status', ['user_status' => $param['user_status']]);
            }
        }
        // 根据电话卡类型筛选
        if (!empty($param['phone_type'])) {
            $this->db->andWhere('phone_type = :phone_type', ['phone_type' => $param['phone_type']]);
        }
        // 根据登记日期范围筛选
        if (!empty($param['register_date'])) {
            $this->db->andWhere('register_date >= :register_date_start and register_date <= :register_date_end', [
                'register_date_start' => $param['register_date'][0],
                'register_date_end'   => $param['register_date'][1],
            ]);
        }
        // 根据更新时间范围筛选
        if (!empty($param['update_time'])) {
            $this->db->andWhere('updated_at >= :update_time_start and updated_at <= :update_time_end', [
                'update_time_start' => $param['update_time'][0],
                'update_time_end'   => $param['update_time'][1]. ' 23:59:59',
            ]);
        }
        // 根据是否自用筛选
        if (!empty($param['is_use_self'])) {
            $this->db->andWhere('is_use_self = :is_use_self', ['is_use_self' => $param['is_use_self']]);
        }
        // 根据用途筛选（JSON数组格式，支持数组中任一值匹配）
        if ((!empty($param['phone_usage']) && is_array($param['phone_usage'])) || !empty($ids)) {
            $conditions = [];
            $param_arr = [];
            if (!empty($param['phone_usage'])){
                $conditions[] = 'JSON_OVERLAPS(phone_usage, :phone_usage)';
                $param_arr['phone_usage'] = json_encode($param['phone_usage']);
            }
            $ids && $conditions[] = 'pc.id in (' . implode(',', $ids) . ')';
            $this->db->andWhere('(' . implode(' OR ', $conditions) . ')', $param_arr);
        }
        // 根据使用状态筛选
        if (!empty($param['use_status'])) {
            $this->db->andWhere('use_status = :use_status', ['use_status' => $param['use_status']]);
        }
        // 根据状态筛选
        if (!empty($param['card_status'])) {
            $this->db->andWhere('card_status = :card_status', ['card_status' => $param['card_status']]);
        }
        $this->db->order($order);
        if (isset($param['page']) && isset($param['page_size'])) {
            $param['page'] = $param['page'] ?: 1;
            $param['page_size'] = $param['page_size'] ?: 10;
            $list = $this->db->pages($param['page'], $param['page_size']);

            $maps = self::getMaps();
            foreach ($list['list'] as &$item) {
                $item = $this->formatItem($item, $maps);
            }

            return $list;
        } else {
            $list = $this->db->list();
            if ($is_export) {
                $export_data = [];
                $maps = self::getMaps();
                foreach ($list as $item) {
                    $item = $this->formatItem($item, $maps);
                    // 使用参数对应的中文作为键名返回
                    $export_data[] = self::changeToCnKey($item, self::$paras_list);
                }
                
                return $export_data;
            }
            return $list;
        }
    }

    public static function getMaps()
    {
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        $user_status = config::get('user_status', 'data_salary');
        $user_status = array_column($user_status, 'name', 'id');

        return ['users' => $users, 'user_status' => $user_status];
    }

    // 批量编辑
    public function editBatch($data)
    {
        $db = $this->db->table($this->table);
        $ids = array_column($data, 'id');
        $db->whereIn('id', $ids);
        $list = $db->list();
        $list = array_column($list, null, 'id');

        $error_ids = [];
        foreach ($data as $item) {
            try {
                $item_id = $item['id'];
                unset($item['id']);
                $this->dataValidCheck($item);
                // 唯一性校验
                $detail = $this->getByPhoneNumber($item['phone_number'], $item_id);
                if ($detail) {
                    throw new Exception('电话号已存在');
                }
                self::edit($item, $item_id, $list[$item_id], '批量编辑');
            } catch (Exception $e) {
                $error_ids[] = [
                    'id' => $item_id,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $error_ids;
    }
}
