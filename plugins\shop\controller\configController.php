<?php


namespace plugins\shop\controller;

use core\lib\config;
use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use plugins\salary\models\salaryCalculationModel;

class configController extends baseController
{
    public function getList()
    {
        $keys = $_GET['keys'] ?? '';
        if ($keys) {
            $keys = json_decode($keys);
        } else {
            $keys = [];
        }
        $sdb = dbShopMysql::getInstance();
        $sdb->table('config');
        if (count($keys)) {
            $sdb->whereIn('key_name', $keys);
        }
        $list = $sdb->list();
        $list_array = [];
        foreach ($list as $v) {
            $v['data'] = json_decode($v['data'], true);
            $list_array[$v['key_name']] = $v['data'];
        }

        returnSuccess($list_array);
    }

    public function setConfig()
    {
        $config = $_POST['config'];
        if (empty($config)) {
            SetReturn(-1, '参数有误');
        }

        $sdb = dbShopMysql::getInstance();
        $config_data = $sdb->table('config')->list();
        $config_keys = array_column($config_data, 'key_name');

        foreach ($config as $key => $v) {
            if (in_array($key, $config_keys)) {
                //修改
                $update_data = [
                    'data' => is_array($v) ? json_encode($v) : $v,
                ];
                $sdb->table('config')->where('where key_name =:key_name', ['key_name' => $key])->update($update_data);
            } else {
                //新增
                $insert_data = [
                    'key_name' => $key,
                    'data'     => is_array($v) ? json_encode($v) : $v,
                ];
                $sdb->insert($insert_data);
            }
        }
        returnSuccess('', '修改成功');
    }

}