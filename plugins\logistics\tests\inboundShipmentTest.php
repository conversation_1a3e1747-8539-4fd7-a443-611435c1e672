<?php
/**
 * FBA发货单功能单元测试（简化版）
 * @purpose 测试发货单同步和数据处理功能，使用单表存储
 * @Author: System
 * @Time: 2025/06/23
 */

require_once dirname(__DIR__, 3) . '/index.php';

use plugins\logistics\models\inboundShipmentModel;
use core\lib\db\dbErpMysql;

class InboundShipmentTest
{
    private $model;
    private $db;
    private $testData;
    
    public function __construct()
    {
        $this->model = new inboundShipmentModel();
        $this->db = dbErpMysql::getInstance();
        $this->prepareTestData();
    }
    
    /**
     * 准备测试数据
     */
    private function prepareTestData()
    {
        $this->testData = [
            [
                'id' => 12345,
                'shipment_sn' => 'SP' . date('ymd') . '001',
                'status' => 0,
                'shipment_time' => null,
                'shipment_time_second' => '2025-06-23 10:00:00',
                'wid' => 8,
                'wname' => '测试仓库',
                'create_user' => '测试用户',
                'logistics_provider_id' => 'LP001',
                'logistics_provider_name' => '测试物流商',
                'logistics_channel_name' => '测试物流渠道',
                'expected_arrival_date' => '2025-07-01',
                'actual_shipment_time' => null,
                'etd_date' => '2025-06-25',
                'eta_date' => '2025-06-30',
                'delivery_date' => null,
                'gmt_create' => '2025-06-23 10:00:00',
                'is_pick' => 1,
                'is_print' => 0,
                'pick_time' => '2025-06-23 09:00:00',
                'print_num' => 0,
                'head_fee_type' => 0,
                'head_fee_type_name' => '按计费重',
                'head_fee_type_name_new' => '产品-计费重',
                'file_id' => '',
                'update_time' => '2025-06-23 10:00:00',
                'remark' => '测试发货单',
                'is_return_stock' => 0,
                'pay_status' => 0,
                'audit_status' => 0,
                'shipment_user' => '发货员A',
                'is_exist_declaration' => 0,
                'is_exist_clearance' => 0,
                'third_party_order_mode' => 0,
                'third_party_order_status' => 0,
                'vat_code' => 'GB123456789',
                'method_id' => 'M001',
                'method_name' => '海运',
                'is_custom_shipment_time' => 0,
                'destination_fulfillment_center_id' => 'FTW1,BHX2',
                'status_name' => '待发货',
                'is_delete' => 0,
                'logistics' => [
                    [
                        'logistics_list_type' => 1,
                        'replace_tracking_number' => '88803089941',
                        'tracking_number' => '8880308989',
                        'tracking_no' => 'ta1234567',
                        'transport_type' => 2,
                        'transport_type_name' => '海运',
                        'order_type_code' => 4,
                        'order_type_code_name' => '其他',
                        'shippers' => 'emc',
                        'shippers_name' => '长荣emc',
                        'remark' => '这里是备注字段'
                    ]
                ],
                'relate_list' => [
                    [
                        'id' => 1039,
                        'mid' => 318,
                        'destination_fulfillment_center_id' => 'FTW1',
                        'quantity_shipped' => 190,
                        'wname' => '测试仓库',
                        'shipment_sn' => 'SP' . date('ymd') . '001',
                        'shipment_id' => 'FBA15D9PQN10',
                        'shipment_status' => '',
                        'wid' => 8,
                        'pid' => 318,
                        'sid' => 8,
                        'sname' => 'PM1：-test6-A0',
                        'num' => 1,
                        'pic_url' => 'http://ecx.images-amazon.com/images/I/31UvRfeNRPL._SL75_.jpg',
                        'packing_type' => 1,
                        'packing_type_name' => '混装商品',
                        'fulfillment_network_sku' => 'xxxx',
                        'sku' => 'TEST-SKU-001',
                        'fnsku' => '',
                        'msku' => 'xxxx',
                        'nation' => '美国',
                        'apply_num' => 1,
                        'product_id' => 19873,
                        'product_name' => '测试产品',
                        'asin' => 'B07TEST001',
                        'parent_asin' => 'B07PARENT1',
                        'remark' => '',
                        'status' => 0,
                        'is_combo' => 1,
                        'create_by_mws' => 1,
                        'whb_code_list' => [],
                        'product_valid_num' => 100,
                        'product_qc_num' => 0,
                        'seqs' => '',
                        'shipment_plan_quantity_total' => 0,
                        'diff_num' => -1,
                        'sta_shipment_id' => '',
                        'is_sta' => '',
                        'sta_inbound_plan_id' => '',
                        'shipment_order_list' => [
                            [
                                'ispr_id' => 0,
                                'zid' => 0,
                                'isp_id' => '',
                                'isim_id' => '',
                                'isil_id' => '',
                                'seq' => '',
                                'shipment_plan_sn' => '',
                                'shipment_mws_sn' => '',
                                'shipment_list_sn' => '',
                                'shipment_plan_quantity' => 0,
                                'shipment_mws_quantity' => 0,
                                'shipment_list_quantity' => 0,
                                'gmt_create' => '',
                                'gmt_modified' => '',
                                'company_id' => 0,
                                'v_uuid' => ''
                            ]
                        ]
                    ]
                ],
                'not_relate_list' => [],
                'fileList' => [
                    [
                        'file_name' => 'test_document.pdf',
                        'file_path' => '/uploads/shipment/test_document.pdf',
                        'file_type' => 'pdf',
                        'file_size' => 1024000
                    ]
                ]
            ]
        ];
    }
    
    /**
     * 测试数据库连接
     */
    public function testDatabaseConnection()
    {
        echo "测试数据库连接...\n";
        
        try {
            $result = $this->db->query("SELECT 1 as test");
            if ($result && $result['test'] == 1) {
                echo "✓ 数据库连接正常\n";
                return true;
            }
        } catch (Exception $e) {
            echo "✗ 数据库连接失败: " . $e->getMessage() . "\n";
            return false;
        }
        
        echo "✗ 数据库连接异常\n";
        return false;
    }
    
    /**
     * 测试创建数据表
     */
    public function testCreateTables()
    {
        echo "测试创建数据表...\n";
        
        try {
            // 读取SQL文件并执行
            $sqlFile = dirname(__DIR__) . '/sql/inbound_shipment.sql';
            if (!file_exists($sqlFile)) {
                echo "✗ SQL文件不存在: $sqlFile\n";
                return false;
            }
            
            $sql = file_get_contents($sqlFile);
            $statements = array_filter(array_map('trim', explode(';', $sql)));
            
            foreach ($statements as $statement) {
                if (!empty($statement)) {
                    $this->db->exec($statement);
                }
            }
            
            echo "✓ 数据表创建成功\n";
            return true;
            
        } catch (Exception $e) {
            echo "✗ 数据表创建失败: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 测试保存发货单数据
     */
    public function testSaveShipmentList()
    {
        echo "测试保存发货单列表...\n";
        
        try {
            $successCount = $this->model->saveShipmentList($this->testData);
            
            if ($successCount > 0) {
                echo "✓ 保存发货单成功，保存条数: $successCount\n";
                return true;
            } else {
                echo "✗ 保存发货单失败，返回条数: $successCount\n";
                return false;
            }
            
        } catch (Exception $e) {
            echo "✗ 保存发货单异常: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 测试查询发货单列表
     */
    public function testGetShipmentList()
    {
        echo "测试查询发货单列表...\n";
        
        try {
            $params = [
                'page' => 1,
                'page_size' => 10,
                'sync_date' => date('Y-m-d')
            ];
            
            $result = $this->model->getShipmentList($params);
            
            if (is_array($result) && isset($result['list']) && isset($result['total'])) {
                echo "✓ 查询发货单列表成功，总数: {$result['total']}，当前页记录数: " . count($result['list']) . "\n";
                
                // 验证JSON数据是否正确解码
                if (!empty($result['list'])) {
                    $firstItem = $result['list'][0];
                    if (isset($firstItem['logistics']) && is_array($firstItem['logistics'])) {
                        echo "  - 物流轨迹数据解码正常\n";
                    }
                    if (isset($firstItem['relate_list']) && is_array($firstItem['relate_list'])) {
                        echo "  - 关联货件数据解码正常\n";
                    }
                    if (isset($firstItem['fileList']) && is_array($firstItem['fileList'])) {
                        echo "  - 文件列表数据解码正常\n";
                    }
                }
                
                return true;
            } else {
                echo "✗ 查询发货单列表失败，结果格式异常\n";
                return false;
            }
            
        } catch (Exception $e) {
            echo "✗ 查询发货单列表异常: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 测试查询发货单详情
     */
    public function testGetShipmentDetail()
    {
        echo "测试查询发货单详情...\n";
        
        try {
            // 先获取一个发货单ID
            $list = $this->model->getShipmentList(['page' => 1, 'page_size' => 1]);
            if (empty($list['list'])) {
                echo "✗ 没有找到发货单记录，无法测试详情查询\n";
                return false;
            }
            
            $shipmentId = $list['list'][0]['id'];
            $detail = $this->model->getShipmentDetail($shipmentId);
            
            if ($detail && is_array($detail)) {
                echo "✓ 查询发货单详情成功，发货单号: {$detail['shipment_sn']}\n";
                
                // 检查JSON数据解码
                if (isset($detail['logistics']) && is_array($detail['logistics'])) {
                    echo "  - 物流轨迹数量: " . count($detail['logistics']) . "\n";
                }
                if (isset($detail['relate_list']) && is_array($detail['relate_list'])) {
                    echo "  - 关联货件数量: " . count($detail['relate_list']) . "\n";
                }
                if (isset($detail['fileList']) && is_array($detail['fileList'])) {
                    echo "  - 文件数量: " . count($detail['fileList']) . "\n";
                }
                
                return true;
            } else {
                echo "✗ 查询发货单详情失败\n";
                return false;
            }
            
        } catch (Exception $e) {
            echo "✗ 查询发货单详情异常: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 测试按SKU搜索
     */
    public function testSearchBySku()
    {
        echo "测试按SKU搜索...\n";
        
        try {
            $result = $this->model->searchBySku('TEST-SKU-001');
            
            if (is_array($result) && count($result) > 0) {
                echo "✓ SKU搜索成功，找到 " . count($result) . " 条记录\n";
                
                // 验证搜索结果
                $found = false;
                foreach ($result as $item) {
                    if (!empty($item['relate_list'])) {
                        foreach ($item['relate_list'] as $relate) {
                            if (strpos($relate['sku'], 'TEST-SKU-001') !== false) {
                                $found = true;
                                break 2;
                            }
                        }
                    }
                }
                
                if ($found) {
                    echo "  - SKU搜索结果验证成功\n";
                    return true;
                } else {
                    echo "  - SKU搜索结果验证失败\n";
                    return false;
                }
            } else {
                echo "✗ SKU搜索失败，未找到记录\n";
                return false;
            }
            
        } catch (Exception $e) {
            echo "✗ SKU搜索异常: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 测试按ASIN搜索
     */
    public function testSearchByAsin()
    {
        echo "测试按ASIN搜索...\n";
        
        try {
            $result = $this->model->searchByAsin('B07TEST001');
            
            if (is_array($result) && count($result) > 0) {
                echo "✓ ASIN搜索成功，找到 " . count($result) . " 条记录\n";
                
                // 验证搜索结果
                $found = false;
                foreach ($result as $item) {
                    if (!empty($item['relate_list'])) {
                        foreach ($item['relate_list'] as $relate) {
                            if (strpos($relate['asin'], 'B07TEST001') !== false) {
                                $found = true;
                                break 2;
                            }
                        }
                    }
                }
                
                if ($found) {
                    echo "  - ASIN搜索结果验证成功\n";
                    return true;
                } else {
                    echo "  - ASIN搜索结果验证失败\n";
                    return false;
                }
            } else {
                echo "✗ ASIN搜索失败，未找到记录\n";
                return false;
            }
            
        } catch (Exception $e) {
            echo "✗ ASIN搜索异常: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 测试状态映射
     */
    public function testStatusMappings()
    {
        echo "测试状态映射...\n";
        
        try {
            $statusMap = $this->model->getStatusMap();
            $feeTypeMap = $this->model->getHeadFeeTypeMap();
            $pickStatusMap = $this->model->getPickStatusMap();
            $printStatusMap = $this->model->getPrintStatusMap();
            
            if (is_array($statusMap) && count($statusMap) > 0) {
                echo "✓ 状态映射正常，状态数量: " . count($statusMap) . "\n";
            } else {
                echo "✗ 状态映射异常\n";
                return false;
            }
            
            if (is_array($feeTypeMap) && count($feeTypeMap) > 0) {
                echo "✓ 头程费类型映射正常，类型数量: " . count($feeTypeMap) . "\n";
            } else {
                echo "✗ 头程费类型映射异常\n";
                return false;
            }

            if (is_array($pickStatusMap) && count($pickStatusMap) > 0) {
                echo "✓ 拣货状态映射正常，状态数量: " . count($pickStatusMap) . "\n";
            } else {
                echo "✗ 拣货状态映射异常\n";
                return false;
            }

            if (is_array($printStatusMap) && count($printStatusMap) > 0) {
                echo "✓ 打印状态映射正常，状态数量: " . count($printStatusMap) . "\n";
            } else {
                echo "✗ 打印状态映射异常\n";
                return false;
            }
            
            return true;
            
        } catch (Exception $e) {
            echo "✗ 状态映射测试异常: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 测试统计功能
     */
    public function testStatistics()
    {
        echo "测试统计功能...\n";
        
        try {
            $params = [
                'sync_date' => date('Y-m-d')
            ];
            
            $statistics = $this->model->getShipmentStatistics($params);
            
            if (is_array($statistics) && isset($statistics['total']) && isset($statistics['by_status'])) {
                echo "✓ 统计功能正常，总数: {$statistics['total']}\n";
                
                foreach ($statistics['by_status'] as $status => $info) {
                    echo "  - {$info['status_name']}: {$info['count']} 条\n";
                }
                
                return true;
            } else {
                echo "✗ 统计功能异常\n";
                return false;
            }
            
        } catch (Exception $e) {
            echo "✗ 统计功能测试异常: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 测试数据更新（重复保存）
     */
    public function testUpdateShipment()
    {
        echo "测试发货单数据更新...\n";
        
        try {
            // 修改测试数据
            $updateData = $this->testData;
            $updateData[0]['status'] = 1; // 改为已发货
            $updateData[0]['remark'] = '更新后的备注信息';
            $updateData[0]['shipment_time'] = '2025-06-23 14:00:00';
            
            $successCount = $this->model->saveShipmentList($updateData);
            
            if ($successCount > 0) {
                echo "✓ 更新发货单成功\n";
                
                // 验证更新结果
                $list = $this->model->getShipmentList([
                    'shipment_sn' => $updateData[0]['shipment_sn'],
                    'page' => 1,
                    'page_size' => 1
                ]);
                
                if (!empty($list['list']) && $list['list'][0]['status'] == 1) {
                    echo "✓ 数据更新验证成功\n";
                    return true;
                } else {
                    echo "✗ 数据更新验证失败\n";
                    return false;
                }
            } else {
                echo "✗ 更新发货单失败\n";
                return false;
            }
            
        } catch (Exception $e) {
            echo "✗ 更新发货单异常: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 清理测试数据
     */
    public function cleanupTestData()
    {
        echo "清理测试数据...\n";
        
        try {
            // 删除测试发货单数据
            $testShipmentSn = $this->testData[0]['shipment_sn'];
            
            // 直接更新删除标记
            $this->db->table('lingxing_inbound_shipment')
                ->where('shipment_sn = :shipment_sn', ['shipment_sn' => $testShipmentSn])
                ->update(['is_deleted' => 1]);
            
            echo "✓ 测试数据清理完成\n";
            return true;
            
        } catch (Exception $e) {
            echo "✗ 清理测试数据异常: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "=== FBA发货单功能单元测试开始（简化版） ===\n\n";
        
        $tests = [
            'testDatabaseConnection' => '数据库连接测试',
            'testCreateTables' => '数据表创建测试',
            'testSaveShipmentList' => '发货单保存测试',
            'testGetShipmentList' => '发货单列表查询测试',
            'testGetShipmentDetail' => '发货单详情查询测试',
            'testSearchBySku' => 'SKU搜索功能测试',
            'testSearchByAsin' => 'ASIN搜索功能测试',
            'testStatusMappings' => '状态映射测试',
            'testStatistics' => '统计功能测试',
            'testUpdateShipment' => '发货单更新测试'
        ];
        
        $passedTests = 0;
        $totalTests = count($tests);
        
        foreach ($tests as $method => $description) {
            echo "--- $description ---\n";
            $result = $this->$method();
            if ($result) {
                $passedTests++;
            }
            echo "\n";
        }
        
        // 清理测试数据
        echo "--- 清理测试数据 ---\n";
        $this->cleanupTestData();
        echo "\n";
        
        echo "=== 测试完成 ===\n";
        echo "总测试数: $totalTests\n";
        echo "通过测试: $passedTests\n";
        echo "失败测试: " . ($totalTests - $passedTests) . "\n";
        echo "通过率: " . round(($passedTests / $totalTests) * 100, 2) . "%\n";
        
        if ($passedTests == $totalTests) {
            echo "🎉 所有测试通过！\n";
        } else {
            echo "❌ 部分测试失败，请检查问题\n";
        }
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new InboundShipmentTest();
    $test->runAllTests();
} else {
    echo "请在命令行环境下运行此测试\n";
}
