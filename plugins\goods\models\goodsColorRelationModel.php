<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/1 16:58
 */

namespace  plugins\goods\models;

use plugins\goods\common\publicMethod;
use plugins\goods\form\goodsNewFrom;
use core\lib\db\dbMysql;
use function Symfony\Component\Translation\t;

class goodsColorRelationModel
{
    public static array $goods_cate_list = [];
    public static array $function_list = [];
    public static function setGoodsColorRelation($goods_id,$handset_types,$color_ids,$handset_color_ids) {
        $db = dbMysql::getInstance();
        $goods = goodsNewModel::$goods_info;
        $update_log_array = [];
        if (!in_array(0,$handset_types)) {
            $db->table('goods_color_relation')
                ->where('where goods_id = :goods_id and has_handset=0',['goods_id'=>$goods_id])
                ->update(['is_delete'=>1]);
            $update_log_array[] = '删除不带遥控产品颜色';
        }
        if (!in_array(1,$handset_types)) {
            $db->table('goods_color_relation')
                ->where('where goods_id = :goods_id and has_handset=1',['goods_id'=>$goods_id])
                ->update(['is_delete'=>1]);
            $update_log_array[] = '删除带遥控产品颜色';
        }
        $colors = $db->queryAll('select * from oa_goods_color_relation where goods_id = :goods_id and is_delete = 0;',['goods_id'=>$goods_id]);
        //删除的颜色，新增颜色
        $del_ids = [];
        $old_color_ids = [];
        $old_handset_color_ids = [];
        foreach ($colors as $color) {
            if ($color['has_handset'] == 0) {
                $old_color_ids[] = $color['color_id'];
                if (!in_array($color['color_id'],$color_ids)) {
                    $del_ids[] = $color['id'];
                }
            }
            if ($color['has_handset'] == 1) {
                $old_handset_color_ids[] = $color['color_id'];
                if (!in_array($color['color_id'],$handset_color_ids)) {
                    $del_ids[] = $color['id'];
                }
            }
        }
        if (count($del_ids)) {
            $db->table('goods_color_relation')
                ->where('where goods_id=:goods_id', ['goods_id' => $goods_id])
                ->whereIn('id', $del_ids)
                ->update(['is_delete' => 1]);
        }
        //不带遥控产品颜色处理
        $add_ids = array_diff($color_ids,$old_color_ids);
        $add_handset_ids = array_diff($handset_color_ids,$old_handset_color_ids);
        if (count($add_ids)) {
            $db->table('goods_color_relation');
            foreach ($add_ids as $k=>$v) {
                $add_data = [
                    'color_id'=>$v,
                    'goods_id'=>$goods_id,
                    'created_time'=>date('Y-m-d H:i:s')
                ];
                $db->insert($add_data);
            }
        }
        if (count($add_handset_ids)) {
            $db->table('goods_color_relation');
            foreach ($add_handset_ids as $k=>$v) {
                $add_data = [
                    'color_id'=>$v,
                    'has_handset'=>1,
                    'goods_id'=>$goods_id,
                    'created_time'=>date('Y-m-d H:i:s')
                ];
                $db->insert($add_data);
            }
        }
        $is_app = (int)$goods['is_app'];
        //新增测颜色才生成sku，之前的数据不要更改sku
        $all_add_ids = array_merge($add_ids,$add_handset_ids);
        if (count($all_add_ids)) {
            //非app产品设置sku
            if ($is_app == 0) {
                $color_list =  $db->table('goods_color_relation','a')
                    ->leftJoin('goods_color','b','b.id = a.color_id')
                    ->where('where a.goods_id=:goods_id',['goods_id'=>$goods_id])
                    ->whereIn('a.color_id',$all_add_ids)
                    ->field('a.id,a.color_id,a.goods_id,b.color_name_en,a.has_handset')
                    ->list();
                foreach ($color_list as $v) {
                    //生成sku
                    $e_sku = self::getSkuForNotApp($goods['cat_id'],$goods['function_id'],$goods['goods_name'],$v['color_name_en'],$v['has_handset']);
                    $e_sku_count = $db->table('goods_color_relation')
                        ->where('where e_sku=:e_sku and id<>:id',['e_sku'=>$e_sku,'id'=>$v['id']])
                        ->count();
                    if ($e_sku_count) {
                        $sku = $e_sku_count.($e_sku_count+1);
                    } else {
                        $sku = $e_sku;
                    }
                    $db->table('goods_color_relation')
                        ->where('where id=:id',['id'=>$v['id']])
                        ->update(['sku'=>$sku,'e_sku'=>$e_sku]);
                }
            } else {
                //有英文名就修改sku
                if (!empty($goods['e_name'])) {
                    self::setColorSku($goods_id,$goods['e_name'],$all_add_ids);
                }
            }
        }
        //颜色记录
        if (count($add_ids)) {
            $update_log_array[] = '不带遥控颜色'.implode(',',$old_color_ids).'->'.implode(',',$color_ids);
        }
        //点遥控颜色记录
        if (count($add_handset_ids)) {
            $update_log_array[] = '不带遥控颜色'.implode(',',$old_color_ids).'->'.implode(',',$color_ids);
        }
        if (count($update_log_array)) {
            goodsNewFrom::$update_log_data['goods_info'][] = [
                'key_name'=>'颜色ID',
                'data'=>implode(',',$update_log_array),
            ];
        }
    }

    /**
     * @param $goods_id
     * @param $e_name
     * @return void 填写商品英文名时修改商品颜色sku
     * @throws \core\lib\ExceptionError
     */
    public static function setColorSku($goods_id, $e_name,$color_ids = '') {
        $db = dbMysql::getInstance();
        $db->table('goods_color_relation', 'a')
            ->where('where a.goods_id=:goods_id and a.is_delete=0',['goods_id'=>$goods_id])
            ->leftJoin('goods_color','b','b.id = a.color_id');
        if ($color_ids != '') {
            $db->whereIn('a.color_id',$color_ids);
        }
        $color_list = $db
            ->field('a.id,b.color_name_en,a.has_handset')
            ->list();
        foreach ($color_list as $v) {
            $sku = $e_name.'-'.str_replace(' ','',$v['color_name_en']);
            if ($v['has_handset']) {
                $sku .= '-WR';
            }
            $db->query('update oa_goods_color_relation set sku=:sku where id=:id',['id'=>$v['id'],'sku'=>$sku]);
        }
    }
    //将sku滞空
    public static function setColorSkuEmpty($goods_id) {
        $db = dbMysql::getInstance();
        $db->table('goods_color_relation')
            ->where('where goods_id=:goods_id',['goods_id'=>$goods_id])
            ->update(['sku'=>'','e_sku'=>'']);
    }
    /**
     * @param $goods_data
     * @return void 填写商品英文名时修改商品颜色sku (非app产品)
     * @throws \core\lib\ExceptionError
     */
    public static function getSkuForNotApp($cat_ids,$function_ids,$goods_name,$color_name_en,$has_handset) {
        $sku_str = '';
        $db = dbMysql::getInstance();
        if (!count(self::$goods_cate_list)) {
            $cat_list = $db->table('goods_cate')
                ->whereIn('id',json_decode($cat_ids))
                ->list();
            self::$goods_cate_list = $cat_list;
        } else {
            $cat_list = self::$goods_cate_list;
        }
        //分类前缀
        foreach ($cat_list as $cate) {
            if (!empty($cate['prefix'])) {
                $sku_str .= $cate['prefix'];
            }
        }
        //获取首字母-产品中文名
        $first_name = publicMethod::getFirstPinyin($goods_name);
        $sku_str .= '-'.$first_name;
        //功能首字母
        if (!count(self::$function_list)) {
            $function_list = $db->table('goods_function')
                ->whereIn('id',json_decode($function_ids))
                ->list();
            self::$function_list = $function_list;
        } else {
            $function_list = self::$function_list;
        }
        $sku_str .= '-';
        foreach ($function_list as $function) {
            $sku_str .= strtoupper(substr($function['fc_name_en'],0,1));
        }
        //颜色
        $sku_str .= '-'.$color_name_en;
        $sku_str = trim($sku_str,'-');
        if ($has_handset) {
            $sku_str .= '-WR';
        }
        return $sku_str;
    }
}