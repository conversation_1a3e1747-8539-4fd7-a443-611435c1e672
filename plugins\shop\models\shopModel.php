<?php

namespace plugins\shop\models;

use admin\models\qwdepartmentModel;
use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use Exception;

class shopModel extends baseModel
{
    public string $table = 'shop';


    protected $excludeFields = ['password_confirm', 'attachments'];

    public static array $paras_list = [
        "register_type"       => "注册类型|required",
        "shop_number"         => "店铺编号|required",
        "company_id"          => "公司|required",
        "account_type"        => "账号类型|required",
        "price"               => "价格|required",
        "currency"            => "币种|required",
        "coordinator"         => "对接人|required",
        "shop_site"           => "店铺站点|required",
        "business_manager"    => "招商经理|required",
        "register_date"       => "店铺注册日期|required",
        "activation_date"     => "交付激活日期",
        "receive_card_id"     => "收款账号|required",
        "contact_group"       => "对接群",
        "register_device"     => "注册设备|required",
        "phone_card_id"       => "注册手机号",
        "email_id"            => "注册邮箱",
        "credit_card_id"      => "信用卡ID|required",
        "shop_password"       => "店铺密码|required",
        "vat_register_status" => "VAT注册情况|required",
        "epr_register_status" => "EPR注册情况|required",
        "remark"              => "备注",
    ];

    public static array $use_paras_list = [
        // 店铺使用信息
        "use_status"                  => "使用情况",
        "shop_status"                 => "店铺状态",
        "account_purpose"             => "账号用途",
        "account_security_plan"       => "账号状况保障计划",
        "account_security_plan_phone" => "账户状况保障计划电话",
        "trademark_id"                => "品牌",
        "brand_registration"          => "品牌备案情况",
        "account_brand_permission"    => "账号品牌权限",
        "sales_benefits"              => "销售权益",
        "listing_permission"          => "Listing编辑权",
        "product_distribution"        => "产品分布情况",
        "seller_id"                   => "卖家ID",
        "company_status"              => "公司状态",
        "company_update_date"         => "公司状态更新时间",
        "lx_shop_id"                  => "LX店铺ID",
        "identity_verification"       => "Identity verification",
        "bank_account_verification"   => "Bank Account verification",
        "address_verification"        => "Address verification",
        "phone_number"                => "Phone Number",
        "tax_id_number"               => "Tax ID Number",
        "consumer_notification_law"   => "消费者告知法案",                     // 数组
        "video_verification"          => "视频验证",
        "purchase_insurance"          => "购买保险",
        "insurance_period"            => "保险期限",                        // 数组
        "amazon_logistics_plan"       => "亚马逊物流新品入仓优惠计划",
        "kyc"                         => "KYC",
        "real_business_review"        => "真实性经营审核",
        "weee"                        => "WEEE",
        "epr"                         => "EPR",
        "eu_tax_info"                 => "欧带信息",
        "account_type2"               => "账号类型(使用信息)", // 重名了，待定
    ];

    public static array $json_keys = [
        'receive_card_id',
        'consumer_notification_law',
        'insurance_period',
        'trademark_id'
    ];

    // 数据校验
    public function dataValidCheck(array $data, array $param_list = [], $is_throw = true, array $error = []): array
    {
        if (empty($param_list)) $param_list = self::$paras_list;
        // 邮箱和电话卡只需要一个即可
        if (!isset($data['email_id']) && !isset($data['phone_card_id'])) {
            if (!$is_throw) {
                $error[] = '邮箱和电话卡不能同时为空';
            } else {
                throw new Exception('邮箱和电话卡不能同时为空');
            }
        } else {
            if (isset($data['email_id']) && isset($data['phone_card_id'])) {
                if (!$is_throw) {
                    $error[] = '不能同时存在邮箱和电话卡';
                } else {
                    throw new Exception('不能同时存在邮箱和电话卡');
                }
            }
        }
        unset($param_list['email_id']);
        unset($param_list['phone_card_id']);

        if ($data['register_type'] == "自注册") {
            unset($param_list['price']);
            unset($param_list['currency']);
        } elseif ($data['register_type'] == "定制" || $data['register_type'] == '现号') {
            // 如果是编辑
            if (!isset($data['id']) || !$data['id']) {
                unset($param_list['company_id']);
                $legal_person = $data['legal_person'];
                $legal_person_model = new legalPersonModel();
                $legal_person_model->dataValidCheck($legal_person);
                $company = $data['company'];
                $company_model = new companyModel();
                $company_model->dataValidCheck($company);
            }
        }

        return parent::dataValidCheck($data, $param_list, $is_throw, $error);
    }

    public function formatItem($item, $maps = [])
    {
        $users = $maps['users'] ?? [];
        if (empty($maps['users'])) {
            $users = redisCached::getUserInfo();
            $users = array_column($users, 'user_name', 'user_id');
        }
        $deps = $maps['deps'] ?? [];
        if (empty($deps)) {
            $deps = redisCached::getDepartment();
            $deps = array_column($deps, 'name', 'id');
        }
        $company = $maps['company'] ?? [];
        if (empty($company)) {
            $company = redisCached::getCompany();
        }
        $phone_card = $maps['phone_card'] ?? [];
        if (empty($phone_card)) {
            $phone_card = redisCached::getPhoneCard();
        }
        $email = $maps['email'] ?? [];
        if (empty($email)) {
            $email = redisCached::getEmail();
        }
        $credit_card = $maps['credit_card'] ?? [];
        if (empty($credit_card)) {
            $credit_card = redisCached::getCreditCard();
        }
        $receive_card = $maps['receive_card'] ?? [];
        if (empty($receive_card)) {
            $receive_card = redisCached::getReceiveCard();
        }
        $trademark = $maps['trademark'] ?? [];
        if (empty($trademark)) {
            $trademark = redisCached::getTrademark();
        }

        $maps = [
            ['name' => 'operator_name', 'maps' => $users, 'key' => 'operator'],
            ['name' => 'coordinator_name', 'maps' => $users, 'key' => 'coordinator'],
            ['name' => 'dep_name', 'maps' => $deps, 'key' => 'dep_id'],

            // 公司相关
            ['name' => 'company_name', 'maps' => array_column($company, 'company_name', 'id'), 'key' => 'company_id'],
            ['name' => 'company_status', 'maps' => array_column($company, 'company_status', 'id'), 'key' => 'company_id'],
            ['name' => 'company_register_country', 'maps' => array_column($company, 'register_country', 'id'), 'key' => 'company_id'],

            // 注册手机/注册邮箱
            ['name' => 'phone_number', 'maps' => array_column($phone_card, 'phone_number', 'id'), 'key' => 'phone_card_id'],
            ['name' => 'phone_manager', 'maps' => array_column($phone_card, 'phone_manager', 'id'), 'key' => 'phone_card_id'],
            ['name' => 'email_account', 'maps' => array_column($email, 'email_account', 'id'), 'key' => 'email_id'],
            ['name' => 'email_safe_phone_number', 'maps' => array_column($email, 'email_safe_phone_number', 'id'), 'key' => 'email_id'],
            ['name' => 'email_assistant_email', 'maps' => array_column($email, 'email_assistant_email', 'id'), 'key' => 'email_id'],

            // 信用卡
            ['name' => 'credit_card_number', 'maps' => array_column($credit_card, 'card_number', 'id'), 'key' => 'credit_card_id'],
            ['name' => 'credit_card_validity_period', 'maps' => array_column($credit_card, 'validity_period', 'id'), 'key' => 'credit_card_id'],

            // 收款卡
            ['name' => 'receive_card', 'maps' => $receive_card, 'key' => 'receive_card_id', 'is_array' => 1, 'keys' => ['card_number', 'receive_platform']],

            // 公司法人
            ['name' => 'legal_person_id', 'maps' => array_column($company, 'legal_person_id', 'id'), 'key' => 'company_id'],
            ['name' => 'legal_person_name', 'maps' => array_column($company, 'legal_person_name', 'id'), 'key' => 'company_id'],
            ['name' => 'legal_person_id_card_expire', 'maps' => array_column($company, 'legal_person_id_card_expire', 'id'), 'key' => 'company_id'],
            ['name' => 'legal_person_cooperation_level', 'maps' => array_column($company, 'legal_person_cooperation_level', 'id'), 'key' => 'company_id'],
            ['name' => 'legal_person_credit_card_id', 'maps' => array_column($company, 'legal_person_credit_card_id', 'id'), 'key' => 'company_id'],

            // 品牌
            ['name' => 'trademark', 'maps' => $trademark, 'key' => 'trademark_id', 'is_array' => 1, 'keys' => ['brand_name']],
        ];

        $item = parent::formatItem($item, $maps);

        // 过滤字段

        //店铺注册信息（含列表字段权限)
        if (!userModel::getUserListAuth('shopRegist')) {
            unset($item['agreement']);
            foreach (self::$paras_list as $key => $value) {
                if (in_array($key, ['register_date', 'shop_number', 'account_type', 'shop_site'])) continue;
                unset($item[$key]);
            }
        }
        //店铺使用信息（含列表字段权限)
        if (!userModel::getUserListAuth('shopUsage')) {
            foreach (self::$use_paras_list as $key => $value) {
                unset($item[$key]);
            }
        }

        return $item;
    }

    // 数据校验
    public function useDataValidCheck(array $data, array $param_list = [], $is_throw = true, array $error = []): array
    {
        if (empty($param_list)) $param_list = self::$use_paras_list;

        return parent::dataValidCheck($data, $param_list, $is_throw, $error);
    }

    public function getTimeAuthStr($type)
    {
        $auth_detail = userModel::$auth_detail;
        if (!isset($auth_detail['shop'])) {
            return [];
        }
        $str = [];
        $arr = [];
        $idx = 0;
        foreach ($auth_detail['shop'] as $auth_item) {
            if ($auth_item['auth'] != $type) continue;
            if (isset($auth_item['detail']) && !empty($auth_item['detail'])) {
                if ($auth_item['detail']['1'] == 'INF') {
                    $str[] = 's.created_at >= :auth_s_date'.$idx;
                    $arr['auth_s_date'.$idx] = $auth_item['detail']['0'].' 00:00:00';
                } else {
                    $str[] = 's.created_at >= :auth_s_date'.$idx.' and s.created_at <= :auth_e_date'.$idx;
                    $arr['auth_s_date'.$idx] = $auth_item['detail']['0']. ' 00:00:00';
                    $arr['auth_e_date'.$idx] = $auth_item['detail']['1']. ' 23:59:59';
                }
            }
            $idx++;
        }
        if (empty($str)) {
            return [];
        }
        $str = implode(' or ', $str);
        return [
            'str' => '(' . $str . ')',
            'arr' => $arr,
        ];
    }

    /**
     * 获取列表
     */
    public function getList($param, $order = 'id desc', $is_export = false)
    {
        // 权限控制
        $user_id = userModel::$qwuser_id;
        $all_department_users = qwdepartmentModel::getUserDepartment(userModel::$wid, false)['all_department_users'] ?: []; // 当前用户管理的部门及其子部门下的用户
        $user_ids = array_column($all_department_users, 'id');

        // 收款卡号
        $receive_card_ids = [];
        if (!empty($param['receive_card'])) {
            $receive_card = dbShopMysql::getInstance()->table('receive_card')
                ->where('card_number = :card_number', ['card_number' => $param['receive_card']])
                ->list();
            if (empty($receive_card)) {
                if ($is_export) return [];
                returnSuccess(['list' => [], 'page' => $param['page'], 'page_size' => $param['page_size'], 'total' => 0]);
            }
            $receive_card_ids = array_column($receive_card, 'id');
        }

        $this->db->table($this->table, 's')
            ->field('s.*')
            ->leftJoin('company', 'c', 'c.id = s.company_id')
            ->leftJoin('legal_person', 'lp', 'lp.id = c.legal_person_id')
            ->leftJoin('phone_card', 'pc', 'pc.id = s.phone_card_id')
            ->leftJoin('email', 'e', 'e.id = s.email_id')
            ->leftJoin('phone_card', 'epc', 'epc.id = e.email_safe_phone_id')
            ->leftJoin('credit_card', 'cc', 'cc.id = s.credit_card_id')
            ->leftJoinOut('aftersales', 'shop_list', 'sl', 'sl.sid = s.lx_shop_id')
            ->where('1=1');

        if (userModel::getUserListAuth('shopListAll')) {
            $str = $this->getTimeAuthStr('shopListAll');
            if (!empty($str) && !empty($str['str']) && !empty($str['arr'])) {
                $this->db->andWhere($str['str'], $str['arr']);
            }
            // 全部权限
        } elseif (userModel::getUserListAuth('shopListDepartment')) {
            if (!empty($all_department_users)) {
                $this->db->whereIn('s.user_id', $user_ids);
            }
            $str = $this->getTimeAuthStr('shopListDepartment');
            if (!empty($str) && !empty($str['str']) && !empty($str['arr'])) {
                $this->db->andWhere($str['str'], $str['arr']);
            }
        } elseif (userModel::getUserListAuth('shopListRelated')) {
            $this->db->andWhere('s.user_id = :user_id', ['user_id' => $user_id]);
            $str = $this->getTimeAuthStr('shopListRelated');
            if (!empty($str) && !empty($str['str']) && !empty($str['arr'])) {
                $this->db->andWhere($str['str'], $str['arr']);
            }
        } elseif (userModel::getUserListAuth('shopListSelected')) {
            $auth_detail = userModel::$auth_detail;
            $shop_ids = [];
            if (isset($auth_detail['shop'])) {
                foreach ($auth_detail['shop'] as $auth_item) {
                    if ($auth_item['auth'] != 'shopListSelected') continue;
                    $shop_ids = array_merge($shop_ids, $auth_item['detail']);
                }
            }
            if (empty($shop_ids)) {
                if ($is_export) return [];
                returnSuccess(['list' => [], 'page' => $param['page'], 'page_size' => $param['page_size'], 'total' => 0]);
            }
            $this->db->whereIn('s.id', $shop_ids);
        }

        // 固定筛选
        if (!empty($param['ids'])) {
            $this->db->whereIn('s.id', $param['ids']);
        }

        // 店铺名称
        if (!empty($param['shop_name'])) {
            $this->db->andWhere('sl.nick_name like :shop_name', ['shop_name' => '%' . $param['shop_name'] . '%']);
        }

        // 店铺编号
        if (!empty($param['shop_number'])) {
            $this->db->andWhere('shop_number = :shop_number', ['shop_number' => $param['shop_number']]);
        }
        // 电话
        if (!empty($param['phone_card'])) {
            $this->db->andWhere('pc.phone_number = :phone_card', ['phone_card' => $param['phone_card']]);
        }
        // 邮箱
        if (!empty($param['email'])) {
            $this->db->andWhere('e.email_account = :email', ['email' => $param['email']]);
        }
        // 邮箱安全手机
        if (!empty($param['email_safe_phone_number'])) {
            $this->db->andWhere('epc.phone_number = :email_safe_phone_number', ['email_safe_phone_number' => $param['email_safe_phone_number']]);
        }
        // 邮箱辅助邮箱
        if (!empty($param['email_assistant_email'])) {
            $this->db->andWhere('e.email_assistant_email = :email_assistant_email', ['email_assistant_email' => $param['email_assistant_email']]);
        }
        // 信用卡
        if (!empty($param['credit_card'])) {
            $this->db->andWhere('cc.card_number = :credit_card', ['credit_card' => $param['credit_card']]);
        }
        // 收款卡
        if (!empty($receive_card_ids)) {
            $this->db->andWhere('JSON_OVERLAPS(s.receive_card_id, :receive_card_id)', ['receive_card_id' => json_encode($receive_card_ids)]);
        }
        // 公司
        if (!empty($param['company'])) {
            $this->db->andWhere('c.company_name like :company', ['company' => '%' . $param['company'] . '%']);
        }
        // 法人
        if (!empty($param['legal_person'])) {
            $this->db->andWhere('lp.name like :legal_person', ['legal_person' => '%' . $param['legal_person'] . '%']);
        }
        // 对接人
        if (!empty($param['coordinator'])) {
            $this->db->andWhere('coordinator like :coordinator', ['coordinator' => '%' . $param['coordinator'] . '%']);
        }
        // 招商经理
        if (!empty($param['business_manager'])) {
            $this->db->andWhere('business_manager like :business_manager', ['business_manager' => '%' . $param['business_manager'] . '%']);
        }
        // 店铺站点
        if (!empty($param['shop_site']) && is_array($param['shop_site'])) {
            $this->db->whereIn('shop_site', $param['shop_site']);
        }
        // 注册类型
        if (!empty($param['register_type'])) {
            $this->db->andWhere('s.register_type = :register_type', ['register_type' => $param['register_type']]);
        }
        // 公司状态
        if (!empty($param['company_status'])) {
            $this->db->andWhere('c.company_status = :company_status', ['company_status' => $param['company_status']]);
        }
        // 账号类型
        if (!empty($param['account_type'])) {
            $this->db->andWhere('s.account_type = :account_type', ['account_type' => $param['account_type']]);
        }
        // VAT注册情况
        if (!empty($param['vat_register_status'])) {
            $this->db->andWhere('s.vat_register_status = :vat_register_status', ['vat_register_status' => $param['vat_register_status']]);
        }
        // EPR注册情况
        if (!empty($param['epr_register_status'])) {
            $this->db->andWhere('s.epr_register_status = :epr_register_status', ['epr_register_status' => $param['epr_register_status']]);
        }
        //是否法人信用卡
        if (isset($param['is_legal_person_credit_card'])) {
            if ($param['is_legal_person_credit_card'] == '是') {
                $this->db->andWhere('s.credit_card_id = lp.credit_card_id');
            } else {
                $this->db->andWhere('s.credit_card_id != lp.credit_card_id');
            }
        }
        // 注册日期
        if (!empty($param['register_date']) && is_array($param['register_date'])) {
            $this->db->andWhere('s.register_date >= :register_date_start and s.register_date <= :register_date_end', [
                'register_date_start' => $param['register_date'][0],
                'register_date_end'   => $param['register_date'][1]
            ]);
        }
        // 交付/激活日期
        if (!empty($param['activation_date']) && is_array($param['activation_date'])) {
            $this->db->andWhere('s.activation_date >= :activation_date_start and s.activation_date <= :activation_date_end', [
                'activation_date_start' => $param['activation_date'][0],
                'activation_date_end'   => $param['activation_date'][1]
            ]);
        }

        $this->db->order($order);

        if (!$is_export && !empty($param['page']) && !empty($param['page_size'])) {
            $list = $this->db->pages($param['page'], $param['page_size']);
            $maps = self::getMaps();
            foreach ($list['list'] as &$item) {
                $item = $this->formatItem($item, $maps);
            }
            return $list;
        } else {
            $list = $this->db->list();
            if ($is_export) {
                $export_data = [];
                $paras_list = static::$paras_list;
                $use_paras_list = static::$use_paras_list;
                $paras_list = array_merge($paras_list, $use_paras_list);
                $maps = self::getMaps();
                $paras_list['credit_card_number'] = '信用卡';
                $paras_list['credit_card_validity_period'] = '信用卡有效期';
                $paras_list['phone_number'] = '注册手机号';
                $paras_list['phone_manager'] = '手机号持有人';
                $paras_list['email_account'] = '注册邮箱';
                $paras_list['email_safe_phone_number'] = '邮箱安全手机';
                $paras_list['email_assistant_email'] = '邮箱辅助邮箱';
                $paras_list['receive_card'] = '收款账号';
                $paras_list['trademark'] = '品牌';
                foreach ($list as $item) {
                    $item = $this->formatItem($item, $maps);
                    // 使用参数对应的中文作为键名返回
                    $export_data[] = self::changeToCnKey($item, $paras_list);
                }
                return $export_data;
            }

            return $list;
        }
    }

    public static function getMaps()
    {
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');
        $deps = redisCached::getDepartment();
        $deps = array_column($deps, 'name', 'id');
        $company = redisCached::getCompany();
        $phone_card = redisCached::getPhoneCard();
        $email = redisCached::getEmail();
        $credit_card = redisCached::getCreditCard();
        $receive_card = redisCached::getReceiveCard();
        $trademark = redisCached::getTrademark();
        return [
            'users'        => $users,
            'deps'         => $deps,
            'company'      => $company,
            'phone_card'   => $phone_card,
            'email'        => $email,
            'credit_card'  => $credit_card,
            'receive_card' => $receive_card,
            'trademark'    => $trademark,
        ];
    }

    /**
     * 获取店铺详情
     */
    public function getDetail($id)
    {
        return $this->db->table($this->table)
            ->where('id = :id', ['id' => $id])
            ->one();
    }

    // 新增
    public function add($data, $type = '新增')
    {
        if ($data['register_type'] == "自注册") {
            unset($data['price']);
            unset($data['currency']);
        } elseif ($data['register_type'] == '定制') {

        } elseif ($data['register_type'] == '现号') {
            unset($data['company_id']);
            $legal_person = $data['legal_person'];
            $legal_person_model = new legalPersonModel();
            $legal_person['type'] = $data['register_type'];
            // 检查身份证号唯一性
            $detail = $legal_person_model->getByIdCard($legal_person['id_card'] ?? '');
            if ($detail) {
                returnError('法人身份证号已存在');
            }
            $legal_person_id = $legal_person_model->add($legal_person);
            $company = $data['company'];
            $company['legal_person_id'] = $legal_person_id;
            $company['register_status'] = companyModel::REGISTER_STATUS_SUCCESS;
            $company_model = new companyModel();
            $company_id = $company_model->add($company);
            $data['company_id'] = $company_id;
            unset($data['company']);
            unset($data['legal_person']);
        }

        $phoneCardId = $data['phone_card_id'] ?? null;
        $emailId = $data['email_id'] ?? null;
        $creditCardId = $data['credit_card_id'] ?? null;
        $companyId = $data['company_id'] ?? null;
        $receiveCardId = $data['receive_card_id'] ?? null;

        $data = $this->jsonEncodeFormat($data);

        $data['user_id'] = userModel::$qwuser_id ?? 0;

        $id = parent::add($data, $type);

        // 创建关联关系
        $relationModel = new relationModel();
        if ($phoneCardId) {
            $relationModel->updateRelation($this->table, $id, 'phone_card', $phoneCardId);
        }
        if ($emailId) {
            $relationModel->updateRelation($this->table, $id, 'email', $emailId);
        }
        if ($creditCardId) {
            $relationModel->updateRelation($this->table, $id, 'credit_card', $creditCardId);
        }
        if ($companyId) {
            $relationModel->updateRelation($this->table, $id, 'company', $companyId);
        }
        // 收款卡是多选！！！！！！
        $relationModel->deleteRelation($this->table, $id, 'receive_card');
        if ($receiveCardId) {
            // 使用updateRelation方法,会自动处理创建或更新
            foreach ($receiveCardId as $d) {
                $relationModel->createRelation($this->table, $id, 'receive_card', $d);
            }
        }

        return $id;
    }

    // 通过店铺编号获取信息
    public function getByShopNumber($shop_number, $id = null)
    {
        $this->db->table($this->table)->where('where shop_number = :shop_number', ['shop_number' => $shop_number]);
        if ($id) {
            $this->db->andWhere('id != :id', ['id' => $id]);
        }
        return $this->db->one();
    }

    // 编辑
    public function edit($data, $id, $old_data = [], $type = '编辑', $remark = '', $result = '', $other_attach = [])
    {
        if (isset($data['credit_card_id'])) {
            $relationModel = new relationModel();

            if ($data['credit_card_id']) {
                // 使用updateRelation方法,会自动处理创建或更新
                $relationModel->updateRelation($this->table, $id, 'credit_card', $data['credit_card_id']);
            }
        }

        $phoneCardId = $data['phone_card_id'] ?? null;
        $emailId = $data['email_id'] ?? null;
        $creditCardId = $data['credit_card_id'] ?? null;
        $companyId = $data['company_id'] ?? null;
        $receiveCardId = $data['receive_card_id'] ?? null;

        $data = $this->jsonEncodeFormat($data);
        parent::edit($data, $id, $old_data, $type, $remark, $result, $other_attach);

        // 创建关联关系
        $relationModel = new relationModel();
        if ($phoneCardId) {
            $relationModel->updateRelation($this->table, $id, 'phone_card', $phoneCardId);
        }
        if ($emailId) {
            $relationModel->updateRelation($this->table, $id, 'email', $emailId);
        }
        if ($creditCardId) {
            $relationModel->updateRelation($this->table, $id, 'credit_card', $creditCardId);
        }
        if ($companyId) {
            $relationModel->updateRelation($this->table, $id, 'company', $companyId);
        }

        // 收款卡是多选！！！！！！
        $relationModel->deleteRelation($this->table, $id, 'receive_card');
        if ($receiveCardId) {
            // 使用updateRelation方法,会自动处理创建或更新
            foreach ($receiveCardId as $d) {
                $relationModel->createRelation($this->table, $id, 'receive_card', $d);
            }
        }
    }

    // 批量编辑
    public function editBatch($data)
    {
        $db = $this->db->table($this->table);
        $ids = array_column($data, 'id');
        $db->whereIn('id', $ids);
        $list = $db->list();
        $list = array_column($list, null, 'id');

        $paras_list = self::$paras_list;
        unset($paras_list['shop_password']);

        $error_ids = [];
        foreach ($data as $item) {
            try {
                $item_id = $item['id'];
                $this->dataValidCheck($item, $paras_list);
                self::edit($item, $item_id, $list[$item_id], '批量编辑');
            } catch (Exception $e) {
                $error_ids[] = [
                    'id'    => $item_id,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $error_ids;
    }
}
