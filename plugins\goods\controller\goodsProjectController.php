<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/19 14:42
 */

namespace  plugins\goods\controller;

use plugins\goods\common\authenticationCommon;
use plugins\goods\form\configFrom;
use plugins\goods\form\goodsAppFunctionTestFrom;
use plugins\goods\form\goodsHardwareTestFrom;
use plugins\goods\form\goodsProjectAbnormalFrom;
use plugins\goods\form\goodsCateFrom;
use plugins\goods\form\goodsMattersFrom;
use plugins\goods\form\goodsNewFrom;
use plugins\goods\form\goodsProjectFileFrom;
use plugins\goods\form\goodsProjectFrom;
use plugins\goods\form\goodsProjectLogFrom;
use plugins\goods\form\goodsQualityBookFrom;
use plugins\goods\form\imgsRequestFrom;
use plugins\goods\form\instructionsRequestFrom;
use plugins\goods\form\messagesFrom;
use plugins\goods\models\userModel;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;
use core\lib\log;
use task\controller\qwSendMessageController;

class goodsProjectController extends goodsProjectFrom
{
    //项目列表-新品开发
    public function getList() {
        $paras_list = array('goods_name', 'status', 'goods_manage_wid','sample_batch','batch_num','current_node_wid','order_by','flow_path_id','page_size','page');
        $param = arrangeParam($_POST, $paras_list);
        $db = dbMysql::getInstance();
        $db->table('goods_project','a')->where('where a.is_delete = 0');
        $db->leftJoin('goods_new','b', 'b.id = a.goods_id');
        $flow_path_id = (int)$param['flow_path_id'];
        if (!empty($param['goods_name'])) {
            $db->andWhere('and b.goods_name like :goods_name',['goods_name'=>'%'.$param['goods_name'].'%']);
        }
        if ($param['status'] > -1) {
            $db->andWhere('and a.status=:status',['status'=>$param['status']]);
        }
        if ((int)$param['flow_path_id'] > 0) {
            $db->andWhere('and a.flow_path_id=:flow_path_id',['flow_path_id'=>(int)$param['flow_path_id']]);
        } else {
            $db->andWhere('and a.flow_path_id <> 1');
        }
        if (!empty($param['goods_manage_wid'])) {
            $db->andWhere("and b.manage_info->'$[*].wid' like :goods_manage_wid",['goods_manage_wid'=>'%'.$param['goods_manage_wid'].'%']);
        }
        if ($flow_path_id > 0) {
            if ($param['sample_batch'] > 0) {
                if ($param['sample_batch'] == 1) {
                    $db->andWhere("and a.sample_batch = 1");
                    if ($flow_path_id == 2) {
                        if ($param['batch_num'] > 0) {
                            $db->andWhere("and a.batch_num = :batch_num",['batch_num'=>$param['batch_num']]);
                        }
                    }
                } else {
                    $db->andWhere("and a.sample_batch = 2 and a.batch_num = :batch_num",['batch_num'=>($param['sample_batch']-1)]);
                }
            }
        }
        if (!empty($param['current_node_wid'])) {
            $db->andWhere("and a.current_node_info->'$[*].manage_info' like :current_node_wid",['current_node_wid'=>'%'.$param['current_node_wid'].'%']);
        }
        $order_str = '';
        if (!empty($param['order_by'])) {
            $order_by = json_decode($param['order_by'],true);
            foreach ($order_by as $k=>$ord){
                $order_str .= 'a.'.$k.' '.$ord.',';
            }
        }
        if (empty($order_str)) {
            $db->order('a.id desc');
        } else {
            $db->order(trim($order_str,','));
        }
        //数据权限
        if (!userModel::$is_super) {
            //运营只能看自己的
            if (in_array(6,userModel::$role_type)) {
                $db->andWhere('JSON_CONTAINS(operator_info, :id_json)',['id_json'=>json_encode(['id'=>userModel::$qwuser_id])]);
            }
        }
        $db->field("a.id,a.goods_id,a.tpl_name,a.tpl_data,a.current_index,a.tpl_id,a.status,a.flow_path_id,a.sample_batch,a.batch_num,a.expected_day,a.current_node_info,a.created_time,a.expected_day,a.is_stop,b.cat_id,b.manage_info as goods_manage_info,b.goods_name");
        $list = $db->pages($param['page'],$param['page_size']);
        foreach ($list['list'] as &$v) {
            $v['created_time'] = date('Y-m-d H:i:s',$v['created_time']);
            $v['cate_data'] = goodsCateFrom::getGoodsCate(json_decode($v['cat_id']));
            //获取当前数据的节点详情
            $v['current_node_info'] = goodsProjectFrom::getGoodsNodeInfoForList($v);
            unset($v['tpl_data']);
            if ($v['sample_batch'] == 1) {
                if ($v['flow_path_id'] == 2) {
                    if ($v['batch_num'] == 1) {
                        $v['sample_batch_name'] = '首批';
                    } else {
                        $v['sample_batch_name'] = "首货".$v['batch_num']."批次";;
                    }
                } else {
                    $v['sample_batch_name'] = '首批';
                }
            } else {
                $v['sample_batch_name'] = '第'.($v['batch_num']+1).'批';
            }
        }
        returnSuccess($list);
    }
    //废除
    public function abolish() {
        $id = (int)$_POST['id'];
        if (!$id) {
            SetReturn(-1,'参数有误');
        }
        $db = dbMysql::getInstance();
        $db->beginTransaction();
        try {
            $db->table('goods_project');
            $db->where('where id=:project_id',['project_id'=>$id]);
            $db->update(['status'=>4,'updated_time'=>date('Y-m-d H:i:s')]);
            //废除待办事项
            $db->table('goods_matters')
                ->where('where goods_project_id=:project_id and (status=0 or (status=1 and is_advance_submit=1)) and type=3',['project_id'=>$id])
                ->update(['status'=>3,'close_time'=>date('Y-m-d H:i:s'),'close_reason'=>'流程废除']);
            $db->commit();
            SetReturn(0,'操作成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }

    }
    //暂停
    public function stopProject() {
        $id = (int)$_POST['id'];
        if (!$id) {
            SetReturn(-1,'参数有误');
        }
        $is_stop= (int)$_POST['is_stop'];

        $db = dbMysql::getInstance();
        $db->table('goods_project');
        $db->where('where id=:project_id',['project_id'=>$id]);
        $db->update(['is_stop'=>$is_stop,'updated_time'=>date('Y-m-d H:i:s')]);
        SetReturn(0,'操作成功');
    }
    //项目详情
    public function getDetail(){
        $id = (int)$_GET['id'];
        if (!$id) {
            SetReturn(-1,'参数有误');
        }
        $db = dbMysql::getInstance();
        $data = $db->query('select id,flow_path_id,goods_id,status,tpl_name,tpl_data,current_index,expected_day,created_time,is_stop from oa_goods_project where id='.$id);
        if (!$data) {
            SetReturn(-1,'流程不存在');
        }
        //获取商品基础信息
        $goods_info = goodsNewFrom::getGoodsBaseInfo($data['goods_id']);
        $tpl_data = json_decode($data['tpl_data'] ,true);
        //获取详情
        $detail_data = goodsProjectFrom::getProjectDtailTable($id,$tpl_data,$data['current_index']);

        $tpl_data[$data['current_index']] = $detail_data['current_node'];
        $data['tpl_data'] = json_encode($tpl_data);

        $hardware_approval_1 = false;
        $hardware_approval = self::$hardware_list['hardware_approval'];
        if ($hardware_approval && $hardware_approval['is_pass'] == 0) {
            $hardware_approval_1 = $hardware_approval;
        }
        returnSuccess([
            'data'=>$data,
            'goods_info'=>$goods_info,
            'wait_agent_event'=>$detail_data['wait_agent_event'],
            'wait_agent_node'=>$detail_data['wait_agent_node'],
            'finish_detail_list'=>$detail_data['finish_detail_list'],
            'wait_check_event'=>$detail_data['wait_check_event'],
            'hardware_ng_list'=>self::$hardware_list['hardware_ng_list'],
            'hardware_approval'=>$hardware_approval_1,
        ]);
    }
    //提交事件 (完成事件)
    public function submitEvent() {
        $paras_list = array('project_id', 'node_index', 'event_index', 'event_type', 'extra_data','remarks','pfd_html');
        $request_list = ['project_id'=>'项目ID','node_index'=>'节点角标', 'event_index'=>'事件角标'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $project_id = (int)$param['project_id'];
        $type = (int)$param['event_type'];
        $request_data = self::verifyEvenRequestData($param);
        $tpl_data = $request_data['tpl_data'];
        $project_data = $request_data['project'];
        $current_node = $request_data['current_node'];
        $current_event = $request_data['current_event'];

        $db = dbMysql::getInstance();
        $goods_info = $db->query('select id,goods_name,manage_info from oa_goods_new where id=:goods_id',['goods_id'=>$project_data['goods_id']]);
        //状态验证
        if ($current_event['status'] == 2) {
            SetReturn(-1,'该事件已经提交，不可重复操作');
        }
        //当前事件是否以提交
        goodsProjectFrom::verifyEventSubmit($project_id,$param['node_index'],$param['event_index'],$type,$current_event);
        //权限验证
        $manage_info = json_decode($current_event['manage_info'],true);
        authenticationCommon::projectManageVatify($manage_info);
        //根据不同的类型处理不同的数据
        //设置当前事件已完成
        $db->beginTransaction();
        switch ($type) {
            case 1:
                break;
            case 10:
                break;
            case 2:
                //图片
                imgsRequestFrom::appImgIsFinished($goods_info['id']);
                break;
            case 3:
                //app功能测试
                goodsAppFunctionTestFrom::varifyAppFunctionTestSubmit($project_data['id'],$param['node_index'],$param['event_index']);
                break;
            case 4:
                //产品硬件检测
                goodsHardwareTestFrom::varifyHardwareTestSubmit($project_data,$current_event,$param['pfd_html'],$param['node_index'],$param['event_index']);
                break;
            case 5:
                //检测标准书
                goodsQualityBookFrom::varifyQualityBookTestSubmit($project_data['id'],$goods_info);
                break;
            case 6:
                //申请下首单
                break;
            case 7:
                //文件上传
                goodsProjectFileFrom::varifyFileSubmit($project_data['id'],$param['node_index'],$param['event_index']);
                break;
            case 8:
                //文件上传
                SetReturn(-1,'请到【样品管理】收样建档');
            case 9:
                //流程申请
                SetReturn(-1,'【流程申请】事件不应该在流程中，请联系管理员处理检查模板');
                break;
            default:
                SetReturn(-1,'没有该类型的事件');
        }
        try {
            $tpl_data = self::setEventStatus($tpl_data,$param['node_index'],$param['event_index'],2);
            $db->table('goods_project')
                ->where('where id = :project_id',['project_id'=>$project_id])
                ->update([
                    'tpl_data'=>json_encode($tpl_data,JSON_UNESCAPED_UNICODE),
                    'updated_time'=>date('Y-m-d H:i:s')
                ]);
            //待办事项处理为已办
            $current_node = goodsProjectFrom::getCurrentNodeData($tpl_data,$param['node_index']);
            $current_event = goodsProjectFrom::getCurrentEventData($current_node,$param['event_index']);
            if ($type != 4) {
                //硬件测试的待办再验证中完成
                goodsMattersFrom::setProjectMatterStatus1(0,$project_id,$param['node_index'],$param['event_index']);
            }

            //审核待办事件生成 --当前事件
            if ($current_event['need_check'] == 1) {
                goodsMattersFrom::setEventCheckMatter($project_data,$current_node,$current_event,$param['node_index'],$param['event_index']);
            }
            //操作记录
            $matter_name = $project_data['matter_name'];
            $goods_info['matter_name'] = $matter_name;
            $msg = "提交了【{$current_node['node_name']}】节点下的【{$current_event['event_name']}】事件";
            log::setGoodsProjectLog($project_data['goods_id'],$project_id,$msg, $matter_name,$type);
            //消息通知,通知人+抄送人+节点负责人+产品开发+审核信息
            messagesFrom::sendMsgForSubmitEvent($project_id,$current_node,$goods_info,$param['node_index'],$param['event_index'],$param['remarks']);
            //审核人通知
            if ($current_event['need_check'] == 1) {
                $check_msg = goodsMattersFrom::$project_event_check_msg;
                messagesFrom::senMeg($check_msg['wids'],$check_msg['msg'],$check_msg['other_data']);
            }
            //本地记录数据
            $this->setLog($project_data,json_encode($tpl_data,JSON_UNESCAPED_UNICODE));
            $db->commit();
            returnSuccess('','提交成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //节点催办(流程村办，会找到当前待办节点下的所有未办事项发送催办消息)
    public function nodeAgentRemind() {
        $project_id = (int)$_POST['project_id'];
        $remarks = $_POST['remarks'];
        if (!$project_id) {
            SetReturn(-1, '参数有误');
        }
        $project_data = goodsProjectFrom::verifyProject($project_id);
        $tpl_data = json_decode($project_data['tpl_data'],true);
        //当前催办的节点集合
        $current_node_list = $tpl_data[$project_data['current_index']];
        //商品信息
        $db = dbMysql::getInstance();
        $goods_info = $db->query('select goods_name,manage_info from oa_goods_new where id=:goods_id',['goods_id'=>$project_data['goods_id']]);
        $remind_msg = [];
        foreach ($current_node_list as $k=>$node) {
            //已完成的不推送
            if ($node['status'] == 2) {
                continue;
            } else {
                $node_manage_info = json_decode($node['manage_info'], true);
                $node_manage_wid = array_column($node_manage_info,'wid');
                $goods_manage_info = json_decode($goods_info['manage_info'], true);
                $goods_manage_wid = array_column($goods_manage_info,'wid');
                $is_allow_remind = goodsProjectFrom::verifyNodeButtomAuth($node_manage_wid,$goods_manage_wid);
                if (!$is_allow_remind) {
                    continue;
                } else {
                    if ($is_allow_remind != 2) {
                        //给节点负责人推送
                        $remind_msg[] = [
                            'wids'=>$node_manage_wid,
                            'msg'=>messagesFrom::getMsgTxt(4,$project_data['matter_name'],$node['node_name'],''),
                            'other_data'=>[
                                'user_id'=>userModel::$qwuser_id,
                                'model_id'=>$project_data['id'],
                                'node_index'=>$project_data['current_index'].'-'.$k,
                                'event_index'=>null,
                                'msg_type'=>4
                            ],
                        ];
                    }
                    foreach ($node['event_detail'] as $k1=>$ev1) {
                        foreach ($ev1 as $k2=>$event) {
                            if ($event['status'] !=1) {
                                $event_manage_wid = array_column(json_decode($event['manage_info'], true),'wid');
                                $event_manage_wid = array_diff($event_manage_wid,$node_manage_wid);
                                if (count($event_manage_wid)) {
                                    $remind_msg[] = [
                                        'wids'=>$event_manage_wid,
                                        'msg'=>messagesFrom::getMsgTxt(4,$project_data['matter_name'],$node['node_name'],$event['event_name']),
                                        'other_data'=>[
                                            'user_id'=>userModel::$qwuser_id,
                                            'model_id'=>$project_data['id'],
                                            'node_index'=>$project_data['current_index'].'-'.$k,
                                            'event_index'=>$k1.'-'.$k2,
                                            'msg_type'=>4
                                        ],
                                    ];
                                }
                            }
                        }
                    }
                }
            }
        }
        //发送消息
        if (count($remind_msg)) {
            foreach ($remind_msg as $msg) {
                messagesFrom::senMeg($msg['wids'],$msg['msg'],$msg['other_data'],$remarks);
            }
            returnSuccess('','催办消息已发送');
        } else {
            SetReturn(-1, '未能匹配到可催办的事项');
        }
    }
    //流程批量催办-批量
//    public function nodeAgentRemindMuch() {
//        $ids = json_decode($_POST['project_ids']);
//        $remarks = $_POST['remarks'];
//        if (!count($ids)) {
//            SetReturn(-1, '参数有误');
//        }
//        $db = dbMysql::getInstance();
//        $project_list = $db->table('goods_project','a')
//            ->leftJoin('goods_new','b','b.id = a.goods_id')
//            ->whereIn('a.id',$ids)
//            ->field('a.*,b.goods_name,b.manage_info')
//            ->list();
//        $remind_msg = [];
//        foreach ($project_list as $project_data) {
//            $tpl_data = json_decode($project_data['tpl_data'],true);
//            //当前催办的节点集合
//            $current_node_list = $tpl_data[$project_data['current_index']];
//            foreach ($current_node_list as $k=>$node) {
//                if ($node['status'] == 2) {
//                    continue;
//                } else {
//                    $node_manage_info = json_decode($node['manage_info'], true);
//                    $node_manage_wid = array_column($node_manage_info,'wid');
//                    $goods_manage_info = json_decode($project_data['manage_info'], true);
//                    $goods_manage_wid = array_column($goods_manage_info,'wid');
//                    $is_allow_remind = goodsProjectFrom::verifyNodeButtomAuth($node_manage_wid,$goods_manage_wid);
//                    if (!$is_allow_remind) {
//                        continue;
//                    } else {
//                        if ($is_allow_remind != 1) {
//                            //给节点负责人推送
//                            $remind_msg[] = [
//                                'wids'=>$node_manage_wid,
//                                'msg'=>messagesFrom::getMsgTxt(4,$project_data['matter_name'],$node['node_name'],''),
//                                'other_data'=>[
//                                    'user_id'=>userModel::$qwuser_id,
//                                    'model_id'=>$project_data['id'],
//                                    'node_index'=>$project_data['current_index'].'-'.$k,
//                                    'event_index'=>null,
//                                    'msg_type'=>4
//                                ],
//                            ];
//                        }
//                        foreach ($node['event_detail'] as $k1=>$ev1) {
//                            foreach ($ev1 as $k2=>$event) {
//                                if ($event['status'] !=2) {
//                                    $event_manage_wid = array_column(json_decode($event['manage_info'], true),'wid');
//                                    $event_manage_wid = array_diff($event_manage_wid,$node_manage_wid);
//                                    if (count($event_manage_wid)) {
//                                        $remind_msg[] = [
//                                            'wids'=>$event_manage_wid,
//                                            'msg'=>messagesFrom::getMsgTxt(4,$project_data['matter_name'],$node['node_name'],$event['event_name']),
//                                            'other_data'=>[
//                                                'user_id'=>userModel::$qwuser_id,
//                                                'model_id'=>$project_data['id'],
//                                                'node_index'=>$project_data['current_index'].'-'.$k,
//                                                'event_index'=>$k1.'-'.$k2,
//                                                'msg_type'=>4
//                                            ],
//                                        ];
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        //发送消息
//        if (count($remind_msg)) {
//            foreach ($remind_msg as $msg) {
//                messagesFrom::senMeg($msg['wids'],$msg['msg'],$msg['other_data'],$remarks);
//            }
//            returnSuccess('','催办消息已发送');
//        } else {
//            SetReturn(-1, '未能匹配到可催办的事项');
//        }
//    }
    //抄送（流程内）
    public function sendToUser() {
        $paras_list = array('project_id', 'send_copy_user','remarks');
        $request_list = ['project_id'=>'流程id', 'send_copy_user'=>'抄送人'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $project_id = (int)$param['project_id'];
        $send_copy_user = json_decode($param['send_copy_user'],true);
        $copy_user_wid = array_column($send_copy_user,'wid');
        $project_data = goodsProjectFrom::verifyProject($project_id);
        $msg = messagesFrom::getMsgTxt(2,$project_data['matter_name'],'','');
        $other_data = [
            'user_id'=>userModel::$qwuser_id,
            'model_id'=>$project_id,
            'msg_type'=>2
        ];
        messagesFrom::senMeg($copy_user_wid,$msg,$other_data,$param['remarks']);
        returnSuccess('','抄送消息已发送');
    }
    //交办
    public function changeAgent() {
        $paras_list = array('project_id', 'node_info', 'event_info');
        $request_list = ['project_id'=>'流程id'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $project_id = (int)$param['project_id'];
        $node_info = !empty($param['node_info'])?json_decode($param['node_info'],true):0;
        $event_info = !empty($param['event_info'])?json_decode($param['event_info'],true):0;
        if (!$node_info && !$event_info) {
            SetReturn(-1,'请选择交办的类型');
        }
        $project_data = goodsProjectFrom::verifyProject($project_id);
        $db = dbMysql::getInstance();
        $goods_info =  $db->query('select id,manage_info from oa_goods_new where id=:goods_id',['goods_id'=>$project_data['goods_id']]);
        $tpl_data = json_decode($project_data['tpl_data'],true);
        $current_node = $tpl_data[$project_data['current_index']];

        //获取未完成的节点和实事件
        $is_goods_manage = goodsNewFrom::isGoodsMnage($goods_info['manage_info']);
        $incomplete_data = goodsProjectFrom::getIncompleteNodeEvent($current_node,$is_goods_manage);
        $in_node_list = $incomplete_data['node_list'];
        $in_event_list = $incomplete_data['event_list'];
        //节点交办
        if (count($node_info)) {
            foreach ($node_info as $k=>$v) {
                if (!isset($in_node_list[$v['node_index']])) {
                    unset($node_info[$k]);
                } else {
                    //交办人是否再处理人之中
                    $olds_manage = json_decode($in_node_list[$v['node_index']]['manage_info'],true);
                    if ($v['agent_info']['agent']['wid'] == $olds_manage[0]['wid']) {
                        SetReturn(-1,'交班人不可以是处理人');
                    }
                    $node_info[$k]['node_index'] = $project_data['current_index'].'-'.$node_info[$k]['node_index'];
                    $node_info[$k]['node_name'] = $in_node_list[$v['node_index']]['node_name'];
                    if (!empty($v['agent_info']['agent'])) {
                        //节点负责人更换
                        $current_node[$k]['manage_info'] = json_encode([$v['agent_info']['agent']],JSON_UNESCAPED_UNICODE);
                    }
                }
            }
        }
        //事件交办
        if (count($event_info)) {
            foreach ($in_event_list as $kk => $vv) {
                $is_agent = 0;
                $event_index_array = [];
                $new_manage = json_decode($vv['manage_info'], true);
                foreach ($event_info as $k => $v) {
                    $event_info[$k]['node_name'] = $vv['node_name'];
                    $event_info[$k]['event_name'] = $vv['event_name'];
                    $index = $v['node_index'] . '-' . $v['event_index'];
                    if (!isset($in_event_list[$index])) {
                        continue;
                    } else {
                        $event_index_array = explode('-', $v['event_index']);
                        //交办人是否再处理人之中
                        $olds_manage = json_decode($vv['manage_info'], true);
                        $wids = array_column($olds_manage, 'wid');
                        if (in_array($v['agent_info']['agent']['wid'], $wids)) {
                            SetReturn(-1, '交办人不可以在处理人中');
                        }
                        foreach ($olds_manage as $k => $manage) {
                            if ($manage['wid'] == $v['agent_info']['manage']['wid']) {
                                $is_agent = 1;
                                if (!empty($v['agent_info']['agent'])) {
                                    $new_manage[$k] = $v['agent_info']['agent'];
                                }
                            }
                        }
                    }
                }
                if ($is_agent) {
                    $current_node[$v['node_index']]['event_detail'][$event_index_array[0]][$event_index_array[1]]['manage_info'] = json_encode($new_manage, JSON_UNESCAPED_UNICODE);
                }
            }
        }
        foreach ($event_info as $k=>$v) {
            $event_info[$k]['node_index'] = $project_data['current_index'].'-'.$event_info[$k]['node_index'];
            $index = $v['node_index'] . '-' . $v['event_index'];
            if (!isset($in_event_list[$index])) {
                unset($event_info[$k]);
            }
        }
        if (!count($node_info) && !count($event_info)) {
            SetReturn(-1,'没有满足可交办的的节点或事件');
        }
        $db->beginTransaction();
        try {
            $tpl_data[$project_data['current_index']] = $current_node;
            //修改流程节点和事件处理人
            $db->table('goods_project')->where('where id=:project_id',['project_id'=>$project_data['id']]);
            $db->update([
                'tpl_data'=>json_encode($tpl_data,JSON_UNESCAPED_UNICODE),
                'updated_time'=>date('Y-m-d H:i:s'),
            ]);
            //修改待办事项交办人+交办记录
            if (count($node_info)) {
                goodsMattersFrom::updateNodeAgentForProject($node_info,$project_data['goods_id'],$project_id,$project_data['matter_name']);
            }
            if (count($event_info)) {
                goodsMattersFrom::updateEventAgentForProject($event_info,$project_data['goods_id'],$project_id,$project_data['matter_name']);
            }

            //生成记录
            goodsProjectLogFrom::changeAgentLog();
            //发送待办消息
            messagesFrom::senMsgForProjectAgent();
            $db->commit();
            //本地json记录
            $this->setLog($project_data,json_encode($tpl_data,JSON_UNESCAPED_UNICODE));
            returnSuccess('','交办成功');
        } catch (ExceptionError $e) {
            $db->rollBack();
            throw new ExceptionError($e->getMessage());
        }
    }
    //节点提交(暂时不用)
    public function submitNode(){
        $paras_list = array('project_id', 'node_index','remarks');
        $request_list = ['project_id'=>'项目ID','node_index'=>'节点角标'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $project_id = (int)$param['project_id'];
        $db = dbMysql::getInstance();
        $project_data =  $db->query('select id,matter_name,goods_id,tpl_name,status,tpl_data,current_index,flow_path_id,sample_batch from oa_goods_project where id=:project_id',['project_id'=>$project_id]);
        $goods_info = $db->query('select id,goods_name,manage_info from oa_goods_new where id=:goods_id',['goods_id'=>$project_data['goods_id']]);
        $goods_info['matter_name'] = $project_data['matter_name'];
        $tpl_data = json_decode($project_data['tpl_data'],true);
        $current_node = goodsProjectFrom::getCurrentNodeData($tpl_data,$param['node_index']);
        //状态验证
        if ($current_node['status'] == 2) {
            SetReturn(-1,'该节点已经提交，不可重复操作');
        }
        //判断当前节点是否可提交
        goodsProjectFrom::verifyNodeStatus($current_node,$project_id,$param['node_index']);
        //权限验证
        authenticationCommon::projectManageVatify(json_decode($current_node['manage_info'],true));
        //设置当前事件已完成
        $db->beginTransaction();
        try {
            //当前节点待办事项处理
            goodsMattersFrom::setProjectMatterStatus1(0,$project_id,$param['node_index']);
            //模板数据更新
            //更新当前节点
            $tpl_data = self::setNodeStatus($tpl_data,$param['node_index'],2,$project_id);
            $update_data = [
                'updated_time'=>date('Y-m-d H:i:s'),
            ];
            //节点集合中是否所有节点已完成
            $is_all_finished = goodsProjectFrom::getNodeListStatus($tpl_data[$project_data['current_index']]);
            if ($is_all_finished) {
                //获取下个节点简要信息
                $next_node = goodsProjectFrom::getNextNodeInfo($tpl_data,$project_data['current_index']);
                $update_data['current_index'] = $project_data['current_index']+1;
                $update_data['current_node_info'] = json_encode($next_node);
                //更新下个节点开始时间
                $tpl_data = self::setNodeBegintime($tpl_data,$update_data['current_index']);
                //生成下个节点待办事项
                goodsMattersFrom::addNextNodeMatter($project_id,$tpl_data[$update_data['current_index']],$goods_info,$update_data['current_index']);
            }
            $update_data['tpl_data'] = json_encode($tpl_data,JSON_UNESCAPED_UNICODE);
            $db->table('goods_project')
                ->where('where id = :project_id',['project_id'=>$project_id])
                ->update($update_data);

            //操作记录
            $matter_name = $project_data['matter_name'];
            $goods_info['matter_name'] = $matter_name;
            $msg = "提交了【{$current_node['node_name']}】节点。";
            log::setGoodsProjectLog($project_data['goods_id'],$project_id,$msg, $matter_name);
            //消息通知当前节点,通知人+抄送人+节点负责人+产品开发
            messagesFrom::sendMsgForSubmitNode($project_id,$current_node,$goods_info,$param['node_index']);
            //通知下个节点的开发
            if ($is_all_finished) {
                messagesFrom::sendNextNodeInfo($param['remarks']);
            }
            //本地记录数据
            $this->setLog($project_data,json_encode($tpl_data,JSON_UNESCAPED_UNICODE));
            $db->commit();
            returnSuccess('','提交成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //节点审核
    public function checkNode(){
        $paras_list = array('project_id', 'node_index', 'is_pass', 'remarks', 'bad_event', 'send_copy_user');
        $param = arrangeParam($_POST, $paras_list);
        //拒绝事件验证
        $is_pass = (int)$param['is_pass'];
        $ext_send_copy_user = empty($param['send_copy_user'])?[]:json_decode($param['send_copy_user'],true);
        $bad_event = empty($param['bad_event'])?[]:json_decode($param['bad_event'],true);
        if ($is_pass == 0) {
            if (!count($bad_event)) {
                SetReturn(-1,'请选择存在问题的事件');
            }
        }
        //节点基础验证
        $request_data = self::verifyNodeRequestData($param);
        $tpl_data = $request_data['tpl_data'];
        $project = $request_data['project'];
        $current_node = $request_data['current_node'];
        goodsProjectFrom::verifyNodeStatus($current_node,$project['id'],$param['node_index']);
        //节点完成状态判断
        self::verifyNodeStatus($current_node, $project['id'], $param['node_index']);
        $db = dbMysql::getInstance();
        $db->beginTransaction();
        try {
            if ($is_pass == 0) {
                $event_type_array = goodsProjectFrom::$check_node_event_type;
                if (in_array(4,$event_type_array)) {
                    SetReturn(-1,'产品硬件检测不应该走到这里');
                }
                //获取打回的事件发送数据
                $bad_data = goodsProjectFrom::getBadEventSendData($current_node['event_detail'],$bad_event,$project,$param['node_index'],$current_node,$param['remarks']);
                //日志记录
                $describe = $bad_data['log_txt'];
                log::setGoodsProjectLog($project['goods_id'],$project['id'], $describe, $project['matter_name']);
                //修改对应事件为进行中
                $node_array = explode('-',$param['node_index']);
                $tpl_data[$node_array[0]][$node_array[1]] = $bad_data['new_current_node'];
                $db->table('goods_project')
                    ->where('where id =:id',['id'=>$project['id']])
                    ->update([
                        'tpl_data'=>json_encode($tpl_data,JSON_UNESCAPED_UNICODE),
                        'updated_time'=>date('Y-m-d H:i:s')
                    ]);
                //抄送人消息发送
                $copy_user = array_merge($ext_send_copy_user,json_decode($current_node['send_copy_user'],true));
                $copy_user = arrayUnique2($copy_user,'wid');
                $other_data = [
                    'user_id'=>userModel::$qwuser_id,
                    'model_id'=>$project['id'],
                    'node_index'=>$param['node_index'],
                    'msg_type'=>2
                ];
                messagesFrom::senMeg(array_column($copy_user,'wid'),$describe,$other_data,$param['remarks']);
                //发送消息给负责人
                foreach ($bad_data['qw_msg_data'] as $msg) {
                    messagesFrom::senMeg($msg['wids'],$msg['msg'],$msg['other_data'],$msg['remarks']);
                }
                //本地记录数据
                $this->setLog($project,json_encode($tpl_data,JSON_UNESCAPED_UNICODE));
            }
            else
            {
                //当前节点下的事件类型
                $event_type_array = goodsProjectFrom::$check_node_event_type;
                //产品信息
                $goods_info = $db->query('select * from oa_goods_new where id=:goods_id',['goods_id'=>$project['goods_id']]);
                $goods_info['matter_name'] = $project['matter_name'];
                //当前节点待办事项处理
                $project_id = $project['id'];
                goodsMattersFrom::setProjectMatterStatus1(0,$project_id,$param['node_index']);
                //模板数据更新，生成下个节点待办
                self::updateTplDataForNodeStatus1($tpl_data,$project_id,$project['current_index'],$goods_info,$param['node_index']);
                if (in_array(10,$event_type_array)) {
                    imgsRequestFrom::setAppPicRequest($goods_info);
                }
                //操作记录
                $matter_name = $project['matter_name'];
                $goods_info['matter_name'] = $matter_name;
                $msg = "提交了【{$current_node['node_name']}】节点。";
                log::setGoodsProjectLog($project['goods_id'],$project_id,$msg, $matter_name);
                $ext_send_copy_user = self::getSendCopyUser($ext_send_copy_user,$project_id);
                //消息通知当前节点,通知人+抄送人+节点负责人+产品开发
                messagesFrom::sendMsgForSubmitNode($project_id,$current_node,$goods_info,$param['node_index'],$ext_send_copy_user);
                //通知下个节点的开发
                if (self::$jump_next_node) {
                    messagesFrom::sendNextNodeInfo($param['remarks']);
                }
                if (in_array(10,$event_type_array)) {
                    //将消息发送给产品图片负责人
                    foreach (imgsRequestFrom::$msg_info as $msg) {
                        messagesFrom::senMeg($msg['wids'],$msg['msg'],$msg['other_data'],$param['remarks']);
                    }
                }
                //本地记录数据
                $this->setLog($project,json_encode($tpl_data,JSON_UNESCAPED_UNICODE));
            }
            $db->commit();
            SetReturn(0,'审核成功');
        } catch (ExceptionError $e) {
            $db->rollBack();
            return new ExceptionError($e->getMessage());
        }
    }
    //异常添加
    public function addProjectAbnormal(){
        $paras_list = array('project_id', 'node_index', 'event_index','imgs','remark','abnormal_type','txt_id');
        $request_list = ['project_id'=>'项目ID','abnormal_type'=>'异常类型','txt_id'=>'具体问题'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $project_id = (int)$param['project_id'];
        $project = goodsProjectFrom::verifyProject($project_id);
        $db = dbMysql::getInstance();
        if (!$project) {
            SetReturn(1,'未查询到对应的项目流程');
        }
        $db->beginTransaction();
        try {
            //保存异常数据
            if ($param['abnormal_type'] == 1) {
                $manage_info = configFrom::getConfigByName('goods_soft_manage');
                if (empty($manage_info)) {
                    SetReturn(1,'软件处理人尚未配置');
                }
            } else {
                $goods_info =  $db->query('select id,manage_info from oa_goods_new where id=:goods_id',['goods_id'=>$project['goods_id']]);
                $manage_info = $goods_info['manage_info'];
            }
            $abnormal_id =  goodsProjectAbnormalFrom::addAbnormal($project,$param,$manage_info);
            //添加异常待办
            $agent_manage = json_decode($manage_info,true);
            $node_name = $param['abnormal_type'] == 1?'软件异常':'硬件异常';
            goodsMattersFrom::addCreatePojectMatter($project['matter_name'],$project['goods_id'],$node_name,3,$agent_manage[0]['id'],$project_id,0,$abnormal_id);
            //设置流程为异常状态 节点和事件状态不修改异常-根据异常数据来显示
            $db->query('update oa_goods_project set status=3 where id=:project_id',['project_id'=>$project['id']]);
            //发送消息给问题处理人
            $manage_info = json_decode($manage_info,true);
            $msg_data = goodsProjectAbnormalFrom::$abnormal_msg_data;
            messagesFrom::senMeg(array_column($manage_info,'wid'),$msg_data['msg'],$msg_data['other_data'],$param['remark']);
            $db->commit();
            returnSuccess('','提交成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
        if (!$project) {
            SetReturn(-1,'未找到对应的项目流程');
        }
        returnSuccess('','提交成功');
    }
    //流程内事件-文件上传
    public function uploadFile(){
        $paras_list = array('project_id','node_index', 'event_index','event_type','file_data','remarks');
        $request_list = ['project_id'=>'项目ID','node_index'=>'节点角标','event_index'=>'事件角标'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        if ($param['event_type'] != 7) {
            SetReturn(-1, '该事件类型不能上传文件');
        }
        $request_data = self::verifyEvenRequestData($param);
        $project = $request_data['project'];
        $current_event = $request_data['current_event'];
        if (!$param['file_data']) {
            SetReturn(-1, '文件上传数据不能未空');
        }
        $file_data = json_decode($param['file_data'], true);
        if (!count($file_data)) {
            SetReturn(-1, '文件上传数据不能为空');
        }
        $db = dbMysql::getInstance();
        $db->beginTransaction();
        try {
            //保存文件
            $this->saveProjectFile($project,$file_data,$param['node_index'],$param['event_index']);
            //操作记录
            $file_name = array_column($file_data, 'filename');
            $file_name_string = implode(',',$file_name);
            log::setGoodsProjectLog($project['goods_id'],$project['id'],'在事件【'.$current_event['event_name'].'】中上传文件【'.$file_name_string.'】',$project['matter_name'],7);

            $db->commit();
            returnSuccess('','保存成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //事件审核
    public function checkEvent(){
        $paras_list = array('project_id', 'node_index', 'is_pass', 'event_index','remarks', 'send_copy_user','reason','event_type');
        $request_list = ['project_id'=>'项目ID','node_index'=>'节点角标', 'event_index'=>'事件角标'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $is_pass = (int)$param['is_pass'];//0,1,2: 未审核，通过，不通过
        //项目的数据验证
        $request_data = self::verifyEvenRequestData($param,1);
        $tpl_data = $request_data['tpl_data'];
        $project = $request_data['project'];
        $current_node = $request_data['current_node'];
        $current_event = $request_data['current_event'];

        $db = dbMysql::getInstance();
        $goods_info = $db->query('select id,goods_name,manage_info from oa_goods_new where id=:goods_id',['goods_id'=>$project['goods_id']]);
        $db->beginTransaction();
        try {
            //保存审核人信息
            $data_ = goodsProjectFrom::setEventCheckedInfo($tpl_data,$param['node_index'],$param['event_index'],$is_pass,$param['reason'],$current_event['event_type']);
            $is_all_pass = $data_['is_all_pass'];
            $tpl_data = $data_['tpl_data'];
            //完成待办是事项
            $db->table('goods_matters')
                ->where('where qwuser_id=:qwuser_id and goods_project_id=:project_id and node_index=:node_index and event_index=:event_index and create_type=1',['qwuser_id'=>userModel::$qwuser_id,'project_id'=>$project['id'],'node_index'=>$param['node_index'],'event_index'=>$param['event_index']])
                ->update([
                    'status'=>1,
                    'is_pass'=>$is_pass,
                    'check_reason'=>$param['reason']
                ]);
            //修改流程数据
            $db->table('goods_project');
            $db->where('where id = '.$project['id']);
            $update_data = ['tpl_data' => json_encode($tpl_data,JSON_UNESCAPED_UNICODE),'updated_time'=>date('Y-m-d H:i:s')];
            if ($db->update($update_data)) {
                //全部审核通过要干的事情
                if ($current_event['event_type'] == 6 && $is_all_pass) {
                    //申请下首单通过开启说明书流
                    instructionsRequestFrom::setIsRequest($project['id'],$goods_info,$project['matter_name']);
                }
                //审核通知
                $msg = messagesFrom::getMsgTxt(($is_pass==1?9:7),$project['matter_name'],$current_node['node_name'],$current_event['event_name']);
                $send_copy_user = empty($param['send_copy_user'])?[]:json_decode($param['send_copy_user'],true);
                $wids = self::getEventSendCopyUser($current_event['event_type'],$project['id'],$send_copy_user);
                $goods_manage = json_decode($goods_info['manage_info'],true);
                $wids[] = $goods_manage[0]['wid'];
                $event_manage = json_decode($current_event['manage_info'],true);
                $wids[] = $event_manage[0]['wid'];
                $other_data = [
                    'user_id'=>userModel::$qwuser_id,
                    'model_id'=>$project['id'],
                    'node_index'=>$param['node_index'],
                    'event_index'=>$param['event_index'],
                    'msg_type'=>7
                ];
                messagesFrom::senMeg($wids,$msg,$other_data,($is_pass==1?$param['remarks']:$param['reason']));
                //其他消息发送
                $IsRequest_msg = instructionsRequestFrom::$msg_info;
                if (count($IsRequest_msg)) {
                    messagesFrom::senMeg($IsRequest_msg['wids'],$IsRequest_msg['msg'],$IsRequest_msg['other_data']);
                }
                $db->commit();
                //本地记录
                $this->setLog($project,json_encode($tpl_data,JSON_UNESCAPED_UNICODE));
                SetReturn(0,'保存成功');
            }
        } catch (ExceptionError $e) {
            $db->rollBack();
            return new ExceptionError($e->getMessage());
        }
    }
    //项目批量抄送
//    public function sendToUserMuch() {
//        $paras_list = array('ids', 'send_copy_user','remarks');
//        $request_list = ['ids'=>'事项id', 'send_copy_user'=>'抄送人'];
//        $param = arrangeParam($_POST, $paras_list, $request_list);
//        $ids = json_decode($param['ids'],true);
//        if (!count($ids)) {
//            SetReturn(-1,'请选择抄送的流程');
//        }
//        $send_copy_user = json_decode($param['send_copy_user'],true);
//        if (!count($send_copy_user)) {
//            SetReturn(-1,'请选择抄送人');
//        }
//        $db = dbMysql::getInstance();
//        $poject_list = $db->table('goods_project')
//            ->whereIn('id',$ids)
//            ->list();
//
//        $send_copy_user_wid = array_column($send_copy_user,'wid');
//        $result_copy = [];
//        foreach ($poject_list as $v) {
//            if ($v['is_stop']) {
//                $result_copy[] = [
//                    'id'=>$v['id'],
//                    'matter_name'=>$v['matter_name'],
//                    'success'=>0,
//                    'msg'=>'该项目流程已暂停，不可操作',
//                ];
//                continue;
//            }
//            if ($v['status'] == 4) {
//                $result_copy[] = [
//                    'id'=>$v['id'],
//                    'matter_name'=>$v['matter_name'],
//                    'success'=>0,
//                    'msg'=>'该项目流程已废除，不可操作',
//                ];
//                continue;
//            }
//            $remind_msg[] = [
//                'wids'=>$send_copy_user_wid,
//                'msg'=>messagesFrom::getMsgTxt(2,$v['matter_name'],'',''),
//                'other_data'=>[
//                    'user_id'=>userModel::$qwuser_id,
//                    'model_id'=>$v['id'],
//                    'msg_type'=>2
//                ],
//            ];
//            $result_copy[] = [
//                'id'=>$v['id'],
//                'matter_name'=>$v['matter_name'],
//                'success'=>1,
//                'msg'=>'',
//            ];
//        }
//        foreach ($remind_msg as $msg) {
//            messagesFrom::senMeg($msg['wids'],$msg['msg'],$msg['other_data'],$param['remarks']);
//        }
//        returnSuccess($result_copy,'抄送消息已发送');
//    }
    //项目暂停 - 启动
//    public function stopProjectMuch() {
//        $ids = $_POST['ids'];
//        if (!$ids) {
//            SetReturn(-1,'请选择要操作的流程');
//        }
//        $ids = json_decode($ids);
//        if (!count($ids)) {
//            SetReturn(-1,'请选择要操作的流程');
//        }
//        $is_stop= (int)$_POST['is_stop'];
//        $db = dbMysql::getInstance();
//        $db->table('goods_project')
//            ->where('where status <> 4')
//            ->whereIn('id',$ids)
//            ->update(['is_stop'=>$is_stop,'updated_time'=>date('Y-m-d H:i:s')]);
//        SetReturn(0,'操作成功');
//    }

}