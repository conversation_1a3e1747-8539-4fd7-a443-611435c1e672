<?php

namespace plugins\assessment\controller;

use admin\models\qwdepartmentModel;
use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\db\dbAMysql;
use core\lib\db\dbMysql;
use core\lib\db\dbSMysql;
use core\lib\ExceptionError;
use plugins\assessment\form\messagesFrom;
use plugins\assessment\models\assessmentModel;
use plugins\assessment\models\assessmentSchemesModel;
use plugins\assessment\models\assessmentTargetsModel;
use plugins\assessment\models\assessmentUsersModel;
use plugins\assessment\models\commissionRulesModel;
use plugins\assessment\models\levelRulesModel;
use plugins\assessment\models\userModel;
use core\lib\redisCached;
use plugins\salary\models\salaryCalculationModel;

class assessmentManageController
{
    // 项目管理中取出第三级
    private static function getThirdLevel($projects)
    {
        $third_level = [];
        foreach ($projects as $project) {
            if ($project['level'] == 3) {
                $third_level[] = $project;
            }
        }
        return $third_level;
    }

    // 获取所有配置项
    public function getOptionData()
    {
        $config = config::all('data_assessment');

        $db = dbMysql::getInstance();
        $fdb = dbFMysql::getInstance();
        $adb = dbAMysql::getInstance();

        $assessment_targets_source = $config['assessment_targets_source'];
        $assessment_targets_source = array_column($assessment_targets_source, 'list', 'id');
        $columns = $assessment_targets_source['1'];
        $config['columns'] = $columns;

        // 企微部门
        $db->table('qwdepartment');
        $db->field('id,wp_id,name,department_leader,qw_parentid,sort');
        $db->order('qw_parentid asc,`sort` desc');
        $partments = $db->list();
        $departments = [];
        if (count($partments)) {
            $departments = buildTree($partments, $partments[0]['qw_parentid'], 'wp_id', 'qw_parentid');
        }

        $config['departments'] = $departments;

        $all_department_users = qwdepartmentModel::getUserDepartment(userModel::$wid)['all_department_users'] ?: []; // 当前用户管理的部门及其子部门下的用户
        $user_ids = array_column($all_department_users, 'wid');
        $db = dbMysql::getInstance();
        $all_users = $db->table('qwuser', 'a')->field('a.id,a.wid,a.wname,a.avatar')->where('where a.is_delete = 0')->list();
        $all_users = array_column($all_users, null, 'wid');

        // 考核管理用户筛选
        $config['assessment_users'] = [];
        if (userModel::getUserListAuth('assessmentAll')) {
            $config['assessment_users'] = $all_users;
        } elseif (userModel::getUserListAuth('assessmentDepartment')) {
            $config['assessment_users'] = array_intersect_key($all_users, array_flip($user_ids));
        } elseif (userModel::getUserListAuth('assessmentRelated')) {
            $config['assessment_users'] = [['wid' => userModel::$wid, 'id' => userModel::$qwuser_id, 'wname' => userModel::$wname, 'avatar' => userModel::$avatar]];
        }
        $config['assessment_users'] = array_values($config['assessment_users']);

        // 考核档案用户筛选
        $config['assessment_file_users'] = [];
        if (userModel::getUserListAuth('assessmentFileAll')) {
            $config['assessment_file_users'] = $all_users;
        } elseif (userModel::getUserListAuth('assessmentFileDepartment')) {
            $config['assessment_file_users'] = array_intersect_key($all_users, array_flip($user_ids));
        } elseif (userModel::getUserListAuth('assessmentFileRelated')) {
            $config['assessment_file_users'] = [['wid' => userModel::$wid, 'id' => userModel::$qwuser_id, 'wname' => userModel::$wname, 'avatar' => userModel::$avatar]];
        }
        $config['assessment_file_users'] = array_values($config['assessment_file_users']);

        // 等级规则
        $level_rules = $adb->table('level_rules')->field('id,level_rules')->where('where is_delete = 0')->list();
        $level_rules_all = [];
        foreach ($level_rules as $level_rule) {
            $level_rule['level_rules'] = json_decode($level_rule['level_rules'], true);
            foreach ($level_rule['level_rules'] as $level) {
                $level_rules_all[] = $level['level'];
            }
        }
        $config['level_rules'] = array_values(array_unique($level_rules_all));

        $config['currency_list'] = [];

        try {
            // 获取最新一个月的日期
            $latestDate = $fdb->table('routing')
                ->field('MAX(date) as latest_date')
                ->one();

            if (!$latestDate || !isset($latestDate['latest_date'])) {
                returnError('未找到最新日期的币种数据');
            }

            // 使用最新日期查询币种数据
            $list = $fdb->table('routing')
                ->where('where date = :date', ['date' => $latestDate['latest_date']])
                ->field('date, code, icon, name, rate_org, my_rate')
                ->list();

            // 定义指定货币的排序顺序
            $preferredOrder = ['人民币', '美元', '欧元', '英镑', '日元', '加元'];

            // 自定义排序函数
            usort($list, function ($a, $b) use ($preferredOrder) {
                $indexA = array_search($a['name'], $preferredOrder);
                $indexB = array_search($b['name'], $preferredOrder);

                if ($indexA === false && $indexB === false) {
                    // 如果两个货币都不在指定顺序中，按照它们在原始数据中的顺序排序
                    return 0;
                } elseif ($indexA === false) {
                    // 如果 $a 不在指定顺序中，将其排在后面
                    return 1;
                } elseif ($indexB === false) {
                    // 如果 $b 不在指定顺序中，将其排在后面
                    return -1;
                } else {
                    // 如果两个货币都在指定顺序中，按照指定顺序排序
                    return $indexA - $indexB;
                }
            });
            $config['currency_list'] = $list;
        } catch (\Exception $e) {
            returnError('查询失败：' . $e->getMessage());
        }


        returnSuccess($config);
    }

    // 获取考核列表
    public function getAssessmentList()
    {
        $paras_list = array('page', 'page_size', 'assessment_name', 'user_id', 'a_s_id', 'assessment_time', 'apply_users');
        $param = arrangeParam($_POST, $paras_list);
        $param['status'] = $_POST['status'] ?? null;

        // 权限控制
        $user_id = userModel::$qwuser_id;
        $all_department_users = qwdepartmentModel::getUserDepartment(userModel::$wid)['all_department_users'] ?: []; // 当前用户管理的部门及其子部门下的用户
        $user_ids = array_column($all_department_users, 'id');

        $adb = dbAMysql::getInstance();
        $adb->table('assessment');
        $adb->where('where is_delete = 0');
        if (!empty($param['assessment_name'])) {
            $adb->andWhere(' assessment_name like :assessment_name', ['assessment_name' => '%' . $param['assessment_name'] . '%']);
        }
        if (!empty($param['status'])) {
            $status = json_decode($param['status'], true) ?: null;
            $status && $adb->whereIn('status', $status);
        }
        if (!empty($param['user_id'])) {
            $a_user_id = json_decode($param['user_id'], true) ?: null;
            $a_user_id && $adb->whereIn('user_id', $a_user_id);
        }
        if (!empty($param['a_s_id'])) {
            $a_s_id = json_decode($param['a_s_id'], true) ?: null;
            $a_s_id && $adb->whereIn('a_s_id', $a_s_id);
        }
        // 具体考核周期和筛选范围有交集
        if (!empty($param['assessment_time'])) {
            $assessment_cycle = json_decode($param['assessment_time'], true);
            $adb->andWhere(
                'JSON_EXTRACT(assessment_cycle, "$[1]") >= :start_time
                                and JSON_EXTRACT(assessment_cycle, "$[0]") <= :end_time ',
                ['start_time' => $assessment_cycle[0], 'end_time' => $assessment_cycle[1]]
            );
        }

        if (userModel::getUserListAuth('assessmentAll')) {
            // 全部权限
        } elseif (userModel::getUserListAuth('assessmentDepartment')) {
            if (empty($all_department_users)) {
                returnSuccess(['total' => 0, 'page' => $param['page'], 'list' => []]);
            }
            $user_str = implode("','", $user_ids);
            $adb->andWhere("(JSON_OVERLAPS(attach->'$.apply_users', JSON_ARRAY('{$user_str}')) OR JSON_EXTRACT(attach, '$.leader') IN ('{$user_str}') OR JSON_EXTRACT(attach, '$.approver_user') IN ('{$user_str}') or user_id in ('{$user_str}') )");
        } elseif (userModel::getUserListAuth('assessmentRelated')) {
            $adb->andWhere("(JSON_OVERLAPS(attach->'$.apply_users', JSON_ARRAY('{$user_id}')) OR JSON_EXTRACT(attach, '$.leader') IN ('{$user_id}') OR JSON_EXTRACT(attach, '$.approver_user') IN ('{$user_id}') OR user_id in ('{$user_id}') )");
        } else {
            returnError('无权限查看');
        }

        // 筛选考核用户
        if (!empty($param['apply_users'])) {
            $apply_users = json_decode($param['apply_users'], true);
            $apply_users_str = implode("','", $apply_users);
            $adb->andWhere("JSON_OVERLAPS(attach->'$.apply_users', JSON_ARRAY('{$apply_users_str}'))");
        }
        $adb->order('id desc');
        $list = $adb->pages($param['page'], $param['page_size']);
        $user_ids = array_column($list['list'], 'user_id');
        $db = dbMysql::getInstance();
        $users = $db->table('qwuser')->whereIn('id', $user_ids)->field('id, wname')->list();
        $userMap = array_column($users, 'wname', 'id');
        $userMap['0'] = '系统发起';

        // 查询当前列表的考核是否存在阶段为上级且超时的任务
        $is_overtime = [];
        $is_abnormal = [];
        $is_audit = [];
        $aids = array_column($list['list'], 'id');
        if (!empty($aids)) {
            $adb->table('assessment_users', 'au')->field('au.a_id,au.status, au.stage, au.process, au.stage_deadline, au.result');
            $adb->whereIn('au.a_id', $aids);
            $assessment_users = $adb->list();
            foreach ($assessment_users as $assessment_user) {
                $result = json_decode($assessment_user['result'], true);
                $process = json_decode($assessment_user['process'], true);
                $process_calc = $process['4']['performance_calculation_method'] ?? null;
                if ($assessment_user['status'] == 0) {
                    if ($assessment_user['stage'] == 4 && strtotime($assessment_user['stage_deadline']) < time()) {
                        $is_overtime[$assessment_user['a_id']] = 1;
                    }
                    if (isset($result['performance_error']) && empty($result['performance']) && ($process_calc != 4) ) {
                        $is_abnormal[$assessment_user['a_id']] = 1;
                    }
                } elseif ($assessment_user['status'] == 1) {
                    if (isset($result['audit_status']) && $result['audit_status'] == 0) {
                        $is_audit[$assessment_user['a_id']] = 1;
                    }
                }
            }
        }

        $salary_months = array_column($list['list'], 'salary_month');
        $salary_months = array_values(array_filter(array_unique($salary_months)));
        $is_calculated = [];
        if (!empty($salary_months)) {
            $sdb = dbSMysql::getInstance();
            $salary_list = $sdb->table('salary_calculation')
                ->where('where is_delete = 0')
                ->whereIn('status', [
                    salaryCalculationModel::STATUS_WAIT_CALC,
                    salaryCalculationModel::STATUS_CALCULATING,
                    salaryCalculationModel::STATUS_WAIT_CHECK,
                    salaryCalculationModel::STATUS_WAIT_APPROVE,
                    salaryCalculationModel::STATUS_FINISHED
                ])
                ->whereIn('month', $salary_months)
                ->list();

            foreach ($salary_list as $item) {
                $is_calculated[$item['salary_month']] = 1;
            }
        }

        foreach ($list['list'] as &$item) {
            $item['user_name'] = $userMap[$item['user_id']];
            $item['is_overtime'] = $is_overtime[$item['id']] ?? 0;
            $item['is_abnormal'] = $is_abnormal[$item['id']] ?? 0;
            $item['is_audit'] = $is_audit[$item['id']] ?? 0;
            if ($item['salary_month']) {
                $item['is_calculated'] = $is_calculated[$item['salary_month']] ?? 0;
            }
        }

        returnSuccess($list);
    }

    // 获取考核方案详情
    public function getAssessmentDetail()
    {
        $paras_list = array('id');
        $param = arrangeParam($_POST, $paras_list);
        $adb = dbAMysql::getInstance();
        $adb->table('assessment', 'a')->field('a.*');
        $adb->where('where a.id = :id', ['id' => $param['id']]);
        $assessment = $adb->one();
        if (!$assessment) returnError('考核不存在');
        $attach = json_decode($assessment['attach'], true);
        $scheme = $attach['scheme'] ?? null;
        $process = $scheme['assessment_scheme_process'] ?? null;
        $targets = $attach['targets'] ?? null;
        $commissions = $attach['commissions'] ?? null;
        $level_rules = $attach['level_rules'] ?? null;

        $fdb = dbFMysql::getInstance();
        $db = dbMysql::getInstance();
        $department = $db->table('qwdepartment')->field('wp_id, name')->where('where wp_id = :id', ['id' => $scheme['attach']['department']])->one();
        $scheme['attach']['department_name'] = $department['name'];
        $process_calc = $scheme['assessment_scheme_process']['4'];
        $target_map = array_column($targets, 'target_name', 'id');
        $commission_map = array_column($commissions, 'rule_name', 'id');
        // 比率核算 和 阶梯奖励 公式解析
        $scheme['assessment_scheme_process']['4']['formula_text'] = assessmentSchemesModel::getFormulaText($process_calc, $department['name'], $commission_map, $target_map);

        $users = $db->table('qwuser')->field('id,wid,wname,avatar')->whereIn('id', $attach['apply_users'])->list();
        $assessment['apply_users'] = array_values($users);

        // 业绩指标
        $assessment_targets_source = config::get('assessment_targets_source','data_assessment');
        $assessment_targets_source = array_column($assessment_targets_source, 'list', 'id');
        $columns = $assessment_targets_source['1'];
        $columns_map = array_column($columns, 'column_name', 'id');
        if (!empty($targets)) {
            foreach ($targets as &$target) {
                $target['target_detail'] = json_decode($target['target_detail'], true);
                $target['target_text'] = assessmentTargetsModel::getTargetText($target['target_detail'], $target['target_type'], $columns_map);
            }
        }
        if (!empty($commissions)) {
            foreach ($commissions as &$commission) {
                $rule = json_decode($commission['rules'], true);
                $commission['rule_text'] = commissionRulesModel::getRuleText($rule, $columns_map[$commission['column_id']]);
            }
        }
        if (!empty($level_rules)) {
            foreach ($level_rules as &$level_rule) {
                $level_rule['level_rule_text'] = levelRulesModel::getLevelRuleText($level_rule['level_rules']);
            }
        }

        // 权限控制
        $user_id = userModel::$qwuser_id;
        $all_department_users = qwdepartmentModel::getUserDepartment(userModel::$wid)['all_department_users'] ?: []; // 当前用户管理的部门及其子部门下的用户
        $user_ids = array_column($all_department_users, 'id');

        // 按考核状态统计
        $adb->table('assessment_users', 'au')->field('au.status, au.stage, au.result');
        $adb->where('where a_id = :a_id', ['a_id' => $param['id']]);
        if (userModel::getUserListAuth('assessmentAll')) {
            // 全部权限
        } elseif (userModel::getUserListAuth('assessmentDepartment')) {
            if (!empty($all_department_users)) {
                $user_str = implode("','", $user_ids);
                $adb->andWhere("JSON_OVERLAPS(related_users, JSON_ARRAY('{$user_str}'))");
            }
        } elseif (userModel::getUserListAuth('assessmentRelated')) {
            $adb->andWhere("JSON_OVERLAPS(related_users, JSON_ARRAY('{$user_id}'))");
        }
        $user_list = $adb->list();

        $status = ['0', '1', '2', '3'];
        $status_count = [];
        foreach ($status as $item) {
            $status_count[$item] = [
                'count' => 0,
                'status' => $item,
            ];
        }
        $stage_count = [];
        $audit_count = [
            'is_change' => ['type' => 'is_change', 'count' => 0],
        ];

        foreach ($process as $item) {
            if (isset($item['status']) && $item['status'] == 0) continue;
            $stage_count[$item['id']] = [
                'count' => '0',
                'stage' => $item['id'],
            ];
        }

        foreach ($user_list as $item) {
            $item['result'] = json_decode($item['result'], true);
            // 进行中、已暂停的统计阶段
            if (in_array($item['status'], [0, 2])) {
                if (!isset($stage_count[$item['stage']])) {
                    $stage_count[$item['stage']] = [
                        'count' => 0,
                        'stage' => $item['stage'],
                    ];
                }
                $stage_count[$item['stage']]['count'] += 1;
            }
            // 调整已调整的统计阶段
            if (isset($item['result']['is_change']) && $item['result']['is_change'] == 1) {
                $audit_count['is_change']['count'] += 1;
            }
            // 按状态统计
            $status_count[$item['status']]['count'] += 1;

        }
        $audit_count = array_values($audit_count);
        $stage_count = array_values($stage_count);
        $status_count = array_values($status_count);


        returnSuccess([
            'assessment'   => $assessment,
            'scheme'       => $scheme,
            'status_count' => $status_count,
            'stage_count'  => $stage_count,
            'audit_count'  => $audit_count,
            'targets'      => $targets,
            'commissions'  => $commissions,
            'level_rules'  => $level_rules,
        ]);
    }

    // 获取方案用户
    public function getUserAssessmentList()
    {
        $paras_list = array('id', 'page', 'page_size', 'status', 'stage', 'keyword', 'is_change');
        $param = arrangeParam($_POST, $paras_list);
        $adb = dbAMysql::getInstance();
        $adb->table('assessment', 'a')->field('a.*');
        $adb->where('where a.id = :id', ['id' => $param['id']]);
        $assessment = $adb->one();
        $attach = json_decode($assessment['attach'], true);
        $scheme = $attach['scheme'] ?? null;
        $process_calc = $scheme['assessment_scheme_process']['4']['performance_calculation_method'];
        // 权限控制
        $user_id = userModel::$qwuser_id;
        $all_department_users = qwdepartmentModel::getUserDepartment(userModel::$wid)['all_department_users'] ?: []; // 当前用户管理的部门及其子部门下的用户
        $user_ids = array_column($all_department_users, 'id');

        // 需要拉取考核人员列表
        $adb->table('assessment_users', 'au')->field('au.*');
        $adb->where('where a_id = :a_id', ['a_id' => $param['id']])->order('id desc');
        if (userModel::getUserListAuth('assessmentAll')) {
        } elseif (userModel::getUserListAuth('assessmentDepartment')) {
            if (!empty($all_department_users)) {
                $user_str = implode("','", $user_ids);
                $adb->andWhere("JSON_OVERLAPS(related_users, JSON_ARRAY('{$user_str}'))");
            }
        } elseif (userModel::getUserListAuth('assessmentRelated')) {
            $adb->andWhere("JSON_OVERLAPS(related_users, JSON_ARRAY('{$user_id}'))");
        }
        if (isset($param['status']) && in_array($param['status'], [0, 1])) {
            if ($param['status'] == 1) $param['status'] = [1, 2];
            if ($param['status'] == 0) $param['status'] = [0];
            $adb->whereIn('status', $param['status']);
        }
        if (isset($param['stage'])) {
            $param['stage'] = json_decode($param['stage'], true);
            $stage = [];
            if (!empty($param['stage'])) {
                foreach ($param['stage'] as $item) {
                    if (in_array($item, ['1', '2', '3', '4', '5', '6', '7'])) {
                        $stage[] = $item;
                    }
                }
                $adb->WhereIn('stage', $stage);
            }
        }
        // 已调整
        if (isset($param['is_change']) && $param['is_change'] == 1) {
            $adb->andWhere("JSON_UNQUOTE(JSON_EXTRACT(result, '$.is_change')) = :is_change", ['is_change' => $param['is_change']]);
        }
        // 调整待审批
        if (isset($param['audit_status']) && $param['audit_status'] == 0) {
            $adb->andWhere("JSON_UNQUOTE(JSON_EXTRACT(result, '$.audit_status')) = :audit_status", ['audit_status' => $param['audit_status']]);
        }
        if (!empty($param['keyword'])) {
            $adb->andWhere("JSON_UNQUOTE(JSON_EXTRACT(attach, '$.user_info.wname')) LIKE :keyword", ['keyword' => "%{$param['keyword']}%"]);
        }
        $assessment_users = $adb->pages($param['page'], $param['page_size']);

        $user_ids = array_column($assessment_users['list'], 'user_id');
        $db = dbMysql::getInstance();
        $users = $db->table('qwuser')->whereIn('id', $user_ids)->field('id, wname')->list();
        $user_map = array_column($users, 'wname', 'id');

        foreach ($assessment_users['list'] as &$assessment_user) {
            $assessment_user['process'] = json_decode($assessment_user['process'], true);
            $assessment_user['attach'] = json_decode($assessment_user['attach'], true);
            $assessment_user['targets'] = json_decode($assessment_user['targets'], true);
            $assessment_user['user_name'] = $user_map[$assessment_user['user_id']];
            // 只有上级才算超时
            $assessment_user['is_overtime'] = $assessment_user['stage'] == 4 && strtotime($assessment_user['stage_deadline']) < time() ? 1 : 0;
            $result = json_decode($assessment_user['result'], true);
            $assessment_user['result'] = $result;
            $assessment_user['is_abnormal'] = (isset($result['performance_error']) && $process_calc != 4) ? 1 : 0;
        }

        returnSuccess([
            'assessment' => $assessment,
            'users'      => $assessment_users,
        ]);
    }

    // 获取个人考核详情
    public function getUserAssessmentDetail()
    {
        $paras_list = array('id', 'a_id');
        $param = arrangeParam($_POST, $paras_list);
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_users', 'au')->field('au.*');
        $adb->where('where id = :id', ['id' => $param['id']]);
        $assessment_user = $adb->one();
        $assessment_user['targets'] = json_decode($assessment_user['targets'], true) ?: null;
        $assessment_user['process'] = json_decode($assessment_user['process'], true) ?: null;
        $assessment_user['result'] = json_decode($assessment_user['result'], true) ?: null;
        $assessment_user['attach'] = json_decode($assessment_user['attach'], true) ?: null;

        // 获取考核
        $adb->table('assessment', 'a')->field('a.*');
        $adb->where('where a.id = :id', ['id' => $assessment_user['a_id']]);
        $assessment = $adb->one();
        $attach = json_decode($assessment['attach'], true);
        $scheme = $attach['scheme'] ?? null;
        $targets = $attach['targets'] ?? null;

        // 下一个
        $adb->table('assessment_users', 'au')->field('au.id,au.user_id');
        $adb->where('where id < :id and a_id = :a_id', ['id' => $param['id'], 'a_id' => $assessment['id']])->order('id desc');
        $next_assessment = $adb->one() ?: null;
        // 如果没有下一个，取第一个
        if (!$next_assessment) {
            $adb->table('assessment_users', 'au')->field('au.id,au.user_id');
            $adb->where('where a_id = :a_id', ['a_id' => $assessment['id']])->order('id desc');
            $next_assessment = $adb->one();
        }

        // 上一个
        $adb->table('assessment_users', 'au')->field('au.id,au.user_id');
        $adb->where('where id > :id and a_id = :a_id', ['id' => $param['id'], 'a_id' => $assessment['id']])->order('id asc');
        $pre_assessment = $adb->one() ?: null;
        // 如果没有上一个，取最后一个
        if (!$pre_assessment) {
            $adb->table('assessment_users', 'au')->field('au.id,au.user_id');
            $adb->where('where a_id = :a_id', ['a_id' => $assessment['id']])->order('id asc');
            $pre_assessment = $adb->one();
        }

        $user_ids = $attach['apply_users'];
        $db = dbMysql::getInstance();
        $users = $db->table('qwuser')->whereIn('id', $user_ids)->field('id, wname')->list();
        $user_map = array_column($users, 'wname', 'id');
        $assessment_user['user_name'] = $user_map[$assessment_user['user_id']];
        $pre_assessment && $pre_assessment['user_name'] = $user_map[$pre_assessment['user_id']];
        $next_assessment && $next_assessment['user_name'] = $user_map[$next_assessment['user_id']];

        returnSuccess([
            'assessment' => $assessment,
            'scheme'     => $scheme,
            'targets'    => $targets,
            'user'       => $assessment_user,
            'pre_user'   => $pre_assessment,
            'next_user'  => $next_assessment,
        ]);
    }

    // 考核周期任务检查
    public function checkAssessmentCycle()
    {
        $paras_list = array('a_s_id', 'assessment_cycle');
        $param = arrangeParam($_POST, $paras_list);
        $assessment_cycle = json_decode($_POST['assessment_cycle'], true);
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_schemes', 'a')->field('a.*');
        $scheme = $adb->where('where a.id = :a_s_id', ['a_s_id' => $param['a_s_id']])->one();

        // 获取当前考核方案下的所有考核任务
        $adb->table('assessment');
        $assessments = $adb->where('where a_s_id = :a_s_id and is_delete = 0', ['a_s_id' => $param['a_s_id']])->list();
        $return = [
            'code' => 0,
            'msg'  => '可以发起考核',
        ];

        foreach ($assessments as $assessment) {
            $a_time = json_decode($assessment['assessment_cycle'], true);
            if (checkTimeHasIntersect(strtotime($a_time[0]), strtotime($a_time[1]), strtotime($assessment_cycle[0]), strtotime($assessment_cycle[1]))) {
                $return['code'] = -1;
                $return['msg'] = '已有该考核周期的考核任务，是否还需再次发起考核';
            }
        }
        // 可以发起考核
        returnSuccess($return);
    }

    // 发起考核
    public function addAssessment()
    {
        $paras_list = array('assessment_name', 'a_s_id', 'assessment_cycle', 'assessment_desc', 'apply_range');
        $request_list = ['assessment_name' => '考核名称', 'assessment_cycle' => '考核周期'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_schemes');
        $adb->where('where id = :id and is_delete = 0', ['id' => $param['a_s_id']]);
        $scheme = $adb->one();
        if (!$scheme) {
            returnError('考核方案不存在');
        }
        $scheme['attach'] = $scheme['attach'] ? json_decode($scheme['attach'], true) : null;
        $scheme['assessment_template'] = $scheme['assessment_template'] ? json_decode($scheme['assessment_template'], true) : null;
        $scheme['assessment_scheme_process'] = $scheme['assessment_scheme_process'] ? json_decode($scheme['assessment_scheme_process'], true) : null;

        // 补充考核对象
        $apply_range = json_decode($param['apply_range'], true);
        if (empty($apply_range['departments']) && empty($apply_range['users']) && empty($apply_range['roles'])) {
            returnError('请选择考核对象');
        }
        $scheme['attach']['apply_range']['departments'] = $apply_range['departments'] ?: [];
        $scheme['attach']['apply_range']['users'] = $apply_range['users'] ?: [];
        $scheme['attach']['apply_range']['roles'] = $apply_range['roles'] ?: [];

        $data = [
            'scheme'           => $scheme,
            'assessment_name'  => $param['assessment_name'],
            'user_id'          => userModel::$qwuser_id,
            'a_s_id'           => $param['a_s_id'],
            'assessment_cycle' => $param['assessment_cycle'],
            'assessment_desc'  => $param['assessment_desc'],
            'apply_range'      => $param['apply_range'],
            'result_view'      => $param['result_view'] ?? [],
            'status'           => 0, // 增加了事务，直接生成进行中的考核
        ];

        try {
            // 生成考核
            assessmentSchemesModel::createAssessment($data);
        } catch (\Throwable $error) {
            returnError($error->getMessage());
        }
        returnSuccess([], '发起成功');
    }

    // 删除考核
    public function deleteAssessment()
    {
        $paras_list = array('id');
        $param = arrangeParam($_POST, $paras_list);
        $adb = dbAMysql::getInstance();
        $adb->table('assessment');
        $assessment = $adb->where('where id = :id', ['id' => $param['id']])->one();
        if (!$assessment) returnError('考核不存在');
        // 只有已完成、已终止的考核才能删除
        if ($assessment['status'] != 1 && $assessment['status'] != 3) {
            returnError('只有已完成、已终止的考核才能删除');
        }

        $assessment_users = $adb->table('assessment_users')->where('where a_id = :a_id', ['a_id' => $param['id']])->list();
        $month = [];
        foreach ($assessment_users as $assessment_user) {
            if ($assessment_user['step'] == 4 && !empty($assessment_user['salary_month'])) {
                !isset($month[$assessment_user['salary_month']]) && $month[$assessment_user['salary_month']] = [];
                $month[$assessment_user['salary_month']][] = $assessment_user['user_id'];
            }
        }

        if (!empty($month)) {
            foreach ($month as $m => $users) {
                $calculation = salaryCalculationModel::getCalculationUserByMonth($m, $users);
                if ($calculation) {
                    returnError('考核下有员工已算薪，无法删除');
                }
            }
        }

        $data = [
            'is_delete' => 1,
        ];
        $adb->table('assessment')->where('where id = :id', ['id' => $param['id']])->update($data);
        returnSuccess([], '删除成功');
    }

    // 暂停考核（方案）
    public function pauseAssessment()
    {
        $paras_list = array('id');
        $param = arrangeParam($_POST, $paras_list);
        $adb = dbAMysql::getInstance();
        $adb->table('assessment');
        $assessment = $adb->where('where id = :id', ['id' => $param['id']])->one();
        if (!$assessment) returnError('考核不存在');
        if ($assessment['status'] != 0) returnError('只有考核中的考核才能暂停');

        // 查询考核任务是否有进行中的
        $adb->table('assessment_users');
        $users = $adb->where('where a_id = :a_id and status = 0', ['a_id' => $param['id']])->one();
        if (!$users) returnError('当前考核没有可以暂停的考核任务！');

        $adb->beginTransaction();
        try {
            // 将所有进行中的个人考核任务暂停
            $data = [
                'status' => 2,
            ];
            $adb->table('assessment_users')->where('where a_id = :a_id and status = 0', ['a_id' => $param['id']])->update($data);

            // 将考核任务暂停
            $adb->table('assessment')->where('where id = :id', ['id' => $param['id']])->update($data);
            $adb->commit();
            returnSuccess([], '暂停成功');
        } catch (\Throwable $error) {
            $adb->rollBack();
            returnError($error->getMessage());
        }
    }

    // 取消暂停（方案）
    public function cancelPauseAssessment()
    {
        $paras_list = array('id');
        $param = arrangeParam($_POST, $paras_list);
        $adb = dbAMysql::getInstance();
        $adb->table('assessment');
        $assessment = $adb->where('where id = :id', ['id' => $param['id']])->one();
        if (!$assessment) returnError('考核不存在');
        if ($assessment['status'] != 2) returnError('只有暂停中的考核才能取消暂停');

        // 查询考核任务是否有进行中的
        $adb->table('assessment_users');
        $users = $adb->where('where a_id = :a_id and status = 2', ['a_id' => $param['id']])->one();
        if (!$users) returnError('当前考核没有可以取消暂停的考核任务！');

        $adb->beginTransaction();
        try {
            // 将所有已暂停的个人考核任务恢复进行中
            $data = [
                'status' => 0,
            ];
            $adb->table('assessment_users')->where('where a_id = :a_id and status = 2', ['a_id' => $param['id']])->update($data);

            // 将考核任务恢复进行中
            $adb->table('assessment')->where('where id = :id', ['id' => $param['id']])->update($data);
            $adb->commit();
            returnSuccess([], '取消暂停成功');
        } catch (ExceptionError $error) {
            $adb->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }

    // 终止考核（方案）
    public function stopAssessment()
    {
        $paras_list = array('id');
        $param = arrangeParam($_POST, $paras_list);
        $adb = dbAMysql::getInstance();
        $adb->table('assessment');
        $assessment = $adb->where('where id = :id', ['id' => $param['id']])->one();
        if (!$assessment) returnError('考核不存在');
        if ($assessment['status'] != 2 && $assessment['status'] != 0) returnError('只有考核中、已暂停的考核才能终止');

        // 查询考核任务是否有进行中、已暂停的
        $adb->table('assessment_users');
        $users = $adb->where('where a_id = :a_id and (status = 0 or status = 2)', ['a_id' => $param['id']])->list();
        if (!$users) returnError('当前考核没有可以终止的考核任务！');

        $adb->beginTransaction();
        try {
            // 将所有进行中、已暂停的个人考核任务终止
            $data = [
                'status' => 3,
            ];

            $adb->table('assessment_users')->where('where a_id = :a_id and (status = 0 or status = 2)', ['a_id' => $param['id']])->update($data);
            // 将考核任务恢复进行中
            $adb->table('assessment')->where('where id = :id', ['id' => $param['id']])->update($data);
            $adb->commit();
        } catch (\Throwable $error) {
            $adb->rollBack();
            returnError($error->getMessage());
        }

        $db = dbMysql::getInstance();
        $user_ids = array_column($users, 'user_id');
        $attach = json_decode($assessment['attach'], true);
        !empty($attach['leader']) && $user_ids[] = $attach['leader']; // 考核的上级
        !empty($attach['approver_user']) && $user_ids[] = $attach['approver_user']; // 考核的审批人
        $user_ids = array_values(array_unique($user_ids));
        $qw_users = $db->table('qwuser')->whereIn('id', $user_ids)->field('id,wid, wname')->list();
        $userMap = array_column($qw_users, null, 'id');
        $send_users = [];
        foreach ($users as $user) {
            $send_users[] = $userMap[$user['user_id']]['wid'];
        }
        $send_users[] = $userMap[$attach['leader']]['wid'];
        $send_users[] = $userMap[$attach['approver_user']]['wid'];
        $send_users = array_values(array_unique($send_users));

        //  发给被考核人、考核/考核方案的发布人和上级领导
        $remind_msg = "【{$assessment['assessment_name']}】的考核已终止";
        messagesFrom::senMeg($send_users, 2, $remind_msg, $param['id']);
        returnSuccess([$send_users], '终止成功');
    }

    // 暂停考核（个人）
    public function pauseUserAssessment()
    {
        $paras_list = array('ids');
        $param = arrangeParam($_POST, $paras_list);
        $param['ids'] = json_decode($param['ids'], true);
        $ids = array_values(array_unique(array_filter($param['ids'])));

        $adb = dbAMysql::getInstance();
        // 查询用户考核任务
        $adb->table('assessment_users');
        $users = $adb->whereIn('id', $ids)->list();
        if (!$users) returnError('考核用户不存在！');
        $handle_user = []; // 需要处理的用户
        foreach ($users as $user) {
            if ($user['status'] != 0) continue;
            $handle_user[] = $user;
        }

        if (empty($handle_user)) returnError('没有可以暂停的考核任务');
        $a_id = $handle_user[0]['a_id'];

        $adb->table('assessment');
        $assessment = $adb->where('where id = :id', ['id' => $a_id])->one();
        if (!$assessment) returnError('考核不存在');

        // 获取考核方案下所有用户
        $adb->table('assessment_users', 'au')->field('au.status, count(au.id) as count');
        $adb->where('where a_id = :a_id', ['a_id' => $a_id]);
        $adb->groupBy(['au.status']);
        $status = $adb->list();
        $status_map = array_column($status, 'count', 'status');
        // 计算修改后的考核状态
        $status_map[0] -= count($handle_user);
        $status_map[2] += count($handle_user);
        $assessment_status = assessmentModel::getAssessmentStatus($a_id, $status_map);

        $adb->beginTransaction();
        try {
            // 将个人考核任务暂停
            $adb->table('assessment_users')->whereIn('id', array_column($handle_user, 'id'))->update([
                'status' => 2,
            ]);

            if ($assessment_status != $assessment['status']) {
                // 将考核任务（方案）暂停
                $adb->table('assessment')->where('where id = :id', ['id' => $a_id])->update([
                    'status' => $assessment_status,
                ]);
            }
            $adb->commit();
            returnSuccess([], '暂停成功');
        } catch (\Throwable $error) {
            $adb->rollBack();
            returnError($error->getMessage());
        }
    }

    // 取消暂停（个人）
    public function cancelPauseUserAssessment()
    {
        $paras_list = array('ids');
        $param = arrangeParam($_POST, $paras_list);
        $param['ids'] = json_decode($param['ids'], true);
        $ids = array_values(array_unique(array_filter($param['ids'])));

        $adb = dbAMysql::getInstance();
        // 查询用户考核任务
        $adb->table('assessment_users');
        $users = $adb->whereIn('id', $ids)->list();
        if (!$users) returnError('考核用户不存在！');
        $handle_user = []; // 需要处理的用户
        foreach ($users as $user) {
            if ($user['status'] != 2) continue;
            $handle_user[] = $user;
        }

        if (empty($handle_user)) returnError('没有可以取消暂停的考核任务');
        $a_id = $handle_user[0]['a_id'];

        $adb->table('assessment');
        $assessment = $adb->where('where id = :id', ['id' => $a_id])->one();
        if (!$assessment) returnError('考核不存在');
        //        if ($assessment['status'] != 2) returnError('只有已暂停的考核才能取消暂停');

        // 获取考核方案下所有用户
        $adb->table('assessment_users', 'au')->field('au.status, count(au.id) as count');
        $adb->where('where a_id = :a_id', ['a_id' => $a_id]);
        $adb->groupBy(['au.status']);
        $status = $adb->list();
        $status_map = array_column($status, 'count', 'status');
        // 计算修改后的考核状态
        $status_map[2] -= count($handle_user);
        $status_map[0] += count($handle_user);
        $assessment_status = assessmentModel::getAssessmentStatus($a_id, $status_map);

        $adb->beginTransaction();
        try {
            // 将个人考核任务暂停
            $adb->table('assessment_users')->whereIn('id', array_column($handle_user, 'id'))->update([
                'status' => 0,
            ]);

            if ($assessment_status != $assessment['status']) {
                // 将考核任务（方案）暂停
                $adb->table('assessment')->where('where id = :id', ['id' => $a_id])->update([
                    'status' => $assessment_status,
                ]);
            }
            $adb->commit();
            returnSuccess([], '取消暂停成功');
        } catch (\Throwable $error) {
            $adb->rollBack();
            returnError($error->getMessage());
        }
    }

    // 终止考核（个人）
    public function stopUserAssessment()
    {
        $paras_list = array('ids');
        $param = arrangeParam($_POST, $paras_list);
        $param['ids'] = json_decode($param['ids'], true);
        $ids = array_values(array_unique(array_filter($param['ids'])));

        $adb = dbAMysql::getInstance();
        // 查询用户考核任务
        $adb->table('assessment_users');
        $users = $adb->whereIn('id', $ids)->list();
        if (!$users) returnError('考核用户不存在！');
        $handle_user = []; // 需要处理的用户
        $running_count = 0;
        $pause_count = 0;

        foreach ($users as $user) {
            if ($user['status'] != 2 && $user['status'] != 0) continue;
            $handle_user[] = $user;
            if ($user['status'] == 0) $running_count++;
            if ($user['status'] == 2) $pause_count++;
        }

        if (empty($handle_user)) returnError('没有可以终止的考核任务');
        $a_id = $handle_user[0]['a_id'];

        $adb->table('assessment');
        $assessment = $adb->where('where id = :id', ['id' => $a_id])->one();
        if (!$assessment) returnError('考核不存在');
        if ($assessment['status'] != 2 && $assessment['status'] != 0) returnError('只有考核中、已暂停的考核才能终止');

        // 获取考核方案下所有用户
        $adb->table('assessment_users', 'au')->field('au.status, count(au.id) as count');
        $adb->where('where a_id = :a_id', ['a_id' => $a_id]);
        $adb->groupBy(['au.status']);
        $status = $adb->list();
        $status_map = array_column($status, 'count', 'status');
        // 计算修改后的考核状态
        $status_map[0] -= $running_count;
        $status_map[2] -= $pause_count;
        $status_map[3] += count($handle_user);
        $assessment_status = assessmentModel::getAssessmentStatus($a_id, $status_map);

        $adb->beginTransaction();
        try {
            // 将个人考核任务终止
            $adb->table('assessment_users')->whereIn('id', array_column($handle_user, 'id'))->update([
                'status' => 3,
            ]);

            if ($assessment_status != $assessment['status']) {
                // 将考核任务（方案）暂停
                $adb->table('assessment')->where('where id = :id', ['id' => $a_id])->update([
                    'status' => $assessment_status,
                ]);
            }
            $adb->commit();
        } catch (\Throwable $error) {
            $adb->rollBack();
            returnError($error->getMessage());
        }

        $db = dbMysql::getInstance();
        $user_ids = array_column($handle_user, 'user_id');
        $attach = json_decode($assessment['attach'], true);
        !empty($attach['leader']) && $user_ids[] = $attach['leader']; // 考核的上级
        !empty($attach['approver_user']) && $user_ids[] = $attach['approver_user']; // 考核的审批人
        $user_ids = array_values(array_unique($user_ids));
        $users = $db->table('qwuser')->whereIn('id', $user_ids)->field('id,wid, wname')->list();
        $userMap = array_column($users, null, 'id');
        $send_users = [];
        $send_user_names = [];
        foreach ($handle_user as $user) {
            $send_users[] = $userMap[$user['user_id']]['wid'];
            $send_user_names[] = $userMap[$user['user_id']]['wname'];
        }
        $send_leader = [$userMap[$attach['leader']]['wid'], $userMap[$attach['approver_user']]['wid']];
        $send_leader = array_values(array_unique($send_leader));

        //  发给被考核人
        $remind_msg = "【{$assessment['assessment_name']}】中您的考核已终止";
        messagesFrom::senMeg($send_users, 2, $remind_msg, $a_id);

        // 发给考核/考核方案的发布人和上级领导
        $send_user_names = implode('、', $send_user_names);
        $remind_msg = "【{$assessment['assessment_name']}】中{$send_user_names}的考核已终止";
        messagesFrom::senMeg($send_leader, 2, $remind_msg, $a_id);

        returnSuccess([], '终止成功');
    }

    // 构造考核结果
    private static function makeResult($stage, $ori_result, $result, $targets): array
    {
        if ($stage == 2) {
            foreach ($result['targets'] as $target) {
                $cur_target = $targets[$target['id']];
                $cur_target['target_detail'] = json_decode($cur_target['target_detail'], true);
                $is_work_desc = $cur_target['target_detail']['work_desc'];
                $target_type = $cur_target['target_type'];
                // 定性指标 && 需要工作描述
                if ($target_type == 2 && $is_work_desc) {
                    $ori_result['targets'][$target['id']]['work_desc'] = $target['work_desc'];
                }
            }
        }
        // 自评保存
        if ($stage == 3) {
            foreach ($result['targets'] as $target) {
                $ori_result['targets'][$target['id']]['self_score'] = $target['self_score'];
            }
        }
        // 上级评分保存
        if ($stage == 4) {
            foreach ($result['targets'] as $target) {
                $cur_target = $targets[$target['id']];
                $cur_target['target_detail'] = json_decode($cur_target['target_detail'], true);
                $target_type = $cur_target['target_type'];
                $ori_result['targets'][$target['id']]['leader_score'] = $target['leader_score'];
                $ori_result['targets'][$target['id']]['score_desc'] = $target['score_desc'];
                // 定量指标
                if ($target_type == 1 && isset($ori_result['targets'][$target['id']]['standard_value_error']) && $ori_result['targets'][$target['id']]['standard_value_error']) {
                    $ori_result['targets'][$target['id']]['standard_value'] = $target['standard_value'];
                }
                if ($target_type == 1 && isset($ori_result['targets'][$target['id']]['real_value_error']) && $ori_result['targets'][$target['id']]['real_value_error']) {
                    $ori_result['targets'][$target['id']]['real_value'] = $target['real_value'];
                }
                // 定量-阶梯-指标实际值
                if (!empty($ori_result['targets'][$target['id']]['column_real_value'])) {
                    $ori_column_real_value = $ori_result['targets'][$target['id']]['column_real_value'];
                    $ori_column_real_value = array_column($ori_column_real_value, null, 'column_id');
                    foreach ($target['column_real_value'] as $column_real_value) {
                        $ori_column_real_value[$column_real_value['column_id']]['real_value'] = $column_real_value['real_value'];
                    }
                    $ori_result['targets'][$target['id']]['column_real_value'] = array_values($ori_column_real_value);
                }
            }
        }
        // 核算修改绩效
        if ($stage == 5) {
            //            foreach ($result['targets'] as $target) {
            //                if ($ori_result['targets'][$target['id']]['result'] == 'error') {
            //                    $ori_result['targets'][$target['id']]['result'] = $target['result'];
            //                }
            //            }
            //            if ($ori_result['target_coe'] == 'error') {
            //                $ori_result['target_coe'] = $result['target_coe'];
            //            }
            $ori_result['performance'] = $result['performance'];
        }
        $ori_result['targets'] = array_values($ori_result['targets']);
        return $ori_result;
    }

    // 保存，仅保存结果，不进行流转
    public function save()
    {
        $paras_list = array('id', 'result');
        $param = arrangeParam($_POST, $paras_list);
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_users');
        $assessment_user = $adb->where('where id = :id', ['id' => $param['id']])->one();
        if (!$assessment_user) returnError('考核用户不存在！');
        if ($assessment_user['status'] != 0) returnError('只有进行中的考核任务才能保存');
        if (!in_array($assessment_user['stage'], [2, 3, 4, 5, 7])) returnError('当前考核节点无法保存');
        if ($assessment_user['stage_user'] != userModel::$qwuser_id) returnError('只有当前节点的人才能保存');
        $assessment = $adb->table('assessment')->where('where id = :id', ['id' => $assessment_user['a_id']])->one();
        $attach = json_decode($assessment['attach'], true);
        $targets_map = $attach['targets'] ? array_column($attach['targets'], null, 'id') : null;
        $ori_result = json_decode($assessment_user['result'], true);
        $ori_result['targets'] = array_column($ori_result['targets'], null, 'id');
        // 本次提交数据
        $result = json_decode($param['result'], true);

        $data = self::makeResult($assessment_user['stage'], $ori_result, $result, $targets_map);
        $adb->table('assessment_users');
        $adb->where('where id = :id', ['id' => $param['id']])->update([
            'result' => json_encode($data, JSON_UNESCAPED_UNICODE),
        ]);

        returnSuccess([], '保存成功');
    }

    // 提交，保存结果并进行流转
    public function submit()
    {
        $paras_list = array('id', 'result');
        $param = arrangeParam($_POST, $paras_list);
        $adb = dbAMysql::getInstance();
        $db = dbMysql::getInstance();
        $adb->table('assessment_users');
        $assessment_user = $adb->where('where id = :id', ['id' => $param['id']])->one();
        if (!$assessment_user) returnError('考核用户不存在！');
        $assessment = $adb->table('assessment')->where('where id = :id', ['id' => $assessment_user['a_id']])->one();
        $assessment['attach'] = json_decode($assessment['attach'], true);
        $targets_map = $assessment['attach']['targets'] ? array_column($assessment['attach']['targets'], null, 'id') : null;

        if ($assessment_user['status'] != 0) returnError('只有进行中的考核任务才能提交');
        if (!in_array($assessment_user['stage'], [2, 3, 4, 5, 7])) returnError('当前考核节点无法提交');
        $assessment_scheme_process = config::get('assessment_scheme_process', 'data_assessment');
        $stage_map = array_column($assessment_scheme_process, 'name', 'id');

        $db = dbMysql::getInstance();
        $users = $db->table('qwuser')->where('where id = :id', ['id' => $assessment_user['stage_user']])->field('id, wname')->list();
        $userMap = array_column($users, 'wname', 'id');
        $userMap['0'] = '系统';
        if ($assessment_user['stage_user'] != userModel::$qwuser_id) returnError('需要' . $userMap[$assessment_user['stage_user']] . '提交【' . $stage_map[$assessment_user['stage']] . '】');
        // 生成下一个节点
        $process = $assessment_user['process'] ? json_decode($assessment_user['process'], true) : [];
        $next_node = assessmentUsersModel::getNextNode($process, $assessment_user['user_id'], $assessment_user['stage']);

        $update_data = [];
        // 绩效确认、自评、上级评分、核算，需要进行结果校验
        if (in_array($assessment_user['stage'], [2, 3, 4, 5])) {
            empty($param['result']) && returnError('提交数据不能为空');
            $ori_result = json_decode($assessment_user['result'], true);
            $ori_result['targets'] = array_column($ori_result['targets'], null, 'id');
            // 本次提交数据
            $result = json_decode($param['result'], true);

            // 校验数据完整性
            foreach ($result['targets'] as $target) {
                if (!$target) returnError('考核指标不存在');
                if (!$target['id']) returnError('考核指标不存在');
                // 当前指标
                $cur_target = $targets_map[$target['id']];
                $target_type = $cur_target['target_type'];
                $cur_target['target_detail'] = json_decode($cur_target['target_detail'], true);
                $is_work_desc = $cur_target['target_detail']['work_desc'];
                $target_method = $cur_target['target_detail']['target_method']; // 公式

                if ($assessment_user['stage'] == 2) {
                    // 定性指标 && 需要工作描述
                    if ($cur_target == 2 && $is_work_desc && !isset($target['work_desc'])) returnError('工作描述不能为空');
                }
                if ($assessment_user['stage'] == 3) {
                    if (!isset($target['self_score'])) returnError('自评分数不能为空');
                }
                if ($assessment_user['stage'] == 4) {
                    if (!isset($target['leader_score'])) returnError('上级评分不能为空');
                    //                    if (!isset($target['score_desc'])) returnError('评分描述不能为空');
                    // 定量指标-公式-如果没有考核指标的标准值和实际值，需要上级填写
                    if ($target_type == 1 && $target_method == 1 && !isset($ori_result['targets'][$target['id']]['standard_value']) && !isset($target['standard_value'])) returnError('指标考核值不能为空');
                    if ($target_type == 1 && $target_method == 1 && !isset($ori_result['targets'][$target['id']]['real_value']) && !isset($target['real_value'])) returnError('指标实际值不能为空');

                    // 定量-阶梯-指标实际值
                    if ($target_type == 1 && $target_method == 2) {
                        foreach ($target['column_real_value'] as $column_real_value) {
                            $ori_column_real_value = $ori_result['targets'][$target['id']]['column_real_value'];
                            $ori_column_real_value = array_column($ori_column_real_value, null, 'column_id');
                            if (!isset($ori_column_real_value[$column_real_value['column_id']]['real_value']) && !isset($column_real_value['real_value'])) returnError('指标实际值不能为空');
                        }
                    }
                }
                if ($assessment_user['stage'] == 5) {
                    if (!isset($ori_result['targets'][$target['id']]['result'])) returnError('核算结果不能为空');
                }
            }
            // 绩效核算节点，需要填写绩效考核结果、绩效
            if ($assessment_user['stage'] == 5) {
                if (!isset($result['performance'])) returnError('绩效不能为空');
                // 清空error
                if (isset($ori_result['performance_error'])) {
                    unset($ori_result['performance_error']);
                }
            }

            $result = self::makeResult($assessment_user['stage'], $ori_result, $result, $targets_map);
            $update_data = [
                'result'         => json_encode($result, JSON_UNESCAPED_UNICODE),
            ];

            // 核算节点，需要记录日志
            if ($assessment_user['stage'] == 5) {
                $adb->table('assessment_users_log');
                $adb->insert([
                    'user_id' => userModel::$qwuser_id,
                    'a_id'    => $assessment_user['a_id'],
                    'au_id'   => $assessment_user['id'],
                    'changes' => json_encode([
                        'type'   => 5,
                        'before' => json_decode($assessment_user['result'], true),
                        'after'  => $result
                    ], JSON_UNESCAPED_UNICODE)
                ]);
            }
        }

        assessmentUsersModel::createNextNode($next_node, $assessment_user, $assessment, $process, $update_data);

        returnSuccess([], '提交成功');
    }

    // 批量提交
    public function submitBatch()
    {
        $paras_list = array('data');
        $param = arrangeParam($_POST, $paras_list);
        $adb = dbAMysql::getInstance();
        $db = dbMysql::getInstance();

        $data = json_decode($param['data'], true);
        empty($data) && returnError('提交数据不能为空');
        $ids = array_column($data, 'id');
        empty($ids) && returnError('提交数据不能为空');

        $assessment_scheme_process = config::get('assessment_scheme_process', 'data_assessment');
        $stage_map = array_column($assessment_scheme_process, 'name', 'id');

        $users = redisCached::getUserInfo(1);
        $users = array_column($users, null, 'user_id');
        $userMap = array_column($users, 'user_name', 'user_id');
        $userMap['0'] = '系统';

        $adb->table('assessment_users');
        $assessment_users = $adb->whereIn('id', $ids)->list();
        $assessment_users = array_column($assessment_users, null, 'id');
        if (!$assessment_users) returnError('考核用户不存在！');

        $a_ids = array_column($assessment_users, 'a_id');
        $assessments = $adb->table('assessment')->whereIn('id', $a_ids)->list();
        $assessments = array_column($assessments, null, 'id');

        foreach ($data as $item) {
            if (!$item['id']) returnError('考核用户不存在');
            if (empty($item['result'])) returnError('提交数据不能为空');
            $assessment_user = $assessment_users[$item['id']];
            $assessment = $assessments[$assessment_user['a_id']];
            $assessment['attach'] = json_decode($assessment['attach'], true);
            $targets_map = $assessment['attach']['targets'] ? array_column($assessment['attach']['targets'], null, 'id') : null;

            if ($assessment_user['status'] != 0) returnError('只有进行中的考核任务才能提交');
            if (!in_array($assessment_user['stage'], [4, 5])) returnError('当前考核节点无法批量提交');
            if ($assessment_user['stage_user'] != userModel::$qwuser_id) returnError('需要' . $userMap[$assessment_user['stage_user']] . '提交【' . $stage_map[$assessment_user['stage']] . '】');

            $ori_result = json_decode($assessment_user['result'], true);
            $ori_result['targets'] = array_column($ori_result['targets'], null, 'id');
            // 本次提交数据
            $result = $item['result'];

            // 校验数据完整性
            foreach ($result['targets'] as $target) {
                if (!$target) returnError('考核指标不存在');
                if (!$target['id']) returnError('考核指标不存在');
                // 当前指标
                $cur_target = $targets_map[$target['id']];
                $target_type = $cur_target['target_type'];
                $cur_target['target_detail'] = json_decode($cur_target['target_detail'], true);
                $is_work_desc = $cur_target['target_detail']['work_desc'];
                $target_method = $cur_target['target_detail']['target_method']; // 公式

                if ($assessment_user['stage'] == 4) {
                    if (!isset($target['leader_score'])) returnError('上级评分不能为空');
                    //                    if (!isset($target['score_desc'])) returnError('评分描述不能为空');
                    // 定量指标-公式-如果没有考核指标的标准值和实际值，需要上级填写
                    if ($target_type == 1 && $target_method == 1 && !isset($ori_result['targets'][$target['id']]['standard_value']) && !isset($target['standard_value'])) returnError('指标考核值不能为空');
                    if ($target_type == 1 && $target_method == 1 && !isset($ori_result['targets'][$target['id']]['real_value']) && !isset($target['real_value'])) returnError('指标实际值不能为空');

                    // 定量-阶梯-指标实际值
                    if ($target_type == 1 && $target_method == 2) {
                        foreach ($target['column_real_value'] as $column_real_value) {
                            $ori_column_real_value = $ori_result['targets'][$target['id']]['column_real_value'];
                            $ori_column_real_value = array_column($ori_column_real_value, null, 'column_id');
                            if (!isset($ori_column_real_value[$column_real_value['column_id']]['real_value']) && !isset($column_real_value['real_value'])) returnError('指标实际值不能为空');
                        }
                    }
                }
                if ($assessment_user['stage'] == 5) {
                    if (!isset($ori_result['targets'][$target['id']]['result'])) returnError('核算结果不能为空');
                }
            }
            // 绩效核算节点，需要填写绩效考核结果、绩效
            if ($assessment_user['stage'] == 5) {
                if (!isset($result['performance'])) returnError('绩效不能为空');
            }
        }

        $update_data = [];
        $log_data = [];
        $handle_data = [];

        foreach ($data as $item) {
            $assessment_user = $assessment_users[$item['id']];
            $assessment = $assessments[$assessment_user['a_id']];
            $assessment['attach'] = json_decode($assessment['attach'], true);
            $targets_map = $assessment['attach']['targets'] ? array_column($assessment['attach']['targets'], null, 'id') : null;

            // 生成下一个节点
            $process = $assessment_user['process'] ? json_decode($assessment_user['process'], true) : [];
            $next_node = assessmentUsersModel::getNextNode($process, $assessment_user['user_id'], $assessment_user['stage']);

            $ori_result = json_decode($assessment_user['result'], true);
            $ori_result['targets'] = array_column($ori_result['targets'], null, 'id');
            // 本次提交数据
            $result = $item['result'];
            // 清空error
            if (isset($ori_result['performance_error'])) {
                unset($ori_result['performance_error']);
            }

            $result = self::makeResult($assessment_user['stage'], $ori_result, $result, $targets_map);
            $update_item = [
                'id'     => $assessment_user['id'],
                'result' => json_encode($result, JSON_UNESCAPED_UNICODE),
            ];
            $update_data[] = $update_item;

            // 核算节点，需要记录日志
            if ($assessment_user['stage'] == 5) {
                $adb->table('assessment_users_log');
                $log_data[] = [
                    'user_id' => userModel::$qwuser_id,
                    'a_id'    => $assessment_user['a_id'],
                    'au_id'   => $assessment_user['id'],
                    'changes' => json_encode([
                        'type'   => 5,
                        'before' => json_decode($assessment_user['result'], true),
                        'after'  => $result
                    ], JSON_UNESCAPED_UNICODE)
                ];
            }

            $handle_data[] = [$next_node, $assessment_user, $assessment, $process, $update_item];

        }

        if (!empty($update_data)) {
            $adb->table('assessment_users');
            $adb->updateBatch($update_data, 'id');
        }
        if (count($log_data)) {
            $keys = array_keys($log_data[0]);
            $list = array_map(function ($item) {
                $item = array_values($item);
                return $item;
            }, $log_data);
            $chunkedArray = array_chunk($list, 500);
            foreach ($chunkedArray as $v) {
                $adb->table('assessment_users_log')->insertBatch($keys, $v);
            }
        }
        if (!empty($handle_data)) {
            foreach ($handle_data as $item) {
                assessmentUsersModel::createNextNode(...$item);
            }
        }

        returnSuccess([], '批量提交成功');
    }


    // 审批
    public function audit()
    {
        $paras_list = array('ids', 'audit_status', 'audit_desc');
        $param = arrangeParam($_POST, $paras_list);
        if (!in_array($param['audit_status'], [1, 0])) returnError('审批状态错误');
        if ($param['audit_status'] == 0 && empty($param['audit_desc'])) returnError('审批不通过时，审批意见不能为空');

        $ids = json_decode($param['ids'], true);
        if (empty($ids)) returnError('考核用户不存在！');

        $adb = dbAMysql::getInstance();
        $adb->table('assessment_users');
        $assessment_users = $adb->whereIn('id', $ids)->list();
        $a_id = array_column($assessment_users, 'a_id');
        $stage_user = array_column($assessment_users, 'stage_user');
        $assessment_users = array_column($assessment_users, null, 'id');
        if (empty($assessment_users)) returnError('考核用户不存在！');

        $assessment_map = $adb->table('assessment')->whereIn('id', $a_id)->list();
        if (empty($assessment_map)) returnError('考核任务不存在！');
        $assessment_map = array_column($assessment_map, null, 'id');

        $db = dbMysql::getInstance();
        $users = $db->table('qwuser')->whereIn('id', $stage_user)->field('id, wname')->list();
        $userMap = array_column($users, 'wname', 'id');
        $assessment_scheme_process = config::get('assessment_scheme_process', 'data_assessment');
        $stage_map = array_column($assessment_scheme_process, 'name', 'id');

        // 需要审批的考核用户
        $need_audit_users = [];
        foreach ($assessment_users as $assessment_user) {
            if ($assessment_user['status'] != 0) returnError('只有进行中的考核任务才能审批');
            if ($assessment_user['stage'] != 6) returnError('当前考核节点无法审批');
            if ($assessment_user['stage_user'] != userModel::$qwuser_id) returnError('需要' . $userMap[$assessment_user['stage_user']] . '提交【' . $stage_map[$assessment_user['stage']] . '】');
            $need_audit_users[] = $assessment_user;
        }

        if (empty($need_audit_users)) returnSuccess([], '没有需要审批的考核用户');

        foreach ($need_audit_users as $assessment_user) {
            $assessment = $assessment_map[$assessment_user['a_id']];
            $assessment['attach'] = json_decode($assessment['attach'], true);
            $process = $assessment_user['process'] ? json_decode($assessment_user['process'], true) : [];
            !empty($param['audit_desc']) && $process[$assessment_user['stage'] - 1]['audit_desc'] = $param['audit_desc'];
            $current_node = assessmentUsersModel::getNode($process, $assessment_user['user_id'], $assessment_user['stage']);
            // 审批不通过时，退回至上级评分节点
            if ($param['audit_status'] == 0) {
                $back_node = assessmentUsersModel::getNode($process, $assessment_user['user_id'], 4);

                // 记录日志
                $adb->table('assessment_users_log');
                $adb->insert([
                    'user_id' => userModel::$qwuser_id,
                    'a_id'    => $assessment_user['a_id'],
                    'au_id'   => $assessment_user['id'],
                    'changes' => json_encode([
                        'type'       => 3, // 退回
                        'from_stage' => $assessment_user['stage'],
                        'to_stage'   => 4,
                        'desc'       => $param['audit_desc'],
                    ], JSON_UNESCAPED_UNICODE)
                ]);

                $result = json_decode($assessment_user['result'], true);
                foreach ($result['targets'] as $key => $target) {
                    unset($result['targets'][$key]['result']); // 清空结果
                    unset($result['targets'][$key]['weight_result']); // 清空加权结果
                    if (isset($result['targets'][$key]['result_error'])) unset($result['targets'][$key]['result_error']); // 清空结果
                }
                unset($result['target_coe']); // 清空绩效结果
                if (isset($result['target_coe_error'])) unset($result['target_coe_error']); // 清空绩效结果
                unset($result['performance']); // 清空绩效
                if (isset($result['performance_error'])) unset($result['performance_error']); // 清空绩效

                $adb->table('assessment_users');
                $adb->where('where id = :id', ['id' => $assessment_user['id']])->update([
                    'result'         => json_encode($result, JSON_UNESCAPED_UNICODE),
                    'process'        => json_encode($process, JSON_UNESCAPED_UNICODE),
                    'stage'          => $back_node['id'],
                    'stage_user'     => $back_node['stage_user'],
                    'stage_deadline' => $back_node['stage_deadline'],
                ]);

                if ($back_node['stage_deadline']) {
                    // 上级评分节点
                    $crontab_data = [
                        'is_crontab_task' => 0, // 非周期任务
                        'link_id'         => $assessment_user['id'],
                        'link_type'       => 3,
                        'runtime'         => date('Y-m-d H:i:s', strtotime('-2 hours', strtotime($back_node['stage_deadline']))),
                    ];
                }
                $adb->table('custom_crontab');
                $adb->insert($crontab_data);

                //  发给当前节点处理人
                $db = dbMysql::getInstance();
                $user_ids = [$back_node['stage_user'], $assessment_user['user_id']];
                $users = $db->table('qwuser')->whereIn('id', $user_ids)->field('id, wid, wname')->list();
                $user_map = array_column($users, null, 'id');
                $remind_msg = "【{$assessment['assessment_name']}】中{$user_map[$assessment_user['user_id']]['wname']}绩效审批未通过，请您重新提交【上级评分】。请注意当前任务的时效";
                messagesFrom::senMeg([$user_map[$back_node['stage_user']]['wid']], 1, $remind_msg, $assessment['id'], $param['audit_desc'], '绩效考核_审批');
                returnSuccess([], '已退回上级评分节点');
            } else {
                // 生成下一个节点
                $next_node = assessmentUsersModel::getNextNode($process, $assessment_user['user_id'], $assessment_user['stage']);
                assessmentUsersModel::createNextNode($next_node, $assessment_user, $assessment, $process);
            }
        }
        returnSuccess([], '审批成功');
    }

    public function stage_back()
    {
        $paras_list = array('id', 'stage', 'back_desc');
        $param = arrangeParam($_POST, $paras_list);
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_users');
        $assessment_user = $adb->where('where id = :id', ['id' => $param['id']])->one();
        if (!$assessment_user) returnError('考核用户不存在！');
        if ($assessment_user['status'] != 0) returnError('只有进行中的考核任务才能退回');
        if (!in_array($param['stage'], [2, 3, 4])) returnError('退回节点错误');
        if ($assessment_user['stage'] == 5 && $assessment_user['stage_user'] == 0) returnError('系统核算中，请勿操作');
        $param['stage'] >= $assessment_user['stage'] && returnError('只能退回至当前节点之前的节点');
        $adb->table('assessment');
        $assessment = $adb->where('where id = :id', ['id' => $assessment_user['a_id']])->one();

        $process = $assessment_user['process'] ? json_decode($assessment_user['process'], true) : [];
        $back_node = assessmentUsersModel::getNode($process, $assessment_user['user_id'], $param['stage']);

        $update_data = [
            'process'        => json_encode($process, JSON_UNESCAPED_UNICODE),
            'stage'          => $back_node['id'],
            'stage_user'     => $back_node['stage_user'],
            'stage_deadline' => $back_node['stage_deadline'],
        ];

        // 退回到绩效核算之前的节点，清空绩效结果
        if ($assessment_user['stage'] >= 5) {
            $result = json_decode($assessment_user['result'], true);
            foreach ($result['targets'] as $key => $target) {
                unset($result['targets'][$key]['result']); // 清空结果
                unset($result['targets'][$key]['weight_result']); // 清空加权结果
                if (isset($result['targets'][$key]['result_error'])) unset($result['targets'][$key]['result_error']); // 清空结果
            }
            unset($result['target_coe']); // 清空绩效结果
            if (isset($result['target_coe_error'])) unset($result['target_coe_error']); // 清空绩效结果
            unset($result['performance']); // 清空绩效
            if (isset($result['performance_error'])) unset($result['performance_error']); // 清空绩效
            $update_data['result'] = json_encode($result, JSON_UNESCAPED_UNICODE);
        }

        $adb->table('assessment_users');
        $adb->where('where id = :id', ['id' => $param['id']])->update($update_data);

        // 记录日志
        $adb->table('assessment_users_log');
        $adb->insert([
            'user_id' => userModel::$qwuser_id,
            'a_id'    => $assessment_user['a_id'],
            'au_id'   => $assessment_user['id'],
            'changes' => json_encode([
                'type'       => 3, // 退回
                'from_stage' => $assessment_user['stage'],
                'to_stage'   => $param['stage'],
                'desc'       => $param['back_desc'],
            ], JSON_UNESCAPED_UNICODE)
        ]);
        //  发给当前节点处理人
        $db = dbMysql::getInstance();
        $to_user = $db->table('qwuser')->where('id=:id', ['id' => $back_node['stage_user']])->field('id, wid')->one();
        $remind_msg = "【{$assessment['assessment_name']}】考核已退回至【{$back_node['stage_name']}】，请及时处理";
        messagesFrom::senMeg([$to_user['wid']], 3, $remind_msg, $assessment['id'], $param['back_desc']);

        if ($back_node['stage_deadline']) {
            // 工作简述/自评/上级评分, 超时通知
            $crontab_data = [
                'is_crontab_task' => 0, // 非周期任务
                'link_id'         => $param['id'],
                'link_type'       => 3,
                'runtime'         => date('Y-m-d H:i:s', strtotime('-2 hours', strtotime($back_node['stage_deadline']))),
            ];
            $adb->table('custom_crontab');
            $adb->insert($crontab_data);
        }

        returnSuccess([], '退回成功');
    }

    public function urge()
    {
        $paras_list = array('id', 'stage');
        $param = arrangeParam($_POST, $paras_list);
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_users');
        $assessment_user = $adb->where('where id = :id', ['id' => $param['id']])->one();
        if (!$assessment_user) returnError('考核用户不存在！');
        if ($assessment_user['status'] != 0) returnError('只有进行中的考核任务才能催一下');
        if ($assessment_user['stage'] != $param['stage'] || !in_array($param['stage'], [2, 3, 4, 5, 6, 7]) || $assessment_user['stage_user'] == 0) returnError('当前考核节点无法催一下');
        $process = json_decode($assessment_user['process'], true);
        foreach ($process as $item) {
            if ($item['id'] == $param['stage']) {
                // 6小时才能再次催一下
                $urge_time = $item['urge_time'] ? strtotime($item['urge_time']) : 0;
                if ($urge_time && time() - $urge_time < 6 * 3600) {
                    returnError('6小时内只能催一次');
                }
            }
        }

        $assessment = $adb->table('assessment')->where('where id = :id', ['id' => $assessment_user['a_id']])->one();

        $assessment_scheme_process = config::get('assessment_scheme_process', 'data_assessment');
        $stage_map = array_column($assessment_scheme_process, 'name', 'id');
        $stage_name = $stage_map[$param['stage']];
        $send_user_name = userModel::$wname;
        $remind_msg = "{$send_user_name}提醒您及时处理【{$assessment['assessment_name']}】的【{$stage_name}】";

        // 记录催一下的时间
        foreach ($process as &$item) {
            if ($item['id'] == $param['stage']) {
                $item['urge_time'] = date('Y-m-d H:i:s');
            }
        }
        $data = [
            'process' => json_encode($process),
        ];
        $adb->table('assessment_users')->where('where id = :id', ['id' => $assessment_user['id']])->update($data);

        //  发给当前节点的人
        $db = dbMysql::getInstance();
        $to_user = $db->table('qwuser')->where('id=:id', ['id' => $assessment_user['stage_user']])->field('id, wid')->one();
        messagesFrom::senMeg([$to_user['wid']], 4, $remind_msg, $assessment['id']);
        returnSuccess([], '催一下成功');
    }

    // 跳过
    public function skip()
    {
        $paras_list = array('id');
        $param = arrangeParam($_POST, $paras_list);
        $adb = dbAMysql::getInstance();
        $db = dbMysql::getInstance();

        $adb->table('assessment_users');
        $assessment_user = $adb->where('where id = :id', ['id' => $param['id']])->one();
        if (!$assessment_user) returnError('考核用户不存在！');
        if ($assessment_user['status'] != 0) returnError('只有进行中的考核任务才能跳过');
        if (!in_array($assessment_user['stage'], [2, 3, 7])) returnError('当前考核节点无法跳过');

        $assessment = $adb->table('assessment')->where('where id = :id', ['id' => $assessment_user['a_id']])->one();
        $assessment['attach'] = json_decode($assessment['attach'], true);
        $process = json_decode($assessment_user['process'], true);

        $next_node = assessmentUsersModel::getNextNode($process, $assessment_user['user_id'], $assessment_user['stage']);
        $current_node = assessmentUsersModel::getNode($process, $assessment_user['user_id'], $assessment_user['stage']);

        // 自评节点需要计算自评得分
        $update_data = [];
        if ($assessment_user['stage'] == 3) {
            $result = assessmentUsersModel::calcDefaultSelfScore($assessment_user, $assessment, $process);
            $update_data['result'] = json_encode($result, JSON_UNESCAPED_UNICODE);
        }

        assessmentUsersModel::createNextNode($next_node, $assessment_user, $assessment, $process, $update_data);

        $remind_msg = "【{$assessment['assessment_name']}】的【{$current_node['stage_name']}】因超时未处理，已自动提交";
        $to_users = $db->table('qwuser')->whereIn('id', [$assessment_user['stage_user']])->field('wid')->list();
        //  发给当前节点的人
        messagesFrom::senMeg(array_column($to_users, 'wid'), 1, $remind_msg, $assessment['id']);

        returnSuccess([], '跳过成功');
    }

    // 调整
    public function changeAssessment()
    {
        $paras_list = array('data');
        $param = arrangeParam($_POST, $paras_list);
        $param['data'] = json_decode($param['data'], true);

        $adb = dbAMysql::getInstance();
        // 查询用户考核任务
        $adb->table('assessment_users');
        $assessment_users = $adb->whereIn('id', array_column($param['data'], 'id'))->list();
        $assessment_users = array_column($assessment_users, null, 'id');
        if (!$assessment_users) returnError('考核用户不存在！');

        $a_id = array_column($assessment_users, 'a_id');
        $aid = array_values(array_unique($a_id));
        if (count($aid) > 1) returnError('只能审批同一考核任务的考核用户');
        $assessment = $adb->table('assessment')->where('where id = :id', ['id' => $aid[0]])->one();
        if (!$assessment) returnError('考核任务不存在！');

        $update_data = [];
        $log_data = [];
        foreach ($param['data'] as $item) {
            $assessment_user = $assessment_users[$item['id']];
            if ($assessment_user['status'] != 1) continue; // 仅考核完成的可变更
            if (!in_array($item['step'], [0,3])) continue; // 仅待提审、审批不通过的可变更
            $assessment_user['result'] = json_decode($assessment_user['result'], true);
            $result = $assessment_user['result'];
            $result['final_performance'] = $item['performance'];
            $result['is_change'] = 1;
            $result['change_desc'] = $item['desc'];
            $update_data[] = [
                'id' => $assessment_user['id'],
                'result' => json_encode($result, JSON_UNESCAPED_UNICODE),
            ];
            // 记录日志
            $log_data[] = [
                'user_id' => userModel::$qwuser_id,
                'a_id'    => $assessment_user['a_id'],
                'au_id'   => $assessment_user['id'],
                'changes' => json_encode([
                    'type'   => 1,
                    'before' => $assessment_user['result'],
                    'after'  => $result,
                    'change_desc' => $item['desc'],
                    'desc'   => '调整为【'. $item['performance'].'】, 调整原因：'.$item['desc']
                ], JSON_UNESCAPED_UNICODE)
            ];
        }

        if (empty($update_data)) returnError('没有需要调整的数据');
        $adb->table('assessment_users')->updateBatch($update_data);
        if (count($log_data)) {
            $keys = array_keys($log_data[0]);
            $list = array_map(function ($item) {
                $item = array_values($item);
                return $item;
            }, $log_data);
            $chunkedArray = array_chunk($list, 500);
            foreach ($chunkedArray as $v) {
                $adb->table('assessment_users_log')->insertBatch($keys, $v);
            }
        }

        returnSuccess([], '调整成功');
    }

    // 重新核算
    public function reCalc() {
        $paras_list = array('id');
        $param = arrangeParam($_POST, $paras_list);

        $assessment_id = $param['id'];
        $adb = dbAMysql::getInstance();
        $assessment = $adb->table('assessment')->where('where id = :id', ['id' => $assessment_id])->one();
        if (!$assessment) returnError('考核任务不存在！');
        if ($assessment['status'] != 1) returnError('只有已完成的考核任务才能重新核算');

        $attach = json_decode($assessment['attach'], true);
        $scheme = $attach['scheme'];
        $targets = $attach['targets'];
        $commissions = $attach['commissions'];
        $level_rules = $attach['level_rules'];

        $adb->table('assessment_users');
        $assessment_users = $adb->where('where a_id = :a_id', ['a_id' => $assessment_id])->list();
        $re_calc_users = [];
        foreach ($assessment_users as $assessment_user) {
            if ($assessment_user['status'] != 1) continue;
            if (!in_array($assessment_user['step'], [0, 3])) continue;
            $re_calc_users[] = $assessment_user;
        }
        if (empty($re_calc_users)) returnError('没有需要重新核算的考核用户');

        $new_scheme = $adb->table('assessment_schemes')->where('where id = :id', ['id' => $scheme['id']])->one();
        $new_scheme['attach'] = json_decode($new_scheme['attach'], true);
        $new_scheme['assessment_template'] = json_decode($new_scheme['assessment_template'], true);
        $new_scheme['assessment_scheme_process'] = json_decode($new_scheme['assessment_scheme_process'], true);
        try {
            assessmentSchemesModel::compareSchemeData($scheme, $new_scheme);
        } catch (\Throwable $e) {
            returnError('当前考核的考核方案已修改: '.$e->getMessage().'，不可重新核算。如需重新考核请发起新的考核: ');
        }

        $target_ids = array_column($targets, 'id');
        $db_targets = $adb->table('assessment_targets')->whereIn('id', $target_ids)->list();
        $db_targets_map = array_column($db_targets, null, 'id');
        foreach ($targets as $target) {
            $target['target_detail'] = json_decode($target['target_detail'], true);
            $db_targets_map[$target['id']]['target_detail'] = json_decode($db_targets_map[$target['id']]['target_detail'], true);
            try {
                assessmentSchemesModel::compareTarget($target, $db_targets_map[$target['id']]);
            } catch (\Throwable $e) {
                returnError('当前考核的考核指标已修改: '.$e->getMessage().'，不可重新核算。如需重新考核请发起新的考核: ');
            }
        }

        $commission_ids = array_column($commissions, 'id');
        if (!empty($commission_ids)) {
            $db_commissions = $adb->table('commission_rules')->whereIn('id', $commission_ids)->list();
            $db_commissions_map = array_column($db_commissions, null, 'id');
            foreach ($commissions as &$commission) {
                $commission['rules'] = json_decode($commission['rules'], true);
                $commission['prize_rules'] = json_decode($commission['prize_rules'], true);
                $db_commissions_map[$commission['id']]['rules'] = json_decode($db_commissions_map[$commission['id']]['rules'], true);
                $db_commissions_map[$commission['id']]['prize_rules'] = json_decode($db_commissions_map[$commission['id']]['prize_rules'], true);
                try {
                    assessmentSchemesModel::compareCommission($commission, $db_commissions_map[$commission['id']]);
                } catch (\Throwable $e) {
                    returnError('当前考核的提成比例已修改: '.$e->getMessage().'，不可重新核算。如需重新考核请发起新的考核: ');
                }
            }
        }

        $level_rule_ids = array_column($level_rules, 'id');
        if (!empty($level_rule_ids)) {
            $db_level_rules = $adb->table('level_rules')->whereIn('id', $level_rule_ids)->list();
        }

        // 相当于把所有的考核用户都退回到绩效核算节点
        $attach['targets'] = $db_targets;
        $attach['commissions'] = $db_commissions ?? [];
        $attach['level_rules'] = $db_level_rules ?? [];
        $attach['scheme'] = $new_scheme;

        $user_update_data = [];
        $user_log_data = [];
        $crontab_data = [];

        foreach ($re_calc_users as $assessment_user) {
            $process = $assessment_user['process'] ? json_decode($assessment_user['process'], true) : [];
            $back_node = assessmentUsersModel::getNode($process, $assessment_user['user_id'], 5);

            $update_data = [
                'status'         => 0,
                'process'        => json_encode($process, JSON_UNESCAPED_UNICODE),
                'stage'          => $back_node['id'],
                'stage_user'     => $back_node['stage_user'],
            ];

            // 退回到绩效核算，清空绩效结果
            $result = json_decode($assessment_user['result'], true);
            foreach ($result['targets'] as $key => $target) {
                unset($result['targets'][$key]['result']); // 清空结果
                unset($result['targets'][$key]['weight_result']); // 清空加权结果
                if (isset($result['targets'][$key]['result_error'])) unset($result['targets'][$key]['result_error']); // 清空结果
            }
            unset($result['target_coe']); // 清空绩效结果
            if (isset($result['target_coe_error'])) unset($result['target_coe_error']); // 清空绩效结果
            unset($result['performance']); // 清空绩效
            if (isset($result['performance_error'])) unset($result['performance_error']); // 清空绩效
            $update_data['result'] = json_encode($result, JSON_UNESCAPED_UNICODE);

            $user_update_data[] = array_merge($update_data, ['id' => $assessment_user['id']]);
            $user_log_data[] = [
                'user_id' => userModel::$qwuser_id,
                'a_id'    => $assessment_user['a_id'],
                'au_id'   => $assessment_user['id'],
                'changes' => json_encode([
                    'type'       => 7, // 重新核算
                ], JSON_UNESCAPED_UNICODE)
            ];
            // 增加定时任务进行绩效核算
            $crontab_data[] = [
                'is_crontab_task' => 0, // 非周期任务
                'link_id'         => $assessment_user['id'],
                'link_type'       => 2,
                'runtime'         => date('Y-m-d H:i:s'),
            ];
        }

        $adb->table('assessment')->where('where id = :id', ['id' => $assessment_id])->update([
            'attach' => json_encode($attach, JSON_UNESCAPED_UNICODE),
            'status' => 0 // 考核中
        ]);
        if (!empty($user_update_data)) {
            $adb->table('assessment_users');
            $adb->updateBatch($user_update_data);
        }
        if (!empty($user_log_data)) {
            $keys = array_keys($user_log_data[0]);
            $list = array_map(function ($item) {
                return array_values($item);
            }, $user_log_data);
            $chunkedArray = array_chunk($list, 500);
            foreach ($chunkedArray as $v) {
                $adb->table('assessment_users_log')->insertBatch($keys, $v);
            }
        }
        if (!empty($crontab_data)) {
            $keys = array_keys($crontab_data[0]);
            $list = array_map(function ($item) {
                return array_values($item);
            }, $crontab_data);
            $chunkedArray = array_chunk($list, 500);
            foreach ($chunkedArray as $v) {
                $adb->table('custom_crontab')->insertBatch($keys, $v);
            }
        }

        returnSuccess([], '重新核算成功');
    }
}
