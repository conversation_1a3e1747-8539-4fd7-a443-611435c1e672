<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/11 10:08
 */

namespace financial\models;

use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;

class authListModel
{
    public static array $base_auth = [
        ['key'=>'pic_url', 'name'=>'图片'],
        ['key'=>'asin', 'name'=>'ASIN'],
        ['key'=>'p_asin', 'name'=>'父ASIN'],
        ['key'=>'sku', 'name'=>'SKU'],
        ['key'=>'product_name', 'name'=>'品名'],
        ['key'=>'country', 'name'=>'国家'],
        ['key'=>'store_name', 'name'=>'店铺'],
        ['key'=>'project1', 'name'=>'项目'],
        ['key'=>'project', 'name'=>'运营部门'],
        ['key'=>'yunying', 'name'=>'运营人员'],
        ['key'=>'product_developer', 'name'=>'开发负责人'],
        ['key'=>'level_name', 'name'=>'产品等级'],
        ['key'=>'is_new', 'name'=>'新品'],
        ['key'=>'category_name', 'name'=>'分类'],
    ];
    //预警的权限
    public static array $waring_auth = [
        ['key'=>'pic_url', 'name'=>'图片'],
        ['key'=>'asin', 'name'=>'ASIN'],
        ['key'=>'country', 'name'=>'国家'],
        ['key'=>'p_asin', 'name'=>'父ASIN'],
        ['key'=>'sku', 'name'=>'SKU'],
        ['key'=>'product_name', 'name'=>'品名'],
        ['key'=>'category_name', 'name'=>'分类'],
        ['key'=>'yunying', 'name'=>'运营人员'],
        ['key'=>'waring_name', 'name'=>'预警名称'],
        ['key'=>'created_time', 'name'=>'预警时间'],
        ['key'=>'status', 'name'=>'预警状态'],
        ['key'=>'reason_txt', 'name'=>'预警值'],
        ['key'=>'rules', 'name'=>'预警监控条件'],
    ];
    /**[
    'key'=>'base_info',
    'name'=>'基础信息',
    'child'=>[
    ]
    ]**/
    private static array $default_auth = [
        [
            'key'=>'asin_table',
            'name'=>'ASIN',
            'child'=>[]
        ],
        [
            'key'=>'pasin_table',
            'name'=>'父ASIN',
            'child'=>[]
        ],
        [
            'key'=>'sku_table',
            'name'=>'SKU',
            'child'=>[]
        ],
        [
            'key'=>'yunying_table',
            'name'=>'运营榜单',
            'child'=>[]
        ],
        [
            'key'=>'store',
            'name'=>'店铺',
            'child'=>[]
        ],
        [
            'key'=>'month_total',
            'name'=>'月度汇总',
            'child'=>[]
        ],
        [
            'key'=>'hot',
            'name'=>'爆款',
            'child'=>[]
        ],
        [
            'key'=>'waring',
            'name'=>'预警',
            'child'=>[]
        ],
        [
            'key'=>'qingcang',
            'name'=>'清仓',
            'child'=>[]
        ],
    ];
    //获取系统权限
    public static function getAuth() {
        $db = dbFMysql::getInstance();
        $all_auth = $db->table('column')
            ->where('where is_delete = 0 and table_index > 0')
            ->field('data_type,key_name,column_name')
            ->list();
        $res_auth = self::getAuthList($all_auth);
        return $res_auth;
    }
    private static function getAuthList($list) {
        $new_list = [];
        $column_type = config::get('column_type','data_financial');
        foreach ($column_type as $v) {
            $item = $v;
            $item['key'] = 'type_'.$v['id'];
            $item['child'] = [];
            foreach ($list as $v1) {
                if ($v1['data_type'] ==  $v['id']) {
                    $item['child'][] = [
                        'key'=>$v1['key_name'],
                        'name'=>$v1['column_name'],
                    ];
                }
            }
            unset($item['id']);
            $new_list[] = $item;
        }
        $res_auth = self::$default_auth;
        foreach ($res_auth as &$v) {
            $v['child'][] = [
                'key'=>'base_info',
                'name'=>'基础信息',
                'child'=>$v['key'] != 'waring'?self::$base_auth:self::$waring_auth,
            ];
            if ($v['key'] != 'waring') {
                $v['child'] = array_merge($v['child'],$new_list);
            }
        }
        return $res_auth;
    }
}