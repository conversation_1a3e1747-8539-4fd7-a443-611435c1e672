openapi: 3.0.0
info:
  title: 手机卡管理API
  version: 1.0.0
  description: 提供手机卡管理的相关接口
paths:
  /shop/phoneCard/getList:
    get:
      tags:
        - 电话卡管理
      summary: 获取电话卡列表
      description: 根据条件筛选获取电话卡列表
      parameters:
        - name: phone_number
          in: query
          description: 电话号码
          required: false
          schema:
            type: string
        - name: user_id
          in: query
          description: 电话卡归属人id
          required: false
          schema:
            type: integer
        - name: user_name
          in: query
          description: 电话卡归属人姓名
          required: false
          schema:
            type: string
        - name: card_status
          in: query
          description: 电话卡状态
          required: false
          schema:
            type: string
        - name: user_status
          in: query
          description: 用户状态
          required: false
          schema:
            type: string
        - name: register_date
          in: query
          description: 注册日期
          required: false
          schema:
            type: string
            format: date
        - name: is_use_self
          in: query
          description: 是否自用
          required: false
          schema:
            type: string
        - name: phone_usage
          in: query
          description: 用途
          required: false
          schema:
            type: string
        - name: use_status
          in: query
          description: 使用状态
          required: false
          schema:
            type: string
        - name: charge_manage
          in: query
          description: 公司充值管理
          required: false
          schema:
            type: string
        - name: phone_type
          in: query
          description: 电话卡类型
          required: false
          schema:
            type: string
        - name: update_time
          in: query
          description: 更新时间
          required: false
          schema:
            type: string
            format: date-time
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页条数
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/PhoneCard'
                      total:
                        type: integer
                        description: 总记录数
                      page:
                        type: integer
                        description: 当前页码
                      page_size:
                        type: integer
                        description: 每页条数

  /shop/phoneCard/add:
    post:
      tags:
        - 电话卡管理
      summary: 新增电话卡
      description: 添加新的电话卡信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PhoneCardCreate'
      responses:
        '200':
          description: 添加成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 添加成功
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 新增记录ID

  /shop/phoneCard/edit:
    post:
      tags:
        - 电话卡管理
      summary: 编辑电话卡
      description: 修改已有电话卡信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PhoneCardEdit'
      responses:
        '200':
          description: 编辑成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 编辑成功
                  data:
                    type: object

  /shop/phoneCard/detail:
    get:
      tags:
        - 电话卡管理
      summary: 获取电话卡详情
      description: 根据ID获取电话卡详细信息
      parameters:
        - name: id
          in: query
          description: 电话卡ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回详情数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    $ref: '#/components/schemas/PhoneCard'

  /shop/phoneCard/import:
    post:
      tags:
        - 电话卡管理
      summary: 导入电话卡数据
      description: 通过Excel文件导入电话卡数据
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                excel_src:
                  type: string
                  description: Excel文件路径
              required:
                - excel_src
      responses:
        '200':
          description: 导入结果
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 导入成功
                  data:
                    type: object
                    properties:
                      error_file:
                        type: string
                        description: 错误文件路径(如果有错误)
                      error_count:
                        type: integer
                        description: 错误数据条数
                      success_count:
                        type: integer
                        description: 成功导入条数
                      data:
                        type: array
                        description: 导入数据或错误数据

  /shop/phoneCard/export:
    post:
      tags:
        - 电话卡管理
      summary: 导出电话卡数据
      description: 导出筛选后的电话卡数据为Excel文件
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                phone_number:
                  type: string
                  description: 电话号码
                user_id:
                  type: integer
                  description: 电话卡归属人id
                user_name:
                  type: string
                  description: 电话卡归属人姓名
                card_status:
                  type: string
                  description: 电话卡状态
                user_status:
                  type: string
                  description: 用户状态
                register_date:
                  type: string
                  description: 注册日期
                is_use_self:
                  type: string
                  description: 是否自用
                phone_usage:
                  type: string
                  description: 用途
                use_status:
                  type: string
                  description: 使用状态
                charge_manage:
                  type: string
                  description: 公司充值管理
                phone_type:
                  type: string
                  description: 电话卡类型
                update_time:
                  type: string
                  description: 更新时间
      responses:
        '200':
          description: 导出成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 导出成功
                  data:
                    type: object
                    properties:
                      file:
                        type: string
                        description: 导出文件路径

  /shop/phoneCard/getLog:
    get:
      tags:
        - 电话卡管理
      summary: 获取操作日志
      description: 获取电话卡的操作日志记录
      parameters:
        - name: id
          in: query
          description: 电话卡ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回日志数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          description: 日志ID
                        table_name:
                          type: string
                          description: 表名
                        table_id:
                          type: integer
                          description: 记录ID
                        before_data:
                          type: object
                          description: 修改前数据
                        after_data:
                          type: object
                          description: 修改后数据
                        operator:
                          type: integer
                          description: 操作人ID
                        created_at:
                          type: string
                          format: date-time
                          description: 创建时间
                        update_time:
                          type: string
                          format: date-time
                          description: 更新时间

  /shop/phoneCard/editBatch:
    post:
      tags:
        - 电话卡管理
      summary: 批量编辑电话卡
      description: 批量修改电话卡信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: array
                  items:
                    $ref: '#/components/schemas/PhoneCardEdit'
                  description: 要批量编辑的数据数组
              required:
                - data
      responses:
        '200':
          description: 批量编辑成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 批量编辑成功
                  data:
                    type: object

components:
  schemas:
    PhoneCard:
      type: object
      properties:
        id:
          type: integer
          description: 电话卡ID
        phone_number:
          type: string
          description: 电话号码
        user_id:
          type: integer
          description: 电话卡归属人id
        user_name:
          type: string
          description: 电话卡归属人姓名
        register_date:
          type: string
          format: date
          description: 注册日期
        phone_type:
          type: string
          description: 电话卡类型
        phone_combo:
          type: string
          description: 电话卡套餐
        is_use_self:
          type: string
          description: 是否自用
        phone_usage:
          type: string
          description: 用途
        use_status:
          type: string
          description: 使用状态
        phone_manager:
          type: string
          description: 保管人
        charge_manage:
          type: string
          description: 公司充值管理
        card_status:
          type: string
          description: 电话卡状态
        remark:
          type: string
          description: 备注
        created_at:
          type: string
          format: date-time
          description: 创建时间
        updated_at:
          type: string
          format: date-time
          description: 更新时间

    PhoneCardCreate:
      type: object
      properties:
        phone_number:
          type: string
          description: 电话号码
        user_id:
          type: integer
          description: 电话卡归属人id
        user_name:
          type: string
          description: 电话卡归属人姓名
        register_date:
          type: string
          format: date
          description: 注册日期
        phone_type:
          type: string
          description: 电话卡类型
        phone_combo:
          type: string
          description: 电话卡套餐
        is_use_self:
          type: string
          description: 是否自用
        phone_usage:
          type: string
          description: 用途
        use_status:
          type: string
          description: 使用状态
        phone_manager:
          type: string
          description: 保管人
        charge_manage:
          type: string
          description: 公司充值管理
        card_status:
          type: string
          description: 电话卡状态
        remark:
          type: string
          description: 备注
      required:
        - phone_number

    PhoneCardEdit:
      type: object
      properties:
        id:
          type: integer
          description: 电话卡ID
        phone_number:
          type: string
          description: 电话号码
        user_id:
          type: integer
          description: 电话卡归属人id
        user_name:
          type: string
          description: 电话卡归属人姓名
        register_date:
          type: string
          format: date
          description: 注册日期
        phone_type:
          type: string
          description: 电话卡类型
        phone_combo:
          type: string
          description: 电话卡套餐
        is_use_self:
          type: string
          description: 是否自用
        phone_usage:
          type: string
          description: 用途
        use_status:
          type: string
          description: 使用状态
        phone_manager:
          type: string
          description: 保管人
        charge_manage:
          type: string
          description: 公司充值管理
        card_status:
          type: string
          description: 电话卡状态
        remark:
          type: string
          description: 备注
      required:
        - id
