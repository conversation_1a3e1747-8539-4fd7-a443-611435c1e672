<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/22 13:47
 */

namespace  plugins\goods\models;

class goodsAppFunctionTestModel
{
    public static array $field_log_list = [
        'noise'=>'异响',
        'stall'=>'连接APP时，堵转断连/关机',
        'stall_not_link'=>'不连接APP时，堵转断连/关机',
        'button_lights'=>'按键灯光，屏幕显示问题',
        'bt_switch'=>'连接蓝牙时，手动关闭开关，是否会正常关闭',
        'recommendation'=>'推荐模式：播放、切换',
        'video'=>'视频模式：播放、切换',
        'diy'=>'DIY模式',
        'voice'=>'声音模式：每个单独都能用',
        'built'=>'对比产品自带模式的功能强度与APP功能内的强度是否一致',
        'noise_des'=>'异响原因',
        'stall_des'=>'连接APP时，堵转断连/关机原因',
        'stall_not_link_des'=>'不连接APP时，堵转断连/关机原因',
        'button_lights_des'=>'按键灯光，屏幕显示问题原因',
        'bt_switch_des'=>'连接蓝牙时，手动关闭开关，是否会正常关闭原因',
        'recommendation_des'=>'推荐模式：播放、切换原因',
        'video_des'=>'视频模式：播放、切换原因',
        'diy_des'=>'DIY模式原因',
        'voice_des'=>'声音模式：每个单独都能用原因',
        'built_des'=>'对比产品自带模式的功能强度与APP功能内的强度是否一致原因',
    ];
    //验证项目的键
    public static array $type_kes = ['noise','stall','stall_not_link','button_lights','bt_switch',
        'recommendation','video','diy','voice','built',];
    //验证 内容
    public static array $type_des_kes = ['noise_des','stall_des','stall_not_link_des','button_lights_des','bt_switch_des',
        'recommendation_des','video_des','diy_des','voice_des','built_des'];
}