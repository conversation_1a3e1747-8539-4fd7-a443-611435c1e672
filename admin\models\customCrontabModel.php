<?php
/**
 * @author: zhangguoming
 * @Time: 2025/4/14 13:50
 */

namespace admin\models;

use core\lib\config;
use core\lib\db\dbMysql;
use plugins\goods\form\messagesFrom;

class customCrontabModel
{
     public static string $result_text = '';
    //产品需求图制作超时提醒
    public static function goodsRequestTimeOut($id) {
        //1已完成-不需再提醒，2循环任务已提醒-还需提醒
        $return_type = 1;
        $db = dbMysql::getInstance();
        $imgs_request = $db->table('imgs_request')
            ->where('id =:id and qwuser_id > 0',['id'=>$id])
            ->one();
        $timeout_remind_id = $imgs_request['timeout_remind_id'];
        if ($imgs_request && $imgs_request['timeout_remind_id'] != 0) {
            if ($imgs_request['status'] == 1 || $imgs_request['status'] == 5) {
                $user_info = $db->table('qwuser')
                    ->where('id = :id',['id'=>$imgs_request['qwuser_id']])
                    ->one();
                $goods_info = $db->table('goods_new')
                    ->where('id = :goods_id',['goods_id'=>$imgs_request['goods_id']])
                    ->one();
                if ($goods_info) {
                    $goods_name = !empty($goods_info['goods_name'])?$goods_info['goods_name']:$goods_info['e_name'];
                    $text = "您的【{$goods_name}】【{$imgs_request['request_name']}】作图任务已超时";
                } else {
                    $text = "您的【{$imgs_request['request_name']}】作图任务已超时";
                }
                messagesFrom::senMessage([$user_info['wid']], $text, 0,$id,9);
                $return_type = 2;
            }

        }
        return [$return_type,$timeout_remind_id];
    }
    //图片测试结果
    public static function goodsRequestTestRes($request_id) {
        //1已完成-不需再提醒，2循环任务已提醒-还需提醒
        $return_type = 1;
        $db = dbMysql::getInstance();
        $request_test = $db->table('imgs_request_test')
            ->where('request_id =:request_id',['request_id'=>$request_id])
            ->one();
        $imgs_request = $db->table('imgs_request')
            ->where('id =:id',['id'=>$request_id])
            ->one();
        $test_scheme = $db->table('imgs_request_test_scheme')
            ->where('request_id = :request_id and is_delete = 0',['request_id'=>$request_id])
            ->list();
        if ($request_test && $imgs_request && count($test_scheme)) {
            $user_info = $db->table('qwuser')
                ->where('id = :id',['id'=>$request_test['user_id']])
                ->one();
            $goods_name = '';
            if ($imgs_request['goods_id']) {
                $goods_info = $db->table('goods_new')
                    ->where('id = :goods_id',['goods_id'=>$imgs_request['goods_id']])
                    ->one();
                $goods_name = !empty($goods_info['goods_name'])?$goods_info['goods_name']:$goods_info['e_name'];
            }
            $url = config::get('app_api_url','api_url').'/api/query/onePost';
            $push_ids = array_column($test_scheme,'push_id');
            $insert_data = [];
            foreach ($push_ids as $id) {
                $r_data = ['id'=>$id,'days'=>1];
                $res = requestLingXing($url,json_encode($r_data));
                if ($res && $res['code'] == 0) {
                    $data = $res['data']['total'];
                    $insert_data[$id] = [
                        'show_num'=>$data['show_num'],
                        'open_num'=>$data['open_num']??0,
                        'link_click_num'=>$data['link_click_num'],
                        'play_num'=>$data['play_num'],
                        'is_syn'=>1
                    ];
                } else {
                    $return_type = 0;
                }
            }
            if ($return_type != 0) {
                foreach ($insert_data as $push_id=>$v) {
                    $db->table('imgs_request_test_scheme')
                        ->where('push_id = :push_id and request_id = :request_id',['push_id'=>$push_id,'request_id'=>$request_id])
                        ->update($v);
                }
                $test_name = config::get('imgs_request_test_channel','data');
                $channel_data = array_column($test_name,'name','id');
                $channel_name = $channel_data[$request_test['channel_id']]??'';
                if ($goods_name) {
                    $text = "您的【{$goods_name}】【{$imgs_request['request_name']}】的【{$channel_name}测试渠道】渠道测试数据已统计，可在详情中查看数据";
                } else {
                    $text = "您的【{$imgs_request['request_name']}】的【{$channel_name}测试渠道】渠道测试数据已统计，可在详情中查看数据";
                }


                messagesFrom::senMessage([$user_info['wid']], $text, 0,$request_id,21);
            }
        }
        return $return_type;
    }
}