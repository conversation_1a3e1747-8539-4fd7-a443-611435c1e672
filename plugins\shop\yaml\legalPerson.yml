openapi: 3.0.0
info:
  title: 法人管理API
  version: 1.0.0
  description: 提供法人管理的相关接口
paths:
  /shop/legalPerson/getList:
    get:
      tags:
        - 法人管理
      summary: 获取法人列表
      description: 根据条件筛选获取法人列表
      parameters:
        - name: name
          in: query
          description: 法人姓名
          required: false
          schema:
            type: string
        - name: phone
          in: query
          description: 手机号
          required: false
          schema:
            type: string
        - name: id_card
          in: query
          description: 身份证号
          required: false
          schema:
            type: string
        - name: amazon_available
          in: query
          description: 是否可用于注册亚马逊
          required: false
          schema:
            type: integer
        - name: user_tags
          in: query
          description: 用户标签
          required: false
          schema:
            type: array
            items:
              type: string
        - name: credit_card_id
          in: query
          description: 信用卡ID
          required: false
          schema:
            type: integer
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页条数
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListResponse'
  
  /shop/legalPerson/add:
    post:
      tags:
        - 法人管理
      summary: 新增法人
      description: 新增法人信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LegalPersonCreate'
      responses:
        '200':
          description: 添加成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'
  
  /shop/legalPerson/edit:
    post:
      tags:
        - 法人管理
      summary: 编辑法人
      description: 编辑法人信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LegalPersonEdit'
      responses:
        '200':
          description: 编辑成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'
  
  /shop/legalPerson/detail:
    get:
      tags:
        - 法人管理
      summary: 获取法人详情
      description: 根据ID获取法人详细信息
      parameters:
        - name: id
          in: query
          description: 法人ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回详情数据
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailResponse'
  
  /shop/legalPerson/applyCompany:
    post:
      tags:
        - 法人管理
      summary: 申请公司
      description: 提交公司注册申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - legal_person_id
              properties:
                legal_person_id:
                  type: integer
                  description: 法人ID
      responses:
        '200':
          description: 申请成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'
  
  /shop/legalPerson/cancelCompany:
    post:
      tags:
        - 法人管理
      summary: 撤销公司申请
      description: 撤销未被接收的公司注册申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - legal_person_id
              properties:
                legal_person_id:
                  type: integer
                  description: 法人ID
      responses:
        '200':
          description: 撤销成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'
  
  /shop/legalPerson/applyFee:
    post:
      tags:
        - 法人管理
      summary: 申请费用
      description: 申请法人费用和推荐费
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - legal_person_id
              properties:
                legal_person_id:
                  type: integer
                  description: 法人ID
                legal_fee:
                  type: object
                  description: 法人费用申请
                  properties:
                    apply:
                      type: boolean
                      description: 是否申请法人费用
                    year:
                      type: integer
                      description: 申请年限
                    amount:
                      type: number
                      description: 申请金额
                recommend_fee:
                  type: object
                  description: 推荐费申请
                  properties:
                    apply:
                      type: boolean
                      description: 是否申请推荐费
                    year:
                      type: integer
                      description: 申请年限
                    amount:
                      type: number
                      description: 申请金额
                remark:
                  type: string
                  description: 备注
      responses:
        '200':
          description: 申请成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'
  
  /shop/legalPerson/cancelFeeApplication:
    post:
      tags:
        - 法人管理
      summary: 撤销费用申请
      description: 撤销未审核的费用申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - legal_person_id
              properties:
                legal_person_id:
                  type: integer
                  description: 法人ID
      responses:
        '200':
          description: 撤销成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'
  
  /shop/legalPerson/delete:
    post:
      tags:
        - 法人管理
      summary: 删除法人
      description: 删除未关联公司的法人
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
              properties:
                id:
                  type: integer
                  description: 法人ID
      responses:
        '200':
          description: 删除成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'

components:
  schemas:
    LegalPersonCreate:
      type: object
      required:
        - name
        - phone
        - id_card
      properties:
        name:
          type: string
          description: 法人姓名
        phone:
          type: string
          description: 手机号
        id_card:
          type: string
          description: 身份证号
        gender:
          type: integer
          description: 性别:1-男,2-女
        birth_date:
          type: string
          format: date
          description: 出生日期
        id_card_address:
          type: string
          description: 身份证地址
        id_card_expire:
          type: string
          format: date
          description: 身份证有效期
        city:
          type: string
          description: 常住城市
        work_status:
          type: string
          description: 法人工作情况
        education:
          type: string
          description: 学历
        education_cert:
          type: string
          description: 学历证明材料
        recommend_channel:
          type: string
          description: 推荐渠道
        recommender:
          type: string
          description: 推荐人
        relation_with_recommender:
          type: string
          description: 推荐人与法人关系
        internal_coordinator:
          type: string
          description: 内部对接人
        seal:
          type: string
          description: 法人印章
        cooperation_level:
          type: integer
          description: 法人配合度:1-差,2-一般,3-好
        amazon_available:
          type: integer
          description: 是否可用于注册亚马逊:0-否,1-是
        user_tags:
          type: array
          description: 用户标签
          items:
            type: string
        agreement:
          type: object
          description: 协议信息
        legal_fee:
          type: object
          description: 法人费用
        recommend_fee:
          type: object
          description: 推荐费
        remark:
          type: string
          description: 备注
        available_materials:
          type: string
          description: 可提供资料
    
    LegalPersonEdit:
      type: object
      required:
        - id
      properties:
        id:
          type: integer
          description: 法人ID
        name:
          type: string
          description: 法人姓名
        phone:
          type: string
          description: 手机号
        id_card:
          type: string
          description: 身份证号
        gender:
          type: integer
          description: 性别:1-男,2-女
        birth_date:
          type: string
          format: date
          description: 出生日期
        id_card_address:
          type: string
          description: 身份证地址
        id_card_expire:
          type: string
          format: date
          description: 身份证有效期
        city:
          type: string
          description: 常住城市
        work_status:
          type: string
          description: 法人工作情况
        education:
          type: string
          description: 学历
        education_cert:
          type: string
          description: 学历证明材料
        recommend_channel:
          type: string
          description: 推荐渠道
        recommender:
          type: string
          description: 推荐人
        relation_with_recommender:
          type: string
          description: 推荐人与法人关系
        internal_coordinator:
          type: string
          description: 内部对接人
        seal:
          type: string
          description: 法人印章
        cooperation_level:
          type: integer
          description: 法人配合度:1-差,2-一般,3-好
        amazon_available:
          type: integer
          description: 是否可用于注册亚马逊:0-否,1-是
        user_tags:
          type: array
          description: 用户标签
          items:
            type: string
        agreement:
          type: object
          description: 协议信息
        legal_fee:
          type: object
          description: 法人费用
        recommend_fee:
          type: object
          description: 推荐费
        remark:
          type: string
          description: 备注
        available_materials:
          type: string
          description: 可提供资料
    
    BaseResponse:
      type: object
      properties:
        code:
          type: integer
          example: 0
        message:
          type: string
          example: 操作成功
        data:
          type: object
    
    ListResponse:
      type: object
      properties:
        code:
          type: integer
          example: 0
        message:
          type: string
          example: 操作成功
        data:
          type: object
          properties:
            list:
              type: array
              items:
                $ref: '#/components/schemas/LegalPersonCreate'
            total:
              type: integer
              description: 总记录数
            page:
              type: integer
              description: 当前页码
            page_size:
              type: integer
              description: 每页条数
    
    DetailResponse:
      type: object
      properties:
        code:
          type: integer
          example: 0
        message:
          type: string
          example: 操作成功
        data:
          $ref: '#/components/schemas/LegalPersonCreate'
