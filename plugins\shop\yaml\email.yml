openapi: 3.0.0
info:
  title: 邮箱管理API
  version: 1.0.0
  description: 提供邮箱管理的相关接口
paths:
  /shop/email/getList:
    get:
      tags:
        - 邮箱管理
      summary: 获取邮箱列表
      description: 根据条件筛选获取邮箱列表
      parameters:
        - name: email_account
          in: query
          description: 邮箱账号
          required: false
          schema:
            type: string
        - name: email_assistant_email
          in: query
          description: 辅助邮箱
          required: false
          schema:
            type: string
        - name: email_safe_phone
          in: query
          description: 安全手机号
          required: false
          schema:
            type: string
        - name: phone_manager
          in: query
          description: 手机管理人
          required: false
          schema:
            type: string
        - name: email_user
          in: query
          description: 邮箱使用人
          required: false
          schema:
            type: string
        - name: use_status
          in: query
          description: 使用状态
          required: false
          schema:
            type: string
        - name: use_platform
          in: query
          description: 使用平台
          required: false
          schema:
            type: string
        - name: register_date
          in: query
          description: 注册日期
          required: false
          schema:
            type: string
            format: date
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/Email'
                      total:
                        type: integer
                        description: 总记录数
                      page:
                        type: integer
                        description: 当前页码
                      page_size:
                        type: integer
                        description: 每页条数

  /shop/email/add:
    post:
      tags:
        - 邮箱管理
      summary: 添加邮箱
      description: 新增邮箱信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailCreate'
      responses:
        '200':
          description: 添加成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 添加成功

  /shop/email/detail:
    get:
      tags:
        - 邮箱管理
      summary: 获取邮箱详情
      description: 根据ID获取邮箱详细信息
      parameters:
        - name: id
          in: query
          description: 邮箱ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回详情数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    $ref: '#/components/schemas/Email'

  /shop/email/import:
    post:
      tags:
        - 邮箱管理
      summary: 批量导入邮箱
      description: 通过Excel文件批量导入邮箱数据
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                excel_src:
                  type: string
                  description: Excel文件链接
              required:
                - excel_src
      responses:
        '200':
          description: 导入成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 导入成功
                  data:
                    type: object
                    properties:
                      error_file:
                        type: string
                        description: 错误数据文件路径
                      error_count:
                        type: integer
                        description: 错误数量
                      success_count:
                        type: integer
                        description: 成功数量
                      data:
                        type: array
                        description: 导入结果数据

  /shop/email/export:
    get:
      tags:
        - 邮箱管理
      summary: 导出邮箱数据
      description: 导出所有邮箱数据
      responses:
        '200':
          description: 导出成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 导出成功
                  data:
                    type: object
                    properties:
                      download_url:
                        type: string
                        description: 下载链接

  /shop/email/getLog:
    get:
      tags:
        - 邮箱管理
      summary: 获取操作日志
      description: 获取邮箱的操作日志记录
      parameters:
        - name: id
          in: query
          description: 邮箱ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回日志数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          description: 日志ID
                        table_name:
                          type: string
                          description: 表名
                        table_id:
                          type: integer
                          description: 记录ID
                        before_data:
                          type: object
                          description: 修改前数据
                        after_data:
                          type: object
                          description: 修改后数据
                        operator:
                          type: integer
                          description: 操作人ID
                        created_at:
                          type: string
                          format: date-time
                          description: 创建时间
                        update_time:
                          type: string
                          format: date-time
                          description: 更新时间

  /shop/email/edit:
    post:
      tags:
        - 邮箱管理
      summary: 编辑邮箱
      description: 修改邮箱信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailEdit'
      responses:
        '200':
          description: 编辑成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'

  /shop/email/delete:
    post:
      tags:
        - 邮箱管理
      summary: 删除邮箱
      description: 删除邮箱信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
              properties:
                id:
                  type: integer
                  description: 邮箱ID
      responses:
        '200':
          description: 删除成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseResponse'

  /shop/email/editBatch:
    post:
      tags:
        - 邮箱管理
      summary: 批量编辑邮箱
      description: 批量修改邮箱信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: array
                  items:
                    $ref: '#/components/schemas/EmailBatchEdit'
                  description: 编辑数据数组
              required:
                - data
      responses:
        '200':
          description: 批量编辑成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 批量编辑成功

components:
  schemas:
    BaseResponse:
      type: object
      properties:
        code:
          type: integer
          example: 0
        message:
          type: string
          example: 操作成功
        data:
          type: object

    Email:
      type: object
      properties:
        id:
          type: integer
          description: ID
        email_account:
          type: string
          description: 邮箱账号
        email_assistant_email:
          type: string
          description: 辅助邮箱
        email_safe_phone:
          type: string
          description: 安全手机号
        phone_manager:
          type: string
          description: 手机管理人
        email_user:
          type: string
          description: 邮箱使用人
        use_status:
          type: string
          description: 使用状态
        use_platform:
          type: string
          description: 使用平台
        register_date:
          type: string
          description: 注册日期
          format: date
        remark:
          type: string
          description: 备注

    EmailCreate:
      type: object
      required:
        - email_account
        - use_status
      properties:
        email_account:
          type: string
          description: 邮箱账号
        email_assistant_email:
          type: string
          description: 辅助邮箱
        email_safe_phone:
          type: string
          description: 安全手机号
        phone_manager:
          type: string
          description: 手机管理人
        email_user:
          type: string
          description: 邮箱使用人
        use_status:
          type: string
          description: 使用状态
        use_platform:
          type: string
          description: 使用平台
        register_date:
          type: string
          description: 注册日期
          format: date
        remark:
          type: string
          description: 备注

    EmailEdit:
      type: object
      required:
        - id
        - email_account
        - use_status
      properties:
        id:
          type: integer
          description: 邮箱ID
        email_account:
          type: string
          description: 邮箱账号
        email_assistant_email:
          type: string
          description: 辅助邮箱
        email_safe_phone:
          type: string
          description: 安全手机号
        phone_manager:
          type: string
          description: 手机管理人
        email_user:
          type: string
          description: 邮箱使用人
        use_status:
          type: string
          description: 使用状态
        use_platform:
          type: string
          description: 使用平台
        register_date:
          type: string
          description: 注册日期
          format: date
        remark:
          type: string
          description: 备注

    EmailBatchEdit:
      type: object
      required:
        - id
        - email_account
        - use_status
      properties:
        id:
          type: integer
          description: 邮箱ID
        email_account:
          type: string
          description: 邮箱账号
        email_assistant_email:
          type: string
          description: 辅助邮箱
        email_safe_phone:
          type: string
          description: 安全手机号
        phone_manager:
          type: string
          description: 手机管理人
        email_user:
          type: string
          description: 邮箱使用人
        use_status:
          type: string
          description: 使用状态
        use_platform:
          type: string
          description: 使用平台
        register_date:
          type: string
          description: 注册日期
          format: date
        remark:
          type: string
          description: 备注
