<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/19 16:44
 */

namespace financial\controller;

use core\lib\db\dbFMysql;

class columnController
{
    //获取数据列
    public function getList() {
        $type = $_GET['type']??0;
        $db = dbFMysql::getInstance();
        $db->table('column')
            ->where('where is_delete = 0');
        if ($type) {
            $db->andWhere('show_type<>0');
        }
        $list = $db->list();
        returnSuccess($list);
    }
    //获取监控字段
    public function getMonitoringList() {
        $type = $_GET['type'];
        $db = dbFMysql::getInstance();
        $db->table('column')
            ->where('where is_delete = 0');
        if ($type == 'level') {
            $db->andWhere('table_index > 0')
                ->field('id,column_name,custom_id,data_type,show_type,is_monitoring_level as status')
                ->order('is_monitoring_level desc');
        }
        if ($type == 'waring') {
            $db->andWhere('table_index > 0')
                ->field('id,column_name,custom_id,data_type,show_type,is_monitoring as status')
                ->order('is_monitoring desc');;
        }
        $list = $db->list();
        returnSuccess($list);
    }
}