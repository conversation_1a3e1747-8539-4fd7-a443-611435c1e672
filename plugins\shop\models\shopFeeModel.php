<?php
namespace plugins\shop\models;

class shopFeeModel extends baseModel
{
    public string $table = 'shop_fee';

    // 确认状态常量
    const STATUS_PENDING = 0;   // 待确认
    const STATUS_CONFIRMED = 1; // 已确认
    const STATUS_CANCELED = 2;  // 已撤销

    /**
     * 获取费用申请列表
     */
    public function getList($params = [], $page = 1, $pageSize = 20)
    {
        $query = $this->db->table($this->table);
            
        if (!empty($params['shop_id'])) {
            $query->where('shop_id', $params['shop_id']);
        }
        if (!empty($params['fee_type'])) {
            $query->where('fee_type', $params['fee_type']);
        }
        if (!empty($params['apply_user'])) {
            $query->where('apply_user', $params['apply_user']);
        }
        if (isset($params['confirm_status'])) {
            $query->where('confirm_status', $params['confirm_status']);
        }
        if (!empty($params['confirm_user'])) {
            $query->where('confirm_user', $params['confirm_user']);
        }
        if (!empty($params['date_start'])) {
            $query->where('created_at', '>=', $params['date_start']);
        }
        if (!empty($params['date_end'])) {
            $query->where('created_at', '<=', $params['date_end']);
        }

        $total = $query->count();
        
        $list = $query->orderBy('id', 'desc')
            ->forPage($page, $pageSize)
            ->get();

        return [
            'list' => $list,
            'total' => $total
        ];
    }

    /**
     * 获取申请详情
     */
    public function getDetail($id)
    {
        return $this->db->table($this->table)
            ->where('id', $id)
            ->one();
    }

    /**
     * 新增费用申请
     */
    public function create($data)
    {
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['confirm_status'] = self::STATUS_PENDING;
        return $this->db->table($this->table)->insert($data);
    }

    /**
     * 更新申请
     */
    public function update($id, $data)
    {
        return $this->db->table($this->table)
            ->where('id', $id)
            ->update($data);
    }

    /**
     * 确认申请
     */
    public function confirm($id, $confirmUser)
    {
        return $this->db->table($this->table)
            ->where('id', $id)
            ->update([
                'confirm_status' => self::STATUS_CONFIRMED,
                'confirm_user' => $confirmUser,
                'confirm_time' => date('Y-m-d H:i:s')
            ]);
    }

    /**
     * 撤销申请
     */
    public function cancel($id)
    {
        return $this->db->table($this->table)
            ->where('id', $id)
            ->update([
                'confirm_status' => self::STATUS_CANCELED,
                'confirm_time' => date('Y-m-d H:i:s')
            ]);
    }

    /**
     * 批量确认
     */
    public function batchConfirm($ids, $confirmUser)
    {
        return $this->db->table($this->table)
            ->whereIn('id', $ids)
            ->update([
                'confirm_status' => self::STATUS_CONFIRMED,
                'confirm_user' => $confirmUser,
                'confirm_time' => date('Y-m-d H:i:s')
            ]);
    }

    /**
     * 获取待确认费用总金额
     */
    public function getPendingAmount()
    {
        return $this->db->table($this->table)
            ->where('confirm_status', self::STATUS_PENDING)
            ->sum('fee_amount');
    }

    /**
     * 获取待确认数量
     */
    public function getPendingCount()
    {
        return $this->db->table($this->table)
            ->where('confirm_status', self::STATUS_PENDING)
            ->count();
    }
}
