<?php
/**
 * 项目创建工具
 * 用于根据需求描述创建新的项目记忆文件
 */

class ProjectCreator
{
    private $taskDir;
    private $registryFile;

    public function __construct()
    {
        $this->taskDir = __DIR__;
        $this->registryFile = $this->taskDir . '/task_registry.xml';
    }

    /**
     * 创建新项目
     * @param array $projectInfo 项目信息
     * @return string 项目文件路径
     */
    public function createProject($projectInfo)
    {
        // 生成项目ID
        $projectId = $this->generateProjectId($projectInfo['name']);
        
        // 创建项目XML文件
        $projectFile = $this->createProjectFile($projectId, $projectInfo);
        
        // 更新注册表
        $this->updateRegistry($projectId, $projectInfo, $projectFile);
        
        return $projectFile;
    }

    /**
     * 生成项目ID
     * @param string $projectName 项目名称
     * @return string
     */
    private function generateProjectId($projectName)
    {
        // 将中文项目名转换为英文ID
        $id = strtolower(preg_replace('/[^a-zA-Z0-9]/', '_', $projectName));
        $id = preg_replace('/_+/', '_', $id);
        $id = trim($id, '_');
        
        // 如果ID为空，使用时间戳
        if (empty($id)) {
            $id = 'project_' . time();
        }
        
        return $id;
    }

    /**
     * 创建项目XML文件
     * @param string $projectId 项目ID
     * @param array $projectInfo 项目信息
     * @return string 文件路径
     */
    private function createProjectFile($projectId, $projectInfo)
    {
        $template = $this->getProjectTemplate();
        
        // 替换模板变量
        $content = str_replace(
            [
                '[PROJECT_ID]',
                '[PROJECT_NAME]',
                '[PROJECT_DESCRIPTION]',
                '[BUSINESS_GOAL]',
                '[TECHNICAL_REQUIREMENTS]',
                '[DATA_REQUIREMENTS]',
                '[PRIORITY]',
                '[CREATION_DATE]'
            ],
            [
                $projectId,
                $projectInfo['name'],
                $projectInfo['description'],
                $projectInfo['business_goal'] ?? '',
                $projectInfo['technical_requirements'] ?? '',
                $projectInfo['data_requirements'] ?? '',
                $projectInfo['priority'] ?? 'medium',
                date('Y-m-d')
            ],
            $template
        );
        
        $filename = $projectId . '.xml';
        $filepath = $this->taskDir . '/' . $filename;
        
        file_put_contents($filepath, $content);
        
        return $filename;
    }

    /**
     * 获取项目模板
     * @return string
     */
    private function getProjectTemplate()
    {
        return '<?xml version="1.0" encoding="UTF-8"?>
<project>
    <meta>
        <id>[PROJECT_ID]</id>
        <name>[PROJECT_NAME]</name>
        <description>[PROJECT_DESCRIPTION]</description>
        <status>planning</status>
        <priority>[PRIORITY]</priority>
        <creation_date>[CREATION_DATE]</creation_date>
    </meta>

    <requirements>
        <business_goal>[BUSINESS_GOAL]</business_goal>
        <technical_requirements>[TECHNICAL_REQUIREMENTS]</technical_requirements>
        <data_requirements>[DATA_REQUIREMENTS]</data_requirements>
    </requirements>

    <current_system>
        <existing_files>
            <!-- 相关的现有文件 -->
        </existing_files>
        <data_flow>
            <!-- 数据流程描述 -->
        </data_flow>
    </current_system>

    <technical_architecture>
        <database_connections>
            <source>dbErpMysql</source>
            <target>dbLMysql</target>
        </database_connections>
        <processing_pattern>
            <approach>分批处理模式</approach>
            <batch_size>100条每批</batch_size>
        </processing_pattern>
    </technical_architecture>

    <implementation_plan>
        <phase name="analysis" status="pending">
            <task name="requirement_analysis" priority="high">
                <description>需求分析和技术方案设计</description>
                <deliverable>技术方案文档</deliverable>
            </task>
            <task name="database_design" priority="high">
                <description>数据库表结构设计</description>
                <deliverable>SQL建表语句</deliverable>
            </task>
        </phase>

        <phase name="development" status="pending">
            <task name="model_implementation" priority="high">
                <description>数据模型实现</description>
                <file>plugins/logistics/models/[模型名称]Model.php</file>
            </task>
            <task name="controller_implementation" priority="medium">
                <description>控制器方法实现</description>
                <file>task/controller/logisticsController.php</file>
            </task>
        </phase>

        <phase name="testing" status="pending">
            <task name="unit_tests" priority="medium">
                <description>单元测试</description>
                <file>plugins/logistics/tests/[功能名称]Test.php</file>
            </task>
            <task name="integration_tests" priority="medium">
                <description>集成测试</description>
                <scope>完整功能测试</scope>
            </task>
        </phase>

        <phase name="deployment" status="pending">
            <task name="production_deployment" priority="low">
                <description>生产环境部署</description>
                <checklist>数据库表创建、代码部署、配置更新</checklist>
            </task>
        </phase>
    </implementation_plan>

    <context_recovery>
        <key_files_to_read>
            <!-- 恢复上下文需要读取的关键文件 -->
        </key_files_to_read>
        <understanding_checkpoints>
            <checkpoint>理解业务需求</checkpoint>
            <checkpoint>设计技术方案</checkpoint>
            <checkpoint>实现核心功能</checkpoint>
            <checkpoint>完成测试验证</checkpoint>
        </understanding_checkpoints>
    </context_recovery>

    <next_actions>
        <immediate>
            <action>分析具体需求</action>
            <action>设计技术方案</action>
            <action>制定详细计划</action>
        </immediate>
        <subsequent>
            <action>实现核心功能</action>
            <action>编写测试用例</action>
            <action>部署和验证</action>
        </subsequent>
    </next_actions>
</project>';
    }

    /**
     * 更新注册表
     * @param string $projectId 项目ID
     * @param array $projectInfo 项目信息
     * @param string $projectFile 项目文件名
     */
    private function updateRegistry($projectId, $projectInfo, $projectFile)
    {
        if (!file_exists($this->registryFile)) {
            return;
        }

        $xml = simplexml_load_file($this->registryFile);
        
        // 添加到active_projects
        $activeProjects = $xml->active_projects;
        $project = $activeProjects->addChild('project');
        $project->addAttribute('id', $projectId);
        $project->addAttribute('status', 'planning');
        
        $project->addChild('name', htmlspecialchars($projectInfo['name']));
        $project->addChild('description', htmlspecialchars($projectInfo['description']));
        $project->addChild('file', $projectFile);
        $project->addChild('creation_date', date('Y-m-d'));
        $project->addChild('priority', $projectInfo['priority'] ?? 'medium');

        // 更新last_updated
        $xml->meta->last_updated = date('Y-m-d H:i:s');

        // 保存文件
        $xml->asXML($this->registryFile);
    }

    /**
     * 根据需求描述分析项目信息
     * @param string $requirementText 需求描述
     * @return array
     */
    public function analyzeRequirement($requirementText)
    {
        // 这里可以实现更智能的需求分析
        // 暂时返回基本结构
        return [
            'name' => '新功能需求',
            'description' => $requirementText,
            'business_goal' => '待分析',
            'technical_requirements' => '待分析',
            'data_requirements' => '待分析',
            'priority' => 'medium'
        ];
    }
}

// 使用示例
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $creator = new ProjectCreator();
    
    // 示例：创建新项目
    $projectInfo = [
        'name' => '示例新功能',
        'description' => '这是一个示例新功能的描述',
        'business_goal' => '提升业务效率',
        'priority' => 'high'
    ];
    
    $projectFile = $creator->createProject($projectInfo);
    echo "项目文件已创建: {$projectFile}\n";
}
