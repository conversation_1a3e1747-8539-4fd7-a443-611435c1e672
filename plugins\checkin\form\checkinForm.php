<?php

namespace plugins\checkin\form;

use plugins\checkin\models\approvelModel;
use plugins\checkin\models\ruleModel;
use core\lib\db\dbCMysql;

/**
 * @author: zhangguoming
 * @Time: 2024/6/12 10:48
 */
class checkinForm
{
    /**
     * @param $user
     * @param $start_time
     * @param $end_time
     * @param $corp_rules
     * @param $rule_map
     * @param $vacation
     * @param $userMap
     * @return array|false
     * @throws \core\lib\ExceptionError
     *
     * 用户考勤汇总
     *
     * 实际数据存在用户在不同打卡规则都打卡的情况
     * 1、发生扣款、计薪，均使用当天打卡规则配置的统计规则算薪
     * 2、迟到早退，每个打卡规则下的免除次数，分别计算
     * 3、事假、病假存在免扣天数，使用用户当前打卡规则判断是否免除，实际扣款使用当天的打卡规则
     * 4、全勤奖使用当前打卡规则配置的逻辑
     */
    public static function userCheckSummary($user, $start_time, $end_time, $corp_rules, $rule_map, $vacation, $userMap): array|false
    {
        $user_id = $user['wid'];
        $cdb = dbCMysql::getInstance();

        // 当月考勤数据
        $checkin_table = 'user_checkin_data_'.date('Y', strtotime($start_time));
        $cdb->table($checkin_table);
        $cdb->where('where checkin_date >= :start_time and checkin_date <= :end_time', ['start_time' => $start_time, 'end_time' => $end_time]);
        $cdb->andWhere("user_id = :user_id", ['user_id' => $user_id]);
        $cdb->order('checkin_date asc');
        $list = $cdb->list();
        if (empty($list)) {
            // 没有考勤数据，直接返回false
            return false;
        }
        $last_data = end($list);
        $list = array_column($list, null, 'checkin_date');

        // 使用考勤数据的最后一天作为结算
        $last_data['base_info'] = json_decode($last_data['base_info'], true);
        $corp_rule_id = $last_data['base_info']['rule_info']['groupid'];
        if (!$corp_rule_id) {
            return false;
        }
        $corp_rule = $corp_rules[$corp_rule_id];
        $full_attendance_day = $corp_rule['full_days'];
        
        // 获取用户五个月的审批单(考虑到产假等超长假期，先取五个月)
        $last_5_month = strtotime('-5 month', strtotime($start_time));
        $approvel = $cdb->table('approvel')->field('sp_no,qw_user_id,template_id, apply_data, apply_time')
            ->where('where qw_user_id = :user_id', ['user_id' => $user_id])
            ->andWhere('apply_time >= :start_time and apply_time <= :end_time and sp_status = 2', ['start_time' => $last_5_month, 'end_time' => time()])
            ->list();
        $format_data = approvelModel::formatApprovel($approvel);

        $user_approvel = $format_data['user_approvel'];// 加班审批单
        $user_mend_card = $format_data['user_mend_card']; // 补卡审批单
        $user_absence = $format_data['user_absence']; // 请假审批单
        $user_vacation = $format_data['user_vacation']; // 请假类型（因为假期被删了，只能从审批单里取 >.<）

        // 获取病假安全天数，使用用户当前打卡规则判断是否免除，实际扣款使用当天的打卡规则
        $rule_safe_sick_day = 0;
        if (isset($corp_rule['rule']['rule_1']) && isset($rule_map[$corp_rule['rule']['rule_1']]) && $rule_map[$corp_rule['rule']['rule_1']]['attach']['sick_leave']['status'] == 1) {
            $rule_safe_sick_day = $rule_map[$corp_rule['rule']['rule_1']]['attach']['sick_leave']['safe_limit'] ?? 0;
        }
        // 获取事假安全小时数，使用用户当前打卡规则判断是否免除，实际扣款使用当天的打卡规则
        $rule_safe_personal_hour = 0;
        if (isset($corp_rule['rule']['rule_1']) && isset($rule_map[$corp_rule['rule']['rule_1']]) && $rule_map[$corp_rule['rule']['rule_1']]['attach']['casual_leave']['status'] == 1) {
            $rule_safe_personal_hour = $rule_map[$corp_rule['rule']['rule_1']]['attach']['casual_leave']['safe_limit'] ?? 0;
        }
        
        // 病假全薪/天 、病假最低/天 、事假/天、事假/小时、旷工/天、
        $sick_full_salary = 0;
        $sick_low_salary = 0;
        $personal_leave_day = 0;
        $personal_leave_hour = 0;
        $person_leave_hour_safe = 0;
        $personal_leave_hour_total = 0;
        $absenteeism_day = 0;
        // 年假/天、调休/天、婚假/天、产假、丧假/天、产检假/天、体检假/天、其他
        $annual_leave_day = 0;
        $adjustable_day = 0;
        $marriage_leave_day = 0;
        $maternity_leave_day = 0;
        $funeral_leave_day = 0;
        $prenatal_check_leave_day = 0;
        $physical_examination_leave_day = 0;
        $other_leave_day = 0;
        // 上班补卡/次、下班补卡/次、缺卡/次、
        $lack_start_card = 0;
        $lack_end_card = 0;
        $lack_card = 0;
        // 出差/小时、迟到/次、迟到不扣款/次、早退/次、早退不扣款/次、
        $business_trip_hour = 0;
        $late = 0;
        $safe_late = 0; // 免除扣款的迟到
        $early = 0;
        $safe_early = 0; // 免除扣款的早退
        // 工作日加班/次、休息日加班/次、节假日加班/次、上班补卡/次、下班补卡/次、
        $workday_overtime = 0;
        $restday_overtime = 0;
        $holiday_overtime = 0;
        $work_card = 0;
        $off_card = 0;
        // 病假扣款、事假扣款、旷工扣款、缺卡扣款、迟到扣款、早退扣款、考勤扣款(病假扣款+事假扣款+旷工扣款+早退扣款)、乐捐扣款(迟到扣款+缺卡扣款)、
        $sick_deduction = 0;
        $personal_deduction = 0;
        $absenteeism_deduction = 0;
        $lack_card_deduction = 0;
        $late_deduction = 0;
        $early_deduction = 0;
        $attendance_deduction = 0;
        $donation_deduction = 0;
        // 加班餐补、加班费、加班调休、
        $overtime_dinner_addition = 0;
        $overtime_addition = 0;
        $overtime_rest = 0;
        // 应出勤天数、实际出勤天数、全勤奖励
        $should_attendance_day = 0;
        $actual_attendance_day = 0;
        $full_attendance_reward = 0;

        // 安全迟到次数、安全早退次数
        $user_safe_limit = []; // 使用用户当前打卡规则判断次数免除，实际扣款使用当天的扣款逻辑
        // 用户迟到阶梯次数
        $user_late_times = []; // 完全使用当天的打卡规则判断

        // 第一次循环，计算应出勤和实际出勤
        // 【实际出勤天数（包含福利假期）】=【应出勤天数】-【旷工天数】-【事假天数】（小时的换算成天数）
        // 把需要判断的值赋值，不然会影响后续引用
        $first_personal_leave_hour = 0;
        $first_absenteeism_day = 0;
        $first_user_safe_limit = [];
        $first_user_late_times = [];

        for ($i = 1; $i <= date('d', strtotime($end_time)); $i++) {
            $day_no_attendance_day = 0; // 当天的缺勤天数
            $day = date('Y-m-'.sprintf('%02d', $i), strtotime($end_time));
            // 单日的假勤统计
            $item = $list[$day] ?? [];
            // 没有考勤数据，直接跳过（可能是员工未入职的情况）
            if (empty($item)) {
                continue;
            }

            $base_info = json_decode($item['base_info'], true);
            $summary_info = json_decode($item['summary_info'], true);
            $exception_infos = json_decode($item['exception_infos'], true);
            $sp_items = json_decode($item['sp_items'], true);
            $ot_info_new = json_decode($item['ot_info_new'], true);

            $rest_arrive_time = $base_info['checkintime'][0]['work_sec'] ?? 9*3600; // 休息日上班打卡时间
            $rest_leave_time = null; // 休息日下班打卡时间

            // 获取当天的企业打卡规则
            $user_day_corp_rule_id = $base_info['rule_info']['groupid'];
            $user_day_corp_rule = $corp_rules[$user_day_corp_rule_id];
            $is_work = $user_day_corp_rule['days'][$day]['is_work'] ?? 0;// 0 休息 1 工作日 2 节假日
            $days[$day]['is_work'] = $is_work;
            if ($is_work == 1) { // 工作日
                $should_attendance_day++; // 应出勤天数
                // 有一次打卡即为出勤
                if ($summary_info['checkin_count'] > 0) {
                    $actual_attendance_day++; // 实际出勤天数
                }
            } else {
                if (empty($ot_info_new) && !isset($user_approvel[$user_id . '-' . $day])) {
                    // 没有加班申请，视为正常休息
                    // 2025/3/25 没有加班审批的来公司打卡的不进行任何扣款和计薪统计
                    continue;
                }
                // tips 加班校准没有标记上半天还是下半天，只能使用默认的下班时间判定
                if (!empty($ot_info_new)) {
                    $overtime_day = 0;
                    foreach ($ot_info_new as $ot) {
                        $overtime_day += $ot['duration'] / $ot['perday_duration'];
                    }
                } // 未校准，取初始加班申请
                elseif (isset($user_approvel[$user_id . '-' . $day])) {
                    $overtime_day = 0;
                    foreach ($user_approvel[$user_id . '-' . $day] as $approvel) {
                        $overtime_day += $approvel['overtime_day'];
                    }
                }
                if ($is_work == 2) { // 节假日
                    // 节假日加班计薪
                    if (isset($user_day_corp_rule['rule']['rule_5']) && isset($rule_map[$user_day_corp_rule['rule']['rule_5']])) {
                        ruleModel::calcOvertime($rule_map[$user_day_corp_rule['rule']['rule_5']], 3, $user, $overtime_day * 8 * 3600, $is_work, $rest_leave_time);
                    }
                }
                else { // 休息日
                    // 休息日加班计薪
                    if (isset($user_day_corp_rule['rule']['rule_5']) && isset($rule_map[$user_day_corp_rule['rule']['rule_5']])) {
                        ruleModel::calcOvertime($rule_map[$user_day_corp_rule['rule']['rule_5']], 2, $user, $overtime_day * 8 * 3600, $is_work, $rest_leave_time);
                    }
                }

                // 根据上午加班还是下午加班，覆盖下班打卡时间
                if ($overtime_day == 0.5 && isset($user_approvel[$user_id . '-' . $day])) {
                    $overtime_type = $user_approvel[$user_id . '-' . $day][0]['overtime_type'] ?? null;
                    if ($overtime_type == 1) { // 上午加班
                        $rest_leave_time = 12 * 3600;
                    } elseif ($overtime_type == 2) { // 下午加班
                        $rest_arrive_time = 13 * 3600 + 30 * 60;
                    }
                }
            }

            // 处理异常
            if (!empty($exception_infos)) {
                $late_absenteeism_duration = 0; // 迟到计算旷工时长
                $early_absenteeism_duration = 0; // 早退计算旷工时长
                foreach ($exception_infos as $exception) {
                    switch ($exception['exception']) {
                        case 1: // 迟到
                            if ($is_work != 1) { // 非工作日
                                // 打卡时间早于休息日上班打卡时间，不算迟到
                                if ($summary_info['earliest_time'] <= $rest_arrive_time) {
                                    break;
                                }
                            }
                            // 迟到时间需要重新计算
                            $exception['duration'] = $exception['duration'] - ($rest_arrive_time - 9 * 3600);
                            if ($exception['duration'] < 60 * 60) {
                                if (isset($user_day_corp_rule['rule']['rule_4']) && isset($rule_map[$user_day_corp_rule['rule']['rule_4']])) {
                                    if (!isset($first_user_safe_limit[$user_day_corp_rule_id]['late'])) {
                                        $first_user_safe_limit[$user_day_corp_rule_id]['late'] = 0;
                                    }
                                    if (!isset($first_user_late_times[$user_day_corp_rule_id])) {
                                        $first_user_late_times[$user_day_corp_rule_id] = [];
                                    }
                                    $late_info = ruleModel::calcComeLate($rule_map[$user_day_corp_rule['rule']['rule_4']], $exception['duration'], $first_user_safe_limit[$user_day_corp_rule_id]['late'], $first_user_late_times[$user_day_corp_rule_id]);
                                    if ($late_info['late_type'] == 2) { // 迟到旷工
                                        $first_absenteeism_day += $late_info['late_deduction'];
                                        $day_no_attendance_day = $late_info['late_deduction'];
                                    } elseif ($late_info['late_type'] == 3) { // 迟到事假
                                        $first_personal_leave_hour += $late_info['late_deduction'];
                                        $day_no_attendance_day = $late_info['late_deduction'] / 8;
                                    }
                                }
                            } else { // 迟到一小时按旷工计算
                                $late_absenteeism_duration += $exception['duration'];
                            }
                            break;
                        case 2: // 早退
                            if ($is_work != 1) { // 非工作日
                                // 打卡时间晚于休息日下班打卡时间，不算早退
                                if ($summary_info['lastest_time'] >= $rest_leave_time) {
                                    break;
                                }
                            }
                            // 早退时间需要重新计算
                            $real_leave_time = isset($base_info['checkintime']) ? last($base_info['checkintime'])['off_work_sec'] : 0;
                            $exception['duration'] = $exception['duration'] - ($real_leave_time - $rest_leave_time) ;
                            if ($exception['duration'] < 60 * 60) {
                                if (isset($user_day_corp_rule['rule']['rule_4']) && isset($rule_map[$user_day_corp_rule['rule']['rule_4']])) {
                                    if (!isset($first_user_safe_limit[$user_day_corp_rule_id]['early'])) {
                                        $first_user_safe_limit[$user_day_corp_rule_id]['early'] = 0;
                                    }
                                    $early_info = ruleModel::calcLeaveEarly($rule_map[$user_day_corp_rule['rule']['rule_4']], $exception['duration'], $first_user_safe_limit[$user_day_corp_rule_id]['early']);

                                    if ($early_info['early_type'] == 2) { // 早退旷工
                                        $first_absenteeism_day += $early_info['early_deduction'];
                                        $day_no_attendance_day = $early_info['early_deduction'];
                                    } elseif ($early_info['early_type'] == 3) { // 早退事假
                                        $first_personal_leave_hour += $early_info['early_deduction'];
                                        $day_no_attendance_day = $early_info['early_deduction'] / 8;
                                    }
                                }
                            } else { // 早退一小时按旷工计算
                                $early_absenteeism_duration += $exception['duration'];
                            }
                            break;
                        case 4: // 旷工
                            if ($is_work == 1) {
                                $first_absenteeism_day += 1;
                                $day_no_attendance_day += 1;
                            }
                            break;
                    }
                }
                // 迟到早退总时长计算旷工扣款
                $total_absenteeism_duration = $late_absenteeism_duration + $early_absenteeism_duration;
                if ($total_absenteeism_duration > 0) {
                    $total_absenteeism_day = $total_absenteeism_duration > (4 * 3600) ? 1 : 0.5;
                    $first_absenteeism_day += $total_absenteeism_day;
                    $day_no_attendance_day += $total_absenteeism_day;
                }
            }

            // 处理假勤
            foreach ($sp_items as $item) {
                if ($item['type'] == 1) { // 假期
                    if ($item['duration'] == 0) continue;
                    $absence_duration = $item['duration'];
                    if (array_key_exists($item['vacation_id'], $vacation)) {
                        $vacation_name = $vacation[$item['vacation_id']]['name'];
                    } // 以下是假期被删除的情况，需要从审批单中找到对应的假期名称
                    else {
                        $vacation_name = $user_vacation[$item['vacation_id']]['vacation_name'] ?? '';
                    }
                    if (strpos($vacation_name, '事假') !== false && strpos($vacation_name, '小时') !== false) { // 事假/小时
                        $day_personal_leave_hour = ceil($absence_duration / 3600);
                        $first_personal_leave_hour += $day_personal_leave_hour;
                        $day_no_attendance_day += $day_personal_leave_hour / 8;

                    }
                    if (strpos($vacation_name, '事假') !== false && strpos($vacation_name, '天') !== false) { // 事假/天
                        $day_personal_leave_day = $absence_duration / 86400;
                        $day_personal_leave_hour = $day_personal_leave_day * 8;
                        $first_personal_leave_hour += $day_personal_leave_hour;
                        $day_no_attendance_day += $day_personal_leave_day;
                    }
                }
            }

            // 需要记录用户每天的实际出勤天数，用于计算薪酬
            if ($days[$day]['is_work'] == 1) {
                $days[$day]['actual_attendance_day'] = 1 - $day_no_attendance_day;
            }
        }

        // 循环完之后
        // 计算未出勤天数 旷工、事假算为未出勤
        $no_attendance_day = $first_absenteeism_day + ($first_personal_leave_hour / 8.0);

        // 先放进用户信息，之后扣款有需要        
        $user['full_attendance_day'] = $full_attendance_day;
        $user['actual_attendance_day'] = $should_attendance_day - $no_attendance_day;
        $user['should_attendance_day'] = $should_attendance_day;

        // 20250328 如果员工薪资有日薪， 基本工资 = 日薪 * 应出勤天数
        if ($user['day_salary']) {
            $user['base_salary'] = $user['day_salary'] * $user['should_attendance_day'];
            $user['total_salary'] = $user['base_salary'];
        }

        // 第二次循环，计算具体的扣款和计薪
        for ($i = 1; $i <= date('d', strtotime($end_time)); $i++) {
            $day = date('Y-m-'.sprintf('%02d', $i), strtotime($end_time));
            // 单日的假勤统计
            $days[$day]['detail'] = [];
            $item = $list[$day] ?? [];
            // 没有考勤数据，直接跳过（可能是员工未入职的情况）
            if (empty($item)) {
                $days[$day]['is_work'] = 0;
                continue;
            }

            $base_info = json_decode($item['base_info'], true);
            $summary_info = json_decode($item['summary_info'], true);
            $holiday_infos = json_decode($item['holiday_infos'], true);
            $holiday_infos_new = json_decode($item['holiday_infos_new'], true);
            $exception_infos = json_decode($item['exception_infos'], true);
            $ot_info_new = json_decode($item['ot_info_new'], true);
            $sp_items = json_decode($item['sp_items'], true);

            // 获取当天的企业打卡规则
            $user_day_corp_rule_id = $base_info['rule_info']['groupid'];
            $user_day_corp_rule = $corp_rules[$user_day_corp_rule_id];
            $is_work = $user_day_corp_rule['days'][$day]['is_work'] ?? 0;// 0 休息 1 工作日 2 节假日
            $days[$day]['is_work'] = $is_work;
            $departs_name = $base_info['departs_name'];
            $rest_arrive_time = 9 * 3600; // 休息日上班打卡时间
            $rest_leave_time = null; // 休息日下班打卡时间
            if ($is_work == 1) { // 工作日
                // 工作日加班
                if (isset($user_day_corp_rule['rule']['rule_5']) && isset($rule_map[$user_day_corp_rule['rule']['rule_5']])) {
                    $overtime_info = ruleModel::calcOvertime($rule_map[$user_day_corp_rule['rule']['rule_5']], 1, $user, $summary_info['lastest_time']);
                    if ($overtime_info['is_overtime']) {
                        $workday_overtime += 1;
                        $overtime_dinner_addition += $overtime_info['overtime_addition'];
                    }
                }

                // 补卡
                if (isset($user_mend_card[$user_id . '-' . $day])) {
                    $overtime_day = 0;
                    foreach ($user_mend_card[$user_id . '-' . $day] as $item) {
                        if ($item['checkin_type'] == 1) { // 上班补卡
                            $work_card += 1;
                        } elseif ($item['checkin_type'] == 2) { // 下班补卡
                            $off_card += 1;
                        }
                    }
                }
            } 
            else { // 非工作日
                if (empty($ot_info_new) && !isset($user_approvel[$user_id . '-' . $day])) {
                    // 没有加班申请，视为正常休息
                    // 2025/3/25 没有加班审批的来公司打卡的不进行任何扣款和计薪统计
                    continue;
                }
                // 先判断加班申请校准
                // tips 加班校准没有标记上半天还是下半天，只能使用默认的下班时间判定
                if (!empty($ot_info_new)) {
                    $overtime_day = 0;
                    foreach ($ot_info_new as $ot) {
                        $overtime_day += $ot['duration'] / $ot['perday_duration'];
                    }
                } // 未校准，取初始加班申请
                elseif (isset($user_approvel[$user_id . '-' . $day])) {
                    $overtime_day = 0;
                    foreach ($user_approvel[$user_id . '-' . $day] as $approvel) {
                        $overtime_day += $approvel['overtime_day'];
                    }
                }
                if ($overtime_day > 1) $overtime_day = 1; // 超过一天的加班按一天计算
                $days[$day]['detail'][] = [
                    'type' => 5,
                    'text' => '加班' . $overtime_day . '天',
                ];

                if ($is_work == 2) { // 节假日
                    $holiday_overtime += $overtime_day;
                    // 节假日加班计薪
                    if (isset($user_day_corp_rule['rule']['rule_5']) && isset($rule_map[$user_day_corp_rule['rule']['rule_5']])) {
                        $overtime_info = ruleModel::calcOvertime($rule_map[$user_day_corp_rule['rule']['rule_5']], 3, $user, $overtime_day * 8 * 3600, $is_work, $rest_leave_time);
                        if ($overtime_info['overtime_addition_type'] == 1) { // 加班补贴
                            $overtime_addition += $overtime_info['overtime_addition'];
                        } elseif ($overtime_info['overtime_addition_type'] == 2) { // 加班调休
                            $overtime_rest += $overtime_info['overtime_addition'];
                        }
                    }
                }
                else { // 休息日
                    $restday_overtime += $overtime_day;
                    // 休息日加班计薪
                    if (isset($user_day_corp_rule['rule']['rule_5']) && isset($rule_map[$user_day_corp_rule['rule']['rule_5']])) {
                        $overtime_info = ruleModel::calcOvertime($rule_map[$user_day_corp_rule['rule']['rule_5']], 2, $user, $overtime_day * 8 * 3600, $is_work, $rest_leave_time);
                        if ($overtime_info['overtime_addition_type'] == 1) { // 加班补贴
                            $overtime_addition += $overtime_info['overtime_addition'];
                        } elseif ($overtime_info['overtime_addition_type'] == 2) { // 加班调休
                            $overtime_rest += $overtime_info['overtime_addition'];
                        }
                    }
                }

                // 根据上午加班还是下午加班，覆盖下班打卡时间
                if ($overtime_day == 0.5 && isset($user_approvel[$user_id . '-' . $day])) {
                    $overtime_type = $user_approvel[$user_id . '-' . $day][0]['overtime_type'] ?? null;
                    if ($overtime_type == 1) { // 上午加班
                        $rest_leave_time = 12 * 3600;
                    } elseif ($overtime_type == 2) { // 下午加班
                        $rest_arrive_time = 13 * 3600 + 30 * 60;
                    }
                }
                // 餐补
                if (isset($user_day_corp_rule['rule']['rule_5']) && isset($rule_map[$user_day_corp_rule['rule']['rule_5']])) {
                    $overtime_info = ruleModel::calcOvertime($rule_map[$user_day_corp_rule['rule']['rule_5']], 1, $user, $summary_info['lastest_time'], $is_work);
                    if ($overtime_info['is_overtime']) {
                        $overtime_dinner_addition += $overtime_info['overtime_addition'];
                    }
                }
            }
            // 处理异常
            if (!empty($exception_infos)) {
                $late_absenteeism_duration = 0; // 迟到计算旷工时长
                $early_absenteeism_duration = 0; // 早退计算旷工时长
                foreach ($exception_infos as $exception) {
                    switch ($exception['exception']) {
                        case 1: // 迟到
                            if ($is_work != 1) { // 非工作日
                                // 打卡时间早于休息日上班打卡时间，不算迟到
                                if ($summary_info['earliest_time'] <= $rest_arrive_time) {
                                    break;
                                }
                            }
                            // 迟到时间需要重新计算
                            $exception['duration'] = $exception['duration'] - ($rest_arrive_time - 9 * 3600);
                            $days[$day]['detail'][] = [
                                'type' => 1,
                                'text' => '迟到' . floor($exception['duration'] / 60) . '分钟',
                            ];
                            if ($exception['duration'] < 60 * 60) {
                                // 没有规则，按迟到计算
                                if (!isset($user_day_corp_rule['rule']['rule_4']) || !isset($rule_map[$user_day_corp_rule['rule']['rule_4']])) {
                                    $late += 1;
                                } else {
                                    if (!isset($user_safe_limit[$user_day_corp_rule_id]['late'])) {
                                        $user_safe_limit[$user_day_corp_rule_id]['late'] = 0;
                                    }
                                    if (!isset($user_late_times[$user_day_corp_rule_id])) {
                                        $user_late_times[$user_day_corp_rule_id] = [];
                                    }
                                    $late_info = ruleModel::calcComeLate($rule_map[$user_day_corp_rule['rule']['rule_4']], $exception['duration'], $user_safe_limit[$user_day_corp_rule_id]['late'], $user_late_times[$user_day_corp_rule_id]);
                                    if ($late_info['late_type'] == 1) { // 迟到扣款
                                        $late += 1;
                                        $late_deduction += $late_info['late_deduction'];
                                    } elseif ($late_info['late_type'] == 2) { // 迟到旷工
                                        $absenteeism_day += $late_info['late_deduction'];
                                        // 旷工扣款
                                        if (isset($user_day_corp_rule['rule']['rule_2']) && isset($rule_map[$user_day_corp_rule['rule']['rule_2']])) {
                                            ruleModel::calcAbsenteeism($rule_map[$user_day_corp_rule['rule']['rule_2']], $late_info['late_deduction'], $user, $absenteeism_deduction);
                                        }
                                        
                                    } elseif ($late_info['late_type'] == 3) { // 迟到事假
                                        $personal_leave_hour += $late_info['late_deduction'];
                                        $day_personal_leave_hour = $late_info['late_deduction'];
                                        // 安全事假时长
                                        if ($person_leave_hour_safe + $day_personal_leave_hour > $rule_safe_personal_hour) {
                                            $day_personal_leave_hour = $person_leave_hour_safe + $day_personal_leave_hour - $rule_safe_personal_hour; // 需要扣款的事假时长
                                            if ($day_personal_leave_hour > 0) {
                                                // 处理病假、事假、旷工扣款
                                                if (isset($user_day_corp_rule['rule']['rule_1']) && isset($rule_map[$user_day_corp_rule['rule']['rule_1']])) {
                                                    // 事假扣款
                                                    ruleModel::calcPersonalLeave($rule_map[$user_day_corp_rule['rule']['rule_1']], $day_personal_leave_hour, $user, 0, $personal_deduction);
                                                }
                                            }
                                            $person_leave_hour_safe = $rule_safe_personal_hour;
                                        } else {
                                            $person_leave_hour_safe += $day_personal_leave_hour;
                                        }
                                    }
                                }
                            } else { // 迟到一小时按旷工计算
                                $late_absenteeism_duration += $exception['duration'];
                            }
                            break;
                        case 2: // 早退
                            if ($is_work != 1) { // 非工作日
                                // 打卡时间早于休息日上班打卡时间，不算迟到
                                if ($summary_info['lastest_time'] >= $rest_leave_time) {
                                    break;
                                }
                            }
                            // 早退时间需要重新计算
                            $real_leave_time = isset($base_info['checkintime']) ? last($base_info['checkintime'])['off_work_sec'] : 0;
                            $exception['duration'] = $exception['duration'] - ($real_leave_time - $rest_leave_time) ;
                            $days[$day]['detail'][] = [
                                'type' => 2,
                                'text' => '早退' . floor($exception['duration'] / 60) . '分钟',
                            ];
                            if ($exception['duration'] < 60 * 60) {
                                if (!isset($user_day_corp_rule['rule']['rule_4']) || !isset($rule_map[$user_day_corp_rule['rule']['rule_4']])) {
                                    $early += 1;
                                } else {
                                    if (!isset($user_safe_limit[$user_day_corp_rule_id]['early'])) {
                                        $user_safe_limit[$user_day_corp_rule_id]['early'] = 0;
                                    }
                                    $early_info = ruleModel::calcLeaveEarly($rule_map[$user_day_corp_rule['rule']['rule_4']], $exception['duration'], $user_safe_limit[$user_day_corp_rule_id]['early']);

                                    if ($early_info['early_type'] == 1) { // 早退扣款
                                        $early += 1;
                                        $early_deduction += $early_info['early_deduction'];
                                    } elseif ($early_info['early_type'] == 2) { // 早退旷工
                                        $absenteeism_day += $early_info['early_deduction'];
                                        // 旷工扣款
                                        if (isset($user_day_corp_rule['rule']['rule_2']) && isset($rule_map[$user_day_corp_rule['rule']['rule_2']])) {
                                            ruleModel::calcAbsenteeism($rule_map[$user_day_corp_rule['rule']['rule_2']], $early_info['early_deduction'], $user, $absenteeism_deduction);
                                        }
                                    } elseif ($early_info['early_type'] == 3) { // 早退事假
                                        $personal_leave_hour += $early_info['early_deduction'];
                                        $day_personal_leave_hour = $late_info['early_deduction'];
                                        // 安全事假时长
                                        if ($person_leave_hour_safe + $day_personal_leave_hour > $rule_safe_personal_hour) {
                                            $day_personal_leave_hour = $person_leave_hour_safe + $day_personal_leave_hour - $rule_safe_personal_hour; // 需要扣款的事假时长
                                            if ($day_personal_leave_hour > 0) {
                                                // 处理病假、事假、旷工扣款
                                                if (isset($user_day_corp_rule['rule']['rule_1']) && isset($rule_map[$user_day_corp_rule['rule']['rule_1']])) {
                                                    // 事假扣款
                                                    ruleModel::calcPersonalLeave($rule_map[$user_day_corp_rule['rule']['rule_1']], $day_personal_leave_hour, $user, 0, $personal_deduction);
                                                }
                                            }
                                            $person_leave_hour_safe = $rule_safe_personal_hour;
                                        } else {
                                            $person_leave_hour_safe += $day_personal_leave_hour;
                                        }
                                    }
                                }
                            } else { // 早退一小时按旷工计算
                                $early_absenteeism_duration += $exception['duration'];
                            }
                            break;
                        case 3: // 缺卡
                            $lack_card += 1;
                            if (empty($summary_infos['earliest_time'])) $lack_start_card += 1;
                            if (empty($summary_infos['latest_time'])) $lack_end_card += 1;
                            $days[$day]['detail'][] = [
                                'type' => 3,
                                'text' => '缺卡',
                            ];
                            if (isset($user_day_corp_rule['rule']['rule_3']) && isset($rule_map[$user_day_corp_rule['rule']['rule_3']])) {
                                $lack_card_deduction += ruleModel::calcNotCheckin($rule_map[$user_day_corp_rule['rule']['rule_3']], $lack_card);
                            }
                            break;
                        case 4: // 旷工
                            $absenteeism_day += 1;
                            $days[$day]['detail'][] = [
                                'type' => 4,
                                'text' => '旷工',
                            ];
                            if (isset($user_day_corp_rule['rule']['rule_2']) && isset($rule_map[$user_day_corp_rule['rule']['rule_2']])) {
                                ruleModel::calcAbsenteeism($rule_map[$user_day_corp_rule['rule']['rule_2']], 1, $user, $absenteeism_deduction);
                            }
                            break;
                    }
                }
                // 迟到早退总时长计算旷工扣款
                $total_absenteeism_duration = $late_absenteeism_duration + $early_absenteeism_duration;
                if ($total_absenteeism_duration > 0) {
                    $total_absenteeism_day = $total_absenteeism_duration > (4 * 3600) ? 1 : 0.5;
                    $absenteeism_day += $total_absenteeism_day;
                    if (isset($user_day_corp_rule['rule']['rule_2']) && isset($rule_map[$user_day_corp_rule['rule']['rule_2']])) {
                        ruleModel::calcAbsenteeism($rule_map[$user_day_corp_rule['rule']['rule_2']], $total_absenteeism_day, $user, $absenteeism_deduction);
                    }
                }
            }

            // 处理假勤
            foreach ($sp_items as $item) {
                if ($item['type'] == 1) { // 假期
                    if ($item['duration'] == 0) continue;
                    $absence_duration = $item['duration'];
                    if (array_key_exists($item['vacation_id'], $vacation)) {
                        $vacation_name = $vacation[$item['vacation_id']]['name'];
                    } // 以下是假期被删除的情况，需要从审批单中找到对应的假期名称
                    else {
                        $vacation_name = $user_vacation[$item['vacation_id']]['vacation_name'] ?? '';
                    }
                    if (strpos($vacation_name, '事假') !== false && strpos($vacation_name, '小时') !== false) { // 事假/小时
                        $days[$day]['detail'][] = [
                            'type' => 6,
                            'text' => '事假' . round($absence_duration / 3600, 1) . '小时',
                        ];
                        $day_personal_leave_hour = ceil($absence_duration / 3600);
                        $personal_leave_hour += $day_personal_leave_hour;
                        // 安全事假时长
                        if ($person_leave_hour_safe + $day_personal_leave_hour > $rule_safe_personal_hour) {
                            $day_personal_leave_hour = $person_leave_hour_safe + $day_personal_leave_hour - $rule_safe_personal_hour; // 需要扣款的事假时长
                            if ($day_personal_leave_hour > 0) {
                                // 处理病假、事假、旷工扣款
                                if (isset($user_day_corp_rule['rule']['rule_1']) && isset($rule_map[$user_day_corp_rule['rule']['rule_1']])) {
                                    // 事假扣款
                                    ruleModel::calcPersonalLeave($rule_map[$user_day_corp_rule['rule']['rule_1']], $day_personal_leave_hour, $user, 0, $personal_deduction);
                                }
                            }
                            $person_leave_hour_safe = $rule_safe_personal_hour;
                        } else {
                            $person_leave_hour_safe += $day_personal_leave_hour;
                        }

                    }
                    if (strpos($vacation_name, '事假') !== false && strpos($vacation_name, '天') !== false) { // 事假/天
                        $days[$day]['detail'][] = [
                            'type' => 7,
                            'text' => '事假' . $absence_duration / 86400 . '天',
                        ];
                        $day_personal_leave_day = $absence_duration / 86400;
                        $day_personal_leave_hour = $day_personal_leave_day * 8;
                        $personal_leave_day += $day_personal_leave_day;
                        // 安全事假时长
                        if ($person_leave_hour_safe + $day_personal_leave_hour > $rule_safe_personal_hour) {
                            $day_personal_leave_hour = $person_leave_hour_safe + $day_personal_leave_hour - $rule_safe_personal_hour; // 需要扣款的事假时长
                            if ($day_personal_leave_hour > 0) {
                                // 处理病假、事假、旷工扣款
                                if (isset($user_day_corp_rule['rule']['rule_1']) && isset($rule_map[$user_day_corp_rule['rule']['rule_1']])) {
                                    // 事假扣款
                                    ruleModel::calcPersonalLeave($rule_map[$user_day_corp_rule['rule']['rule_1']], $day_personal_leave_hour, $user, 0, $personal_deduction);
                                }
                            }
                            $person_leave_hour_safe = $rule_safe_personal_hour;
                        } else {
                            $person_leave_hour_safe += $day_personal_leave_hour;
                        }
                    }
                    if (strpos($vacation_name, '病假') !== false) { // 病假
                        $days[$day]['detail'][] = [
                            'type' => 8,
                            'text' => '病假' . $absence_duration / 86400 . '天',
                        ];
                        $day_sick_leave_day = $absence_duration/ 86400;
                        if ($sick_full_salary + $day_sick_leave_day > $rule_safe_sick_day) {
                            // 本次需要扣款的天数
                            $remind_sick_leave_day = $day_sick_leave_day - ($rule_safe_sick_day - $sick_full_salary);
                            $sick_low_salary += $remind_sick_leave_day;
                            $sick_full_salary = $rule_safe_sick_day;
                            if (isset($user_day_corp_rule['rule']['rule_1']) && isset($rule_map[$user_day_corp_rule['rule']['rule_1']])) {
                                // 病假扣款
                                ruleModel::calcSickLeave($rule_map[$user_day_corp_rule['rule']['rule_1']], $remind_sick_leave_day, $user, $sick_deduction);
                            }
                        } else {
                            $sick_full_salary += $day_sick_leave_day;
                        }
                    }
                    if (strpos($vacation_name, '年假') !== false) { // 年假
                        $days[$day]['detail'][] = [
                            'type' => 9,
                            'text' => '年假' . $absence_duration / 86400 . '天',
                        ];
                        $annual_leave_day += $absence_duration / 86400;
                    }
                    if (strpos($vacation_name, '调休假') !== false) { // 调休
                        $days[$day]['detail'][] = [
                            'type' => 10,
                            'text' => '调休假' . $absence_duration / 86400 . '天',
                        ];
                        $adjustable_day += $absence_duration / 86400;
                    }
                    if (strpos($vacation_name, '婚假') !== false) { // 婚假
                        $days[$day]['detail'][] = [
                            'type' => 11,
                            'text' => '婚假' . $absence_duration / 86400 . '天',
                        ];
                        $marriage_leave_day += $absence_duration / 86400;
                    }
                    if (strpos($vacation_name, '产假') !== false) { // 产假
                        $days[$day]['detail'][] = [
                            'type' => 12,
                            'text' => '产假' . $absence_duration / 86400 . '天',
                        ];
                        $maternity_leave_day += $absence_duration / 86400;
                    }
                    if (strpos($vacation_name, '丧假') !== false) { // 丧假
                        $days[$day]['detail'][] = [
                            'type' => 13,
                            'text' => '丧假' . $absence_duration / 86400 . '天',
                        ];
                        $funeral_leave_day += $absence_duration / 86400;
                    }
                    if (strpos($vacation_name, '产检假') !== false) { // 产检假
                        $days[$day]['detail'][] = [
                            'type' => 14,
                            'text' => '产检假' . $absence_duration / 86400 . '天',
                        ];
                        $prenatal_check_leave_day += $absence_duration / 86400;
                    }
                    if (strpos($vacation_name, '体检假') !== false) { // 体检假
                        $days[$day]['detail'][] = [
                            'type' => 15,
                            'text' => '体检假' . $absence_duration / 86400 . '天',
                        ];
                        $physical_examination_leave_day += $absence_duration / 86400;
                    }
                    if (strpos($vacation_name, '其他') !== false) { // 其他
                        $days[$day]['detail'][] = [
                            'type' => 16,
                            'text' => '其他' . $absence_duration / 86400 . '天',
                        ];
                        $other_leave_day += $absence_duration / 86400;
                    }

                }
                if ($item['type'] == 3) { // 出差
                    $business_trip_hour += $item['duration'] / 3600;
                }
            }
        }

        // 乐捐扣款
        $donation_deduction = $late_deduction + $lack_card_deduction;
        // 考勤扣款
        $attendance_deduction = $sick_deduction + $personal_deduction + $absenteeism_deduction + $early_deduction;

        // 计算安全迟到、安全早退
        foreach ($user_safe_limit as $safe_limit) {
            $safe_late += $safe_limit['late'] ?? 0;
            $safe_early += $safe_limit['early'] ?? 0;
        }

        $user_summary_info = [
            'sick_full_salary'               => $sick_full_salary,
            'sick_low_salary'                => $sick_low_salary,
            'personal_leave_day'             => $personal_leave_day,
            'personal_leave_hour'            => $personal_leave_hour,
            'absenteeism_day'                => $absenteeism_day,
            'annual_leave_day'               => $annual_leave_day,
            'adjustable_day'                 => $adjustable_day,
            'marriage_leave_day'             => $marriage_leave_day,
            'maternity_leave_day'            => $maternity_leave_day,
            'funeral_leave_day'              => $funeral_leave_day,
            'prenatal_check_leave_day'       => $prenatal_check_leave_day,
            'physical_examination_leave_day' => $physical_examination_leave_day,
            'other_leave_day'                => $other_leave_day,
            'lack_start_card'                => $lack_start_card,
            'lack_end_card'                  => $lack_end_card,
            'lack_card'                      => $lack_card,
            'business_trip_hour'             => $business_trip_hour,
            'late'                           => $late,
            'safe_late'                      => $safe_late,
            'early'                          => $early,
            'safe_early'                     => $safe_early,
            'workday_overtime'               => $workday_overtime,
            'restday_overtime'               => $restday_overtime,
            'holiday_overtime'               => $holiday_overtime,
            'work_card'                      => $work_card,
            'off_card'                       => $off_card,
            'sick_deduction'                 => $sick_deduction,
            'personal_deduction'             => $personal_deduction,
            'absenteeism_deduction'          => $absenteeism_deduction,
            'lack_card_deduction'            => $lack_card_deduction,
            'late_deduction'                 => $late_deduction,
            'early_deduction'                => $early_deduction,
            'attendance_deduction'           => $attendance_deduction,
            'donation_deduction'             => $donation_deduction,
            'overtime_dinner_addition'       => $overtime_dinner_addition,
            'overtime_addition'              => $overtime_addition,
            'overtime_rest'                  => $overtime_rest,
            'should_attendance_day'          => $should_attendance_day,
//            'actual_attendance_day'          => $actual_attendance_day,
            'actual_attendance_day'          => $should_attendance_day - $no_attendance_day,
            'full_attendance_day'            => $full_attendance_day,
            'full_attendance_reward'         => $full_attendance_reward,
        ];
        // 全勤奖励
        if (isset($corp_rule['rule']['rule_6']) && isset($rule_map[$corp_rule['rule']['rule_6']])) {
            if ($full_attendance_day == $should_attendance_day) {
                $full_attendance_reward = ruleModel::calcFullAttendance($rule_map[$corp_rule['rule']['rule_6']], $user_summary_info, $vacation);
                $user_summary_info['full_attendance_reward'] = $full_attendance_reward;
            }
        }

        $userSummary = [
            'user_id'      => $user_id,
            'base_info'    => json_encode([
                'wid'          => $user_id,
                'wname'        => $user['wname'],
                'position'     => $user['position'],
                'departs_name' => $user['departs_name'],
                'rule_groupid' => intval($corp_rule_id)
            ], JSON_UNESCAPED_UNICODE),
            'checkin_day'  => json_encode($days, JSON_UNESCAPED_UNICODE),
            'summary_info' => json_encode($user_summary_info, JSON_UNESCAPED_UNICODE),
        ];
        return $userSummary;

    }

    // 根据节假日判断是否工作日 0 休息 1 工作日 2 节假日
    public static function isWorkDay($day, $corpRule, $holidayOption)
    {
        $rule = $corpRule['rule'];
        $off_type = $rule['off_type'] ?? 0;// 1 双休 2单休
        if (in_array($day, $holidayOption['holiday'])) { // 节假日
            return 2;
        } elseif (in_array($day, $holidayOption['workday'])) { // 调班
            return 1;
        } elseif (in_array($day, $holidayOption['nonworkday'])) { // 节日日期
            return 0;
        } elseif ($off_type == 1 && date('N', strtotime($day)) >= 6) { // 双休
            return 0;
        } elseif ($off_type == 2 && date('N', strtotime($day)) == 7) { // 单休
            return 0;
        } else {
            return 1;
        }
    }

}