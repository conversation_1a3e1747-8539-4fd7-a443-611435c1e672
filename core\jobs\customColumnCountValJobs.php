<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/24 9:57
 *
 * 导出报告数据
 */

namespace core\jobs;

use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\ExceptionError;
use core\lib\rediskeys;
use financial\common\mskuReportBase;
use financial\form\costSharingForm;
use financial\form\coustomColumnJobForm;
use financial\form\customColumnForm;
use financial\form\mskuReportForm;
use financial\form\mskuReportJobForm;
use financial\form\runShellTaskForm;
use financial\models\customColumnModel;
use financial\models\tableDataModel;

//自定义字段时计算字段值
class customColumnCountValJobs
{
    public string $unqueid = '';
    public int $id;
    public string $column_name;
    public array $rules;
    public int $show_type;
    public string $key;
    public array $relation_column;
    public array $last_relation_column;
    public int $page_size = 500;
    public static string $this_year;
    public static string $next_year;
    public array $all_keys = [];//要使用的领星所有字段
    public int $page = 1;
    public function __construct(){
        $this->unqueid = uniqid();
    }
    public function task(){
        //获取当前要分摊的数据
        $redis = (new \core\lib\predisV())::$client;
        //查看结账是否中断
        $this->key = rediskeys::$oa_custom_column_count;
        if (!$redis->exists($this->key)) {
            return;
        }
        //整个
//        if (!$redis->exists(rediskeys::$oa_count_msku_month)) {
//            //中断就终止自定在字段计算
//            $redis->del($this->key);
//            return;
//        }
        $redis_data = json_decode($redis->get($this->key),true);
        $column_id = $redis_data['column_id'];
        $m_date = $redis_data['m_date'];
        $db = dbFMysql::getInstance();
        $column_ = $db->table('custom_column_used')
            ->where('where custom_id=:id and m_date=:m_date',['id'=>$column_id,'m_date'=>$m_date])
            ->field('column_name,rules,show_type,relation_column,last_relation_column')
            ->one();
        if (!$column_) {
            //下一个字段
            return true;
        } else {
            $this->id = $column_id;
            $this->column_name = $column_['column_name'];
            $this->rules = json_decode($column_['rules'],true);
            $this->show_type = $column_['show_type'];
            $relation_column = array_unique(json_decode($column_['relation_column']));
            if ($this->id == 4) {
                $relation_column = array_diff($relation_column,customColumnForm::$aggregation_keys);
            }
            $this->relation_column = array_values($relation_column);
            $this->last_relation_column = array_values(array_unique(json_decode($column_['last_relation_column'])));
        }
        $page = $redis_data['page'];
        $this->page = $page;
        //根据规则分包分分出来，便于后后边儿查询
        coustomColumnJobForm::$last_relation_column = $this->last_relation_column;
        coustomColumnJobForm::$relation_column = $this->relation_column;
        coustomColumnJobForm::getKeys();
        $this->all_keys = coustomColumnJobForm::$all_keys;
        //领星上月字段
        $last_lx_keys = coustomColumnJobForm::$last_lx_keys;
        //领星本月字段
        $this_lx_keys = coustomColumnJobForm::$this_lx_keys;
        //自定义本月id
        $this_oa_cids = coustomColumnJobForm::$this_oa_cids;
        //自定义上月字段
        $last_oa_cids = coustomColumnJobForm::$last_oa_cids;
        //存在上月数据 （建表）
        $this_year = date('Y',strtotime($m_date));
        $next_year = date('Y',strtotime($m_date . ' +1 month'));
        tableDataModel::creatTableMonth($this_year);
        if ($this_year != $next_year) {
            tableDataModel::creatTableMonth($next_year);
        }
        self::$next_year = $next_year;
        self::$this_year = $this_year;
        //获取本月数据
        $report_list = $this->getMdateMskuReportData($m_date,$this_lx_keys,$page,1);
        $report_data = $report_list['list'];
        $all_total = $report_list['total'];
        if (count($report_data)) {
            //没有规则的直接写入数值为0
            $db = dbFMysql::getInstance();
            $db->beginTransaction();
            if (count($this->rules) == 0) {
                $this->saveCustomVal($report_data,$m_date,1);
            } else {
                //循环计算规则
                if (count($this->relation_column) == 0) {
                    //没有字段 - 直接计算
                    $this->saveCustomVal($report_data,$m_date,2);
                } else {
                    //有字段
                    $this->savHasCustomVal($report_data,$last_lx_keys,$this_oa_cids,$last_oa_cids,$m_date,$page);
                }
            }
            $db->commit();
            $file = SELF_FK.'/log/shell/count_month_msku_data.log';
            $contentToAppend = "time:".date('Y-m-d H:i:s')."，column_id：{$column_id}，page：{$page}，total:{$all_total}，page_size:{$this->page_size}。\n";
            file_put_contents($file, $contentToAppend, FILE_APPEND);
        } else {
            $redis->del($this->key);
            $file = SELF_FK.'/log/shell/count_month_msku_data.log';
            $contentToAppend = "无数据。\n";
            file_put_contents($file, $contentToAppend, FILE_APPEND);
        }
        $all_page = ceil($all_total/$this->page_size);
        $queue_key = config::get('delay_queue_key', 'app');
        if ($page < $all_page) {
            $redis_data['page'] = $redis_data['page']+1;
            $redis_data['total'] = $all_total;
            $redis_data['success_count'] += count($report_data);
            $redis->set($this->key,json_encode($redis_data));
            $redis->expire($this->key,60*60);
            $task = new customColumnCountValJobs(); // 创建任务类实例
            $redis->zAdd($queue_key, [], 0, serialize($task));
        } else {
            $used_ids = $redis_data['used_ids'];
            $used_ids[] = $column_id;
            $column_ids = $redis_data['column_ids'];
            if (count($used_ids) == count($column_ids)) {
                $redis->del($this->key);
                //更新产品等级
                runShellTaskForm::setGoodsLeve();
            } else {
                $r_data = [
                    'm_date' => $m_date,
                    'column_id'=> $column_ids[count($used_ids)],
                    'used_ids'=>$used_ids,
                    'column_ids'=>$column_ids,
                    'page' => 1,
                    'success_count'=>0,
                    'total' => 0,
                ];
                $redis->set($this->key,json_encode($r_data));
                $redis->expire($this->key,60*60);
                $task = new customColumnCountValJobs(); // 创建任务类实例
                $redis->zAdd($queue_key, [], 0, serialize($task));
            }
        }

    }
    //保存没有规则的数据
    private function saveCustomVal($report_data,$m_date,$type) {
        $year = $type == 2?self::$next_year:self::$this_year;
        if ($type == 1) {
            $total_val = 0;
        }
        $db = dbFMysql::getInstance();
        //先清除数据
        if ($this->page == 1) {
            $db->table('custom_val_'.$year)
                ->where('where m_date=:m_date and custom_id=:custom_id',['m_date'=>$m_date,'custom_id'=>$this->id])
                ->update(['custom_val'=>0]);
        }
        foreach ($report_data as $v) {
            if ($type == 2) {
                foreach ($this->rules as $rules_val) {
                    if ($rules_val['type'] == 1) {
                        $total_val = coustomColumnJobForm::getValue($rules_val['rules']);
                        break;
                    } elseif($rules_val['type'] == 2) {
                        if (in_array($v['country_code'],$rules_val['country_code'])) {
                            $total_val = coustomColumnJobForm::getValue($rules_val['rules']);
                            break;
                        }
                    } elseif($rules_val['type'] == 3) {
                        $total_val = coustomColumnJobForm::getValue($rules_val['rules']);
                        break;
                    }
                }
            }
            $res_total = $type==3?$v['custom_val']:$total_val;
            if ($this->show_type == 2) {
                $res_total = roundToString($res_total*100);
            } else {
                $res_total = roundToString($res_total);
            }
            $where_data = [
                'm_date'=>$m_date,
                'msku'=>$v['msku'],
                'sid'=>$v['sid'],
                'country_code'=>$v['country_code'],
                'custom_id'=>$this->id,
                'asin'=>$v['asin'],
                'p_asin'=>$v['p_asin'],
                'sku'=>$v['sku'],
                'project_id'=>$v['project_id'],
                'yunying_id'=>$v['yunying_id'],
                'supplier_id'=>$v['supplier_id']
            ];
            $custom_data = $db->table('custom_val_'.$year)
                ->where('where m_date=:m_date and msku=:msku and country_code=:country_code and custom_id=:custom_id and asin=:asin and p_asin=:p_asin and sku=:sku and project_id=:project_id and yunying_id=:yunying_id and sid=:sid and supplier_id=:supplier_id',$where_data)
                ->one();
            if (!$custom_data) {
                $db->table('custom_val_'.$year)
                    ->insert([
                        'm_date'=>$m_date,
                        'sid'=>$v['sid'],
                        'msku'=>$v['msku'],
                        'country_code'=>$v['country_code'],
                        'asin'=>$v['asin'],
                        'p_asin'=>$v['p_asin'],
                        'sku'=>$v['sku'],
                        'project_id'=>$v['project_id'],
                        'yunying_id'=>$v['yunying_id'],
                        'supplier_id'=>$v['supplier_id'],
                        'custom_id'=>$this->id,
                        'custom_name'=>$this->column_name,
                        'custom_val'=>$res_total,
                        'created_time'=>date('Y-m-d H:i:s'),
                        'val_type'=>$this->show_type,
                        'm_data_id'=>$v['id'],
                    ]);
            } else {
                $update_data = [
                    'custom_name'=>$this->column_name,
                    'updated_time'=>date('Y-m-d H:i:s'),
                    'val_type'=>$this->show_type,
                    'm_data_id'=>$v['id'],
                ];
                if ($type == 2 || $type == 3) {
                    $update_data['custom_val'] = $res_total;
                }
                $db->table('custom_val_'.$year)
                    ->where('where id=:id',['id'=>$custom_data['id']])
                    ->update($update_data);
            }
        }

    }

    //保存有字段规则的数据
    private function savHasCustomVal($report_data,$last_lx_keys,$this_oa_cids,$last_oa_cids,$m_date,$page) {
        $msku_list = array_column($report_data,'msku');
        //获取上月数据
        $last_m_date = date('Y-m',strtotime($m_date.'-01 -1 month'));
        $last_report_data = [];
        if (count($last_lx_keys)) {
            $last_report_list = $this->getMdateMskuReportData($last_m_date,$last_lx_keys,$page,2,$msku_list);
            $last_report_data = $last_report_list['list'];
        }
        //获取这月和上月的自定义数据
        $this_oa_data = [];
        $last_oa_data = [];
        if (count($this_oa_cids)) {
            $this_oa_data = $this->getOaData($m_date,$this_oa_cids,1,$msku_list);
        }
        if (count($last_oa_cids)) {
            $last_oa_data = $this->getOaData($m_date,$last_oa_cids,2,$msku_list);
        }
        //获取计算要用的数据、
        foreach ($report_data as $rd_k=>$row_v) {
            //获取数据值
            foreach ($this->rules as $rules_val) {
                //  type国家类型（1全部，2部分，3其余）
                if ($rules_val['type'] == 1) {
                    //国际去
                    $rule_data = $rules_val['rules'];
                    break;
                } elseif($rules_val['type'] == 2) {
                    //值
                    if (in_array($row_v['country_code'],$rules_val['country_code'])) {
                        $rule_data = $rules_val['rules'];
                        break;
                    }
                } elseif($rules_val['type'] == 3) {
                    //别分别
                    $rule_data = $rules_val['rules'];
                    break;
                }
            }
            foreach ($rule_data as &$rule_) {
                if ($rule_['group_type'] == 1) {
                    $rule_['val'] = coustomColumnJobForm::getValRow($row_v,$rule_,$last_report_data,$this_oa_data,$last_oa_data);
                } else {
                    foreach ($rule_['list'] as &$rule_l) {
                        $rule_l['val'] = coustomColumnJobForm::getValRow($row_v,$rule_l,$last_report_data,$this_oa_data,$last_oa_data);
                    }
                }
            }
            $report_data[$rd_k]['custom_val'] = coustomColumnJobForm::getValue($rule_data);
        }
        $this->saveCustomVal($report_data,$m_date,3);
    }

    //获取领星月份数据 $type 1本月，2上月 （有字段就获取字段，五字段就回去基础信息）
    private function getMdateMskuReportData($m_date,$key_list,$page,$type,$msku_list=[]) {
        $year = $type == 2?self::$next_year:self::$this_year;
        $db = dbFMysql::getInstance();
        $db->table('table_month_count_'.$year)
            ->where('where reportDateMonth=:m_date and is_delete = 0',['m_date'=>$m_date]);
        if (count($msku_list)) {
            $db->whereIn('msku',$msku_list);
        }
        $fields = 'sid,msku,asin,parentAsin as p_asin,localSku as sku,project_id,yunying_id,countryCode as country_code,supplier_id';
        if (count($key_list)) {
            //获取要查询的字段
            $fields .= ','.implode(',',$key_list);
        }
        $fields = 'id,'.$fields;
        $db->field($fields);
        $report_data = $db
            ->order('id asc')
            ->pages($page,$this->page_size);
        return $report_data;
    }
    //获取自定义月份数据 $type 1本月，2上月
    private function getOaData($m_date,$ids,$type,$msku_list) {
        $year = $type == 2?self::$next_year:self::$this_year;
        $db = dbFMysql::getInstance();
        $oa_list = $db->table('custom_val_'.$year)
            ->where('where m_date=:m_date',['m_date'=>$m_date])
            ->whereIn('custom_id',$ids)
            ->whereIn('msku',$msku_list)
            ->field('m_date,sid,custom_val,msku,asin,p_asin,sku,project_id,yunying_id,country_code,supplier_id,custom_id')
            ->list();
        return $oa_list;
    }

}