<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/1 10:02
 */

namespace  plugins\goods\models;

use plugins\goods\form\goodsNewFrom;
use core\lib\db\dbMysql;

class goodsPurchaseModel
{
    public static array $field = [
        'outer_box_info'=>'外箱规格',
        'outer_box_unit'=>'外箱规格单位',
        'pack_info'=>'产品包装规格',
        'pack_unit'=>'产品包装规格单位',
        'single_weight'=>'单个重量',
        'number'=>'单箱数量',
        'weight'=>'单箱重量',
    ];

    /**
     * @param $goods_id
     * @param $goods_purchase
     * @return void
     * 修改新品的采购信息
     */
    public static function setGoodsPurchase($goods_id, $goods_purchase) {
        $save_data = [
            'outer_box_info'=>$goods_purchase['outer_box_l'].'*'.$goods_purchase['outer_box_w'].'*'.$goods_purchase['outer_box_h'],
            'outer_box_unit'=>empty($goods_purchase['outer_box_unit'])?1:(int)$goods_purchase['outer_box_unit'],
            'pack_info'=>$goods_purchase['pack_info_l'].'*'.$goods_purchase['pack_info_w'].'*'.$goods_purchase['pack_info_h'],
            'pack_unit'=>empty($goods_purchase['pack_unit'])?1:(int)$goods_purchase['pack_unit'],
            'single_weight'=>!empty($goods_purchase['single_weight'])?$goods_purchase['single_weight']:0,
            'number'=>(int)$goods_purchase['number'],
            'weight'=>!empty($goods_purchase['weight'])?$goods_purchase['weight']:0
        ];
        //$id = $goods_purchase['id']??0;
        $db = dbMysql::getInstance();
        $purchase = $db->query('select * from oa_goods_purchase where goods_id=:goods_id',['goods_id'=>$goods_id]);
        $db->table('goods_purchase');
        if ($purchase) {
            $save_data['updated_time'] = date('Y-m-d H:i:s');
            $db->where('where id=:id', ['id'=>$purchase['id']]);
            $db->update($save_data);
            //设置修改信息
            goodsNewFrom::getGoodsPurchaseUpdate($purchase,$save_data);
        } else {
            $save_data['created_time'] = date('Y-m-d H:i:s');
            $save_data['goods_id'] = $goods_id;
            $save_data['user_id'] = userModel::$qwuser_id;
            $db->insert($save_data);
        }
    }
}