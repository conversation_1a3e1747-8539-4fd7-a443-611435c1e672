openapi: 3.0.0
info:
  title: 商标管理API
  version: 1.0.0
  description: 提供商标管理的相关接口
paths:
  /shop/trademark/getList:
    get:
      tags:
        - Trademark
      summary: 获取商标列表
      description: 根据条件筛选获取商标列表
      parameters:
        - name: department_id
          in: query
          description: 部门ID
          required: false
          schema:
            type: integer
        - name: type
          in: query
          description: 商标类型(1-自注册,2-外购)
          required: false
          schema:
            type: integer
        - name: status
          in: query
          description: 状态
          required: false
          schema:
            type: integer
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/Trademark'
                      total:
                        type: integer
                        description: 总记录数
                      page:
                        type: integer
                        description: 当前页码
                      page_size:
                        type: integer
                        description: 每页条数

  /shop/trademark/apply:
    post:
      tags:
        - Trademark
      summary: 申请商标
      description: 提交商标申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - department_id
                - country
                - category
                - reminder_interval
              properties:
                department_id:
                  type: integer
                  description: 部门ID
                country:
                  type: string
                  description: 国家
                category:
                  type: string
                  description: 类目
                reminder_interval:
                  type: integer
                  description: 提醒间隔(天)
      responses:
        '200':
          description: 申请成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonResponse'

  /shop/trademark/cancel:
    post:
      tags:
        - Trademark
      summary: 取消申请
      description: 取消商标申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
              properties:
                id:
                  type: integer
                  description: 商标ID
      responses:
        '200':
          description: 取消成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonResponse'

  /shop/trademark/startRegistration:
    post:
      tags:
        - Trademark
      summary: 启动注册
      description: 启动自注册类型商标的注册流程
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - trademark_type
                - brand_name
                - register_date
                - domain
                - register_email
                - service_provider
              properties:
                id:
                  type: integer
                  description: 商标ID
                trademark_type:
                  type: string
                  description: 商标类型
                brand_name:
                  type: string
                  description: 品牌名
                register_date:
                  type: string
                  description: 注册时间
                  format: date
                domain:
                  type: string
                  description: 域名
                register_email:
                  type: string
                  description: 注册邮箱
                service_provider:
                  type: string
                  description: 服务商
                certificate_date:
                  type: string
                  description: 下证日期
                  format: date
                certificate_number:
                  type: string
                  description: 注册号/证书号
                original_storage:
                  type: string
                  description: 原件保管地
                id_card_expire:
                  type: string
                  description: 身份证有效期
                  format: date
                earliest_declaration_date:
                  type: string
                  description: 最早宣誓日期
                  format: date
                shop_id:
                  type: string
                  description: 备案店铺
                record_date:
                  type: string
                  description: 备案日期
                  format: date
                remark:
                  type: string
                  description: 备注
                use_status:
                  type: integer
                  description: 使用状态
      responses:
        '200':
          description: 操作成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonResponse'

  /shop/trademark/registerInfo:
    post:
      tags:
        - Trademark
      summary: 商标信息登记
      description: 登记外购类型商标信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - trademark_type
                - brand_name
                - purchase_date
                - transfer_lawyer
                - trademark_holder
              properties:
                id:
                  type: integer
                  description: 商标ID
                trademark_type:
                  type: string
                  description: 商标类型
                brand_name:
                  type: string
                  description: 品牌名
                purchase_date:
                  type: string
                  description: 购买时间
                  format: date
                transfer_lawyer:
                  type: string
                  description: 转让律师
                trademark_holder:
                  type: string
                  description: 商标持有人
                transfer_date:
                  type: string
                  description: 转让日期
                  format: date
                transfer_provider:
                  type: string
                  description: 转让服务商
                price:
                  type: number
                  description: 价格
                currency:
                  type: string
                  description: 币种
                domain:
                  type: string
                  description: 域名
                register_email:
                  type: string
                  description: 注册邮箱
                service_provider:
                  type: string
                  description: 服务商
                certificate_date:
                  type: string
                  description: 下证日期
                  format: date
                certificate_number:
                  type: string
                  description: 注册号/证书号
                original_storage:
                  type: string
                  description: 原件保管地
                id_card_expire:
                  type: string
                  description: 身份证有效期
                  format: date
                earliest_declaration_date:
                  type: string
                  description: 最早宣誓日期
                  format: date
                shop_id:
                  type: string
                  description: 备案店铺
                record_date:
                  type: string
                  description: 备案日期
                  format: date
                remark:
                  type: string
                  description: 备注
                use_status:
                  type: integer
                  description: 使用状态
      responses:
        '200':
          description: 登记成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonResponse'

  /shop/trademark/add:
    post:
      tags:
        - Trademark
      summary: 新增商标
      description: 新增商标信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - trademark_type
                - brand_name
                - type
              properties:
                trademark_type:
                  type: string
                  description: 商标类型
                brand_name:
                  type: string
                  description: 品牌名
                type:
                  type: integer
                  description: 类型(1-自注册,2-外购)
      responses:
        '200':
          description: 添加成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonResponse'

  /shop/trademark/updateProgress:
    post:
      tags:
        - Trademark
      summary: 更新商标进展
      description: 更新商标状态和进展描述
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - status
                - progress_desc
              properties:
                id:
                  type: integer
                  description: 商标ID
                status:
                  type: integer
                  description: 状态
                progress_desc:
                  type: string
                  description: 进展描述
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonResponse'

  /shop/trademark/assign:
    post:
      tags:
        - Trademark
      summary: 分配商标
      description: 将商标分配给部门
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - department_id
              properties:
                id:
                  type: integer
                  description: 商标ID
                department_id:
                  type: integer
                  description: 部门ID
      responses:
        '200':
          description: 分配成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonResponse'

  /shop/trademark/getLog:
    get:
      tags:
        - Trademark
      summary: 获取操作日志
      description: 获取商标的操作日志记录
      parameters:
        - name: id
          in: query
          description: 商标ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回日志数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          description: 日志ID
                        table_name:
                          type: string
                          description: 表名
                        table_id:
                          type: integer
                          description: 记录ID
                        before_data:
                          type: object
                          description: 修改前数据
                        after_data:
                          type: object
                          description: 修改后数据
                        operator:
                          type: integer
                          description: 操作人ID
                        created_at:
                          type: string
                          format: date-time
                          description: 创建时间
                        update_time:
                          type: string
                          format: date-time
                          description: 更新时间

  /shop/trademark/getDetail:
    get:
      tags:
        - Trademark
      summary: 获取商标详情
      description: 根据ID获取商标详细信息
      parameters:
        - name: id
          in: query
          description: 商标ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回详情数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    $ref: '#/components/schemas/Trademark'

components:
  schemas:
    Trademark:
      type: object
      properties:
        id:
          type: integer
          description: ID
        department_id:
          type: integer
          description: 部门ID
        type:
          type: integer
          description: 商标类型(1-自注册,2-外购)
        status:
          type: integer
          description: 状态
        trademark_type:
          type: string
          description: 商标类型
        brand_name:
          type: string
          description: 品牌名
        country:
          type: string
          description: 国家
        category:
          type: string
          description: 类目
        reminder_interval:
          type: integer
          description: 提醒间隔(天)
        register_date:
          type: string
          description: 注册时间
          format: date
        purchase_date:
          type: string
          description: 购买时间
          format: date
        transfer_lawyer:
          type: string
          description: 转让律师
        trademark_holder:
          type: string
          description: 商标持有人
        transfer_date:
          type: string
          description: 转让日期
          format: date
        transfer_provider:
          type: string
          description: 转让服务商
        price:
          type: number
          description: 价格
        currency:
          type: string
          description: 币种
        domain:
          type: string
          description: 域名
        register_email:
          type: string
          description: 注册邮箱
        service_provider:
          type: string
          description: 服务商
        certificate_date:
          type: string
          description: 下证日期
          format: date
        certificate_number:
          type: string
          description: 注册号/证书号
        original_storage:
          type: string
          description: 原件保管地
        id_card_expire:
          type: string
          description: 身份证有效期
          format: date
        earliest_declaration_date:
          type: string
          description: 最早宣誓日期
          format: date
        shop_id:
          type: string
          description: 备案店铺
        record_date:
          type: string
          description: 备案日期
          format: date
        progress_desc:
          type: string
          description: 进展描述
        remark:
          type: string
          description: 备注
        use_status:
          type: integer
          description: 使用状态
        created_at:
          type: string
          description: 创建时间
          format: date-time
        updated_at:
          type: string
          description: 更新时间
          format: date-time

    CommonResponse:
      type: object
      properties:
        code:
          type: integer
          example: 0
        message:
          type: string
          example: 操作成功
