<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/10 18:56
 */

namespace financial\form;

use core\lib\db\dbFMysql;
use financial\common\mskuReportBase;

class coustomColumnJobForm
{
    public static array $relation_column;//本月字段
    public static array $last_relation_column;//上月字段
    public static array $all_keys;//所有领星字段
    public static array $last_lx_keys;//领星上月字段
    public static array $this_lx_keys;//领星本月字段
    public static array $this_oa_cids;//自定义本月id
    public static array $last_oa_cids;//自定义上月字段
    public static array $all_custom_ids;//所有自定字段id
    public static array $currency_keys;//货币字段（计算时要按汇率转）
    public static array $all_custom_keys;//领星字段key

    //根据规则分包分分出来，便于后后边儿查询
    public static function getKeys() {
        $db = dbFMysql::getInstance();
        //查看这些字段都再哪些表,和自定义字段的id
        $all_columns = array_merge(self::$relation_column,self::$last_relation_column);
        $column_list = $db->table('column')
            ->whereIn('key_name',$all_columns)
            ->field('custom_id,key_name,show_type')
            ->list();
        $all_keys = []; //所有领星字段
        $custom_ids = [];//所有自定义字段id
        $currency_keys = ['aggregation_val'];//金额字段
        $all_custom_keys = [];//自定义字段key
        foreach ($column_list as $column_item) {
            if ($column_item['custom_id'] > 0) {
                $custom_ids[$column_item['key_name']] = $column_item['custom_id'];
                $all_custom_keys[] = $column_item['key_name'];
            } else {
                $all_keys[] = $column_item['key_name'];
            }
            if ($column_item['show_type'] == 1) {
                $currency_keys[] = $column_item['key_name'];
            }
        }
        self::$currency_keys = array_values($currency_keys);
        self::$all_keys = $all_keys;
        self::$all_custom_ids = array_values($custom_ids);
        self::$all_custom_keys = $all_custom_keys;
        //领星上月字段
        self::$last_lx_keys = array_intersect(self::$last_relation_column,$all_keys);
        //领星本月字段
        self::$this_lx_keys = array_intersect(self::$relation_column,$all_keys);
        //自定义本月id
        self::$this_oa_cids = array_values(array_intersect_key($custom_ids,array_flip(self::$relation_column)));
        //自定义上月字段
        self::$last_oa_cids = array_values(array_intersect_key($custom_ids,array_flip(self::$last_relation_column)));
    }

    //根据运算规则获取值(已经获得所有值，只用symbol，和val)
    public static function getValue($rules_split) {
        //先计算出组的值
        $new_rules = [];
        foreach ($rules_split as $k1=>$r) {
            //group_type ，1规则，2规则组
            if ($r['group_type'] == 1) {
                $new_rules[] = ['symbol'=>$k1==0?1:$r['symbol'],'val'=>$r['val'],'is_absolute'=>$r['is_absolute']??0];
            } else {
                $list_val = self::recursionVal($r['list']);
                $new_rules[] = ['symbol'=>$r['symbol'],'val'=>$list_val,'is_absolute'=>$r['is_absolute']??0];
            }
        }
        $total_ =  round(self::recursionVal($new_rules),4);
        return  $total_;
    }
    //递归按加减乘除优先级计算数据
    private static function recursionVal($list) {
        $row_val = 0;
        $list = self::getRrecursionList($list);
        foreach ($list as $row_) {
            $val_ = $row_['is_absolute']?abs($row_['val']):$row_['val'];
            if ($row_['symbol'] == 1) {
                $row_val += $val_;
            } else {
                $row_val = ($row_val*10000 - $val_*10000)/10000;
            }
        }
        $row_val =  round($row_val,4);
        return $row_val;
    }
    private static function getRrecursionList($list) {
        //看看所有的符号
        $symbol_list = array_column($list,'symbol');
        if (in_array('3',$symbol_list) || in_array('4',$symbol_list)) {
            //有乘或者除，先算乘除
            foreach ($list as $k=>$v) {
                if ($k > 0) {
                    $last_v = $list[$k-1];
                    if (in_array($v['symbol'],[3,4])) {
                        $val1 = $last_v['is_absolute']?abs($last_v['val']):$last_v['val'];
                        $val2 = $v['is_absolute']?abs($v['val']):$v['val'];
                        if ($v['symbol'] == 4) {
                            if ($v['val'] == 0) {
                                //除数未0 直接返回0
                                return [];
                            } else {
                                $val_34 = $val1/$val2;
                            }
                        } else {
                            $val_34 = $val1*$val2;
                        }
                        $list[$k-1]['val'] = $val_34;
                        unset($list[$k]);
                        break;
                    }
                }
            }
            $list = self::getRrecursionList(array_values($list));
        }
        return $list;
    }
    public static function getValRow($row_v,$rule_,$last_report_data,$this_oa_data,$last_oa_data) {
        $row_val = 0;
        //唯一性判断
        $same_column = ['sid','msku','asin','p_asin','sku','project_id','yunying_id','currency_code','supplier_id'];
        $same_column = array_flip($same_column);
        $array_val1 = array_intersect_key($row_v,$same_column);
        //type 1本月，2上月，3自定义值
        if ($rule_['type'] == 1) {
            if (in_array($rule_['coulmn_key'],self::$all_keys)) {
                $row_val = $row_v[$rule_['coulmn_key']];
            } else {
                if (count($this_oa_data)) {
                    foreach ($this_oa_data as $gvr_v) {
                        $same = 1;
                        foreach ($array_val1 as $key=>$val) {
                            if ($val != $gvr_v[$key]) {
                                $same = 0;
                            }
                            if ($rule_['coulmn_key'] != ('oa_key_'.$gvr_v['custom_id'])) {
                                $same = 0;
                            }
                        }
                        if ($same) {
                            $row_val = $gvr_v['custom_val'];
                            break;
                        }
                    }
                }
            }
        } elseif ($rule_['type'] == 2){
            if (in_array($rule_['coulmn_key'],self::$all_keys)) {
                if (count($last_report_data)) {
                    foreach ($last_report_data as $gvr_v) {
                        $same = 1;
                        foreach ($array_val1 as $key=>$val) {
                            if ($val != $gvr_v[$key]) {
                                $same = 0;
                            }
                        }
                        if ($same) {
                            $row_val = $gvr_v[$rule_['coulmn_key']];
                            break;
                        }
                    }
                }
            } else {
                if (count($last_oa_data)) {
                    foreach ($last_oa_data as $gvr_v) {
                        $same = 1;
                        foreach ($array_val1 as $key=>$val) {
                            if ($val != $gvr_v[$key]) {
                                $same = 0;
                            }
                            if ($rule_['coulmn_key'] != ('oa_key_'.$gvr_v['custom_id'])) {
                                $same = 0;
                            }
                        }
                        if ($same) {
                            $row_val = $gvr_v['custom_val'];
                            break;
                        }
                    }
                }
            }
        } else if ($rule_['type'] == 3){
            $row_val = round($rule_['val'],4);
        }
        return $row_val;
    }
}