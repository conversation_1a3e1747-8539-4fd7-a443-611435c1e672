<?php

/**
 * @author: zhangguoming
 * @Time: 2024/6/19 9:33
 */

namespace financial\controller;

use core\lib\db\dbFMysql;
use core\lib\ExceptionError;
use financial\form\goodsWaringCountForm;
use financial\form\waringRulesForm;
use financial\models\userModel;

class waringRulesController
{
    //获取列表
    public function getList()
    {
        $paras_list = array('w_name', 'page', 'page_size','times');
        $param = arrangeParam($_POST, $paras_list);
        $db = dbFMysql::getInstance();
        $db->table('waring_rules', 'a')
            ->where('where a.is_delete = 0');

        if (!empty($param['w_name'])) {
            $db->andWhere('a.waring_name like :w_name', ['w_name' => '%' . $param['w_name'] . '%']);
        }
        // 处理时间过滤条件
        $timestampArray = self::processTimesParam($param['times']);
        if (!empty($timestampArray)) {
            $Start_Time = $timestampArray[0];
            $End_Time = $timestampArray[1];
            $db->andWhere('a.begin_time BETWEEN :start_time AND :end_time', [
                'start_time' => $Start_Time,
                'end_time' => $End_Time
            ]);
        }
        $db->leftJoinOut('db', 'qwuser', 'b', 'b.id=a.updated_user_id')
            ->leftJoin('column', 'c', 'c.id=a.column_id')
            ->field('a.id,a.waring_name,a.status,a.level,a.dimension,a.begin_time,a.receive_type,a.updated_time,b.wname as update_wname,c.key_name as monitoring_column');

        $list = $db->pages($param['page'], $param['page_size']);

        if (count($list['list'])) {
            $list['list'] = waringRulesForm::getWaringList($list['list']);

            // 获取所有的 monitoring_column 字段
            $monitoringColumns = array_column($list['list'], 'monitoring_column');

            // 查询对应的 column_name
            $columnNames = [];
            if (!empty($monitoringColumns)) {
                // 使用占位符准备查询
                $placeholders = implode(',', array_fill(0, count($monitoringColumns), '?'));
                $columns = $db->table('column')
                    ->where("where key_name IN ($placeholders)", $monitoringColumns)
                    ->field('key_name, column_name')
                    ->list();

                foreach ($columns as $column) {
                    $columnNames[$column['key_name']] = $column['column_name'];
                }
            }

            // 替换 monitoring_column 为对应的 column_name
            foreach ($list['list'] as &$item) {
                if (isset($columnNames[$item['monitoring_column']])) {
                    $item['monitoring_column'] = $columnNames[$item['monitoring_column']];
                }
            }
        }

        returnSuccess($list);
    }
    /**
     * 处理 `times` 参数并转换为时间戳数组
     *
     * @param string|array $times
     * @return array
     */
    private static function processTimesParam($times) {
        $timestampArray = [];
        // 处理 `times` 参数
        if (!empty($times)) {
            $times = json_decode($times, true);
            foreach ($times as $dateStr) {
                $dateTime = strtotime($dateStr);
                if ($dateTime) {
                    $timestampArray[] = $dateTime;
                }
            }
        }
        return $timestampArray;
    }

    //新增修改
    public function edit()
    {
        $paras_list = array('id', 'waring_name', 'status', 'level', 'dimension', 'begin_time', 'receive_type', 'rules', 'column_id', 'description');
        $request_list = ['waring_name' => '预警名称', 'dimension' => '维度', 'begin_time' => '生效时间', 'receive_type' => '预警接收人', 'rules' => '规则', 'column_id' => '监控指标'];
        $length_data = ['waring_name' => ['name' => '预警名称', 'length' => 30], 'description' => ['name' => '描述', 'length' => 200]];
        $param = arrangeParam($_POST, $paras_list, $request_list, $length_data);
        //规则验证
        $db = dbFMysql::getInstance();
        $column = $db->table('column')
            ->where('where id=:id',['id'=>$param['column_id']])
            ->one();
        if (!$column) {
            returnError('未查询到该监控指标字段');
        }
        //维度只允许asin
        if ($param['dimension'] != 1) {
            returnError('维度只允许asin');
        }
        //提交时需要判断是否有该月相同规则名称的规则，若有则不允许提交，提示“该月份已有此规则”
        $check = $db->table('waring_rules')
            ->where('where waring_name=:waring_name and begin_time=:begin_time and is_delete=0 and id<>:id',
                ['waring_name' => $param['waring_name'], 'begin_time' => strtotime($param['begin_time']), 'id' => $param['id']])
            ->one();
        if ($check){
            returnError('该月份已有此规则');
        }

        // 获取传递的生效时间
        $begin_time = $param['begin_time'] ?? '';

        // 获取当前的年份和月份
        $current_year_month = date('Y-m');

        // 检查生效时间是否早于当前月份
        if ($begin_time < $current_year_month) {
            returnError('生效时间不可选择之前的月份。');
        }

        $rules = waringRulesForm::verifyRules($param['rules'],$column['show_type']);
        $id = (int)$param['id'];
        $db->beginTransaction();
        try {
            if ($id) {
                $waring = $db->table('waring_rules')
                    ->where('where id=:id', ['id' => $id])->one();
                if (!$waring) {
                    returnError('未找到预警数据');
                }
                $update_data = [
                    'waring_name' => $param['waring_name'],
                    'status' => (int)$param['status'],
                    'level' => (int)$param['level'],
                    'dimension' => (int)$param['dimension'],
                    'begin_time' => strtotime($param['begin_time']),
                    'receive_type' => $param['receive_type'],
                    'rules' => json_encode($rules),
                    'updated_time' => date('Y-m-d H:i:s'),
                    'created_time' => date('Y-m-d H:i:s'),
                    'updated_user_id' => userModel::$qwuser_id,
                    'column_id' => (int)$param['column_id'],
                    'description' => $param['description'],
                ];
                $db->table('waring_rules')
                    ->where('where id=:id', ['id' => $id])
                    ->update($update_data);
                waringRulesForm::setUpdateLog($id, $update_data, 2, $waring);
                $db->commit();
                returnSuccess([], '修改成功');
            } else {
                $insert_data = [
                    'user_id' => userModel::$qwuser_id,
                    'waring_name' => $param['waring_name'],
                    'status' => (int)$param['status'],
                    'level' => (int)$param['level'],
                    'dimension' => (int)$param['dimension'],
                    'begin_time' => strtotime($param['begin_time']),
                    'receive_type' => $param['receive_type'],
                    'rules' => json_encode($rules),
                    'updated_time' => date('Y-m-d H:i:s'),
                    'created_time' => date('Y-m-d H:i:s'),
                    'updated_user_id' => userModel::$qwuser_id,
                    'column_id' => (int)$param['column_id'],
                    'description' => $param['description'],
                ];
                $id = $db->table('waring_rules')
                    ->insert($insert_data);
                waringRulesForm::setUpdateLog($id, $insert_data, 1);
                $db->commit();
                returnSuccess([], '新增成功');
            }
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //详情获取
    public function getDetail()
    {
        $paras_list = array('id');
        $request_list = ['id' => '预警ID'];
        $param = arrangeParam($_POST, $paras_list, $request_list,);
        $id = (int)$param['id'];
        $db = dbFMysql::getInstance();
        $waring = $db->table('waring_rules')
            ->where('where id=:id', ['id' => $id])
            ->one();
        if (!$waring) {
            returnError('未找到预警数据');
        }
        returnSuccess($waring);
    }
    //查看修改记录
    public function getUpdateList()
    {
        $paras_list = array('id', 'page', 'page_size');
        $request_list = ['id' => '预警ID'];
        $param = arrangeParam($_POST, $paras_list, $request_list,);
        $id = (int)$param['id'];
        $db = dbFMysql::getInstance();
        $list = $db->table('waring_rules_log', 'a')
            ->where('where a.waring_id=:id', ['id' => $id])
            ->leftJoinOut('db', 'qwuser', 'b', 'b.id = a.user_id')
            ->field('a.*,b.wname as update_wname')
            ->order('id desc')
            ->pages($param['page'], $param['page_size']);
        if (count($list['list'])) {
            foreach ($list['list'] as &$v) {
                $v['updated_data'] = json_decode($v['updated_data'], true);
            }
        }
        returnSuccess($list);
    }
    //删除
    public function del()
    {
        $paras_list = array('id');
        $request_list = ['id' => '预警ID'];
        $param = arrangeParam($_POST, $paras_list, $request_list,);
        $id = (int)$param['id'];
        $db = dbFMysql::getInstance();
        $db->beginTransaction();
        try {
            $db->table('waring_rules')
                ->where('where id=:id', ['id' => $id])
                ->update(['is_delete' => 1]);
            waringRulesForm::setUpdateLog($id, '', 3);
            $db->commit();
            returnSuccess([], '删除成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //导出
    public function export()
    {
        $paras_list = array('w_name','times');
        $param = arrangeParam($_POST, $paras_list);
        $db = dbFMysql::getInstance();
        $db->table('waring_rules', 'a')
            ->where('where a.is_delete = 0');
        if (!empty($param['w_name'])) {
            $db->andWhere('a.waring_name like :w_name', ['w_name' => '%' . $param['w_name'] . '%']);
        }
        // 处理时间过滤条件
        $timestampArray = self::processTimesParam($param['times']);
        if (!empty($timestampArray)) {
            $Start_Time = $timestampArray[0];
            $End_Time = $timestampArray[1];
            $db->andWhere('a.begin_time BETWEEN :start_time AND :end_time', [
                'start_time' => $Start_Time,
                'end_time' => $End_Time
            ]);
        }
        $db->leftJoinOut('db', 'qwuser', 'b', 'b.id=a.updated_user_id')
            ->leftJoin('column', 'c', 'c.id=a.column_id')
//            ->field('a.id,a.waring_name,a.status,a.level,a.rules,a.dimension,c.key_name as monitoring_column,a.begin_time,a.receive_type,a.updated_time,b.wname as update_wname');
            ->field('a.id,a.waring_name,a.status,a.level,a.rules,a.dimension,c.key_name as monitoring_column,a.begin_time,a.receive_type,a.updated_time,b.wname as update_wname');
        $list = $db->list();
//        dd($list);
        $url = waringRulesForm::exportWaringList($list);
        returnSuccess(['url' => $url]);
    }

    //配置监控指标
    public function setMonitoring()
    {
        $ids = $_POST['ids'] ?? '[]';
        $ids = json_decode($ids);
        $db = dbFMysql::getInstance();
        $db->table('column')
            ->where('where is_delete = 0')
            ->update(['is_monitoring' => 0]);
        if (count($ids)) {
            $db->table('column')
                ->whereIn('id', $ids)
                ->update(['is_monitoring' => 1]);
        }
        returnSuccess([], '设置成功');
    }
    //根据预警生成预警商品
    public function setWaringGoods() {
        $id = $_POST['waring_id'];
        $m_date = $_POST['m_date']??date('Y-m');
        goodsWaringCountForm::setWaringGoods($id,$m_date);
        if (!empty(goodsWaringCountForm::$return_msg)) {
            returnError(goodsWaringCountForm::$return_msg);
        } else {
            returnSuccess('',"更新成功");
        }
    }

}
