<?php
/**
 * FBA货件明细功能单元测试
 * @purpose 测试FBA货件明细的完整功能
 * @Author: System
 * @Time: 2025/07/01
 */

require_once __DIR__ . '/../../../core/fk.php';

use plugins\logistics\models\fbaInboundShipmentRawModel;
use plugins\logistics\models\fbaInboundShipmentDetailModel;

class fbaInboundShipmentTest
{
    private $rawModel;
    private $detailModel;
    private $testData;

    public function setUp()
    {
        $this->rawModel = new fbaInboundShipmentRawModel();
        $this->detailModel = new fbaInboundShipmentDetailModel();
        $this->prepareTestData();
    }

    /**
     * 准备测试数据
     */
    private function prepareTestData()
    {
        // 模拟领星API返回的数据
        $this->testData = [
            'api_data' => [
                'list' => [
                    [
                        'id' => 999999,
                        'shipment_sn' => 'TEST_FBA_001',
                        'status' => 1,
                        'wname' => '测试仓库',
                        'wid' => 8888,
                        'create_time' => '2024-12-01',
                        'gmt_create' => '2024-12-01 10:00:00',
                        'shipment_time' => '2024-12-02',
                        'logistics_provider_name' => '测试物流商',
                        'logistics_channel_name' => '测试物流渠道',
                        'method_name' => '测试运输方式',
                        'create_user' => '测试用户',
                        'remark' => '测试备注',
                        'status_name' => '已发货',
                        'relate_list' => [
                            [
                                'id' => 111111,
                                'shipment_id' => 'FBA_TEST_001',
                                'sku' => 'TEST_SKU_001',
                                'product_name' => '测试产品1',
                                'asin' => 'TEST_ASIN_001',
                                'fnsku' => 'TEST_FNSKU_001',
                                'num' => 100,
                                'quantity_shipped' => 100,
                                'sname' => 'TEST_SHOP:测试店铺',
                                'nation' => '美国',
                                'destination_fulfillment_center_id' => 'FTW1',
                                'product_id' => 12345,
                                'parent_asin' => 'TEST_PARENT_ASIN',
                                'msku' => 'TEST_MSKU_001',
                                'fulfillment_network_sku' => 'TEST_FN_SKU_001',
                                'pic_url' => 'http://test.com/image.jpg',
                                'packing_type' => 1,
                                'packing_type_name' => '混装商品',
                                'is_combo' => 0,
                                'create_by_mws' => 1,
                                'product_valid_num' => 50,
                                'product_qc_num' => 0,
                                'diff_num' => 0,
                                'remark' => '测试明细备注'
                            ]
                        ],
                        'logistics' => [
                            'tracking_no' => 'TEST_TRACKING_001',
                            'transport_type' => 2,
                            'transport_type_name' => '海运'
                        ]
                    ]
                ],
                'total' => 1
            ]
        ];
    }

    /**
     * 测试原始数据保存功能
     */
    public function testRawDataSave()
    {
        echo "测试原始数据保存功能...\n";
        
        $apiData = $this->testData['api_data'];
        $result = $this->rawModel->batchSaveApiData($apiData);
        
        if (!$result) {
            throw new Exception("原始数据保存失败");
        }
        
        // 验证数据是否正确保存
        $savedData = $this->rawModel->getByShipmentSn('TEST_FBA_001');
        if (!$savedData) {
            throw new Exception("保存的原始数据无法查询到");
        }
        
        // 验证关键字段
        if ($savedData['shipment_sn'] !== 'TEST_FBA_001') {
            throw new Exception("发货单号保存错误");
        }
        
        if ($savedData['wname'] !== '测试仓库') {
            throw new Exception("仓库名称保存错误");
        }
        
        echo "✅ 原始数据保存功能测试通过\n";
    }

    /**
     * 测试数据转换功能
     */
    public function testDataTransformation()
    {
        echo "测试数据转换功能...\n";
        
        // 先保存原始数据
        $this->testRawDataSave();
        
        // 获取原始数据进行转换
        $rawDataList = $this->rawModel->getUnprocessedData(0, 100, date('Y-m-d'));
        
        if (empty($rawDataList)) {
            throw new Exception("无法获取待转换的原始数据");
        }
        
        // 执行数据转换
        $result = $this->detailModel->syncFromRawData($rawDataList);
        
        if ($result['success_count'] === 0) {
            throw new Exception("数据转换失败：" . implode(', ', $result['errors']));
        }
        
        // 验证转换后的数据
        $detailData = $this->detailModel->getByShipmentSnAndRelateId('TEST_FBA_001', 111111);
        if (!$detailData) {
            throw new Exception("转换后的明细数据无法查询到");
        }
        
        // 验证关键字段映射
        if ($detailData['warehouse_name'] !== '测试仓库') {
            throw new Exception("仓库名称映射错误");
        }
        
        if ($detailData['product_name'] !== '测试产品1') {
            throw new Exception("产品名称映射错误");
        }
        
        if ($detailData['plan_quantity'] !== 100) {
            throw new Exception("计划数量映射错误");
        }
        
        if ($detailData['tracking_number'] !== 'TEST_TRACKING_001') {
            throw new Exception("跟踪单号映射错误");
        }
        
        echo "✅ 数据转换功能测试通过\n";
    }

    /**
     * 测试列表查询功能
     */
    public function testListQuery()
    {
        echo "测试列表查询功能...\n";
        
        // 先确保有测试数据
        $this->testDataTransformation();
        
        // 测试基本查询
        $params = [
            'page' => 1,
            'page_size' => 10
        ];
        
        $result = $this->detailModel->getDetailList($params);
        
        if (empty($result['list'])) {
            throw new Exception("列表查询返回空结果");
        }
        
        if ($result['total'] < 1) {
            throw new Exception("总数统计错误");
        }
        
        // 测试筛选查询
        $filterParams = [
            'page' => 1,
            'page_size' => 10,
            'warehouse_name' => '测试仓库',
            'country' => '美国',
            'product_name' => '测试产品'
        ];
        
        $filterResult = $this->detailModel->getDetailList($filterParams);
        
        if (empty($filterResult['list'])) {
            throw new Exception("筛选查询返回空结果");
        }
        
        // 验证筛选结果
        $firstItem = $filterResult['list'][0];
        if ($firstItem['warehouse_name'] !== '测试仓库') {
            throw new Exception("仓库筛选失败");
        }
        
        if ($firstItem['country'] !== '美国') {
            throw new Exception("国家筛选失败");
        }
        
        echo "✅ 列表查询功能测试通过\n";
    }

    /**
     * 测试可编辑字段更新功能
     */
    public function testEditableFieldsUpdate()
    {
        echo "测试可编辑字段更新功能...\n";
        
        // 先确保有测试数据
        $this->testDataTransformation();
        
        // 准备更新数据
        $editableData = [
            'tracking_number' => 'UPDATED_TRACKING_001',
            'transparent_label' => '更新的透明标签',
            'is_label_changed' => 1,
            'remark' => '更新的备注',
            'remark2' => '更新的备注2'
        ];
        
        // 执行更新
        $result = $this->detailModel->updateEditableFields('FBA_TEST_001', $editableData);
        
        if (!$result) {
            throw new Exception("可编辑字段更新失败");
        }
        
        // 验证更新结果
        $updatedData = $this->detailModel->getByShipmentSnAndRelateId('TEST_FBA_001', 111111);
        
        if ($updatedData['tracking_number'] !== 'UPDATED_TRACKING_001') {
            throw new Exception("跟踪单号更新失败");
        }
        
        if ($updatedData['transparent_label'] !== '更新的透明标签') {
            throw new Exception("透明标签更新失败");
        }
        
        if ($updatedData['is_label_changed'] != 1) {
            throw new Exception("是否换标更新失败");
        }
        
        if ($updatedData['remark'] !== '更新的备注') {
            throw new Exception("备注更新失败");
        }
        
        echo "✅ 可编辑字段更新功能测试通过\n";
    }

    /**
     * 测试数据统计功能
     */
    public function testDataStatistics()
    {
        echo "测试数据统计功能...\n";
        
        // 先确保有测试数据
        $this->testDataTransformation();
        
        // 测试原始数据统计
        $rawStats = $this->rawModel->getRawDataStats(date('Y-m-d'));
        
        if ($rawStats['total_count'] < 1) {
            throw new Exception("原始数据统计错误");
        }
        
        // 测试明细数据统计
        $detailStats = $this->detailModel->getDetailStats(date('Y-m-d'));
        
        if ($detailStats['total_count'] < 1) {
            throw new Exception("明细数据统计错误");
        }
        
        if ($detailStats['shipment_count'] < 1) {
            throw new Exception("发货单统计错误");
        }
        
        echo "✅ 数据统计功能测试通过\n";
    }

    /**
     * 测试数据验证功能
     */
    public function testDataValidation()
    {
        echo "测试数据验证功能...\n";
        
        // 测试空数据处理
        $emptyApiData = ['list' => []];
        $result = $this->rawModel->batchSaveApiData($emptyApiData);
        
        if ($result !== false) {
            throw new Exception("空数据应该返回false");
        }
        
        // 测试无效数据处理
        $invalidApiData = [
            'list' => [
                [
                    'id' => 888888,
                    'shipment_sn' => '', // 空的发货单号
                    'wname' => '测试仓库'
                ]
            ]
        ];
        
        $result = $this->rawModel->batchSaveApiData($invalidApiData);
        // 应该能处理，但不会保存无效记录
        
        echo "✅ 数据验证功能测试通过\n";
    }

    /**
     * 测试性能
     */
    public function testPerformance()
    {
        echo "测试性能...\n";
        
        $startTime = microtime(true);
        
        // 模拟大量数据处理
        $largeApiData = [
            'list' => []
        ];
        
        // 生成50个测试货件
        for ($i = 1; $i <= 50; $i++) {
            $largeApiData['list'][] = [
                'id' => 800000 + $i,
                'shipment_sn' => "PERF_TEST_" . str_pad($i, 3, '0', STR_PAD_LEFT),
                'status' => 1,
                'wname' => "性能测试仓库{$i}",
                'wid' => 8000 + $i,
                'create_time' => '2024-12-01',
                'method_name' => '性能测试运输方式',
                'relate_list' => [
                    [
                        'id' => 200000 + $i,
                        'shipment_id' => "PERF_FBA_{$i}",
                        'sku' => "PERF_SKU_{$i}",
                        'product_name' => "性能测试产品{$i}",
                        'num' => rand(10, 100),
                        'sname' => "PERF_SHOP_{$i}:性能测试店铺",
                        'nation' => '美国'
                    ]
                ],
                'logistics' => []
            ];
        }
        
        // 测试批量保存性能
        $saveResult = $this->rawModel->batchSaveApiData($largeApiData);
        
        $saveTime = microtime(true);
        $saveDuration = $saveTime - $startTime;
        
        if (!$saveResult) {
            throw new Exception("性能测试批量保存失败");
        }
        
        // 测试批量转换性能
        $rawDataList = $this->rawModel->getUnprocessedData(0, 100, date('Y-m-d'));
        $transformResult = $this->detailModel->syncFromRawData($rawDataList);
        
        $endTime = microtime(true);
        $totalDuration = $endTime - $startTime;
        $transformDuration = $endTime - $saveTime;
        
        echo "性能测试结果:\n";
        echo "- 保存50个货件耗时: " . round($saveDuration, 3) . "秒\n";
        echo "- 转换数据耗时: " . round($transformDuration, 3) . "秒\n";
        echo "- 总耗时: " . round($totalDuration, 3) . "秒\n";
        
        // 性能要求：处理50个货件应该在10秒内完成
        if ($totalDuration > 10) {
            echo "⚠️ 性能警告：处理时间超过10秒\n";
        } else {
            echo "✅ 性能测试通过\n";
        }
    }

    /**
     * 清理测试数据
     */
    private function cleanupTestData()
    {
        try {
            // 清理测试数据
            echo "清理测试数据...\n";
        } catch (Exception $e) {
            echo "清理测试数据失败: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 运行所有测试
     */
    public static function runAllTests()
    {
        echo "\n=== FBA货件明细功能测试开始 ===\n";
        
        $test = new self();
        $test->setUp();
        
        try {
            $test->testRawDataSave();
            $test->testDataTransformation();
            $test->testListQuery();
            $test->testEditableFieldsUpdate();
            $test->testDataStatistics();
            $test->testDataValidation();
            $test->testPerformance();
            
            echo "\n✅ 所有测试通过！\n";
        } catch (Exception $e) {
            echo "\n❌ 测试失败: " . $e->getMessage() . "\n";
            echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
        } finally {
            $test->cleanupTestData();
        }
        
        echo "=== FBA货件明细功能测试结束 ===\n\n";
    }
}

// 如果直接运行此文件，执行所有测试
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    fbaInboundShipmentTest::runAllTests();
}
