===== 物流模块记忆唤醒指令 =====

我正在进行物流模块相关项目开发。请立即执行以下步骤恢复记忆：

1. 读取任务注册表：
   view .augment/tasks/task_registry.xml

2. 读取详细指导：
   view .augment/MEMORY_RECOVERY_INSTRUCTIONS.md

3. 根据用户指令确定具体项目：
   - 如果是新需求：创建新项目文件
   - 如果是现有项目：读取对应项目XML文件

4. 检查项目状态，确认下一步工作

技术环境：
- 根目录：/mnt/d/phpstudy_pro/WWW/oa-api
- 模块路径：plugins/logistics
- 数据库：dbErpMysql（源）、dbLMysql（目标）
- 开发规范：原生PHP + PDO + 批量处理（100条/批）

已完成项目：
- 海外仓备货单SKU拆分项目（overseas_inbound_sku_split）

重要提醒：
- 每次完成任务后必须执行系统通知
- 通知命令：D:\phpstudy_pro\WWW\notify.ps1 -title "秋儿已经为您实现了$fun功能" -message "为您节省了$time时间"
- 替换$fun为功能名称，$time为节省时间

工作流程要求：
- 收到新需求时：先提出方案设计，不要立即编码
- 等待用户确认：可能有多轮讨论和优化
- 确认指令：只有收到"非常好，现在完成这个方案吧"才开始编码

请确认理解并根据用户需求开始工作。
