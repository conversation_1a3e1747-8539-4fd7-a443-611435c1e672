<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/11 10:08
 */

namespace plugins\assessment\models;

use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;

class authListModel
{
    public static array $base_auth = [];
    /**[
    'key'=>'base_info',
    'name'=>'基础信息',
    'child'=>[
    ]
    ]**/
    private static array $default_auth = [];
    //获取系统权限
    public static function getAuth() {
        $db = dbFMysql::getInstance();
        $all_auth = $db->table('column')
            ->where('where is_delete = 0 and table_index > 0')
            ->field('data_type,key_name,column_name')
            ->list();
        $res_auth = self::getAuthList($all_auth);
        return $res_auth;
    }
    private static function getAuthList($list) {
        $new_list = [];
        $column_type = config::get('column_type','data_financial');
        foreach ($column_type as $v) {
            $item = $v;
            $item['key'] = 'type_'.$v['id'];
            $item['child'] = [];
            foreach ($list as $v1) {
                if ($v1['data_type'] ==  $v['id']) {
                    $item['child'][] = [
                        'key'=>$v1['key_name'],
                        'name'=>$v1['column_name'],
                    ];
                }
            }
            unset($item['id']);
            $new_list[] = $item;
        }
        $res_auth = self::$default_auth;
        foreach ($res_auth as &$v) {
            $v['child'][] = [
                'key'=>'base_info',
                'name'=>'基础信息',
                'child'=>self::$base_auth
            ];
            if ($v['name'] != '预警') {
                $v['child'] = array_merge($v['child'],$new_list);
            }
        }
        return $res_auth;
    }
}