<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/23 10:23
 */

namespace  plugins\goods\form;

use plugins\goods\models\userModel;
use core\lib\config;
use core\lib\db\dbMysql;
use core\lib\log;
use setasign\Fpdi\Fpdi;

class goodsHardwareTestFrom
{
    //产品硬件功能测试
    public static function saveHardwareTest($param) {
        $project_id = (int)$param['project_id'];
        //保存验证
        $data = goodsProjectFrom::verifyEventSave($project_id,$param['node_index'],$param['event_index'],4);
        $param = self::getSaveHardwareTestParam($param);
        $project = $data['project'];
        $db = dbMysql::getInstance();
        $test_data = $db->table('goods_hardware_test')
            ->where('where project_id=:project_id and qwuser_id=:qwuser_id and node_index=:node_index',['project_id'=>$project_id,'qwuser_id'=>userModel::$qwuser_id,'node_index'=>$param['node_index']])
            ->one();
        if ($test_data) {
            $db->table('goods_hardware_test')
                ->where('where id = '.$test_data['id'])
                ->update([
                    'updated_at'=>date('Y-m-d H:i:s'),
                    'test_num'=>(int)$param['test_num'],
                    'constituent_part'=>$param['constituent_part'],
                    'from_data'=>$param['from_data'],
                    'abnormal_data'=>$param['abnormal_data'],
                    'conclusion'=>$param['conclusion'],
                    'goods_type'=>$param['goods_type'],
                    'prototype'=>$param['prototype'],
                ]);
        } else {
            $db->table('goods_hardware_test')
                ->insert([
                    'qwuser_id'=>userModel::$qwuser_id,
                    'wname'=>userModel::$wname,
                    'goods_id'=>$project['goods_id'],
                    'project_id'=>$project_id,
                    'created_at'=>date('Y-m-d H:i:s'),
                    'updated_at'=>date('Y-m-d H:i:s'),
                    'test_num'=>(int)$param['test_num'],
                    'constituent_part'=>$param['constituent_part'],
                    'from_data'=>$param['from_data'],
                    'abnormal_data'=>$param['abnormal_data'],
                    'conclusion'=>$param['conclusion'],
                    'goods_type'=>$param['goods_type'],
                    'prototype'=>$param['prototype'],
                    'node_index'=>$param['node_index'],
                ]);
        }
        //未通过数据保存
        if (!empty($param['no_list'])) {
            $no_list = json_decode($param['no_list'],true);
            self::saveHardwareNoList($project_id,$no_list,$param['node_index']);
        }
        //修改日志
        self::setHardwareTestLog($test_data,$param,$project);
    }
    //为通过数据保存
    public static function saveHardwareNoList($project_id,$no_list,$node_index){
        $db = dbMysql::getInstance();
        if (count($no_list)) {
            $old_no_list = $db->table('goods_hardware_no')
                ->where('where project_id=:project_id and qwuser_id=:qwuser_id and is_delete=0 and node_index=:node_index',['project_id'=>$project_id,'qwuser_id'=>userModel::$qwuser_id,'node_index'=>$node_index])
                ->list();
            //修改和删除
            foreach ($old_no_list as $v) {
                $need_del = 1;
                foreach ($no_list as $k1=>$v1) {
                    if ($v['model_id'] == $v1['id'] && $v['type'] == $v1['type']) {
                        $need_del = 0;
                        //修改
                        $db->table('goods_hardware_no')
                            ->where('where id=:id',['id'=>$v['id']])
                            ->update([
                                'qwuser_id'=>userModel::$qwuser_id,
                                'description'=>$v1['description'],
                                'reason'=>$v1['reason'],
                                'suggestion'=>$v1['suggestion'],
                                'updated_time'=>date('Y-m-d H:i:s'),
                            ]);
                        unset($no_list[$k1]);
                        break;
                    }
                }
                if ($need_del) {
                    $db->table('goods_hardware_no')
                        ->where('where id ='.$v['id'])
                        ->update([
                            'is_delete'=>1
                        ]);
                }
            }
            //新增
            if (count($no_list)) {
                foreach ($no_list as $v1) {
                    $db->table('goods_hardware_no')
                        ->insert([
                            'model_id'=>$v1['id'],
                            'qwuser_id'=>userModel::$qwuser_id,
                            'project_id'=>$project_id,
                            'row_name'=>$v1['row_name'],
                            'description'=>$v1['description'],
                            'reason'=>$v1['reason'],
                            'suggestion'=>$v1['suggestion'],
                            'created_time'=>date('Y-m-d H:i:s'),
                            'type'=>$v1['type'],
                            'node_index'=>$node_index,
                        ]);
                }
            }
        } else {
            $db->table('goods_hardware_no')
                ->where('where project_id=:project_id and qwuser_id=:qwuser_id and is_delete=0 and node_index=:node_index',['project_id'=>$project_id,'qwuser_id'=>userModel::$qwuser_id,'node_index'=>$node_index])
                ->update([
                    'is_delete'=>1
                ]);
        }
    }

    public static function setHardwareTestLog($old_data, $update_data,$project)
    {
        $types = ['未检测','通过','不通过'];
        $need_log = 0;
        if (!$old_data) {
            $need_log = 1;
            //新增，项表单
            $new_from = json_decode($update_data['from_data'],true);
            $describe = '硬件功能测试数据新增。';
            foreach ($new_from as $v) {
                $describe .= $v['row_name'].'：【'.$types[$v['is_pass']] .'】;';
            }
            //异常
            if (!empty($update_data['abnormal_data'])) {
                $new_abnormal = json_decode($update_data['abnormal_data'],true);
                foreach ($new_abnormal as $v) {
                    $describe .= $v['txt'].'：【'.$types[$v['is_pass']] .'】；';
                }
            }
        } else {
            //修改
            $describe = '硬件功能测试数据修改。';
            //数量+组成
            if ($old_data['test_num'] != $update_data['test_num']) {
                $describe .= '测试数量：【'.$old_data['test_num'].'->'.$update_data['test_num'].'】;';
            }
            if ($old_data['constituent_part'] != $update_data['constituent_part']) {
                $describe .= '组成部分：【'.$old_data['constituent_part'] .'->'.$update_data['constituent_part'].'】；';
            }
            //基本项
            $old_from = json_decode($old_data['from_data'],true);
            $new_from = json_decode($update_data['from_data'],true);
            foreach ($new_from as $v) {
                $is_have = 0;
                foreach ($old_from as $v1) {
                    if ($v['row_name'] == $v1['row_name']) {
                        $is_have = 1;
                        if ($v['is_pass'] != $v1['is_pass']) {
                            $need_log = 1;
                            $describe .= $v1['row_name'].'：【'.$types[$v1['is_pass']] .'->'.$types[$v['is_pass']].'】；';
                        }
                        break;
                    }
                }
                if (!$is_have) {
                    $describe .= $v['row_name'].'：【'.$types[$v['is_pass']] .'】；';
                }
            }
            //异常
            if (!empty($update_data['abnormal_data'])) {
                $old_abnormal = json_decode($old_data['abnormal_data'],true);
                $new_abnormal = json_decode($update_data['abnormal_data'],true);
                foreach ($old_abnormal as $w1) {
                    foreach ($new_abnormal as $w2) {
                        if ($w1['id'] == $w2['id']) {
                            if ($w1['is_pass'] != $w2['is_pass']) {
                                $need_log = 1;
                                $describe .= $w1['txt'].'：【'.$types[$w1['is_pass']] .'->'.$types[$w2['is_pass']].'】；';
                            }
                        }
                    }

                }
            }
            $describe = trim($describe,'；');
        }
        if ($need_log) {
            log::setGoodsProjectLog($project['goods_id'],$project['id'], $describe, $project['matter_name'],4);
        }

    }

    public static function varifyHardwareTestSubmit($project,$current_event,$pfd_html,$node_index,$event_index){
        //可直接提交就跳过该验证
        $project_id = $project['id'];
        $flow_path_id = $project['flow_path_id'];
        $is_advance_submit = $current_event['is_advance_submit'];
        if (empty($pfd_html)) {
            SetReturn(-1,'缺少文件html');
        }
        $db = dbMysql::getInstance();
        $hard_test = $db->table('goods_hardware_test')
            ->where('where qwuser_id=:qwuser_id and project_id=:project_id and node_index=:node_index',['qwuser_id'=>userModel::$qwuser_id,'project_id'=>$project_id,'node_index'=>$node_index])
            ->one();
        if ($hard_test['is_submit'] == 1) {
            SetReturn(-1,'已提交，切勿重复操作');
        }
        $all_test = 0;
        if (!$hard_test) {
            SetReturn(-1,'请先保存在提交');
        } else {
            $from_data = json_decode($hard_test['from_data'],true);
            $all_test = 1;
            foreach ($from_data as $v) {
                if ($v['is_pass'] == 0) {
                    $all_test = 0;
                    if (!$is_advance_submit) {
                        SetReturn(-1,"【{$v['row_name']}】还没测试结果");
                    }
                }
            }
            if ($hard_test['abnormal_data']) {
                $abnormal_data = json_decode($hard_test['abnormal_data'],true);
                foreach ($abnormal_data as $v) {
                    if ($v['is_pass'] == 0) {
                        $all_test = 0;
                        if (!$is_advance_submit) {
                            SetReturn(-1,"【{$v['txt']}】问题还没测试结果");
                        }
                    }
                }
            }
            if ($all_test == 1) {
                //pdf保存
                self::setPdf($pfd_html,$project_id,$hard_test['goods_id'],$flow_path_id,$node_index);
                //修改提交状态
                $db->table('goods_hardware_test')
                    ->where('where id=:id',['id'=>$hard_test['id']])
                    ->update(['submit_time'=>date('Y-m-d H:i:s'),'is_submit'=>1,'is_filished'=>1]);
            } else {
                $db->table('goods_hardware_test')
                    ->where('where id=:id',['id'=>$hard_test['id']])
                    ->update(['submit_time'=>date('Y-m-d H:i:s'),'is_submit'=>1,'is_filished'=>0]);
            }
        }
        //项目试完成了，验证是否每个人都有提交
        $ids = array_column(json_decode($current_event['manage_info'],true),'id');
        $test_count = $db->table('goods_hardware_test')
            ->where('where project_id=:project_id and is_submit=1 and node_index=:node_index',['project_id'=>$project_id,'node_index'=>$node_index])
            ->whereIn('qwuser_id',$ids)
            ->count();
        //设置待办事件状态
        goodsMattersFrom::setProjectMatterStatus1(0,$project_id,$node_index,$event_index,$all_test,$current_event['delay_hour']);
        if ($test_count != count($ids)) {
            $db->commit();
            SetReturn(0,'提交成功，还有其他人未提交');
        }
    }

    public static function setPdf(string $html, int $project_id,int $goods_id, int $flow_path_id,string $nodex_index) {
        //文件保存路径
        $save_path = "/public/upload/hardware_test_pdf/{$goods_id}-{$project_id}/$nodex_index/".userModel::$wid;
        //文件名称
        $file_name = config::getDataName('flow_path',$flow_path_id).'硬件检测报告_'.userModel::$wname.'.pdf';
        $url = SELF_FK.$save_path;
        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path.'/'.$file_name;
        $url = SELF_FK.$path;
        //生成pfd
        $mpdf = new \Mpdf\Mpdf();
        $mpdf->autoLangToFont = true;
        $mpdf->autoScriptToLang = true;
        $mpdf->useSubstitutions = true;
        // 添加水印图片
        $mpdf->showImageErrors = true;//图片错误就显示
        $mpdf->WriteHTML($html);
        // I生成，F保存到本地
        $mpdf->Output($url, 'F');
        //logo写入
        $pdf = new Fpdi();
        $numPages = $pdf->setSourceFile($url);
        // 遍历每一页
        $logo_img = SELF_FK.'/public/static/logo.png';
        for ($pageNo = 1; $pageNo <= $numPages; $pageNo++) {
            // 添加一个新页面，大小和原始页面一致
            $pdf->AddPage();
            // 导入当前页
            $tplIdx = $pdf->importPage($pageNo);
            // 将导入的页面添加到当前文档中
            $pdf->useTemplate($tplIdx, 0, 0);
            if ($pageNo == 1) {
                $pdf->Image($logo_img,10,10,20,20);
            }
        }
        $pdf->Output('F',$url);
        //保存文件
        goodsProjectFileFrom::saveProjectFile($file_name,$flow_path_id,$project_id,4,$path,$goods_id,$nodex_index);
        return ['file_name'=>$file_name,'path'=>$path];
    }
    private static function getSaveHardwareTestParam($param){
        $no_pass_num = 0;
        if (!empty($param['from_data'])) {
            $from_data = json_decode($param['from_data'],true);
            foreach ($from_data as &$v) {
                $v['is_pass'] = isset($v['is_pass'])?(string)$v['is_pass']:0;
                if ($v['is_pass'] == 2) {
                    $no_pass_num++;
                }
            }
            $param['from_data'] = json_encode($from_data);
        }
        if (!empty($param['abnormal_data'])) {
            $abnormal_data = json_decode($param['abnormal_data'],true);
            foreach ($abnormal_data as &$v) {
                $v['is_pass'] = isset($v['is_pass'])?(string)$v['is_pass']:0;
                if ($v['is_pass'] == 2) {
                    $no_pass_num++;
                }
            }
            $param['abnormal_data'] = json_encode($abnormal_data);
        }
        $no_list_num = 0;
        if (!empty($param['no_list'])) {
            $no_list = json_decode($param['no_list'],true);
            $no_list_num = count($no_list);
        }
        if ($no_list_num != $no_pass_num) {
            returnError('异常ng项和不合格项数量不一致');
        }

        return $param;
    }
}