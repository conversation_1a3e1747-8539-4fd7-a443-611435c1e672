<?php
/**
 * @author: zhangguoming
 * @Time: 2024/5/31 9:38
 */

namespace  plugins\goods\form;

use plugins\goods\models\userModel;
use plugins\goods\models\userRolesModel;
use core\lib\config;
use core\lib\db\dbMysql;

class takePictureRequestFrom
{
    public static array $msg_arry = [];
    public static function editRes($param) {
        $id = (int)$param['id'];
        $goods_id = (int)$param['goods_id'];
        $type = (int)$param['type'];
        $db = dbMysql::getInstance();
        if ($id) {
            SetReturn(-1,'暂时不可修改。');
            $request = $db->table('take_picture_request')
                ->where('where id=:id',['id'=>$id])
                ->one();
            if ($request['status'] == 2) {
                SetReturn(-1,'该拍摄已完成，不可修改。');
            }
            if (!$request) {
                SetReturn(-1,'未找到该拍摄需求');
            }
            $db->table('take_picture_request')
                ->where('where id=:id',['id'=>$id])
                ->update([
                    'expected_time'=>strtotime($param['expected_time']),
                    'description'=>$param['description'],
                    'updated_time'=>date('Y-m-d H:i:s'),
                ]);
            $request_id = $id;
        } else {
            $goods_info = $db->table('goods_new')
                ->where('where id=:goods_id',['goods_id'=>$goods_id])
                ->field('id,goods_name')
                ->one();
            $request_name = $goods_info['goods_name'].'_'.config::getDataName('take_picture_type',(int)$param['type']);
            $request_data = $db->table('take_picture_request')
                ->where('where goods_id=:goods_id and type=:type',['goods_id'=>$goods_id,'type'=>$type])
                ->one();
            if ($request_data) {
                SetReturn(-1,'【'.$request_name.'】的需求已经存在，不可重复申请');
            }
            if (!$goods_info) {
                SetReturn(-1,'未找到对应产品');
            }
            //查询拍摄人
            $take_pic_user = configFrom::getConfigByName('take_pic_user');
            if (!$take_pic_user) {
                SetReturn(-1,'图片拍摄负责人尚未配置，请联系系统管理员配置');
            }
            $take_pic_user = json_decode($take_pic_user,true);
            $request = [
                'goods_id'=>(int)$param['goods_id'],
                'user_id'=>userModel::$qwuser_id,
                'qwuser_id'=>$take_pic_user[0]['id'],
                'type'=>(int)$param['type'],
                'request_name'=>$request_name,
                'created_time'=>date('Y-m-d H:i:s'),
                'begin_time'=>time(),
                'expected_time'=>strtotime($param['expected_time']),
                'description'=>$param['description'],
            ];
            $request_id = $db->table('take_picture_request')
                ->insert($request);
            //修改需需求的待办事件预计完成时间
            if ($type == 1) {
                imgsRequestFrom::changeExpectTime($goods_id);
            }

            //新增待办
            goodsMattersFrom::addCreateMatter($request['request_name'],$request['goods_id'],'图片拍摄',0,7,$take_pic_user[0]['id'],$request_id,0);
            self::$msg_arry[] = [
                'wid'=>[$take_pic_user[0]['wid']],
                'msg'=>messagesFrom::getMsgTxtForImgRequest(11,$goods_info['goods_name'],$request['request_name']),
                'other_data' => [
                    'user_id'=>userModel::$qwuser_id,
                    'model_id'=>$request_id,
                    'msg_type'=>5
                ],
            ];
        }
        return $request_id;
    }

    public static function sendQwMsg() {
        $ms_array = self::$msg_arry;
        if (count($ms_array)) {
            foreach ($ms_array as $msg) {
                messagesFrom::senMeg($msg['wid'],$msg['msg'],$msg['other_data']);
            }
        }
    }

}