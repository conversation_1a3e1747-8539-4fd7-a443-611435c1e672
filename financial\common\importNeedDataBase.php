<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/27 9:22
 */


//数据导入时，获取一些基础数据
namespace financial\common;

use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;

class importNeedDataBase
{
    //根据名字获取洲信息(及洲下的国家信息)
    public static function getContinentsByName(array $continents_array){
        $continents_list = config::get('continents','data_financial');
        $continents_ = [];
        foreach ($continents_list as $v) {
            if (in_array($v['name'],$continents_array)) {
                $continents_[$v['name']] = $v;
            }
        }
        if (count($continents_)) {
            $db = dbFMysql::getInstance();
            $country_list_ = $db->table('market')
                ->where('where is_delete = 0')
                ->whereIn('ct_id',array_column($continents_,'id'))
                ->list();
            foreach ($continents_ as $k_=>$v_ct_) {
                $v_ct_['country_code'] = [];
                foreach ($country_list_ as $v_cl_) {
                    if ($v_ct_['id'] == $v_cl_['ct_id']) {
                        $v_ct_['country_code'][] = $v_cl_['code'];
                    }
                }
                $continents_[$k_]['country_code'] = $v_ct_['country_code'];
            }
        }
        return $continents_;
    }
    //根据名字获取国家信息
    public static function getCountryListByName(array $country_name){
        $db = dbFMysql::getInstance();
        $country_list = $db->table('market')
            ->where('where is_delete = 0')
            ->whereIn('country',$country_name)
            ->field('country,code,ct_id')
            ->list();
        $country_ = [];
        if (count($country_list)) {
            foreach ($country_list as $v) {
                $country_[$v['country']] = $v;
            }
        }
        return $country_;
    }
    //根据姓名获取用户信息
    public static function  getQwuserByName(array $name_array) {
        $db_ = dbMysql::getInstance();
        $qwuser_list = $db_->table('qwuser')->field('wname,id')
            ->where('where is_delete=0')
            ->whereIn('wname',$name_array)
            ->list();
        $qwuser_ = [];
        if (count($qwuser_list)) {
            foreach ($qwuser_list as $v) {
                $qwuser_[$v['id']] = $v;
            }
        }
        return $qwuser_;
    }
    //获取项目通过项目名称（第三级）
    public static function getProjectByName(array $project_name,array $qwuser_) {
        $db = dbFMysql::getInstance();
        $project_list = $db->table('project')->field('project_name,id,user_ids')
            ->where('where level=3')
            ->whereIn('project_name',$project_name)
            ->list();
        $project_ = [];
        if (count($project_list)) {
            foreach ($project_list as $v) {
                if (!empty($v['user_ids'])) {
                    $v['user_ids'] = trim($v['user_ids'],',');
                    $v['user_ids'] = explode(',',$v['user_ids']);
                } else {
                    $v['user_ids'] = [];
                }
                $project_yunying = [];
                foreach ($v['user_ids'] as $id) {
                    if (isset($qwuser_[$id])) {
                        $project_yunying[$qwuser_[$id]['wname']] = $qwuser_[$id];
                    }
                }
                $v['yunying_list'] = $project_yunying;
                $project_[$v['project_name']] = $v;
            }
        }
        return $project_;
    }
    //同步项目一级名称获取项目下的第三级id集合
    public static function getProject3ByName1(array $project_name) {
        function getList($list, $pid){
            $res_data = [];
            if (count($list)) {
                foreach ($list as $k=>$v_) {
                    if ($v_['pid'] == $pid) {
                        $res_data[$k] = $v_;
                        unset($list[$k]);
                        $res_data[$k]['child'] = getList($list,$v_['id']);
                    }
                }
            }
            return $res_data;
        };
        $db = dbFMysql::getInstance();
        $project_1 = $db->table('project')
            ->where('where level=1')
            ->whereIn('project_name',$project_name)
            ->field('id,p_id as pid,project_name')
            ->list();
        $project_ = [];
        if (count($project_1)) {
            $project_2 = $db->table('project')
                ->whereIn('p_id',array_column($project_1,'id'))
                ->field('id,p_id as pid,project_name')
                ->list();
            $project_3 = [];
            if (count($project_2)) {
                $project_3 = $db->table('project')
                    ->whereIn('p_id',array_column($project_2,'id'))
                    ->field('id,p_id as pid,user_ids,project_name')
                    ->list();
            }
            $project_list = array_merge($project_1,$project_2,$project_3);
            $project_ = getList($project_list,0);
        }
        $res = [];
        foreach ($project_ as $v) {
            $res[$v['project_name']] = $v;
        }
        return $res;
    }
    //根据币种编号获取币种信息
    public static function getRoutingByCode(array $code,string $date,int $get_all = 0) {
        $db = dbFMysql::getInstance();
        $db->table('routing')->field('code,icon,my_rate')
            ->where('where date=:date',['date'=>$date]);
        if (!$get_all) {
            $db->whereIn('code',$code);
        }
        $routing_list =  $db->list();
        $routing_ = [];
        if (count($routing_list)) {
            foreach ($routing_list as $v) {
                $routing_[$v['code']] = $v;
            }
        }
       return $routing_;
    }
    //根据sid获取店铺信息
    public static function getSellerBySid(array $sid_array) {
        $db = dbFMysql::getInstance();
        $seller_list = $db->table('seller')->field('sid,real_name')
            ->where('where is_delete = 0')
            ->whereIn('sid',$sid_array)->list();
        $seller_ = [];
        if (count($seller_list)) {
            foreach ($seller_list as $v) {
                $seller_[$v['sid']] = $v;
            }
        }
        return $seller_;
    }
    //根据名称获取店铺信息
    public static function getSellerByName(array $seller_name) {
        $db = dbFMysql::getInstance();
        $seller_list = $db->table('seller')->field('sid,real_name')
            ->where('where is_delete = 0')
            ->whereIn('real_name',$seller_name)->list();
        $seller_ = [];
        if (count($seller_list)) {
            foreach ($seller_list as $v) {
                $seller_[$v['real_name']] = $v;
            }
        }
        return $seller_;
    }
    //根据列名称获取列数据
    public static function getColumnByName(array $column_array,array $table_index) {
        $db = dbFMysql::getInstance();
        $column = $db->table('column')
            ->where('where is_delete = 0')
            ->whereIn('column_name', $column_array)
            ->whereIn('table_index', $table_index)
            ->list();
        $column_ = [];
        if (count($column)) {
            foreach ($column as $col) {
                $column_[$col['column_name']] = $col;
            }
        }
        return $column_;
    }
    //供应商获取
    //更加列名称获取列数据
    public static function getSupplierList() {
        $db = dbFMysql::getInstance();
        $column = $db->table('supplier')
            ->field('id,supplier_name')
            ->list();
        return $column;
    }

    //获取所有项目
    public static function getProjectAll() {
        $db = dbFMysql::getInstance();
        $project_list = $db->table('project')
            ->where('where is_delete = 0')
            ->field('id,user_id,p_id,level,project_name,user_ids')
            ->list();
        $project_1 = array_filter($project_list,function ($row){
            return $row['level'] = 1;
        });
        $project_2 = array_filter($project_list,function ($row){
            return $row['level'] = 2;
        });
        $project_3 = array_filter($project_list,function ($row){
            return $row['level'] = 3;
        });
        $project_ = [];
        foreach ($project_1 as $k1=>$v1) {
            foreach ($project_2 as $k2=>$v2) {
                if ($v1['id'] == $v2['p_id']) {
                    foreach ($project_3 as $k3=>$v3) {
                        if ($v2['id'] == $v3['p_id']) {
                            $project_name = implode('/',[$v1['project_name'],$v2['project_name'],$v3['project_name']]);
                            $project_[$project_name] = [
                                'id'=>$v3['id'],
                                'user_id'=>$v3['user_id'],
                                'name'=>$project_name,
                                'user_ids'=>empty($v3['user_ids'])?[]:explode(',',trim($v3['user_ids'],','))
                            ];
                            unset($project_3[$k3]);
                        }
                    }
                    unset($project_2[$k2]);
                }
            }
        }
        return $project_;
    }
    //根据姓名获取用户信息,并已姓名为键返回数据
    public static function qwuserByNameList(array $name_array) {
        $db_ = dbMysql::getInstance();
        $qwuser_list = $db_->table('qwuser')->field('wname,id')
            ->where('where is_delete=0')
            ->whereIn('wname',$name_array)
            ->list();
        $qwuser_ = [];
        if (count($qwuser_list)) {
            foreach ($qwuser_list as $v) {
                $qwuser_[$v['wname']] = $v['id'];
            }
        }
        return $qwuser_;
    }







}