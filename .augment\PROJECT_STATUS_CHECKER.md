# 项目状态检查工具

## 快速状态检查命令

### 查看所有项目状态
```bash
# 读取任务注册表
view .augment/tasks/task_registry.xml

# 查看活跃项目列表
grep -A 10 "<active_projects>" .augment/tasks/task_registry.xml
```

### 查看特定项目详情
```bash
# 查看海外仓备货单SKU拆分项目
view .augment/tasks/overseas_inbound_sku_split.xml

# 查看新项目（替换project_id）
view .augment/tasks/[project_id].xml
```

## 项目状态说明

### 已完成项目
- **overseas_inbound_sku_split**: 海外仓备货单SKU拆分项目
  - 状态: completed
  - 完成日期: 2025-07-01
  - 核心功能: SKU拆分、导入导出、自动化处理

### 活跃项目
- 查看 task_registry.xml 中的 active_projects 部分

## 新项目创建检查清单

### 创建前检查
- [ ] 确认需求不与现有项目重复
- [ ] 分析对现有系统的影响
- [ ] 确定技术方案的可行性
- [ ] 评估开发工作量和优先级

### 创建步骤
1. **分析需求**: 理解用户的具体需求
2. **设计方案**: 制定技术实现方案
3. **创建项目文件**: 使用模板创建项目XML
4. **更新注册表**: 添加到活跃项目列表
5. **制定计划**: 详细的实施计划

### 创建后验证
- [ ] 项目文件格式正确
- [ ] 注册表已更新
- [ ] 实施计划完整
- [ ] 技术方案可行

## 项目维护检查清单

### 定期检查项目
- [ ] 检查项目进度
- [ ] 更新任务状态
- [ ] 记录重要决策
- [ ] 更新技术文档

### 项目完成检查
- [ ] 所有任务已完成
- [ ] 测试验证通过
- [ ] 部署文档完整
- [ ] 更新项目状态为completed
- [ ] 执行系统通知

## 常用检查命令

### 检查文件完整性
```bash
# 检查模型文件
ls -la plugins/logistics/models/

# 检查控制器文件
ls -la task/controller/logisticsController.php

# 检查测试文件
ls -la plugins/logistics/tests/

# 检查SQL文件
ls -la plugins/logistics/sql/
```

### 检查数据库表
```sql
-- 检查海外仓备货单明细表
DESC overseas_inbound_detail;

-- 检查数据量
SELECT COUNT(*) FROM overseas_inbound_detail;

-- 检查最新数据
SELECT * FROM overseas_inbound_detail ORDER BY created_at DESC LIMIT 5;
```

### 检查定时任务
```bash
# 检查Shell脚本
cat task/shell/lingxing_overseas_inbound.sh

# 检查定时任务配置
crontab -l | grep overseas
```

## 问题排查指南

### 项目文件问题
- **文件不存在**: 检查文件路径和权限
- **XML格式错误**: 验证XML语法
- **内容不完整**: 对比模板检查必要字段

### 功能问题
- **接口调用失败**: 检查控制器方法和路由
- **数据处理错误**: 检查模型逻辑和数据库连接
- **测试失败**: 检查测试用例和环境配置

### 性能问题
- **处理速度慢**: 检查批量处理大小和数据库索引
- **内存使用高**: 检查数据处理逻辑和内存释放
- **数据库压力大**: 检查SQL查询效率和并发控制

## 项目协调要点

### 多项目开发
- 确保项目间不冲突
- 合理安排开发优先级
- 共享通用组件和工具

### 代码复用
- 复用现有模型和工具类
- 保持代码风格一致
- 避免重复实现相同功能

### 数据库管理
- 避免表结构冲突
- 合理设计索引策略
- 保持数据一致性

## 系统通知检查

### 通知功能测试
```powershell
# 测试系统通知
D:\phpstudy_pro\WWW\notify.ps1 -title "秋儿已经为您实现了系统通知测试功能" -message "为您节省了调试时间"
```

### 通知执行检查
- [ ] 任务完成后是否执行了通知
- [ ] 通知内容是否准确描述了完成的工作
- [ ] 时间估算是否合理
- [ ] 通知命令是否执行成功

### 常见通知模板
```powershell
# 功能开发
D:\phpstudy_pro\WWW\notify.ps1 -title "秋儿已经为您实现了[功能名称]" -message "为您节省了[时间]"

# 系统优化
D:\phpstudy_pro\WWW\notify.ps1 -title "秋儿已经为您优化了[优化内容]" -message "为您节省了[时间]"

# 问题修复
D:\phpstudy_pro\WWW\notify.ps1 -title "秋儿已经为您修复了[问题描述]" -message "为您节省了[时间]"
```

记住：多项目管理需要全局视角，既要保证单个项目的质量，也要确保整体系统的稳定性和可维护性。完成任务后务必执行系统通知。
