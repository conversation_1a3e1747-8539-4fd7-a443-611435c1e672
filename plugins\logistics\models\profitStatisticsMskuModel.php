<?php

namespace plugins\logistics\models;

use core\lib\db\dbErpMysql;
use core\lib\log;

/**
 * 利润统计MSKU数据模型
 * 处理利润统计数据的保存、查询和更新操作
 */
class profitStatisticsMskuModel
{
    private $db;
    private $table = 'lingxing_profit_statistics_msku_2025';

    public function __construct()
    {
        $this->db = dbErpMysql::getInstance();
    }

    /**
     * 批量保存利润统计数据
     * @param array $dataList 数据列表
     * @param string $dataDate 数据日期
     * @return array 保存结果
     */
    public function saveProfitData($dataList, $dataDate)
    {
        $successCount = 0;
        $errorList = [];
        
        if (empty($dataList)) {
            return ['success_count' => 0, 'error_list' => []];
        }

        $this->db->beginTransaction();
        
        try {
            foreach ($dataList as $index => $data) {
                try {
                    // 数据验证
                    $validationResult = $this->validateData($data);
                    if (!$validationResult['valid']) {
                        $errorList[] = [
                            'index' => $index,
                            'data' => $data,
                            'error' => $validationResult['error']
                        ];
                        continue;
                    }

                    // 准备保存数据
                    $saveData = $this->prepareSaveData($data, $dataDate);
                    
                    // 执行upsert操作
                    $result = $this->upsertData($saveData);
                    
                    if ($result) {
                        $successCount++;
                    } else {
                        $errorList[] = [
                            'index' => $index,
                            'data' => $data,
                            'error' => '数据保存失败'
                        ];
                    }
                    
                } catch (\Exception $e) {
                    $errorList[] = [
                        'index' => $index,
                        'data' => $data,
                        'error' => $e->getMessage()
                    ];
                    log::lingXingApi('ProfitMsku')->error('保存数据异常: ' . $e->getMessage(), $data);
                }
            }
            
            $this->db->commit();
            
        } catch (\Exception $e) {
            $this->db->rollback();
            log::lingXingApi('ProfitMsku')->error('批量保存事务异常: ' . $e->getMessage());
            throw $e;
        }

        return [
            'success_count' => $successCount,
            'error_list' => $errorList
        ];
    }

    /**
     * 执行upsert操作（插入或更新）
     * @param array $data 数据
     * @return bool 操作结果
     */
    private function upsertData($data)
    {
        // 检查是否已存在记录
        $existing = $this->db->table($this->table)
            ->where('dataDate = :dataDate AND msku = :msku AND asin = :asin AND sid = :sid AND countryCode = :countryCode', [
                'dataDate' => $data['dataDate'],
                'msku' => $data['msku'],
                'asin' => $data['asin'],
                'sid' => $data['sid'],
                'countryCode' => $data['countryCode']
            ])
            ->one();

        if ($existing) {
            // 更新现有记录
            return $this->db->table($this->table)
                ->where('id = :id', ['id' => $existing['id']])
                ->update($data);
        } else {
            // 插入新记录
            return $this->db->table($this->table)->insert($data);
        }
    }

    /**
     * 验证数据格式
     * @param array $data 原始数据
     * @return array 验证结果
     */
    private function validateData($data)
    {
        $required = ['msku', 'asin', 'sid', 'countryCode'];
        
        foreach ($required as $field) {
            if (empty($data[$field])) {
                return [
                    'valid' => false,
                    'error' => "必填字段 {$field} 不能为空"
                ];
            }
        }

        return ['valid' => true, 'error' => ''];
    }

    /**
     * 准备保存数据
     * @param array $rawData 原始数据
     * @param string $dataDate 数据日期
     * @return array 处理后的数据
     */
    private function prepareSaveData($rawData, $dataDate)
    {
        return [
            'lingxing_id' => $rawData['id'] ?? 0,
            'dataDate' => $dataDate,
            'smallImageUrl' => $rawData['smallImageUrl'] ?? '',
            'msku' => $rawData['msku'] ?? '',
            'asin' => $rawData['asin'] ?? '',
            'sid' => $rawData['sid'] ?? '',
            'storeName' => $rawData['storeName'] ?? '',
            'countryCode' => $rawData['countryCode'] ?? '',
            'country' => $rawData['country'] ?? '',
            'localName' => $rawData['localName'] ?? '',
            'localSku' => $rawData['localSku'] ?? '',
            'itemName' => $rawData['itemName'] ?? '',
            'principalRealname' => $rawData['principalRealname'] ?? '',
            'listingTagIds' => $rawData['listingTagIds'] ?? '',
            'categoryName' => $rawData['categoryName'] ?? '',
            'brandName' => $rawData['brandName'] ?? '',
            'currencyCode' => $rawData['currencyCode'] ?? '',
            'currencyIcon' => $rawData['currencyIcon'] ?? '',
            
            // 销量数据
            'totalSalesQuantity' => $rawData['totalSalesQuantity'] ?? 0,
            'fbaSalesQuantity' => $rawData['fbaSalesQuantity'] ?? 0,
            'fbmSalesQuantity' => $rawData['fbmSalesQuantity'] ?? 0,
            'totalReshipQuantity' => $rawData['totalReshipQuantity'] ?? 0,
            'reshipFbmProductSalesQuantity' => $rawData['reshipFbmProductSalesQuantity'] ?? 0,
            'reshipFbmProductSaleRefundsQuantity' => $rawData['reshipFbmProductSaleRefundsQuantity'] ?? 0,
            'reshipFbaProductSalesQuantity' => $rawData['reshipFbaProductSalesQuantity'] ?? 0,
            'reshipFbaProductSaleRefundsQuantity' => $rawData['reshipFbaProductSaleRefundsQuantity'] ?? 0,
            'mcFbaFulfillmentFeesQuantity' => $rawData['mcFbaFulfillmentFeesQuantity'] ?? 0,
            
            // 广告数据
            'totalAdsSales' => $rawData['totalAdsSales'] ?? 0.00,
            'adsSdSales' => $rawData['adsSdSales'] ?? 0.00,
            'adsSpSales' => $rawData['adsSpSales'] ?? 0.00,
            'totalAdsSalesQuantity' => $rawData['totalAdsSalesQuantity'] ?? 0,
            'adsSdSalesQuantity' => $rawData['adsSdSalesQuantity'] ?? 0,
            'adsSpSalesQuantity' => $rawData['adsSpSalesQuantity'] ?? 0,
            
            // 销售额数据
            'totalSalesAmount' => $rawData['totalSalesAmount'] ?? 0.00,
            'fbaSaleAmount' => $rawData['fbaSaleAmount'] ?? 0.00,
            'fbmSaleAmount' => $rawData['fbmSaleAmount'] ?? 0.00,
            'totalSalesAmountWithTax' => $rawData['totalSalesAmountWithTax'] ?? 0,
            'shippingCredits' => $rawData['shippingCredits'] ?? 0.00,
            'promotionalRebates' => $rawData['promotionalRebates'] ?? 0.00,
            'fbaInventoryCredit' => $rawData['fbaInventoryCredit'] ?? 0.00,
            'cashOnDelivery' => $rawData['cashOnDelivery'] ?? 0.00,
            'otherInAmount' => $rawData['otherInAmount'] ?? 0.00,
            'giftWrapCredits' => $rawData['giftWrapCredits'] ?? 0.00,
            'guaranteeClaims' => $rawData['guaranteeClaims'] ?? 0.00,
            'costOfPoIntegersGranted' => $rawData['costOfPoIntegersGranted'] ?? 0.00,
            'fbaLiquidationProceeds' => $rawData['fbaLiquidationProceeds'] ?? 0.00,
            'fbaLiquidationProceedsAdjustments' => $rawData['fbaLiquidationProceedsAdjustments'] ?? 0.00,
            'amazonShippingReimbursement' => $rawData['amazonShippingReimbursement'] ?? 0.00,
            'safeTReimbursement' => $rawData['safeTReimbursement'] ?? 0.00,
            'netcoTransaction' => $rawData['netcoTransaction'] ?? 0.00,
            'reimbursements' => $rawData['reimbursements'] ?? 0.00,
            'clawbacks' => $rawData['clawbacks'] ?? 0.00,
            'sharedComminglingVatIncome' => $rawData['sharedComminglingVatIncome'] ?? 0.00,
            'others' => $rawData['others'] ?? 0.00,
            
            // 退款数据
            'totalSalesRefunds' => $rawData['totalSalesRefunds'] ?? 0.00,
            'fbaSalesRefunds' => $rawData['fbaSalesRefunds'] ?? 0.00,
            'fbmSalesRefunds' => $rawData['fbmSalesRefunds'] ?? 0.00,
            'shippingCreditRefunds' => $rawData['shippingCreditRefunds'] ?? 0.00,
            'giftWrapCreditRefunds' => $rawData['giftWrapCreditRefunds'] ?? 0.00,
            'chargebacks' => $rawData['chargebacks'] ?? 0.00,
            'costOfPoIntegersReturned' => $rawData['costOfPoIntegersReturned'] ?? 0.00,
            'promotionalRebateRefunds' => $rawData['promotionalRebateRefunds'] ?? 0.00,

            // 费用退款
            'totalFeeRefunds' => $rawData['totalFeeRefunds'] ?? 0.00,
            'sellingFeeRefunds' => $rawData['sellingFeeRefunds'] ?? 0.00,
            'fbaTransactionFeeRefunds' => $rawData['fbaTransactionFeeRefunds'] ?? 0.00,
            'refundAdministrationFees' => $rawData['refundAdministrationFees'] ?? 0.00,
            'otherTransactionFeeRefunds' => $rawData['otherTransactionFeeRefunds'] ?? 0.00,
            'refundForAdvertiser' => $rawData['refundForAdvertiser'] ?? 0.00,
            'pointsAdjusted' => $rawData['pointsAdjusted'] ?? 0.00,
            'shippingLabelRefunds' => $rawData['shippingLabelRefunds'] ?? 0.00,

            // 退货数据
            'refundsQuantity' => $rawData['refundsQuantity'] ?? 0,
            'refundsRate' => $rawData['refundsRate'] ?? 0.0000,
            'fbaReturnsQuantity' => $rawData['fbaReturnsQuantity'] ?? 0,
            'fbaReturnsSaleableQuantity' => $rawData['fbaReturnsSaleableQuantity'] ?? 0,
            'fbaReturnsUnsaleableQuantity' => $rawData['fbaReturnsUnsaleableQuantity'] ?? 0,

            // 平台费用
            'platformFee' => $rawData['platformFee'] ?? 0.00,
            'fbaDeliveryFee' => $rawData['fbaDeliveryFee'] ?? 0.00,
            'otherTransactionFees' => $rawData['otherTransactionFees'] ?? 0.00,

            // 广告费用
            'totalAdsCost' => $rawData['totalAdsCost'] ?? 0.00,
            'adsSpCost' => $rawData['adsSpCost'] ?? 0.00,
            'adsSbCost' => $rawData['adsSbCost'] ?? 0.00,
            'adsSbvCost' => $rawData['adsSbvCost'] ?? 0.00,
            'adsSdCost' => $rawData['adsSdCost'] ?? 0.00,
            'sharedCostOfAdvertising' => $rawData['sharedCostOfAdvertising'] ?? 0.00,

            // 推广费用
            'promotionFee' => $rawData['promotionFee'] ?? 0.00,
            'sharedSubscriptionFee' => $rawData['sharedSubscriptionFee'] ?? 0.00,
            'sharedLdFee' => $rawData['sharedLdFee'] ?? 0.00,
            'sharedCouponFee' => $rawData['sharedCouponFee'] ?? 0.00,
            'sharedEarlyReviewerProgramFee' => $rawData['sharedEarlyReviewerProgramFee'] ?? 0.00,
            'sharedVineFee' => $rawData['sharedVineFee'] ?? 0.00,

            // 仓储费用
            'totalStorageFee' => $rawData['totalStorageFee'] ?? 0.00,
            'fbaStorageFee' => $rawData['fbaStorageFee'] ?? 0.00,
            'sharedFbaStorageFee' => $rawData['sharedFbaStorageFee'] ?? 0.00,
            'longTermStorageFee' => $rawData['longTermStorageFee'] ?? 0.00,
            'sharedLongTermStorageFee' => $rawData['sharedLongTermStorageFee'] ?? 0.00,
            'sharedStorageRenewalBilling' => $rawData['sharedStorageRenewalBilling'] ?? 0.00,
            'sharedFbaDisposalFee' => $rawData['sharedFbaDisposalFee'] ?? 0.00,
            'sharedFbaRemovalFee' => $rawData['sharedFbaRemovalFee'] ?? 0.00,
            'sharedFbaInboundTransportationProgramFee' => $rawData['sharedFbaInboundTransportationProgramFee'] ?? 0.00,
            'sharedLabelingFee' => $rawData['sharedLabelingFee'] ?? 0.00,
            'sharedPolybaggingFee' => $rawData['sharedPolybaggingFee'] ?? 0.00,
            'sharedBubblewrapFee' => $rawData['sharedBubblewrapFee'] ?? 0.00,
            'sharedTapingFee' => $rawData['sharedTapingFee'] ?? 0.00,
            'sharedFbaCustomerReturnFee' => $rawData['sharedFbaCustomerReturnFee'] ?? 0.00,
            'sharedFbaInboundDefectFee' => $rawData['sharedFbaInboundDefectFee'] ?? 0.00,
            'sharedFbaOverageFee' => $rawData['sharedFbaOverageFee'] ?? 0.00,
            'sharedAmazonPartneredCarrierShipmentFee' => $rawData['sharedAmazonPartneredCarrierShipmentFee'] ?? 0.00,
            'sharedFbaInboundConvenienceFee' => $rawData['sharedFbaInboundConvenienceFee'] ?? 0.00,
            'sharedItemFeeAdjustment' => $rawData['sharedItemFeeAdjustment'] ?? 0.00,
            'sharedOtherFbaInventoryFees' => $rawData['sharedOtherFbaInventoryFees'] ?? 0.00,
            'sharedFbaIntegerernationalInboundFee' => $rawData['sharedFbaIntegerernationalInboundFee'] ?? 0.00,
            'adjustments' => $rawData['adjustments'] ?? 0.00,

            // 平台其他费用
            'totalPlatformOtherFee' => $rawData['totalPlatformOtherFee'] ?? 0.00,
            'shippingLabelPurchases' => $rawData['shippingLabelPurchases'] ?? 0.00,
            'sharedChargesToCreditCard' => $rawData['sharedChargesToCreditCard'] ?? 0,
            'sharedCarrierShippingLabelAdjustments' => $rawData['sharedCarrierShippingLabelAdjustments'] ?? 0.00,
            'sharedLiquidationsFees' => $rawData['sharedLiquidationsFees'] ?? 0.00,
            'sharedManualProcessingFee' => $rawData['sharedManualProcessingFee'] ?? 0.00,
            'sharedOtherServiceFees' => $rawData['sharedOtherServiceFees'] ?? 0.00,

            // 销售税
            'totalSalesTax' => $rawData['totalSalesTax'] ?? 0.00,
            'taxCollected' => $rawData['taxCollected'] ?? 0.00,
            'tcsIgstCollected' => $rawData['tcsIgstCollected'] ?? 0.00,
            'tcsSgstCollected' => $rawData['tcsSgstCollected'] ?? 0.00,
            'tcsCgstCollected' => $rawData['tcsCgstCollected'] ?? 0.00,
            'sharedComminglingVatExpenses' => $rawData['sharedComminglingVatExpenses'] ?? 0.00,
            'sharedTaxAdjustment' => $rawData['sharedTaxAdjustment'] ?? 0.00,
            'salesTaxRefund' => $rawData['salesTaxRefund'] ?? 0.00,
            'taxRefunded' => $rawData['taxRefunded'] ?? 0.00,
            'tcsIgstRefunded' => $rawData['tcsIgstRefunded'] ?? 0.00,
            'tcsSgstRefunded' => $rawData['tcsSgstRefunded'] ?? 0.00,
            'tcsCgstRefunded' => $rawData['tcsCgstRefunded'] ?? 0.00,
            'salesTaxWithheld' => $rawData['salesTaxWithheld'] ?? 0.00,
            'refundTaxWithheld' => $rawData['refundTaxWithheld'] ?? 0.00,
            'tdsSection194ONet' => $rawData['tdsSection194ONet'] ?? 0.00,

            // 订单其他费用
            'customOrderFee' => $rawData['customOrderFee'] ?? 0.00,
            'customOrderFeePrincipal' => $rawData['customOrderFeePrincipal'] ?? 0.00,
            'customOrderFeeCommission' => $rawData['customOrderFeeCommission'] ?? 0.00,

            // 预估费用
            'estimateFeeStr' => isset($rawData['estimateFeeStr']) ? json_encode($rawData['estimateFeeStr'], JSON_UNESCAPED_UNICODE) : '',

            // 成本数据
            'cgPrice' => $rawData['cgPrice'] ?? 0.00,
            'hasCgPriceDetail' => $rawData['hasCgPriceDetail'] ?? 0,
            'cgUnitPrice' => $rawData['cgUnitPrice'] ?? 0.00,
            'proportionOfCg' => $rawData['proportionOfCg'] ?? 0.0000,
            'cgTransportCosts' => $rawData['cgTransportCosts'] ?? 0.00,
            'hasCgTransportCostsDetail' => $rawData['hasCgTransportCostsDetail'] ?? 0,
            'firstTripUnitPrice' => $rawData['firstTripUnitPrice'] ?? null,
            'proportionOfCgTransport' => $rawData['proportionOfCgTransport'] ?? 0.0000,
            'cgOtherCostsTotal' => $rawData['cgOtherCostsTotal'] ?? 0.00,
            'cgOtherUnitCosts' => $rawData['cgOtherUnitCosts'] ?? 0.00,
            'hasCgOtherCostsDetail' => $rawData['hasCgOtherCostsDetail'] ?? 0,
            'proportionOfCgOtherCosts' => $rawData['proportionOfCgOtherCosts'] ?? 0.0000,
            'totalCost' => $rawData['totalCost'] ?? 0.00,
            'proportionOfTotalCost' => $rawData['proportionOfTotalCost'] ?? 0.0000,

            // 利润数据
            'grossProfit' => $rawData['grossProfit'] ?? 0.00,
            'grossProfitTax' => $rawData['grossProfitTax'] ?? 0.0000,
            'grossRate' => $rawData['grossRate'] ?? 0.0000,
            'grossRateWithTax' => $rawData['grossRateWithTax'] ?? null,

            // 税收明细
            'taxCollectedGiftWrap' => $rawData['taxCollectedGiftWrap'] ?? 0.00,
            'taxCollectedShipping' => $rawData['taxCollectedShipping'] ?? 0.00,
            'taxCollectedDiscount' => $rawData['taxCollectedDiscount'] ?? 0.00,
            'taxCollectedProduct' => $rawData['taxCollectedProduct'] ?? 0.00,
            'taxRefundedGiftWrap' => $rawData['taxRefundedGiftWrap'] ?? 0.00,
            'taxRefundedShipping' => $rawData['taxRefundedShipping'] ?? 0.00,
            'taxRefundedDiscount' => $rawData['taxRefundedDiscount'] ?? 0.00,
            'taxRefundedProduct' => $rawData['taxRefundedProduct'] ?? 0.00,

            // 监控信息
            'alarmInfo' => isset($rawData['alarmInfo']) ? json_encode($rawData['alarmInfo'], JSON_UNESCAPED_UNICODE) : '',

            // 计算汇总字段
            'totalFbaAndFbmQuantity' => ($rawData['fbaSalesQuantity'] ?? 0) + ($rawData['fbmSalesQuantity'] ?? 0),
            'totalFbaAndFbmAmount' => ($rawData['fbaSaleAmount'] ?? 0.00) + ($rawData['fbmSaleAmount'] ?? 0.00),
        ];
    }

    /**
     * 根据日期和条件查询利润统计数据
     * @param string $dataDate 数据日期
     * @param array $conditions 查询条件
     * @return array 查询结果
     */
    public function getProfitData($dataDate, $conditions = [])
    {
        $query = $this->db->table($this->table)
            ->where('dataDate = :dataDate', ['dataDate' => $dataDate]);

        if (!empty($conditions['msku'])) {
            $query->andWhere('msku = :msku', ['msku' => $conditions['msku']]);
        }

        if (!empty($conditions['asin'])) {
            $query->andWhere('asin = :asin', ['asin' => $conditions['asin']]);
        }

        if (!empty($conditions['sid'])) {
            $query->andWhere('sid = :sid', ['sid' => $conditions['sid']]);
        }

        if (!empty($conditions['countryCode'])) {
            $query->andWhere('countryCode = :countryCode', ['countryCode' => $conditions['countryCode']]);
        }

        return $query->list();
    }

    /**
     * 获取指定日期范围的销量统计
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @param array $conditions 查询条件
     * @return array 统计结果
     */
    public function getSalesStatistics($startDate, $endDate, $conditions = [])
    {
        $query = $this->db->table($this->table)
            ->where('dataDate BETWEEN :startDate AND :endDate', [
                'startDate' => $startDate,
                'endDate' => $endDate
            ]);

        if (!empty($conditions['msku'])) {
            $query->andWhere('msku = :msku', ['msku' => $conditions['msku']]);
        }

        return $query->field('
            msku,
            asin,
            sid,
            countryCode,
            SUM(totalSalesQuantity) as total_quantity,
            SUM(totalSalesAmount) as total_amount,
            SUM(fbaSalesQuantity) as fba_quantity,
            SUM(fbmSalesQuantity) as fbm_quantity
        ')
        ->groupBy('msku, asin, sid, countryCode')
        ->list();
    }

    /**
     * 删除指定日期的数据
     * @param string $dataDate 数据日期
     * @return bool 删除结果
     */
    public function deleteByDate($dataDate)
    {
        return $this->db->table($this->table)
            ->where('dataDate = :dataDate', ['dataDate' => $dataDate])
            ->delete();
    }
}
