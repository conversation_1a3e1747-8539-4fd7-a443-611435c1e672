<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/7 15:22
 */

namespace core\lib;

use plugins\shop\models\userModel;
use plugins\shop\models\userRolesModel;

class checkTokenShop
{
    public string $ip;
    public function __construct($_url, $prefix)
    {
        $headerData = getallheaders();
        $SkipToken = array(
            "admin/login/login",
            "shop/message/getMsgDetail",
        );
        $this->ip = GetIP();
        define('USER_TOKEN', $headerData['Authorization']??''); //调试模式设置
        if (!in_array($_url, $SkipToken)) {
            //验证是否带了token
            if (!isset($headerData['Authorization']) || $headerData['Authorization'] == '') {
                SetReturn(1, '请先登录');
            }
            if (!in_array($_url,['admin/login/logOut'])) {
                //验证登录是否过期
                $redis = (new predisV())::$client;
                $key = $prefix.$headerData['Authorization'];

                $data = $redis->hmget($key,['id','name','wid','avatar','is_super','wname','auth', 'list_auth','role_type','user_mac'])??'';
                if (!$data['id']) {
                    SetReturn(1, '请先登录');
                } else {
                    userModel::$wid = $data['wid'];
                    userModel::$qwuser_id = $data['id'];
                    userModel::$wname = $data['wname'];
                    userModel::$avatar = $data['avatar']??'';
                    userModel::$is_super = $data['is_super'];

                    // 把权限相关的拆分出来
                    // 拉取人力系统的权限
                    $auth_ = userRolesModel::getAuthByQuserId($data['id']);
                    if ($auth_) {
                        userModel::$auth = $auth_['auth'] ?? '[]';
                        userModel::$role_type = $auth_['role_type'];
                    }
//                    userModel::$auth = $data['auth'];
//                    $role_type = json_decode($data['role_type'],true);
//                    userModel::$role_type = $role_type;
//                    userModel::$list_auth = $data['list_auth'];

                    define('USER_INFO',json_encode($data));
                    //登录信息更新和时间缓存时间重置
                    $redis->expire($key,4*60*60);
                }
            }
        }

    }


}