<?php

namespace plugins\shop\models;

use core\lib\redisCached;
use Exception;

class receiveAccountModel extends baseModel
{
    public string $table = 'receive_account';

    // 账户定位
    const LEVEL_MAIN = 1;  // 主账户
    const LEVEL_SUB = 2;   // 子账户

    public static array $paras_list = [
        "account_name"         => "收款账户|required",
        "transaction_password" => "交易密码|required",
        "account_type"         => "账户类型|required",
        "account_holder"       => "账户主体|required",
        "use_platform"         => "使用平台|required",
        "registration_date"    => "注册日期|required",
        "customer_manager"     => "客户经理|required",
        "user_number"          => "用户ID|required",
        "pid"                  => "PID",
        "is_collected"         => "是否归集|required",
        "login_type"           => "登录方式|required",
        "phone_card_id"        => "手机号ID",
        "phone_login_password" => "登录密码",
        "email_id"             => "邮箱ID",
        "receive_platform"     => "收款平台|required",
        "account_usage"        => "用途",
        "credit_limit_info"    => "额度授权信息",
        "security_question"    => "安全问题|required",
        "remark"               => "备注",
    ];

    public static array $json_keys = [
        'security_question',
        'account_usage'
    ];

    public function getList($param, $order = 'id desc', $is_export = false)
    {
        $ids = [];
        if (isset($param['relations']) && !empty($param['relations'])) {
            // 虚拟卡，直接关联 receive_account
            if (in_array('credit_card', $param['relations'])) {
                $res = (new relationModel)->getIdsByRelation('receive_account', ['credit_card']);
                if (!empty($res)) $ids = array_merge($ids, $res);
            }
            // 店铺， 关联 receive_card， receive_card关联 receive_account
            if (in_array('shop', $param['relations'])) {
                $receive_card_ids = (new relationModel)->getIdsByRelation('receive_card', ['shop']);
                // 查询 receive_account
                if ($receive_card_ids) {
                    $res = $this->db->table('receive_card')
                        ->whereIn('id', $receive_card_ids)
                        ->list();
                    $res = array_column($res, 'receive_account_id');
                }
                if (!empty($res)) $ids = array_merge($ids, $res);
            }
            empty($ids) && returnSuccess(['total' => 0, 'page' => 0, 'list' => []]);
        }
        $this->db->table($this->table, 'ra')->field('ra.*')
            ->leftJoin('phone_card', 'pc', 'pc.id = ra.phone_card_id')
            ->leftJoin('email', 'e', 'e.id = ra.email_id');
        // 固定筛选
        if (!empty($param['ids'])) {
            $this->db->whereIn('ra.id', $param['ids']);
        }

        // 类型映射查询
        if (!empty($param['account_name'])) {
            $this->db->andWhere('account_name LIKE :account_name',
                ['account_name' => '%' . $param['account_name'] . '%']);
        }
        if (!empty($param['account_holder'])) {
            $this->db->andWhere('account_holder LIKE :account_holder',
                ['account_holder' => '%' . $param['account_holder'] . '%']);
        }
        if (!empty($param['phone_card'])) {
            $this->db->andWhere('pc.phone_number = :phone_card',
                ['phone_card' => $param['phone_card']]);
        }
        if (!empty($param['email'])) {
            $this->db->andWhere('e.email_account = :email',
                ['email' => $param['email']]);
        }
        if (!empty($param['customer_manager'])) {
            $this->db->andWhere('customer_manager LIKE :customer_manager',
                ['customer_manager' => '%' . $param['customer_manager'] . '%']);
        }
        if (!empty($param['user_number'])) {
            $this->db->andWhere('user_number = :user_number',
                ['user_number' => $param['user_number']]);
        }

        // 账户定位
        if (!empty($param['account_level'])) {
            if ($param['account_level'] == self::LEVEL_MAIN) {
                $this->db->andWhere('pid = 0');
            } elseif ($param['account_level'] == self::LEVEL_SUB) {
                $this->db->andWhere('pid > 0');
            }
        }

        // 账户类型
        if (!empty($param['account_type'])) {
            $this->db->andWhere('account_type = :account_type',
                ['account_type' => $param['account_type']]);
        }

        // 登录方式
        if (!empty($param['login_type'])) {
            $this->db->andWhere('login_type = :login_type',
                ['login_type' => $param['login_type']]);
        }

        // 收款平台
        if (!empty($param['receive_platform'])) {
            $this->db->andWhere('receive_platform = :receive_platform',
                ['receive_platform' => $param['receive_platform']]);
        }

        // 根据用途筛选（JSON数组格式，支持数组中任一值匹配）
        if ((!empty($param['account_usage']) && is_array($param['account_usage'])) || !empty($ids)) {
            $conditions = [];
            $param_arr = [];
            if (!empty($param['account_usage'])){
                $conditions[] = 'JSON_OVERLAPS(account_usage, :account_usage)';
                $param_arr['account_usage'] = json_encode($param['account_usage']);
            }
            $ids && $conditions[] = 'ra.id in (' . implode(',', $ids) . ')';
            $this->db->andWhere('(' . implode(' OR ', $conditions) . ')', $param_arr);
        }

        // 注册时间范围
        if (!empty($param['registration_date']) && is_array($param['registration_date']) && count($param['registration_date']) == 2) {
            $this->db->andWhere('registration_date >= :registration_date_start AND registration_date <= :registration_date_end', [
                'registration_date_start' => $param['registration_date'][0],
                'registration_date_end'   => $param['registration_date'][1]
            ]);
        }

        // 操作时间范围
        if (!empty($param['update_time']) && is_array($param['update_time']) && count($param['update_time']) == 2) {
            $this->db->andWhere('ra.updated_at >= :update_time_start AND ra.updated_at <= :update_time_end', [
                'update_time_start' => $param['update_time'][0],
                'update_time_end'   => $param['update_time'][1] .' 23:59:59'
            ]);
        }

        // 排序
        $this->db->order($order);

        if (!$is_export && !empty($param['page']) && !empty($param['page_size'])) {
            $list = $this->db->pages($param['page'], $param['page_size']);
            $maps = self::getMaps();
            foreach ($list['list'] as &$item) {
                $item = $this->formatItem($item, $maps);
            }
            return $list;
        } else {
            $list = $this->db->list();
            if ($is_export) {
                $export_data = [];
                $maps = self::getMaps();
                $paras_list = static::$paras_list;
                $paras_list['main_receive_account_name'] = '主账户';
                $paras_list['phone_number'] = '手机号';
                $paras_list['phone_user_name'] = '手机号持有人';
                $paras_list['phone_manager'] = '手机号对接人';
                $paras_list['email_account'] = '邮箱';
                $paras_list['email_password'] = '邮箱密码';
                foreach ($list as $item) {
                    $item = $this->formatItem($item, $maps);
                    // 使用参数对应的中文作为键名返回
                    $export_data[] = self::changeToCnKey($item, $paras_list);
                }
                return $export_data;
            }

            return $list;
        }
    }

    public static function getMaps()
    {
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');
        $email = redisCached::getEmail();
        $email_account = array_column($email, 'email_account', 'id');
        $email_password = array_column($email, 'email_password', 'id');
        $phone_cards = redisCached::getPhoneCard();
        $receive_account = redisCached::getReceiveAccountAll();
        $receive_account = array_column($receive_account, 'account_name', 'id');


        return [
            'users'           => $users,
            'email_account'   => $email_account,
            'email_password'  => $email_password,
            'phone_cards'     => $phone_cards,
            'receive_account' => $receive_account,
        ];
    }

    // 格式化输出项
    public function formatItem($item, $maps = [])
    {
        $users = $maps['users'] ?? [];
        if (empty($users)) {
            $users = redisCached::getUserInfo();
            $users = array_column($users, 'user_name', 'user_id');
        }
        $email_account = $maps['email_account'] ?? [];
        $email_password = $maps['email_password'] ?? [];
        if (empty($email_account) || empty($email_password)) {
            $email = redisCached::getEmail();
            $email_account = array_column($email, 'email_account', 'id');
            $email_password = array_column($email, 'email_password', 'id');
        }
        $phone_cards = $maps['phone_cards'] ?? [];
        if (empty($phone_cards)) {
            $phone_cards = redisCached::getPhoneCard();
        }
        $receive_account = $maps['receive_account'] ?? [];
        if (empty($receive_account)) {
            $receive_account = redisCached::getReceiveAccountAll();
            $receive_account = array_column($receive_account, 'account_name', 'id');
        }
        $maps = [
            ['name' => 'operator_name', 'maps' => $users, 'key' => 'operator'],
            ['name' => 'email_account', 'maps' => $email_account, 'key' => 'email_id'],
            ['name' => 'email_password', 'maps' => $email_password, 'key' => 'email_id'],
            ['name' => 'phone_number', 'maps' => array_column($phone_cards, 'phone_number', 'id'), 'key' => 'phone_card_id'],
            ['name' => 'phone_user_name', 'maps' => array_column($phone_cards, 'user_name', 'id'), 'key' => 'phone_card_id'],
            ['name' => 'phone_manager', 'maps' => array_column($phone_cards, 'phone_manager', 'id'), 'key' => 'phone_card_id'],
            ['name' => 'main_receive_account_name', 'maps' => $receive_account, 'key' => 'pid'],
        ];
        return parent::formatItem($item, $maps);
    }

    // 数据校验
    public function dataValidCheck(array $data, array $param_list = [], $is_throw = true, array $error = []): array
    {
        if (empty($param_list)) $param_list = self::$paras_list;
        // 邮箱和电话卡只需要一个即可
        if (!isset($data['email_id']) && !isset($data['phone_card_id'])) {
            if (!$is_throw) {
                $error[] = '邮箱和电话卡不能同时为空';
            } else {
                throw new Exception('邮箱和电话卡不能同时为空');
            }
        }
        unset($param_list['email_id']);
        unset($param_list['phone_card_id']);
        unset($param_list['phone_login_password']);
        return parent::dataValidCheck($data, $param_list, $is_throw, $error);
    }

    public function add($data, $type='新增')
    {
        $emailId = $data['email_id'] ?? null;
        $phoneCardId = $data['phone_card_id'] ?? null;
        $id = parent::add($data, $type);
        // 创建关联关系
        $relationModel = new relationModel();
        if ($emailId) {
            $relationModel->updateRelation($this->table, $id, 'email', $emailId);
        }
        if ($phoneCardId) {
            $relationModel->updateRelation($this->table, $id, 'phone_card', $phoneCardId);
        }

        return $id;
    }

    public function edit($data, $id, $old_data = [], $type = '编辑', $remark = '', $result = '', $other_attach = [])
    {
        $emailId = $data['email_id'] ?? null;
        $phoneCardId = $data['phone_card_id'] ?? null;
        parent::edit($data, $id, $old_data, $type, $remark, $result, $other_attach);

        // 创建关联关系
        $relationModel = new relationModel();
        if ($emailId) {
            $relationModel->updateRelation($this->table, $id, 'email', $emailId);
        }
        if ($phoneCardId) {
            $relationModel->updateRelation($this->table, $id, 'phone_card', $phoneCardId);
        }
    }

    // 批量编辑
    public function editBatch($data)
    {
        $db = $this->db->table($this->table);
        $ids = array_column($data, 'id');
        $db->whereIn('id', $ids);
        $list = $db->list();
        $list = array_column($list, null, 'id');


        $error_ids = [];
        foreach ($data as $item) {
            try {
                $item_id = $item['id'];
                unset($item['id']);
                $this->dataValidCheck($item);
                // 唯一性校验
                $detail = $this->getByAccountName($item['account_name'], $item_id);
                if ($detail) {
                    throw new Exception('收款账户已存在');
                }
                self::edit($item, $item_id, $list[$item_id], '批量编辑');
            } catch (Exception $e) {
                $error_ids[] = [
                    'id' => $item_id,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $error_ids;
    }

    // 根据ID获取数据
    public function getById($id)
    {
        if (empty($id)) {
            return null;
        }

        return $this->db->table($this->table)
            ->where('id = :id', ['id' => $id])
            ->one();
    }

    // 通过手机号获取信息
    public function getByAccountName($account_name, $id = null)
    {
        $this->db->table($this->table)->where('where account_name = :account_name', ['account_name' => $account_name]);
        if ($id) {
            $this->db->andWhere('id != :id', ['id' => $id]);
        }
        return $this->db->one();
    }
}
