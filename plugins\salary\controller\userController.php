<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/13 9:10
 */

namespace plugins\salary\Controller;

use core\lib\config;
use core\lib\db\dbSMysql;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;
use plugins\salary\models\userModel;

class userController
{
    //通过token获取用户信息
    public function getUserInfo()
    {
        $data = [
            'id'        => userModel::$qwuser_id,
            'wid'       => userModel::$wid,
            'wname'     => userModel::$wname,
            'avatar'    => userModel::$avatar,
            'is_super'  => userModel::$is_super,
            'auth'      => userModel::$auth,
            'role_type' => userModel::$role_type,
        ];
        returnSuccess(['data' => $data]);
    }

    //获取用户列表
    public function getList()
    {
        $paras_list = array('wname', 'qw_partment_id', 'wstatus', 'page', 'page_size', 'order_by', 'roles_id');
        $param = arrangeParam($_POST, $paras_list);
        //先查出角色绑定的用户id
        $sdb = dbSMysql::getInstance();
        $user_ids = [];
        if (!empty($param['roles_id'])) {
            $roles_ids = json_decode($param['roles_id']);
            if (count($roles_ids)) {
                $user_ids = [-1];
                $user_roles = $sdb->table('user_roles')
                    ->where('where is_delete = 0')
                    ->whereIn('role_id', $roles_ids)
                    ->field('user_id')
                    ->list();
                if (count($user_roles)) {
                    $user_ids = array_column($user_roles, 'user_id');
                }
            }
        }
        //婴孩查询
        $db = dbMysql::getInstance();
        $db->table('qwuser', 'a')->field('a.id,a.wid,a.wname,a.wphone,a.wdepartment_ids,a.wstatus,a.updated_at,a.is_manage,a.avatar,a.position')->where('where a.is_delete = 0');
        if (!empty($param['wname'])) {
            $db->andWhere('and a.wname like :wname', ['wname' => '%' . $param['wname'] . '%']);
        }
        if (!empty(trim($param['qw_partment_id']))) {
            $qw_partment_ids = explode(',', $param['qw_partment_id']);
            $db->andWhere('and JSON_CONTAINS(a.wdepartment_ids,:qw_partment_id)', ['qw_partment_id' => json_encode($qw_partment_ids, JSON_NUMERIC_CHECK)]);
        }
        if (!empty($param['wstatus'])) {
            $db->andWhere('and a.wstatus = :wstatus', ['wstatus' => $param['wstatus']]);
        }
        $order_str = '';
        if (!empty($param['order_by'])) {
            $order_by = json_decode($param['order_by'], true);
            $order_str = '';
            foreach ($order_by as $k => $ord) {
                $order_str .= 'a.' . $k . ' ' . $ord . ',';
            }
        }
        if (count($user_ids)) {
            $db->whereIn('id', $user_ids);
        }
        if (empty($order_str)) {
            $db->order('a.id desc');
        } else {
            $db->order(trim($order_str, ','));
        }
        $data = $db->pages($param['page'], $param['page_size']);
//        $data['list'] = roleModel::getUserListRole($data['list']);
        returnSuccess($data);
    }

    //个人修改密码
    public function updateUserPwd()
    {
        $paras_list = array('password');
        $param = arrangeParam($_POST, $paras_list);

        $prefix = config::get('token_key_prefix', 'app');
        $key = $prefix . USER_TOKEN;
        $redis = (new \core\lib\predisV())::$client;
        $login_data = $redis->hMGet($key, ['id', 'wid']);
        $wid = $login_data['wid'];
        if (!$param['password']) {
            SetReturn(-1, "密码不能为空");
        }
        $db = dbMysql::getInstance();
        $user = $db->query("select id,uniqueid from oa_user where qw_userid = :qw_userid and is_delete = 0", ['qw_userid' => $wid]);
        if (!$user) {
            SetReturn(-1, "该用户还不是管理员");
        }

        $pwd = getPwdMd5($param['password'], $user['uniqueid']);

        $db->table('user');
        $db->where('where id = :id', ['id' => $user['id']]);
        $up_data = ['pwd' => $pwd, 'updated_at' => time()];
        if ($db->update($up_data)) {
            returnSuccess([], '修改成功');
        } else {
            SetReturn(-1, '修改失败');
        }
    }

    /**
     * @return void   常用用户查询
     * @throws ExceptionError
     */
    public function getOftenUsedUser()
    {
        $db = dbMysql::getInstance();
        $db->table('user_often_used', 'a');
        $db->leftJoin('qwuser', 'b', 'b.id = a.used_qwuser_id');
        $db->where('where qwuser_id=:qwuser_id', ['qwuser_id' => userModel::$qwuser_id]);
        $db->order('a.updated_at desc');
        $db->field('b.id,b.wid,b.wname,b.avatar,b.position');
        $list = $db->list();
        returnSuccess(['data' => $list]);
    }

    public function setOftenUsedUser()
    {
        $paras_list = array('qwuser_ids');
        $param = arrangeParam($_POST, $paras_list);
        if (!empty($param['qwuser_ids'])) {
            $qwuser_ids = $need_add_ids = json_decode($param['qwuser_ids']);
            $db = dbMysql::getInstance();
            //查询这些用户是否在数据中
            $db->table('user_often_used');
            $db->where('where qwuser_id=:qwuser_id', ['qwuser_id' => userModel::$qwuser_id]);
            $db->field('used_qwuser_id');
            $db->whereIn('used_qwuser_id', $qwuser_ids);
            $old_user = $db->list();
            //修改
            $db->update(['updated_at' => time()]);

            if (count($old_user)) {
                $old_used_qwuser_ids = array_column($old_user, 'used_qwuser_id');
                $need_add_ids = array_diff($qwuser_ids, $old_used_qwuser_ids);
            }
            if (count($need_add_ids)) {
                $insert_sql = 'insert into oa_user_often_used (qwuser_id,used_qwuser_id,updated_at) values ';
                $insert_data = ['qwuser_id' => userModel::$qwuser_id, 'updated_at' => time()];
                foreach ($need_add_ids as $k => $v) {
                    $insert_sql .= "(:qwuser_id,:used_qwuser_id_$k,:updated_at),";
                    $insert_data["used_qwuser_id_$k"] = $v;
                }
                $insert_sql = trim($insert_sql, ',');
                $db->query($insert_sql, $insert_data);
            }
            returnSuccess('', '');
        }
    }
}