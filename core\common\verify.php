<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/7 17:42
 */


// 检查参数是否完备
function checkParaments($ReqObj, $ParaList) {
    if ($ReqObj == null) {
        return("ROOT");
    }
    foreach($ParaList as $Field) {
        if (!isset($ReqObj[$Field])) {
            return($Field);
        }
    }
    return(NULL);
}

// 检查参数是否为空字符串
function checkEmptyString($ReqObj, $ParaList) {
    if ($ReqObj == null) {
        return("ROOT");
    }
    foreach($ParaList as $Field) {
        if ($ReqObj[$Field] == '') {
            return($Field);
        }
    }

    return(NULL);
}

function arrangeParam(array $arry, $param_list,array $request = [],array $length_data=[]){
    $r_result = [];
    foreach($param_list as $v){
        if (isset($arry[$v])) {
            $r_result[$v] = $arry[$v];
            switch ($v) {
                case 'page':
                    $r_result[$v] = (empty($r_result[$v]) || is_null($r_result[$v])) ? 1:$r_result[$v];
                    break;
                case 'page_size':
                    $r_result[$v] = (empty($r_result[$v]) || is_null($r_result[$v])) ? 10:$r_result[$v];
                    break;
//                case 'status':
//                    $r_result[$v] = (is_null($r_result[$v])) ? -1: (int)$r_result[$v];
//                    break;
            }
        } else {
            switch ($v) {
                case 'page':
                    $r_result[$v] = 1;
                    break;
                case 'page_size':
                    $r_result[$v] = 10;
                    break;
//                case 'status':
//                    $r_result[$v] = -1;
//                    break;
                default:
                    $r_result[$v] = '';
                    break;
            }
        }
        if (isset($request[$v]) && $r_result[$v] == ''){
            SetReturn(-1,$request[$v].'必填');
        }
    }
    if (count($length_data)) {
        foreach ($length_data as $k=>$v) {
            if (isset($arry[$k]) && !empty($v['length'])) {
                $length_ = mb_strlen($arry[$k], 'UTF-8');
                if ($length_ > $v['length']) {
                    SetReturn(-1,$v['name'].'长度不能超过'.$v['length'].'个字符串');
                }
            }
        }
    }
    return $r_result;
}