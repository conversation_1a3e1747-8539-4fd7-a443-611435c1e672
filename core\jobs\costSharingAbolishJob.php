<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/2 9:32
 */

namespace core\jobs;

//废除费用
use core\lib\config;
use core\lib\db\dbFMysql;
use financial\form\costSharingForm;
use financial\form\customColumnForm;
use financial\form\mskuReportJobForm;
use financial\form\runShellTaskForm;

class costSharingAbolishJob
{
    public string $unqueid = '';
    public int $user_id;
    public $type; //1作废，2作废完后计算
    public function __construct($user_id,$type){
        $this->unqueid = uniqid();
        $this->user_id = $user_id;
        $this->type = $type;
    }
    public function task(){
        $key = 'oa_cost_sharing_abolish';
        $redis = (new \core\lib\predisV())::$client;
        if ($redis->get($key)) {
            $data = json_decode($redis->get($key),true);
            $import_data_ids = $data['import_data_ids'];
            $data_id = $import_data_ids[0];
            //修改导入数据状态
            $db = dbFMysql::getInstance();
            $db->table('cost_sharing_data')
                ->where('where id=:id',['id'=>$data_id])
                ->update([
                    'status'=>4,
                    'abolish_user_id'=>$this->user_id
                ]);
            if (count($import_data_ids)) {
                //获取要还原的数据
                $shared_list = $db->table('cost_sharing_log')
                    ->where('where csd_id=:csd_id and is_back=0',['csd_id'=>$data_id])
                    ->list();
                if (count($shared_list)) {
                    //获取数据
                    $data_ = $db->table('cost_sharing_data','a')
                        ->leftJoin('cost_sharing','b','b.id = a.import_id')
                        ->where('where a.id=:id',['id'=>$data_id])
                        ->field('a.data,b.m_date')
                        ->one();
                    $import_data = json_decode($data_['data'],true);
                    //获取报告数据
                    $base_ids = array_column($shared_list,'msku_report_id');
                    $year = date('Y',strtotime($data_['m_date'].'-01'));
                    $report_list = $db->table('msku_report_data_'.$year)
                        ->whereIn('id',$base_ids)
                        ->field('id,'.$import_data['cost_key'])
                        ->list();
                    $db->beginTransaction();
                    //修改数据
                    foreach ($report_list as $v1) {
                        foreach ($shared_list as $v2) {
                            if ($v1['id'] == $v2['msku_report_id']) {
                                $share_val = $v2['share_val'];
                                $res_amount = ($v1[$import_data['cost_key']]*100 - $share_val*100)/100;
                                $db->table('msku_report_data_'.$year)
                                    ->where('where id=:id',['id'=>$v1['id']])
                                    ->update([$import_data['cost_key']=>$res_amount]);
                            }
                        }
                    }
                    //修改导入数据状态
                    $db->table('cost_sharing_data')
                        ->where('where id=:id',['id'=>$data_id])
                        ->update([
                            'status'=>($this->type == 1)?5:0,
                            'abolish_time'=>date('Y-m-d H:i:s'),
                        ]);
                    //修改log状态
                    $db->table('cost_sharing_log')
                        ->where('where csd_id=:csd_id',['csd_id'=>$data_id])
                        ->update([
                            'is_back'=>1,
                        ]);
                    $db->commit();
                } else {
                    //修改导入数据状态
                    $db->table('cost_sharing_data')
                        ->where('where id=:id',['id'=>$data_id])
                        ->update([
                            'status'=>($this->type == 1)?5:0,
                            'abolish_time'=>date('Y-m-d H:i:s'),
                        ]);
                }

            }
            //移出数据进行下一个
            unset($import_data_ids[0]);

            if (count($import_data_ids)) {
                $data['import_data_ids'] = array_values($import_data_ids);
                $redis->set($key,json_encode($data));
                $redis->expire($key,60*60);
                $task = new costSharingAbolishJob($this->user_id,$this->type);
                $queue_key = config::get('delay_queue_key', 'app');
                $redis->zAdd($queue_key, [], 0, serialize($task));
            } else {
                $import_ids = $data['import_ids'];
                $redis->del($key);
                if ($this->type == 2) {
                    //重新计算
                    costSharingForm::setTask($data_id,$import_ids);
                } else {
                    //修改表单状态
                    costSharingForm::updateSharingStatus($import_ids);
                }
                $redis->del($key);

            }
        }
    }
}