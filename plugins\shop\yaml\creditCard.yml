openapi: 3.0.0
info:
  title: 信用卡管理API
  version: 1.0.0
  description: 提供信用卡管理的相关接口，支持实体卡和虚拟卡
paths:
  /shop/creditCard/getList:
    get:
      tags:
        - 信用卡管理
      summary: 获取信用卡列表
      description: 根据条件筛选获取信用卡列表
      parameters:
        - name: type
          in: query
          description: 卡类型：1-实体卡，2-虚拟卡
          required: false
          schema:
            type: integer
            enum: [1, 2]
            default: 1
        # 通用参数
        - name: card_number
          in: query
          description: 卡号
          required: false
          schema:
            type: string
        - name: validity_period
          in: query
          description: 有效期
          required: false
          schema:
            type: string
        - name: shop_name
          in: query
          description: 店铺名称
          required: false
          schema:
            type: string
        - name: shop_status
          in: query
          description: 店铺情况
          required: false
          schema:
            type: string
        # 实体卡参数
        - name: cardholder_name
          in: query
          description: 开户人姓名
          required: false
          schema:
            type: string
        - name: activation_bank
          in: query
          description: 开户银行
          required: false
          schema:
            type: string
        - name: credit_card_status
          in: query
          description: 信用卡状态
          required: false
          schema:
            type: string
        - name: activation_status
          in: query
          description: 激活状态
          required: false
          schema:
            type: string
        - name: storage_location
          in: query
          description: 存储位置
          required: false
          schema:
            type: string
        - name: usage_group
          in: query
          description: 使用对接群
          required: false
          schema:
            type: string
        - name: fee_group
          in: query
          description: 费用对接群
          required: false
          schema:
            type: string
        - name: is_use_self
          in: query
          description: 是否自用
          required: false
          schema:
            type: integer
        - name: user_email
          in: query
          description: 用户邮箱
          required: false
          schema:
            type: string
        - name: application_time
          in: query
          description: 提供日期
          required: false
          schema:
            type: string
            format: date
        - name: activation_period
          in: query
          description: 提供时间范围
          required: false
          schema:
            type: array
            items:
              type: string
              format: date-time
        # 虚拟卡参数
        - name: activation_platform
          in: query
          description: 开卡平台
          required: false
          schema:
            type: string
        - name: main_account
          in: query
          description: 主账号
          required: false
          schema:
            type: string
        - name: use_platform
          in: query
          description: 使用平台
          required: false
          schema:
            type: string
        - name: currency
          in: query
          description: 币种
          required: false
          schema:
            type: string
        - name: use_status
          in: query
          description: 使用状态
          required: false
          schema:
            type: string
        - name: service_provider
          in: query
          description: 对接服务商
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/CreditCard'
                      total:
                        type: integer
                        description: 总记录数
                      page:
                        type: integer
                        description: 当前页码
                      page_size:
                        type: integer
                        description: 每页条数

  /shop/creditCard/add:
    post:
      tags:
        - 信用卡管理
      summary: 添加信用卡
      description: 新增信用卡信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/PhysicalCardCreate'
                - $ref: '#/components/schemas/VirtualCardCreate'
      responses:
        '200':
          description: 添加成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 添加成功
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 新增记录ID

  /shop/creditCard/detail:
    get:
      tags:
        - 信用卡管理
      summary: 获取信用卡详情
      description: 根据ID获取信用卡详细信息
      parameters:
        - name: id
          in: query
          description: 信用卡ID
          required: true
          schema:
            type: integer
        - name: type
          in: query
          description: 卡类型：1-实体卡，2-虚拟卡
          required: false
          schema:
            type: integer
            enum: [1, 2]
            default: 1
      responses:
        '200':
          description: 成功返回详情数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    $ref: '#/components/schemas/CreditCard'

  /shop/creditCard/edit:
    post:
      tags:
        - 信用卡管理
      summary: 编辑信用卡
      description: 修改信用卡信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/PhysicalCardEdit'
                - $ref: '#/components/schemas/VirtualCardEdit'
      responses:
        '200':
          description: 编辑成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 编辑成功

  /shop/creditCard/import:
    post:
      tags:
        - 信用卡管理
      summary: 批量导入信用卡
      description: 通过Excel文件批量导入信用卡数据
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                excel_src:
                  type: string
                  description: Excel文件链接
                type:
                  type: integer
                  description: 卡类型：1-实体卡，2-虚拟卡
              required:
                - excel_src
                - type
      responses:
        '200':
          description: 导入成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 导入成功
                  data:
                    type: object
                    properties:
                      error_file:
                        type: string
                        description: 错误数据文件路径
                      error_count:
                        type: integer
                        description: 错误数量
                      success_count:
                        type: integer
                        description: 成功数量

  /shop/creditCard/export:
    get:
      tags:
        - 信用卡管理
      summary: 导出信用卡数据
      description: 根据筛选条件导出信用卡数据
      parameters:
        - name: type
          in: query
          description: 卡类型：1-实体卡，2-虚拟卡
          required: false
          schema:
            type: integer
            enum: [1, 2]
            default: 1
      responses:
        '200':
          description: 导出成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 导出成功
                  data:
                    type: object
                    properties:
                      download_url:
                        type: string
                        description: 下载链接

  /shop/creditCard/applyFee:
    post:
      tags:
        - 信用卡管理
      summary: 申请信用卡费用
      description: 提交信用卡费用申请，支持年费、使用费等类型
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 信用卡ID
                fee_type:
                  type: integer
                  description: 费用类型(1-年费 2-使用费 3-其他费用)
                amount:
                  type: number
                  format: decimal
                  description: 费用金额
                fee_date:
                  type: string
                  format: date
                  description: 费用日期，默认当前日期
                currency:
                  type: string
                  description: 币种，默认CNY
                description:
                  type: string
                  description: 费用说明
                remark:
                  type: string
                  description: 备注
              required:
                - id
                - fee_type
                - amount
      responses:
        '200':
          description: 申请成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 费用申请成功
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 费用记录ID

  /shop/creditCard/confirmFee:
    post:
      tags:
        - 信用卡管理
      summary: 确认信用卡费用
      description: 确认信用卡费用金额
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 信用卡ID
                confirm_amount:
                  type: number
                  format: decimal
                  description: 确认金额
                confirm_remark:
                  type: string
                  description: 确认备注
              required:
                - id
                - confirm_amount
      responses:
        '200':
          description: 确认成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 费用确认成功

  /shop/creditCard/reportLoss:
    post:
      tags:
        - 信用卡管理
      summary: 实体卡挂失
      description: 对使用中的信用卡进行挂失操作
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 信用卡ID
                is_reissued:
                  type: boolean
                  description: 是否补卡
                notifyer:
                  type: string
                  description: 通知人
                remark:
                  type: string
                  description: 备注
              required:
                - id
      responses:
        '200':
          description: 挂失成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 挂失成功

  /shop/creditCard/applyReissue:
    post:
      tags:
        - 信用卡管理
      summary: 实体卡申请补办
      description: 对已挂失的信用卡申请补办
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 信用卡ID
              required:
                - id
      responses:
        '200':
          description: 申请补办成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 申请补办成功

  /shop/creditCard/cancel:
    post:
      tags:
        - 信用卡管理
      summary: 实体卡注销
      description: 对使用中的信用卡进行注销操作
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 信用卡ID
                notifyer:
                  type: string
                  description: 通知人
                remark:
                  type: string
                  description: 备注
              required:
                - id
      responses:
        '200':
          description: 注销成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 注销成功

  /shop/creditCard/addFollowUp:
    post:
      tags:
        - 信用卡管理
      summary: 实体卡跟进记录
      description: 添加信用卡状态变更的跟进记录
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 信用卡ID
                is_finished:
                  type: boolean
                  description: 是否完成
                credit_card_status:
                  type: integer
                  description: 信用卡状态
                notifyer:
                  type: string
                  description: 通知人
                remark:
                  type: string
                  description: 备注
              required:
                - id
      responses:
        '200':
          description: 添加跟进记录成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 添加跟进记录成功

components:
  schemas:
    BaseCard:
      type: object
      properties:
        id:
          type: integer
          description: ID
        type:
          type: integer
          description: 卡类型：1-实体卡，2-虚拟卡
        card_number:
          type: string
          description: 卡号
        validity_period:
          type: string
          description: 有效期
        security_code:
          type: string
          description: 安全码
        shop_name:
          type: string
          description: 绑定店铺名称
        shop_status:
          type: string
          description: 店铺情况
        remark:
          type: string
          description: 备注

    PhysicalCard:
      allOf:
        - $ref: '#/components/schemas/BaseCard'
        - type: object
          properties:
            cardholder_name:
              type: string
              description: 开户人姓名
            activation_bank:
              type: string
              description: 开户银行
            credit_card_status:
              type: string
              description: 信用卡状态
            activation_status:
              type: string
              description: 激活状态
            storage_location:
              type: string
              description: 存储位置
            usage_group:
              type: string
              description: 使用对接群
            fee_group:
              type: string
              description: 费用对接群
            legal_person_shop:
              type: string
              description: 法人店铺使用
            dep_id:
              type: integer
              description: 部门ID
            job_status:
              type: string
              description: 工作情况
            record_keeper:
              type: string
              description: 信用卡登记负责人
            user_email:
              type: string
              description: 用户邮箱
            is_use_self:
              type: integer
              description: 是否自用
            annual_fee_amount:
              type: number
              description: 年费金额
            annual_fee_standard:
              type: string
              description: 年费达标标准
            annual_fee_deduction_time:
              type: string
              format: date-time
              description: 扣年费日期
            billing_date:
              type: string
              description: 还款日
            application_time:
              type: string
              format: date-time
              description: 提供时间

    VirtualCard:
      allOf:
        - $ref: '#/components/schemas/BaseCard'
        - type: object
          properties:
            activation_platform:
              type: string
              description: 开卡平台
            main_account:
              type: string
              description: 主账号
            use_platform:
              type: string
              description: 使用平台
            currency:
              type: string
              description: 币种
            use_status:
              type: string
              description: 使用状态
            service_provider:
              type: string
              description: 对接服务商
            activation_date:
              type: string
              format: date-time
              description: 开卡日期
            bind_shop_date:
              type: string
              format: date-time
              description: 绑定店铺日期

    CreditCard:
      oneOf:
        - $ref: '#/components/schemas/PhysicalCard'
        - $ref: '#/components/schemas/VirtualCard'
      discriminator:
        propertyName: type
        mapping:
          1: '#/components/schemas/PhysicalCard'
          2: '#/components/schemas/VirtualCard'

    PhysicalCardCreate:
      allOf:
        - $ref: '#/components/schemas/PhysicalCard'
        - type: object
          required:
            - type
            - card_number
            - activation_bank
            - validity_period
            - card_type
            - activation_status
            - credit_card_status

    VirtualCardCreate:
      allOf:
        - $ref: '#/components/schemas/VirtualCard'
        - type: object
          required:
            - type
            - card_number
            - activation_platform
            - main_account
            - use_platform
            - currency
            - use_status

    PhysicalCardEdit:
      allOf:
        - $ref: '#/components/schemas/PhysicalCardCreate'
        - type: object
          required:
            - id

    VirtualCardEdit:
      allOf:
        - $ref: '#/components/schemas/VirtualCardCreate'
        - type: object
          required:
            - id
