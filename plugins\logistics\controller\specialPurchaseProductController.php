<?php

namespace plugins\logistics\controller;

use Exception;
use core\lib\validator;
use plugins\logistics\models\specialPurchaseProductModel;

class specialPurchaseProductController extends baseController
{
    private $model;

    public function __construct()
    {
        parent::__construct();
        $this->model = new specialPurchaseProductModel();
    }

    /**
     * 获取特殊产品列表
     */
    public function getList()
    {
        $paras_list = [
            'country_code', 'listing_stage',
        ];
        $param = array_intersect_key($_GET, array_flip($paras_list));
        // 参数验证
        $error = validator::validate($param, [
            'country_code' => '站点|string|required',
            'listing_stage' => '阶段|string|required',
        ]);

        if (!empty($error['errors'])) {
            returnError($error['error_msg']);
        }

        $param['page'] = $param['page'] ?? 1;
        $param['page_size'] = $param['page_size'] ?? 10;

        $result = $this->model->getList($param);

        foreach ($result['list'] as &$item) {
            $item = $this->model->handleJsonFields($item, false);
        }
        
        returnSuccess($result);
    }

    /**
     * 添加特殊产品
     */
    public function add()
    {
        $paras_list = specialPurchaseProductModel::$paras_list;
        $param = array_intersect_key($_POST, array_flip(array_keys($paras_list)));
        $id = $_POST['id'] ?? 0;

        // 参数验证
        $error = validator::validate($param, [
            'id' => 'ID|integer|gt:0',
            'sku' => 'SKU|string|required',
            'special_days' => '采购备货天数|array|len:2|required',
            'special_days.*' => '采购备货天数子项|integer|gt:0|required',
        ]);

        if (!empty($error['errors'])) {
            returnError($error['error_msg']);
        }
        // sku校验

        if ($id) {
            // 验证数据正确性
            $detail = $this->model->getById($id);
            if (!$detail) {
                returnError('数据不存在');
            }
            // sku不能变更
            if ($detail['sku'] != $param['sku']) {
                returnError('SKU不能变更');
            }

            $this->model->edit($param, $id, $detail);
            returnSuccess([], '编辑成功');
        } else {
            $exist = $this->model->getBySku($param['sku']);
            if ($exist) {
                returnError('该商品已存在');
            }
            $result = $this->model->add($param);
            returnSuccess(['id' => $result], '添加成功');
        }
    }

    /**
     * 删除特殊产品
     */
    public function delete()
    {
        $id = $_POST['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        try {
            $this->model->delete($id);
            returnSuccess([], '删除成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 获取可选商品列表（根据站点和listing阶段筛选）
     */
    public function getAvailableProducts()
    {
        $paras_list = ['country_code', 'listing_stage', 'product_name', 'sku', 'page', 'page_size'];
        $param = array_intersect_key($_GET, array_flip($paras_list));
        
        $param['page'] = $param['page'] ?? 1;
        $param['page_size'] = $param['page_size'] ?? 10;

        $error = validator::validate($param, [
            'country_code' => '站点|required|string',
            'listing_stage' => '阶段|required|string',
            'product_name' => '商品名称|string',
            'sku' => 'SKU|string',
        ]);

        if (!empty($error['errors'])) {
            returnError($error['error_msg']);
        }

        $result = $this->model->getAvailableProducts($param);
        returnSuccess($result);
    }
}