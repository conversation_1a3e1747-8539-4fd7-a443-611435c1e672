<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/11 13:32
 */

namespace task\controller;

use core\jobs\qwUserSynJobs;
use core\lib\config;
use core\lib\db\dbCMysql;
use core\lib\db\dbMysql;
use core\lib\log;
use core\lib\predisV;



class qwCallbackController{

    private static $_corp_id = 'ww30cba3012bb1e660';
    private static $_token = '9HPq8EOvnrhz9uLXR4uSC5TMpmfEtY';
    private static $_aesKey = 'DXoDnJbX4oRxz26GJTBAC1qER2USOk7Xgh3TYw03iPe';


    public function company_user_recall(){
        // 获取接口是get还是post
        $request_method = $_SERVER['REQUEST_METHOD'];
        returnError($request_method);
        if ($request_method == 'GET') {
        }
        $paras_list = array('msg_signature', 'timestamp', 'nonce', 'echostr');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $signature = $param["msg_signature"];
        $timestamp = $param["timestamp"];
        $nonce = $param["nonce"];
        $echo = $param["echostr"];

        $token = self::$_token;
        $tmpArr = array($token, $timestamp, $nonce,$echo);
        sort($tmpArr, SORT_STRING);
        $tmpStr = sha1(implode($tmpArr));
        $aesKey = base64_decode((self::$_aesKey.'='));
        $aes = new Aes($aesKey);
        $res = $aes->decryptWithBase64($echo);
        if( $tmpStr == $signature ){
            // 假设企业号在公众平台上设置的参数如下
            $encodingAesKey = self::$_aesKey;
            $corpId =  self::$_corp_id;
            $sVerifyMsgSig = $signature;
            $sVerifyTimeStamp = $timestamp;
            $sVerifyNonce = $nonce;
            $sVerifyEchoStr = $echo;
            // 需要返回的明文
            $sEchoStr = "";

            $wxcpt = new \WXBizMsgCrypt($token, $encodingAesKey, $corpId);
            $errCode = $wxcpt->VerifyURL($sVerifyMsgSig, $sVerifyTimeStamp, $sVerifyNonce, $sVerifyEchoStr, $sEchoStr);

            if ($errCode == 0) {
                $this->htmlReturn($sEchoStr);return;
            }

            dd($echo);
        }else{
            dd(404);
        }
    }
}