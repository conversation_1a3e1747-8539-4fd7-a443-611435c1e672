<?php

/**
 * @author: zhangguoming
 * @Time: 2024/6/14 9:08
 */

namespace task\form;
use core\lib\db\dbFMysql;
use core\lib\ExceptionError;
use financial\models\mskuReportModel;

class lixingXingApiNewForm
{
    public static $m_date_routing = [];//本月的汇率（同步报表数据用得到）
    public static $currency_keys = [];//要转换金额的字段
    //msku报告数据储存
    public static function saveMskuReport($list,$date,$page) {
        //查看表单是否存在
        $year = date('Y',strtotime($date.'-01'));
        mskuReportModel::creatMskuReportTable($year);
        $table = 'msku_report_data_'.$year;
        $dbF = dbFMysql::getInstance();
        if ($page == 1) {
            $dbF->beginTransaction();
            //删除之前的数据
            $dbF->table($table)
                ->where('where reportDateMonth = :m_date and import_id=0',['m_date'=>$date])
                ->delete();
            //清除库存
            $dbF->table($table)
                ->where('where reportDateMonth = :m_date',['m_date'=>$date])
                ->update(['key15'=>0,'key16'=>0,'key17'=>0]);
            //清除导入增量的数据
            $goods_data_ = $dbF->table('goods_data_import')
                ->where('where report_date = :m_date',['m_date'=>$date])
                ->field('id')
                ->list();
            if (count($goods_data_)) {
                $goods_data_ids = array_column($goods_data_,'id');
                $dbF->table('goods_data_'.$year)
                    ->where('where m_date = :m_date',['m_date'=>$date])
                    ->update(['is_error'=>1,'base_id'=>0]);
                $dbF->table('goods_data_import_error_log')
                    ->whereIn('import_id',$goods_data_ids)
                    ->delete();
            }
            //清除导入库存的数据
            $goods_stock_ = $dbF->table('goods_stock_import')
                ->where('where report_date = :m_date',['m_date'=>$date])
                ->field('id')
                ->list();
            if (count($goods_stock_)) {
                $goods_stock_ids = array_column($goods_stock_,'id');
                $dbF->table('goods_stock_'.$year)
                    ->where('where m_date = :m_date',['m_date'=>$date])
                    ->update(['is_error'=>1]);
                $dbF->table('goods_stock_import_error_log')
                    ->whereIn('import_id',$goods_stock_ids)
                    ->delete();
            }
            $dbF->commit();
        }
        $db = dbFMysql::getInstance();
        //商品
        $goods_list = $db->table('goods')->field('sku,supplier_id')
            ->list();
        $sku_supplier = [];
        foreach ($goods_list as $v) {
            $sku_supplier[$v['sku']] = $v['supplier_id'];
        }
        //获取金额字段和汇率字段
//        self::getHuilv($date);
        //异常记录
        $insert_list = [];
        $dbF->beginTransaction();
        try {
            foreach ($list as $vv) {
                //整理数据，根据汇率计算值规则修改
//            $v = self::getMskuRow($vv);
                $v = $vv;
                //部门验证
                $listing_where = [
                    'm_date'=>$date,
                    'sid'=>$v['sid'],
                    'marketplace'=>$v['country'],
                    'seller_sku'=>$v['msku'],
                    'asin'=>$v['asin'],
                    'parent_asin'=>$v['parentAsin'],
                    'local_sku'=>$v['localSku'],
                ];
                $listing_data = $db->table('project_listing_'.$year)
                    ->where('where m_date=:m_date and sid=:sid and marketplace=:marketplace and seller_sku=:seller_sku and asin=:asin and parent_asin=:parent_asin and local_sku=:local_sku',$listing_where)->one();
                $project_id = 0;
                $yunying_id = 0;
                if ($listing_data) {
                    $project_id = $listing_data['project_id'];
                    $yunying_id = $listing_data['yunying_id'];
                }
                $supplier_id = isset($sku_supplier[$v['localSku']])?$sku_supplier[$v['localSku']]:0;
                $category_id = 0;
                $insert_data = ['lingxing_id'=>$v['id'],'project_id'=>$project_id,'yunying_id'=>$yunying_id,'category_id'=>$category_id,'sid'=>$v['sid'],'reportDateMonth'=>$v['reportDateMonth'],'postedDateLocale'=>$v['postedDateLocale'],'msku'=>$v['msku'],'asin'=>$v['asin'],'parentAsin'=>$v['parentAsin'],'storeName'=>$v['storeName'],'countryCode'=>$v['countryCode'],'localName'=>$v['localName'],'localSku'=>$v['localSku'],'itemName'=>$v['itemName'],'principalRealname'=>$v['principalRealname'],'productDeveloperRealname'=>$v['productDeveloperRealname'],'categoryName'=>$v['categoryName'],'brandName'=>$v['brandName'],'currencyCode'=>$v['currencyCode'],'currencyIcon'=>$v['currencyIcon'],'listingTagIds'=>$v['listingTagIds'],'country'=>$v['country'], 'supplier_id'=>$supplier_id,'totalFbaAndFbmQuantity'=>$v['totalFbaAndFbmQuantity'],'totalFbaAndFbmAmount'=>$v['totalFbaAndFbmAmount'],'totalSalesQuantity'=>$v['totalSalesQuantity'],'fbaSalesQuantity'=>$v['fbaSalesQuantity'],'fbmSalesQuantity'=>$v['fbmSalesQuantity'],'totalReshipQuantity'=>$v['totalReshipQuantity'],'reshipFbmProductSalesQuantity'=>$v['reshipFbmProductSalesQuantity'],'reshipFbmProductSaleRefundsQuantity'=>$v['reshipFbmProductSaleRefundsQuantity'],'reshipFbaProductSalesQuantity'=>$v['reshipFbaProductSalesQuantity'],'reshipFbaProductSaleRefundsQuantity'=>$v['reshipFbaProductSaleRefundsQuantity'],'cgAbsQuantity'=>$v['cgAbsQuantity'],'cgQuantity'=>$v['cgQuantity'],'totalAdsSales'=>$v['totalAdsSales'],'adsSdSales'=>$v['adsSdSales'],'adsSpSales'=>$v['adsSpSales'],'sharedAdsSbSales'=>$v['sharedAdsSbSales'],'sharedAdsSbvSales'=>$v['sharedAdsSbvSales'],'totalAdsSalesQuantity'=>$v['totalAdsSalesQuantity'],'adsSdSalesQuantity'=>$v['adsSdSalesQuantity'],'adsSpSalesQuantity'=>$v['adsSpSalesQuantity'],'sharedAdsSbSalesQuantity'=>$v['sharedAdsSbSalesQuantity'],'sharedAdsSbvSalesQuantity'=>$v['sharedAdsSbvSalesQuantity'],'totalSalesAmount'=>$v['totalSalesAmount'],'fbaSaleAmount'=>$v['fbaSaleAmount'],'fbmSaleAmount'=>$v['fbmSaleAmount'],'shippingCredits'=>$v['shippingCredits'],'promotionalRebates'=>$v['promotionalRebates'],'fbaInventoryCredit'=>$v['fbaInventoryCredit'],'cashOnDelivery'=>$v['cashOnDelivery'],'otherInAmount'=>$v['otherInAmount'],'fbaLiquidationProceeds'=>$v['fbaLiquidationProceeds'],'fbaLiquidationProceedsAdjustments'=>$v['fbaLiquidationProceedsAdjustments'],'amazonShippingReimbursement'=>$v['amazonShippingReimbursement'],'mcFbaFulfillmentFeesQuantity'=>$v['mcFbaFulfillmentFeesQuantity'],'safeTReimbursement'=>$v['safeTReimbursement'],'netcoTransaction'=>$v['netcoTransaction'],'reimbursements'=>$v['reimbursements'],'clawbacks'=>$v['clawbacks'],'sharedComminglingVatIncome'=>$v['sharedComminglingVatIncome'],'giftWrapCredits'=>$v['giftWrapCredits'],'guaranteeClaims'=>$v['guaranteeClaims'],'costOfPoIntegersGranted'=>$v['costOfPoIntegersGranted'],'totalSalesRefunds'=>$v['totalSalesRefunds'],'fbaSalesRefunds'=>$v['fbaSalesRefunds'],'fbmSalesRefunds'=>$v['fbmSalesRefunds'],'shippingCreditRefunds'=>$v['shippingCreditRefunds'],'giftWrapCreditRefunds'=>$v['giftWrapCreditRefunds'],'chargebacks'=>$v['chargebacks'],'costOfPoIntegersReturned'=>$v['costOfPoIntegersReturned'],'promotionalRebateRefunds'=>$v['promotionalRebateRefunds'],'totalFeeRefunds'=>$v['totalFeeRefunds'],'sellingFeeRefunds'=>$v['sellingFeeRefunds'],'fbaTransactionFeeRefunds'=>$v['fbaTransactionFeeRefunds'],'refundAdministrationFees'=>$v['refundAdministrationFees'],'otherTransactionFeeRefunds'=>$v['otherTransactionFeeRefunds'],'refundForAdvertiser'=>$v['refundForAdvertiser'],'pointsAdjusted'=>$v['pointsAdjusted'],'shippingLabelRefunds'=>$v['shippingLabelRefunds'],'refundsQuantity'=>$v['refundsQuantity'],'refundsRate'=>$v['refundsRate'],'fbaReturnsQuantity'=>$v['fbaReturnsQuantity'],'fbaReturnsSaleableQuantity'=>$v['fbaReturnsSaleableQuantity'],'fbaReturnsUnsaleableQuantity'=>$v['fbaReturnsUnsaleableQuantity'],'fbaReturnsQuantityRate'=>$v['fbaReturnsQuantityRate'],'platformFee'=>$v['platformFee'],'totalFbaDeliveryFee'=>$v['totalFbaDeliveryFee'],'fbaDeliveryFee'=>$v['fbaDeliveryFee'],'mcFbaDeliveryFee'=>$v['mcFbaDeliveryFee'],'otherTransactionFees'=>$v['otherTransactionFees'],'totalAdsCost'=>$v['totalAdsCost'],'adsSpCost'=>$v['adsSpCost'],'adsSbCost'=>$v['adsSbCost'],'adsSbvCost'=>$v['adsSbvCost'],'adsSdCost'=>$v['adsSdCost'],'sharedCostOfAdvertising'=>$v['sharedCostOfAdvertising'],'promotionFee'=>$v['promotionFee'],'sharedSubscriptionFee'=>$v['sharedSubscriptionFee'],'sharedLdFee'=>$v['sharedLdFee'],'sharedCouponFee'=>$v['sharedCouponFee'],'sharedEarlyReviewerProgramFee'=>$v['sharedEarlyReviewerProgramFee'],'sharedVineFee'=>$v['sharedVineFee'],'totalStorageFee'=>$v['totalStorageFee'],'fbaStorageFee'=>$v['fbaStorageFee'],'sharedFbaStorageFee'=>$v['sharedFbaStorageFee'],'longTermStorageFee'=>$v['longTermStorageFee'],'sharedLongTermStorageFee'=>$v['sharedLongTermStorageFee'],'sharedStorageRenewalBilling'=>$v['sharedStorageRenewalBilling'],'sharedFbaDisposalFee'=>$v['sharedFbaDisposalFee'],'sharedFbaRemovalFee'=>$v['sharedFbaRemovalFee'],'sharedFbaInboundTransportationProgramFee'=>$v['sharedFbaInboundTransportationProgramFee'],'sharedLabelingFee'=>$v['sharedLabelingFee'],'sharedPolybaggingFee'=>$v['sharedPolybaggingFee'],'sharedBubblewrapFee'=>$v['sharedBubblewrapFee'],'sharedTapingFee'=>$v['sharedTapingFee'],'sharedFbaCustomerReturnFee'=>$v['sharedFbaCustomerReturnFee'],'sharedFbaInboundDefectFee'=>$v['sharedFbaInboundDefectFee'],'sharedFbaOverageFee'=>$v['sharedFbaOverageFee'],'sharedAmazonPartneredCarrierShipmentFee'=>$v['sharedAmazonPartneredCarrierShipmentFee'],'sharedFbaInboundConvenienceFee'=>$v['sharedFbaInboundConvenienceFee'],'sharedItemFeeAdjustment'=>$v['sharedItemFeeAdjustment'],'sharedOtherFbaInventoryFees'=>$v['sharedOtherFbaInventoryFees'],'fbaStorageFeeAccrual'=>$v['fbaStorageFeeAccrual'],'fbaStorageFeeAccrualDifference'=>$v['fbaStorageFeeAccrualDifference'],'longTermStorageFeeAccrual'=>$v['longTermStorageFeeAccrual'],'longTermStorageFeeAccrualDifference'=>$v['longTermStorageFeeAccrualDifference'],'sharedFbaIntegerernationalInboundFee'=>$v['sharedFbaIntegerernationalInboundFee'],'adjustments'=>$v['adjustments'],'totalPlatformOtherFee'=>$v['totalPlatformOtherFee'],'shippingLabelPurchases'=>$v['shippingLabelPurchases'],'fbaInventoryCreditQuantity'=>$v['fbaInventoryCreditQuantity'],'disposalQuantity'=>$v['disposalQuantity'],'removalQuantity'=>$v['removalQuantity'],'others'=>$v['others'],'sharedCarrierShippingLabelAdjustments'=>$v['sharedCarrierShippingLabelAdjustments'],'sharedLiquidationsFees'=>$v['sharedLiquidationsFees'],'sharedManualProcessingFee'=>$v['sharedManualProcessingFee'],'sharedOtherServiceFees'=>$v['sharedOtherServiceFees'],'sharedMfnPostageFee'=>$v['sharedMfnPostageFee'],'totalSalesTax'=>$v['totalSalesTax'],'tcsIgstCollected'=>$v['tcsIgstCollected'],'tcsSgstCollected'=>$v['tcsSgstCollected'],'tcsCgstCollected'=>$v['tcsCgstCollected'],'sharedComminglingVatExpenses'=>$v['sharedComminglingVatExpenses'],'taxCollected'=>$v['taxCollected'],'taxCollectedProduct'=>$v['taxCollectedProduct'],'taxCollectedDiscount'=>$v['taxCollectedDiscount'],'taxCollectedShipping'=>$v['taxCollectedShipping'],'taxCollectedGiftWrap'=>$v['taxCollectedGiftWrap'],'sharedTaxAdjustment'=>$v['sharedTaxAdjustment'],'salesTaxRefund'=>$v['salesTaxRefund'],'tcsIgstRefunded'=>$v['tcsIgstRefunded'],'tcsSgstRefunded'=>$v['tcsSgstRefunded'],'tcsCgstRefunded'=>$v['tcsCgstRefunded'],'taxRefunded'=>$v['taxRefunded'],'taxRefundedProduct'=>$v['taxRefundedProduct'],'taxRefundedDiscount'=>$v['taxRefundedDiscount'],'taxRefundedShipping'=>$v['taxRefundedShipping'],'taxRefundedGiftWrap'=>$v['taxRefundedGiftWrap'],'salesTaxWithheld'=>$v['salesTaxWithheld'],'refundTaxWithheld'=>$v['refundTaxWithheld'],'tdsSection194ONet'=>$v['tdsSection194ONet'],'cgPriceTotal'=>$v['cgPriceTotal'],'hasCgPriceDetail'=>$v['hasCgPriceDetail'],'cgUnitPrice'=>$v['cgUnitPrice'],'proportionOfCg'=>$v['proportionOfCg'],'cgTransportCostsTotal'=>$v['cgTransportCostsTotal'],'hasCgTransportCostsDetail'=>$v['hasCgTransportCostsDetail'],'cgTransportUnitCosts'=>$v['cgTransportUnitCosts'],'proportionOfCgTransport'=>$v['proportionOfCgTransport'],'totalCost'=>$v['totalCost'],'proportionOfTotalCost'=>$v['proportionOfTotalCost'],'cgOtherCostsTotal'=>$v['cgOtherCostsTotal'],'cgOtherUnitCosts'=>$v['cgOtherUnitCosts'],'hasCgOtherCostsDetail'=>$v['hasCgOtherCostsDetail'],'proportionOfCgOtherCosts'=>$v['proportionOfCgOtherCosts'],'grossProfit'=>$v['grossProfit'],'grossProfitTax'=>$v['grossProfitTax'],'grossProfitIncome'=>$v['grossProfitIncome'],'grossRate'=>$v['grossRate'],'customOrderFee'=>$v['customOrderFee'],'customOrderFeePrincipal'=>$v['customOrderFeePrincipal'],'customOrderFeeCommission'=>$v['customOrderFeeCommission'],'roi'=>$v['roi'],];
                $other_fee_data = [
                    'key1'=>0, 'key2'=>0, 'key3'=>0, 'key4'=>0, 'key5'=>0, 'key6'=>0, 'key7'=>0, 'key8'=>0, 'key9'=>0, 'key10'=>0,
                    'key11'=>0, 'key12'=>0, 'key13'=>0, 'key14'=>0, 'key18'=>0, 'key19'=>0, 'key20'=>0, 'key21'=>0,
                ];
                $insert_data = array_merge($insert_data,$other_fee_data);
                if (!empty($v['otherFeeStr'])) {
                    $otherFeeStr = $v['otherFeeStr'];
                    foreach ($otherFeeStr as $fee) {
                        $feeAllocation = !empty($fee['feeAllocation'])?$fee['feeAllocation']:0;
                        $key = '';
                        switch ($fee['otherFeeTypeId']) {
                            case 1165: //（调整的采购成本）弃置清算亚马逊破损成本
                                $key = 'key1';break;
                            case 1169://服务商费用
                                $key = 'key2';break;
                            case 1170://店铺相关费用
                                $key = 'key3';break;
                            case 1171://测评费+推广费-推广部返款
                                $key = 'key4';break;
                            case 1172://测评费+推广费-服务商返款
                                $key = 'key5';break;
                            case 1173://预留资金
                                $key = 'key6';break;
                            case 1175://（调整的头程成本）FBM多渠道独立站调整
                                $key = 'key7';break;
                            case 1176://欧洲VAT税金
                                $key = 'key8';break;
                            case 1177://（调整的尾程成本）站内多渠道尾程成本调整
                                $key = 'key9';break;
                            case 1178://物流其他费用
                                $key = 'key10';break;
                            case 1179://站外广告费
                                $key = 'key11';break;
                            case 1023999://物流商赔偿收入
                                $key = 'key12';break;
                            case 1024000://福利政策-采购成本
                                $key = 'key13';break;
                            case 1024001://福利政策-头程成本
                                $key = 'key14';break;
                            case 1024020://福利政策-其他成本
                                $key = 'key18';break;
                            case 1024024://海外营销费用
                                $key = 'key19';break;
                            case 1024025://站内数据调整
                                $key = 'key20';break;
                            case 1024039://调整的数量
                                $key = 'key21';break;
                        }
                        if (!empty($key)) {
                            $insert_data[$key] = $feeAllocation;
                        }
                    }
                }
                $insert_list[] = $insert_data;
            }
            if (count($insert_list)) {
                $keys = array_keys($insert_list[0]);
                $list_ = array_map(function ($row){
                    return array_values($row);
                },$insert_list);
                $dbF->table($table)
                    ->insertBatch($keys,$list_);
            }
            $dbF->commit();
        } catch (ExceptionError $error) {
            $dbF->rollBack();
        }

        return true;
    }
    public static function setMskuLog($date) {
        $dbF = dbFMysql::getInstance();
        $dbF->table('msku_report_data_syn_log')
            ->insert([
                'syn_time'=>date('Y-m-d H:i:s'),
                'm_date'=>$date,
            ]);
    }

    //获取汇率和字段
    public static function getHuilv($date) {
        $db = dbFMysql::getInstance();
        //字段
        $column_list = $db->table('column')
            ->where('where is_delete=0 and show_type=1 and custom_id =0')
            ->list();
        $column_ = array_column($column_list,'key_name');
        //汇率
        $routing_list = $db->table('routing')
            ->where('where date=:date',['date'=>$date])
            ->field('code,my_rate')
            ->list();
        if (!count($routing_list)) {
            SetReturn(2,'汇率未同步');
        }
        $routing_ = [];
        foreach ($routing_list as $v) {
            $routing_[$v['code']] = $v['my_rate'];
        }
        self::$currency_keys = $column_;
        self::$m_date_routing = $routing_;
    }
    //更具汇率计算值
    public static function getMskuRow($row) {
        $countryCode = $row['currencyCode'];
        $route = self::$m_date_routing[$countryCode];
        foreach ($row as $k_=>&$v_) {
            if (in_array($k_,self::$currency_keys)) {
                $v_ = round($v_*$route,'2');
            }
        }
        return $row;
    }
}
