<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/31 9:48
 */

namespace financial\form;

use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use financial\models\goodsCategoryModel;
use financial\models\goodsWaringModel;
use financial\models\mskuReportModel;

//用于产品预警的的更新
class goodsWaringCountForm
{
    public static string $m_date;//计算的月份
    public static array $month_list = []; //规则用到的月份（本月起的倒叙）(用于数据查询)
    public static array $year_list = []; //规则用到的时间(用于数据查询)
    public static array $country_code= []; //规则中涉及到的国家编码(用于数据查询)
    public static array $cate_cids;//该规则涉及到的产品分类(用于数据查询)
    public static array $lx_keys = [];//需要查询的领星字段(用于数据查询)
    public static array $oa_keys = [];//需要查询的oa字段
    public static array $oa_custom_ids = [];//需要查询的oa字段自定义字段ids(用于数据查询)
    public static int $leve_type;//维度
    public static string $leve_type_key;//维度对应的key
    public static array $rules = []; //整理后的规则
    public static array $lx_list = [];//查出来的领星数据
    public static array $oa_list = [];//查出来的oa数据
    public static string $return_msg = '';//报错异常信息
    public static array $rule_month;//9个月的月份（用于后期规则计算）
    public static array $custom_column_rules = [];//自定义字段需要算的规则
    public static string $monitor_key; //监控指标字段
    public static array $waring_reason;//失败的原因
    public static array $rules_text;//规则内容
    public static string $column_name; //列名
    public static string $receive_type;//预警
    public static array $sku_cateid_relation;//产品sku和分类id的关系
    public static array $rule_country_cata_sku;//规则中的分类sku关系
    public static array $this_waring_rule;//本规则
    //根据等级规则修改商品等级
    public static function setWaringGoods($id,$m_date) {
        set_time_limit(0);
        self::$m_date = $m_date;
        $db = dbFMysql::getInstance();
        $data = $db->table('waring_rules')
            ->where('where id =:id and status = 1 and is_delete = 0',['id'=>$id])
            ->one();
        if (!$data) {
            self::$return_msg = '未找到预警规则';
            return '';
        }
        if ($data['begin_time'] != strtotime($m_date)) {
            self::$return_msg = "预警规则不属于{$m_date}";
            return '';
        }
        self::$this_waring_rule = $data;
        self::$receive_type = $data['receive_type'];
        self::getRules($data,$m_date);
        //字段整理，规则整理
        self::getColumn($data['column_id']);
        //数据查询
        self::getMskuData();
        //计算统计符合规则的sku数据
        $res = self::getSkuByRules();
        //没有字段在规则中，规则就是问题规则计算
        //设置等级数据
        self::setLevelRelation($id,$res,$data['waring_name']);
    }
    //查询规则
    public static function getRules($data,$m_date) {
        $rules = json_decode($data['rules'],true);
        //获取所有列
        $month_type = [];//月份，要用到数据的月份（上月，连续两月..）
        $market_ids = []; //国家
        $reference_type = []; //监控规则对比对象（环比）
        foreach ($rules as $v) {
            foreach ($v['condition'] as $v1) {
                $month_type[] = $v1['month'];
                $market_ids = array_merge($market_ids,$v['market_id']);
                $reference_type[] = $v1['reference'];
            }
        }
        //获取查询年份+建表
        self::getMonthByRules($m_date,$month_type,$reference_type);
        self::$leve_type = $data['dimension'];
        if ($data['dimension'] == 1) {
            self::$leve_type_key = 'asin';
        } elseif ($data['dimension'] == 2) {
            self::$leve_type_key = 'p_asin';
        } else {
            self::$leve_type_key = 'sku';
        }
        $db = dbFMysql::getInstance();
        $country_ = [];
        if (count($market_ids)) {
            $country_list  = $db->table('market')
                ->whereIn('id',$market_ids)
                ->list();
            foreach ($country_list as $v) {
                $country_[$v['id']] = $v;
            }
            self::$country_code = array_unique(array_column($country_list,'code'));
        } else {
            $country_list  = $db->table('market')
                ->list();
            foreach ($country_list as $v) {
                $country_[$v['id']] = $v;
            }
            self::$country_code = array_unique(array_column($country_list,'code'));
        }
        //规则整理
        $cate_cids = []; //分类cid
        foreach ($rules as &$v) {
            $v['country_code'] = [];
            if (count($v['market_id'])) {
                foreach ($v['market_id'] as $market_id) {
                    $v['country_code'][] = $country_[$market_id]['code'];
                }
            } else {
                $v['country_code'] = self::$country_code;
            }
            //分类整理
            $cate_ids = $v['category_id'];
            if (count($cate_ids)) {
                //1、分类cid查询
                $goods_category = $db->table('goods_category','a')
                    ->whereIn('id',$cate_ids)
                    ->field('cid')
                    ->list();
                //获取分类的cid和子分类的cid
                $category_cids = array_column($goods_category,'cid');
                $p_cids = array_diff($category_cids,[0]);
                //2、查询子分类
                if (count($p_cids)) {
                    $next_category = $db->table('goods_category','a')
                        ->whereIn('parent_cid',$p_cids)
                        ->field('cid')
                        ->list();
                    $category_cids = array_merge($category_cids,array_column($next_category,'cid'));
                }
                $cate_cids = array_merge($cate_cids,$category_cids);
                $v['category_id'] = $cate_cids;
            }
        }
        self::$cate_cids = $cate_cids;
        self::$rules = $rules;
        $rules_text = goodsWaringModel::getRuleTxt($rules,$country_,$cate_cids);//规则详细说明
        self::$rules_text = $rules_text;
    }
    //查询字段整理数据，//字段整理，规则整理
    public static function getColumn($c_ids) {
        //获取字段
        $db = dbFMysql::getInstance();
        $column = $db->table('column')
            ->where('where id =:id',['id'=>$c_ids])
            ->field('id,key_name,column_name,table_index,custom_id,show_type')
            ->one();
        self::$monitor_key = $column['key_name'];//监控指标字段
        self::$column_name = $column['column_name'];
        //比例字段 客单价字段
        $lx_keys = [];
        $oa_keys = [];
        //如果是自定义字段
        if ($column['custom_id'] > 0) {
            //毛利润增加合并计算字段
            $custom_column = $db->table('custom_column')
                ->where('where id = :id',['id'=>$column['custom_id']])
                ->one();
            //客单价或者比例字段
            $rules = boardRateForm::getResRules($custom_column);
            $relation_column = $rules['relation_column'];
            if ($custom_column['sort'] == 5 || $custom_column['sort'] == 6 || $custom_column['show_type'] == 2 || in_array('oa_key_5',$relation_column)){
                foreach ($relation_column as $c_c_key) {
                    if (strpos($c_c_key,'oa_key_') !== false) {
                        //自定义字段
                        $oa_key_array = explode('_',$c_c_key);
                        $oa_keys[] = $c_c_key;
                        self::$oa_custom_ids[] = end($oa_key_array);
                    } else {
                        $lx_keys[] = $c_c_key;
                    }
                }
                self::$custom_column_rules['show_type'] = $custom_column['show_type'];
                self::$custom_column_rules['rules'] = $rules['rules'];
            } else {
                $oa_keys = [$column['key_name']];
                self::$oa_custom_ids = [$column['custom_id']];
            }
        } else {
            $lx_keys[] = $column['key_name'];
        }
        if (in_array('oa_key_4',$oa_keys)){
            $lx_keys = array_values(array_unique(array_merge($lx_keys,customColumnForm::$aggregation_keys)));
        }
        self::$lx_keys = $lx_keys;
        self::$oa_keys = $oa_keys;
    }
    //数据查询
    public static function getMskuData() {
        $db = dbFMysql::getInstance();
        //先根据分类获取产品的可用sku
        $sku_list = self::getSkuByCateId(self::$cate_cids);
        //领星数据
        $lx_list = [];
        $lx_c_list = [];
        if (count(self::$lx_keys)) {
            //组别和字段整理
            foreach (self::$lx_keys as $key_) {
                $where_array[] = "sum($key_) as $key_";
            }
            if (self::$leve_type == 1) {
                $fields = 'asin,countryCode as country_code,reportDateMonth as m_date,';
                $groupby = ['asin','countryCode','reportDateMonth'];
            } elseif (self::$leve_type == 2) {
                $fields = 'parentAsin as p_asin,countryCode as country_code,reportDateMonth as m_date,';
                $groupby = ['parentAsin','countryCode','reportDateMonth'];
            } else {
                $fields = 'localSku as sku,countryCode as country_code,reportDateMonth as m_date,';
                $groupby = ['localSku','countryCode','reportDateMonth'];
            }
            $fields .= implode(',',$where_array);
            foreach (self::$year_list as $year=>$m_date_list) {
                $db->table("table_month_count_$year")
                    ->where("where is_delete = 0")
//                    ->andWhere("asin = 'B0C1MR6VZZ'")
                    ->whereIn('localSku',$sku_list)
                    ->whereIn('reportDateMonth',self::$month_list);
                if (count(self::$country_code)) {
                    $db->whereIn('countryCode', self::$country_code);
                }
                $db->field($fields)->groupBy($groupby);
                $list = $db->list();
                $lx_list = array_merge($lx_list,$list);
            }
            foreach ($lx_list as $v) {
                $key_m = $v[self::$leve_type_key].'_'.$v['m_date'];
                $country_code = $v['country_code']??'-1';
                $lx_c_list[$country_code][$key_m] = $v;
            }
        }
        //oa数据
        $oa_list = [];
        $oa_c_list = [];
        if (count(self::$oa_keys)) {
            //分组和字段
            //分组
            if (self::$leve_type == 1) {
                $fields = 'asin,m_date,custom_id,country_code,sum(custom_val) as total';
                $groupby = ['asin','country_code','custom_id','m_date'];
            } elseif (self::$leve_type == 2) {
                $fields = 'p_asin,m_date,custom_id,country_code,sum(custom_val) as total';
                $groupby = ['p_asin','country_code','custom_id'];
            } else {
                $fields = 'sku,m_date,custom_id,country_code,sum(custom_val) as total';
                $groupby = ['sku','country_code','custom_id','m_date'];
            }
            foreach (self::$year_list as $year=>$m_date_list) {
                $db->table("custom_val_$year")
//                    ->where("asin ='B0C1MR6VZZ'")
                    ->whereIn('sku',$sku_list)
                    ->whereIn('custom_id',self::$oa_custom_ids)
                    ->whereIn('m_date',self::$month_list);
                //国家
                if (count(self::$country_code)) {
                    $db->whereIn('country_code',self::$country_code);
                }
                $db->field($fields)->groupBy($groupby);
                $list = $db->list();
                $oa_list = array_merge($oa_list,$list);
            }
            foreach ($oa_list as $v) {
                $key_m = $v[self::$leve_type_key].'_'.$v['m_date'];
                $country_code = $v['country_code']??'-1';
                $oa_key_ = 'oa_key_'.$v['custom_id'];
                if (isset($oa_c_list[$country_code][$key_m])) {
                    $oa_c_list[$country_code][$key_m][$oa_key_] = $v['total'];
                } else {
                    $v[$oa_key_] = $v['total'];
                    unset($v['total']);
                    $oa_c_list[$country_code][$key_m] = $v;
                }
            }
        }
        self::$lx_list = $lx_c_list;
        self::$oa_list = $oa_c_list;
    }
    //计算统计符合规则的sku数据
    public static function getSkuByRules() {
        $sku_list = [];
        $rule_country_cata_sku = [];
        $all_sku_cateid_relation =  self::$sku_cateid_relation;
        foreach (self::$rules as $v) {
            $contry_code = $v['country_code'];
            if (!count($contry_code)) {
                $contry_code = [-1];
            }
            $cate_cids = $v['category_id'];
            foreach ($contry_code as $c_code) {
                $rule_country_cata_sku[$c_code] = array_keys(array_intersect($all_sku_cateid_relation,$cate_cids));
                $weidu_array_all = [];//最终的维度对应的数据集合
                foreach ($v['condition'] as $k=>$c1) {
                    $weidu_array = self::getValRow($c_code,$c1);
                    if ($k > 0) {
                        if ($c1['type'] == 1) {
                            $weidu_array_all = array_intersect($weidu_array_all,$weidu_array);
                        } else {
                            $weidu_array_all = array_merge($weidu_array_all,$weidu_array);
                        }
                    } else {
                        $weidu_array_all = $weidu_array;
                    }
                }
                $sku_list[$c_code] = array_values(array_unique($weidu_array_all));
            }
            //全部国家就循环一次
            if ($contry_code[0] == -1) {
                break;
            }
        }
        self::$rule_country_cata_sku = $rule_country_cata_sku;
        return $sku_list;
    }
    //规则最小单位获取结果
    public static function getValRow(string $contry_code,array $rule_row) {
        $weidu_array = [];//满足该条件的维度数据
        $monitor_key = self::$monitor_key;
        if (in_array($monitor_key,self::$lx_keys)) {
            $res_data = self::$lx_list[$contry_code]??[];
        } else {
            $res_data = self::$oa_list[$contry_code]??[];
            if (strpos($monitor_key,'oa_key_') !== false) {
                $res2_data = self::$lx_list[$contry_code]??[];
            }
        }
        $weidu_all_array = array_unique(array_column($res_data,self::$leve_type_key));
        if (count($res_data)) {
            foreach ($weidu_all_array as $weidu_key) {
                $month_val = [];
                //获取每个月的值
                foreach (self::$rule_month as $k=>$month) {
                    $key_m = $weidu_key.'_'.$month;
                    $total = 0;
                    if (isset($res_data[$key_m])) {
                        $total = $res_data[$key_m][$monitor_key]??0;
                        if (self::$monitor_key == 'oa_key_4' && isset($res2_data[$key_m])) {
                            //毛利润
                            $lx_row_data = $res2_data[$key_m];
                            $aggregation_keys = customColumnForm::$aggregation_keys;
                            foreach ($aggregation_keys as $cus_key) {
                                $total += (float)($lx_row_data[$cus_key]??0);
                            }
                        } elseif (count(self::$custom_column_rules)) {
                            $row_data =  $res_data[$key_m];
                            if (isset($res2_data[$key_m])) {
                                $row_data = array_merge($res_data[$key_m],$res2_data[$key_m]);
                            }

                            if (isset($row_data['oa_key_4'])) {
                                $aggregation_keys = customColumnForm::$aggregation_keys;
                                foreach ($aggregation_keys as $cus_key) {
                                    $row_data['oa_key_4'] += (float)($row_data[$cus_key]??0);
                                }
                            }
                            //需要用规则计算的字段
                            $column_rules = self::$custom_column_rules;
                            //计算数据
                            $show_type = $column_rules['show_type'];
                            $rule_data = $column_rules['rules'][0]['rules'];
                            foreach ($rule_data as &$rule_) {
                                if ($rule_['group_type'] == 1) {
                                    $rule_['val'] = rulesDataForm::getValByCustomRule($rule_,$row_data);
                                } else {
                                    foreach ($rule_['list'] as &$rule_l) {
                                        $rule_l['val'] =  rulesDataForm::getValByCustomRule($rule_l,$row_data);
                                    }
                                }
                            }
                            $total = roundToString(coustomColumnJobForm::getValue($rule_data));
                        }
                    }
                    $month_val[$k] = $total??0;
                }
                //计算获取最终的值
                $val_res = self::getSecondVal($month_val,$rule_row);
                $val_array = $val_res['val'];
                $waring_msg = $val_res['waring_msg'];
                $status = self::countRowRes($val_array,$rule_row);
                if ($status) {
                    $key_ = $weidu_key.'_'.$contry_code;
                    $weidu_array[] = $weidu_key;
                    $last_waring_reason = self::$waring_reason[$key_]??[];
                    self::$waring_reason[$key_] = array_merge($last_waring_reason,$waring_msg);
                }
            }
        }
        return $weidu_array;
    }
    //根据规则计算值
    public static function getSecondVal($month_val,$rule_row) {
        $val_array = [];//最终计算出来的的数据
        $compare_month = $rule_row['month'];//监听字段的月份
        $interval_value = empty($rule_row['Interval_value'])?0:(float)$rule_row['Interval_value'];//自定义值
        $reference = (int)$rule_row['reference'];//字段的月份比较的月份（上月等）
        $is_absolute_value = (int)$rule_row['is_absolute_value'];//0值，1绝对值
        $waring_msg = [];//预警的具体信息
        if ($compare_month == 1) {
            //本月
            $res_val1 = 0;
            $val_1 = $is_absolute_value?abs($month_val[0]):$month_val[0];
            $waring_msg[] = '本月值：'.$month_val[0];
            if ($reference == 1) {
                //与自定义值
                $res_val1 = $val_1 - $interval_value;
            }
            if ($reference == 2) {
                //上月数据比
                $val_2 = $is_absolute_value?abs($month_val[1]):$month_val[1];
                $res_val1 = $val_1 - $val_2;
                $waring_msg[] = '上月值：'.$month_val[1];
            }
            if ($reference == 3) {
                //环比
                $val_2 = $is_absolute_value?abs($month_val[1]):$month_val[1];
                if ($val_2 != 0) {
                    $res_val1 = ($val_1 - $val_2)*100/$val_2;
                }  else {
                    $res_val1 = '-';
                }
                $waring_msg[] = '本月环比值：'.$res_val1;
            }
            if ($reference == 4) {
                //同比
                $val_2 = $is_absolute_value?abs($month_val[1]):$month_val[6];
                if ($val_2 != 0) {
                    $res_val1 = ($val_1 - $val_2)*100/$val_2;
                }  else {
                    $res_val1 = '-';
                }
                $waring_msg[] = '本月同比值：'.$res_val1;
            }
            $val_array[] = $res_val1;
        }
        elseif ($compare_month == 2) {
            //上月数据
            $res_val1 = 0;
            $val_1 = $is_absolute_value?abs($month_val[1]):$month_val[1];
            $waring_msg[] = '上月值：'.$month_val[1];
            if ($reference == 1) {
                //与自定义值
                $res_val1 = $val_1 - $interval_value;
            }
            if ($reference == 2) {
                //上月数据比
                $val_2 = $is_absolute_value?abs($month_val[2]):$month_val[2];
                $res_val1 = $val_1 - $val_2;
                $waring_msg[] = '上上月值：'.$month_val[2];
            }
            if ($reference == 3) {
                //环比
                $val_2 = $is_absolute_value?abs($month_val[2]):$month_val[2];
                if ($val_2 != 0) {
                    $res_val1 = ($val_1 - $val_2)*100/$val_2;
                }  else {
                    $res_val1 = '-';
                }
                $waring_msg[] = '上月环比值：'.$res_val1;
            }
            if ($reference == 4) {
                //同比
                $val_2 = $is_absolute_value?abs($month_val[7]):$month_val[7];
                if ($val_2 != 0) {
                    $res_val1 = ($val_1 - $val_2)*100/$val_2;
                }  else {
                    $res_val1 = '-';
                }
                $waring_msg[] = '上月同比值：'.$res_val1;
            }
            $val_array[] = $res_val1;
        }
        elseif ($compare_month == 3) {
            //连续2月
            $val_1 = $is_absolute_value ? abs($month_val[0]) : $month_val[0];
            $val_2 = $is_absolute_value ? abs($month_val[1]) : $month_val[1];
            $res_val1 = 0;
            $res_val2 = 0;
            $waring_msg[] = '本月值：'.$val_1;
            $waring_msg[] = '上月值：'.$val_2;
            if ($reference == 1) {
                //与自定义值
                $res_val1 = $val_1 - $interval_value;
                $res_val2 = $val_2 - $interval_value;
            }
            if ($reference == 2) {
                //上月数据比
                $d_v_1 = $is_absolute_value ? abs($month_val[1]) : $month_val[1];
                $d_v_2 = $is_absolute_value ? abs($month_val[2]) : $month_val[2];
                $res_val1 = $val_1 - $d_v_1;
                $res_val2 = $val_2 - $d_v_2;
                $waring_msg[] = '上上月值：'.$month_val[2];
            }
            if ($reference == 3) {
                //环比
                $d_v_1 = $is_absolute_value ? abs($month_val[1]) : $month_val[1];
                $d_v_2 = $is_absolute_value ? abs($month_val[2]) : $month_val[2];
                if ($d_v_2 != 0) {
                    $res_val1 = ($val_1 - $d_v_1) * 100 / $d_v_1;
                    $res_val2 = ($val_1 - $d_v_2) * 100 / $d_v_2;
                } else {
                    $res_val1 = '-';
                    $res_val2 = '-';
                }
                $waring_msg[] = '本月环比值：'.$res_val1;
                $waring_msg[] = '上月环比值：'.$res_val2;
            }
            if ($reference == 4) {
                //同比
                $d_v_1 = $is_absolute_value ? abs($month_val[6]) : $month_val[6];
                $d_v_2 = $is_absolute_value ? abs($month_val[7]) : $month_val[7];
                if ($d_v_2 != 0) {
                    $res_val1 = ($val_1 - $d_v_1) * 100 / $d_v_1;
                    $res_val2 = ($val_1 - $d_v_2) * 100 / $d_v_2;
                } else {
                    $res_val1 = '-';
                    $res_val2 = '-';
                }
                $waring_msg[] = '本月同比值：'.$res_val1;
                $waring_msg[] = '上月同比值：'.$res_val2;
            }
            $val_array = [$res_val1,$res_val2];
        }
        elseif ($compare_month == 4) {
            //连续3月
            $val_1 = $is_absolute_value ? abs($month_val[0]) : $month_val[0];
            $val_2 = $is_absolute_value ? abs($month_val[1]) : $month_val[1];
            $val_3 = $is_absolute_value ? abs($month_val[2]) : $month_val[2];
            $res_val1 = 0;
            $res_val2 = 0;
            $res_val3 = 0;
            $waring_msg[] = '本月值：'.$val_1;
            $waring_msg[] = '上月值：'.$val_2;
            $waring_msg[] = '上上月值：'.$val_3;
            if ($reference == 1) {
                //与自定义值
                $res_val1 = $val_1 - $interval_value;
                $res_val2 = $val_2 - $interval_value;
                $res_val3 = $val_3 - $interval_value;
            }
            if ($reference == 2) {
                //上月数据比
                $d_v_1 = $is_absolute_value ? abs($month_val[1]) : $month_val[1];
                $d_v_2 = $is_absolute_value ? abs($month_val[2]) : $month_val[2];
                $d_v_3 = $is_absolute_value ? abs($month_val[2]) : $month_val[3];
                $res_val1 = $val_1 - $d_v_1;
                $res_val2 = $val_2 - $d_v_2;
                $res_val3 = $val_3 - $d_v_3;
                $waring_msg[] = '上上上月值：'.$month_val[3];
            }
            if ($reference == 3) {
                //环比
                $d_v_1 = $is_absolute_value ? abs($month_val[1]) : $month_val[1];
                $d_v_2 = $is_absolute_value ? abs($month_val[2]) : $month_val[2];
                $d_v_3 = $is_absolute_value ? abs($month_val[2]) : $month_val[3];
                if ($d_v_3 != 0) {
                    $res_val1 = ($val_1 - $d_v_1) * 100 / $d_v_1;
                    $res_val2 = ($val_1 - $d_v_2) * 100 / $d_v_2;
                    $res_val3 = ($val_1 - $d_v_3) * 100 / $d_v_3;
                } else {
                    $res_val1 = '-';
                    $res_val2 = '-';
                    $res_val3 = '-';
                }
                $waring_msg[] = '本月环比值：'.$res_val1;
                $waring_msg[] = '上月环比值：'.$res_val2;
                $waring_msg[] = '上上月环比值：'.$res_val3;
            }
            if ($reference == 4) {
                //同比
                $d_v_1 = $is_absolute_value ? abs($month_val[6]) : $month_val[6];
                $d_v_2 = $is_absolute_value ? abs($month_val[7]) : $month_val[7];
                $d_v_3 = $is_absolute_value ? abs($month_val[7]) : $month_val[8];
                if ($d_v_3 != 0) {
                    $res_val1 = ($val_1 - $d_v_1) * 100 / $d_v_1;
                    $res_val2 = ($val_1 - $d_v_2) * 100 / $d_v_2;
                    $res_val3 = ($val_1 - $d_v_3) * 100 / $d_v_3;
                } else {
                    $res_val1 = '-';
                    $res_val2 = '-';
                    $res_val3 = '-';
                }
                $waring_msg[] = '本月同比值：'.$res_val1;
                $waring_msg[] = '上月同比值：'.$res_val2;
                $waring_msg[] = '上上月同比值：'.$res_val3;
            }
            $val_array = [$res_val1,$res_val2,$res_val3];
        }
        elseif ($compare_month == 5) {
            //累计2月
            $val_1 = $month_val[0]+$month_val[1];
            $waring_msg[] = '累计2月值：'.$val_1;
            $val_1 = $is_absolute_value ? abs($val_1) : $val_1;
            $res_val1 = 0;
            if ($reference == 1) {
                //与自定义值
                $res_val1 = $val_1 - $interval_value;
            }
            if ($reference == 2) {
                //上月数据比
                $d_v_1 = $month_val[2] + $month_val[3];
                $waring_msg[] = '累计2月的上月值：'.$d_v_1;
                $d_v_1 = $is_absolute_value ? abs($d_v_1) : $d_v_1;
                $res_val1 = $val_1 - $d_v_1;
            }
            if ($reference == 3) {
                //环比
                $d_v_1 = $month_val[2] + $month_val[3];
                $d_v_1 = $is_absolute_value ? abs($d_v_1) : $d_v_1;
                if ($d_v_1 != 0) {
                    $res_val1 = ($val_1 - $d_v_1) * 100 / $d_v_1;
                }
                $waring_msg[] = '累计2月环比值：'.$res_val1;
            }
            if ($reference == 4) {
                //同比
                $d_v_1 = $month_val[6] + $month_val[7];
                $d_v_1 = $is_absolute_value ? abs($d_v_1) : $d_v_1;
                if ($d_v_1 != 0) {
                    $res_val1 = ($val_1 - $d_v_1) * 100 / $d_v_1;
                }
                $waring_msg[] = '累计2月同比值：'.$res_val1;
            }
            $val_array = [$res_val1];
        }
        elseif ($compare_month == 6) {
            //累计3月
            $val_1 = $month_val[0] + $month_val[1] + $month_val[3];
            $waring_msg[] = '累计3月值：'.$val_1;
            $val_1 = $is_absolute_value ? abs($val_1) : $val_1;
            $res_val1 = 0;
            if ($reference == 1) {
                //与自定义值
                $res_val1 = $val_1 - $interval_value;
            }
            if ($reference == 2) {
                //上月数据比
                $d_v_1 = $month_val[3] + $month_val[4] + $month_val[5];
                $waring_msg[] = '累计3月的上月值：'.$d_v_1;
                $d_v_1 = $is_absolute_value ? abs($d_v_1) : $d_v_1;
                $res_val1 = $val_1 - $d_v_1;
            }
            if ($reference == 3) {
                //环比
                $d_v_1 = $month_val[3] + $month_val[4] + $month_val[5];
                $d_v_1 = $is_absolute_value ? abs($d_v_1) : $d_v_1;
                if ($d_v_1 != 0) {
                    $res_val1 = ($val_1 - $d_v_1) * 100 / $d_v_1;
                } else {
                    $res_val1 = '-';
                }
                $waring_msg[] = '累计3月环比值：'.$res_val1;
            }
            if ($reference == 4) {
                //同比
                $d_v_1 = $month_val[6] + $month_val[7] + $month_val[8];
                $d_v_1 = $is_absolute_value ? abs($d_v_1) : $d_v_1;
                if ($d_v_1 != 0) {
                    $res_val1 = ($val_1 - $d_v_1) * 100 / $d_v_1;
                } else {
                    $res_val1 = '-';
                }
                $waring_msg[] = '累计3月同比值：'.$res_val1;
            }
            $val_array = [$res_val1];
        }
        return ['val'=>$val_array,'waring_msg'=>$waring_msg];
    }
    //根据计算出来的值获取结果(计算每个维度数据的每个跪着组的结果)
    public static function countRowRes($val_array,$rule_row) {
        $symbol = $rule_row['symbol'];
        $val1 = $rule_row['value1'];
        $val2 = $rule_row['value2'];
        //$abs = $rule_row['is_absolute_value'];
        $is_true = [];
        foreach ($val_array as $k=>$val_a) {
//            if ($val_a != '-' && $abs) {
//                $val_a = abs($val_a);
//            }
            $is_true[$k] = 0;
            switch ($symbol) {
                case 1:
                    if ($val_a != '-') {
                        if ($val_a > $val1) {
                            $is_true[$k] = 1;
                        }
                    }
                    break;
                case 2:
                    if ($val_a != '-') {
                        if ($val_a >= $val1) {
                            $is_true[$k] = 1;
                        }
                    }
                    break;
                case 3:
                    if ($val_a != '-') {
                        if ($val_a < $val1) {
                            $is_true[$k] = 1;
                        }
                    }
                    break;
                case 4:
                    if ($val_a != '-') {
                        if ($val_a <= $val1) {
                            $is_true[$k] = 1;
                        }
                    }
                    break;
                case 5:
                    if ($val_a != '-') {
                        if ($val_a == $val1) {
                            $is_true[$k] = 1;
                        }
                    }
                    break;
                case 6:
                    if ($val_a != '-') {
                        if ($val_a >= $val1 && $val_a <= $val2) {
                            $is_true[$k] = 1;
                        }
                    }
                    break;
            }
        }
        if (in_array(0,$is_true) || !count($is_true)) {
            return 0;
        } else {
            return 1;
        }
    }
    //保存设置等级数据
    public static function setLevelRelation($id,$res_data,$waring_name) {
        //取国家和维度苏剧
        $conntry_code = [];
        $weidu_data = [];
        foreach ($res_data as $k=>$v) {
            if ($k == -1) {
                $weidu_data = $v;
                break;
            } else {
                $conntry_code[] = $k;
                $weidu_data = array_merge($weidu_data,$v);
            }
        }
        $weidu_data = array_unique($weidu_data);
        $db = dbFMysql::getInstance();
        //维度不是sku就去查sku
        $res_list = [];
        if (self::$leve_type != 3) {
            $msku_list = [];
            $key_ = self::$leve_type == 1?'asin':'parentAsin';
            foreach (self::$year_list as $year=>$m_date_list) {
                $db->table("table_month_count_$year")
                    ->where("where is_delete = 0 and localSku<> ''")
                    ->whereIn($key_,$weidu_data)
                    ->field('localSku as sku,countryCode as country_code,'.$key_);
                if (count($conntry_code)) {
                    $db->whereIn('countryCode',$conntry_code);
                }
                $list = $db
                    ->groupBy(['localSku,countryCode',$key_])
                    ->list();
                $msku_list = array_merge($msku_list,$list);
            }
            foreach ($res_data as $k=>$v) {
                $sku_c_list = self::$rule_country_cata_sku[$k]??[];
                if (count($v)) {
                    foreach ($msku_list as $v2) {
                        //国家相等，并且维度在计算当中
                        if ($v2['country_code'] == $k && in_array($v2[self::$leve_type_key],$v)) {
                            //国家可用sku
                            if (in_array($v2['sku'],$sku_c_list)) {
                                $res_list[] = [
                                    self::$leve_type_key=>$v2[self::$leve_type_key],
                                    'country_code'=>$v2['country_code'],
                                    'sku'=>$v2['sku'],
                                    'weidu_key'=>$v2[$key_]
                                ];
                            }
                        }
                    }
                }
            }
        } else {
            foreach ($res_data as $k=>$v) {
                $sku_c_list = self::$rule_country_cata_sku[$k]??[];
                if (count($v)) {
                    foreach ($v as $v1) {
                        if (in_array($v1,$sku_c_list)) {
                            $res_list[] = [
                                'country_code'=>$k,
                                'sku'=>$v1,
                                'weidu_key'=>$v1
                            ];
                        }
                    }
                }
            }
        }
        //获取sku并查询对应的运营
        $sku_s = array_column($res_list,'sku');
        if (count($sku_s)) {
            $msku_list = [];
            foreach (self::$year_list as $year=>$m_date_list) {
                $db->table("table_month_count_$year")
                    ->where("where is_delete = 0")
                    ->whereIn('localSku',$sku_s)
                    ->field('localSku as sku,project_id,yunying_id');
                $list = $db
                    ->groupBy(['localSku,project_id,yunying_id'])
                    ->list();
                $msku_list = array_merge($msku_list,$list);
            }
            $project_ids = array_column($msku_list,'project_id');
            //项目组长
            $project_ = [];
            $project_list = $db->table('project')
                ->whereIn('id',$project_ids)
                ->field('id,user_id')
                ->list();
            foreach ($project_list as $p_item) {
                $project_[$p_item['id']] = $p_item;
            }
            foreach ($res_list as $k=>$v) {
                $res_list[$k]['yunying_id'] = [];
                $res_list[$k]['zuzhang_id'] = [];
                foreach ($msku_list as $k1=>$v1) {
                    if ($v['sku'] == $v1['sku']) {
                        $res_list[$k]['yunying_id'][] = $v1['yunying_id'];
                        if (isset($project_[$v1['project_id']])) {
                            $res_list[$k]['zuzhang_id'][] = $project_[$v1['project_id']]['user_id'];
                        }
                        unset($res_list[$k][$k1]);
                    }
                }
            }
        }
        //删除关系
        $db->table('goods_waring')
            ->where('where waring_id=:id and m_date=:m_date',['id'=>$id,'m_date'=>self::$m_date])
            ->delete();
        //删除审核数据
        $db->table('goods_waring_check')
            ->where('where waring_id=:id and m_date=:m_date',['id'=>$id,'m_date'=>self::$m_date])
            ->delete();
        $dimension_name = self::$leve_type_key;
        $save_goods_waring_ = [];
        $save_check_waring_ = [];
        foreach ($res_list as $sku_data) {
            $sku = $sku_data['sku'];
            $country_code = $sku_data['country_code'];
            $r_key = $sku_data[self::$leve_type_key].'_'.$country_code;
            $reason_txt = array_values(array_unique(self::$waring_reason[$r_key]));
            $reason_txt_ = json_encode($reason_txt,JSON_UNESCAPED_UNICODE);
            //本月值为0的这些数据。除了毛利率不达标预警这个需要预警，其他规则是不用预警的-2025-02-07(财务提出的需求)
            if (self::$this_waring_rule['waring_name'] != '毛利率不达标预警') {
                if ($reason_txt_ == '["本月值：0.00"]') {
                    continue;
                }
            }
            $save_goods_waring_[] =  [
                'm_date'=>self::$m_date,
                'sku'=>$sku,
                'waring_id'=>$id,
                'country_code'=>$country_code,
                'yunying_id'=>json_encode(array_values(array_unique($sku_data['yunying_id']))),
                'zuzhang_id'=>json_encode(array_values(array_unique($sku_data['zuzhang_id']))),
                'weidu_key'=>$sku_data['weidu_key']
            ];
            $key_ = $sku_data['weidu_key'].'_'.$country_code;
            if (isset($save_check_waring_[$key_])) {
                //运营
                $yunying_ids = $save_check_waring_[$key_]['yunying_ids'];
                $yunying_ids = array_unique(array_merge($yunying_ids,$sku_data['yunying_id']));
                $save_check_waring_[$key_]['yunying_ids'] = $yunying_ids;
                //组长
                $zuzhang_ids = $save_check_waring_[$key_]['zuzhang_ids'];
                $zuzhang_ids = array_unique(array_merge($zuzhang_ids,$sku_data['zuzhang_id']));
                $save_check_waring_[$key_]['zuzhang_ids'] = $zuzhang_ids;
            } else {
                $save_check_waring_[$key_] = [
                    'waring_id'=>$id,
                    'm_date'=>self::$m_date,
                    'weidu_key'=>$sku_data['weidu_key'],
                    'country_code'=>$country_code,
                    'waring_name'=>$waring_name,
                    'rules'=>json_encode(self::$rules_text,JSON_UNESCAPED_UNICODE),
                    'reason_txt'=>$reason_txt_,
                    'dimension'=>$dimension_name,
                    'column_name'=>self::$column_name,
                    'receive_type'=>self::$receive_type,
                    'created_time'=>date('Y-m-d H:i:s'),
                    'yunying_ids'=>array_unique($sku_data['yunying_id']),
                    'zuzhang_ids'=>array_unique($sku_data['zuzhang_id']),
                ];
            }

        }
        //预警数据保存
        $save_check_waring_ = array_values($save_check_waring_);

        if (count($save_check_waring_)) {
            $keys = array_keys($save_check_waring_[0]);
            $list = array_map(function($item) {
                $item['yunying_ids'] = json_encode(array_values( $item['yunying_ids']));
                $item['zuzhang_ids'] = json_encode(array_values( $item['zuzhang_ids']));
                $item = array_values($item);
                return $item;
            }, $save_check_waring_);
            $chunkedArray = array_chunk($list, 500);
            foreach ($chunkedArray as $v) {
                $db->table('goods_waring_check')
                    ->insertBatch($keys,$v);
            }
        }
        //最小维度保存
        if (count($save_goods_waring_)) {
            $keys = array_keys($save_goods_waring_[0]);
            $list = array_map(function($item) {
                $item = array_values($item);
                return $item;
            }, $save_goods_waring_);
            $chunkedArray = array_chunk($list, 500);
            foreach ($chunkedArray as $v) {
                $db->table('goods_waring')
                    ->insertBatch($keys,$v);
            }
        }
    }
    //获取可查询的时间
    public static function getMonthByRules($current_month,$month_type,$reference_type) {
        //月份整理
        foreach ($month_type as $v1) {
            if ($v1 == 1) {
                $year = date('Y',strtotime($current_month));
                $year_list[$year][] = $current_month;
                $month_list[] = $current_month;
            }
            if ($v1 == 2) {
                //上月
                $month = date('Y-m',strtotime("$current_month-01 -1 month"));
                $year = date('Y',strtotime($month));
                $month_list[] = $month;
                $year_list[$year][] = $month;
            }
            if ($v1 == 3 || $v1 == 5) {
                $year = date('Y',strtotime($current_month));
                $year_list[$year][] = $current_month;
                $month_list[] = $current_month;
                //上月
                $month = date('Y-m',strtotime("$current_month-01 -1 month"));
                $year = date('Y',strtotime($month));
                $month_list[] = $month;
                $year_list[$year][] = $month;
            }
            if ($v1 == 4 || $v1 == 6) {
                $year_list[$year][] = $current_month;
                $year = date('Y',strtotime($current_month));
                $month_list[] = $current_month;
                //上月
                $month = date('Y-m',strtotime("$current_month-01 -1 month"));
                $year = date('Y',strtotime($month));
                $month_list[] = $month;
                $year_list[$year][] = $month;
                //上上月
                $month =  date('Y-m', strtotime("$current_month-01 -2 month"));
                $month_list[] = $month;
                $year = date('Y',strtotime($month));
                $year_list[$year][] = $month;
            }
        }
        //比较对象-获取查询数据的时间
        foreach ($reference_type as $v) {
            if ($v == 2 || $v == 3) {
                if (in_array(1,$month_type)) {
                    //上月
                    $month = date('Y-m',strtotime("$current_month-01 -1 month"));
                    $year = date('Y',strtotime($month));
                    $month_list[] = $month;
                    $year_list[$year][] = $month;
                }
                if (in_array(2,$month_type) || in_array(3,$month_type)) {
                    //上上月
                    $month = date('Y-m',strtotime("$current_month-01 -2 month"));
                    $year = date('Y',strtotime($month));
                    $month_list[] = $month;
                    $year_list[$year][] = $month;
                }
                if (in_array(4,$month_type)) {
                    //上上上月
                    $month = date('Y-m',strtotime("$current_month-01 -3 month"));
                    $year = date('Y',strtotime($month));
                    $month_list[] = $month;
                    $year_list[$year][] = $month;
                }
                if (in_array(5,$month_type)) {
                    for ($i = 2; $i <= 3; $i++) {
                        $month = date('Y-m', strtotime("$current_month-01 -$i month"));
                        $month_list[] = $month;
                        $year = date('Y',strtotime($month));
                        $year_list[$year][] = $month;
                    }
                }
                if (in_array(6,$month_type)) {
                    for ($i = 3; $i <= 5; $i++) {
                        $month = date('Y-m', strtotime("$current_month-01 -$i month"));
                        $month_list[] = $month;
                        $year = date('Y',strtotime($month));
                        $year_list[$year][] = $month;
                    }
                }
            }
            if ($v == 4) {
                if (in_array(1,$month_type)) {
                    //本月同比时间
                    $month =  date('Y-m', strtotime("$current_month-01 -1 year"));
                    $month_list[] = $month;
                    $year = date('Y',strtotime($month));
                    $year_list[$year][] = $month;
                }
                if (in_array(2,$month_type)) {
                    //上月
                    $month = date('Y-m',strtotime("$current_month-01 -13 month"));
                    $year = date('Y',strtotime($month));
                    $month_list[] = $month;
                    $year_list[$year][] = $month;
                }
                if (in_array(3,$month_type) || in_array(5,$month_type)) {
                    //连续两月
                    for ($i = 12; $i <=13; $i++) {
                        $month =  date('Y-m', strtotime("$current_month-01 -$i month"));
                        $month_list[] = $month;
                        $year = date('Y',strtotime($month));
                        $year_list[$year][] = $month;
                    }
                }
                if (in_array(4,$month_type) || in_array(6,$month_type)) {
                    //连续3月
                    for ($i = 12; $i <=14; $i++) {
                        $month =  date('Y-m', strtotime("$current_month-01 -$i month"));
                        $month_list[] = $month;
                        $year = date('Y',strtotime($month));
                        $year_list[$year][] = $month;
                    }
                }
            }
        }
        foreach ($year_list as &$v) {
            $v = array_values(array_unique($v));
        }

        $month_list = array_values(array_unique($month_list));
        self::$month_list = $month_list;
        self::$year_list = $year_list;
        //用于计算的年
        $rule_month[] = $current_month;
        for ($i = 1; $i <= 5; $i++) {
            $month =  date('Y-m', strtotime("$current_month-01 -$i month"));
            $rule_month[] = $month;
        }
        for ($i = 12; $i <=14; $i++) {
            $month =  date('Y-m', strtotime("$current_month-01 -$i month"));
            $rule_month[] = $month;
        }
        self::$rule_month = $rule_month;
        foreach ($year_list as $year=>$v) {
            mskuReportModel::creatMskuReportTable($year);
        }
    }


    //根据分类id获取产品的sku(不可循环调用）
    public static function getSkuByCateId(array $cate_cids) {
        $db = dbFMysql::getInstance();
        if (count($cate_cids)) {
            $goods_list = $db->table('goods')
                ->whereIn('cid',$cate_cids)
                ->field('sku,cid')
                ->list();
        } else {
            $goods_list = $db->table('goods')
                ->field('sku,cid')
                ->list();
        }
        //产品id对应的产看sku
        $cate_sku_r = array_column($goods_list,'cid','sku');
        self::$sku_cateid_relation = $cate_sku_r;
        $sku_list = array_column($goods_list,'sku');
        return $sku_list;
    }
}