<?php
namespace plugins\shop\controller;


use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use plugins\shop\form\messagesFrom;
use plugins\shop\models\configModel;
use plugins\shop\models\shopPlanModel;
use plugins\shop\models\userModel;

class shopPlanController extends baseController
{
    public function getList() {
        $paras_list = ['asin','product_name','upc','trademark_id','yunying_id','trademark_holder','seller_id','shop_email',
            'country_code','dep_id','ipp', 'opr', 'status', 'page', 'page_size'];

        $param = array_intersect_key($_GET, array_flip($paras_list));
        $param['page'] = intval($param['page'] ?? 1);
        $param['page_size'] = intval($param['page_size'] ?? 10);
        $model = new shopPlanModel();

        $data = $model->getList($param);
        returnSuccess($data);
    }

    public function getDetail() {
        $paras_list = ['id'];
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $model = new shopPlanModel();
        $data = $model->getDetail($param);
        returnSuccess($data);
    }

    public function edit() {
        $paras_list = ['id','upc', 'seller_id', 'ipp', 'opr', 'contract_manager', 'contract_group', 'remark'];

        $param = array_intersect_key($_POST, array_flip($paras_list));
        $listing_id = $param['id'] ?? '';
        $model = new shopPlanModel();
        $detail = $model->getByListingId($listing_id);
        unset($param['id']);
        $param['listing_id'] = $listing_id;

        if (!$detail) {
            $model->add($param);
        } else {
            $model->edit($param, $detail['id'], $detail);
        }
        returnSuccess([],'编辑成功');
    }

    public function openPlan() {
        $paras_list = ['id'];
        $param = array_intersect_key($_POST, array_flip($paras_list));
        $listing_id = $param['id'] ?? '';
        $model = new shopPlanModel();
        $detail = $model->getByListingId($listing_id);
        if (!$detail) {
            returnError('没有透明计划');
        }
        $model->edit(['status' => $model::STATUS_OPEN], $detail['id'], $detail, '开启计划');
        returnSuccess([], '开启成功');
    }

    public function closePlan() {
        $paras_list = ['id'];
        $param = array_intersect_key($_POST, array_flip($paras_list));
        $listing_id = $param['id'] ?? '';
        $model = new shopPlanModel();
        $detail = $model->getDetail(['id' => $listing_id]);
        if (!$detail['shop_plan_id']) {
            returnError('没有透明计划');
        }
        $one = $model->getByListingId($listing_id);
        $model->edit(['status' => $model::STATUS_CLOSE_WAITING, 'close_operator' => userModel::$qwuser_id], $one['id'], $one, '关闭计划');

        $users = configModel::noticeUser('plan', 'audit');
        $user_name = userModel::$wname;
        if (!empty($users)) {
            messagesFrom::senMeg($users, 1 , "【{$user_name}】关闭了透明计划ID为{$listing_id}【{$detail['asin']}_{$detail['country']}】的透明计划，请及时审核", $listing_id, '', '透明计划关闭审核');
        }
        returnSuccess([], '关闭成功');
    }

    public function auditClosePlan() {
        $paras_list = ['id', 'audit_status', 'audit_remark'];
        $param = array_intersect_key($_POST, array_flip($paras_list));
        $listing_id = $param['id'] ?? '';
        $model = new shopPlanModel();
        $detail = $model->getByListingId($listing_id);
        if (!$detail) {
            returnError('没有透明计划');
        }
        $one = $model->getByListingId($listing_id);
        $model->edit([
            'status' => $param['audit_status'] == 1 ? $model::STATUS_CLOSE : $model::STATUS_CLOSE_FAIL,
        ], $one['id'], $one,'审核', $param['audit_remark']);

        if ($param['audit_status']) {
            $users = configModel::noticeUser('plan', 'close');
            if (!empty($users)) {
                messagesFrom::senMeg($users, 1 , "透明计划ID为{$listing_id}【{$detail['asin']}_{$detail['country']}】的透明计划已通过关闭审核，请及时关闭该透明计划", $listing_id, '', '关闭透明计划');
            }
        } else {
            $users = redisCached::getUserInfo();
            $users = array_column($users, 'user_wid', 'user_id');
            messagesFrom::senMeg([$users[$one['close_operator']]], 1, "您提交的关闭透明计划ID为{$listing_id}【{$detail['asin']}_{$detail['country']}】的透明计划审核结果为不通过，已自动将透明计划调整为开启状态", $listing_id, '', '透明计划关闭审核不通过');
        }
        returnSuccess([], '审核成功');
    }

    // 获取日志
    public static function getLog()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $db = dbShopMysql::getInstance();
        $list = $db->table('operation_log')->where('where table_name = :table_name and table_id = :table_id',
            ['table_name' => 'domain', 'table_id' => $id])->order('id desc')->list();

        $users =  redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        $model = new shopPlanModel();

        foreach ($list as &$item) {
            $item['operator_name'] = $users[$item['operator']] ?? '';
            $attach = json_decode($item['attach'], true);
            $item['before'] = $attach['before'] ?? null;
            $item['before'] = $model->formatItem($item['before']);
            $item['after'] = $attach['after'] ?? null;
            $item['after'] = $model->formatItem($item['after']);
            unset($item['attach']);
        }
        returnSuccess($list);
    }



}
