<?php

namespace financial\controller;

use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;
use financial\form\levelForm;
use financial\form\sellerForm;
use financial\form\waringRulesForm;
use financial\models\userModel;
use financial\form\levelRulesForm;
use Rap2hpoutre\FastExcel\FastExcel;


class levelController
{
    public static array $countryCode;//国家列表
    public static array $ColumnList;//规则列表
    public static array $level_rules_month;//
    public static array $level_rules_value;//
    public static array $level_rules_symbol;//
    // 获取等级列表或根据名字查询等级
    public static function getLevel() {
        try {
            // 定义所需参数列表
            $paras_list = array('level_name', 'times');

            // 组织参数
            $param = arrangeParam($_POST, $paras_list);

            // 获取数据库实例
            $db = dbFMysql::getInstance();
            $dby = dbMysql::getInstance();
            if (
                !empty($param['level_name'])||
                !empty($param['times']) ||
                ($param['times'] != '[]' &&
                $param['level_name'] != '')
            ) {
                $level_name = $param['level_name'];
                $db->table('goods_level')->where('where is_delete = 0');
                $db->andWhere('level_name LIKE :level_name',
                        ['level_name' => '%' . $level_name . '%']);

                // 处理时间过滤条件
                $timestampArray = self::processTimesParam($param['times']);
                if (!empty($timestampArray)) {
                    //开始时间
                    $Start_Time = $timestampArray[0];
                    $End_Time = $timestampArray[1];
                    $db->andWhere('start_time BETWEEN :Start_Time AND :End_Time and is_delete = 0',
                        ['Start_Time' => $Start_Time, 'End_Time' => $End_Time]);
                }

                // 查询结果
                $level_list = $db->order('id DESC')->list();

                // 如果查询结果为空，返回错误
                if (empty($level_list)) {
                    returnError("未找到名为 $level_name 的等级");
                }

                // 获取并映射用户名
                $user_map = self::getUserMap($level_list, $dby);

                // 替换 updated_user_id 为对应的用户名
                foreach ($level_list as &$level) {
                    $level['updated_user_name'] = $user_map[$level['updated_user_id']] ?? '';
                    unset($level['updated_user_id']);
                }

                // 返回成功信息和等级数据
                returnSuccess($level_list, "获取等级成功");
            } else {
                // 获取所有等级
                $levels = $db->table('goods_level')
                    ->where('where is_delete = 0')
                    ->order('id DESC')
                    ->list();

                // 获取并映射用户名
                $user_map = self::getUserMap($levels, $dby);

                // 替换 updated_user_id 为对应的用户名
                foreach ($levels as &$level) {
                    $level['updated_user_name'] = $user_map[$level['updated_user_id']] ?? '未知用户';
                    unset($level['updated_user_id']);
                }

                // 返回成功信息和所有等级数据
                returnSuccess($levels, "获取等级列表成功");
            }
        } catch (\Exception $e) {
            returnError("获取等级列表失败: " . $e->getMessage());
        }
    }

    /**
     * 处理 `times` 参数并转换为时间戳数组
     *
     * @param string|array $times
     * @return array
     */
    private static function processTimesParam($times) {
        $timestampArray = [];
        // 处理 `times` 参数
        if (!empty($times)) {
            $times = json_decode($times, true);
            foreach ($times as $dateStr) {
                $dateTime = strtotime($dateStr);
                if ($dateTime) {
                    $timestampArray[] = $dateTime;
                }
            }
        }
        return $timestampArray;
    }

    /**
     * 获取用户 ID 到用户名的映射
     *
     * @param array $level_list
     * @param object $dby
     * @return array
     */
    private static function getUserMap($level_list, $dby) {
        // 获取所有的 updated_user_id
        $user_ids = array_column($level_list, 'updated_user_id');
        $user_ids = array_unique($user_ids);

        // 查询对应的用户名
        $users = $dby->table('qwuser')
            ->whereIn('id', $user_ids)
            ->field('id, wname')
            ->list();

        // 创建 user_id 到 name 的映射
        $user_map = [];
        foreach ($users as $user) {
            $user_map[$user['id']] = $user['wname'];
        }

        return $user_map;
    }

    //获取产品等级详情
    public static function getLevelDetail()
    {
        // 定义所需参数列表
        $paras_list = ['id'];
        $request_list = ['id' => '等级ID'];
        // 组织参数
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $data = levelForm::getLevelDetail($param);

        returnSuccess($data);
    }

    //禁用产品等级名称
    public static function disablelevel()
    {
        try {
            // 定义所需参数列表
            $paras_list = ['id'];
            $request_list = ['id' => '等级ID'];

            // 组织参数
            $param = arrangeParam($_POST, $paras_list, $request_list);

            // 获取数据库实例
            $db = dbFMysql::getInstance();

            // 更新产品等级的禁用属性
            $id = $param['id'];
            $user_id = userModel::$qwuser_id; // 直接从 userModel 获取修改人 id

            // 检查记录是否存在
            $level = $db->table('goods_level')
                ->where('where id = :id', ['id' => $id])
                ->one();
            if ($level['id'] == 1) {
                returnError('不可修改');
            }
//            if ($level['is_fixed'] == 1) {
//                if ($param['is_delete']) {
//                    returnError('内置等级不能删除');
//                }
//            }
            if (!$level) {
                returnError('未找到对应的等级项目');
            }

            // 更新产品等级状态
            $db->table('goods_level')
                ->where('where id = :id', ['id' => $id])
                ->update(['status' => 0, 'updated_user_id' => $user_id]);

            // 返回成功信息
            returnSuccess('禁用等级成功');
        } catch (\Exception $e) {
            returnError('禁用等级失败: ' . $e->getMessage());
        }
    }

    //删除产品等级名称
    public static function deletelevel()
    {
        // 定义所需参数列表
        $paras_list = ['id'];
        $request_list = ['id' => '等级ID'];

        // 组织参数
        $param = arrangeParam($_POST, $paras_list, $request_list);

        // 获取数据库实例
        $db = dbFMysql::getInstance();

        // 获取修改人 id
        $user_id = userModel::$qwuser_id;

        // 检查记录是否存在
        $level = $db->table('goods_level')
            ->where('where id = :id', ['id' => $param['id']])
            ->one();
        if ($level['id'] == 1) {
            returnError('不可修改');
        }
//        if ($level['is_fixed'] == 1) {
//            if ($param['is_delete']) {
//                returnError('内置等级不能删除');
//            }
//        }
        if (!$level) {
            returnError('未找到对应的等级项目');
        }
        $db->beginTransaction();
        try {
            // 删除产品等级
            $db->table('goods_level')
                ->where('where id = :id', ['id' => $param['id']])
                ->update(['is_delete' => 1, 'updated_user_id' => $user_id]);
            //日志
            levelForm::setLog($db,$level,$param['id'],3);
            $db->commit();
            // 返回成功信息
            returnSuccess('删除等级成功');
        } catch (\Exception $e) {
            $db->rollBack();
            returnError('删除等级失败: ' . $e->getMessage());
        }
    }
    //新增修改
    public static function editlevel()
    {
        // 产品等级名称 是否启用 产品维度 描述 规则
        $paras_list = array('id', 'level_name', 'status', 'type', 'description', 'rules', 'level_type','start_time');
        $param = arrangeParam($_POST, $paras_list);
        $level_name = trim($param['level_name'] ?? '');
        $db = dbFMysql::getInstance();
        // 检查 $level_name 是否为空
        if ($level_name === '') {
            // 处理错误，提示用户输入有效的名称
            returnError('请输入有效的等级名称');
        }
        //生效时间验证
        if (empty($param['start_time'])) {
            returnError('请设置生效时间');
        }
        //验证数据唯一性
        if ($param['level_type']!=3){
            $level_name_check = $db->table('goods_level')
                ->where('where level_name=:level_name and id!=:id and level_type=:level_type',
                    ['level_name' => $level_name,'id'=>$param['id'],'level_type'=>$param['level_type']])
                ->one();
            if ($level_name_check) {
                returnError('等级名称已存在');
            }
        }
        $rule_mount = $db->table('goods_level')->where('where start_time=:start_time and level_name=:level_name',
            ['start_time'=>$param['start_time'],'level_name'=>$param['level_name']])->one();
//        dd($rule_mount);
        if (!empty($rule_mount)){
            returnError('该月份已有此规则');
        }
        // 规则验证
        $rules = levelRulesForm::verifyRules($param['rules']);
        $id = (int)$param['id'];
        $user_id = userModel::$qwuser_id;
        $db->beginTransaction();
        try {
            //修改数据
            if ($id) {
                $waring = $db->table('goods_level')
                    ->where('where id=:id', ['id' => $id])
                    ->one();
//                if ($waring['is_fixed'] == 1) {
//                    if ($param['level_name']!= $waring['level_name']) {
//                        returnError('产品等级名称不可修改');
//                    }
//                    if ($waring['status'] != $param['status']) {
//                        returnError('内置产品的状态不能修改');
//                    }
//                }
                if ($param['level_name']!= $waring['level_name']) {
                    returnError('产品等级名称不可修改');
                }
                if ($waring['status'] != $param['status']) {
                    returnError('内置产品的状态不能修改');
                }
                if (!$waring) {
                    returnError('未找到等级项目');
                }
                // 更新数据
                $update_data = [
                    'status' => (int)$param['status'],
                    'description' => mb_substr($param['description'], 0, 200), // 限制描述长度
                    'rules' => json_encode($rules),
                    'level_name' => $param['level_name'],
                    'type' => (int)$param['type'],
                    'updated_time' => date('Y-m-d H:i:s'),
                    'start_time'=> empty($param['start_time'])?0:strtotime($param['start_time']),
                    'updated_user_id' => $user_id,
                    'level_type' => $param['level_type']
                ];
                $db->table('goods_level')
                    ->where('where id=:id', ['id' => $id])
                    ->update($update_data);
                //日志
                levelForm::setLog($db,$update_data,$id,2);
                $db->commit();
                returnSuccess([], '修改成功');
            } else {
                //检测数据库中是否有同名
                $lel = $db->table('goods_level')
                    ->where('where level_name = :level_name and is_delete = 0', ['level_name' => $level_name])
                    ->one();
                if ($lel){
                    returnError('等级名称已存在');
                }
                //新增数据
                $insert_data = [
                    'level_name' => $param['level_name'],
                    'status' => (int)$param['status'],
                    'type' => (int)$param['type'],
                    'description' => mb_substr($param['description'], 0, 200), // 限制描述长度
                    'rules' => json_encode($rules),
//                    'is_fixed' => (int)$param['is_fixed'],
                    'created_time' => date('Y-m-d H:i:s'),
                    'updated_time' => date('Y-m-d H:i:s'),
                    'start_time'=> empty($param['start_time'])?0:strtotime($param['start_time']),
                    'updated_user_id' => $user_id,
                    'level_type' => $param['level_type']
                ];
                $id = $db->table('goods_level')
                    ->insert($insert_data);
                //日志
                levelForm::setLog($db,$insert_data,$id,1);
                $db->commit();
                returnSuccess([], '新增成功');
            }
        } catch (Exception $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //修改记录
    public static function getEditLog() {
        $id = (int)$_GET['id'];
        $db = dbFMysql::getInstance();
        $list = $db->table('goods_level_log','a')
            ->leftJoinOut('db','qwuser','b','b.id=a.user_id')
            ->where('goods_level_id = :id',['id'=>$id])
            ->field('a.*,b.wname')
            ->order('a.id desc')
            ->list();
        foreach ($list as &$v) {
            $v['start_time'] = empty($v['start_time'])?0:date('Y-m-d',$v['start_time']);
            $v['rules'] = levelForm::getRulestext(json_decode($v['rules'],true));
        }
        returnSuccess($list);
    }
    //配置监控指标
    public function setMonitoring()
    {
        $ids = $_POST['ids'] ?? '[]';
        $ids = json_decode($ids);
        $db = dbFMysql::getInstance();


        // 将 column 表中所有 is_delete = 0 的记录的 is_monitoring 字段设置为 0
        $db->table('column')
            ->where('where is_delete = 0')
            ->update(['is_monitoring_level' => 0]);

        // 如果有指定 ID 列表，将 column 表中这些 ID 的记录的 is_monitoring 字段设置为 1
        if (count($ids)) {
            $db->table('column')
                ->whereIn('id', $ids)
                ->update(['is_monitoring_level' => 1]);
        }

        returnSuccess([], '设置成功');
    }
    //配置是否启用
    public static function enable()
    {
        // 定义所需参数列表
        $paras_list = ['id', 'status'];
        $request_list = ['id' => '等级ID', 'status' => '是否启用'];
        // 组织参数
        $param = arrangeParam($_POST, $paras_list, $request_list);
        // 获取数据库实例
        $db = dbFMysql::getInstance();
        // 获取修改人 id
        $user_id = userModel::$qwuser_id;
        // 修改产品等级的启用状态
        $level =  $db->table('goods_level')
            ->where('where id = :id', ['id' => $param['id']])
            ->one();
        if ($level['status'] == 1) {
            $status = 0;
        } else {
            $status = 1;
        }
        $db->beginTransaction();
        try {
            $db->table('goods_level')
                ->where('where id = :id', ['id' => $param['id']])
                ->update(['status' => $status, 'updated_user_id' => $user_id]);
            $level['status'] = $status;
            levelForm::setLog($db,$level,$param['id'],2);
            $db->commit();
            // 返回成功信息
            returnSuccess("修改启用状态成功");
        } catch (\Exception $e) {
            $db->rollBack();
            returnError("修改启用状态失败: " . $e->getMessage());
        }
    }
    // 获取规则列表
    public static function ColumnList()
    {
        $db = dbFMysql::getInstance();
        self::$ColumnList = $db->table('column')
            ->field('id, column_name')
            ->list();
    }
    // 获取国家列表
    public static function countryCode()
    {
        $db = dbFMysql::getInstance();
        self::$countryCode = $db->table('market')
            ->field('id, country')
            ->list();
    }
    public function rules($rules)
    {
        $rule = json_decode($rules, true);
        $monthMap = array_column(self::$level_rules_month, 'name', 'id');
        $valueMap = array_column(self::$level_rules_value, 'name', 'id');
        $symbolMap = array_column(self::$level_rules_symbol, 'name', 'id');

        foreach ($rule as &$value) {
            if (!empty($value)) {
                $value['market_id'] = $this->processMarketId($value['market_id']);
                if (isset($value['conditions'])) {
                    foreach ($value['conditions'] as &$condition) {
                        $this->processCondition($condition, $monthMap, $valueMap, $symbolMap);
                    }
                }
            }
        }

        return self::formatRulesDescription($rule);
    }
    private function processMarketId($marketId)
    {
        // 如果 marketId 是数组
        if (is_array($marketId)) {
            // 将数组中的每个 ID 映射为对应的国家名称
            $marketId = array_map(function ($id) {
                return isset(self::$countryCode[$id - 1]) ? self::$countryCode[$id - 1]['country'] : '未知国家';
            }, $marketId);

            // 如果数组不为空，将其转换为字符串以便显示
            return empty($marketId) ? '全部国家' : implode(', ', $marketId);
        }

        // 如果 marketId 不是数组
        if (empty($marketId) || $marketId == -1) {
            return '全部国家';
        }

        return isset(self::$countryCode[$marketId - 1]) ? self::$countryCode[$marketId - 1]['country'] : '未知国家';
    }
    private function processCondition(&$condition, $monthMap, $valueMap, $symbolMap)
    {
        foreach ($condition as $key => &$item) {
            switch ($key) {
                case 'index':
                    $item = isset(self::$ColumnList[$item - 1]) ? self::$ColumnList[$item - 1]['column_name'] : '未知规则';
                    break;
                case 'type':
                    $item = $item == 1 ? '且' : '或';
                    break;
                case 'compare_month':
                    $item = $monthMap[$item] ?? '未知月份';
                    break;
                case 'reference':
                    $item = $valueMap[$item] ?? '未知参考值';
                    break;
                case 'symbol':
                    $item = $symbolMap[$item] ?? '未知符号';
                    break;
            }
        }
    }
    public function formatRulesDescription($rules)
    {
        if (is_string($rules)) {
            $rules = json_decode($rules, true);
        }

        $formattedDescription = [];
        foreach ($rules as $rule) {
            $marketDescription = "目标国家：{$rule['market_id']}";
            $conditionsDescription = $this->formatConditions($rule['conditions']);

            $formattedDescription[] = "{$marketDescription}，" . implode('，', $conditionsDescription);
        }

        return implode("\n", $formattedDescription);
    }
    private function formatConditions($conditions)
    {
        $descriptions = [];
        foreach ($conditions as $condition) {
            if (isset($condition['group']) && is_array($condition['group'])) {
                $groupDescriptions = array_map([$this, 'formatCondition'], $condition['group']);
                $descriptions[] = "产品数据条件：[" . implode('；', $groupDescriptions) . "]";
            } else {
                $descriptions[] = "产品数据条件：[" . $this->formatCondition($condition) . "]";
            }
        }
        return $descriptions;
    }
    private function formatCondition($condition)
    {
        $index = $condition['index'];
        $compareMonth = $condition['compare_month'];
        $reference = $condition['reference'];
        $symbol = $condition['symbol'];

        $symbolDescription = $symbol === '区间'
            ? "计算符号：区间，区间值1：{$condition['value1']}，区间值2：{$condition['Interval_value']}"
            : "计算符号：{$symbol}，值1：{$condition['value1']}，值2：{$condition['value2']}";

        return "指标：{$index}，比较月份：{$compareMonth}，对比对象：{$reference}，{$symbolDescription}";
    }
    public function exportTemplate()
    {
        $paras_list = ['level_name','times'];
        $param = arrangeParam($_POST, $paras_list);

        $excelData = [
            ['产品等级' => '产品等级', '是否启用' => '是否启用', '备注' => '备注', '最新修改人' => '最新修改人', '最近一次修改时间' => '最近一次修改时间', '规则' => '规则', '规则月份' => '规则月份'],
        ];

        self::countryCode();
        self::ColumnList();
        $config = config::all('data_financial');
        self::$level_rules_month = $config['level_rules_month'];
        self::$level_rules_value = $config['level_rules_value'];
        self::$level_rules_symbol = $config['waring_rules_symbol'];

        $dby = dbMysql::getInstance();

        $levels = $this->getLevels($param);

        $userMap = array_column($dby->table('qwuser')->field('id, wname')->list(), 'wname', 'id');

        foreach ($levels as $level) {
//            dd($level['start_time']);
            $updatedUser = $userMap[$level['updated_user_id']] ?? '';
            $status = $level['status'] == 1 ? '启用' : '禁用';
            $rules = self::rules($level['rules']);
            if (!empty($level['start_time'])){
                $start_time = date('Y-m', $level['start_time']);
            }else{
                $start_time = '';
            }

            $excelData[] = [
                '产品等级' => $level['level_name'],
                '是否启用' => $status,
                '备注' => $level['description'],
                '最新修改人' => $updatedUser,
                '最近一次修改时间' => $level['updated_time'],
                '规则' => $rules,
                '规则月份' => $start_time,
            ];
        }
        $exportPath = $this->getExportPath();
        $this->createDirectoryIfNeeded(dirname($exportPath));

        unset($excelData[0]); // 删除标题行
        try {
            (new FastExcel($excelData))->export($exportPath);
            returnSuccess('/public_financial/temp/level/data/userexported_projects.xlsx');
        } catch (\Exception $e) {
            returnError('导出失败: ' . $e->getMessage());
        }
    }
    //导出列表查询
    private function getLevels($param)
    {
        $dbf = dbFMysql::getInstance();
        $dbf->table('goods_level')->where('where is_delete = 0');
        if (!empty($param['level_name'])){
            $dbf->andWhere('level_name LIKE :level_name', ['level_name' => '%' . $param['level_name'] . '%']);
        }
        if (!empty($param['times'])){
            // 处理时间过滤条件
            $timestampArray = self::processTimesParam($param['times']);
            if (!empty($timestampArray)) {
                //开始时间
                $Start_Time = $timestampArray[0];
                $End_Time = $timestampArray[1];
                $dbf->andWhere('start_time BETWEEN :Start_Time AND :End_Time and is_delete = 0',
                    ['Start_Time' => $Start_Time, 'End_Time' => $End_Time]);
            }
        }
        $list = $dbf->field('level_name, status, is_fixed, description, updated_user_id, updated_time, rules,start_time')
            ->list();
        return $list;
    }
    private function getExportPath()
    {
        $projectRoot = dirname(__DIR__, 2); // 假设控制器位于 'project_root/financial/controller' 目录下
        return $projectRoot . '/public_financial/temp/level/data/userexported_projects.xlsx';
    }
    private function createDirectoryIfNeeded($dir)
    {
        if (!file_exists($dir)) {
            if (!mkdir($dir, 0777, true) && !is_dir($dir)) {
                returnError('无法创建目录: ' . $dir);
            }
        }
    }

}
