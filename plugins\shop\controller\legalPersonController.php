<?php

namespace plugins\shop\controller;

use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use Exception;
use plugins\shop\models\legalPersonFeeModel;
use plugins\shop\models\legalPersonModel;
use Rap2hpoutre\FastExcel\FastExcel;

class legalPersonController extends baseController
{
    // 获取列表
    public function getList()
    {
        $param = array_intersect_key($_GET, array_flip([
            'name', 'recommender', 'id_card', 'id_card_address', 'phone', 'internal_coordinator',
            'education', 'work_status', 'gender', 'amazon_available',
            'register_date', 'id_card_expire', 'update_at',
            'page', 'page_size'
        ]));

        $model = new legalPersonModel();
        $list = $model->getList($param);

        // 添加费用申请
        $fee_ids = array_column($list['list'], 'fee_id');
        $fee_ids = array_values(array_filter($fee_ids));
        if ($fee_ids) {
            $feeModel = new legalPersonFeeModel();
            $fee_list = $feeModel->getList(['id' => $fee_ids]);
            $fee_list = array_column($fee_list, null, 'id');
            foreach ($list['list'] as &$item) {
                if (isset($item['fee_id']) && isset($fee_list[$item['fee_id']])) {
                    $item['fee_apply'] = $fee_list[$item['fee_id']];
                }
            }
        }

        returnSuccess($list);
    }

    // 新增
    public static function add()
    {
        $model = new legalPersonModel();
        $param = $_POST;
        $id = $_POST['id'] ?? 0;
        
        try {
            $model->dataValidCheck($param);
            // 检查身份证号唯一性
            $detail = $model->getByIdCard($param['id_card'] ?? '', $id);
            if ($detail) {
                returnError('身份证号已存在');
            }
        } catch (Exception $e) {
            returnError($e->getMessage());
        }

        if ($id) {
            // 验证数据正确性
            $detail = $model->getById($id);
            if (!$detail) {
                returnError('数据不存在');
            }
            $model->edit($param, $id, $detail);

            // 注册公司
            !$detail['is_register_company'] && $param['is_register_company'] && $model->applyCompany($id);

            returnSuccess([], '编辑成功');
        } else {
            $param['type'] = '自有法人';
            $id = $model->add($param);

            // 注册公司
            $param['is_register_company'] && $model->applyCompany($id);

            returnSuccess([], '添加成功');
        }
    }

    // 详情
    public static function detail()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $model = new legalPersonModel();
        $detail = $model->getById($id);
        if (!$detail) {
            returnError('数据不存在');
        }

        $detail = $model->formatItem($detail);

        // 分别获取法人费用和推荐费用
        $legal_fee_rule = $detail['legal_fee']['rule'] ?? [];
        $recommend_fee_rule = $detail['recommend_fee']['rule'] ?? [];

        $legal_fee_rule_year = [];
        if (!empty($legal_fee_rule)) {
            $apply_year = $detail['legal_fee']['apply_year'] ?? [];
            foreach ($legal_fee_rule as $item) {
                for ($start = $item['start']; $start <= $item['end']; $start++) {
                    $legal_fee_rule_year[$start] = [
                        'year' => $start,
                        'price' => $item['price'],
                        'flag' => !in_array($start, $apply_year) ? 1 : 0
                    ];
                }
            }
        }
        $recommend_fee_rule_year = [];
        if (!empty($recommend_fee_rule)) {
            $apply_year = $detail['recommend_fee']['apply_year'] ?? [];
            foreach ($recommend_fee_rule as $item) {
                for ($start = $item['start']; $start <= $item['end']; $start++) {
                    $recommend_fee_rule_year[$start] = [
                        'year' => $start,
                        'price' => $item['price'],
                        'flag' => !in_array($start, $apply_year) ? 1 : 0
                    ];
                }
            }
        }

        $detail['recommend_fee_year'] = array_values($recommend_fee_rule_year);
        $detail['legal_fee_year'] = array_values($legal_fee_rule_year);

        // 公司
        $company = dbShopMysql::getInstance()
            ->table('company')
            ->field('id, company_name,register_date,register_status,company_status')
            ->where('legal_person_id = :legal_person_id', ['legal_person_id' => $id])
            ->list();
        $detail['company'] = $company ?: [];

        returnSuccess($detail);
    }

    // 获取日志
    public static function getLog()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $db = dbShopMysql::getInstance();
        $list = $db->table('operation_log')->where('where table_name = :table_name and table_id = :table_id', 
        ['table_name' => 'legal_person', 'table_id' => $id])->order('id desc')->list();

        $users =  redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        $model = new legalPersonModel();
        $maps = $model::getMaps();

        foreach ($list as &$item) {
            $item['operator_name'] = $users[$item['operator']] ?? '';
            $attach = json_decode($item['attach'], true);
            $item['before'] = $attach['before'] ?? null;
            $item['before'] = $model->formatItem($item['before'], $maps);
            $item['after'] = $attach['after'] ?? null;
            $item['after'] = $model->formatItem($item['after'], $maps);
            unset($item['attach']);
        }
        returnSuccess($list);
    }

    // 获取费用申请记录
    public static function getFeeList()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $feeModel = new legalPersonFeeModel();
        $list = $feeModel->getList(['legal_person_id' => $id]);

        returnSuccess($list);
    }

    // 申请费用
    public static function applyFee()
    {
        $paras_list = array('id', 'legal_fee_year', 'recommend_fee_year', 'remark');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        if (empty($param['id'])) {
            returnError('法人ID不能为空');
        }

        $model = new legalPersonModel();
        $model->dataValidCheck($param, $paras_list);

        if (empty($param['legal_fee_year']) && empty($param['recommend_fee_year'])) {
            returnError('费用不能为空');
        }

        try {
            $model->applyFee($param['id'], $param);
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
        returnSuccess([], '申请成功');
    }

    // 确认费用
    public static function confirmFee()
    {
        $paras_list = array('id', 'fee_detail', 'remark');
        $param = array_intersect_key($_POST, array_flip($paras_list));

        if (!$param['id']) {
            returnError('ID不能为空');
        }

        try {
            $model = new legalPersonModel();
            $model->dataValidCheck($param, $paras_list);

            $model->confirmFee($param['id'], $param);
            returnSuccess([], '申请成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    // 撤销费用申请
    public static function cancelFee()
    {
        try {
            if (empty($_POST['id'])) {
                returnError('法人ID不能为空');
            }

            $model = new legalPersonModel();
            $model->cancelFee($_POST['id']);
            returnSuccess([], '撤销成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    // 删除
    public static function delete()
    {
        if (empty($_POST['id'])) {
            returnError('ID不能为空');
        }
        try {
            $model = new legalPersonModel();
            $model->delete($_POST['id']);

            // 重新生成缓存
            redisCached::del(redisCached::YWX_LEGAL_PERSON);
        } catch (Exception $e) {
            returnError($e->getMessage());
        }

        returnSuccess([], '删除成功');
    }

    /**
     * 批量导入
     */
    public function import()
    {
        $paras_list = array('excel_src');
        $request_list = ['excel_src' => '表格链接'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

        if (empty($param['excel_src'])) {
            returnError('表格链接不能为空');
        }

        $excel_url = SELF_FK . $param['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格文件不存在');
        }

        $data = (new FastExcel())->import($excel_url, function ($row) {
            // 将每一项 trim 去空格，再判断整行是否为空
            $cleaned = collect($row)->map(function ($value) {
                if (is_string($value)) {
                    return trim((string)$value);
                }
                return $value;
            });
            // 全部为空则跳过
            if ($cleaned->filter()->isEmpty()) {
                return null;
            }
            return $cleaned->toArray(); // 返回处理后的数据
        });
        $first = $data['0'];
        if (empty($first['法人名字']) || empty($first['注册公司']) || empty($first['法人手机号']) || empty($first['是否可用于注册亚马逊']) ||
            empty($first['身份证号']) || empty($first['性别']) || empty($first['出生日期']) ||
            empty($first['身份证地址']) || empty($first['身份证有效起始日期']) || empty($first['身份证有效结束日期']) ||
            !isset($first['常驻城市']) ||!isset($first['工作情况']) || !isset($first['学历']) || !isset($first['推荐渠道']) ||
            !isset($first['推荐人']) || !isset($first['关系说明']) || empty($first['报名日期']) ||
            empty($first['法人的对接人-公司内部（也就是店铺费用支付对接人）']) || !isset($first['法人配合度']) ||
            empty($first['信用卡情况']) || !isset($first['法人信用卡']) || !isset($first['信用卡有效开始日期']) || !isset($first['信用卡有效结束日期']) ||
            empty($first['协议开始日期']) || empty($first['协议结束日期']) || empty($first['年限']) ||
            empty($first['协议公司']) || empty($first['协议盖章发放日期']) || !isset($first['协议原件存放编号']) ||
            !isset($first['法人费收款方式']) || !isset($first['法人费收款站账户名']) || !isset($first['法人费收款账号']) ||
            !isset($first['法人费对接群']) || !isset($first['推荐费收款方式']) || !isset($first['推荐费收款账户名']) ||
            !isset($first['推荐费收款账号']) || !isset($first['推荐费对接群']) || !isset($first['备注']))
        {
            returnError('表头错误');
        }

        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_id', 'user_name');

        $credit_card = redisCached::getCreditCard();
        $credit_card = array_column($credit_card, null, 'card_number');

        $model = new legalPersonModel();
        $paras_list = $model::$paras_list;
        $import_data = [];
        $error_data = [];

        foreach ($data as $row) {
            $error_msg = [];
            empty($row['法人名字']) && $error_msg[] = '法人名字不能为空';
            empty($row['注册公司']) && $error_msg[] = '注册公司不能为空';
            empty($row['法人手机号']) && $error_msg[] = '法人手机号不能为空';
            empty($row['是否可用于注册亚马逊']) && $error_msg[] = '是否可用于注册亚马逊不能为空';
            empty($row['身份证号']) && $error_msg[] = '身份证号不能为空';
            empty($row['性别']) && $error_msg[] = '性别不能为空';
            empty($row['出生日期']) && $error_msg[] = '出生日期不能为空';
            $birth_date = null;
            try {
                $birth_date = $row['出生日期']->format('Y-m-d');
                if (empty($row['出生日期']) || strtotime($birth_date) === false) {
                    $error_msg[] = '出生日期格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '出生日期格式错误';
            }
            empty($row['身份证地址']) && $error_msg[] = '身份证地址不能为空';
            empty($row['身份证有效起始日期']) && $error_msg[] = '身份证有效起始日期不能为空';
            $start_date = null;
            try {
                $start_date = $row['身份证有效起始日期']->format('Y-m-d');
                if (empty($row['身份证有效起始日期']) || strtotime($start_date) === false) {
                    $error_msg[] = '身份证有效起始日期格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '身份证有效起始日期格式错误';
            }
            empty($row['身份证有效结束日期']) && $error_msg[] = '身份证有效结束日期不能为空';
            $end_date = null;
            try {
                $end_date = $row['身份证有效结束日期']->format('Y-m-d');
                if (empty($row['身份证有效结束日期']) || strtotime($end_date) === false) {
                    $error_msg[] = '身份证有效结束日期格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '身份证有效结束日期格式错误';
            }
            empty($row['报名日期']) && $error_msg[] = '报名日期不能为空';
            $register_date = null;
            try {
                $register_date = $row['报名日期']->format('Y-m-d');
                if (empty($row['报名日期']) || strtotime($register_date) === false) {
                    $error_msg[] = '报名日期格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '报名日期格式错误';
            }
            empty($row['法人的对接人-公司内部（也就是店铺费用支付对接人）']) && $error_msg[] = '法人的对接人-公司内部（也就是店铺费用支付对接人）不能为空';
            $internal_coordinator = null;
            if (!isset($users[$row['法人的对接人-公司内部（也就是店铺费用支付对接人）']])) {
                $error_msg[] = '法人的对接人不存在';
            } else {
                $internal_coordinator = $users[$row['法人的对接人-公司内部（也就是店铺费用支付对接人）']];
            }
            empty($row['信用卡情况']) && $error_msg[] = '信用卡情况不能为空';
            $credit_card_id = null;
            if (!empty($row['法人信用卡'])) {
                if (!isset($credit_card[$row['法人信用卡']])) {
                    $error_msg[] = '法人信用卡不存在';
                } else {
                    $credit_card_id = $credit_card[$row['法人信用卡']];
                }
            }
            empty($row['协议开始日期']) && $error_msg[] = '协议开始日期不能为空';
            $agreement_start_date = null;
            try {
                $agreement_start_date = $row['协议开始日期']->format('Y-m-d');
                if (empty($row['协议开始日期']) || strtotime($agreement_start_date) === false) {
                    $error_msg[] = '协议开始日期格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '协议开始日期格式错误';
            }
            empty($row['协议结束日期']) && $error_msg[] = '协议结束日期不能为空';
            $agreement_end_date = null;
            try {
                $agreement_end_date = $row['协议结束日期']->format('Y-m-d');
                if (empty($row['协议结束日期']) || strtotime($agreement_end_date) === false) {
                    $error_msg[] = '协议结束日期格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '协议结束日期格式错误';
            }
            empty($row['年限']) && $error_msg[] = '年限不能为空';
            empty($row['协议公司']) && $error_msg[] = '协议公司不能为空';
            empty($row['协议盖章发放日期']) && $error_msg[] = '协议盖章发放日期不能为空';

            $item_data = [
                'name' => $row['法人名字'],
                'type' => '自有法人',
                'is_register_company' => $row['注册公司'] == '是' ? 1 : 0,
                'phone' => $row['法人手机号'],
                'amazon_available' => $row['是否可用于注册亚马逊'],
                'id_card' => $row['身份证号'],
                'gender' => $row['性别'],
                'birth_date' => $birth_date,
                'id_card_address' => $row['身份证地址'],
                'id_card_expire' => ($start_date && $end_date) ? [
                    $start_date,
                    $end_date
                ] : null,
                'city' => $row['常驻城市'] ?? '',
                'work_status' => $row['工作情况'] ?? '',
                'education' => $row['学历'] ?? '',
                'recommend_channel' => $row['推荐渠道'] ?? '',
                'recommender' => $row['推荐人'] ?? '',
                'relation_with_recommender' => $row['关系说明'] ?? '',
                'register_date' => $register_date,
                'internal_coordinator' => $internal_coordinator,
                'cooperation_level' => $row['法人配合度'] ?? '',
                'credit_card_status' => $row['信用卡情况'] ?? '',
                'credit_card_id' => $credit_card_id,
                'agreement' => [
                    'date' => ($agreement_start_date && $agreement_end_date) ? [
                        $agreement_start_date,
                        $agreement_end_date
                    ] : null,
                    'year' => $row['年限'] ?? '',
                    'company' => $row['协议公司'] ?? '',
                    'sign_date' => $row['协议盖章发放日期'] ?? '',
                    'no' => $row['协议原件存放编号'] ?? ''
                ],
                'legal_fee' => [
                    'type' => $row['法人费收款方式'] ?? '',
                    'name' => $row['法人费收款站账户名'] ?? '',
                    'account' => $row['法人费收款账号'] ?? '',
                    'group' => $row['法人费对接群'] ?? ''
                ],
                'recommend_fee' => [
                    'type' => $row['推荐费收款方式'] ?? '',
                    'name' => $row['推荐费收款账户名'] ?? '',
                    'account' => $row['推荐费收款账号'] ?? '',
                    'group' => $row['推荐费对接群'] ?? ''
                ],
                'remark' => $row['备注'] ?? ''
            ];
            $check_row = $model->dataValidCheck($item_data, $paras_list, false);
            if (!empty($check_row['error'])) {
                $error_msg = array_merge($error_msg, $check_row['error']);
            }

            if (!empty($error_msg)) {
                $row['失败原因'] = implode(';', $error_msg);
                $error_data[] = $row;
                continue;
            }

            $import_data[] = $item_data;
            $id = $model->add($item_data, '导入');
        }

        if (count($error_data) > 0) {
            // 使用项目根目录动态构建导出文件路径
            $projectRoot = SELF_FK;
            $src = '/public/shop/temp/error/user_import_errors' . time() . rand(100, 1000) . '.xlsx';
            $exportPath = $projectRoot . $src;

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径

            returnSuccess(['error_file' => $src, 'error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $error_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $import_data], '导入成功');
        }

    }

    /**
     * 导出
     */
    public function export()
    {
        $param = array_intersect_key($_POST, array_flip([
            'name', 'recommender', 'id_card', 'id_card_address', 'phone', 'internal_coordinator',
            'education', 'work_status', 'gender', 'amazon_available',
            'register_date', 'id_card_expire', 'update_at',
            'keys', 'ids'
        ]));

        $model = new legalPersonModel();
        $data = $model->getList($param, 'id desc', true);

        if (empty($data)) {
            returnError('没有可导出的数据');
        }

        $export_data = [];
        foreach ($data as $item) {
            $keys = [
                '法人', '是否注册公司', '法人手机号', '手机可用于注册亚马逊', '身份证号', '性别', '出生日期', '身份证地址',
                '身份证开始有效期', '身份证结束有效期', '常住城市', '法人工作情况', '学历', '推荐渠道', '推荐人', '关系说明', '报名日期',
                '内部对接人', '法人配合度', '信用卡情况', '法人信用卡',
                '信用卡有效期', '协议开始日期', '协议结束日期', '年限', '协议公司',
                '协议盖章发放日期', '协议原件存放编号', '法人费收款方式', '法人费收款账户名',
                '法人费收款账号', '法人费收款账对接群', '推荐费收款方式', '推荐费收款账户名',
                '推荐费收款卡号', '推荐费收款对接群'
            ];
            $sortedItem = [];
            foreach ($keys as $key) {
                if (!empty($param['keys']) && !in_array($key, $param['keys'])) continue;
                if ($key == '是否注册公司') {
                    $sortedItem[$key] = $item['是否注册公司'] == 1 ? '是' : '否';
                } elseif ($key == '身份证开始有效期') {
                    $sortedItem[$key] = $item['身份证有效期'][0] ?? '';
                } elseif ($key == '身份证结束有效期') {
                    $sortedItem[$key] = $item['身份证有效期'][1] ?? '';
                } elseif ($key == '信用卡有效期') {
                    $sortedItem[$key] = $item[$key] ? implode('~', $item[$key]) : '';
                } elseif ($key == '协议开始日期') {
                    $sortedItem[$key] = $item['协议信息']['date'][0] ?? '';
                } elseif ($key == '协议结束日期') {
                    $sortedItem[$key] = $item['协议信息']['date'][1] ?? '';
                } elseif ($key == '年限') {
                    $sortedItem[$key] = $item['协议信息']['year'] ?? '';
                } elseif ($key == '协议公司') {
                    $sortedItem[$key] = $item['协议信息']['company'] ?? '';
                } elseif ($key == '协议盖章发放日期') {
                    $sortedItem[$key] = $item['协议信息']['sign_date'] ?? '';
                } elseif ($key == '协议原件存放编号') {
                    $sortedItem[$key] = $item['协议信息']['no'] ?? '';
                } elseif ($key == '法人费收款方式') {
                    $sortedItem[$key] = $item['法人费用']['type'] ?? '';
                } elseif ($key == '法人费收款账户名') {
                    $sortedItem[$key] = $item['法人费用']['name'] ?? '';
                } elseif ($key == '法人费收款账号') {
                    $sortedItem[$key] = $item['法人费用']['account'] ?? '';
                } elseif ($key == '法人费收款账对接群') {
                    $sortedItem[$key] = $item['法人费用']['group'] ?? '';
                } elseif ($key == '推荐费收款方式') {
                    $sortedItem[$key] = $item['推荐费']['type'] ?? '';
                } elseif ($key == '推荐费收款账户名') {
                    $sortedItem[$key] = $item['推荐费']['name'] ?? '';
                } elseif ($key == '推荐费收款卡号') {
                    $sortedItem[$key] = $item['推荐费']['account'] ?? '';
                } elseif ($key == '推荐费收款对接群') {
                    $sortedItem[$key] = $item['推荐费']['group'] ?? '';
                } else {
                    $sortedItem[$key] = $item[$key] ?? '';
                }
            }
            $export_data[] = $sortedItem;
        }

        $filePath = '/public/shop/temp/receive_account_export_' . date('YmdHis') . '.xlsx';
        (new FastExcel($export_data))->export(SELF_FK.$filePath);

        // 导出数据
        returnSuccess(['src' => $filePath], '导出成功');

    }

    public function editBatch()
    {
        $paras_list = array('data');
        $request_list = ['data' => '编辑数据'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

        if (count($param['data']) > 500) {
            returnError('批量编辑数据不能超过500条');
        }

        $id_cards = array_column($param['data'], 'id_card');
        $id_cards = array_unique($id_cards);
        if (count($id_cards) != count($param['data'])) {
            returnError('批量编辑数据不能有重复的身份证号码');
        }

        $model = new legalPersonModel();
        $result = $model->editBatch($param['data']);

        returnSuccess($result);

    }

}