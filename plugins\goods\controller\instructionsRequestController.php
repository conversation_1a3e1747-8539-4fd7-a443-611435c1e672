<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/29 10:36
 */

namespace  plugins\goods\controller;

use plugins\goods\common\authenticationCommon;
use plugins\goods\form\configFrom;
use plugins\goods\form\downLoadFrom;
use plugins\goods\form\goodsMattersFrom;
use plugins\goods\form\instructionsRequestFrom;
use plugins\goods\form\messagesFrom;
use plugins\goods\models\userModel;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;
use core\lib\log;

class instructionsRequestController
{
    //获取列表
    public function getList() {
        $paras_list = array('request_name','qwuser_id','status','expected_time','order_by','page','page_size');
        $param = arrangeParam($_POST, $paras_list);
        $db = dbMysql::getInstance();
        $db->table('instructions_request','ir');
        $db->leftJoin('qwuser','q','q.id = ir.qwuser_id');
        $db->leftJoin('qwuser','q2','q2.id = ir.allocation_user_id');
        $db->leftJoin('goods_new','gn','gn.id = ir.goods_id');
            $db->where('where 1=1');
        if (!empty($param['request_name'])) {
            $db->andWhere('and ir.request_name like :request_name',['request_name'=>'%'.$param['request_name'].'%']);
        }

        if ((int)$param['qwuser_id']) {
            $db->andWhere('and ir.qwuser_id=:qwuser_id',['qwuser_id'=>$param['qwuser_id']]);
        }
        if ((int)$param['status'] > -1) {
            if ($param['status'] == 2) {
                $db->andWhere('and (ir.status=2 or ir.status=3)');
            } else {
                $db->andWhere('and ir.status=:status',['status'=>$param['status']]);
            }
        }
        if (!empty($param['expected_time'])) {
            $param['expected_time'] = json_decode($param['expected_time']);
            if (count($param['expected_time'])) {
                $start_time = strtotime($param['expected_time'][0]);
                $end_time = strtotime($param['expected_time'][1]);
                $db->andWhere('and ir.expected_time > :start_time and ir.expected_time < :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
            }
        }

        $order_str = '';
        if (!empty($param['order_by'])) {
            $order_by = json_decode($param['order_by'],true);
            foreach ($order_by as $k=>$ord){
                $order_str .= 'ir.'.$k.' '.$ord.',';
            }
        }
        if (empty($order_str)) {
            $db->order('ir.id desc');
        } else {
            $db->order(trim($order_str,','));
        }

        $db->field('ir.*,q.wname as agent_wname,q2.wname as allocation_wname,gn.goods_name,gn.operator_info');
        $list = $db->pages($param['page'],$param['page_size']);
        foreach ($list['list'] as &$v) {
            $v['operator_info'] = json_decode($v['operator_info'],true);
            $v['operator_pass_wid'] = json_decode($v['operator_pass_wid']);
            $v['expected_time'] = empty($v['expected_time'])?'':$v['expected_time'];
            //运营
            $yunying_ids = array_column($v['operator_info'] ,'id');
            $v['yunying_ids'] = $yunying_ids;
        }
        returnSuccess($list);
    }
    //配置  制作者
    public function setAgentUser() {
        $paras_list = array('request_id','agent_info');
        $param = arrangeParam($_POST, $paras_list);
        if (empty($param['agent_info'])) {
            SetReturn(-1,'参数有误');
        }
        $db = dbMysql::getInstance();
        $reques = $db->table('instructions_request')
            ->where('where id=:id',['id'=>(int)$param['request_id']])
            ->field('id,goods_id,project_id,expected_time,request_name,qwuser_id')
            ->one();
        if ($reques['qwuser_id'] > 0) {
            SetReturn(-1,'该需求已配置，无需重复配置');
        }
        //判断操作权限
        $allocation_user = configFrom::getConfigData(['pic_manage','fs_expected_day']);
        if (!$allocation_user['pic_manage']) {
            SetReturn(-1,'图片管理员未配置，请联系系统管理员');
        }
        $manage_info = json_decode($allocation_user['pic_manage'],true);
        $wids = array_column($manage_info,'id');
        if (!in_array(userModel::$qwuser_id,$wids)) {
            SetReturn(-1,'非图片管理员，无权配置制作者');
        }
        $db->beginTransaction();
        try {
            $agent_info = json_decode($param['agent_info'],true);
            $qwuser_id = $agent_info[0]['id'];
            //需求修改信息
            $expected_day = $allocation_user['fs_expected_day'];
            $expected_time = strtotime("+{$expected_day} days", time());
            $db->table('instructions_request')
                ->where('where id=:id',['id'=>$reques['id']])
                ->update([
                    'qwuser_id'=>$qwuser_id,
                    'status'=>1,
                    'allocation_user_id'=>userModel::$qwuser_id,
                    'begin_time'=>time(),
                    'expected_time'=>$expected_time,
                ]);
            //完成配置制作者待办事项
            $db->table('goods_matters')
                ->where('where type = 5 and model_id=:reques_id and status <> 1 and create_type = 0',['reques_id'=>$reques['id']])
                ->update([
                    'status'=>1,
                    'completion_time'=>time(),
                ]);
            //生成制作待办
            $goods_info = $db->table('goods_new')
                ->where('where id=:goods_id',['goods_id'=>$reques['goods_id']])
                ->field('id,goods_name')
                ->one();
            goodsMattersFrom::addCreateMatter($goods_info['goods_name'].'_说明书',$reques['goods_id'],'图片制作',0,5,$qwuser_id,$reques['id'],$reques['expected_time']);
            //消息通知
            $w_userids = array_column($agent_info,'wid');
            $text = messagesFrom::getMsgTxtForImgRequest(1,$goods_info['goods_name'],'说明书');
            $other_data = [
                'user_id'=>userModel::$qwuser_id,
                'model_id'=>$param['request_id'],
                'msg_type'=>5,
            ];
            messagesFrom::senMeg($w_userids, $text, $other_data);
            $db->commit();
            returnSuccess('','配置成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //上传文件
    public function uploadImgFile() {
        $paras_list = array('request_id','source_file','pdf','images');
        $request_list = ['source_file'=>'源文件','pdf'=>'说明书（pdf版）','images'=>'说明书（竖版）'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $request_id = (int)$param['request_id'];
        $db = dbMysql::getInstance();
        $request = $db->table('instructions_request')
            ->where('where id=:request_id',['request_id'=>$request_id])
            ->one();
        if (!$request) {
            SetReturn(-1,'未找到该需求');
        }
        if (!$request['qwuser_id']) {
            SetReturn(-1,'该需求还未配置制作者');
        }
        if ($request['qwuser_id'] != userModel::$qwuser_id) {
            SetReturn(-1,'非当前配置美工不可上传');
        }
        //源文件
        $source_file = json_decode($param['source_file'],true);
        if (!count($source_file)) {
            SetReturn(-1,'源文件必传');
        }
        $db->beginTransaction();
        try {
            //之前的文件
            $old_file_list = $db->table('imgs_request_collection')
                ->where('where request_id=:request_id and is_delete = 0 and source_type=1',['request_id'=>$request_id])
                ->list();
            $old_url_list = array_column($old_file_list,'url');
            //文件处理
            $new_list_url = [];
            $db->table('imgs_request_collection');
            //竖版
            $images = json_decode($param['images'],true);
            foreach ($images as $image) {
                $new_list_url[] = $image['url'];
                if (!isset($image['thumb_src'])) {
                    SetReturn(-1,'图片缺少压缩路径');
                }
                if (!in_array($image['url'],$old_url_list)) {
                    $name_array = explode('.',$image['file_name']);
                    $insert_data = [
                        'user_id' => userModel::$qwuser_id,
                        'goods_id'=>$request['goods_id'],
                        'request_id'=>$request_id,
                        'file_name'=>$image['file_name'],
                        'url'=>$image['url'],
                        'thumb_src'=>$image['thumb_src'],
                        'created_time'=>date('Y-m-d H:i:s'),
                        'file_type'=>5,
                        'source_type'=>1,
                        'extension'=>end($name_array),
                    ];
                    $db->insert($insert_data);
                }
            }
            //pdf
            $pdf = json_decode($param['pdf'],true);
            foreach ($pdf as $v) {
                $new_list_url[] = $v['url'];
                if (!in_array($v['url'],$old_url_list)) {
                    $name_array = explode('.',$v['file_name']);
                    $insert_data = [
                        'user_id' => userModel::$qwuser_id,
                        'goods_id'=>$request['goods_id'],
                        'request_id'=>$request_id,
                        'file_name'=>$v['file_name'],
                        'url'=>$v['url'],
                        'created_time'=>date('Y-m-d H:i:s'),
                        'file_type'=>4,
                        'source_type'=>1,
                        'extension'=>end($name_array),
                        'thumb_src'=>'',
                    ];
                    $db->insert($insert_data);
                }

            }
            //源文件
            foreach ($source_file as $v) {
                $new_list_url[] = $v['url'];
                if (!in_array($v['url'],$old_url_list)) {
                    $name_array = explode('.', $v['file_name']);
                    $insert_data = [
                        'user_id' => userModel::$qwuser_id,
                        'goods_id' => $request['goods_id'],
                        'request_id' => $request_id,
                        'file_name' => $v['file_name'],
                        'url' => $v['url'],
                        'created_time' => date('Y-m-d H:i:s'),
                        'file_type' => 1,
                        'source_type' => 1,
                        'extension' => end($name_array),
                        'thumb_src'=>'',
                    ];
                    $db->insert($insert_data);
                }
            }

            $del_url = array_diff($old_url_list,$new_list_url);
            if (count($del_url)) {
                $del_ids = [];
                foreach ($del_url as $url) {
                    foreach ($old_file_list as $file) {
                        if ($file['url'] == $url) {
                            $del_ids[] = $url;
                        }
                    }
                }
                //删除源文件
                $db->table('imgs_request_collection')
                    ->whereIn('id',$del_ids)
                    ->update([
                        'is_delete'=>1,
                    ]);
            }

            //设置上传时间
            $db->table('instructions_request')
                ->where('where id=:request_id',['request_id'=>$request_id])
                ->update([
                    'file_update_time'=>date('Y-m-d H:i:s')
                ]);
            $db->commit();
            returnSuccess('','保存成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //获取需求图
    public function getImages(){
        $id = (int)$_GET['request_id'];
        if (!$id) {
            SetReturn(-1,'参数有误');
        }
        $db = dbMysql::getInstance();
        $list = $db->table('imgs_request_collection')
            ->where('where request_id=:request_id and is_delete = 0 and source_type=1',['request_id'=>$id])
            ->list();
        returnSuccess(['list'=>$list]);
    }
    //催办
    public function remind() {
        $paras_list = array('request_id');
        $param = arrangeParam($_POST, $paras_list);
        $db = dbMysql::getInstance();
        $reques = $db->table('instructions_request')
            ->where('where id=:id',['id'=>(int)$param['request_id']])
            ->field('id,qwuser_id,request_name,status,goods_id')
            ->one();
        if ($reques['status'] != 1) {
            SetReturn(-1,'当前状态不可催办');
        }
        $user_info = $db->table('qwuser')
            ->where('where id=:id',['id'=>$reques['qwuser_id']])
            ->field('wid')
            ->one();
        $goods_info = $db->table('goods_new')
            ->where('where id=:goods_id',['goods_id'=>$reques['goods_id']])
            ->field('id,manage_info,goods_name')
            ->one();
        $remind_msg = [
            'wids'=>[$user_info['wid']],
            'msg'=>messagesFrom::getMsgTxtForImgRequest(5,$goods_info['goods_name'],$reques['request_name']),
            'other_data'=>[
                'user_id'=>userModel::$qwuser_id,
                'model_id'=>$param['request_id'],
                'msg_type'=>4
            ],
        ];
        messagesFrom::senMeg($remind_msg['wids'],$remind_msg['msg'],$remind_msg['other_data']);
        returnSuccess('','已提醒');
    }
    //提交
    public function submitFile() {
        $paras_list = array('request_id');
        $param = arrangeParam($_POST, $paras_list);
        $request_id = (int)$param['request_id'];
        $db = dbMysql::getInstance();
        $collection_list = $db->table('imgs_request_collection')
            ->where('where request_id=:request_id and is_delete = 0 and is_confirm = 0 and source_type=1',['request_id'=>$request_id])
            ->field('id,url')
            ->list();
        if (!count($collection_list)) {
            SetReturn(-1,'请先上传文件');
        }
        $reques = $db->table('instructions_request')
            ->where('where id=:id',['id'=>$request_id])
            ->field('id,goods_id,qwuser_id,project_id,expected_time,request_name,allocation_user_id,status')
            ->one();
        if ($reques['status'] != 1) {
            SetReturn(-1,'当前状态不可提交操作');
        }
        if ($reques['qwuser_id'] != userModel::$qwuser_id) {
            SetReturn(-1,'非当前配置美工不可上传');
        }
        $db->beginTransaction();
        try {
            //修改需求状态
            $db->table('instructions_request')
                ->where('where id=:id',['id'=>$request_id])
                ->update(['status'=>2,'is_check'=>0]);
            //生成待办事件 + 处理待办事件为待审 +消息推送
            $goods_info = $db->table('goods_new')
                ->where('where id=:goods_id',['goods_id'=>$reques['goods_id']])
                ->field('id,goods_name')
                ->one();
            instructionsRequestFrom::setCheckMatter($reques,$goods_info);
            $db->commit();
            returnSuccess('','提交成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }

    }
    //审核
    public function checkFs() {
        $paras_list = array('request_id','copy_user','remarks','reason','is_pass');
        $length_data = ['remarks'=>['name'=>'备注','length'=>500],'reason'=>['name'=>'原因','length'=>500]];
        $param = arrangeParam($_POST, $paras_list, [],$length_data);
        $is_pass = (int)$param['is_pass'];
        if (!$is_pass && empty($param['reason'])) {
            SetReturn(-1,'请填写不通过原因');
        }
        if (empty($param['copy_user'])) {
            $copy_user = [];
        } else {
            $copy_user = json_decode($param['copy_user'],true);
        }

        $remarks = $is_pass==1?$param['remarks']:$param['reason'];
        $db = dbMysql::getInstance();
        $request = $db->table('instructions_request')
            ->where('where id=:id',['id'=>(int)$param['request_id']])
            ->field('id,status,goods_id,project_id,allocation_user_id,qwuser_id,request_name,is_check,expected_time')
            ->one();
        if (!$request) {
            SetReturn(-1,'未找到说明书任务');
        }
        if ($request['status'] != 2) {
            SetReturn(-1,'当前状态不可审核');
        }
        if ($request['is_check'] != 0) {
            SetReturn(-1,'切勿重复操作');
        }
        authenticationCommon::authFsRequrest();
        $goods_info = $db->table('goods_new')
            ->where('where id=:goods_id',['goods_id'=>$request['goods_id']])
            ->field('id,manage_info,goods_name,operator_info')
            ->one();
        $db->beginTransaction();
        try {
            //审核状态处理
            instructionsRequestFrom::setPassStatus($is_pass,$request,$goods_info,$remarks);
            //消息推送给抄送人 +运营人员
            instructionsRequestFrom::sendMsgForCheck($is_pass,$request,$goods_info,$copy_user,$remarks);
            $db->commit();
            SetReturn(0,'操作成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //运营审核
    public function checkOperator() {
        $paras_list = array('request_id','copy_user','remarks','reason','is_pass');
        $length_data = ['remarks'=>['name'=>'备注','length'=>500],'reason'=>['name'=>'原因','length'=>500]];
        $param = arrangeParam($_POST, $paras_list,[],$length_data);
        $is_pass = (int)$param['is_pass'];
        if (!$is_pass && empty($param['reason'])) {
            SetReturn(-1,'请填写不通过原因');
        }
        if (empty($param['copy_user'])) {
            $copy_user = [];
        } else {
            $copy_user = json_decode($param['copy_user'],true);
        }

        $remarks = $is_pass==1?$param['remarks']:$param['reason'];
        $db = dbMysql::getInstance();
        $request = $db->table('instructions_request')
            ->where('where id=:id',['id'=>(int)$param['request_id']])
            ->field('id,goods_id,status,project_id,allocation_user_id,qwuser_id,request_name,is_check,expected_time,status,operator_pass_wid')
            ->one();
        if (!$request) {
            SetReturn(-1,'未找到说明书任务');
        }
        if ($request['status'] != 3) {
            SetReturn(-1,'当前状态不可审核');
        }
        if ($request['is_check'] != 1) {
            SetReturn(-1,'该任务还未审核通过');
        }
        $goods_info = $db->table('goods_new')
            ->where('where id=:goods_id',['goods_id'=>$request['goods_id']])
            ->field('id,manage_info,goods_name,operator_info')
            ->one();
        authenticationCommon::authFsOperatorRequrest($goods_info['operator_info'],$request['operator_pass_wid']);
        $db->beginTransaction();
        try {
            //审核状态处理
            instructionsRequestFrom::setOperatorPassStatus($is_pass,$request,$goods_info,$remarks);
            //消息推送给抄送人 + 运营人员
            instructionsRequestFrom::sendMsgForOperatorCheck($is_pass,$request,$goods_info,$copy_user,$remarks);
            $db->commit();
            SetReturn(0,'操作成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //更换美工
    public function changeAgent() {
        $paras_list = array('request_id','agent_info','remarks');
        $param = arrangeParam($_POST, $paras_list);
        if (empty($param['agent_info'])) {
            SetReturn(-1,'参数有误');
        }
        $db = dbMysql::getInstance();
        $reques = $db->table('instructions_request')
            ->where('where id=:id',['id'=>(int)$param['request_id']])
            ->field('id,status,goods_id,project_id,expected_time,request_name,qwuser_id')
            ->one();
        if ($reques['status'] != 1) {
            SetReturn(-1,'进行中的需求才可以更换美工');
        }
        $agent_info = json_decode($param['agent_info'],true);
        if ($reques['qwuser_id'] == $agent_info[0]['id']) {
            SetReturn(-1,'不可交办给当前美工');
        }
        //判断操作权限
        $pic_manage = configFrom::getConfigByName('pic_manage');
        if (!$pic_manage) {
            SetReturn(-1,'图片管理员未配置，请联系系统管理员');
        }
        $manage_info = json_decode($pic_manage,true);
        $wids = array_column($manage_info,'id');
        if (!in_array(userModel::$qwuser_id,$wids)) {
            SetReturn(-1,'非图片管理员，无权配置制作者');
        }
        $db->beginTransaction();
        try {
            $qwuser_id = $agent_info[0]['id'];
            //需求修改信息
            $db->table('instructions_request');
            $db->where('where id=:id',['id'=>$reques['id']]);
            $db->update([
                'qwuser_id'=>$qwuser_id,
            ]);
            //更换代办人
            $matter = $db->table('goods_matters')
                ->where('where type = 5 and model_id=:reques_id and status = 0 and create_type = 0',['reques_id'=>$reques['id']])
                ->one();
            if ($matter) {
                goodsMattersFrom::setChangeAgent($matter,$agent_info[0]);
                //消息通知
                $msg = goodsMattersFrom::$change_matter_msg;
                messagesFrom::senMeg($msg[0]['wids'], $msg[0]['msg'], $msg[0]['other_data'],$param['remarks']);
            }
            $db->commit();
            returnSuccess('','更换成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //重新上传说明书文件（需求已完成）
    public function reUploadImgFile() {
        $paras_list = array('request_id','source_file','pdf','images');
        $request_list = ['source_file'=>'源文件','pdf'=>'说明书（pdf版）','images'=>'说明书（竖版）'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $request_id = (int)$param['request_id'];
        $db = dbMysql::getInstance();
        $request = $db->table('instructions_request')
            ->where('where id=:request_id',['request_id'=>$request_id])
            ->one();
        $goods_info = $db->table('goods_new')
            ->where('where id=:goods_id',['goods_id'=>$request['goods_id']])
            ->field('id,manage_info,goods_name,operator_info')
            ->one();
        if (!$request) {
            SetReturn(-1,'未找到该需求');
        }
        if ($request['qwuser_id'] != userModel::$qwuser_id) {
            SetReturn(-1,'非当前配置美工不可上传');
        }
        if ($request['status'] != 4) {
            SetReturn(-1,'当前状态不可重新上传文件');
        }
        $db->beginTransaction();
        try {
            //设置上传时间
            $db->table('instructions_request')
                ->where('where id=:request_id',['request_id'=>$request_id])
                ->update([
                    'file_update_time'=>date('Y-m-d H:i:s')
                ]);
            //源文件处理
            $source_file = json_decode($param['source_file'],true);
            if (!count($source_file)) {
                SetReturn(-1,'源文件必传');
            }
            $pdf = json_decode($param['pdf'],true);
            $images = json_decode($param['images'],true);
            //原有的文件
            $old_files = $db->table('imgs_request_collection')
                ->where('where request_id=:request_id and is_delete = 0 and source_type=1',['request_id'=>$request_id])
                ->list();
            //新文件
            $new_files = [];
            foreach ($source_file as $v) {
                $v['file_type'] = 1;
                $new_files[] = $v;
            }
            foreach ($pdf as $v) {
                $v['file_type'] = 4;
                $new_files[] = $v;
            }
            foreach ($images as $v) {
                $v['file_type'] = 5;
                $new_files[] = $v;
            }
            $old_files_urls = array_column($old_files,'url');
            $new_files_urls = array_column($new_files,'url');
            //删除和新增的文件
            $del_files_urls = array_diff($old_files_urls,$new_files_urls);
            $add_files_urls = array_diff($new_files_urls,$old_files_urls);
            //删除
            $del_ids = [];
            foreach ($del_files_urls as $url_) {
                foreach ($old_files as $file_) {
                    if ($file_['url'] == $url_) {
                        $del_ids[] = $file_['id'];
                        break;
                    }
                }
            }
            $db->table('imgs_request_collection')
                ->whereIn('id',$del_ids)
                ->update(['is_delete'=>1]);
            //新增
            $db->table('imgs_request_collection');
            $is_change_4or5 = 0;//是否修改了说明书竖版和横板
            foreach ($add_files_urls as $url_) {
                foreach ($new_files as $v) {
                    if ($v['url'] == $url_) {
                        if ($v['file_type'] == 4 || $v['file_type'] == 5) {
                            $is_change_4or5 = 1;
                        }
                        $name_array = explode('.',$v['file_name']);
                        $insert_data = [
                            'user_id' => userModel::$qwuser_id,
                            'goods_id'=>$request['goods_id'],
                            'request_id'=>$request_id,
                            'file_name'=>$v['file_name'],
                            'url'=>$v['url'],
                            'created_time'=>date('Y-m-d H:i:s'),
                            'file_type'=>$v['file_type'],
                            'source_type'=>1,
                            'is_confirm'=>1,
                            'thumb_src'=>$v['thumb_src']??'',
                            'extension'=>end($name_array),
                        ];
                        $db->insert($insert_data);
                        break;
                    }
                }
            }
            //横板竖版有修改蔡更新文档库
            if ($is_change_4or5) {
                //将说明书放到产品文档库
                $fs_list =  $db->table('imgs_request_collection')
                    ->where('where request_id=:request_id and is_delete = 0 and is_confirm = 1 and source_type = 1 and (file_type=4 or file_type=5)',['request_id'=>$request['id']])
                    ->list();
                $db->table('goods_project_file')
                    ->where('where goods_id =:goods_id and source_type=1',['goods_id'=>$request['goods_id']])
                    ->update(['is_delete'=>1]);
                foreach ($fs_list as $file) {
                    $db->table('goods_project_file')
                        ->insert([
                            'user_id'=>userModel::$qwuser_id,
                            'wid'=>userModel::$wid,
                            'source_type'=>1,
                            'goods_id'=>$request['goods_id'],
                            'project_id'=>$request['project_id'],
                            'src'=>$file['url'],
                            'extension'=>$file['extension'],
                            'thumb_src'=>$file['thumb_src'],
                            'filename'=>'说明书'.($file['file_type']==4?'pdf版.':'竖版.').$file['extension']
                        ]);
                }

                //通知开发和运维
                $operator_info = json_decode($goods_info['operator_info'],true);
                $goods_manage = json_decode($goods_info['manage_info'],true);
                $send_user = array_merge($operator_info,$goods_manage);
                $other_data = [
                    'user_id'=>userModel::$qwuser_id,
                    'model_id'=>$request['id'],
                ];
                $wids = array_column($send_user,'wid');
                $msg = messagesFrom::getMsgTxtForImgRequest(10,$goods_info['goods_name'],$request['request_name']);
                messagesFrom::senMeg($wids,$msg,$other_data,'');
            }
            $db->commit();
            returnSuccess('','保存成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }


    }

    //单个下载
    public function downLoad() {
        $id = $_POST['id'];
        if (!$id) {
            SetReturn(-1,'参数有误');
        }
        $db = dbMysql::getInstance();
        $img = $db->table('imgs_request_collection')
            ->field('id,url,goods_id')
            ->where('where id=:id',['id'=>$id])
            ->one();
        if (!$img) {
            SetReturn(-1,'未找到图片');
        }
        $base64Data = downLoadFrom::getdownLoadBase64Encode($img['url'],$img['goods_id'],1);
        log::downLoadLog($id, 4);
        returnSuccess(['data'=>$base64Data]);
    }

}