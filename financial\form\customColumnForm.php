<?php

/**
 * @author: zhangguoming
 * @Time: 2024/7/3 16:13
 */

namespace financial\form;

use core\jobs\customColumnCountValJobs;
use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;
use core\lib\rediskeys;
use financial\common\mskuReportBase;
use financial\models\checkoutModel;
use financial\models\customColumnModel;
use financial\models\userModel;
use phpseclib3\Net\SSH2;

class customColumnForm
{
    public static $error_msg = '';
    //欧洲VAT税金key8、海外营销费用key19、福利政策-采购成本key13、福利政策-头程成本key14、福利政策-其他成本key18、预留资金key6
    public static $aggregation_keys = ['key8','key19','key13','key14','key18','key6'];//合并计算字段
    public static function edit($param)
    {
        $dbs = dbFMysql::getInstance();
        $old_data = $dbs->table('custom_column')
            ->where('where id=:id', ['id' => $param['id']])
            ->field('column_name')
            ->one();
        $id = (int)$param['id'];
        $begin_time = date('Y-m', strtotime($param['begin_time']));
        $db = dbFMysql::getInstance();
        $rules = json_decode($param['rules'], true);
        //验证规则数据
        $rules = customColumnModel::verifyRule($rules, (int)$param['show_type']);
        $sort = count($rules) ? 1 : 2;
        $relation_column_ = json_encode(customColumnModel::$relation_column);
        $last_relation_column_ = json_encode(customColumnModel::$last_relation_column);
        $has_oa_key = strpos($relation_column_, 'oa_key_');
        $has_oa_last_key = strpos($last_relation_column_, 'oa_key_');
        if ($has_oa_key !== false || $has_oa_last_key !== false) {
            //有自定义字段
            $sort = (count(customColumnModel::$relation_column) || count(customColumnModel::$last_relation_column)) ? 5 : $sort;
            $sort = $param['show_type'] == 2 ? 6 : $sort;
        } else {
            //无自定义字段
            $sort = (count(customColumnModel::$relation_column) || count(customColumnModel::$last_relation_column)) ? 3 : $sort;
            $sort = $param['show_type'] == 2 ? 4 : $sort;
        }
        if ($id) {
            //毛利润不可添加字段
            //欧洲VAT税金key8、海外营销费用key19、福利政策-采购成本key13、福利政策-头程成本key14、福利政策-其他成本key18、预留资金key6
            $same_array = array_intersect(customColumnModel::$relation_column,self::$aggregation_keys);
            if ($id == 4 && count($same_array)) {
                returnError('毛利润不可添加字段：欧洲VAT税金、海外营销费用、福利政策-采购成本、福利政策-头程成本、福利政策-其他成本、预留资金。报表根据勾选[合并计算字段]自动计算。');
            }
            //修改数据
            $custom_data = $db->table('custom_column')
                ->where('where id=:id', ['id' => $id])
                ->one();
            if ($custom_data['is_lock'] == 1) {
                if ($old_data['column_name'] != $param['column_name']){
                    if ($param['column_name']) {
                        returnError('内置自定义字段名称不能修改');
                    }
                }
            }
            if (!$custom_data) {
                returnError('未查询到该数据');
            }
            $other_custom_data = $db->table('custom_column')
                ->where('where id<>:id and column_name=:column_name', ['id' => $id, 'column_name' => $param['column_name']])
                ->one();
            if ($other_custom_data) {
                returnError('该自定义名称已存在');
            }
            //验证该字段是否再计算
            $redis = (new \core\lib\predisV())::$client;
            $key = 'custom_column_count_val_' . $id;


            if ($redis->exists($key)) {
                returnError('该自定义字段正在计算，请稍后再试');
            }
            $update_data = [
                'column_name' => $param['column_name'],
                'status' => (int)$param['status'],
                'column_type' => (int)$param['column_type'],
                'rules' => json_encode($rules),
                'show_type' => (int)$param['show_type'],
                'begin_time' => $begin_time,
                'description' => $param['description'],
                'updated_time' => date('Y-m-d H:i:s'),
                'updated_user_id' => userModel::$qwuser_id,
                'relation_column' => $relation_column_,
                'last_relation_column' => $last_relation_column_,
                'sort' => $sort
            ];
            $db->beginTransaction();
            try {
                //修改
                $db->table('custom_column')
                    ->where('where id=:id', ['id' => $id])
                    ->update($update_data);
                //日志记录
                self::setLog($id, $custom_data, $update_data);
                //更新数据到列
                self::setColumn($id, (int)$param['status'], $param['column_name'], (int)$param['column_type'], $param['show_type']);
                $db->commit();
                returnSuccess('', '修改成功');
            } catch (ExceptionError $error) {
                $db->rollBack();
                returnSuccess('', '修改失败');
            }
        } else {
            //新增数据
            $other_custom_data = $db->table('custom_column')
                ->where('where column_name=:column_name', ['column_name' => $param['column_name']])
                ->one();

            if ($other_custom_data) {
                returnError('该自定义名称已存在');
            }

            $insert_data = [
                'column_name' => $param['column_name'],
                'status' => (int)$param['status'],
                'column_type' => (int)$param['column_type'],
                'rules' => json_encode($rules),
                'show_type' => (int)$param['show_type'],
                'begin_time' => $begin_time,
                'description' => $param['description'],
                'created_time' => date('Y-m-d H:i:s'),
                'updated_time' => date('Y-m-d H:i:s'),
                'updated_user_id' => userModel::$qwuser_id,
                'relation_column' => $relation_column_,
                'last_relation_column' => $last_relation_column_,
                'sort' => $sort
            ];
            $db->beginTransaction();
            try {
                $id = $db->table('custom_column')
                    ->insert($insert_data);
                $db = dbFMysql::getInstance();

                self::setLog($id, [], $insert_data);
                //更新数据到列
                self::setColumn($id, (int)$param['status'], $param['column_name'], (int)$param['column_type'], $param['show_type']);
                $db->commit();
                returnSuccess('', '添加成功');
            } catch (ExceptionError $error) {
                $db->rollBack();
                returnSuccess('', '添加失败');
            }
        }
    }
    //列字段记录
    public static function setColumn(int $id, int $status, string $column_name, int $column_type, int $show_type)
    {
        $db = dbFMysql::getInstance();
        $column_data = $db->table('column')
            ->where('where custom_id=:custom_id', ['custom_id' => $id])
            ->field('id')
            ->one();
        if ($column_data) {
            $db->table('column')->where('where id=:id', ['id' => $column_data['id']])
                ->update([
                    'column_name' => $column_name,
                    'data_type' => $column_type,
                    'table_index' => 5,
                    'is_delete' => $status == 1 ? 0 : 1,
                    'show_type' => $show_type,
                ]);
        } else {
            if ($status == 1) {
                $db->table('column')
                    ->insert([
                        'key_name' => 'oa_key_' . $id, //键名规则不能更改，应为其他地方要用
                        'custom_id' => $id,
                        'column_name' => $column_name,
                        'data_type' => $column_type,
                        'table_index' => 5,
                        'show_type' => $show_type,
                    ]);
            }
        }
    }
    //修改日志记录
    public static function setLog($id, $olds_data, $new_data)
    {
        $insert_log_data = [
            'custom_id' => $id,
            'created_time' => date('Y-m-d H:i:s'),
            'user_id' => userModel::$qwuser_id,
            'previous_rules' => $new_data['rules'],
            'new_rules' => json_encode($new_data, JSON_UNESCAPED_UNICODE),
            'log_data' => json_encode($new_data, JSON_UNESCAPED_UNICODE) // 将旧数据直接保存到log_data中
        ];

        $db = dbFMysql::getInstance();
        $db->table('custom_column_log')
            ->insert($insert_log_data);
    }
    //数据生成值队列
    public static function countValQueue(array $column_ids, string $m_date)
    {
        $redis = (new \core\lib\predisV())::$client;
        $key = rediskeys::$oa_custom_column_count;
        //没有数据也就不计算了
        $r_data = [
            'm_date' => $m_date,
            'column_id'=>$column_ids[0],
            'used_ids'=>[],
            'column_ids'=>$column_ids,
            'page' => 1,
            'success_count'=>0,
            'total' => 0,
        ];
        $redis->set($key, json_encode($r_data));
        $redis->expire($key, 60 * 60);
        $queue_key = config::get('delay_queue_key', 'app');
        $task = new customColumnCountValJobs(); // 创建任务类实例
        $redis->zAdd($queue_key, [], 0, serialize($task));
    }
    //获取可重新计算的列(并重新计算),只允许计算上一月的
    public static function recalculate($type, $dis = [], $m_date = '')
    {
        if (empty($m_date)) {
            $m_date = date('Y-m', strtotime('-1 month'));
        }
        //验证上月数据是否结账
        $db = dbFMysql::getInstance();
        //查是否历史使用
        $list = $db->table('custom_column_used')
            ->where('m_date=:m_date',['m_date'=>$m_date])
            ->field('custom_id')
            ->whereIn('custom_id')
            ->list();
        $has_history = 0;
        if (count($list)) {
            $has_history = 1;
        } else {
            $db->table('custom_column')
                ->where("where status=1 and begin_time<=:m_date and is_delete = 0 and rules<>'[]' and show_type <> 2", ['m_date' => $m_date]);
            if ($type == 0) {
                $db->whereIn('id', $dis);
            }
            $list = $db->order('sort asc')
                ->field('id as custom_id,column_name,column_type,show_type,begin_time,relation_column,last_relation_column,rules')
                ->list();
        }
        if (count($list)) {
            $column_ids = array_column($list,'custom_id');
            if (!$has_history) {
                //保存结账记录
                $keys = array_merge(array_keys($list[0]),['m_date','created_time']);
                $date_ = [$m_date,date('Y-m-d H:i:s')];
                $list = array_map(function($item) use ($date_) {
                    $item = array_values($item);
                    return array_merge($item,$date_);
                }, $list);
//                $chunkedArray = array_chunk($list, 50);
//                foreach ($chunkedArray as $v) {
//                    $db->table($table_name)
//                        ->insertBatch($keys,$v);
//                }

                $db->table('custom_column_used')
                    ->insertBatch($keys,$list);
            }
            self::countValQueue($column_ids, $m_date);
            return 1;
        } else {
            //没重新计算自定义字段就直接更新产品等级
            runShellTaskForm::setGoodsLeve();
        }
        return 0;
    }

    public static function setLogs($id, $olds_data, $new_data)
    {
        // 初始化插入日志数据数组
        $insert_log_data = [
            'custom_id' => $id,
            'created_time' => date('Y-m-d H:i:s'),
            'user_id' => userModel::$qwuser_id,
            'previous_rules' => isset($olds_data['rules']) ? $olds_data['rules'] : '[]',
            'new_rules' => isset($new_data['rules']) ? $new_data['rules'] : '',
        ];
        // 初始化日志数据数组
        $log_data = [];

        // 定义需要比较的关键字段数组
        $need_key = ["id", "key_name", "column_name", "custom_id", "data_type", "is_monitoring", "is_monitoring_level", "is_delete", "table_index"];

        // 检查并比较每个关键字段的值
        foreach ($need_key as $r_key) {
            if (isset($olds_data[$r_key]) && isset($new_data[$r_key]) && $olds_data[$r_key] != $new_data[$r_key]) {
                $log_data[$r_key] = $new_data[$r_key];
            }
        }

        // 特殊处理规则字段
        if (!isset($log_data['rules']) && isset($new_data['rules']) && $new_data['rules'] != $insert_log_data['previous_rules']) {
            $insert_log_data['new_rules'] = $new_data['rules'];
        }

        // 根据is_delete字段判断操作类型并设置type字段
        if ($new_data['is_delete'] == 1) {
            $insert_log_data['type'] = 3; // 删除操作
        } elseif (empty($olds_data) && !empty($new_data)) {
            $insert_log_data['type'] = 1; // 新增操作
        } elseif (!empty($olds_data) && !empty($new_data)) {
            $insert_log_data['type'] = 2; // 修改操作
        } else {
            $insert_log_data['type'] = null; // 其他情况，根据实际需求设置默认值
        }

        // 将字段修改数据记录到日志数据数组中
        foreach ($log_data as $key => $value) {
            $log_data[$key . '_old'] = isset($olds_data[$key]) ? $olds_data[$key] : null;
        }

        // 将日志数据数组转换为JSON格式
        $insert_log_data['log_data'] = json_encode($log_data, JSON_UNESCAPED_UNICODE);

        // 插入数据库操作
        $db = dbFMysql::getInstance();
        $db->table('custom_column_log')
            ->insert($insert_log_data);
    }

    //自定义字段限制
    public static function customColumnLimit($param)
    {

        $rules = json_decode($param['rules'], true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            returnError("无效的 JSON 格式：" . json_last_error_msg());
        }

        // 获取前端传递的其他参数
        $show_type = $param['show_type'];

        // 获取传递的生效时间
        $begin_time = $param['begin_time'] ?? '';

        // 获取当前的年份和月份
        $current_year_month = date('Y-m');

        // 从配置文件获取合并字段
        $config = config::get('aggregation_key', 'data_financial');


        // 递归检查规则
        self::checkRules($rules, $show_type, $config);
    }

    private static function checkRules($rules, $show_type, $config)
    {
        // 确认 $rules 是数组
        if (!is_array($rules)) {
            returnError("rules 参数必须是数组");
        }
        foreach ($rules as $rule) {

            if (isset($rule['rules']) && is_array($rule['rules'])) {
                // 3. 百分比字段不能选择国家
                if ($show_type == 2 && $rule['type'] == 2) {
                    returnError("百分比字段不能选择国家");
                }
                foreach ($rule['rules'] as $rul) {

                    // 1. 百分比的字段不能再参与自定义字段设置
                    if ($show_type == 2 && isset($rul['column_key']) && strpos($rul['column_key'], 'oa_key') === 0) {
                        returnError("百分比的字段不能再参与自定义字段设置");
                    }

                    // 2. 百分比字段规则中不能存在上月数据
                    if ($show_type == 2 && isset($rul['type']) && $rul['type'] == 2) {
                        returnError("百分比字段规则中不能存在上月数据");
                    }
                    // 检查合并字段
                    foreach ($config as $key) {

                        if (isset($rul['coulmn_key'])) {
                            if ($rul['coulmn_key'] === 'oa_key_' . $key['code']) {
                                returnError("自定义字段添加不能添加合并字段");
                            }
                        } else {
                            // 处理键不存在的情况
                            $rul['coulmn_key'] = null; // 或其他默认值
                        }


                    }
                    // 选择自定义字段做规则时不能作为上月
                    if (strpos($rul['coulmn_key'], 'oa_key') === 0 && $rul['type'] == 2) {
                        returnError("选择自定义字段做规则时不能作为上月");
                    }

                    // 检查自定义字段嵌套
                    if (isset($rul['coulmn_key']) && strpos($rul['coulmn_key'], 'oa_key') === 0) {

                        $nested_rules = self::getCustomColumnRules($rul['coulmn_key']);
                        foreach ($nested_rules as $nested_rule) {
                            foreach ($nested_rule['rules'] as $inrule)
                                if (strpos($inrule['coulmn_key'], 'oa_key') === 0) {
                                    returnError("自定义字段不可包含已包含自定义字段的字段");
                                }
                        }
                    }
                    // 递归检查嵌套的规则列表
                    if (isset($rul['rules']) && is_array($rul['rules'])) {
                        self::checkRules($rul['rules'], $show_type, $config);
                    }
                    if (isset($rul['list']) && is_array($rul['list'])) {
                        self::checkRules($rul['list'], $show_type, $config);
                    }
                }
            }
        }
    }
    //获取自定义字段的规则
    private static function getCustomColumnRules($column_key)
    {

        $db = dbFMysql::getInstance();


        // 查询需要的信息
        $result = $db->table('column')
            ->where('where key_name = :key_name AND is_delete = 0', ['key_name' => $column_key])
            ->list();
        foreach ($result as $value) {

            $resultt = $db->table('custom_column')
                ->where('where id = :id AND is_delete = 0', ['id' => $value['custom_id']])
                ->list();
            if ($resultt) {
                foreach ($resultt as $values) {
                    return self::decodeRules($values['rules']);
                }
            }
        }


        return [];
    }
    //数据排序
    public static function setSort($sort_ids) {
        $sort_ids = json_decode($sort_ids);
        $db = dbFMysql::getInstance();
        foreach ($sort_ids as $k=>$id) {
            $data_sort = $k+1;
            $db->table('custom_column')
                ->where('id = :id',['id'=>$id])
                ->update(['data_sort'=>$data_sort]);
        }
    }

    private static function decodeRules($rules)
    {
        return json_decode($rules, true);
    }
}
