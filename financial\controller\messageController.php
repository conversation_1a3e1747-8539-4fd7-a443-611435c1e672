<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/22 15:07
 */

namespace financial\controller;

use core\lib\db\dbFMysql;
use core\lib\predisV;
use financial\form\messagesFrom;
use financial\models\userModel;
use core\lib\db\dbMysql;

class messageController
{
    public function getList()
    {
        $paras_list = array('is_read','page','page_size','type');
        $param = arrangeParam($_GET, $paras_list);
        $is_read = (int)$param['is_read'];
        $type = (int)$param['type'];
        $db = dbFMysql::getInstance();
        $db->table('messages')
            ->where('where qw_userid=:qw_userid',['qw_userid'=>userModel::$wid]);
        if ($is_read > -1) {
            $db->andWhere('is_read=:is_read',['is_read'=>$is_read]);
        }
        if ($type == 2) {
            $db->andWhere('type=:type',['type'=>$type]);
        }
        $db->field('id,title,qw_userid,text,type,is_read,created_at,remarks');
        $db->order('is_read asc,id desc');
        $list = $db->pages($param['page'],$param['page_size']);
        returnSuccess($list);
    }
    public function getMessageCount() {
        //计数
        $db = dbFMysql::getInstance();
        $project_msg_count = $db->table('messages')
            ->where('where qw_userid=:qw_userid and is_read=0',['qw_userid'=>userModel::$wid])
            ->count();
        returnSuccess(['total_count'=>$project_msg_count]);
    }
    public function getDetail()
    {
        $id = (int)$_GET['id'];
        if (!$id) {
            SetReturn(-1, '参数有误');
        }
        $db = dbFMysql::getInstance();
        $db->table('messages');
        $db->where('where id = '.$id);
        $db->field('id,title,qw_userid,type,text,model_id,created_at,remarks');
        $data = $db->one();
        $db->update(['is_read'=>1]);
        returnSuccess(['data'=>$data]);
    }

    //全部标记已读
    public function setAllRead()
    {
        $db = dbFMysql::getInstance();
        $db->table('messages');
        $db->where('where qw_userid = :qw_userid and is_read = 0',['qw_userid'=>userModel::$wid]);
        $db->update(['is_read'=>1]);
        SetReturn(0, '全部已读');
    }
    //获取企微通知的消息(系统外)
    public function getMsgDetail() {
        $secret_code = $_POST['data']??'';
        if (empty($secret_code)) {
            SetReturn(-1,'参数有误');
        }
        $data = qwMsgDecryption($secret_code);
        $data  = json_decode($data,true);
        $id = $data['id']??0;
        if (!$id) {
            SetReturn(-1,'参数有误');
        }
        $db = dbFMysql::getInstance();
        $db->table('messages');
        $db->where('where id = '.$id);
        $db->field('id,title,qw_userid,type,text,model_id,created_at,remarks');
        $data = $db->one();
        $db->update(['is_read'=>1]);
        returnSuccess(['data'=>$data]);
    }

    public function test() {
        $paras_list = array('qw_userids','msg_type','text','model_id');
        $request_list = array('qw_userids'=>'接收人','msg_type'=>'消息类型','text'=>'内容','model_id'=>'相关数据id');
        $param = arrangeParam($_POST, $paras_list, $request_list);
        messagesFrom::senMsgByNoticeType($param['msg_type'],$param['text'],10);
//        $qw_userids = json_decode($param['qw_userids']);
//        messagesFrom::senMeg($qw_userids,$param['msg_type'],$param['text'],$param['model_id']);
    }
}