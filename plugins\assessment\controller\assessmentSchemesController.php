<?php

namespace plugins\assessment\controller;

use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\db\dbAMysql;
use core\lib\db\dbMysql;
use core\lib\db\dbShopMysql;
use core\lib\ExceptionError;
use core\lib\redisCached;
use Cron\CronExpression;
use DateTime;
use Exception;
use plugins\assessment\form\messagesFrom;
use plugins\assessment\models\assessmentModel;
use plugins\assessment\models\assessmentSchemesModel;
use admin\models\qwuserModel;
use plugins\assessment\models\assessmentTargetsModel;
use plugins\assessment\models\assessmentUsersModel;
use plugins\assessment\models\customCrontabModel;
use plugins\assessment\models\userModel;
use plugins\assessment\models\userRolesModel;
use plugins\salary\models\salaryCalculationModel;
use plugins\salary\models\userInfoModel;
use plugins\shop\models\shopApplyModel;
use plugins\shop\models\trademarkApplyModel;

class assessmentSchemesController
{
    // 获取考核方案列表
    public function getAssessmentSchemesList()
    {
        $paras_list = array('scheme_name', 'assessment_type', 'page', 'page_size', 'light_query');
        $param = arrangeParam($_POST, $paras_list);
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_schemes');
        $adb->where('where is_delete = 0');
        if ($param['scheme_name']) {
            $adb->andWhere('scheme_name like :scheme_name', ['scheme_name' => '%' . $param['scheme_name'] . '%']);
        }
        if ($param['assessment_type']) {
            $adb->andWhere('assessment_type = :assessment_type', ['assessment_type' => $param['assessment_type']]);
        }
        // 用于轻量查询，不需要获取用户信息，减轻数据库压力
        if ($param['light_query']) {
            $adb->field('id,scheme_name');
            $adb->order('id desc');
            $data = $adb->pages($page, $limit);
            returnSuccess($data);
        }
        $adb->order('id desc');
        $data = $adb->pages($page, $limit);


        $user_roles = userRolesModel::getRolesAndUsers();
        $department_users = qwuserModel::getDepartmentUsers();

        $user_id = array_column($data['list'], 'user_id');
        $db = dbMysql::getInstance();
        $users = [];
        if (!empty($user_id)) {
            $users = $db->table('qwuser')->field('id,wid,wname,avatar')->whereIn('id', $user_id)->list();
            $users = array_column($users, null, 'id');
        }

        $department_ids = [];
        foreach ($data['list'] as $item) {
            $item['attach'] = $item['attach'] ? json_decode($item['attach'], true) : null;
            $department_ids[] = $item['attach']['department'];
        }
        $departments = [];
        if (!empty($department_ids)) {
            $departments = $db->table('qwdepartment')->field('wp_id, name')->whereIn('wp_id', $department_ids)->list();
            $departments = array_column($departments, 'name', 'wp_id');
        }

        foreach ($data['list'] as &$item) {
            if ($item['is_auto']) {
                $item['next_auto_time'] = self::calcNextAutoTime($item, $item['next_auto_time']);
            }
            $item['user_name'] = $users[$item['user_id']]['wname'];
            $item['attach'] = $item['attach'] ? json_decode($item['attach'], true) : null;
            $item['assessment_template'] = $item['assessment_template'] ? json_decode($item['assessment_template'], true) : null;
            $item['assessment_scheme_process'] = $item['assessment_scheme_process'] ? json_decode($item['assessment_scheme_process'], true) : null;
            $item['assessment_scheme_process']['4']['formula_text'] = assessmentSchemesModel::getFormulaText($item['assessment_scheme_process']['4'], $departments[$item['attach']['department']]);
            $user_ids = assessmentSchemesModel::getAssessmentSchemesUsers($item, $user_roles, $department_users);
            $item['assessment_users'] = $user_ids;
        }
        unset($item);

        returnSuccess($data);
    }

    // 新增/编辑考核方案
    public function addAssessmentSchemes()
    {
        $paras_list = array('id', 'scheme_name', 'assessment_cycle', 'assessment_type', 'attach', 'assessment_template', 'assessment_scheme_process');
        $length_data = ['scheme_name' => ['name' => '方案名称', 'length' => 20]];
        $request_list = ['scheme_name' => '方案名称', 'assessment_cycle' => '考核周期', 'assessment_type' => '考核类型', 'assessment_template' => '考核模版', 'assessment_scheme_process' => '考核方案流程'];
        $param = arrangeParam($_POST, $paras_list, $request_list, $length_data);

        $adb = dbAMysql::getInstance();
        $adb->table('assessment_schemes');

        // 判断考核方案是否存在
        if ($param['id']) {
            $adb->where('where id = :id and is_delete = 0', ['id' => $param['id']]);
            $is_exist = $adb->one();
            if (!$is_exist) {
                returnError('方案不存在');
            }

            // 判断是否有进行中的绩效考核在使用该方案
            if (in_array($param['id'], assessmentModel::getRunningAssessmentDetail('scheme'))) {
                returnError('有进行中的绩效考核在使用该方案，不允许修改');
            }
        }

        // 判断考核方案名称是否重复
        $adb->table('assessment_schemes');
        $adb->where('where scheme_name = :scheme_name and is_delete = 0', ['scheme_name' => $param['scheme_name']]);
        if ($param['id']) {
            $adb->andWhere('id != :id', ['id' => $param['id']]);
        }
        $is_exist = $adb->one();
        if ($is_exist) {
            returnError('方案名称重复');
        }

        // 校验
        try {
            assessmentSchemesModel::checkAttach(json_decode($param['attach'], true));
            assessmentSchemesModel::checkAssessmentTemplate(json_decode($param['assessment_template'], true));
            assessmentSchemesModel::checkAssessmentSchemeProcess(json_decode($param['assessment_scheme_process'], true));
        } catch (\Throwable $error) {
            returnError($error->getMessage());
        }

        // 关于自动发起的逻辑
        $assessment_scheme_process = json_decode($param['assessment_scheme_process'], true);
        $create_info = $assessment_scheme_process[0];
        $is_auto = $create_info['assessment_launch_type'] == 1 ? 1 : 0;

        $data = [
            'scheme_name'               => $param['scheme_name'],
            'user_id'                   => userModel::$qwuser_id,
            'assessment_cycle'          => $param['assessment_cycle'],
            'assessment_type'           => $param['assessment_type'],
            'attach'                    => $param['attach'],
            'assessment_template'       => $param['assessment_template'],
            'assessment_scheme_process' => $param['assessment_scheme_process'],
            'is_auto'                   => $is_auto,
        ];
        if ($is_auto) {
            $cron_expression = assessmentSchemesModel::getSchemeRuntime($param['assessment_cycle'], $create_info['launch_time']);
            // 创建 CronExpression 对象
            $cron = new CronExpression($cron_expression);
            $currentDate = new DateTime();
            // 计算下一次执行时间
            $next_run = $cron->getNextRunDate($currentDate)->format('Y-m-d H:i:s');
            $assessment_scheme_process[0]['next_runtime'] = $next_run;
            $data['assessment_scheme_process'] = json_encode($assessment_scheme_process);
            $data['next_auto_time'] = $next_run;
        }
        if ($param['id']) {
            $id = $param['id'];
            $adb->table('assessment_schemes')->where('where id = :id and is_delete = 0', ['id' => $param['id']]);
            $res = $adb->update($data);
            if ($res) {
                // 删除原有的定时任务
                $adb->table('custom_crontab');
                $adb->where('where link_id = :link_id and link_type = 1', ['link_id' => $id]);
                $adb->delete();
            }
        } else {
            $res = $adb->table('assessment_schemes')->insert($data);
            $id = $res;
            if (!$res) {
                returnError('fail');
            }
        }
        if ($res) {
            if ($is_auto) {
                $crontab_data = [
                    'is_crontab_task' => 1,
                    'link_id'         => $id,
                    'link_type'       => 1,
                    'cron_expression' => $cron_expression,
                    'runtime'         => $next_run,
                ];
                $adb->table('custom_crontab');
                $adb->insert($crontab_data);
            }

        }
        returnSuccess([], 'success');
    }

    // 删除考核方案
    public function delAssessmentSchemes()
    {
        $id = $_POST['id'];
        if (!$id) returnError('id不能为空');
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_schemes');
        $adb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $is_exist = $adb->one();
        if (!$is_exist) returnError('方案不存在');

        // 判断是否有进行中的绩效考核在使用该方案
        if (in_array($id, assessmentModel::getRunningAssessmentDetail('scheme'))) {
            returnError('有进行中的绩效考核在使用该方案，不允许删除');
        }
        $data = [
            'is_delete' => 1,
            'user_id'   => userModel::$qwuser_id
        ];
        $adb->table('assessment_schemes');
        $adb->where('where id = :id', ['id' => $id]);
        $res = $adb->update($data);
        if ($res) {
            returnSuccess([], 'success');
        } else {
            returnError('fail');
        }
    }

    // 获取考核方案详情
    public function getAssessmentSchemesDetail()
    {
        $id = $_POST['id'];
        if (!$id) returnError('id不能为空');
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_schemes');
        $adb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $detail = $adb->one();
        if (!$detail) returnError('方案不存在');

        $detail['attach'] = $detail['attach'] ? json_decode($detail['attach'], true) : null;
        $department_id = $detail['attach']['department'];
        $department_name = dbMysql::getInstance()->table('qwdepartment')->field('name')->where('where wp_id = :wp_id', ['wp_id' => $department_id])->one()['name'];
        $detail['assessment_template'] = $detail['assessment_template'] ? json_decode($detail['assessment_template'], true) : null;
        $detail['assessment_scheme_process'] = $detail['assessment_scheme_process'] ? json_decode($detail['assessment_scheme_process'], true) : null;
        $detail['assessment_scheme_process']['4']['formula_text'] = assessmentSchemesModel::getFormulaText($detail['assessment_scheme_process']['4'], $department_name);

        // 拼接指标信息
        $target_ids = [];
        foreach ($detail['assessment_template']['list'] as $item) {
            $target_ids[] = $item['id'];
        }
        $targets = [];
        if (!empty($target_ids)) {
            $adb->table('assessment_targets');
            $target_ids_str = implode(',', $target_ids);
            $adb->where('where id in (' . $target_ids_str . ')');
            $targets = $adb->list();

            // 业绩指标
            $assessment_targets_source = config::get('assessment_targets_source','data_assessment');
            $assessment_targets_source = array_column($assessment_targets_source, 'list', 'id');
            $columns = $assessment_targets_source['1'];
            $columns_map = array_column($columns, 'column_name', 'id');

            foreach ($targets as &$item) {
                $item['target_detail'] = $item['target_detail'] ? json_decode($item['target_detail'], true) : null;
                $item['target_text'] = assessmentTargetsModel::getTargetText($item['target_detail'], $item['target_type'], $columns_map);
            }
        }
        $detail['assessment_targets'] = $targets;
        // 计算方案当前实际用户
        $user_ids = assessmentSchemesModel::getAssessmentSchemesUsers($detail);
        $users = [];
        if (!empty($user_ids)) {
            $db = dbMysql::getInstance();
            $users = $db->table('qwuser')
                ->whereIn('id', $user_ids)
                ->field('id, wname, avatar')
                ->list();
        }
        $detail['assessment_users'] = $users;

        returnSuccess($detail);
    }

    // 考核方案发布
    public function publishAssessmentSchemes()
    {
        $paras_list = array('id', 'notice_person', 'users');
        $request_list = ['id' => '方案ID', 'notice_person' => '发布通知人'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_schemes');
        $adb->where('where id = :id and is_delete = 0', ['id' => $param['id']]);
        $scheme = $adb->one();
        if (!$scheme) returnError('方案不存在');
        $scheme['attach'] = $scheme['attach'] ? json_decode($scheme['attach'], true) : null;

        // 发送给方案中的所有人
        if ($param['notice_person'] == 1) {
            $user_ids = assessmentSchemesModel::getAssessmentSchemesUsers($scheme);
        } else { // 发送给指定人员
            $users = json_decode($param['users'], true);
            $user_ids = array_column($users, 'id');
        }

        $adb = dbAMysql::getInstance();
        $adb->table('assessment_schemes');
        $adb->where('where id = :id and is_delete = 0', ['id' => $param['id']]);
        $adb->update(['release_time' => date('Y-m-d H:i:s')]);

        $db = dbMysql::getInstance();
        $user_ids = array_values(array_unique($user_ids));
        $qw_users = $db->table('qwuser')->whereIn('id', $user_ids)->field('id,wid, wname')->list();
        $userMap = array_column($qw_users, null, 'id');
        $send_users = [];
        foreach ($user_ids as $user_id) {
            $send_users[] = $userMap[$user_id]['wid'];
        }
        $remind_msg = "【{$scheme['scheme_name']}】的考核说明已发布，可以在【考核方案】中查看此方案的考核详情";
        messagesFrom::senMeg($send_users, 0, $remind_msg, $param['id']);
        returnSuccess([], 'success');
    }

    // 人员检查，未在任何方案中的人，和多方案中的人
    public function checkAssessmentSchemesUsers()
    {
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_schemes');
        $adb->where('where is_delete = 0');
        $data = $adb->list();

        $user_roles = userRolesModel::getRolesAndUsers();
        $department_users = qwuserModel::getDepartmentUsers();
        $users = qwuserModel::getUser();
        $user_map = array_column($users, null, 'id');

        foreach ($data as $item) {
            $item['attach'] = $item['attach'] ? json_decode($item['attach'], true) : null;
            $user_ids = assessmentSchemesModel::getAssessmentSchemesUsers($item, $user_roles, $department_users);
            if (empty($user_ids)) {
                continue;
            }
            foreach ($user_ids as $user_id) {
                $user_map[$user_id]['schemes'][] = $item['scheme_name'];
            }
        }

        $multi_schemes_users = [];
        $no_schemes_users = [];
        $user_map = array_values($user_map);
        foreach ($user_map as $user) {
            if (empty($user['schemes'])) {
                $no_schemes_users[] = [
                    'id'    => $user['id'],
                    'wname' => $user['wname'],
                ];
            } elseif (count($user['schemes']) > 1) {
                $multi_schemes_users[] = [
                    'id'      => $user['id'],
                    'wname'   => $user['wname'],
                    'schemes' => $user['schemes']
                ];
            }
        }

        returnSuccess([
            'multi_schemes_users' => $multi_schemes_users,
            'no_schemes_users'    => $no_schemes_users
        ]);
    }

    // 计算自动发起的时间 ，仅用于展示
    public function calcNextAutoTime($scheme, $next_auto_time)
    {
        $next_auto_time = date('Y-m-d', strtotime($next_auto_time));
        if (!$scheme['is_auto']) {
            return '';
        }
        $adb = dbAMysql::getInstance();
        // 找到定时任务
        $adb->table('custom_crontab');
        $adb->where('where link_id = :link_id and link_type = 1', ['link_id' => $scheme['id']]);
        $crontab = $adb->one();
        if (!$crontab) {
            return '';
        }
        $cron = new CronExpression($crontab['cron_expression']);
        // 获取当前考核方案下的所有考核任务
        $adb->table('assessment');
        $assessments = $adb->where('where is_delete = 0 and a_s_id = :a_s_id', ['a_s_id' => $scheme['id']])->list();

        $flag = true;$max_count = 0;
        while ($flag) {
            if ($max_count > 10) {
                return '';
            }
            $is_next = 0;
            foreach ($assessments as $assessment) {
                $a_time = json_decode($assessment['assessment_cycle'], true);
                // 自动发起时间在考核周期内
                if (strtotime($next_auto_time) >= strtotime($a_time[0]) && strtotime($next_auto_time) <= strtotime($a_time[1])) {
                    $is_next = 1;
                    // 执行时间在考核周期内，生成下一个自动发起时间
                    $next_auto_time = $cron->getNextRunDate($next_auto_time)->format('Y-m-d H:i:s');
                    break;
                }
            }
            if (!$is_next) {
                $flag = false;
            }
            $max_count++;
        }

        return $next_auto_time;

    }

    public function test()
    {
        $id = $_POST['id'];
        if (!$id) returnError('id不能为空');
        $adb = dbAMysql::getInstance();
        $adb->table('custom_crontab');
        $adb->where('id = :id', ['id' => $id]);
        $crontab = $adb->one();

        // 将当前任务设置为进行中
        $adb->table('custom_crontab');
        $adb->where('where id = :id',['id'=>$crontab['id']]);
        $adb->update(['status'=>0]);

        // 薪酬模块
        if ($crontab['link_module'] == 2) {
            switch ($crontab['link_type']) {
                case 1: // 薪酬方案自动生成
                    salaryCalculationModel::autoCalc($crontab);
                    break;
                case 2: // 薪酬核算
                    salaryCalculationModel::calcResult($crontab);
                    break;
                case 3: // 薪酬核算失败
                    salaryCalculationModel::calcFail($crontab);
                    break;
                case 4: // 改变用户状态
                    userInfoModel::changeUserStatus($crontab);
                    break;
            }
        }
        // 店铺
        else if ($crontab['link_module'] == 3) {
            switch ($crontab['link_type']) {
                case 1: // 商标需求
                    $model = new trademarkApplyModel();
                    $detail = $model->getById($crontab['link_id']);
                    if (in_array($detail['status'], [$model::STATUS_ASSIGNED, $model::STATUS_CANCELLED])) {
                        customCrontabModel::finishCrontab($crontab['id']);
                    } else {
                        // 推送消息，保存下一次执行时间
                        $shop_db = dbShopMysql::getInstance();
                        $one = $shop_db->table('operation_log')
                            ->where('table_name = :table_name and table_id = :table_id',
                                ['table_name' => 'trademark_apply', 'table_id' => $crontab['link_id']])
                            ->order('id desc')
                            ->one();
                        $users = redisCached::getUserInfo();
                        $users = array_column($users, 'user_wid', 'user_id');
                        $content = "请您及时跟进商标需求ID为{$crontab['link_id']}的【{$detail['country']}_{$detail['category']}】商标任务";
                        \plugins\shop\form\messagesFrom::senMeg([$users[$one['operator']]], 1 , $content, $crontab['link_id'], '', '商标任务跟进');

                        $next_runtime = strtotime($crontab['runtime']) + ($detail['reminder_interval'] * 60);
                        $adb->table('custom_crontab')
                            ->where('where id = :id', ['id' => $crontab['id']])
                            ->update(['status' => -1, 'runtime' => date('Y-m-d H:i:s', $next_runtime)]);
                    }
                    break;
                case 2: // 店铺需求
                    $model = new shopApplyModel();
                    $detail = $model->getById($crontab['link_id']);
                    if (in_array($detail['status'], [$model::STATUS_SUCCESS])) {
                        customCrontabModel::finishCrontab($crontab['id']);
                    } else {
                        // 推送消息，保存下一次执行时间
                        // 关联此需求的注册任务
                        $shop_db = dbShopMysql::getInstance();
                        $list = $shop_db->table('shop_register')->where('where shop_apply_id = :shop_apply_id', ['shop_apply_id' => $crontab['link_id']])->list();

                        if (!empty($list)) {
                            $deps = redisCached::getDepartment();
                            $deps = array_column($deps, 'name', 'id');

                            $users = redisCached::getUserInfo();
                            $users = array_column($users, 'user_wid', 'user_id');

                            // 找到每个注册任务最后一个操作人
                            $last_operator = [];
                            $log_list = $shop_db->table('operation_log')
                                ->where('table_name = :table_name', ['table_name' => 'shop_register'])
                                ->whereIn('table_id', array_column($list, 'id'))
                                ->order('id desc')
                                ->groupBy(['table_id'])
                                ->list();

                            foreach ($log_list as $one) {
                                $last_operator[] = $users[$one['operator']];
                            }

                            $content = "请您及时跟进【{$deps[$detail['dep_id']]}_{$detail['country_site']}_{$detail['shop_type']}】的需求";
                            \plugins\shop\form\messagesFrom::senMeg($last_operator, 1 , $content, $crontab['link_id'], '', '店铺需求提醒');
                        }

                        $next_runtime = strtotime($crontab['runtime']) + ($detail['remind_interval'] * 60);
                        $adb->table('custom_crontab')
                            ->where('where id = :id', ['id' => $crontab['id']])
                            ->update(['status' => -1, 'runtime' => date('Y-m-d H:i:s', $next_runtime)]);
                    }
                    break;
            }
        }
        else {
            switch ($crontab['link_type']){
                case 1: // 考核方案自动生成
                    assessmentSchemesModel::autoAssessment($crontab);
                    break;
                case 2: // 绩效核算
                    assessmentUsersModel::calcPerformance($crontab);
                    break;
                case 3: // 超时通知
                    assessmentUsersModel::overtimeMsg($crontab);
                    break;
                case 4: // 超时处理
                    assessmentUsersModel::overtimeHandle($crontab);
                    break;
                case 5: // 生成考核：
                    assessmentUsersModel::createAssessmentDetail($crontab);
                    break;
            }
        }
        return $crontab['id'];
    }


}