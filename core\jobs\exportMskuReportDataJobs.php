<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/24 9:57
 *
 * 导出报告数据
 */

namespace core\jobs;

use core\lib\config;
use core\lib\db\dbFMysql;
use financial\form\customColumnForm;
use financial\form\mskuReportForm;
use financial\models\userModel;

class exportMskuReportDataJobs //导入店铺数据
{
    public string $unqueid = '';
    public string $key = '';//到表得redis_key
    public string $page;//到表得redis_key
    public string $user_id;//
    public array $export_list=[];
    public array $param;
    public int $page_size = 1000;
    public function __construct($key,$param,$export_list,$page,$user_id){
        $this->key = $key;
        $this->export_list = $export_list;
        $this->user_id = $user_id;
        $this->page = $page;
        $this->unqueid = uniqid();
        $this->param = $param;
    }
    public function task(){
//        return true;
        $redis_key = $this->key;
        $page = $this->page;
        $redis = (new \core\lib\predisV())::$client;
        $export_data = json_decode($redis->get($redis_key),true);
        $db = dbFMysql::getInstance();
        $param = $this->param;
        if ($param['type'] == 0) {
            $param_ = [
                'date_time'=> $this->param['date_time'],
                'sid'=> $this->param['sid']??0,
                'ids'=> $this->param['ids'],
                'project_ids'=> $this->param['project_ids']??'[]',
                'search_type'=> $this->param['search_type']??'',
                'search_value'=> $this->param['search_value']??'',
                'import_time'=> $this->param['import_time']??'[]',
                'page'=>$page,
                'page_size'=>$this->page_size,
            ];
        } else {
            $param_ = [
                'date_time'=>$param['date_time'],
                'sid'=>$param['sid'],
                'project_ids'=> $this->param['project_ids']??'[]',
                'search_type'=> $this->param['search_type']??'',
                'search_value'=> $this->param['search_value']??'',
                'import_time'=> $this->param['import_time']??'[]',
                'page'=>$page,
                'page_size'=>$this->page_size,
            ];
        }
        $form = new mskuReportForm($param['date_time']);
        $data = $form->getList($param_);
        if (count($data['list'])) {
            $url = $form->exportSellerReportData($data['list'],$this->export_list);
            $export_data['success_count'] = $export_data['success_count']+count($data['list']);
            $export_data['excel_url'][] = $url;
            if ($export_data['success_count'] < $export_data['total']) {
                $queue_key = config::get('delay_queue_key', 'app');
                $param['page'] = $this->page+1;
                $task = new exportMskuReportDataJobs($redis_key,$param,$this->export_list,$param['page'],$this->user_id); // 创建任务类实例
                $redis->zAdd($queue_key, [], 0, serialize($task));
            } else {
                //保存
                //生成压缩包
                $save_path = '/public_financial/downLoad/msku_report/data';
                if (!file_exists(SELF_FK.$save_path)) {
                    mkdir(SELF_FK.$save_path, 0777, true);
                }
                $zip_url = $save_path ."/".date('YmdHis').'.zip';
                //生成压缩包
                setZipByUrl($export_data['excel_url'],$zip_url);
                $export_data['zip_url'] = $zip_url;
                //保存导出历史
                $db->table('msku_report_import')
                    ->insert([
                        'user_id'=>$this->user_id,
                        'excel_name'=>'',
                        'excel_path'=>'',
                        'total'=>$export_data['total'],
                        'success_count'=>0,
                        'fail_count'=>0,
                        'created_time'=>date('Y-m-d H:i:s'),
                        'report_date'=>$this->param['date_time'],
                        'type'=>1,
                        'redis_key'=>$redis_key,
                        'export_path'=>$zip_url
                    ]);
            }
            $redis->set($redis_key,json_encode($export_data));
            $redis->expire($redis_key,60*60);
        }
    }
}