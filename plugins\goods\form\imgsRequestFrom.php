<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/17 15:23
 */

namespace  plugins\goods\form;

use admin\form\customCrontabForm;
use core\lib\ExceptionError;
use plugins\goods\common\authenticationCommon;
use plugins\goods\models\imgsRequestModel;
use plugins\goods\models\userModel;
use core\lib\config;
use core\lib\db\dbMysql;

class imgsRequestFrom
{
    //图片审核时消息发送的数据
    public static $chechk_img_msg_wid = [];
    //生产需求时的通知消息
    public static $msg_info = [];
    //获取列表
    public static function getList($param) {
        $db = dbMysql::getInstance();
        $db->table('imgs_request', 'ir')
            ->leftJoin('qwuser', 'q', 'q.id = ir.qwuser_id')
            ->leftJoin('qwuser', 'q1', 'q1.id = ir.user_id')
            ->leftJoin('qwuser', 'q2', 'q2.id = ir.allocation_user_id')
            ->leftJoin('goods_new', 'g', 'g.id = ir.goods_id')
            ->leftJoin('imgs_request_checker', 'c', 'c.id = ir.request_checke_id')
            ->leftJoin('imgs_request_distributor', 'd', 'd.id = ir.request_distributor_id')
            ->leftJoin('qwdepartment', 'qw', 'qw.wp_id = ir.wp_id')
            ->where('ir.is_delete = 0');
        //如果不是
        if (!userModel::$is_super) {
            $db->leftJoin('view_request_participant', 'vrp', 'vrp.request_id = ir.id')
               ->andWhere('vrp.user_id = :user_id', ['user_id' => userModel::$qwuser_id]);
        }


        // 需求名称：模糊查询
        if (!empty($param['request_name'])) {
            $db->andWhere('ir.request_name LIKE :request_name', ['request_name' => '%' . $param['request_name'] . '%']);
        }

        // 品名：搜索+下拉多选
        if (!empty($param['goods_id']) && $param['goods_id'] != '[]') {
            $goods_ids = json_decode($param['goods_id'], true);
            $db->whereIn('ir.goods_id', $goods_ids);
        }

        // 需求类型：下拉多选；枚举
        if (!empty($param['type']) && $param['type'] != '[]') {
            $types = json_decode($param['type'], true);
            $db->whereIn('ir.type', $types);
        }

        // 业务平台：下拉搜索+多选；枚举
        if (!empty($param['platform_id']) && $param['platform_id'] != '[]') {
            $platform_ids = json_decode($param['platform_id'], true);
            $db->whereIn('ir.platform_id', $platform_ids);
        }

        // 类目：下拉搜索级联选择
        if (!empty($param['category_id']) && $param['category_id'] != '[]') {
            $db->andWhere('JSON_OVERLAPS(ir.category_id, :category_id_json)', [
                'category_id_json' => $param['category_id']
            ]);
        }
        // 任务状态：下拉多选
        if (!empty($param['status']) && $param['status'] != '[]' && $param['status'] != '["-1"]') {
            $statuses = json_decode($param['status'], true);
            $db->whereIn('ir.status', $statuses);
        }

        // 颜色：下拉多选
        if (!empty($param['color_info']) && $param['color_info'] != '[]') {
//            $colors = json_decode($param['color_info'], true);
//            dd($colors);
//            $db->whereIn('ir.color_info', $param['color_info']);
            $db->andWhere('JSON_OVERLAPS(ir.color_info, :color_info)', [
                'color_info' => $param['color_info']
            ]);
        }

        // 品牌：搜索+下拉多选
        if (!empty($param['brand']) && $param['brand'] != '[]') {
            $brands = json_decode($param['brand'], true);
            $db->whereIn('ir.brand_id', $brands);
        }

        // 运营组别：级联选择
        if (!empty($param['wp_id']) && $param['wp_id'] != '[]') {
            $wp_ids = json_decode($param['wp_id'], true);
            $db->whereIn('ir.wp_id', $wp_ids);
        }

        // 语言：下拉搜索+多选
        if (!empty($param['language']) && $param['language'] != '[]') {
            $languages = json_decode($param['language'], true);
            $db->whereIn('ir.language_code', $languages);
        }

        // 申请人：下拉搜索+多选；级联选择
        if (!empty($param['user_id']) && $param['user_id'] != '[]') {
            $user_ids = json_decode($param['user_id'], true);
            $db->whereIn('ir.user_id', $user_ids);
        }

        // 执行人(美工)：下拉多选
        if (!empty($param['qwuser_id']) && $param['qwuser_id'] != '[]') {
            $qwuser_ids = json_decode($param['qwuser_id'], true);
            $db->whereIn('ir.qwuser_id', $qwuser_ids);
        }

        // 组别：支持单个组别或组别数组
        if (!empty($param['group_name_id']) && $param['group_name_id'] !== '[]') {
            $group_name = json_decode($param['group_name_id'], true);
            $db->whereIn('ir.request_distributor_id', $group_name);

        }


        // SOP情况：下拉单选
        if (isset($param['has_sop']) && $param['has_sop'] !== '') {
            $db->andWhere('ir.has_sop IS NOT NULL AND ir.has_sop != "[]"');
        }

        // 样品情况：下拉单选
        if (isset($param['sample_status']) && $param['sample_status'] !== '') {
            $db->andWhere('ir.sample_status = :sample_status', ['sample_status' => $param['sample_status']]);
        }

        // 模型情况：下拉单选
        if (isset($param['model_status']) && $param['model_status'] !== '') {
            $db->andWhere('ir.model_status = :model_status', ['model_status' => $param['model_status']]);
        }

        // 是否采购：下拉单选
        if (isset($param['is_purchase']) && $param['is_purchase'] !== '') {
            $db->andWhere('ir.is_purchase = :is_purchase', ['is_purchase' => $param['is_purchase']]);
        }

        // 领星建档：下拉单选
        if (isset($param['is_file_established']) && $param['is_file_established'] !== '') {
            $db->andWhere('ir.is_file_established = :is_file_established', ['is_file_established' => $param['is_file_established']]);
        }

        // 需求提交时间
        if (!empty($param['created_time']) && $param['created_time'] !== '[]') {
            $submit_time = json_decode($param['created_time'], true);
            if (is_array($submit_time) && count($submit_time) == 2) {
                $db->andWhere(
                    'ir.created_time >= :submit_time1 AND ir.created_time <= :submit_time2',
                    [
                        'submit_time1' => $submit_time[0],
                        'submit_time2' => $submit_time[1],
                    ]
                );
            }
        }
        // 预计时间(最晚期望时间)
        if (!empty($param['latest_expect_time']) && $param['latest_expect_time'] !== '[]') {
            $expected_time = json_decode($param['latest_expect_time'], true);
            if (is_array($expected_time) && count($expected_time) == 2) {
                $expected_time[0] = strtotime($expected_time[0]);
                $expected_time[1] = strtotime($expected_time[1]);
                $db->andWhere(
                    'ir.latest_expect_time >= :expected_time1 AND ir.latest_expect_time <= :expected_time2',
                    [
                        'expected_time1' => $expected_time[0],
                        'expected_time2' => $expected_time[1],
                    ]
                );
            }
        }

        // 预计完成时间(预计交稿时间)
        if (!empty($param['real_expected_time']) && $param['real_expected_time'] !== '[]') {
            $real_expected = json_decode($param['real_expected_time'], true);
            if (is_array($real_expected) && count($real_expected) == 2) {
                $real_expected[0] = strtotime($real_expected[0]);
                $real_expected[1] = strtotime($real_expected[1]);
                $db->andWhere(
                    'ir.real_expected_time >= :real_expected_time1 AND ir.real_expected_time <= :real_expected_time2',
                    [
                        'real_expected_time1' => $real_expected[0],
                        'real_expected_time2' => $real_expected[1],
                    ]
                );
            }
        }

        // 任务开始时间
        if (!empty($param['begin_time']) && $param['begin_time'] !== '[]') {
            $begin_time = json_decode($param['begin_time'], true);
            if (is_array($begin_time) && count($begin_time) == 2) {
                $begin_time[0] = strtotime($begin_time[0]);
                $begin_time[1] = strtotime($begin_time[1]);
                $db->andWhere(
                    'ir.begin_time >= :begin_time1 AND ir.begin_time <= :begin_time2',
                    [
                        'begin_time1' => $begin_time[0],
                        'begin_time2' => $begin_time[1],
                    ]
                );
            }
        }

        // 完成时间
        if (!empty($param['completion_time']) && $param['completion_time'] !== '[]') {
            $complete_time = json_decode($param['completion_time'], true);
            if (is_array($complete_time) && count($complete_time) == 2) {
                $complete_time[0] = strtotime($complete_time[0]);
                $complete_time[1] = strtotime($complete_time[1]);
                $db->andWhere(
                    'ir.completion_time >= :completion_time1 AND ir.completion_time <= :completion_time2',
                    [
                        'completion_time1' => $complete_time[0],
                        'completion_time2' => $complete_time[1],
                    ]
                );
            }
        }

        // 审核时间
        if (!empty($param['task_reviewed_time']) && $param['task_reviewed_time'] !== '[]') {
            $reviewed_time = json_decode($param['task_reviewed_time'], true);
            if (is_array($reviewed_time) && count($reviewed_time) == 2) {
                $reviewed_time[0] = strtotime($reviewed_time[0]);
                $reviewed_time[1] = strtotime($reviewed_time[1]);
                $db->andWhere(
                    'ir.task_reviewed_time >= :task_reviewed_time1 AND ir.task_reviewed_time <= :task_reviewed_time2',
                    [
                        'task_reviewed_time1' => $reviewed_time[0],
                        'task_reviewed_time2' => $reviewed_time[1],
                    ]
                );
            }
        }

        // 需求更新时间
        if (!empty($param['update_time']) && $param['update_time'] !== '[]') {
            $update_time = json_decode($param['update_time'], true);
            if (is_array($update_time) && count($update_time) == 2) {
                $update_time[0] = strtotime($update_time[0]);
                $update_time[1] = strtotime($update_time[1]);
                $db->andWhere(
                    'ir.update_time >= :update_time1 AND ir.update_time <= :update_time2',
                    [
                        'update_time1' => $update_time[0],
                        'update_time2' => $update_time[1],
                    ]
                );
            }
        }

        // 实际发货日期
        if (!empty($param['actual_delivery_date']) && $param['actual_delivery_date'] !== '[]') {
            $actual_delivery_date = json_decode($param['actual_delivery_date'], true);
            if (is_array($actual_delivery_date) && count($actual_delivery_date) == 2) {
                $actual_delivery_date[0] = strtotime($actual_delivery_date[0]);
                $actual_delivery_date[1] = strtotime($actual_delivery_date[1]);
                $db->andWhere(
                    'ir.actual_delivery_date >= :actual_delivery_date1 AND ir.actual_delivery_date <= :actual_delivery_date2',
                    [
                        'actual_delivery_date1' => $actual_delivery_date[0],
                        'actual_delivery_date2' => $actual_delivery_date[1],
                    ]
                );
            }
        }

        // 预估到货日期
        if (!empty($param['estimated_arrival_date']) && $param['estimated_arrival_date'] !== '[]') {
            $estimated_arrival_date = json_decode($param['estimated_arrival_date'], true);
            if (is_array($estimated_arrival_date) && count($estimated_arrival_date) == 2) {
                $estimated_arrival_date[0] = strtotime($estimated_arrival_date[0]);
                $estimated_arrival_date[1] = strtotime($estimated_arrival_date[1]);
                $db->andWhere(
                    'ir.estimated_arrival_date >= :estimated_arrival_date1 AND ir.estimated_arrival_date <= :estimated_arrival_date2',
                    [
                        'estimated_arrival_date1' => $estimated_arrival_date[0],
                        'estimated_arrival_date2' => $estimated_arrival_date[1],
                    ]
                );
            }
        }

        // 排序处理
        $defaultOrder = 'ir.id DESC';

        $orderClauses = [];
        if (!empty($param['order_by'])) {
            $decoded = json_decode($param['order_by'], true);
            if (is_array($decoded)) {
                foreach ($decoded as $col => $dir) {
                    if (!is_string($col) || !is_string($dir)) {
                        continue;
                    }
                    $dir = strtoupper($dir) === 'ASC' ? 'ASC' : 'DESC';
                    $orderClauses[] = "ir.{$col} {$dir}";
                }
            }
        }

        $db->order(
            $orderClauses
                ? implode(', ', $orderClauses)
                : $defaultOrder
        );

        // 查询字段
        $db->field('ir.*,q.wname as agent_wname,q1.wname as created_wname,q2.wname as allocation_wname,g.goods_name,d.distributor_id,g.manage_info,g.operator_info,d.group_name,qw.name as qw_group_name');

        // 分页查询
        $list = $db->pages($param['page'], $param['page_size']);
        // 获取列表商品颜色
        $list['list'] = goodsColorRelationFrom::getImgsRequestListColor($list['list']);
        //处理时间格式和获取其他信息
        $list = self::getImgsRequestList($db,$list);

        returnSuccess($list);
    }
    //处理时间格式和获取其他信息
    public static function getImgsRequestList($db, &$list) {
        $now = time();

        // —— 1. 初始化各类 ID 数组 ——
        $wp_ids               = [];
        $check_ids            = [];  // 暂不直接用，但保留以防后续扩展
        $allocation_user_ids  = [];
        $category_ids         = [];
        $brand_ids            = [];
        $color_ids            = [];
        $country_codes        = [];
        $request_ids          = array_column($list['list'], 'id');

        // —— 2. 读取默认配置 ——
        $cfgChecker     = $db->table('config')->where('key_name = "imgs_request_sop_checker"')->one();
        $cfgDistributor = $db->table('config')->where('key_name = "imgs_request_distributor"')->one();
        $default_check_ids         = !empty($cfgChecker['data'])     ? json_decode($cfgChecker['data'], true)     : [];
        $default_distributor_ids   = !empty($cfgDistributor['data']) ? json_decode($cfgDistributor['data'], true) : [];
        $default_distributor_remark = $cfgDistributor['remarks'] ?? '';

        // —— 3. 查询所有审核者信息 ——
        $checker_participants = $db->table('imgs_request_participant')
            ->whereIn('request_id', $request_ids)
            ->where('type = 2')
            ->field('request_id, user_id')
            ->list();
        // 将审核者信息按 request_id 分组
        $checker_map = [];
        foreach ($checker_participants as $p) {
            $checker_map[$p['request_id']][] = $p['user_id'];
        }

        // —— 4. 第一遍循环：聚合要查询的 ID ——
        foreach ($list['list'] as $item) {
            $wp_ids[]        = $item['wp_id'];
            $brand_ids[]     = $item['brand_id'];
            $country_codes[] = $item['country_code'];

            // check_ids 从 participant 表获取
            $ids = $checker_map[$item['id']] ?? [];
            if (empty($ids) && (int)$item['request_checke_id'] === 0) {
                $ids = $default_check_ids;
            }
            $check_ids = array_merge($check_ids, $ids);

            // allocation_user_id（单值）
            if (!empty($item['allocation_user_id'])) {
                $allocation_user_ids[] = $item['allocation_user_id'];
            }
            if ((int)$item['request_distributor_id'] === 0) {
                $allocation_user_ids = array_merge($allocation_user_ids, $default_distributor_ids);
            }

            // category_id（JSON）
            $ids = json_decode($item['category_id'] ?? '[]', true);
            if (is_array($ids)) $category_ids = array_merge($category_ids, $ids);

            // color_info（JSON）
            $ids = json_decode($item['color_info'] ?? '[]', true);
            if (is_array($ids)) $color_ids = array_merge($color_ids, $ids);
        }

        // —— 5. 去重、去空 ——
        $wp_ids               = array_unique(array_filter($wp_ids));
        $check_ids            = array_unique(array_filter($check_ids));
        $allocation_user_ids  = array_unique(array_filter($allocation_user_ids));
        $category_ids         = array_unique(array_filter($category_ids));
        $brand_ids            = array_unique(array_filter($brand_ids));
        $color_ids            = array_unique(array_filter($color_ids));
        $country_codes        = array_unique(array_filter($country_codes));

        // —— 6. 批量查询映射 ——
        $wp_name_list     = array_column(
            $db->table('qwdepartment')->whereIn('id', $wp_ids)->list(),
            'name', 'id'
        );
        $checker_list     = array_column(
            $db->table('qwuser')->whereIn('id', $check_ids)->field('id,wname')->list(),
            'wname', 'id'
        );
        $distributor_list = array_column(
            $db->table('qwuser')->whereIn('id', $allocation_user_ids)->field('id,wname')->list(),
            'wname', 'id'
        );
        $category_list    = array_column(
            $db->table('goods_cate')->whereIn('id', $category_ids)->where('is_delete = 0')->field('id,cate_name')->list(),
            'cate_name', 'id'
        );
        $color_list       = array_column(
            $db->table('goods_color')->whereIn('id', $color_ids)->field('id,color_name')->list(),
            'color_name', 'id'
        );
        $brand_list       = array_column(
            $db->table('brands')->whereIn('id', $brand_ids)->field('id,name')->list(),
            'name', 'id'
        );
        $country_list     = array_column(
            $db->table('market')->whereIn('code', $country_codes)->field('code,country')->list(),
            'country', 'code'
        );
        $country_list     = array_merge($country_list, ['NONE' => '无']);

        // —— 7. 时间字段列表 ——
        $timeFields = [
            'submit_time','expected_time','begin_time','real_expected_time',
            'assigned_time','task_reviewed_time','update_time','completion_time',
            'actual_delivery_date','estimated_arrival_date','latest_expect_time',
            'first_batch_date'
        ];

        // —— 8. 第二遍循环：格式化 & 映射 ——
        foreach ($list['list'] as &$v) {
            // 基本映射
            $v['wp_group_name'] = $wp_name_list[$v['wp_id']]        ?? '';
            $v['brand_name']    = $brand_list[$v['brand_id']]       ?? '';
            $v['country_name']  = $country_list[$v['country_code']] ?? '';

            // check_names & check_id
            $checker_ids = $checker_map[$v['id']] ?? [];
            if (empty($checker_ids) && (int)$v['request_checke_id'] === 0) {
                $checker_ids = $default_check_ids;
            }
            $v['check_id']    = $checker_ids;  // 将 ID 赋回 check_id
            $v['check_names'] = array_map(fn($id) => $checker_list[$id] ?? '', $checker_ids);

            // distributor_names（allocation_user_id）
            if (!empty($v['allocation_user_id'])) {
                $id = $v['allocation_user_id'];
                $v['distributor_names'] = [$distributor_list[$id] ?? ''];
            }
            elseif ((int)$v['request_distributor_id'] === 0) {
                $v['group_name'] = $default_distributor_remark;
                $v['distributor_names'] = array_map(
                    fn($id) => $distributor_list[$id] ?? '',
                    $default_distributor_ids
                );
            }
            else {
                $v['distributor_names'] = [];
            }

            // color_names
            $ids = json_decode($v['color_info'] ?? '[]', true) ?: [];
            $v['color_names'] = array_map(fn($id) => $color_list[$id] ?? '', $ids);

            // category_names
            $ids = json_decode($v['category_id'] ?? '[]', true) ?: [];
            $v['category_names'] = array_map(fn($id) => $category_list[$id] ?? '', $ids);

            // manage_info / operator_info
            $manage   = json_decode($v['manage_info']   ?? '[]', true) ?: [];
            $operator = json_decode($v['operator_info'] ?? '[]', true) ?: [];
            $v['manage_names']    = $manage[0]['wname']   ?? '';
            $v['lxyunying_names'] = $operator[0]['wname'] ?? '';

            // 超时标记
            $v['is_timeout'] = (!empty($v['latest_expect_time']) && $v['latest_expect_time'] < $now) ? 1 : 0;

            // 时间格式化
            foreach ($timeFields as $f) {
                if (isset($v[$f])) {
                    $v[$f] = self::fmtTime($v[$f]);
                }
            }
        }

        return $list;
    }



    /**
     * 将多种时间输入统一转成 "Y-m-d H:i:s" 字符串
     *
     * @param mixed $val 秒级时间戳（int|string）或 DATETIME 字符串
     * @return string 原始就空则返回空字符串，解析失败则返回原始值
     */
    private static function fmtTime($val): string
    {
        if ($val === '' || $val === null || $val === 0 || $val === '0') {
            return '';
        }
        // 数字或数字字符串，且长度 ≤ 10，算作秒级时间戳
        if (is_numeric($val) && strlen((string)$val) <= 10) {
            return date('Y-m-d H:i:s', (int)$val);
        }
        // 其它情况当成字符串解析
        $ts = strtotime($val);
        if ($ts !== false && $ts > 0) {
            return date('Y-m-d H:i:s', $ts);
        }
        // 解析失败，保留原值
        return (string)$val;
    }
    //配置美工
    public static function setAgentUser($param) {
        $db = dbMysql::getInstance();
        $imgs_request = imgsRequestModel::getRequest($db,(int)$param['id']);
        if ($imgs_request['qwuser_id'] > 0) {
            SetReturn(-1,'美工已配置，不可重复操做');
        }
        if (in_array($imgs_request['status'],[2,3])) {
            SetReturn(-1,'需求已提交，不可再配置');
        }
        //美工
        $agent_info = json_decode($param['agent_info'],true);
        if (!count($agent_info)) {
            SetReturn(-1, '请选择美工');
        }
        //判断操作权限
        $db->table('imgs_request_participant')
            ->where('type = 3 and request_id = :request_id',['request_id'=>$param['id']])
            ->one();
        if ($imgs_request['allocation_user_id'] != userModel::$qwuser_id) {
            SetReturn(-1, '无权配置美工');
        }
        $qwuser_id = $agent_info[0]['id'];
        $is_expected_completion = (int)$param['is_expected_completion'];
        //不能在最晚期望时间完成就使用自己的时间，能就使用该时间
        $real_expected_time = $imgs_request['latest_expect_time'];
        if ($is_expected_completion == 0) {
            if (empty($param['real_expected_time'])) {
                SetReturn(-1, '请设置预计完成时间');
            }
            $real_expected_time = strtotime($param['real_expected_time']);
        }
        $db->beginTransaction();
        try {
            //提醒数据保存
            if ($param['timeout_remind_id'] > 0) {
                customCrontabForm::saveTask($imgs_request['id'],1,1,$real_expected_time,1);
            }
            //新增参与者
            requestParticipantForm::updateUser($db,$imgs_request['id'],[$qwuser_id],4);
            //待办处理
            //关闭之前的待办
            $db->table('goods_matters')
                ->where('where type = 4 and model_id=:reques_id and status <> 1 and create_type = 0 and node_name = :node_name',['reques_id'=>$imgs_request['id'],'node_name'=>'图片制作'])
                ->update([
                    'status'=>3,
                    'completion_time'=>time(),
                ]);
            //生成新待办
            goodsMattersFrom::addCreateMatter($imgs_request['request_name'],$imgs_request['goods_id'],'图片制作',0,4,$qwuser_id,$param['id'],$imgs_request['real_expected_time']);
            //建企微群
            //新图制作才建群
            $chat_name = '';
            if ($imgs_request['nature_demand'] == 1) {
                list($chat_name) = imgsRequestModel::createdChat($db,$imgs_request);
            }
            //修改信息-状态改完待接收
            $db->table('imgs_request')
                ->where('where id=:id',['id'=>(int)$param['id']])
                ->update([
                    'qwuser_id'=>$qwuser_id,
                    'is_expected_completion'=>$is_expected_completion,
                    'real_expected_time'=> $real_expected_time,
                    'status'=>5,
                    'remarks'=>$param['remarks'],
                    'timeout_remind_id'=>$param['timeout_remind_id'],
                    'chat_name'=>$chat_name,
                    'assigned_time'=>strtotime(date('Y-m-d H:i:s')),
                ]);
            //日志
            imgsRequestModel::setLog($db,$imgs_request['id'],3,$param['remarks']);
            //消息通知
            $w_userids = array_column($agent_info,'wid');
            if ($imgs_request['status'] == 5) {
                //未接收
                $text = imgsRequestModel::getMessage(5,'','');
            } else {
                if ($imgs_request['goods_id']) {
                    $goods = $db->table('goods_new')
                        ->where('id = :id',['id'=>$imgs_request['goods_id']])
                        ->one();
                    $goods_name = $goods['goods_name'];
                } else {
                    $goods_name = '';
                }

                //更换美工
                $text = imgsRequestModel::getMessage(1,$imgs_request['request_name'],$goods_name);
            }
            $other_data = [
                'user_id'=>userModel::$qwuser_id,
                'model_id'=>$param['id'],
                'msg_type'=>5,
            ];
            messagesFrom::senMeg($w_userids, $text, $other_data);
            $db->commit();
            returnSuccess('','配置成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //美工接收任务
    public static function acceptRequest($id) {
        $db = dbMysql::getInstance();
        $imgs_request = imgsRequestModel::getRequest($db,$id);
        if (!$imgs_request) {
            returnError('未查询到需求');
        }
        if ($imgs_request['status'] != 5) {
            returnError('当前任务状态不为【待接收】，不可接收任务');
        }
        if ($imgs_request['qwuser_id'] != userModel::$qwuser_id) {
            returnError('该任务未分配给您，不可接收');
        }
        $db->table('imgs_request')
            ->where('id = :id',['id'=>$id])
            ->update([
                'status'=>1,
                'begin_time'=>time(),
            ]);
        //日志记录
        imgsRequestModel::setLog($db,$id,4,'','美工接收任务');
    }
    /**
     * @param $id
     * @param $type 1暂停，2取消暂停
     * @return void
     * @throws ExceptionError 暂停，取消暂停
     */
    public static function pauseed($id,$type) {
        $db = dbMysql::getInstance();
        $imgs_request = $db->table('imgs_request')
            ->where('id = :id',['id'=>$id])
            ->one();
        if (!$imgs_request) {
            returnError('未查询到需求');
        }
        if ($imgs_request['status'] == 3) {
            returnError('需求已完成不可操做');
        }
        if ($type == 2 && $imgs_request['status'] != 8) {
            return true;
        }
        if ($type == 1 && $imgs_request['status'] == 8) {
            return true;
        }
        $db->beginTransaction();
        try {
            if ($type == 2) {
                //取消暂停
                $db->table('imgs_request')
                    ->where('id = :id',['id'=>$id])
                    ->update([
                        'status'=>$imgs_request['last_status'],
                    ]);
                //通知
                $msg_type = 20;
                $msg_text = '【'.userModel::$wname.'】已恢复当前作图任务';
            } else {
                $db->table('imgs_request')
                    ->where('id = :id',['id'=>$id])
                    ->update([
                        'status'=>8,
                        'last_status'=>$imgs_request['status'],
                    ]);
                $msg_type = 19;
                $msg_text = '【'.userModel::$wname.'】已暂停当前作图任务';
            }
            $users = $db->table('imgs_request_participant','a')
                ->leftJoin('qwuser','b','b.id = a.user_id')
                ->where('a.request_id = :request_id',['request_id'=>$id])
                ->field('b.wid')
                ->list();
            if (count($users)) {
                $wids = array_column($users,'wid');
                //通知
                messagesFrom::senMessage($wids,$msg_text,userModel::$qwuser_id,$id,$msg_type);
            }
            $db->commit();
        } catch (ExceptionError $error) {
            $db->rollBack();
            returnError($error->getMessage());
        }
    }
    //催办
    public static function remind(int $id, string $remark) {
        $db = dbMysql::getInstance();
        $imgs_request = imgsRequestModel::getRequest($db, $id);
        $goods_info = $db->table('goods_new')
            ->where('id = :goods_id', ['goods_id' => $imgs_request['goods_id']])
            ->field('id, manage_info, goods_name')
            ->one();
        // 根据状态，准备要通知的 (type, msg_type) 列表
        $targets = [];
        switch ($imgs_request['status']) {
            case 4:
                // sop 审核，通过后还要给待配置的人也发一次
                $targets[] = ['status'=>$imgs_request['status'], 'type' => 2];
                break;
            case 0:
                // 待配置
                $targets[] = ['status'=>$imgs_request['status'], 'type' => 3]; //ok
                break;
            case 5:
                // 待接收
                $targets[] = ['status'=>$imgs_request['status'], 'type' => 4]; //接收任务
                break;
            case 1:
                // 进行中
                $targets[] = ['status'=>$imgs_request['status'], 'type' => 4];//做图
                break;
            case 2:
                // 待审核
                if ($imgs_request['is_check'] == 0) {
                    $targets[] = ['status'=>$imgs_request['status'], 'type' => 3];
                    break;
                }
            // 否则走 default
            default:
                returnError('此状态不可催办');
        }
        // 对每一种 (type, msg_type) 都查询参与者并发送
        foreach ($targets as $t) {
            $participants = $db->table('imgs_request_participant', 'a')
                ->leftJoin('qwuser', 'b', 'b.id = a.user_id')
                ->where('a.request_id = :request_id AND a.type = :type', [
                    'request_id' => $id,
                    'type'       => $t['type'],
                ])
                ->field('b.wid')
                ->list();
            if (!empty($participants)) {
                $wids = array_column($participants, 'wid');
                $msg  = imgsRequestModel::getMessage(
                    $t['status'],
                    $imgs_request['request_name'],
                    ($goods_info['goods_name']??''),
                );
                messagesFrom::senMessage($wids,$msg,userModel::$qwuser_id,$id,4,0,0,$remark);
            }
        }
    }

    //上传文件
    public static function uploadImg($param) {
        $request_id = (int)$param['request_id'];
        $db = dbMysql::getInstance();
        $imgs_request = imgsRequestModel::getRequest($db,$request_id);
        if ($imgs_request['platform_id'] != 1){
            //校验是否传递rendering_path参数
            if (!isset($param['rendering_path'])) {
                returnError('工程文件地址必传');
            }
        }
        if (!$imgs_request['qwuser_id']) {
            SetReturn(-1,'该需求还未配置美工');
        }
        if ($imgs_request['qwuser_id'] != userModel::$qwuser_id) {
            SetReturn(-1,'非当前配置美工不可上传');
        }
        if ($imgs_request['platform_id'] == 1 && $imgs_request['type'] == 1) {
            if ($imgs_request['status'] != 1) {
                SetReturn(-1,'当前状态不可上传文件');
            }
        } else {
            if (!in_array($imgs_request['status'],[1,3])) {
                SetReturn(-1,'当前状态不可上传文件');
            }
        }

        $images = json_decode($param['images'],true);
        if (!count($images)) {
            SetReturn(-1,'结果件必传');
        }
        $source_file = json_decode($param['source_file'],true);
        //获取列表
        $collection_list = $db->table('imgs_request_collection')
            ->where('where request_id=:request_id and is_delete = 0 and source_type=0',['request_id'=>$request_id])
            ->field('id,url')
            ->list();
        $collection = array_column($collection_list,'url');
        $new_list = [];
        //是否为app产需求
        $is_goods_request = 0;
        if ($imgs_request['platform_id'] == 1 && $imgs_request['type'] == 1) {
            $is_goods_request = 1;
        }
        $db->beginTransaction();
        try {
            //结果文件
            if ($is_goods_request) {
                //app产品图
                foreach ($images as $imgs) {
                    $color_id = (int)$imgs['color_id'];
                    if (empty($color_id) && $is_goods_request) {
                        SetReturn(-1,'结果件缺少颜色ID');
                    }
                    foreach ($imgs['imgs'] as $AB) {
                        $pic_type = empty($AB['pic_type'])?1:(int)$AB['pic_type'];
                        if (!in_array($pic_type,[1,2])) {
                            SetReturn(-1,'结果件AB图标识有误');
                        }
                        foreach ($AB['imgs'] as $v) {
                            if (!isset($v['url']) || !isset($v['thumb_src'])) {
                                SetReturn(-1,'结果文件图片压缩路径必传');
                            }
                            $new_list[] = $v['url'];
                            if (!in_array($v['url'],$collection)) {
                                $name_array = explode('.',$v['file_name']);
                                $insert_data = [
                                    'user_id' => userModel::$qwuser_id,
                                    'goods_id'=>$imgs_request['goods_id'],
                                    'request_id'=>$request_id,
                                    'language_id'=>$imgs_request['language_code'],
                                    'file_name'=>$v['file_name'],
                                    'url'=>$v['url'],
                                    'created_time'=>date('Y-m-d H:i:s'),
                                    'file_type'=>3,
                                    'color_id'=>$color_id,
                                    'thumb_src'=>$v['thumb_src'],
                                    'type'=>$imgs_request['type'],
                                    'platform_id' => (int) $imgs_request['platform_id'],
                                    'second_type'=>$imgs_request['second_type'],
                                    'extension'=>end($name_array),
                                    'pic_type'=>$pic_type
                                ];
                                $db->table('imgs_request_collection')->insert($insert_data);
                            }
                        }
                    }
                }
            } else {
                //其他图
                foreach ($images as $imgs) {
                    $color_id = (int)$imgs['color_id'];
                    foreach ($imgs['imgs'] as $v) {
                        $pic_type = 0;
                        if (!isset($v['url']) || !isset($v['thumb_src'])) {
                            SetReturn(-1, '结果文件图片压缩路径必传');
                        }
                        $new_list[] = $v['url'];
                        if (!in_array($v['url'], $collection)) {
                            $name_array = explode('.', $v['file_name']);
                            $insert_data = [
                                'user_id' => userModel::$qwuser_id,
                                'goods_id' => $imgs_request['goods_id'],
                                'request_id' => $request_id,
                                'language_id'=>$imgs_request['language_code'],
                                'file_name' => $v['file_name'],
                                'url' => $v['url'],
                                'created_time' => date('Y-m-d H:i:s'),
                                'file_type' => 3,
                                'color_id' => $color_id,
                                'thumb_src' => $v['thumb_src'],
                                'type' => $imgs_request['type'],
                                'platform_id' => (int) $imgs_request['platform_id'],
                                'second_type' => $imgs_request['second_type'],
                                'extension' => end($name_array),
                                'pic_type' => $pic_type
                            ];
                            $db->table('imgs_request_collection')->insert($insert_data);
                        }
                    }
                }
            }
            //源文件处理
            if (count($source_file)) {
                foreach ($source_file as $v) {
                    $new_list[] = $v['url'];
                    if (!in_array($v['url'],$collection)) {
                        $name_array = explode('.',$v['file_name']);
                        $insert_data = [
                            'user_id' => userModel::$qwuser_id,
                            'goods_id'=>$imgs_request['goods_id'],
                            'language_id'=>$imgs_request['language_code'],
                            'request_id'=>$request_id,
                            'file_name'=>$v['file_name'],
                            'url'=>$v['url'],
                            'created_time'=>date('Y-m-d H:i:s'),
                            'file_type'=>1,
                            'type'=>$imgs_request['type'],
                            'platform_id' => (int) $imgs_request['platform_id'],
                            'second_type'=>$imgs_request['second_type'],
                            'extension'=>end($name_array),
                            'thumb_src'=>$v['thumb_src'],
                        ];
                        $db->table('imgs_request_collection')->insert($insert_data);
                    }
                }
            }
            //渲染文件
            $rendering_files = empty($param['rendering_files'])?[]:json_decode($param['rendering_files'],true);
            if (count($rendering_files)) {
                foreach ($rendering_files as $v) {
                    $new_list[] = $v['url'];
                    $name_array = explode('.',$v['file_name']);
                    if (!in_array($v['url'],$collection)) {
                        $insert_data = [
                            'user_id' => userModel::$qwuser_id,
                            'goods_id'=>$imgs_request['goods_id'],
                            'request_id'=>$request_id,
                            'language_id'=>$imgs_request['language_code'],
                            'file_name'=>$v['file_name'],
                            'url'=>$v['url'],
                            'created_time'=>date('Y-m-d H:i:s'),
                            'file_type'=>2,
                            'type'=>$imgs_request['type'],
                            'platform_id' => (int) $imgs_request['platform_id'],
                            'second_type'=>$imgs_request['second_type'],
                            'extension'=>$name_array[1],
                            'thumb_src'=>$v['thumb_src'],
                        ];
                        $db->table('imgs_request_collection')->insert($insert_data);
                    }
                }
                //共享路径写进去
                $db->table('imgs_request_collection')
                    ->where('file_type = 2 and request_id = :request_id',['request_id'=>$request_id])
                    ->update([
                        'share_path'=>$param['rendering_path']
                    ]);
            } else {
                $imgs_request_file = $db->table('imgs_request_collection')
                    ->where('file_type = 2 and request_id = :request_id and url = \'\' and is_delete = 0',['request_id'=>$request_id])
                    ->one();
                //保存一个路径再
                $insert_data = [
                    'user_id' => userModel::$qwuser_id,
                    'goods_id'=>$imgs_request['goods_id'],
                    'request_id'=>$request_id,
                    'language_id'=>$imgs_request['language_code'],
                    'file_name'=>'',
                    'url'=>'',
                    'created_time'=>date('Y-m-d H:i:s'),
                    'file_type'=>2,
                    'type'=>$imgs_request['type'],
                    'platform_id' =>(int) $imgs_request['platform_id'],
                    'second_type'=>$imgs_request['second_type'],
                    'extension'=>'',
                    'thumb_src'=>'',
                    'share_path'=>$param['rendering_path']
                ];
                if (!$imgs_request_file) {
                    $db->table('imgs_request_collection')->insert($insert_data);
                } else {
                    $db->table('imgs_request_collection')
                        ->where('id = :id',['id'=>$imgs_request_file['id']])
                        ->update($insert_data);
                }
            }
            $del_list = array_diff($collection,$new_list);
            if (count($del_list)) {
                $ids = [];
                foreach ($collection_list as $v) {
                    if (in_array($v['url'],$del_list)) {
                        $ids[] = $v['id'];
                    }
                }
                $db->table('imgs_request_collection');
                $db->whereIn('id',$ids);
                $db->update(['is_delete'=>1]);
            }
            //日志
            imgsRequestModel::setLog($db,$imgs_request['id'],5,$param['remarks']);
            $db->commit();
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //更新文件
    public static function updateImg($param) {
        $request_id = (int)$param['request_id'];
        $db = dbMysql::getInstance();
        $imgs_request = imgsRequestModel::getRequest($db,$request_id);
        $images = json_decode($param['images'],true);
        if (!count($images)) {
            SetReturn(-1,'结果件必传');
        }
        $source_file = json_decode($param['source_file'],true);
        //获取列表
        $collection_list = $db->table('imgs_request_collection')
            ->where('where request_id=:request_id and is_delete = 0 and source_type=0',['request_id'=>$request_id])
            ->field('id,url')
            ->list();
        $collection = array_column($collection_list,'url');
        $new_list = [];
        //是否为app产需求
        $is_goods_request = 0;
        if ($imgs_request['platform_id'] == 1 && $imgs_request['type'] == 1) {
            $is_goods_request = 1;
        }
        $db->beginTransaction();
        try {
            //结果文件
            if ($is_goods_request) {
                //app产品图
                foreach ($images as $imgs) {
                    $color_id = (int)$imgs['color_id'];
                    if (empty($color_id) && $is_goods_request) {
                        SetReturn(-1,'结果件缺少颜色ID');
                    }
                    foreach ($imgs['imgs'] as $AB) {
                        $pic_type = empty($AB['pic_type'])?1:(int)$AB['pic_type'];
                        if (!in_array($pic_type,[1,2])) {
                            SetReturn(-1,'结果件AB图标识有误');
                        }
                        foreach ($AB['imgs'] as $v) {
                            if (!isset($v['url']) || !isset($v['thumb_src'])) {
                                SetReturn(-1,'结果文件图片压缩路径必传');
                            }
                            $new_list[] = $v['url'];
                            if (!in_array($v['url'],$collection)) {
                                $name_array = explode('.',$v['file_name']);
                                $insert_data = [
                                    'is_confirm'=>1,
                                    'user_id' => userModel::$qwuser_id,
                                    'goods_id'=>$imgs_request['goods_id'],
                                    'request_id'=>$request_id,
                                    'language_id'=>$imgs_request['language_code'],
                                    'file_name'=>$v['file_name'],
                                    'url'=>$v['url'],
                                    'created_time'=>date('Y-m-d H:i:s'),
                                    'file_type'=>3,
                                    'color_id'=>$color_id,
                                    'thumb_src'=>$v['thumb_src'],
                                    'type'=>$imgs_request['type'],
                                    'platform_id' => (int) $imgs_request['platform_id'],
                                    'second_type'=>$imgs_request['second_type'],
                                    'extension'=>end($name_array),
                                    'pic_type'=>$pic_type
                                ];
                                $db->table('imgs_request_collection')->insert($insert_data);
                            }
                        }
                    }
                }
            } else {
                //其他图
                foreach ($images as $imgs) {
                    $color_id = (int)$imgs['color_id'];
                    foreach ($imgs['imgs'] as $v) {
                        $pic_type = 0;
                        if (!isset($v['url']) || !isset($v['thumb_src'])) {
                            SetReturn(-1, '结果文件图片压缩路径必传');
                        }
                        $new_list[] = $v['url'];
                        if (!in_array($v['url'], $collection)) {
                            $name_array = explode('.', $v['file_name']);
                            $insert_data = [
                                'is_confirm'=>1,
                                'user_id' => userModel::$qwuser_id,
                                'goods_id' => $imgs_request['goods_id'],
                                'request_id' => $request_id,
                                'language_id'=>$imgs_request['language_code'],
                                'file_name' => $v['file_name'],
                                'url' => $v['url'],
                                'created_time' => date('Y-m-d H:i:s'),
                                'file_type' => 3,
                                'color_id' => $color_id,
                                'thumb_src' => $v['thumb_src'],
                                'type' => $imgs_request['type'],
                                'platform_id' => (int) $imgs_request['platform_id'],
                                'second_type' => $imgs_request['second_type'],
                                'extension' => end($name_array),
                                'pic_type' => $pic_type
                            ];
                            $db->table('imgs_request_collection')->insert($insert_data);
                        }
                    }
                }
            }
            //源文件处理
            if (count($source_file)) {
                foreach ($source_file as $v) {
                    $new_list[] = $v['url'];
                    if (!in_array($v['url'],$collection)) {
                        $name_array = explode('.',$v['file_name']);
                        $insert_data = [
                            'is_confirm'=>1,
                            'user_id' => userModel::$qwuser_id,
                            'goods_id'=>$imgs_request['goods_id'],
                            'language_id'=>$imgs_request['language_code'],
                            'request_id'=>$request_id,
                            'file_name'=>$v['file_name'],
                            'url'=>$v['url'],
                            'created_time'=>date('Y-m-d H:i:s'),
                            'file_type'=>1,
                            'type'=>$imgs_request['type'],
                            'platform_id' => (int) $imgs_request['platform_id'],
                            'second_type'=>$imgs_request['second_type'],
                            'extension'=>end($name_array),
                            'thumb_src'=>$v['thumb_src'],
                        ];
                        $db->table('imgs_request_collection')->insert($insert_data);
                    }
                }
            }
            //渲染文件
            $rendering_files = empty($param['rendering_files'])?[]:json_decode($param['rendering_files'],true);
            if (count($rendering_files)) {
                foreach ($rendering_files as $v) {
                    $new_list[] = $v['url'];
                    $name_array = explode('.',$v['file_name']);
                    if (!in_array($v['url'],$collection)) {
                        $insert_data = [
                            'is_confirm'=>1,
                            'user_id' => userModel::$qwuser_id,
                            'goods_id'=>$imgs_request['goods_id'],
                            'request_id'=>$request_id,
                            'language_id'=>$imgs_request['language_code'],
                            'file_name'=>$v['file_name'],
                            'url'=>$v['url'],
                            'created_time'=>date('Y-m-d H:i:s'),
                            'file_type'=>2,
                            'type'=>$imgs_request['type'],
                            'platform_id' => (int) $imgs_request['platform_id'],
                            'second_type'=>$imgs_request['second_type'],
                            'extension'=>$name_array[1],
                            'thumb_src'=>$v['thumb_src'],
                        ];
                        $db->table('imgs_request_collection')->insert($insert_data);
                    }
                }
                //共享路径写进去
                $db->table('imgs_request_collection')
                    ->where('file_type = 2 and request_id = :request_id',['request_id'=>$request_id])
                    ->update([
                        'share_path'=>$param['rendering_path']
                    ]);
            } else {
                $imgs_request_file = $db->table('imgs_request_collection')
                    ->where('file_type = 2 and request_id = :request_id and url = \'\' and is_delete = 0',['request_id'=>$request_id])
                    ->one();
                //保存一个路径再
                $insert_data = [
                    'is_confirm'=>1,
                    'user_id' => userModel::$qwuser_id,
                    'goods_id'=>$imgs_request['goods_id'],
                    'request_id'=>$request_id,
                    'language_id'=>$imgs_request['language_code'],
                    'file_name'=>'',
                    'url'=>'',
                    'created_time'=>date('Y-m-d H:i:s'),
                    'file_type'=>2,
                    'type'=>$imgs_request['type'],
                    'platform_id' =>(int) $imgs_request['platform_id'],
                    'second_type'=>$imgs_request['second_type'],
                    'extension'=>'',
                    'thumb_src'=>'',
                    'share_path'=>$param['rendering_path']
                ];
                if (!$imgs_request_file) {
                    $db->table('imgs_request_collection')->insert($insert_data);
                } else {
                    $db->table('imgs_request_collection')
                        ->where('id = :id',['id'=>$imgs_request_file['id']])
                        ->update($insert_data);
                }
            }
            $del_list = array_diff($collection,$new_list);
            if (count($del_list)) {
                $ids = [];
                foreach ($collection_list as $v) {
                    if (in_array($v['url'],$del_list)) {
                        $ids[] = $v['id'];
                    }
                }
                $db->table('imgs_request_collection');
                $db->whereIn('id',$ids);
                $db->update(['is_delete'=>1]);
            }
            //日志
            imgsRequestModel::setLog($db,$imgs_request['id'],5,$param['remarks']);
            $db->commit();
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //提交
    public static function submitImgs($param) {
        $request_id = (int)$param['request_id'];
        $db = dbMysql::getInstance();
        $imgs_request = imgsRequestModel::getRequest($db,$request_id);
        if (!$imgs_request['qwuser_id']) {
            SetReturn(-1,'该需求还未配置美工');
        }
        if ($imgs_request['qwuser_id'] != userModel::$qwuser_id) {
            SetReturn(-1,'非当前配置美工不可提交');
        }
        $collection_list = $db->table('imgs_request_collection')
            ->where('where request_id=:request_id and is_delete = 0 and is_confirm = 0 and source_type=0',['request_id'=>$request_id])
            ->field('id,url')
            ->list();
        if (!count($collection_list)) {
            SetReturn(-1,'请先上传文件');
        }
        $goods_info = $db->table('goods_new')
            ->where('where id=:goods_id',['goods_id'=>$imgs_request['goods_id']])
            ->field('id,goods_name')
            ->one();
        $db->beginTransaction();
        try {
            //修改需求状态
            if ($imgs_request['type'] == 1 && $imgs_request['platform_id'] == 1) {
                //app产品图需要要审核
                $db->table('imgs_request')
                    ->where('where id=:id',['id'=>$request_id])
                    ->update(['status'=>2,'is_check'=>0,'submit_time'=>date('Y-m-d H:i:s')]);
                //审核人添加
                $check_info = configFrom::getConfigByName('images_check_user');
                $check_info = json_decode($check_info,true);
                if (!count($check_info)) {
                    returnError('app产品图审核人未配置，请联系管理员');
                }
                //生成待办事件 + 处理待办事件为待审 +消息推送
                imgsRequestFrom::setImgCheckMatter($imgs_request,$goods_info);
                //绑定审核人
                $user_ids = array_column($check_info,'id');
                requestParticipantForm::updateUser($db,$request_id,$user_ids,10);
            } else {
                $db->table('imgs_request')
                    ->where('where id=:id',['id'=>$request_id])
                    ->update([
                        'status'=>3,
                        'submit_time'=>date('Y-m-d H:i:s'),
                        'completion_time'=>time(),
                    ]);
                //图片集合归档
                $db->table('imgs_request_collection')
                    ->where('request_id=:request_id and is_delete = 0 and is_confirm = 0 and source_type = 0',['request_id'=>$request_id])
                    ->update(['is_confirm'=>1]);
                    
                // 更新美工待办事件状态为已完成
                $db->table('goods_matters')
                    ->where('model_id = :model_id AND status = 0 AND status = 0 AND type = 4 AND create_type = 0 AND qwuser_id = :qwuser_id',
                        ['model_id' => $request_id,'qwuser_id' => $imgs_request['qwuser_id']])
                    ->update([
                        'status' => 1,
                        'completion_time' => time()
                    ]);
                    
                //完成消息发送
                $msg_text = "【{$goods_info['goods_name']}】【{$imgs_request['request_name']}】的任务已提交，当前作图任务已完成";
                imgsRequestModel::sendMsgToParticipant($db,$request_id,$msg_text);
            }
            imgsRequestModel::setLog($db,$imgs_request['id'],6,$param['remarks']);
            $db->commit();
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //app图片审核
    public static function checkAppImg($param) {
        $is_pass = (int)$param['is_pass'];
        if (!$is_pass && empty($param['reason'])) {
            SetReturn(-1,'请填写不通过原因');
        }
        $db = dbMysql::getInstance();
        $imgs_request = imgsRequestModel::getRequest($db,$param['request_id']);
        if ($imgs_request['status'] != 2) {
            SetReturn(-1,'请先提交');
        }
        if ($imgs_request['is_check'] != 0) {
            SetReturn(-1,'切勿重复操作');
        }
        if (!($imgs_request['type'] == 1 && $imgs_request['platform_id'] == 1)) {
            returnError('非app产品图不用审核');
        }
        //审核通过告知抄送人
        $copy_user = [];
        if ($is_pass==1) {
            $app_images_check_copy_user = configFrom::getConfigByName('app_images_check_copy_user');
            if ($app_images_check_copy_user) {
                $check_info = json_decode($app_images_check_copy_user,true);
                $copy_user = $check_info;
            }
        }
        $remarks = $is_pass==1?$param['remarks']:$param['reason'];
        //审核人判断
        $participant = $db->table('imgs_request_participant')
            ->where('type = 10 and request_id = :request_id',['request_id'=>$param['request_id']])
            ->field('user_id')
            ->list();
        $user_ids = array_column($participant,'user_id');
        if (!in_array(userModel::$qwuser_id,$user_ids)) {
            returnError('无权审核');
        }
        $goods_info = $db->table('goods_new')
            ->where('where id=:goods_id',['goods_id'=>$imgs_request['goods_id']])
            ->field('id,manage_info,goods_name')
            ->one();
        $db->beginTransaction();
        try {
            //审核状态处理
            imgsRequestFrom::setPassStatus($is_pass,$imgs_request,$goods_info,$remarks);
            //消息推送给
            $users_id = imgsRequestFrom::sendMsgForCheck($is_pass,$imgs_request,$goods_info,$copy_user,$remarks);
            requestParticipantForm::updateUser($db,$param['request_id'],$users_id,7);
            //日志
            $content = $is_pass == 1 ? '审核通过':'审核未通过';
            imgsRequestModel::setLog($db,$imgs_request['id'],12,$remarks,$content);
            $db->commit();
            SetReturn(0,'操作成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }


    //验证该商品需求图是否完成
    public static function appImgIsFinished($goods_id) {
        $db = dbMysql::getInstance();
        $imgs_request = $db->table('imgs_request')
            ->where('where goods_id=:goods_id and type=1 and is_delete = 0',['goods_id'=>$goods_id])
            ->one();
        if ($imgs_request['status'] != 3) {
            SetReturn(-1,'图片需求还未完成');
        }
    }
    //app适配节点完成，生成产品需求 (app产品图需求生产不再是独立的事件，必选跟着app适配后自动生成)
    public static function setAppPicRequest($goods_info){
        $db = dbMysql::getInstance();
        $imgs_request = $db->table('imgs_request')
            ->where('where goods_id=:goods_id and type=1 and platform_id = 1 and is_delete = 0',['goods_id'=>$goods_info['id']])
            ->one();
        if (!$imgs_request) {
            $images_expect_hours = configFrom::getConfigByName('images_expect_hours');
            //生成app图需求
            $goods_manage = json_decode($goods_info['manage_info'],true);
            if ($images_expect_hours) {
                $expected_time = strtotime("+{$images_expect_hours} hours", time());
            } else {
                $expected_time = 0;
            }
            $color_info = $db->table('goods_color_relation')
                ->where('goods_id=:goods_id',['goods_id'=>$goods_info['id']])
                ->field('id,color_id')
                ->list();
            $color_ids = array_column($color_info, 'color_id');
            $color_id = json_encode($color_ids);
            //app直接走终线配置,终线配置相关数据id为0
            $request_checke_id = 0;
            $request_distributor_id = 0;

            // 1. 先查一行
            $row = $db->table('imgs_request_distributor')
                ->where('type = :t', ['t' => '["5"]'])
                ->field('id, distributor_id')
                ->one();

            if (!empty($row)) {
                // 2. 记录 distributor 表里的主键
                $request_distributor_id = $row['id'];

                // 3. 拿到 JSON 字符串
                $json = $row['distributor_id'];

                // 4. 解码成 PHP 数组
                $ids = json_decode($json, true);
                if (!is_array($ids)) {
                    $ids = [];
                }

                // 5. 取第一个负责人 ID
                $request_distributor_user_id = $ids[0] ?? null;
            }
            else {
                // fallback: 从 config 表里拿默认列表
                $cfg = $db->table('config')
                    ->where('key_name = "imgs_request_distributor"')
                    ->field('data')
                    ->one();
                $ids = json_decode($cfg['data'] ?? '[]', true);
                if (!is_array($ids)) {
                    $ids = [];
                }
                $request_distributor_user_id = $ids[0] ?? null;
            }
            if (!$request_distributor_user_id) {
                returnError('图片任务配置-未配置负责人请联系管理员');
            }
            $insert_data = [
                'goods_id'=>$goods_info['id'],
                'user_id'=>$goods_manage[0]['id'],
                'request_checke_id'=>$request_checke_id,
                'request_distributor_id'=>$request_distributor_id,
                'allocation_user_id'=>$request_distributor_user_id,
                'request_name'=>$goods_info['goods_name'].'_APP产品图',
                'type'=>1,
                'nature_demand'=>5,
                'category_id'=>$goods_info['cat_id'],
                'color_info'=>$color_id,
                'platform_id'=>1,
                'status'=>0,
                'latest_expect_time'=>$expected_time,
                'created_time'=>date('Y-m-d H:i:s'),
                'begin_time'=>$expected_time,
                'real_expected_time'=>$expected_time,
            ];

            $id = $db->table('imgs_request')->insert($insert_data);
            
            // 存储参与者信息
            if (!empty($request_distributor_user_id)) {
                $participant_data = [
                    'request_id' => $id,
                    'user_id' => $request_distributor_user_id,
                    'type' => 3  // 3表示配置美工者
                ];
                $db->table('imgs_request_participant')->insert($participant_data);
            }
            
            //开发和运营人员存储
            self::getDevAndOperate($goods_info['id'],$id);
            //通知
            //获取配置人
            $allocation_user = configFrom::getConfigData(['pic_manage']);
            if (!$allocation_user['pic_manage']) {
                SetReturn(-1,'图片负责人未配置，请联系系统管理员');
            }
            $manage_info = json_decode($allocation_user['pic_manage'],true);
            //生成待办事项
            goodsMattersFrom::addCreateMatter($goods_info['goods_name'].'_APP产品图需求',$goods_info['id'],'美工配置',0,4,$manage_info[0]['id'],$id,$expected_time);
            //消息
            self::$msg_info[] = [
                'wids'=>array_column($manage_info,'wid'),
                'msg'=>"产品【{$goods_info['goods_name']}】APP产品图需求已提交给您，请及时查看并分配制作员。",
                'other_data'=>[
                    'user_id'=>userModel::$qwuser_id,
                    'model_id'=>$id,
                    'msg_type'=>5
                ],
            ];
        }
    }
    //美工提交时 生成交办数据
    public static function setImgCheckMatter($imgs_reques,$goods_info) {
        if ($imgs_reques['type'] == 1) {
            //修改提交的待办事件为完成
            $db = dbMysql::getInstance();
            $db->table('goods_matters')
                ->where('where type = 4 and model_id=:reques_id and status <> 1 and create_type = 0',['reques_id'=>$imgs_reques['id']])
                ->update(['status'=>1]);
            //生成审核待办
            $check_info = configFrom::getConfigByName('images_check_user');
            $check_info = json_decode($check_info,true);
            goodsMattersFrom::addCreateMatter($imgs_reques['request_name'],$imgs_reques['goods_id'],'审核美工图',1,4,$check_info[0]['id'],$imgs_reques['id'],0);
            //消息推送
            $msg = messagesFrom::getMsgTxtForImgRequest(2,$goods_info['goods_name'],$imgs_reques['request_name']);
            $other_data = [
                'user_id'=>userModel::$qwuser_id,
                'model_id'=>$imgs_reques['id'],
                'msg_type'=>7
            ];
            messagesFrom::senMeg([$check_info[0]['wid']],$msg,$other_data);
        } else {
            SetReturn(-1,'类型不对');
        }

    }
    //需求审核状态更新+待办事件处理
    public static function setPassStatus($is_pass,$imgs_reques,$goods_info,$remarks) {
        $is_check = $is_pass?1:2;
        $now_time = time();
        $db = dbMysql::getInstance();
        
        // 准备更新数据
        $update_data = [
            'is_check' => $is_check
        ];
        
        // 只在审核通过时更新这些时间
        if ($is_pass) {
            $update_data['task_reviewed_time'] = $now_time;
            $update_data['completion_time'] = $now_time;
        }
        
        //修改审核状态
        $db->table('imgs_request')
            ->where('where id=:request_id',['request_id'=>$imgs_reques['id']])
            ->update($update_data);
        //记录处理日志
        $db->table('imgs_request_check_log');
        $db->insert([
            'request_id'=>$imgs_reques['id'],
            'check_user_id'=>userModel::$qwuser_id,
            'is_check'=>$is_check,
            'reason'=>$remarks,
            'created_time'=>date('Y-m-d H:i:s'),
        ]);
        if ($is_pass) {
            //设置上传的图片为确认状态
            $db->table('imgs_request_collection')
                ->where('where request_id=:request_id and is_delete = 0 and is_confirm = 0 and source_type = 0',['request_id'=>$imgs_reques['id']])
                ->update(['is_confirm'=>1]);
            //设需求状态为已完成
            $db->table('imgs_request')
                ->where('where id=:id',['id'=>$imgs_reques['id']])
                ->update(['status'=>3]);
        } else {
            //设需求状态为进行中
            $db->table('imgs_request')
                ->where('where id=:id',['id'=>(int)$imgs_reques['id']])
                ->update(['status'=>1]);
            //重新添加待办事项
            goodsMattersFrom::addCreateMatter($goods_info['goods_name'],$imgs_reques['goods_id'],$imgs_reques['request_name'],0,4,$imgs_reques['qwuser_id'],$imgs_reques['id'],$imgs_reques['real_expected_time']);
        }
        //审核待办事项为已完成
        $db->table('goods_matters')
            ->where('where type = 4 and model_id=:reques_id and status <> 1 and create_type = 1',['reques_id'=>$imgs_reques['id']])
            ->update(['status'=>1]);
        self::msgForCheckWid($is_pass,$imgs_reques,$goods_info);
    }
    //图片审核推送数据获取 不可在循环中使用
    public static function msgForCheckWid($is_pass,$imgs_reques,$goods_info) {
        $db = dbMysql::getInstance();
        //推给配置人，美工
        $user_ids = [$imgs_reques['allocation_user_id'],$imgs_reques['qwuser_id']];
        $user_list = $db->table('qwuser')
            ->whereIn('id',$user_ids)
            ->field('wid')
            ->list();
        $wids = array_column($user_list,'wid');
        if (!$is_pass) {
            //推给产品负责人
            $manage_wid = array_column(json_decode($goods_info['manage_info']),'wid');
            $wids = array_merge($wids,$manage_wid);
        }
        $wids = array_unique($wids);

        self::$chechk_img_msg_wid = array_unique($wids);
    }
    //发送消息给抄送人
    public static function sendMsgForCheck($is_pass,$imgs_reques,$goods_info,$copy_user,$remarks = ''){
        $msg = messagesFrom::getMsgTxtForImgRequest(($is_pass==1?3:4),$goods_info['goods_name'],$imgs_reques['request_name']);
        $wids = array_column($copy_user,'wid');
        $wids = array_unique(array_merge($wids,self::$chechk_img_msg_wid));
        $other_data = [
            'user_id'=>userModel::$qwuser_id,
            'model_id'=>$imgs_reques['id'],
            'msg_type'=>7
        ];
        messagesFrom::senMeg($wids,$msg,$other_data,$remarks);
        return array_column($copy_user,'id');
    }
    //修改需需求的待办事件预计完成时间
    public static function changeExpectTime($goods_id) {
        $db = dbMysql::getInstance();
        $imgs_request = $db->table('imgs_request')
            ->where('where goods_id=:goods_id and type=1 and is_delete = 0 and (status=0 or status=1)',['goods_id'=>$goods_id])
            ->one();
        if ($imgs_request) {
            $take_pic_expected_hours = configFrom::getConfigByName('take_pic_expected_hours');
            if ($take_pic_expected_hours) {
                $expected_time = strtotime("+{$take_pic_expected_hours} hours", $imgs_request['expected_time']);
            } else {
                $expected_time = 0;
            }
            //需求时间修改
            $db->table('imgs_request')
                ->where('where id=:id',['id'=>$imgs_request['id']])
                ->update([
                    'expected_time'=>$expected_time,
                    'real_expected_time'=>$expected_time,
                ]);
            //待办时间修改
            $db->table('goods_matters')
                ->where('where model_id=:model_id and type=4 and status=0 and create_type=0',['model_id'=>$imgs_request['id']])
                ->update(['expected_time'=>$expected_time]);
        }
    }
    public static function getFileTypeName(int $type) {
        $type_name = '';
        switch ($type) {
            case 1:
                $type_name =  '源文件';break;
            case 2:
                $type_name = '渲染文件';break;
            case 3:
                $type_name = '结果文件';break;
            case 4:
                $type_name = '说明书pdf';break;
            case 5:
                $type_name = '说明书竖版';break;
        }
        return  $type_name;
    }
    public static function getTypeName(int $source_type, int $type, int $platform_id) {
        $type_name = '';
        switch ($source_type) {
            case 0:
                $type_name = $type==0?'':config::getDataName('imgs_request_type',$platform_id);
                break;
            case 1:
                $type_name = '说明书';break;
        }
        return  $type_name;
    }
    //根据产品信息，获取开发和运营
    public static function getDevAndOperate($goods_id,$id): void
    {
        $db = dbMysql::getInstance();

        // 1. 读取商品表 manage_info 和 operator_info
        $row = $db->table('goods_new')
            ->where('id = :id', ['id' => $goods_id])
            ->field('manage_info, operator_info')
            ->one();

        if (! $row) {
            return;
        }

        // 2. 解析 JSON，容错返回空数组
        $devList = json_decode($row['manage_info'], true) ?: [];
        $opList  = json_decode($row['operator_info'], true) ?: [];

        // 3. 提取 user_id 列表
        $devIds     = array_column($devList, 'id');
        $operateIds = array_column($opList,   'id');

        // 4. 事务：删除旧数据 & insertIgnore 新数据
            $requestId = $id;

            // 删除旧的"开发"和"运营"记录
            $db->table('imgs_request_participant')
                ->where('request_id = :rid AND type = :t', ['rid' => $requestId, 't' => 5])
                ->delete();
            $db->table('imgs_request_participant')
                ->where('request_id = :rid AND type = :t', ['rid' => $requestId, 't' => 6])
                ->delete();

            // 插入新的开发记录 (type=5)
            foreach ($devIds as $uid) {
                $db->table('imgs_request_participant')
                    ->insertIgnore([
                        'request_id' => $requestId,
                        'user_id'    => $uid,
                        'type'       => 5,
                    ]);
            }

            // 插入新的运营记录 (type=6)
            foreach ($operateIds as $uid) {
                $db->table('imgs_request_participant')
                    ->insertIgnore([
                        'request_id' => $requestId,
                        'user_id'    => $uid,
                        'type'       => 6,
                    ]);
            }

    }

    //图片资源申请(新增修改)
    /**
     * 新增或编辑图片资源申请
     */
    public static function applyForImageResource(array $param)
    {
        $db = dbMysql::getInstance();
        // 校验备注长度
        $remarks = trim((string)($param['description'] ?? ''));
        if ($remarks !== '' && mb_strlen($remarks) > 201) {
            returnError('备注信息长度不能超过 200 个字符');
        }
        // 1. SOP 文件校验
        self::checkSopFiles($param['has_sop'] ?? '');
        // 2. 构建 $data
        $data = [
            'request_name'       => $param['request_name'] ?? '',  // 需求名称，非空时使用提供的值，空时使用默认值
            'type'               => $param['type'] ?? '',  // 类型，非空时使用提供的值，空时使用默认值
            'platform_id'        => isset($param['platform_id']) && $param['platform_id'] !== '' ? (int)$param['platform_id'] : null,  // 平台ID，非空时转换为整数，空时为null
            'brand_id'           => isset($param['brand_id']) && is_numeric($param['brand_id']) ? (int)$param['brand_id'] : null,  // 品牌ID，非空时转换为整数，空时为null
            'country_code'       => $param['country_code'] ?? '',  // 国家编码，非空时使用提供的值，空时使用默认值
            'goods_id'           => isset($param['goods_id']) && $param['goods_id'] !== '' ? (int)$param['goods_id'] : 0,  // 商品ID，非空时转换为整数，空时为null
            'category_id'        => $param['category_id'] ?? null,  // 类目ID，若未提供，设置为null
            'color_info'         => $param['color_info'] ?? null,
            'nature_demand'      => isset($param['nature_demand']) && $param['nature_demand'] !== '' ? (int)$param['nature_demand'] : null,  // 需求性质，非空时转换为整数，空时为null
            'wp_id'              => isset($param['wp_id']) && $param['wp_id'] !== '' ? $param['wp_id'] : null,  // 企微部门ID，非空时使用提供的值，空时为null
            'language_code'      => $param['language'] ?? '',  // 语言编码，非空时使用提供的值，空时使用默认值
            'latest_expect_time' => !empty($param['latest_expect_time']) ? strtotime($param['latest_expect_time']) : null,  // 最晚期望时间，非空时转换为时间戳，空时为null
            'has_sop'            => $param['has_sop'] ?? '',  // SOP路径，非空时使用提供的值，空时使用默认值
            'description'        => $param['description'] ?? '',  // 需求描述，非空时使用提供的值，空时使用默认值
            'request_checke_id'  => 0,  // 默认审核人配置ID为0
            'request_distributor_id' => 0,  // 默认图片负责人配置ID为0
            'status' => 4,  // 修改和新增后状态都为4，sop待审核
        ];
        $now = time();
        $id  = !empty($param['id']) ? (int)$param['id'] : null;

        try {
            $db->beginTransaction();

            // 3. 获取审核人列表
            $checkerInfo = self::fetchCheckers($db, $param);
            if (!empty($checkerInfo[0])) {
                // 存储审核人配置ID
                $data['request_checke_id'] = $checkerInfo[1];
            }
            // 4. 获取分配人
            $distributorInfo = self::fetchDistributor($db, $param);
            if (!empty($distributorInfo[0])) {
                // 存储分配人信息
                $data['allocation_user_id'] = $distributorInfo[0][0];
                // 存储图片负责人配置ID
                $data['request_distributor_id'] = $distributorInfo[1];
            }

            // 5. 新增或更新
            if ($id) {
                imgsRequestModel::getRequest($db, $id);
                $info = $db->table('imgs_request')->where('id = :id', ['id' => $id])->one();
                if (!$info) {
                    returnError('需求不存在');
                }
                if ($info['status'] != 4 && $info['status'] != 9) {
                    returnError('状态不允许修改');
                }

                $data['update_time'] = $now;

                $db->table('imgs_request')->where('id = :id', ['id' => $id])->update($data);

                imgsRequestModel::setLog($db,$id, 7, '');
                // 如果是更新且有分配人，记录分配人日志
                if (!empty($distributorInfo[0])) {
                    self::User_log($id, 3, $distributorInfo[0][0]);
                }
            } else {
                $data['user_id']    = userModel::$qwuser_id;
                $data['created_time']= date('Y-m-d H:i:s', $now);

                $id = $db->table('imgs_request')->insert($data);
                imgsRequestModel::setLog($db,$id, 1, '');
                self::User_log($id, 1);
                // 如果是新增且有分配人，记录分配人日志
                if (!empty($distributorInfo[0])) {
                    self::User_log($id, 3, $distributorInfo[0][0]);
                }
            }
            $goods_name = $db->table('goods_new')->where('id = :goods_id', ['goods_id' => $param['goods_id']])->one();
            if (!empty($goods_name)){
                $goods_name = $goods_name['goods_name'];
            }else{
                $goods_name = '';
            }

            // 6. 生成待办 & 通知
            if (!empty($checkerInfo[0])) {
                //$checkerInfo[0]为sop审核配置的人员
                self::notifyCheckers($checkerInfo[0], strtotime($param['latest_expect_time']), $data['goods_id'], $id, $data, $goods_name);
            }
            //记录开发和运营人员
            self::getDevAndOperate($param['goods_id'],$id);
            $db->commit();
            returnSuccess('', '操作成功');
        } catch (\Exception $e) {
            $db->rollBack();
            returnError('操作失败：' . $e->getMessage());
        }
    }
    /**
     * 统一封装为 JSON 数组字符串（["xxx"] 形式）
     */
    private static function toJsonArray($input): string
    {
        if (is_array($input)) {
            return json_encode($input, JSON_UNESCAPED_UNICODE);
        }
        if (is_string($input) && str_starts_with($input, '[')) {
            return $input;
        }
        return json_encode([$input], JSON_UNESCAPED_UNICODE);
    }

    /**
     * 校验 SOP 文件格式
     */
    private static function checkSopFiles(string $hasSopJson): void
    {
        if (empty($hasSopJson) || $hasSopJson === '[]') {
            return;
        }
        $allowed = ['xlsx', 'xls', 'docx', 'doc', 'pdf', 'ppt', 'pptx'];
        $files = json_decode($hasSopJson, true);
        if (json_last_error() !== JSON_ERROR_NONE || !is_array($files)) {
            returnError("文件路径格式不正确");
        }
        foreach ($files as $file) {
            $extension = strtolower(pathinfo($file['file_name'], PATHINFO_EXTENSION));
            if (!in_array($extension, $allowed, true)) {
                returnError("文件 '{$file['file_name']}' 类型不正确");
            }
        }
    }

    /**
     * 从 imgs_request_checker 表中获取审核人列表
     * 返回一个 check_id 数组
     */
    public static function fetchCheckers($db, array $param): array
    {
        $category_id  = self::toJsonArray($param['category_id']);
        $country_code = self::toJsonArray($param['country_code']);

        // 先查询国家对应的配置
        $row = $db->table('imgs_request_checker')
            ->where(
                'JSON_OVERLAPS(country_code, :country_code)',
                ['country_code' => $country_code]
            )
            ->one();

        if (empty($row)) {
            $result = $db->table('config')->where('key_name = "imgs_request_sop_checker"')->one();
            $row['check_id'] = $result['data'] ?? '[]';
        }
        // 如果category_id为[]，表示适用于所有类目，直接返回
        if ($row['category_id'] === '[]') {
            $ids = json_decode($row['check_id'], true);
            return [$ids ?? [], $row['id'] ?? 0];
        }

        // 否则需要检查类目是否匹配
        $row = $db->table('imgs_request_checker')
            ->where(
                'JSON_OVERLAPS(category_id, :category_id)
                 AND JSON_OVERLAPS(country_code, :country_code)',
                [
                    'category_id'  => $category_id,
                    'country_code' => $country_code,
                ]
            )
            ->one();

        if (empty($row['check_id'])) {
            $result = $db->table('config')->where('key_name = "imgs_request_sop_checker"')->one();
            $row['check_id'] = $result['data'] ?? '[]';
        }
        $ids = json_decode($row['check_id'], true);
        return [$ids ?? [], $row['id'] ?? 0];
    }

    /**
     * 从 imgs_request_distributor 表中获取分配人 ID
     * @return array [distributor_id, config_id] 找到则返回分配人ID和配置ID，否则返回[null, 0]
     */
    private static function fetchDistributor($db, array $param): array
    {
        $category_id = self::toJsonArray($param['category_id']);
        $country_code = self::toJsonArray($param['country_code']);
        $type = self::toJsonArray($param['type']);

        // 先查询国家和type对应的配置
        $row = $db->table('imgs_request_distributor')
            ->where(
                'country_code = :cc AND type = :type AND is_delete = 0',
                [
                    'cc'  => $country_code,
                    'type'=> $type
                ]
            )
            ->one();
        if (empty($row)) {
            $result = $db->table('config')->where('key_name = "imgs_request_distributor"')->one();
            $row['distributor_id'] = $result['data'] ?? '[]';
        }

        // 如果category_id为[]，表示适用于所有类目，直接返回
        if ($row['category_id'] === '[]') {
            $ids = json_decode($row['distributor_id'], true);
            return [$ids ?? [], $row['id'] ?? 0];
        }

        // 否则需要检查类目是否匹配
        $row = $db->table('imgs_request_distributor')
            ->where(
                'country_code = :cc AND type = :type AND is_delete = 0',
                [
                    'cc'  => $country_code,
                    'type'=> $type
                ]
            )
            ->andWhere('JSON_OVERLAPS(category_id, :category_id_json)', ['category_id_json' => $category_id])
            ->one();

        if (empty($row)) {
            $result = $db->table('config')->where('key_name = "imgs_request_distributor"')->one();
            $row['distributor_id'] = $result['data'] ?? '[]';
        }
        $ids = json_decode($row['distributor_id'], true);
        return [$ids ?? '[]', $row['id'] ?? 0];
    }


    /**
     * 生成待办事项并发送通知
     */
    private static function notifyCheckers(array $checkerIds, int $latest_expect_time, $goods_id, int $modelId,$data,$goods_name,int $matterType = 4): void
    {
        $db = dbMysql::getInstance();
        $expected = time();
        foreach ($checkerIds as $cid) {
            goodsMattersFrom::addCreateMatter(
                $data['request_name'],
                $goods_id,
                '图片需求SOP审核',
                1,
                $matterType,
                $cid,
                $modelId,
                $latest_expect_time
            );

            self::User_log($modelId, 2,$cid);
            // 获取申请人信息
            $message = imgsRequestModel::getMessage(4,$data['request_name'],$goods_name);
            $wid = $db->table('qwuser')->where('id = :id', ['id' => $cid])->one();
            $wid = $wid['wid'];
            messagesFrom::senMessage([$wid], $message, userModel::$qwuser_id, $modelId, $matterType);
        }

    }
    //审核
    public static function audit(array $param): void
    {
        $db = dbMysql::getInstance();

        // 校验备注长度
        $remarks = trim((string)($param['check_remarks'] ?? ''));
        if ($remarks !== '' && mb_strlen($remarks) > 201) {
            returnError('备注信息长度不能超过 200 个字符');
        }

        // 获取申请记录
        $request = $db->table('imgs_request')
            ->where('id = :id', ['id' => $param['id']])
            ->one();
        if (!$request) {
            returnError('需求不存在');
        }

        // 获取商品和申请人
        $goods     = $db->table('goods_new')
            ->where('id = :gid', ['gid' => $request['goods_id']])
            ->one();
        $applicant = $db->table('qwuser')
            ->where('id = :uid', ['uid' => $request['user_id']])
            ->field('id, wid, wname')
            ->one();

        $db->beginTransaction();
        try {
            // 初始化变量
            $status    = 0;
            $logType   = 0;
            $logRemark = '';

            if ((int)$param['is_check'] === 1) {
                // 审核通过
                $status   = 0;
                $logType  = 2;

                // 查询分配人配置
                $distRow = $db->table('imgs_request_participant')
                    ->where('type =:type AND request_id =:request_id',['type' => 3, 'request_id' => $request['id']])
                    ->one();
                
                // 统一处理分配人ID格式
                $distributorId = null;
                if (!empty($distRow['user_id'])) {
                    // 如果是单个ID的情况
                    $distributorId = (int)$distRow['user_id'];
                } else {
                    // 如果是配置中的JSON数组
                    $configData = $db->table('config')->where('key_name = "imgs_request_distributor"')->one()['data'] ?? '[]';
                    $distributors = json_decode($configData, true) ?: [];
                    if (!empty($distributors)) {
                        $distributorId = (int)$distributors[0]; // 只取第一个
                    }
                }

                // 如果有分配人，则创建待办并通知
                if ($distributorId) {
                    $expected = $request['latest_expect_time'];
                    goodsMattersFrom::addCreateMatter(
                        $request['request_name'],
                        $request['goods_id'],
                        '配置图片负责人',
                        0,
                        4,
                        $distributorId,
                        $param['id'],
                        $expected
                    );
                    //处理之前的待办，且如果有其他的待办但qwuser_id不同，则将其他的修改为废除status = 3
                    $db->table('goods_matters')
                        ->where('where type = 4 and model_id=:reques_id and status <> 1 and create_type = 1 and qwuser_id =:qwuser_id',
                            ['reques_id'=>$request['id'],
                                'qwuser_id'=>userModel::$qwuser_id
                            ])
                        ->update([
                            'status'=>1,
                            'completion_time'=>time(),
                        ]);

                    // 2. 将其他用户的相同待办标记为废除
                    $db->table('goods_matters')
                        ->where('type = 4 and model_id = :request_id and status <> 1 and create_type = 1 and qwuser_id <> :qwuser_id',
                            [
                                'request_id' => $request['id'],
                                'qwuser_id' => userModel::$qwuser_id
                            ])
                        ->update([
                            'status' => 3,
                            'close_reason' =>'其他人已处理',
                            'close_time' =>date('Y-m-d H:i:s')
                        ]);
                    // 获取商品信息
                    $goods_info = $db->table('goods_new')
                        ->where('id = :id', ['id' => $request['goods_id']])
                        ->field('goods_name')
                        ->one();
                        
                    $designer_info = $db->table('qwuser')
                        ->where('id = :id', ['id' => $distributorId])
                        ->field('wid')
                        ->one();

                    if (!empty($designer_info['wid'])) {
                        $goods_name = $goods_info?$goods_info['goods_name']:'';
                        $msg = imgsRequestModel::getMessage(0,$request['request_name'],$goods_name);
                        messagesFrom::senMessage(
                            [$designer_info['wid']],
                            $msg,
                            userModel::$qwuser_id,
                            $param['id'],
                            5
                        );

                    }
                }
            } elseif ((int)$param['is_check'] === 2) {
                // 审核不通过
                $status    = 9;
                $logType   = 10;
                $logRemark = $remarks;
                // 获取商品信息
                $goods_info = $db->table('goods_new')
                    ->where('id = :id', ['id' => $request['goods_id']])
                    ->field('goods_name')
                    ->one();
                if (!empty($applicant['wid'])) {
                    $msg  = messagesFrom::getMsgTxtForImgRequest(
                       15,
                        ($goods_info['goods_name']??''),
                        $request['request_name']
                    );
                    messagesFrom::senMessage([
                        $applicant['wid']
                    ],
                        $msg,
                        userModel::$qwuser_id,
                        $param['id'],
                        5);
                }

                // 2. 废除所有相关的待办事项
                $db->table('goods_matters')
                    ->where('type = 4 and model_id = :request_id and status <> 1 and create_type = 1 ',
                        [
                            'request_id' => $request['id']
                        ])
                    ->update([
                        'status' => 3,
                        'close_reason' =>'审核不通过，待办已废除',
                        'close_time' =>date('Y-m-d H:i:s')
                    ]);
            }

            // 更新状态
            $db->table('imgs_request')
                ->where('id = :id', ['id' => $param['id']])
                ->update(['status' => $status]);

            // 写入日志
            imgsRequestModel::setLog(
                $db,
                $param['id'],
                $logType,
                $logRemark,
                $param['is_check'] == 1 ? '审核通过' : '审核不通过'
            );

            $db->commit();
            returnSuccess('', '审核成功');

        } catch (\Exception $e) {
            $db->rollBack();
            // 可记录日志：$e->getMessage()
            returnError('审核失败，请稍后重试');
        }
    }
    //相关人员记录
    public static function User_log($id,$type,$qwuser_id = null) {
        $db = dbMysql::getInstance();
        if (empty($qwuser_id)){
            $qwuser_id = userModel::$qwuser_id;
        }

        //所有情况下，先检查是否存在匹配的记录
        $existingRecord = $db->table('imgs_request_participant')
            ->where('request_id = :id AND type = :type', [
                'id' => $id,
                'type' => $type
            ])
            ->one();

        // 只有在存在匹配记录时才进行清除
        if ($existingRecord) {
            $db->table('imgs_request_participant')
                ->where('request_id = :id AND type = :type', [
                    'id' => $id,
                    'type' => $type
                ])
                ->delete();
        }
        
        // 插入新的参与记录
        $db->table('imgs_request_participant')
            ->insertIgnore([
                'request_id' => $id,
                'user_id' => $qwuser_id,
                'type' => $type,
            ]);
    }
    // 删除
    // 1、app产品图不可删除
    // 2、需求方提出需求申请后，在图片管理员还没有配置人员前，可以删除，若已配置人员，则需求方不可删除
    // 3、图片管理员可以删除已配置的需求，删除后当前美工的待办作废
    // 4、超管直接删除
    public static function delete(array $param)
    {
        $id = !empty($param['id']) ? (int)$param['id'] : 0;
        if (!$id) {
            error_log("缺少删除的记录 ID");
            returnError('缺少删除的记录 ID');
            return;
        }

        $db = dbMysql::getInstance();

        try {
            // 获取需求信息
            $info = $db->table('imgs_request')
                ->where('id = :id', ['id' => $id])
                ->one();

            if (!$info) {
                error_log("记录不存在或已删除，ID: " . $id);
                returnError('记录不存在或已删除');
                return;
            }

            // 获取当前需求对应的图片管理员配置
            $distributor = $db->table('imgs_request_distributor')
                ->where('country_code = :country_code AND type = :type AND group_name = :group_name', [
                    'country_code' => $info['country_code'],
                    'type' => $info['type'],
                    'group_name' => $info['group_name']
                ])
                ->one();

            // 权限判断
            if (!userModel::isSuper()) {
                // 普通用户只能在未配置且非 app 平台时删除
                if ($info['platform_id'] == 1) {
                    error_log("app产品图不可删除，ID: " . $id);
                    returnError('app产品图不可删除');
                    return;
                }

                // 检查当前用户是否为图片管理员
                $isDistributor = $distributor && $distributor['distributor_id'] == userModel::$qwuser_id;

                // 如果不是图片管理员，则检查是否为需求创建者且未分配美工
                if (!$isDistributor) {
                    if ($info['user_id'] != userModel::$qwuser_id) {
                        error_log("您没有权限删除此需求，ID: " . $id);
                        returnError('您没有权限删除此需求');
                        return;
                    }
                    if (!empty($info['qwuser_id'])) {
                        error_log("已配置人员，需求方不可删除，ID: " . $id);
                        returnError('已配置人员，需求方不可删除');
                        return;
                    }
                }
            }

            // 开始事务
            $db->beginTransaction();

            try {
                // 标记删除
                $db->table('imgs_request')
                    ->where('id = :id', ['id' => $id])
                    ->update([
                        'is_delete' => 1,
                        'update_time' => time(),
                    ]);

                // 操作日志
                imgsRequestModel::setLog($db,$id,11,'');
//                self::User_log($id, 8); // 8 = 删除操作

                // 图片管理员或超管删除时，作废当前美工的待办
                if (userModel::isSuper() || $isDistributor) {
                    $db->table('goods_matters')
                        ->where('type = 4 and model_id = :reques_id and status <> 1 and create_type = 0', ['reques_id' => $id])
                        ->update(['status' => 3]);
                }

                // 提交事务
                $db->commit();
                returnSuccess('', '删除成功');
            } catch (\Exception $e) {
                // 回滚事务
                $db->rollBack();
                error_log("删除失败，ID: " . $id . ", 错误: " . $e->getMessage());
                returnError('删除失败：' . $e->getMessage());
            }
        } catch (\Exception $e) {
            error_log("获取需求信息失败，ID: " . $id . ", 错误: " . $e->getMessage());
            returnError('获取需求信息失败：' . $e->getMessage());
        }
    }
    //详情获取
    public static function getDetail($param)
    {
        $db = dbMysql::getInstance();

        try {
            // 主表查询，添加更多关联
            $detail = $db->table('imgs_request', 'a')
                ->leftJoin('qwuser', 'q', 'q.id = a.user_id')
                ->leftJoin('qwuser', 'q1', 'q1.id = a.qwuser_id')
                ->leftJoin('qwuser', 'q2', 'q2.id = a.allocation_user_id')
                ->leftJoin('goods_new', 'g', 'g.id = a.goods_id')
                ->leftJoin('imgs_request_checker', 'c', 'c.id = a.request_checke_id')
                ->leftJoin('imgs_request_distributor', 'd', 'd.id = a.request_distributor_id')
                ->leftJoin('qwdepartment', 'w', 'w.id = a.wp_id')
                ->leftJoin('qwdepartment', 'qw', 'qw.wp_id = a.wp_id')
                ->where('a.id = :id', ['id' => $param['id']])
                ->field('a.*, q.wname as user_name, q1.wname as agent_name, q2.wname as allocation_name, g.goods_name, g.cat_id, c.check_id, d.distributor_id, w.name as wp_group_name,d.group_name,qw.name as wp_name')
                ->one();
            if (!$detail) {
                returnError('未找到该需求');
            }
            // 处理类目信息
            if (!empty($detail['cat_id'])) {
                $catIdArr = json_decode($detail['cat_id'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($catIdArr)) {
                    // 获取所有类目信息
                    $cateList = $db->table('goods_cate')
                                  ->field('id, cate_name')
                                  ->whereIn('id', $catIdArr)
                                  ->where('is_delete = 0')
                                  ->list();
                    
                    // 构建类目映射
                    $cateMap = [];
                    foreach ($cateList as $cate) {
                        $cateMap[$cate['id']] = $cate['cate_name'];
                    }
                    
                    // 获取所有类目名称
                    $categoryNames = [];
                    foreach ($catIdArr as $catId) {
                        if (isset($cateMap[$catId])) {
                            $categoryNames[] = $cateMap[$catId];
                        }
                    }
                    
                    $detail['category_names'] = $categoryNames;
                }
            }
            // 处理审核人信息
            $checker_participants = $db->table('imgs_request_participant')
                ->where('request_id = :request_id AND type = 2', ['request_id' => $param['id']])
                ->field('user_id')
                ->list();
            
            $checker_ids = array_column($checker_participants, 'user_id');
            if (empty($checker_ids) && (int)$detail['request_checke_id'] === 0) {
                // 从config表获取默认审核人
                $result = $db->table('config')->where('key_name = "imgs_request_sop_checker"')->one();
                if (!empty($result['data'])) {
                    $checker_ids = json_decode($result['data'], true);
                }
            }
            
            if (!empty($checker_ids)) {
                $checker_list = $db->table('qwuser')
                    ->whereIn('id', $checker_ids)
                    ->field('id,wname')
                    ->list();
                $checker_list = array_column($checker_list, 'wname', 'id');
                $detail['check_id'] = $checker_ids;
                $detail['check_names'] = array_map(function($id) use ($checker_list) {
                    return $checker_list[$id] ?? '';
                }, $checker_ids);
            }
            // 处理颜色信息（color_info 转化）
            if (!empty($detail['color_info'])) {
                $colorIds = json_decode($detail['color_info'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($colorIds)) {
                    $colorList = $db->table('goods_color')
                        ->whereIn('id', $colorIds)
                        ->field('id, color_name')
                        ->list();
                    $colorMap = [];
                    foreach ($colorList as $col) {
                        $colorMap[$col['id']] = $col['color_name'];
                    }
                    $detail['color_name'] = [];
                    foreach ($colorIds as $cid) {
                        if (isset($colorMap[$cid])) {
                            $detail['color_name'][] = $colorMap[$cid];
                        }
                    }
                }
            }
            // 处理分配人信息
            $distributor_participants = $db->table('imgs_request_participant')
                ->where('request_id = :request_id AND type = 3', ['request_id' => $param['id']])
                ->field('user_id')
                ->list();
            
            $distributor_ids = array_column($distributor_participants, 'user_id');
            if (empty($distributor_ids) && (int)$detail['request_distributor_id'] === 0) {
                // 从config表获取默认分配人
                $result = $db->table('config')->where('key_name = "imgs_request_distributor"')->one();
                if (!empty($result['data'])) {
                    $distributor_ids = json_decode($result['data'], true);
                }
            }
            
            if (!empty($distributor_ids)) {
                $distributor_list = $db->table('qwuser')
                    ->whereIn('id', $distributor_ids)
                    ->field('id,wname')
                    ->list();
                $distributor_list = array_column($distributor_list, 'wname', 'id');
                $detail['distributor_names'] = array_map(function($id) use ($distributor_list) {
                    return $distributor_list[$id] ?? '';
                }, $distributor_ids);
            }

            //品牌
            if (!empty($detail['brand_id'])){
                $brand_name = $db->table('brands')
                    ->where('id = :id', ['id' => $detail['brand_id']])
                    ->field('name')
                    ->one();
                $detail['brand_name'] = $brand_name['name'];
            }else{
                $detail['brand_name'] = '';
            }
            // 格式化时间字段
            $timeFields = [
                'submit_time', 'expected_time', 'begin_time', 'real_expected_time',
                'assigned_time', 'task_reviewed_time', 'update_time', 'completion_time',
                'actual_delivery_date', 'estimated_arrival_date',
                'latest_expect_time', 'first_batch_date'
            ];

            foreach ($timeFields as $field) {
                if (isset($detail[$field]) && $detail[$field] !== '') {
                    $val = $detail[$field];

                    if (is_numeric($val)) {
                        // 秒级时间戳
                        $detail[$field] = date('Y-m-d H:i:s', (int)$val);
                    } else {
                        // 字符串格式的日期，尝试转换
                        $ts = strtotime($val);
                        $detail[$field] = $ts
                            ? date('Y-m-d H:i:s', $ts)
                            : $val;  // 转换失败就保留原字符串
                    }
                }
            }

            // 判断是否超时
            $now = time();
            if (!empty($detail['latest_expect_time'])) {
                $detail['is_timeout'] = $detail['latest_expect_time'] < $now ? 1 : 0;
            } else {
                $detail['is_timeout'] = 0;
            }

            // 获取操作日志
            $operationLogs = $db->table('imgs_request_log', 'l')
                ->leftJoin('qwuser', 'q', 'q.id = l.user_id')
                ->where('l.request_id = :request_id', ['request_id' => $param['id']])
                ->field('l.*, q.wname as user_name')
                ->order('l.id DESC')
                ->list() ?: [];

            // 获取测试信息
            $testInfo = $db->table('imgs_request_test', 't')
                ->leftJoin('qwuser', 'q', 'q.id = t.user_id')
                ->where('t.request_id = :request_id', ['request_id' => $param['id']])
                ->field('t.*, q.wname as tester_name')
                ->one() ?: [];

            // 获取测试方案
            $testSchemes = [];
            if ($testInfo) {
                $testSchemes = $db->table('imgs_request_test_scheme')
                    ->where('test_id = :test_id and is_delete = 0', ['test_id' => $testInfo['id']])
                    ->field('id, imgs, push_id, remark,show_num,open_num,link_click_num,play_num,is_syn')
                    ->list() ?: [];
                // 处理图片JSON字段
                foreach ($testSchemes as &$scheme) {
                    if (!empty($scheme['imgs'])) {
                        $scheme['imgs'] = json_decode($scheme['imgs'], true) ?: [];
                    }
                }
            }
            // 获取图片集合
            $images = $db->table('imgs_request_collection')
                ->where('request_id = :request_id AND is_delete = 0', ['request_id' => $param['id']])
                ->list() ?: [];
            //相关人员获取
            $participant = $db->table('imgs_request_participant','a')
                ->leftJoin('qwuser','b','b.id = a.user_id')
                ->where('a.request_id = :request_id',['request_id'=> $param['id']])
                ->field('a.request_id,a.user_id,a.type,b.wname as user_name')
                ->list();
            $participant_ = [];
            foreach ($participant as $v) {
                $participant_[$v['type']]['type'] = $v['type'];
                $participant_[$v['type']]['list'][] = $v;
            }
            if (!isset($participant_[11])) {
                $participant_[11] = [
                    'type'=>'11',
                    'list'=>[],
                ];
            }

            // 组装返回数据
            $result = [
                'participant'=>array_values($participant_),
                'detail' => $detail,
                'images' => $images,
                'operation_logs' => $operationLogs,
                'test_info' => $testInfo,
                'test_schemes' => $testSchemes,
            ];

            returnSuccess($result);
        } catch (\Exception $e) {
            error_log("Error in getDetail: " . $e->getMessage());
            returnError('获取详情失败：' . $e->getMessage());
        }
    }
    //修改相关人员
    public static function updateParticipant($param)
    {
        $db = dbMysql::getInstance();
        $request_id = (int)$param['request_id'];
        $type = (int)$param['type'];
        //非其他人员只能单一修改
        if ($type == 11) {
            $user_ids = json_decode($param['user_ids']);
            $db->beginTransaction();
            try {
                $db->table('imgs_request_participant')
                    ->where('type = 11 and request_id = :request_id',['request_id'=>$request_id])
                    ->delete();
                if (count($user_ids)) {
                    $keys = ['request_id','user_id','type'];
                    $insert_data = [];
                    foreach ($user_ids as $user_id) {
                        $insert_data[] = [$request_id,$user_id,11];
                    }
                    $db->table('imgs_request_participant')
                        ->insertBatch($keys,$insert_data);
                }
                $db->commit();
            } catch (ExceptionError $error) {
                $db->rollBack();
                returnError($error->getMessage());
            }
        } else {
            if (!in_array($type,[1,2,3,4,10])) {
                returnError('类型错误');
            }
            $old_user_id = (int)$param['old_user_id'];
            $user_id = (int)$param['user_id'];
            if ($old_user_id  != $user_id) {
                //1创建者，2sop审核者，3配置美工者，4美工，5开发，6运营,10app图审核者,11其他人员
                $user_list = $db->table('qwuser')
                    ->whereIn('id',[$old_user_id,$user_id])
                    ->field('id,wname,wid')
                    ->list();
                $user_ = [];
                foreach ($user_list as $v) {
                    $user_[$v['id']] = $v;
                }
                $old_user = $user_[$old_user_id];
                $new_user =  $user_[$user_id];
                $remarks = "【{$old_user['wname']}】改为【{$new_user['wname']}】";
                $imgs_request = $db->table('imgs_request')
                    ->where('id = :id',['id'=>$request_id])
                    ->one();
                $goods = '';
                if ($imgs_request['goods_id']) {
                    $goods = $db->table('goods_new')
                        ->where('id = :id',['id'=>$imgs_request['goods_id']])
                        ->one();
                }
                if ($imgs_request['status'] == 3) {
                    returnError('流程已完成，不可更改');
                }
                $db->beginTransaction();
                try {
                    //修改相关人员
                    $db->table('imgs_request_participant')
                        ->where('type = :type and request_id = :request_id and user_id = :user_id1',[
                            'type'=>$type,
                            'user_id1'=>$old_user_id,
                            'request_id'=>$request_id
                        ])->update([
                            'user_id'=>$user_id
                        ]);
                    //其他数据修改
                    if ($type == 1) {
                        $db->table('imgs_request')
                            ->where('id = :id',['id'=>$request_id])
                            ->update(['user_id'=>$user_id]);
                        $content = '申请人';
                    }
                    elseif ($type == 2) {
                        $node_name = '图片需求SOP审核';
                        //待办完成否完成
                        //待审核状态才去修改待办
                        if ($imgs_request['status'] == 4) {
                            //关闭上一个人的待办
                            $db->table('goods_matters')
                                ->where('type = 4 and status = 0 and create_type = 1 and node_name = :node_name and model_id = :qwuser_id',[
                                    'node_name'=>$node_name,
                                    'qwuser_id'=>$request_id
                                ])
                                ->update([
                                    'status'=>3,
                                    'close_reason'=>'sop审核人被更换',
                                    'close_time'=>date('Y-m-d H:i:s')
                                ]);
                            //生成下一个人的待办
                            goodsMattersFrom::addCreateMatter(
                                $imgs_request['request_name'],
                                $imgs_request['goods_id'],
                                $node_name,
                                1,
                                4,
                                $user_id,
                                $request_id,
                                0
                            );
                            //消息发送
                            if ($goods) {
                                $message = "您有一个新的图片需求SOP需要审核\n需求名称：{$imgs_request['request_name']}\n商品：{$goods['goods_name']}";
                            } else {
                                $message = "您有一个新的图片需求SOP需要审核\n需求名称：{$imgs_request['request_name']}";
                            }
                            messagesFrom::senMessage([$new_user['wid']],$message,userModel::$qwuser_id, $request_id, 5);
                        }
                        $content = 'sop审核人';
                    }
                    elseif ($type == 3) {
                        $node_name = '配置图片负责人';

                        //修改主表分配人
                        $db->table('imgs_request')
                            ->where('id = :id',['id'=>$request_id])
                            ->update(['allocation_user_id'=>$user_id]);
                        //待审核状态才去修改待办
                        if ($imgs_request['status'] == 0) {
                            //关闭上一个人的待办
                            $db->table('goods_matters')
                                ->where('type = 4 and status = 0 and create_type = 0 and node_name = :node_name and model_id = :qwuser_id',[
                                    'node_name'=>$node_name,
                                    'qwuser_id'=>$request_id
                                ])
                                ->update([
                                    'status'=>3,
                                    'close_reason'=>'配置图片负责人被更改',
                                    'close_time'=>date('Y-m-d H:i:s')
                                ]);
                            //生成下一个人的待办
                            goodsMattersFrom::addCreateMatter(
                                $imgs_request['request_name'],
                                $imgs_request['goods_id'],
                                $node_name,
                                0,
                                4,
                                $user_id,
                                $request_id,
                                0
                            );
                            //消息发送
                            $message = messagesFrom::getMsgTxtForImgRequest(
                                17,
                                ($goods['goods_name']??''),
                                $imgs_request['request_name']
                            );
                            messagesFrom::senMessage([$new_user['wid']],$message,userModel::$qwuser_id, $request_id, 17);
                        }
                        $content = '配置美工人';
                    }
                    elseif ($type == 4) {
                        $node_name = '图片制作';
                        //待上传
                        if ($imgs_request['status'] == 1) {
                            if (!empty($imgs_request['qwuser_id'])) {
                                // 一次性查询：操作人、商品、旧美工
                                $operator = $db->table('qwuser')
                                    ->where('id = :id', ['id' => userModel::$qwuser_id])
                                    ->one();
                                $goods    = $db->table('goods_new')
                                    ->where('id = :id', ['id' => $imgs_request['goods_id']])
                                    ->one();
                                $oldUser  = $db->table('qwuser')
                                    ->where('id = :id', ['id' => $imgs_request['qwuser_id']])
                                    ->one();

                                // 给旧美工发消息
                                if (!empty($oldUser['wid'])) {
                                    $msg = sprintf(
                                        "%s 已将 %s%s的作图任务配置给其他人",
                                        $operator['wname'] ?? '系统',
                                        $goods['goods_name'] ?? '',
                                        $imgs_request['request_name'] ?? ''
                                    );
                                    messagesFrom::senMeg(
                                        [$oldUser['wid']],
                                        $msg,
                                        [
                                            'user_id'  => userModel::$qwuser_id,
                                            'model_id' => $imgs_request['id'],
                                            'msg_type' => 1
                                        ]
                                    );
                                }
                            }

                            // 给新美工发消息
                            if (!empty($new_user['wid'])) {
                                messagesFrom::senMeg(
                                    [$new_user['wid']],
                                    '您有新的作图需求，请您及时接收，开启任务',
                                    [
                                        'user_id'  => userModel::$qwuser_id,
                                        'model_id' => $imgs_request['id'],
                                        'msg_type' => 1
                                    ]
                                );
                            }
                            //关闭上一个人的待办
                            $db->table('goods_matters')
                                ->where('type = 4 and status = 0 and create_type = 0 and node_name = :node_name and model_id = :qwuser_id',[
                                    'node_name'=>$node_name,
                                    'qwuser_id'=>$request_id
                                ])
                                ->update([
                                    'status'=>3,
                                    'close_reason'=>'美工更换',
                                    'close_time'=>date('Y-m-d H:i:s')
                                ]);
                            //生成下一个人的待办
                            goodsMattersFrom::addCreateMatter(
                                $imgs_request['request_name'],
                                $imgs_request['goods_id'],
                                $node_name,
                                0,
                                4,
                                $user_id,
                                $request_id,
                                $imgs_request['real_expected_time']
                            );
                            //消息发送
                            $msg = messagesFrom::getMsgTxtForImgRequest(2,$goods['goods_name']??'',$imgs_request['request_name']);
                            $other_data = [
                                'user_id'=>userModel::$qwuser_id,
                                'model_id'=>$imgs_request['id'],
                                'msg_type'=>7
                            ];
                            messagesFrom::senMeg([$new_user['wid']],$msg,$other_data);
                        }
                        $content = '美工';
                    }
                    elseif ($type == 10) {
                        //待审核
                        $node_name = '审核美工图';
                        if ($imgs_request['status'] == 2) {
                            //关闭上一个人的待办
                            $db->table('goods_matters')
                                ->where('type = 4 and status = 0 and create_type = 1 and node_name = :node_name and model_id = :qwuser_id',[
                                    'node_name'=>$node_name,
                                    'qwuser_id'=>$request_id
                                ])
                                ->update([
                                    'status'=>3,
                                    'close_reason'=>'美工更换',
                                    'close_time'=>date('Y-m-d H:i:s')
                                ]);
                            //生成下一个人的待办
                            goodsMattersFrom::addCreateMatter(
                                $imgs_request['request_name'],
                                $imgs_request['goods_id'],
                                $node_name,
                                0,
                                4,
                                $user_id,
                                $request_id,
                                0
                            );
                            //消息发送
                            if ($goods) {
                                $message = "请您及时为【{$goods['goods_name']}】【{$imgs_request['request_name']}】需求配置作图美工";
                            } else {
                                $message = "请您及时为【需求名称】需求配置作图美工";
                            }
                            messagesFrom::senMessage([$new_user['wid']],$message,userModel::$qwuser_id, $request_id, 17);
                        }
                        $content = 'app图审核';
                    }
                    //日志
                    imgsRequestModel::setLog($db,$request_id,13,$remarks,$content);
                    $db->commit();
                } catch (ExceptionError $error) {
                    $db->rollBack();
                    returnError($error->getMessage());
                }
            }
        }
        returnSuccess('修改成功');
    }
    /**
     * 私有：处理美工变更时的消息通知和待办关闭/生成
     *
     * @param dbMysql $db
     * @param array   $request            原始请求记录
     * @param int     $newDesignerId      新分配的美工 ID
     * @param string  $realExpectedTime   提交的预计交稿时间（字符串）
     * @param string  $alterNote          变更说明
     */
    private static function handleDesignerChange($db, $request, $newDesignerId, $realExpectedTime, $alterNote)
    {
        // 1. 必填校验
        if (empty($realExpectedTime)) {
            error_log("作图美工变更时，预计交稿时间必填，ID: {$request['id']}");
            SetReturn(-1, '作图美工变更时，预计交稿时间必填');
            throw new \Exception('Missing real_expected_time');
        }
        if (empty($alterNote)) {
            error_log("作图美工变更时，变更说明必填，ID: {$request['id']}");
            SetReturn(-1, '作图美工变更时，变更说明必填');
            throw new \Exception('Missing alter_note');
        }

        // 2. 关闭原美工待办并发送通知
        $oldDesignerId = $request['qwuser_id'];
        if ($oldDesignerId) {
            // 取商品名和请求名
            $goods = $db->table('goods_new')->where('id = :id', ['id' => $request['goods_id']])->one();
            $operator = $db->table('qwuser')->where('id = :id', ['id' => userModel::$qwuser_id])->one();
            $msg = sprintf(
                "%s 已将 %s%s的作图任务配置给其他人",
                $operator['wname'] ?? '系统',
                $goods['goods_name'] ?? '',
                $request['request_name'] ?? ''
            );

            // 站内信
            messagesFrom::senMeg(
                [ $db->table('qwuser')->where('id = :id', ['id' => $oldDesignerId])->one()['wid'] ],
                $msg,
                [
                    'user_id'  => userModel::$qwuser_id,
                    'model_id' => $request['id'],
                    'msg_type' => 1,
                ]
            );

            // 关闭待办
            $db->table('goods_matters')
                ->where('model_id = :mid AND qwuser_id = :uid AND status = 0 AND type = 4 AND create_type = 0', [
                    'mid' => $request['id'], 'uid' => $oldDesignerId
                ])
                ->update([
                    'status'       => 3,
                    'close_reason' => '美工变更，任务已重新分配',
                    'close_time'   => date('Y-m-d H:i:s'),
                ]);
        }

        // 3. 给新美工生成待办并通知
        $expTs = strtotime($realExpectedTime);
        goodsMattersFrom::addCreateMatter(
            $request['request_name'],
            $request['goods_id'],
            '图片任务待接收',
            0,
            4,
            $newDesignerId,
            $request['id'],
            $expTs
        );

        messagesFrom::senMeg(
            [ $db->table('qwuser')->where('id = :id', ['id' => $newDesignerId])->one()['wid'] ],
            '您有新的作图需求，请您及时接收，开启任务',
            [
                'user_id'  => userModel::$qwuser_id,
                'model_id' => $request['id'],
                'msg_type' => 5,
            ]
        );
    }
    // 封装时间字段处理逻辑
    private static function formatTimestamps(array $data, array $fields): array {
        foreach ($fields as $field) {
            if (isset($data[$field]) && !empty($data[$field])) {
                $timestamp = is_numeric($data[$field]) ? (int)$data[$field] : strtotime($data[$field]);
                $data[$field] = $timestamp !== false ? date('Y-m-d H:i:s', $timestamp) : null;
            }
        }
        return $data;
    }
    // 更新信息
    public static function update($param) {
        $db = dbMysql::getInstance();
        // 开始事务
        $db->beginTransaction();
        try {
            // 获取当前需求信息
            $request = $db->table('imgs_request')
                ->where('id = :id', ['id' => $param['id']])
                ->one();

            if (!$request) {
                error_log("需求不存在，ID: " . $param['id']);
                SetReturn(-1, '需求不存在');
                return;
            }

            // 检查状态限制
            if ($request['status'] == 7) { // 已完成状态
                // 检查是否尝试修改受限字段
                if (isset($param['user_id']) || isset($param['type']) || isset($param['qwuser_id'])) {
                    error_log("已完成状态不可修改申请人、需求性质、作图美工，ID: " . $param['id']);
                    SetReturn(-1, '已完成状态不可修改申请人、需求性质、作图美工');
                    return;
                }
            }

            // 检查作图美工变更
            if (isset($param['qwuser_id']) && $param['qwuser_id'] != $request['qwuser_id']) {
                // 检查必填字段
                if (empty($param['real_expected_time'])) {
                    error_log("作图美工变更时，预计交稿时间必填，ID: " . $param['id']);
                    SetReturn(-1, '作图美工变更时，预计交稿时间必填');
                    return;
                }
                if (empty($param['alter_note'])) {
                    error_log("作图美工变更时，变更说明必填，ID: " . $param['id']);
                    SetReturn(-1, '作图美工变更时，变更说明必填');
                    return;
                }

                // 如果有原美工，发送消息通知并关闭待办
                if ($request['qwuser_id']) {
                    $goods_info = $db->table('goods_new')->where('id = :id', ['id' => $request['goods_id']])->one();
                    $operator = $db->table('qwuser')->where('id = :id', ['id' => userModel::$qwuser_id])->one();
                    $wid = $db->table('qwuser')->where('id = :id', ['id' => $request['qwuser_id']])->one();

                    $message = sprintf(
                        "%s已将%s%s作图任务配置给其他人",
                        $operator['wname'] ?? '系统',
                        $goods_info['goods_name'] ?? '',
                        $request['request_name'] ?? ''
                    );

                    // 使用senMeg方法发送消息
                    $other_data = [
                        'user_id' => userModel::$qwuser_id,
                        'model_id' => $request['id'],
                        'msg_type' => 1
                    ];
                    messagesFrom::senMeg([$wid['wid']], $message, $other_data);

                    // 关闭原美工的待办事项
                    $db->table('goods_matters')
                        ->where('model_id = :model_id AND qwuser_id = :qwuser_id AND status = 0 AND type = 4 AND create_type = 0', [
                            'model_id' => $request['id'],
                            'qwuser_id' => $request['qwuser_id']
                        ])
                        ->update([
                            'status' => 3,
                            'close_reason' => '美工变更，任务已重新分配',
                            'close_time' => date('Y-m-d H:i:s')
                        ]);
                }
                //给新美工生成待办事项
                goodsMattersFrom::addCreateMatter(
                    $request['request_name'],  // matter_name
                    $request['goods_id'],  // goods_id
                    '图片任务待接收',  // node_name
                    0,  // create_type
                    4,  // type
                    $param['qwuser_id'],  // agent_id
                    $request['id'],  // model_id
                    strtotime($param['real_expected_time'])  // expected_time
                );
                // 给新美工生成待办事项,您有新的作图需求，请您及时接收，开启任务
                $new_wid = $db->table('qwuser')->where('id = :id', ['id' => $param['qwuser_id']])->one();
                //还要给新美工发消息
                $new_other_data = [
                    'user_id' => userModel::$qwuser_id,
                    'model_id' => $request['id'],
                    'msg_type' => 5
                ];
                messagesFrom::senMeg([$new_wid['wid']], '您有新的作图需求，请您及时接收，开启任务', $new_other_data);
            }

            // 准备更新数据
            $data = [
                'user_id' => isset($param['user_id']) && $param['user_id'] !== '' ? (int)$param['user_id'] : null,
                'nature_demand' => $param['nature_demand'] ?? null,
                'qwuser_id' => isset($param['qwuser_id']) && $param['qwuser_id'] !== '' ? (int)$param['qwuser_id'] : null,
                'real_expected_time' => !empty($param['real_expected_time']) ? strtotime($param['real_expected_time']) : null,
                'alter_note' => $param['alter_note'] ?? null,
                'sample_status' => isset($param['sample_status']) && $param['sample_status'] !== '' ? (int)$param['sample_status'] : null,
                'model_status' => isset($param['model_status']) && $param['model_status'] !== '' ? (int)$param['model_status'] : null,
                'is_purchase' => isset($param['is_purchase']) && $param['is_purchase'] !== '' ? (int)$param['is_purchase'] : null,
                'is_file_established' => isset($param['is_file_established']) && $param['is_file_established'] !== '' ? (int)$param['is_file_established'] : null,
                'actual_delivery_date' => !empty($param['actual_delivery_date']) ? strtotime($param['actual_delivery_date']) : null,
                'estimated_arrival_date' => !empty($param['estimated_arrival_date']) ? strtotime($param['estimated_arrival_date']) : null,
                'first_batch_date' => !empty($param['first_batch_date']) ? strtotime($param['first_batch_date']) : null,
                'update_remark' => $param['update_remark'] ?? null,
                'update_time' => time(),
            ];
            if (!empty($param['qwuser_id'])){
                $data['assigned_time'] = strtotime(date('Y-m-d H:i:s'));
            }

            try {
                // 更新需求信息
                $db->table('imgs_request')
                    ->where('id = :id', ['id' => $param['id']])
                    ->update($data);
                // 记录操作日志
                imgsRequestModel::setLog($db,$param['id'],9,'');
                // 提交事务
                $db->commit();
                returnSuccess('', '更新成功');
            } catch (\Exception $e) {
                // 回滚事务
                $db->rollBack();
                error_log("更新需求失败，ID: " . $param['id'] . ", 错误: " . $e->getMessage());
                SetReturn(-1, '更新失败：' . $e->getMessage());
            }
        } catch (\Exception $e) {
            error_log("获取需求信息失败，ID: " . $param['id'] . ", 错误: " . $e->getMessage());
            SetReturn(-1, '获取需求信息失败：' . $e->getMessage());
        }
    }
    //图片测试保存
    public static function saveTestScheme($param) {
        $db = dbMysql::getInstance();
        $imgs_request = imgsRequestModel::getRequest($db, $param['request_id']);

        // 只有进行中状态才允许保存
        if ($imgs_request['status'] != 1) {
            returnError('图片测试信息只能在需求进行中才能保存');
        }

        // ======= 合并原有方案与新方案 =======
        // 获取原有方案列表（未标记删除）
        $existing_schemes = $db->table('imgs_request_test_scheme')
            ->where('request_id = :request_id AND is_delete = 0', ['request_id' => $imgs_request['id']])
            ->field('imgs, push_id, remark')
            ->list();

        // 以 push_id 作为键，构建原有方案映射
        $merged_schemes = [];
        foreach ($existing_schemes as $scheme) {
            $merged_schemes[$scheme['push_id']] = [
                'imgs'    => json_decode($scheme['imgs'], true),
                'push_id' => $scheme['push_id'],
                'remark'  => $scheme['remark'],
            ];
        }

        // 将传入的新方案与原有方案合并
        foreach ($param['test_scheme'] as $scheme) {
            $pushId = $scheme['push_id'];
            $newImgs = $scheme['imgs'];
            $newRemark = $scheme['remark'];

            if (isset($merged_schemes[$pushId])) {
                // 合并图片列表并去重
                $allImgs = array_merge($merged_schemes[$pushId]['imgs'], $newImgs);
                $merged_schemes[$pushId]['imgs'] = array_values(array_unique($allImgs));
                // 覆盖备注为最新
                $merged_schemes[$pushId]['remark'] = $newRemark;
            } else {
                // 新增方案
                $merged_schemes[$pushId] = [
                    'imgs'    => $newImgs,
                    'push_id' => $pushId,
                    'remark'  => $newRemark,
                ];
            }
        }

        // 重新整理合并后方案为索引数组
        $param['test_scheme'] = array_values($merged_schemes);
        // ======================================

        // 检查已有测试数据
        $test_data = $db->table('imgs_request_test')
            ->where('request_id = :request_id', ['request_id' => $imgs_request['id']])
            ->one();

        // 标记所有旧方案为删除，后续再更新/插入
        $db->table('imgs_request_test_scheme')
            ->where('request_id = :request_id', ['request_id' => $imgs_request['id']])
            ->update(['is_delete' => 1]);

        $db->beginTransaction();
        try {
            // 保存或更新测试主体数据
            if (!$test_data) {
                $test_id = $db->table('imgs_request_test')
                    ->insert([
                        'user_id'      => userModel::$qwuser_id,
                        'request_id'   => $imgs_request['id'],
                        'channel_id'   => (int)$param['channel_id'],
                        'begin_time'   => $param['begin_time'],
                        'end_time'     => $param['end_time'],
                        'created_time' => date('Y-m-d H:i:s'),
                        'conclusion'   => $param['conclusion'],
                    ]);
            } else {
                $db->table('imgs_request_test')
                    ->where('id = :id', ['id' => $test_data['id']])
                    ->update([
                        'user_id'      => userModel::$qwuser_id,
                        'channel_id'   => (int)$param['channel_id'],
                        'begin_time'   => $param['begin_time'],
                        'end_time'     => $param['end_time'],
                        'updated_time' => date('Y-m-d H:i:s'),
                        'conclusion'   => $param['conclusion'],
                    ]);
                $test_id = $test_data['id'];
            }

            // 保存合并后的测试方案
            foreach ($param['test_scheme'] as $scheme) {
                $db->table('imgs_request_test_scheme')
                    ->insert([
                        'request_id' => $imgs_request['id'],
                        'test_id'    => $test_id,
                        'imgs'       => json_encode($scheme['imgs'], JSON_UNESCAPED_UNICODE),
                        'push_id'    => $scheme['push_id'],
                        'remark'     => $scheme['remark'],
                    ]);
            }

            // 安排定时脚本任务
            $endTimestamp = strtotime($param['end_time']);
            customCrontabForm::saveTask($imgs_request['id'], 2, 1, $endTimestamp, 0);

            // 记录日志
            $action = empty($test_data) ? '新增图片测试' : '更新图片测试';
            imgsRequestModel::setLog($db, $imgs_request['id'], 8, '', $action);

            $db->commit();
        } catch (ExceptionError $error) {
            $db->rollBack();
            returnError($error->getMessage());
        }
    }
    //图片测试详情
    public static function testSchemeDetail($id) {
        $db = dbMysql::getInstance();
        $imgs_request = $db->table('imgs_request')
            ->where('id = :id',['id'=>$id])
            ->one();
        if (!$imgs_request) {
            returnError('此需求不存在');
        }
        //数据保存
        $test_data = $db->table('imgs_request_test')
            ->where('request_id = :request_id',['request_id'=>$imgs_request['id']])
            ->one();
        if ($test_data) {
            $test_schemes = $db->table('imgs_request_test_scheme')
                ->where('request_id = :request_id',['request_id'=>$imgs_request['id']])
                ->field('id,imgs,push_id,remark')
                ->list();
            foreach ($test_schemes as $k=>$v) {
                $test_schemes[$k]['imgs'] = json_decode($v['imgs'],true);
            }
            $test_data['test_schemes'] = $test_schemes;
        }
        return $test_data;
    }
//    //上传文件
//    public static function uploadFile($param) {
//        $db = dbMysql::getInstance();
//        $request_id = (int)$param['id'];
//
//        // 1. 验证需求是否存在
//        $imgs_request = imgsRequestModel::getRequest($db, $request_id);
//        if (!$imgs_request) {
//            returnError('未找到该需求');
//        }
//
//        // 2. 检查需求状态
//        if ($imgs_request['status'] != 1) {
//            returnError('当前需求状态不允许上传文件');
//        }
//
//        // 3. 权限验证
//        if ($imgs_request['qwuser_id'] != userModel::$qwuser_id) {
//            returnError('您没有权限上传文件');
//        }
//
//        $db->beginTransaction();
//        // 插入数据库
//        $insert_data = [
//
//        ];
//        $db->table('imgs_request_collection')->where('id =:id',['id' => $param['id']])->updata($insert_data);
//
//            // 5. 记录日志
//            imgsRequestModel::setLog($db, $request_id, 4, '上传文件');
//
//            $db->commit();
//            returnSuccess('', '上传成功');
//        } catch (ExceptionError $e) {
//            $db->rollBack();
//            returnError('上传失败：' . $e->getMessage());
//        }
//    }

    //下载指定文件
    public static function downLoadFile(array $param)
    {
        $db = dbMysql::getInstance();

        // 获取 ids 参数，支持 JSON 字符串或数组
        $ids = $param['ids'] ?? null;
        if (is_string($ids)) {
            // 尝试解析 JSON
            $decoded = json_decode($ids, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                $ids = $decoded;
            } else {
                // 如果不是合法 JSON，则按逗号分隔字符串处理
                $ids = array_filter(explode(',', trim($ids, '[]')), fn($v) => $v !== '');
            }
        }

        // 确保 ids 是数组
        if (!is_array($ids) || empty($ids)) {
            SetReturn(-1, '请选择要下载的文件');
            return;
        }

        // 清洗并转换为整数ID数组，去重
        $ids = array_unique(array_map('intval', $ids));
        if (empty($ids)) {
            SetReturn(-1, '无有效的文件ID');
            return;
        }

        // 查询数据库
        $img_list = $db->table('imgs_request_collection')
            ->field('id, url, goods_id')
            ->whereIn('id', $ids)
            ->list();

        if (empty($img_list)) {
            SetReturn(-1, '未找到要下载的图片');
            return;
        }

        // 获取 Base64 编码数据
        $base64_data = [];
        foreach ($img_list as $img) {
            $fileContent = downLoadFrom::getdownLoadBase64Encode(
                $img['url'],
                $img['goods_id'],
                1
            );
            $base64_data[] = [
                'id'   => $img['id'],
                'data' => $fileContent,
            ];
        }

        returnSuccess(['data' => $base64_data]);
    }


}