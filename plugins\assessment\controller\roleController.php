<?php

/**
 * @author: zhangguoming
 * @Time: 2024/6/11 10:34
 */

namespace plugins\assessment\controller;

use core\lib\config;
use core\lib\db\dbAMysql;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;
use plugins\assessment\form\rolesForm;
use plugins\assessment\models\authListModel;
use plugins\assessment\models\authModel;
use Rap2hpoutre\FastExcel\FastExcel;

class roleController
{
    //获取权限列表
    public function getDefaultAuth()
    {
        $auth = authModel::getAuth();
//        $auth_list = authListModel::getAuth();
        returnSuccess(['auth' => $auth]);
    }

    //获取角色列表
    public function getRoleList()
    {
        $paras_list = array('role_name', 'page_size', 'page');
        $param = arrangeParam($_POST, $paras_list);
        $adb = dbAMysql::getInstance();
        $adb->table('roles')->where('where is_delete = 0');
        if (!empty($param['role_name'])) {
            $adb->andWhere('role_name like :role_name', ['role_name' => '%' . $param['role_name'] . '%']);
        }
        $data = $adb->pages($param['page'], $param['page_size']);
        if (count($data['list'])) {
            foreach ($data['list'] as &$v) {
                $v['auth'] = $v['auth'] == 'null' ? [] : json_decode($v['auth'], true);
                if (!empty($v['auth'])) {
                    $auth_map = $adb->table('auth') // 假设表名为 auth_table
                    ->whereIn('auth', $v['auth'])
                    ->list();
                    $auth_map = array_column($auth_map, null, 'auth');
                }
                $v['auth_name'] = [];
                foreach ($v['auth'] as $v1) {
                    // 在这里根据对应条件 $v1 去查找对应表中的 auth 字段然后替换为 auth_name 字段
                    $authRecord = $auth_map[$v1] ?? [];
                    if ($authRecord) {
                        $v['auth_name'][] = $authRecord['auth_name']; // 替换为 auth_name 字段
                    }
                    
                }
                $v['list_auth'] = $v['list_auth'] == 'null' ? [] : json_decode($v['list_auth'], true);
            }
        }
        returnSuccess($data);
    }

    //新增，修改角色
    public function addRole()
    {
        $paras_list = array('id', 'role_name', 'inherit_ids');
        $request_list = ['role_name' => '角色名称'];
        $length_data = ['role_name' => ['name' => '角色名称', 'length' => 20]];
        $param = arrangeParam($_POST, $paras_list, $request_list, $length_data);
        $adb = dbAMysql::getInstance();
        if (!empty($param['id'])) {
            //重复判断
            $role = $adb->table('roles')
                ->where('where role_name=:role_name and is_delete=0 and id <> :id', ['role_name' => $param['role_name'], 'id' => $param['id']])
                ->one();
            if ($role) {
                returnError('角色名称已存在');
            }
            //修改
            $role = $adb->table('roles')->where('where id=:id', ['id' => $param['id']])
                ->one();
            if ($role['type'] == 1) {
                returnError('系统角色不能修名称');
            }
            if (!$role) {
                returnError('该角色不存在');
            }
            $adb->table('roles')
                ->where('where id=:id', ['id' => $role['id']])
                ->update([
                    'role_name'  => $param['role_name'],
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            returnSuccess([], '修改成功');
        } else {
            //重复判断
            $role = $adb->table('roles')
                ->where('where role_name=:role_name and is_delete=0', ['role_name' => $param['role_name']])
                ->one();
            if ($role) {
                returnError('角色名称已存在');
            }
            $auth = [];
            if (!empty($param['inherit_ids']) && $param['inherit_ids'] != '[]') {
                $inherit_ids = json_decode($param['inherit_ids']);
                $role_list = $adb->table('roles')
                    ->whereIn('id', $inherit_ids)
                    ->list();
                $list_auth = [];
                if (count($role_list)) {
                    foreach ($role_list as $v) {
                        $auth = array_merge($auth, json_decode($v['auth']));
                        $list_auth_ = json_decode($v['list_auth'], true);
                        foreach ($list_auth_ as $v1) {
                            if (isset($list_auth[$v1['name']])) {
                                $list_auth[$v1['name']]['key'] = array_unique(array_merge($v1['key'], $list_auth[$v1['name']]['key']));
                            } else {
                                $list_auth[$v1['name']] = $v1;
                            }
                        }
                    }
                }
                $auth = array_values(array_unique($auth));
                $res_list_auth = [];
                foreach ($list_auth as $v) {
                    $v['key'] = array_values($v['key']);
                    $res_list_auth[] = $v;
                }
                $list_auth = json_encode($res_list_auth);
            } else {
                //默认权限
                $list_auth = '[{"name":"asin_table","key":["country","pic_url","asin","product_name","is_new","level_name","yunying"]},{"name":"pasin_table","key":["country","pic_url","p_asin","product_name","is_new","level_name","yunying"]},{"name":"sku_table","key":["pic_url","product_name","sku","category_name"]},{"name":"yunying_table","key":["project","yunying"]},{"name":"store","key":["country","store_name"]},{"name":"month_total","key":["asin","product_name"]},{"name":"hot","key":["country","pic_url","p_asin","product_name","is_new","yunying"]},{"name":"waring","key":["country","pic_url","asin","p_asin","product_name","level_name","sku","yunying"]},{"name":"qingcang","key":["country","pic_url","asin","product_name","is_new","yunying"]}]';
            }
            $adb->table('roles')
                ->insert([
                    'role_name'  => $param['role_name'],
                    'auth'       => json_encode($auth),
                    'list_auth'  => $list_auth,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            returnSuccess('', '新增成功');
        }
    }

    //删除角色
    public function delRole()
    {
        $id = (int)$_POST['id'];
        if (!$id) {
            returnError('角色ID必传');
        }
        $adb = dbAMysql::getInstance();
        $adb->beginTransaction();
        try {
            $role = $adb->table('roles')->where('where id=:id', ['id' => $id])
                ->one();
            if ($role['id'] == 1 || $role['id'] == 2) {
                returnError('管理员和运营不能删除');
            }
            //删除角色
            $adb->table('roles')->where('where id=:id', ['id' => $id])
                ->update(['is_delete' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
            //删除角色关系
            $adb->table('user_roles')->where('where role_id=:role_id', ['role_id' => $id])
                ->update(['is_delete' => 1]);
            $adb->commit();
            returnSuccess('', '删除成功');
        } catch (ExceptionError $error) {
            $adb->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }

    //编辑权限
    public function editAuth()
    {
        $paras_list = array('role_id', 'auth');
        $request_list = ['role_id' => '角色ID', 'auth' => '权限信息'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $role_id = (int)$param['role_id'];
        $auth = $param['auth'] ?? '[]';
        $adb = dbAMysql::getInstance();
        $adb->table('roles')
            ->where('where id=:id', ['id' => $role_id])
            ->update([
                'auth' => $auth, 'updated_at' => date('Y-m-d h:i:s')
            ]);
        returnSuccess('', '提交成功');
    }

    //编辑列表权限
    public function editListAuth()
    {
        $paras_list = array('role_id', 'list_auth');
        $request_list = ['role_id' => '角色ID', 'list_auth' => '权限信息'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $role_id = (int)$param['role_id'];
        $auth = $param['list_auth'] ?? '[]';
        $adb = dbAMysql::getInstance();
        $adb->table('roles')
            ->where('where id=:id', ['id' => $role_id])
            ->update([
                'list_auth' => $auth, 'updated_at' => date('Y-m-d h:i:s')
            ]);
        returnSuccess('', '提交成功');
    }

    //角色用户列表
    public function addRoleUserList()
    {
        $paras_list = array('role_id', 'user_name', 'page', 'page_size');
        $request_list = ['role_id' => '角色ID'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $role_id = (int)$param['role_id'];
        // 设置默认分页参数
        $page = !empty($param['page']) ? $param['page'] : 1;
        $page_size = !empty($param['page_size']) ? $param['page_size'] : 10;

        $adb = dbAMysql::getInstance();
        $adb->table('user_roles', 'a')
            ->leftJoin('roles', 'b', 'b.id=a.role_id')
            ->leftJoinOut('db', 'qwuser', 'c', 'c.id=a.user_id')
            ->where('where role_id=:role_id and a.is_delete = 0', ['role_id' => $role_id]);
        if (!empty($param['user_name'])) {
            $adb->andWhere('c.wname like :wname', ['wname' => "%" . $param['user_name'] . "%"]);
        }
        $list = $adb->field('a.id,a.status,b.role_name,c.wname,a.created_time')
            ->order('a.id desc')
            ->pages($page, $page_size);
        returnSuccess($list);
    }

    //角色用户列表
    public function addRolesUserList()
    {
        $paras_list = array('role_ids', 'user_name', 'page', 'page_size');
        $request_list = ['role_ids' => '角色ID'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $role_ids = json_decode(trim($param['role_ids']));
        // 设置默认分页参数
        $page = !empty($param['page']) ? $param['page'] : 1;
        $page_size = !empty($param['page_size']) ? $param['page_size'] : 10;

        $adb = dbAMysql::getInstance();
        $adb->table('user_roles', 'a')
            ->leftJoin('roles', 'b', 'b.id=a.role_id')
            ->leftJoinOut('db', 'qwuser', 'c', 'c.id=a.user_id')
            ->where('where a.is_delete = 0');
        $adb->whereIn('a.role_id', $role_ids);
        if (!empty($param['user_name'])) {
            $adb->andWhere('c.wname like :wname', ['wname' => "%" . $param['user_name'] . "%"]);
        }
        $list = $adb->field('a.id,a.status,b.role_name,c.wname,a.created_time,c.avatar')
            ->order('a.id desc')
            ->pages($page, $page_size);
        returnSuccess($list);
    }

    //添加角色用户
    public function addRoleUser()
    {
        $paras_list = array('role_id', 'user_list');
        $request_list = ['role_id' => '角色ID', 'user_list' => '用户id'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $role_id = (int)$param['role_id'];
        $user_list = json_decode($param['user_list']);
        //已有的角色用户
        $adb = dbAMysql::getInstance();
        $user_roles = $adb->table('user_roles')
            ->where('where role_id=:role_id and is_delete=0', ['role_id' => $role_id])
            ->list();
        $ids_has = array_column($user_roles, 'user_id');
        $add_ids = array_diff($user_list, $ids_has);
        $adb->table('user_roles');
        foreach ($add_ids as $user_id) {
            $adb->insert([
                'user_id'      => $user_id,
                'role_id'      => $role_id,
                'created_time' => date('Y-m-d H:i:s')
            ]);
        }
        returnSuccess('', '添加成功');
    }

    //禁用、启用
    public function banRoleUser()
    {
        $paras_list = array('rid', 'uid');
        $request_list = ['uid' => '用户id', 'rid' => '角色id'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
//        $id = (int)$param['id'];
        $rid = (int)$param['rid'];
        $uid = (int)$param['uid'];
        $adb = dbAMysql::getInstance();
        $user_roles = $adb->table('user_roles')
            ->where('where id = :id and role_id = :role_id', [
                'id'      => $uid,
                'role_id' => $rid
            ])
            ->one();
        if (!$user_roles) {
            returnError('未找到该数据');
        }
        $status = $user_roles['status'] == 1 ? 0 : 1;
        $adb->table('user_roles')
            ->where('where id = :id and role_id = :role_id', [
                'id'      => $uid,
                'role_id' => $rid
            ])
            ->update(['status' => $status, 'updated_time' => date('Y-m-d H:i:s')]);
        if ($status) {
            returnSuccess([], '已启用');
        } else {
            returnSuccess([], '已禁用');
        }
    }

    //删除用户
    public function delRoleUser()
    {
        $paras_list = array('id');
        $request_list = ['id' => '数据ID'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $id = (int)$param['id'];
        $adb = dbAMysql::getInstance();
        $role = $adb->table('roles')->where('where id=:id', ['id' => $id])
            ->one();
        if ($role['id'] == 1 || $role['id'] == 2) {
            returnError('管理员和运营不能删除');
        }
        $adb->table('user_roles')
            ->where('where id=:id', ['id' => $id])
            ->update(['is_delete' => 1]);
        returnSuccess('删除成功');
    }

    //用户导入
    public function importRoleUser()
    {
        $paras_list = array('excel_src');
        $request_list = ['excel_src' => '表格链接'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $excel_url = SELF_FK . $param['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格不存在');
        }

        $excel_data = (new FastExcel)->import($excel_url);
        $data = $excel_data->toArray();
        if (!count($data)) {
            returnError('表格内不存在数据');
        }

        //        判断表头
        $first_user = $data[0];
        if (empty($first_user['角色名称']) || empty($first_user['姓名']) || !isset($first_user['状态(1启用,0禁用)'])) {
            returnError('表头错误');
        }

        $user_name_list = array_column($data, '姓名');
        $db = dbMysql::getInstance();
        $user_list = $db->table('qwuser')
            ->whereIn('wname', $user_name_list)
            ->field('id, wname')
            ->list();

        $adb = dbAMysql::getInstance();
        $roles = $adb->table('roles')
            ->where('where is_delete = 0')
            ->list();
        $roles_map = array_column($roles, null, 'role_name');

        $role_users = $adb->table('user_roles')
            ->where('where is_delete = 0')
            ->list();
        $olds_roles_user_ids = [];
        foreach ($role_users as $v) {
            $olds_roles_user_ids[$v['role_id']][] = $v['user_id'];
        }

        $res_data = [];
        $error_data = [];
        $failed_res_data = []; // 新增变量存储失败的记录
        $success_count = 0;
        $error_count = 0;
        $row_num = 2; // 行数计数器，Excel表头为第一行，所以从2开始

        foreach ($data as $v) {
            $res_itme = ['name' => $v['姓名'], 'row' => $row_num]; // 添加行数信息
            $has_this_user = 0;
            $status = $v['状态(1启用,0禁用)'] == 1 ? 1 : 0;
            if (!array_key_exists($v['角色名称'], $roles_map))  {
                $res_itme['status'] = 0;
                $res_itme['msg'] = '未找到该角色';
                $error_count++;
                $error_data[] = [
                    '行号'     => $row_num,
                    '失败原因' => '未找到该角色'
                ];
                $failed_res_data[] = $res_itme; // 记录失败的项到新变量中
                continue;
            }
            $rid = $roles_map[$v['角色名称']]['id'];

            foreach ($user_list as $v1) {
                if ($v['姓名'] == $v1['wname']) {
                    $has_this_user = 1;
                    if (in_array($v1['id'], $olds_roles_user_ids[$rid])) {
                        $adb->table('user_roles')
                            ->where('where role_id=:role_id and user_id=:user_id and is_delete=0', ['role_id' => $rid, 'user_id' => $v1['id']])
                            ->update(['status' => $status, 'updated_time' => date('Y-m-d H:i:s')]);
                        $res_itme['status'] = 1;
                        $res_itme['msg'] = '修改成功';
                        $success_count++;
                    } else {
                        $adb->table('user_roles')
                            ->insert([
                                'user_id'      => $v1['id'],
                                'role_id'      => $rid,
                                'created_time' => date('Y-m-d H:i:s'),
                                'status'       => $status
                            ]);
                        $res_itme['status'] = 1;
                        $res_itme['msg'] = '新增成功';
                        $success_count++;
                    }
                    break;
                }
            }

            if (!$has_this_user) {
                $res_itme['status'] = 0;
                $res_itme['msg'] = '未找到该用户';
                $error_count++;
                $error_data[] = [
                    '行号'     => $row_num,
                    '失败原因' => '未找到该用户'
                ];
                $failed_res_data[] = $res_itme; // 记录失败的项到新变量中
            }

            $res_data[] = $res_itme;
            $row_num++; // 增加行数计数器
        }

        if ($error_count > 0) {
            // 使用项目根目录动态构建导出文件路径
            $exportPath = SELF_FK . '/public/assessment/temp/error/user_import_errors.xlsx';

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径
            $exportPathqd = '/public/assessment/temp/error/user_import_errors.xlsx';

//            $erroe_res_data = array_slice($res_data, 0, 10);
            returnSuccess(['error_file' => $exportPathqd, 'error_count' => $error_count, 'success_count' => $success_count, 'data' => $failed_res_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => $error_count, 'success_count' => $success_count, 'data' => $res_data], '导入成功');
        }
    }

    //用户导出
    public function exportRoleUser()
    {
        $paras_list = array('type', 'ids');
        $request_list = ['type' => '导出类型', 'ids' => '选中的数据ID'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $type = (int)$param['type'];
        $adb = dbAMysql::getInstance();
        if ($type == 1) {
            $ids = json_decode($param['ids']);
            if (!count($ids)) {
                returnError('请选择要导出的数据');
            }
            $data = $adb->table('user_roles', 'a')
                ->leftJoin('roles', 'b', 'b.id=a.role_id')
                ->leftJoinOut('db', 'qwuser', 'c', 'c.id=a.user_id')
                ->whereIn('a.id', $ids)
                ->field('c.wname,b.role_name,a.created_time,a.status')
                ->list();
        } else {
            $data = $adb->table('user_roles', 'a')
                ->leftJoin('roles', 'b', 'b.id=a.role_id')
                ->leftJoinOut('db', 'qwuser', 'c', 'c.id=a.user_id')
                ->field('c.wname,b.role_name,a.created_time,a.status')
                ->list();
        }

        $url = rolesForm::exportUser($data);
        returnSuccess(['url' => $url]);
    }

    //导出
    public function exportUserRolesAndPermissions()
    {
        $adb = dbAMysql::getInstance();

        // 获取roles表中的数据，过滤is_delete为0的数据
        $data = $adb->table('roles')
            ->where('where is_delete = 0')
            ->list();

        // 遍历每个role，获取对应的authname并替换auth字段
        foreach ($data as &$role) {
            $array = json_decode($role['auth'], true);
            $authNames = [];

            foreach ($array as $value) {
                $auth = $adb->table('auth')
                    ->where('where auth = :auth', ['auth' => $value])
                    ->one(); // 获取单个值的方法应该是one

                if ($auth && isset($auth['auth_name'])) {
                    $authNames[] = $auth['auth_name'];
                } else {
                    $authNames[] = $value; // 如果找不到authname，保留原始auth值
                }
            }

            // 替换 auth 字段
            $role['auth'] = $authNames;
        }

        // 准备导出数据
        $excelData = [];
        foreach ($data as $roles) {
            $excelData[] = [
                'ID'       => $roles['id'],
                '角色名称' => $roles['role_name'],
                '权限'     => implode(', ', $roles['auth'])
            ];
        }
        //        returnSuccess($excelData);
        // 使用项目根目录动态构建导出文件路径
        $exportPath = SELF_FK . '/public/assessment/temp/role/data/user_roles_permissions.xlsx';

        // 检查并创建文件夹
        $exportDir = dirname($exportPath);
        if (!file_exists($exportDir)) {
            mkdir($exportDir, 0777, true);
        }

        // 使用 FastExcel 导出数据
        (new FastExcel($excelData))->export($exportPath);

        // 返回导出文件路径
        $exportPathqd = '/public/assessment/temp/role/data/user_roles_permissions.xlsx';
        returnSuccess($exportPathqd);
    }

    //移除角色
    public function removeRole()
    {
        $paras_list = array('rid', 'uid');
        $request_list = ['uid' => '用户id', 'rid' => '角色id'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

        $rid = (int)$param['rid'];
        $uid = (int)$param['uid'];

        $adb = dbAMysql::getInstance();
        $ce = $adb->table('user_roles')
            ->where('where id = :id and role_id = :role_id', [
                'id'      => $uid,
                'role_id' => $rid
            ])
            ->update([
                'is_delete'    => 1,
                'updated_time' => date('Y-m-d H:i:s')
            ]);

        returnSuccess([], '移除成功');

    }
}
