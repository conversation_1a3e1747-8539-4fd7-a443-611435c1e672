<?php

/**
 * @author: zhangguoming
 * @Time: 2024/3/27 9:02
 */

namespace plugins\shop\models;

use core\lib\db\dbShopMysql;

class userRolesModel
{
    //根据用户id获取用户角色权限
    public static function getAuthByQuserId($user_id) {
        $db = dbShopMysql::getInstance();
        $db->table('user_roles','a');
        $db->where('where user_id = :user_id and a.is_delete = 0 and a.status = 1',['user_id'=>$user_id]);
        $db->leftJoin('roles','b','b.id = a.role_id');
        $db->field('a.role_id,b.role_name,b.auth,b.type,b.list_auth,b.attach');
        $list = $db->list();
        $data = [
            'auth'=>'[]',
            'list_auth'=>[],
            'role_name'=>'',
            'role_type'=>[],
            'auth_detail' => []
        ];
        if (count($list)) {
            $role_name = [];
            $auth = [];
            $role_type=[];
            $list_auth = [];
            foreach ($list as $v) {
                $attach = json_decode($v['attach'],true);
                foreach ($attach as $k => $item) {
                    switch ($k) {
                        case 'shop/shop/getList;is_select,shop/shop/getList':
                            $type = 'shop';
                            break;
                        case 'shop/legalPerson/getList;is_select,shop/legalPerson/getList':
                            $type = 'legal_person';
                            break;
                        case 'shop/company/getList;is_select,shop/company/getList':
                            $type = 'company';
                            break;
                    }
                    if (isset($item['date_range']) && !empty($item['date_range'])) {
                        $detail = $item['date_range'];
                    } elseif (isset($item['shops']) && !empty($item['shops'])) {
                        $detail = $item['shops'];
                    } else {
                        continue;
                    }
                    $data['auth_detail'][$type][] = [
                        'auth' => $item['key'][0],
                        'detail' => $detail
                    ];
                }
                if ($v['auth'] == 'null') {
                    $v['auth'] = [];
                } else {
                    $v_auth = json_decode($v['auth'],true);
                    $v['auth'] = [];
                    foreach ($v_auth as $item_auth) {
                        $list_auth = explode(',',$item_auth);
                        $v['auth'] = array_merge($v['auth'],$list_auth);
                    }
                }
                if ($v['list_auth'] == 'null') {
                    $v['list_auth'] = [];
                } else {
                    $v['list_auth'] = json_decode($v['list_auth'],true);
                }
                $role_name[] = $v['role_name'];
                $auth = array_merge($auth,$v['auth']);
                $list_auth = array_merge($list_auth,$v['list_auth']);
                if (!in_array($v['type'],$role_type)) {
                    $role_type[] = $v['type'];
                }
            }
            $data['role_name'] = implode(',',$role_name);
            $auth = array_unique($auth);
            $auth = array_values($auth);
            $data['auth'] = json_encode($auth);
            $data['list_auth'] = json_encode($list_auth);
            $data['role_type'] = $role_type;
        }
        return $data;
    }

    // 获取所有角色，及角色下的用户
    public static function getRolesAndUsers(){
        $db = dbShopMysql::getInstance();
        $db->table('roles','a');
        $db->where('where a.is_delete = 0');
        $db->field('a.id,a.role_name,a.type,a.auth,a.list_auth');
        $roles = $db->list();
        $roles = array_column($roles,null,'id');

        $db->table('user_roles');
        $db->field('role_id,user_id');
        $db->where('where is_delete = 0 and status = 1');
        $user_roles = $db->list();

        foreach ($user_roles as $user_role) {
            $roles[$user_role['role_id']]['users'][] = $user_role['user_id'];
        }
        return array_values($roles);
    }


}