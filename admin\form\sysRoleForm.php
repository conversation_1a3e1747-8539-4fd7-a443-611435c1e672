<?php
namespace admin\form;
/**
 * @author: zhangguoming
 * @Time: 2025/3/24 14:33
 */

use admin\models\userModel;
use core\lib\db\dbMysql;
use Rap2hpoutre\FastExcel\FastExcel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
class sysRoleForm
{
    //获取角色列表
    public static function getlist($param)
    {
        $db = dbMysql::getInstance();
        $db->table('sys_role')->where('1 = 1');
        if (!empty($param['name'])){
            $db->andWhere('role_name LIKE :role_name',['role_name'=>'%'.$param['name'].'%']);
        }
        $list = $db->pages($param['page'], $param['page_size']);
        return $list;
    }
    //获取角色用户列表
    public static function getdetail($param)
    {
        $db = dbMysql::getInstance();
        $db->table('sys_role','a')
            ->leftJoin('sys_user_roles','b', 'b.role_id = a.id')
            ->where('a.id =:id and b.is_delete != 1',['id'=>$param['id']]);
        $info = $db->pages($param['page'], $param['page_size']);
        //姓名转化，获取用户详细信息
        $info = self::getUserDetail($db,$info);
        return $info;
    }
    //获取用户详细信息
    public static function getUserDetail($db,$info)
    {
        //获取数组中所有用户id
        if (empty($info['list'])) {
            return $info;
        }

        // 提取所有的 user_id（不再去重）
        $userIds = array_column($info['list'], 'qwuser_id');
        $qw_info = $db->table('qwuser')->whereIn('id', $userIds)->list();
        $userIds_name = array_column($qw_info, 'wname','id');
        $userIds_status = array_column($qw_info, 'wstatus','id');
        foreach ($info['list'] as &$item){
            $item['qwuser_name'] = $userIds_name[$item['qwuser_id']] ?? '';
            $item['wstatus'] = $userIds_status[$item['qwuser_id']] ?? '';
        }
        return $info;
    }
    //新增编辑角色
    public static function edit($param)
    {
        $db = dbMysql::getInstance();
        $updated_at = date('Y-m-d H:i:s');
        if ($param['id']){
            //检测除自身外是否有重名角色
            $check_role = $db->table('sys_role')
                ->where('id != :id AND role_name = :role_name', ['id' => $param['id'], 'role_name' => $param['name']])
                ->one();
            if ($check_role){
                returnError('角色名重复');
            }
            $db->table('sys_role')
                ->where('id = :id', ['id' => $param['id']])
                ->update([
                    'role_name' => $param['name'],
                    'updated_at'=>$updated_at
                ]);
            returnSuccess('','修改成功');
        }else{
            //检测是否有重名角色
            $check_role = $db->table('sys_role')
                ->where('role_name = :role_name and is_delete = 0', ['role_name' => $param['name']])
                ->one();
            if ($check_role){
                returnError('角色名重复');
            }

            $auth = self::inheritRole($param);

            $updated_at = date('Y-m-d H:i:s');
            $db->table('sys_role')
                ->insert([
                    'role_name' => $param['name'],
                    'auth' => $auth,
                    'created_at' => $updated_at,
                    'updated_at' =>$updated_at,
                ]);
            returnSuccess('','新增成功');
        }
    }
    //处理角色继承权限
    public static function inheritRole($param)
    {
        if (empty($param['role_id'])){
            //跳出
            return '[]';
        }
        $db = dbMysql::getInstance();
        $role_ids = json_decode($param['role_id'], true);
        $auth_list = [];

        foreach ($role_ids as $role_id){
            $auth = $db->table('role')
                ->where('id = :id', ['id' => $role_id])
                ->field('auth')
                ->one();

            $auth_list[] = $auth['auth'];
        }

        // 解析 JSON 字符串为数组
        $parsedArrays = array_map('json_decode', $auth_list);

        // 合并所有数组
        $merged_array = array_merge(...$parsedArrays);

        // 去重
        $uniqueArray = array_unique($merged_array);

        // 给每个元素加上引号
        $quoted_array = array_map(function($item) {
            return '"' . $item . '"';
        }, $uniqueArray);

        // 将带引号的元素拼接为一个字符串，用逗号和空格分隔
        $result = implode(", ", $quoted_array);

        // 用 [] 包裹结果
        $final_result = '[' . $result . ']';
        return $final_result;
    }
    //删除角色
    public static function delete($param){
        $db = dbMysql::getInstance();
        $updated_at = date('Y-m-d H:i:s');
        $db->table('sys_role')
            ->where('id = :id', ['id' => $param['role_id']])
            ->update(['is_delete' => 1,'updated_at'=>$updated_at]);
        $db->table('sys_user_roles')
            ->where('role_id = :role_id', ['role_id' => $param['role_id']])
            ->update(['is_delete' => 1,'updated_time'=>$updated_at]);
        returnSuccess('','删除成功');
    }
    //导入
    public static function import($param)
    {
        $db = dbMysql::getInstance();
        $excel_url = SELF_FK . $param['excel_src'];

        // 步骤1：验证Excel文件是否存在
        if (!file_exists($excel_url)) {
            returnError('Excel文件不存在');
        }

        // 步骤2：读取Excel内容
        try {
            $excelData = (new FastExcel)->import($excel_url);
            $data = $excelData->toArray();
            if (empty($data)) {
                returnError('Excel文件内容为空');
            }
        } catch (\Exception $e) {
            returnError('Excel文件读取失败：' . $e->getMessage());
        }

        // 步骤3：准备基础数据映射
        // 角色映射：角色名称 => ID
        $role_list = $db->table('sys_role')
            ->field('id,role_name')
            ->list();
        $role_map = array_column($role_list, 'id', 'role_name');

        // 用户映射：手机号（wphone） => ID
        $user_list = $db->table('qwuser')
            ->field('id, wname, wphone')
            ->list();
        $user_map = array_column($user_list, 'id', 'wphone');

        $currentTime  = date('Y-m-d H:i:s');
        $errorRecords = [];      // 错误记录集合
        $successCount = 0;       // 成功计数

        // 步骤4：开启事务处理
        $db->beginTransaction();
        try {
            foreach ($data as $index => $row) {
                $lineNumber = $index + 2; // 表头占1行，数据从第2行开始

                // 步骤4.1：数据清洗与默认值处理，注意Excel表头字段：姓名、手机号、角色
                $rawName      = trim($row['姓名'] ?? '');
                $rawPhone     = trim($row['手机号'] ?? '');
                $rawRoleStr   = trim($row['角色'] ?? '');

                // 用户验证：以手机号（wphone）为唯一性判断
                if (empty($rawPhone) || !isset($user_map[$rawPhone])) {
                    $errorRecords[] = [
                        '行号' => $lineNumber,
                        '原因' => "用户不存在：手机号 {$rawPhone}",
                    ];
                    continue;
                }
                $userId = $user_map[$rawPhone];

                // 角色验证及解析（支持多个角色，以逗号分隔）
                if (empty($rawRoleStr)) {
                    $errorRecords[] = [
                        '行号' => $lineNumber,
                        '原因' => '角色字段为空',
                    ];
                    continue;
                }
                $roleNames = array_filter(array_map('trim', explode(',', $rawRoleStr)));
                if (empty($roleNames)) {
                    $errorRecords[] = [
                        '行号' => $lineNumber,
                        '原因' => '未找到有效角色',
                    ];
                    continue;
                }

                // 检查所有角色是否存在
                $roleIds = [];
                $allExist = true;
                foreach ($roleNames as $roleName) {
                    if (!isset($role_map[$roleName])) {
                        $errorRecords[] = [
                            '行号' => $lineNumber,
                            '原因' => "角色不存在：{$roleName}",
                        ];
                        $allExist = false;
                        break;
                    }
                    $roleIds[] = $role_map[$roleName];
                }
                if (!$allExist) {
                    continue;
                }

                // 默认状态为启用（1）
                $status = 1;

                // 步骤4.2：针对每个角色进行导入操作
                foreach ($roleIds as $roleId) {
                    $operationData = [
                        'qwuser_id'      => $userId,
                        'role_id'      => $roleId,
                        'status'       => $status,
                        'updated_time' => $currentTime,
                    ];

                    try {
                        // 检查记录是否已存在（未删除）
                        $existing = $db->table('sys_user_roles')
                            ->where('qwuser_id = :uid AND role_id = :rid AND is_delete = 0', [
                                'uid' => $userId,
                                'rid' => $roleId,
                            ])
                            ->one();

                        if ($existing) {
                            // 已存在则更新记录
                            $db->table('sys_user_roles')
                                ->where('id = :id', ['id' => $existing['id']])
                                ->update($operationData);
                        } else {
                            // 不存在则插入新记录
                            $operationData['created_at'] = $currentTime;
                            $operationData['is_delete']    = 0;
                            $db->table('sys_user_roles')->insert($operationData);
                        }
                        $successCount++;
                    } catch (\Exception $e) {
                        // 捕获当前角色操作异常，不影响整体导入
                        $errorRecords[] = [
                            '行号' => $lineNumber,
                            '原因' => '数据库操作失败：' . $e->getMessage(),
                        ];
                    }
                }
            }

            // 步骤5：提交事务
            $db->commit();

            // 步骤6：返回结果
            if (!empty($errorRecords)) {
                $errorFile = self::generateErrorReport($errorRecords);
                returnSuccess(
                    [
                        'error_file'    => $errorFile,
                        'success_count' => $successCount,
                        'error_count'   => count($errorRecords),
                        'error_list'=>$errorRecords
                    ],
                    '部分数据导入失败，已生成错误报告'
                );
            }
            returnSuccess(['total' => count($data)], '数据导入成功');
        } catch (\Exception $e) {
            // 步骤7：异常时回滚事务
            $db->rollBack();
            returnError('导入过程中发生错误：' . $e->getMessage());
        }
    }
    //生成错误报告文件
    private static function generateErrorReport($errorRecords)
    {
        $fileName = '/public/temp/role_error/error_report_' . date('YmdHis') . '.xlsx';
        $filePath = SELF_FK . $fileName;

        // 确保目录存在
        $directory = dirname($filePath);
        if (!file_exists($directory)) {
            mkdir($directory, 0777, true);
        }

        // 使用 FastExcel 创建错误报告
        (new FastExcel($errorRecords))->export($filePath);
        return $fileName;
    }
    //导出模版生成
    public static function getTemplate() {
        $db = dbMysql::getInstance();

        try {
            // 步骤1：创建 Spreadsheet 对象，并移除默认工作表
            $spreadsheet = new Spreadsheet();
            $spreadsheet->removeSheetByIndex(0);

            // 步骤2：创建用户信息表
            $userSheet = new \PhpOffice\PhpSpreadsheet\Worksheet\Worksheet($spreadsheet, '导入模版');
            $spreadsheet->addSheet($userSheet);
            $userSheet->setCellValue('A1', '姓名')
                ->setCellValue('B1', '手机号')
                ->setCellValue('C1', '角色');

            // 步骤3：创建角色参照表
            $roleSheet = new \PhpOffice\PhpSpreadsheet\Worksheet\Worksheet($spreadsheet, '角色表');
            $spreadsheet->addSheet($roleSheet);
            $roleSheet->setCellValue('A1', '角色名');

            // 步骤4：获取角色数据
            $roleList = $db->table('sys_role')
                ->field('role_name')
                ->order('role_name ASC')
                ->list();

            if (empty($roleList)) {
                throw new \Exception("系统中暂无角色数据，请先创建角色");
            }

            // 步骤5：填充角色数据
            $rowNum = 2;
            foreach ($roleList as $role) {
                $roleSheet->setCellValue("A{$rowNum}", $role['role_name']);
                $rowNum++;
            }

            // 步骤6：保存文件
            $save_path = "/public/templates/sysRole/import";
            $url = SELF_FK . $save_path;
            // 创建目录（如果不存在）
            if (!file_exists($url)) {
                if (!mkdir($url, 0777, true)) {
                    returnError('Unable to create directory：' . $url);
                }
            }
            // 生成唯一的文件名
            $path = $save_path . DIRECTORY_SEPARATOR . date('YmdHis') . uniqid() . '.xlsx';
            $fullPath = SELF_FK . $path;

            // 保存文件到本地
            $writer = new Xlsx($spreadsheet);
            $writer->save($fullPath);

            // 验证文件是否生成成功
            if (!file_exists($fullPath)) {
                throw new \Exception("文件保存失败，请检查目录权限");
            }

            // 返回可访问的相对路径
            returnSuccess([
                'file_path' => $path,  // 最终返回保存的相对路径
            ], '模板生成成功');
        } catch (\Exception $e) {
            // 错误处理
            header_remove();
            http_response_code(500);
            exit("模板生成失败：" . $e->getMessage());
        }
    }
    //新增角色用户
    public static function addUser($param){
        $db = dbMysql::getInstance();
        $role_id = $param['role_id'];
        $returnErrorlist = [];
        $user_ids = json_decode($param['user_ids'], true);

        // 遍历每个用户ID，插入 oa_sys_user_roles 关联表
        foreach ($user_ids as $user_id) {
            // 检查是否已有该用户和角色的关系，避免重复插入（使用 qwuser_id 字段）
            $existing = $db->table('sys_user_roles')
                ->where('qwuser_id = :user_id AND role_id = :role_id AND is_delete != 1', [
                    'user_id' => $user_id,
                    'role_id' => $role_id
                ])
                ->one();

            // 检查用户是否存在，使用 oa_qwuser 表
            $user = $db->table('qwuser')
                ->where('id = :id', ['id' => $user_id])
                ->one();
            if (!$user) {
                returnError('用户'.$user_id.'不存在');
            }
            if (!$existing) {
                // 如果不存在该关系，则插入新记录
                $db->table('sys_user_roles')
                    ->insert([
                        'qwuser_id'  => $user_id,
                        'role_id'    => $role_id,
                        'created_at' => date('Y-m-d H:i:s'),
                        'status'     => 1,
                        'is_delete'  => 0,
                    ]);
            } else {
                $returnErrorlist[] = '用户ID: ' . $user_id . ' 已存在角色ID: ' . $role_id;
            }
        }

        if (!empty($returnErrorlist)){
            returnError('以下用户已经具有相同的角色关系: ' . implode(', ', $returnErrorlist));
        }
    }
    //移除角色用户
    public static function removeUser($param){
        $db = dbMysql::getInstance();
        $db->table('sys_user_roles')
            ->where('id = :user_id AND role_id = :role_id AND is_delete != 1', [
                'user_id' => $param['user_id'],
                'role_id' => $param['role_id']
            ])
            ->update(['is_delete' => 1]);
        returnSuccess('', '移除成功');
    }
    //启用禁用角色
    public static function disable($param){
        $db = dbMysql::getInstance();

        // 查询当前状态，使用 qwuser_id 替代 user_id
        $statusData = $db->table('sys_user_roles')
            ->where('id = :id AND role_id = :role_id AND is_delete != 1', [
                'id' => $param['user_id'],
                'role_id' => $param['role_id']
            ])
            ->field('status')
            ->one();
        if (empty($statusData)) {
            returnError('用户未在角色列表中列出');
        }

        // 切换状态：如果当前为 1 则更新为 0，否则更新为 1
        $newStatus = $statusData['status'] ? 0 : 1;

        $db->table('sys_user_roles')
            ->where('id = :id AND role_id = :role_id AND is_delete != 1', [
                'id' => $param['user_id'],
                'role_id' => $param['role_id']
            ])
            ->update(['status' => $newStatus]);
    }
    //权限设置
    public static function auth($param) {
        $db = dbMysql::getInstance();
        $db->table('sys_role');
        $time = date('Y-m-d H:i:s');
        if (!empty($param['role_id'])) {
            $db->where('id = :id', ['id' => $param['role_id']])
                ->update([
                    'auth' => $param['auth'],
                    'updated_at' => $time
                ]);
        } else {
            // 插入新记录
            $db->insert([
                'auth' => $param['auth'],
                'created_at' => $time,
            ]);
        }
    }
    //获取权限列表
    public static function getAuth() {
        $db = dbMysql::getInstance();
        $data = $db->table('sys_auth')
            ->where('status = 1')
            ->order('sort asc')
            ->field('id,p_id,auth_name,auth')
            ->list();
        return self::getAuthList(0,$data);
    }
    private static function getAuthList($pid,$auth_list) {
        $res_data = [];
        if (count($auth_list) > 0) {
            foreach ($auth_list as $key=>$val) {
                if ($val['p_id'] == $pid) {
                    $res_data[$key] = $val;
                    unset($auth_list[$key]);
                    $res_data[$key]['child'] = array_values(self::getAuthList($val['id'],$auth_list));
                }
            }
        }
        $res_data = array_values($res_data);
        return $res_data;
    }
    //获取角色权限
    public static function getRoleAuth($param)
    {
        $db = dbMysql::getInstance();
        $role_id = $param['role_id'];
        $auth = $db->table('sys_role')
            ->where('id = :id', ['id' => $role_id])
            ->field('auth')
            ->one();
        return $auth['auth'];
    }







}