<?php

/**
 * @author: zhangguoming
 * @Time: 2024/3/27 9:02
 */

namespace plugins\assessment\models;


use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\db\dbAMysql;
use core\lib\db\dbMysql;
use Cron\CronExpression;
use Exception;
use plugins\assessment\form\messagesFrom;
use Throwable;

class assessmentUsersModel
{

    // 用户考核任务
    public static function createAssessmentDetail($crontab) {
        $aid = $crontab['link_id'];
        $adb = dbAMysql::getInstance();
        $assessment = $adb->table('assessment')->where('where id = :id', ['id' => $aid])->one();
        $assessment['attach'] = json_decode($assessment['attach'], true);
        $scheme = $assessment['attach']['scheme'];
        $users = $assessment['attach']['apply_users'];
        $finance_column_ids = $assessment['attach']['finance_column_ids'];
        $dep_finance_column_ids = $assessment['attach']['dep_finance_column_ids'];
        $currency = $assessment['attach']['currency'];

        // 校验财务相关
        $finance_data = [];
        if ( !empty($finance_column_ids) || !empty($dep_finance_column_ids) ) {
            try {
                $finance_data = assessmentSchemesModel::checkFinanceCheckOut($assessment['assessment_cycle'],$scheme['assessment_cycle'], $finance_column_ids, $dep_finance_column_ids, $users, $scheme['attach']['department'], $currency);
            } catch (\Throwable $error) {
                returnError('财务数据有误，请联系管理员');
            }
        }

        $remind_user = $users; // 消息通知人
        // 上级
        $leader = $scheme['assessment_scheme_process'][3]['leader'] ?? null;
        $remind_user[] = $leader;
        // 审批人
        if ($scheme['assessment_scheme_process'][5]['status']) {
            $approver_user = $scheme['assessment_scheme_process'][5]['approver'] == 1 ? $scheme['assessment_scheme_process'][5]['approver_user'] : $leader;
            $remind_user[] = $approver_user;
        }

        $adb->beginTransaction();
        try {
            self::createAssessmentUser($assessment, $scheme, $users, $finance_data);
            $adb->commit();
        } catch (\Throwable $error) {
            $adb->rollBack();
            returnError($error->getMessage());
        }

        // 消息通知
        $remind_msg = "【{$assessment['assessment_name']}】已发起考核";

        $db = dbMysql::getInstance();
        $to_users = $db->table('qwuser')->whereIn('id', $remind_user)->field('wid')->list();
        //  发给当前节点的人
        messagesFrom::senMeg(array_column($to_users, 'wid'), 1, $remind_msg, $aid);

        // 定时任务设置为已完成
        $adb->table('custom_crontab')->where('where id = :id', ['id' => $crontab['id']])->update(
            ['status' => 1]
        );
        // 考核设置为进行中
        $adb->table('assessment')->where('where id = :id', ['id' => $aid])->update(
            ['status' => 0]
        );
        return true;
    }


    // 生成考核用户任务
    public static function createAssessmentUser($assessment, $scheme, $users, $finance_data): bool
    {
        if (empty($users)) return false;
        $adb = dbAMysql::getInstance();
        $db = dbMysql::getInstance();

        $user_map = $db->table('qwuser')->whereIn('id', $users)->field('id, wid, wname, avatar,wdepartment_ids')->list();
        $user_map = array_column($user_map, null, 'id');

        $department_map = $db->table('qwdepartment')->field('wp_id, name')->list();
        $department_map = array_column($department_map, null, 'wp_id');

        // 考核部门
        $assessment_department_id = $scheme['attach']['department'];

        // 获取指标custom_id
        $target_maps = $assessment['attach']['targets'];
        $target_maps = array_column($target_maps, null, 'id');

        $a_user_id = $assessment['user_id'];
        $a_user_name = $db->table('qwuser')->where('where id=:id', ['id' => $a_user_id])->field('wname')->one()['wname'];

        foreach ($users as $user) {
            $related_users = [
                $user, // 考核人自己
                $a_user_id ?: $scheme['user_id'], // 考核发起人或、考核方案发起人
                $scheme['assessment_scheme_process'][3]['leader'], // 考核人上级
            ];
            // 指定审批人
            if ($scheme['assessment_scheme_process'][5]['status'] && $scheme['assessment_scheme_process'][5]['approver'] == 1) {
                $related_users[] = $scheme['assessment_scheme_process'][5]['approver_user'];
            }
            $scheme['assessment_scheme_process'][0]['update_time'] = date('Y-m-d H:i:s');
            $scheme['assessment_scheme_process'][0]['update_user_id'] = $a_user_id ?: 0;
            $scheme['assessment_scheme_process'][0]['update_user_name'] = $a_user_name ?: '系统';

            $user_department_ids = json_decode($user_map[$user]['wdepartment_ids'], true);
            /* 1.0.0 废弃此类逻辑，用户实际考核部门为考核方案的部门
            // 单部门用户
            if (count($user_department_ids) == 1) {
                // 这里存在一种情况，用户仅在部门B，但是参与了部门A的考核，在实际计算时，使用用户在部门B的指标数据
                $assessment_department_id = $user_department_ids[0];
            } else {
                // 多部门用户
                if (!in_array($assessment_department_id, $user_department_ids)) {
                    // 这里用户肯定有一个部门在考核部门内
                }
            }
            */
            $user_departments = [];
            foreach ($user_department_ids as $department_id) {
                $user_departments[] = $department_map[$department_id];
            }

            $dep_column_result = $finance_data[$user]['dep_column_result'] ?? [];
            $dep_pre_column_result = $finance_data[$user]['dep_pre_column_result'] ?? [];
            $column_result = $finance_data[$user]['column_result'] ?? [];
            $pre_column_result = $finance_data[$user]['pre_column_result'] ?? [];
            $targets = [];
            $skip_flag = true; // 是否跳过上级评分节点
            foreach ($scheme['assessment_template']['list'] as $target) {
                $target_result = []; // target的结果
                if ($target_maps[$target['id']]['target_type'] == 1) { // 定量
                    $target_detail = json_decode($target_maps[$target['id']]['target_detail'], true);
                    $currency = $target_detail['currency'] ?? 0; // 只有财务才会有币种
                    if ($target_detail['target_method'] == 1) { // 按公式 -单指标
                        if ($target_detail['leader_score'] == 1) {
                            $skip_flag = false; // 上级评分节点
                        }
                        $is_solid = $target_detail['standard_value']['type'] == 1 ?: 0;
                        $solid_value = $target_detail['standard_value']['value'] ?? 0;
                        if ($target_detail['target_source'] == -1 || $target_detail['target_source'] == 2) { // 不可拉取、备货
                            $target_result = [
                                'id'             => $target['id'],
                                'standard_value' => $is_solid ? $solid_value : '',
                                'real_value'     => '',
                            ];
                            $skip_flag = false; // 不可拉取的指标不跳过上级评分节点
                        } else {
                            // tips: 这里的数据格式之后可能不一致，财务业绩指标下有不同币种！！！
                            $range_type = $target_detail['range_type'];
                            $pre_value_map = $range_type == 1 ? $pre_column_result : $dep_pre_column_result;
                            $value_map = $range_type == 1 ? $column_result : $dep_column_result;
                            // 拉取的数据有可能是个人的，也有可能是部门的
                            $target_result = [
                                'id'             => $target['id'],
                                'standard_value' => $is_solid ? $solid_value : ($pre_value_map[$target_detail['column_id']][$currency] ?? ''),
                                'real_value'     => $value_map[$target_detail['column_id']][$currency] ?? '',
                            ];
                        }
                        if (!$target_result['standard_value'] || $target_result['standard_value'] == 'error') {
                            $target_result['standard_value'] = '';
                            $target_result['standard_value_error'] = 1;
                            $skip_flag = false;
                        }
                        if (!$target_result['real_value'] || $target_result['real_value'] == 'error') {
                            $target_result['real_value'] = '';
                            $target_result['real_value_error'] = 1;
                            $skip_flag = false;
                        }

                    } 
                    elseif ($target_detail['target_method'] == 2) { // 按阶段 - 多指标
                        $skip_flag = false;
                        if ($target_detail['target_source'] == -1) { // 不可拉取
                            $target_result = [
                                'id'                => $target['id'],
                                'column_real_value' => [
                                    ['column_id' => '-1', 'real_value' => '', 'real_value_error' => 1]
                                ],
                            ];
                        } elseif ($target_detail['target_source'] == 2) { // 备货
                            $column_real_value = [];
                            foreach ($target_detail['column_ids'] as $column_id) {
                                $column_real_value[] = [
                                    'column_id'   => $column_id,
                                    'real_value' => '',
                                    'real_value_error' => 1,
                                ];
                            }
                            $target_result = [
                                'id'                => $target['id'],
                                'column_real_value' => $column_real_value,
                            ];
                        } else {
                            $stage_column_result = $target_detail['range_type'] == 1 ? $column_result : $dep_column_result;
                            $column_real_value = [];
                            foreach ($target_detail['column_ids'] as $column_id) {
                                $column_real_value[$column_id] = [
                                    'column_id'   => $column_id,
                                    'real_value' => $stage_column_result[$column_id][$currency] ?? '',
                                ];
                                if (!isset($stage_column_result[$column_id][$currency]) || $stage_column_result[$column_id][$currency] == 'error') {
                                    $column_real_value[$column_id]['real_value'] = '';
                                    $column_real_value[$column_id]['real_value_error'] = 1;
                                }
                            }
                            $target_result = [
                                'id'                => $target['id'],
                                'column_real_value' => array_values($column_real_value),
                            ];
                        }
                    }
                }
                else { // 定性
                    $skip_flag = false;
                    $target_result = [
                        'id' => $target['id'],
                    ];
                }
                $targets[] = $target_result;
            }

            // process 需要根据情况判断是否跳过上级评分节点
            $process = $scheme['assessment_scheme_process'];
            $process[3]['status'] = $skip_flag ? '0' : '1';

            $next_node = self::getNextNode($process, $user, 1);// 手动发起考核，直接从工作简述开始

            $attach = [
                'user_info'                    => $user_map[$user],
                'user_departments'             => $user_departments,
                'user_department_ids'          => $user_department_ids,
                'user_assessment_department'   => $department_map[$assessment_department_id],
                'finance_column_value'         => $column_result,
                'finance_column_value_pre'     => $pre_column_result,
                'finance_dep_column_value'     => $dep_column_result,
                'finance_dep_column_value_pre' => $dep_pre_column_result,
            ];
            $data = [
                'a_id'           => $assessment['id'],
                'user_id'        => $user,
                'attach'         => json_encode($attach, JSON_UNESCAPED_UNICODE),
                'status'         => 0, // 未完成
                'targets'        => json_encode($scheme['assessment_template']),
                'process'        => json_encode($process),
                'stage'          => $next_node['id'],
                'result'         => json_encode(['targets' => $targets], JSON_UNESCAPED_UNICODE),
                'stage_user'     => $next_node['stage_user'],
                'related_users'  => json_encode(array_values(array_unique(array_filter($related_users)))),
                'stage_deadline' => $next_node['stage_deadline'],
            ];
            $au_id = $adb->table('assessment_users')->insert($data);
            $data['id'] = $au_id;

            assessmentUsersModel::createNextNode($next_node, $data, $assessment, $process);

        }
        return true;

    }
    
    // 获取指定节点信息
    public static function getNode($process, $user_id, $stage_id)
    {
        $assessment_scheme_process = config::get('assessment_scheme_process', 'data_assessment');
        $stage_map = array_column($assessment_scheme_process, 'name', 'id');
        $current_node = [];
        foreach ($process as $node) {
            // 没开启的节点直接跳过
            if (isset($node['status']) && $node['status'] == 0) continue;
            if ($node['id'] == $stage_id) {
                $current_node = $node;
                break;
            }
        }
        if (empty($current_node)) return [];
        switch ($current_node['id']) {
            case 1:// 发起考核
                $current_node['stage_user'] = userModel::$qwuser_id ?? 0; // -1代表自动发起
                break;
            case 2:// 工作简述
            case 3:// 自评
            case 7:// 绩效确认
                $current_node['stage_user'] = $user_id ?? 0;
                break;
            case 4:// 上级
                $current_node['stage_user'] = $current_node['leader'];
                break;
            case 5:// 绩效核算
                $current_node['stage_user'] = 0; // 系统
                break;
            case 6:// 审批
                if ($current_node['approver'] == 1) {   // 指定人员
                    $current_node['stage_user'] = $current_node['approver_user'];
                } else { // 上级
                    $current_node['stage_user'] = $process[3]['leader'];
                }
                break;
        }

        if (isset($current_node['confirm_day']) && $current_node['confirm_day'] > 0) {
            $stage_deadline = date('Y-m-d H:i:s', strtotime('+' . $current_node['confirm_day'] . ' day'));
        }
        $current_node['stage_deadline'] = $stage_deadline ?? null;
        $current_node['stage_name'] = $stage_map[$current_node['id']] ?? '';
        return $current_node;
    }

    // 获取下一个节点信息
    public static function getNextNode($process, $user_id, $current_node = 0)
    {
        $assessment_scheme_process = config::get('assessment_scheme_process', 'data_assessment');
        $stage_map = array_column($assessment_scheme_process, 'name', 'id');
        $next_node = [];
        foreach ($process as $node) {
            // 没开启的节点直接跳过
            if (isset($node['status']) && $node['status'] == 0) continue;
            if ($node['id'] > $current_node) {
                $next_node = $node;
                break;
            }
        }
        if (empty($next_node)) return [];
        switch ($next_node['id']) {
            case 1:// 发起考核
                $next_node['stage_user'] = userModel::$qwuser_id ?? 0; // -1代表自动发起
                break;
            case 2:// 工作简述
            case 3:// 自评
            case 7:// 绩效确认
                $next_node['stage_user'] = $user_id ?? 0;
                break;
            case 4:// 上级
                $next_node['stage_user'] = $next_node['leader'];
                break;
            case 5:// 绩效核算
                $next_node['stage_user'] = 0; // 系统
                break;
            case 6:
                if ($next_node['approver'] == 1) {   // 指定人员
                    $next_node['stage_user'] = $next_node['approver_user'];
                } else { // 上级
                    $next_node['stage_user'] = $process[3]['leader'];
                }
                break;
        }

        if (isset($next_node['confirm_day']) && $next_node['confirm_day'] > 0) {
            $stage_deadline = date('Y-m-d H:i:s', strtotime('+' . $next_node['confirm_day'] . ' day'));
        }
        $next_node['stage_deadline'] = $stage_deadline ?? null;
        $next_node['stage_name'] = $stage_map[$next_node['id']] ?? '';
        return $next_node;
    }
    
    // 生成下一个节点
    public static function createNextNode($next_node, $assessment_user, $assessment, $process, $update_data = []) {
        $adb = dbAMysql::getInstance();
        $db = dbMysql::getInstance();

        // 当前节点更新时间
        $current_node_id = $assessment_user['stage'];
        $assessment_scheme_process = config::get('assessment_scheme_process', 'data_assessment');
        $stage_map = array_column($assessment_scheme_process, 'name', 'id');
        $current_node_name = $stage_map[$current_node_id] ?? '';
        $process[$current_node_id - 1]['update_time'] = date('Y-m-d H:i:s');
        $process[$current_node_id - 1]['update_user_id'] = userModel::$qwuser_id ?? 0;
        $process[$current_node_id - 1]['update_user_name'] = userModel::$wname ?? '系统';

        // 没有下一个节点，考核结束
        if (empty($next_node)) {
            // 更新考核用户状态
            $update_data['process'] = json_encode($process, JSON_UNESCAPED_UNICODE);
            $update_data['status'] = 1;
            $adb->table('assessment_users');
            $adb->where('where id = :id', ['id' => $assessment_user['id']])->update($update_data);
            // 记录日志-考核完成
            $adb->table('assessment_users_log');
            $adb->insert([
                'user_id' => userModel::$qwuser_id ?? 0,
                'a_id'    => $assessment_user['a_id'],
                'au_id'   => $assessment_user['id'],
                'changes' => json_encode([
                    'type'   => 4,
                ], JSON_UNESCAPED_UNICODE)
            ]);

            // 更新考核状态
            $adb->table('assessment_users', 'au')->field('au.status, count(au.id) as count');
            $adb->where('where a_id = :a_id', ['a_id' => $assessment['id']]);
            $adb->groupBy(['au.status']);
            $status = $adb->list();
            $status_map = array_column($status, 'count', 'status');
            $assessment_status = assessmentModel::getAssessmentStatus($assessment['id'], $status_map);
            if ($assessment_status != $assessment['status']) {
                $adb->table('assessment')->where('where id = :id', ['id' => $assessment['id']])->update([
                    'status' => $assessment_status,
                ]);
            }
            // 最后一个节点是自动核算，发给绩效发起人和被考核人
            if ($assessment_user['stage'] == 5 && !isset(userModel::$qwuser_id)) {
                //  发给绩效发起人和被考核人
                $to_user_ids = [$assessment_user['user_id'], $assessment['attach']['user_id']];
                $to_users = $db->table('qwuser')->whereIn('id', $to_user_ids)->field('id, wid')->list();
                $remind_msg = "【{$assessment['assessment_name']}】的绩效已核算完成，您可以查看考核结果";
                messagesFrom::senMeg(array_column($to_users, 'wid'), 1, $remind_msg, $assessment['id']);
                return true;
            } 
            // 所有人都完成
            if ($assessment_status == 1) {
                $remind_user = $assessment['attach']['apply_users']; // 消息通知人
                // 上级
                $assessment['attach']['leader'] && $remind_user[] = $assessment['attach']['leader'];
                // 审批人
                $assessment['attach']['approver_user'] && $remind_user[] = $assessment['attach']['approver_user'];
                $remind_user = array_values(array_unique($remind_user));
                $remind_msg = "【{$assessment['assessment_name']}】已全部完成，您可以查看绩效考核结果";
                $db = dbMysql::getInstance();
                $to_users = $db->table('qwuser')->whereIn('id', $remind_user)->field('wid')->list();
                //  发给当前节点的人
                messagesFrom::senMeg(array_column($to_users, 'wid'), 1, $remind_msg, $assessment['id']);
            }
            return true;
        }

        // 更新阶段、阶段人、阶段截止时间
        $update_data['stage'] = $next_node['id'];
        $update_data['stage_user'] = $next_node['stage_user'];
        $update_data['stage_deadline'] = $next_node['stage_deadline'];
        $update_data['process'] = json_encode($process, JSON_UNESCAPED_UNICODE);
        
        $adb->table('assessment_users');
        $adb->where('where id = :id', ['id' => $assessment_user['id']])->update($update_data);
        
        // 绩效确认
        if ($next_node['id'] == 2) { 
            $remind_msg = "【{$assessment['assessment_name']}】需要您【工作简述】，请在{$next_node['confirm_day']}天内及时处理。超时将自动提交";
            $title = '绩效考核_工作简述';
        }         
        // 自评节点的消息通知
        if ($next_node['id'] == 3) {
            $remind_msg = "请在{$next_node['confirm_day']}天内及时完成绩效考核的【{$next_node['stage_name']}】。超时将自动按系统设定的默认值提交";
            $title = "绩效考核_{$next_node['stage_name']}";
            //  发给当前节点的人
            $to_user = $db->table('qwuser')->where('id = :id', ['id' => $next_node['stage_user']])->field('id, wid')->one();
            messagesFrom::senMeg([$to_user['wid']], 1, $remind_msg, $assessment['id'], '', $title);
        }
        // 上级评分的消息通知
        if ($next_node['id'] == 4) {
            $adb->table('assessment_users', 'au');
            $adb->where('where a_id = :a_id', ['a_id' => $assessment['id']]);
            $assessment_users = $adb->list();
            $leader_stage_count = 0; // 已到达上级评分的人数
            foreach ($assessment_users as $user) {
                if ($user['stage'] >= 4) {
                    $leader_stage_count++;
                }
            }
            // 第一个到达该上级
            if ($leader_stage_count == 1 && $current_node_id != 1) {
                $remind_msg = "【{$assessment['assessment_name']}】可以开始【{$next_node['stage_name']}】，请您及时处理";
                $title = "绩效考核_{$next_node['stage_name']}";
                //  发给当前节点的人
                $to_user = $db->table('qwuser')->where('id=:id', ['id' => $next_node['stage_user']])->field('id, wid')->one();
                messagesFrom::senMeg([$to_user['wid']], 1, $remind_msg, $assessment['id'], '', $title);
            } // 所有都到达上级
            elseif ($leader_stage_count == count($assessment_users)) {
                $remind_msg = "【{$assessment['assessment_name']}】内的所有人员都可进行【{$next_node['stage_name']}】，请您及时处理";
                $title = "绩效考核_{$next_node['stage_name']}";
                //  发给当前节点的人
                $to_user = $db->table('qwuser')->where('id=:id', ['id' => $next_node['stage_user']])->field('id, wid')->one();
                messagesFrom::senMeg([$to_user['wid']], 1, $remind_msg, $assessment['id'], '', $title);
            }
        }

        // 绩效核算
        if ($next_node['id'] == 5) {
            // 增加定时任务进行绩效核算
            $crontab_data = [
                'is_crontab_task' => 0, // 非周期任务
                'link_id'         => $assessment_user['id'],
                'link_type'       => 2,
                'runtime'         => date('Y-m-d H:i:s'),
            ];
            $adb->table('custom_crontab');
            $adb->insert($crontab_data);
        }

        // 审批
        if ($next_node['id'] == 6) {
            // 上级评分的消息通知
            $adb->table('assessment_users', 'au');
            $adb->where('where a_id = :a_id', ['a_id' => $assessment['id']]);
            $assessment_users = $adb->list();
            $audit_stage_count = 0; // 已到达审批的人数
            foreach ($assessment_users as $user) {
                if ($user['stage'] >= 6) {
                    $audit_stage_count++;
                }
            }
            // 第一个到达审批
            if ($audit_stage_count == 1) {
                $assessment_user_name = $db->table('qwuser')->where('id = :id', ['id' => $assessment_user['user_id']])->field('id, wid,wname')->one()['wname'];
                $remind_msg = "【{$assessment['assessment_name']}】中 {$assessment_user_name} 的绩效已核算完成，可以进行【审批】，请您及时进行处理";
                $title = '绩效考核_审批';
                //  发给当前节点的人
                $to_user = $db->table('qwuser')->where('id =:id', ['id' => $next_node['stage_user']])->field('id, wid')->one();
                messagesFrom::senMeg([$to_user['wid']], 1, $remind_msg, $assessment['id'], '', $title);
            } // 所有都到达审批
            elseif ($audit_stage_count == count($assessment_users)) {
                $remind_msg = "【{$assessment['assessment_name']}】中成员的绩效已全部核算完成，可以进行【审批】，请您及时进行处理";
                $title = '绩效考核_审批';
                //  发给当前节点的人
                $to_user = $db->table('qwuser')->where('id =:id', ['id' => $next_node['stage_user']])->field('id, wid')->one();
                messagesFrom::senMeg([$to_user['wid']], 1, $remind_msg, $assessment['id'], '', $title);
            }
        }

        // 绩效确认
        if ($next_node['id'] == 7) {
            $remind_msg = "【{$assessment['assessment_name']}】的绩效已{$current_node_name}完成，可以进行【{$next_node['stage_name']}】，请您及时处理。超时将自动提交确认";
            $title = "绩效考核_{$next_node['stage_name']}";
            //  发给当前节点的人
            $db = dbMysql::getInstance();
            $to_user = $db->table('qwuser')->where('id=:id', ['id' => $next_node['stage_user']])->field('id, wid')->one();
            messagesFrom::senMeg([$to_user['wid']], 1, $remind_msg, $assessment['id'], '', $title);
        }

        //  下一个节点的超时通知
        if ($next_node['stage_deadline']) {
            // 下一个节点自评/上级
            $crontab_data = [
                'is_crontab_task' => 0, // 非周期任务
                'link_id'         => $assessment_user['id'],
                'link_type'       => 3,
                'runtime'         => date('Y-m-d H:i:s', strtotime('-2 hours', strtotime($next_node['stage_deadline']))),
            ];
            $adb->table('custom_crontab');
            $adb->insert($crontab_data);
        }
    }

    // 绩效核算
    public static function calcPerformance($crontab): bool
    {

        $au_id = $crontab['link_id'];
        $adb = dbAMysql::getInstance();
        $db = dbMysql::getInstance();
        $assessment_user = $adb->table('assessment_users')->where('where id = :id', ['id' => $au_id])->one();
        // 只处理暂停和进行中的任务
        if (!in_array($assessment_user['status'], ['0', '2'])) return false;

        $attach = json_decode($assessment_user['attach'], true);
        $user_process = json_decode($assessment_user['process'], true);
        $assessment = $adb->table('assessment')->where('where id = :id', ['id' => $assessment_user['a_id']])->one();
        $assessment['attach'] = json_decode($assessment['attach'], true);
        $scheme = $assessment['attach']['scheme'];
        $template = $scheme['assessment_template'];
        $result = json_decode($assessment_user['result'], true);

        $targets = $assessment['attach']['targets'];
        $commissions = $assessment['attach']['commissions'];
        $level_rules = $assessment['attach']['level_rules'];

        $target_map = array_column($targets, null, 'id');
        $commission_map = array_column($commissions, null, 'id');
        $level_rule_map = array_column($level_rules, null, 'id');
        $target_weight = array_column($template['list'], 'value', 'id');
        $target_score_type = $scheme['attach']['score_type'];
        // 指标考核结果
        $error = false;

        foreach ($result['targets'] as &$target) {
            $a_target = $target_map[$target['id']];
            $target_detail = json_decode($a_target['target_detail'], true);

            // 考核指标值、考核实际值
            $standard_value = 0;
            $real_value = 0;
            $column_real_value = [];
            if ($a_target['target_type'] == 1) { // 定量
                if ($target_detail['target_method'] == 1) { //按公式，只有一个业绩指标
                    $standard_value = $target['standard_value'];
                    $real_value = $target['real_value'];
                } else { // 按阶梯，多个业绩指标
                    $column_real_value = array_column($target['column_real_value'], 'real_value', 'column_id');
                }
            }

            // 清除之前的错误标记
            unset($target['result_error']);

            try {
                empty($target['self_score']) && $target['self_score'] = 0;
                $target['result'] = assessmentTargetsModel::getTargetResult(
                    $target_weight[$target['id']],
                    $target_detail,
                    $a_target['target_type'],
                    $target_score_type,
                    $standard_value,
                    $real_value,
                    $column_real_value,
                    ['self_score' => $target['self_score'] ?? null, 'leader_score' => $target['leader_score'] ?? null]
                );
            } catch (Throwable $e) {
                // 捕获Exception、Error
                $error = true;
                $target['result'] = '';
                $target['result_error'] = 1;
            }
        }

        if ($error) {
            $result['target_coe'] = '';
            $result['target_coe_error'] = 1;
            $result['performance'] = '';
            $result['performance_error'] = 1;
        } 
        
        else {
            // 绩效考核结果
            unset($result['target_coe_error']);
            $result['target_coe'] = assessmentSchemesModel::calcCeo($result['targets'], $scheme['attach']['score_type'], $target_weight);

            try {
                // 绩效核算
                $calculation_detail = $user_process[4];
                $formula = $calculation_detail['formula'];
                $level_rules = [];
                $coefficient_rules = [];
                $default_coefficient = 0;
                // 等级规则
                if ($calculation_detail['performance_calculation_method'] == 3) {
                    $level_rule_id = $calculation_detail['level_rule_id'];
                    $level_rule = $level_rule_map[$level_rule_id];
                    $level_rules = json_decode($level_rule['level_rules'], true);
                    $coefficient_rules = json_decode($level_rule['coefficient_rules'], true);
                    $default_coefficient = $level_rule['default_coefficient'];
                }
                $finance_data = [
                    'column_result'         => $attach['finance_column_value'],
                    'pre_column_result'     => $attach['finance_column_value_pre'],
                    'dep_column_result'     => $attach['finance_dep_column_value'],
                    'dep_pre_column_result' => $attach['finance_dep_column_value_pre'],
                ];

                // 按公式计算，先计算部门下员工的绩效结果
                // 提成比例奖励
                $prize_commission_rule_type = null;
                $prize_commission_rule = [];
                if ($calculation_detail['performance_calculation_method'] == 1) {
                    $is_wait = 0;
                    foreach ($formula as &$item) {
                        if ($item['type'] == 1) { // 条件
                            if ($item['value_type'] == 2 && $item['value'][0] == 3 && $item['value'][1] == 1) {
                                $user_scores = self::getPerformanceByUsers($item['users'], $scheme['attach']['department'], json_decode($assessment['assessment_cycle'], true), $scheme['assessment_type']);
                                foreach ($item['users'] as $user) {
                                    // 员工结果还没算出来
                                    if (!isset($user_scores[$user['id']])) {
                                        $is_wait = 1;
                                    }
                                }
                                $item['user_scores'] = $user_scores;
                            }
                        }
                        elseif ($item['type'] == 2) { // 条件组
                            foreach ($item['list'] as &$subItem) {
                                if ($subItem['value_type'] == 2 && $subItem['value'][0] == 3 && $subItem['value'][1] == 1) {
                                    $user_scores = self::getPerformanceByUsers($subItem['users'], $scheme['attach']['department'], json_decode($assessment['assessment_cycle'], true), $scheme['assessment_type']);
                                    foreach ($subItem['users'] as $user) {
                                        // 员工结果还没算出来
                                        if (!isset($user_scores[$user['id']])) {
                                            $is_wait = 1;
                                        }
                                    }
                                    $subItem['user_scores'] = $user_scores;
                                }
                            }
                        }
                    }
                    $user_process[4]['formula'] = $formula;
                    $adb->table('assessment_users');
                    $adb->where('where id = :id', ['id' => $assessment_user['id']])->update([
                        'process' => json_encode($user_process, JSON_UNESCAPED_UNICODE),
                    ]);

                    // 需要等员工核算完成，才能进行核算
                    if ($is_wait) {
                        // 任务未完成，10分钟后再次执行
                        $adb->table('custom_crontab')->where('where id = :id', ['id' => $crontab['id']])->update([
                            'status'  => -1,
                            'runtime' => date('Y-m-d H:i:s', strtotime('+10 minutes')),
                        ]);
                        return true;
                    }
                    $prize_commission_rule_id = $calculation_detail['prize_commission_rule_id'] ?? 0;
                    $prize_commission_rule_type = $calculation_detail['prize_commission_rule_type'] ?? 0;
                    $prize_commission_rule = $commission_map[$prize_commission_rule_id] ?? [];
                }

                unset($result['performance_error']);

                $performance_result = assessmentSchemesModel::calcPerformance(
                    $calculation_detail['performance_calculation_method'],
                    $assessment_user['user_id'],
                    $scheme,
                    json_decode($assessment['assessment_cycle'], true),
                    $finance_data,
                    $result['targets'],
                    $result['target_coe'],
                    $level_rules,
                    $coefficient_rules,
                    $default_coefficient,
                    $formula,
                    $commission_map,
                    $prize_commission_rule,
                    $prize_commission_rule_type
                );
                $result['performance'] = $performance_result['performance'];
                if (!empty($performance_result['level'])) {
                    $result['level'] = $performance_result['level'];
                }
            } catch (Throwable $e) {
                $error = true;
                $result['performance'] = '';
                $result['performance_error'] = 1;
            }
        }

        // 未命中规则时，核算出错
        if ($result['performance'] == 'error') {
            $error = true;
            $result['performance'] = '';
            $result['performance_error'] = 1;
        }

        // 手动录入
        if ($result['performance'] == 'manual') {
            $result['performance'] = '';
            // 修改当前处理人为上级
            $adb->table('assessment_users');
            $adb->where('where id = :id', ['id' => $assessment_user['id']])->update([
                'stage_user' => $assessment['attach']['leader'],
                'result'     => json_encode($result, JSON_UNESCAPED_UNICODE),
            ]);
            // 定时任务设置为已完成
            $adb->table('custom_crontab')->where('where id = :id', ['id' => $crontab['id']])->update(
                ['status' => 1]
            );
            return true;
        }

        if ($error) {
            // 修改当前处理人为上级
            $adb->table('assessment_users');
            $adb->where('where id = :id', ['id' => $assessment_user['id']])->update([
                'stage_user' => $assessment['attach']['leader'],
                'result'     => json_encode($result, JSON_UNESCAPED_UNICODE),
            ]);
            //  发给上级
            $to_user = $db->table('qwuser')->where('where id=:id', ['id' => $assessment['attach']['leader']])->field('id, wid')->one();
            $remind_msg = "【{$assessment['assessment_name']}】核算出错，请您手动维护考核数据";
            messagesFrom::senMeg([$to_user['wid']], 1, $remind_msg, $assessment['id']);
            // 定时任务设置为已完成
            $adb->table('custom_crontab')->where('where id = :id', ['id' => $crontab['id']])->update(
                ['status' => 1]
            );
            return true;
        }
        $process = $assessment_user['process'] ? json_decode($assessment_user['process'], true) : [];
        // 生成下一个节点
        $next_node = assessmentUsersModel::getNextNode($process, $assessment_user['user_id'], $assessment_user['stage']);
        
        $update_data = [
            'result' => json_encode($result, JSON_UNESCAPED_UNICODE),
        ];
        assessmentUsersModel::createNextNode($next_node, $assessment_user, $assessment, $process, $update_data);
        // 定时任务设置为已完成
        $adb->table('custom_crontab')->where('where id = :id', ['id' => $crontab['id']])->update(
            ['status' => 1]
        );
        return true;
    }

    // 超时提醒
    public static function overtimeMsg($crontab)
    {
        $assessment_user_id = $crontab['link_id'];
        $adb = dbAMysql::getInstance();
        $assessment_user = $adb->table('assessment_users')->where('where id = :id', ['id' => $assessment_user_id])->one();
        $process = $assessment_user['process'] ? json_decode($assessment_user['process'], true) : [];
        $assessment = $adb->table('assessment')->where('where id = :id', ['id' => $assessment_user['a_id']])->one();
        $current_node = assessmentUsersModel::getNode($process, $assessment_user['user_id'], $assessment_user['stage']);
        $next_node = assessmentUsersModel::getNextNode($process, $assessment_user['user_id'], $assessment_user['stage']);
        if ($assessment_user['status'] == 0 && // 考核中
            time() > strtotime($assessment_user['stage_deadline']) && // 节点超时时间
            $assessment_user['stage_user'] != 0 && // 有处理人
            in_array($assessment_user['stage'], [2, 3, 4, 7])) { // 工作简述、自评、上级评分、绩效确认
            // 消息通知
            $remind_msg = "【{$assessment['assessment_name']}】的【{$current_node['stage_name']}】2小时候将超时，请您及时处理";
            $db = dbMysql::getInstance();
            $to_users = $db->table('qwuser')->whereIn('id', [$assessment_user['stage_user']])->field('wid')->list();
            //  发给当前节点的人
            messagesFrom::senMeg(array_column($to_users, 'wid'), 1, $remind_msg, $assessment['id']);
            if (in_array($assessment_user['stage'], [2, 3, 7])) { // 工作简述、自评、绩效确认
                // 生成超时处理任务
                $crontab_data = [
                    'is_crontab_task' => 0, // 非周期任务
                    'link_id'         => $assessment_user_id,
                    'link_type'       => 4,
                    'runtime'         => date('Y-m-d H:i:s', time() + 7200),
                ];
                $adb->table('custom_crontab');
                $adb->insert($crontab_data);
            }
        }
        // 将定时任务设置为已完成
        $adb->table('custom_crontab')->where('where id = :id', ['id' => $crontab['id']])->update(
            ['status' => 1]
        );
    }

    // 超时处理
    public static function overtimeHandle($crontab)
    {
        $assessment_user_id = $crontab['link_id'];
        $adb = dbAMysql::getInstance();
        $db = dbMysql::getInstance();
        $assessment_user = $adb->table('assessment_users')->where('where id = :id', ['id' => $assessment_user_id])->one();
        $process = $assessment_user['process'] ? json_decode($assessment_user['process'], true) : [];
        
        $assessment = $adb->table('assessment')->where('where id = :id', ['id' => $assessment_user['a_id']])->one();
        $assessment['attach'] = json_decode($assessment['attach'], true);
        $next_node = assessmentUsersModel::getNextNode($process, $assessment_user['user_id'], $assessment_user['stage']);
        $current_node = assessmentUsersModel::getNode($process, $assessment_user['user_id'], $assessment_user['stage']);
        
        // 不处理
        if ($assessment_user['status'] != 0 || // 非考核中
        time() < strtotime($assessment_user['stage_deadline']) || // 节点未超时
        $assessment_user['stage_user'] == 0 || // 没有处理人
        !in_array($assessment_user['stage'], [2, 3, 7])) { // 工作简述、自评、绩效确认
            $adb->table('custom_crontab')->where('where id = :id', ['id' => $crontab['id']])->update(
                ['status' => 1]
            );
            return;
        }
        
        // 自评节点需要计算自评得分
        $update_data = [];
        if ($assessment_user['stage'] == 3) {
            $result = assessmentUsersModel::calcDefaultSelfScore($assessment_user, $assessment, $process);
            $update_data['result'] = json_encode($result, JSON_UNESCAPED_UNICODE);
        }
        assessmentUsersModel::createNextNode($next_node, $assessment_user, $assessment, $process, $update_data);

        $remind_msg = "【{$assessment['assessment_name']}】的【{$current_node['stage_name']}】因超时未处理，已自动提交";
        $to_users = $db->table('qwuser')->whereIn('id', [$assessment_user['stage_user']])->field('wid')->list();
        //  发给当前节点的人
        messagesFrom::senMeg(array_column($to_users, 'wid'), 1, $remind_msg, $assessment['id']);

        // 将定时任务设置为已完成
        $adb->table('custom_crontab')->where('where id = :id', ['id' => $crontab['id']])->update(
            ['status' => 1]
        );

    }


    // 获取某周期内的绩效等级次数
    public static function getPerformanceLevelCount($user_id, $scheme_id, $assessment_cycle, $level_arr = [])
    {
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_users', 'au');
        $adb->where('where au.user_id = :user_id and au.status = 1', [
            'user_id' => $user_id,
        ]);
        $adb->leftJoin('assessment', 'a', 'a.id = au.a_id');
        $adb->andWhere('a.a_s_id = :scheme_id', ['scheme_id' => $scheme_id]);
        $adb->andWhere('JSON_EXTRACT(assessment_cycle, "$[1]") >= :start_time and JSON_EXTRACT(assessment_cycle, "$[0]") <= :end_time ',
            ['start_time' => $assessment_cycle[0], 'end_time' => $assessment_cycle[1]]);
        $assessment_users = $adb->list();
        if (empty($assessment_users)) return $level_arr;

        foreach ($assessment_users as $user) {
            $result = json_decode($user['result'], true);
            if (!empty($result['level'])) {
                $level_arr[$result['level']] = $level_arr[$result['level']] ?? 0;
                $level_arr[$result['level']]++;
            }
        }
        return $level_arr;

    }

    // 获取批量员工考核周期内某方案的绩效/提成
    public static function getPerformanceByUsers($users, $department_id, $assessment_cycle, $assessment_type)
    {
        $user_ids = array_column($users, 'id');
        $adb = dbAMysql::getInstance();
        $adb->table('assessment_users', 'au')->field('au.user_id, au.attach, au.result, a.attach as a_attach');
        $adb->where('where au.status = 1 and a.status = 1 and a.is_delete = 0');
        $adb->whereIn('au.user_id', $user_ids);
        $adb->leftJoin('assessment', 'a', 'a.id = au.a_id');
        $adb->andWhere('JSON_EXTRACT(assessment_cycle, "$[0]") = :start_time and JSON_EXTRACT(assessment_cycle, "$[1]") = :end_time ',
            ['start_time' => $assessment_cycle[0], 'end_time' => $assessment_cycle[1]]);
        $assessment_users = $adb->list();
        $result = [];
        // 需要找到员工实际考核部门的绩效
        foreach ($assessment_users as $user) {
            $a_attach = json_decode($user['a_attach'], true);
            if ($a_attach['scheme']['assessment_type'] != $assessment_type) continue;
            $attach = json_decode($user['attach'], true);
            $user_assessment_department = $attach['user_assessment_department'];
            if ($user_assessment_department['wp_id'] != $department_id) continue;
            if (!array_key_exists($user['user_id'], $result)) {
                $result[$user['user_id']] = 0;
            }
            $performance_result = json_decode($user['result'], true);
            $performance = $performance_result['final_performance'] ?? $performance_result['performance'] ?? 0;
            $result[$user['user_id']] += floatval($performance);
        }
        return $result;
    }

    // 计算默认自评得分
    public static function calcDefaultSelfScore($assessment_user, $assessment, $process)
    {
        $targets = json_decode($assessment_user['targets'], true);
        $result = json_decode($assessment_user['result'], true);
        $assessment_attach = json_decode($assessment['attach'], true);
        $score_type = $assessment_attach['scheme']['attach']['score_type'];
        $score_system = $assessment_attach['scheme']['attach']['score_system']; // 分制
        $target_score_map = array_column($targets['list'], 'value', 'id');
        $default_value = $process[2]['default_value'];
        foreach ($result['targets'] as &$target) {
            if ($score_type == 1) {// 加权
                $target['self_score'] = $default_value;
            } elseif ($score_type == 2) { // 加和
                $target['self_score'] = $target_score_map[$target['id']] * $default_value / 100;
            }
            // 四舍五入保留两位小数
            $target['self_score'] = round($target['self_score'], 2);
        }
        return $result;
    }

}