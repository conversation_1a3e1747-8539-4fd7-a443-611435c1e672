<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/12 17:43
 */

namespace financial\form;

use Rap2hpoutre\FastExcel\FastExcel;

class routingForm
{
    public static array $export_key = [
        'date'=>'汇率年月',
        'code'=>'币种',
        'icon'=>'币种符号',
        'name'=>'币种名',
        'my_rate'=>'我的汇率',
    ];
    public static function export($list) {
        //表头
        $new_data = [];
        //数据
        $xuhao = 1;
        foreach ($list as $v) {
            $item['序号'] = $xuhao;
            foreach ($v as $k=>$v1) {
                if (!isset(self::$export_key[$k])) {
                    continue;
                }
                $value = $v1;
                $item[self::$export_key[$k]] = $value;
            }
            $new_data[] = $item;
            $xuhao++;
        }
        //保存
        $save_path = "/public_financial/temp/routing";
        $url = SELF_FK.$save_path;
        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path."/".date('YmdHis').uniqid().'.xlsx';
        $url = SELF_FK.$path;
        if (!file_exists($url)) {
            touch($url);
        }
        (new FastExcel($new_data))->export($url);
        return $path;
    }
}