<?php
/**
 * Created by 漠山.
 * @purpose 描述
 * @Author: 漠山
 * @Time: 2023/12/3 16:21
 */
namespace core;
use core\lib\checkToken;
use core\lib\checkTokenFinancial;
use core\lib\route;

class fk
{
    public static $classMap = [];
    static public function run(){
        ini_set('memory_limit', '256M');
        $http_host  = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http";
        $http_host .= "://" . $_SERVER['HTTP_HOST'];
        define('HTTP_HOST',$http_host); //请求路由
        define('SERVER_NAME',$_SERVER['SERVER_NAME']); //请求路由
        $route = new route();
        $module = $route->module;
        $file_module = $route->module;
        if (!in_array($module,['task','admin','financial'])) {
            $file_module = 'plugins/'.$module;
            $module  = 'plugins\\'.$module;
        }
        define('APP', SELF_FK.'/'.$file_module); // 项目文件目录
        $ctrlClass = $route->ctrl;
        $action = $route->action;
        define('URL_ACTION',$action); //请求路由
        $ctrlFile = APP.'/controller/'.$ctrlClass.'Controller.php';
        if (is_file($ctrlFile)) {
            $Controller = $module.'\\controller\\'.$ctrlClass.'Controller';
            $ctrl = new $Controller();
            if (!method_exists($ctrl, $route->action)) {
                showError('方法'.$route->action.'不存在');die;
            }
            //加载控制器之前实现token验证
            $url = $route->module.'/'.$route->ctrl.'/'.$route->action;
            if ($module == 'financial') {
                publish($url);
                $headerData = getallheaders();
                $mac = $headerData['Mac']??'';
                define('MAC', $mac);
                if (!$mac) {
                    SetReturn(-1, "mac地址错误");
                }
                $prefix = (new \core\lib\config)::get('financial_token_key_prefix', 'app');
                $check_token = new checkTokenFinancial($url, $prefix);
                $auth = (new \core\lib\authFinancial())::checkAuth($url, $prefix);
            } else {
                if ($module != 'task') {
                    publish($url);
                    //登录验证
                    $prefix = (new \core\lib\config)::get('token_key_prefix', 'app');
                    $check_token = new checkToken($url, $prefix);
                    if ($module == 'admin') {
                        //权限验证
                        $auth = (new \core\lib\app_auth\authAdmin())::checkAuth($url, $module);
                    } elseif($module == 'plugins\goods') {
                        //权限验证-产品
                        $auth = (new \core\lib\app_auth\plugins\authGoods())::checkAuth($url, $module);
                    } elseif($module == 'plugins\assessment') {
                        //权限验证 -绩效
                        $auth = (new \core\lib\app_auth\plugins\authAssessment())::checkAuth($url, $prefix);
                    } elseif($module == 'plugins\checkin') {
                        //权限验证-考勤
                        $auth = (new \core\lib\app_auth\plugins\authCheckin())::checkAuth($url, $prefix);
                    } elseif($module == 'plugins\salary') {
                        //权限验证-薪酬
                        $auth = (new \core\lib\app_auth\plugins\authSalary())::checkAuth($url, $prefix);
                    } elseif($module == 'plugins\shop') {
                        //权限验证-店铺
                        $auth = (new \core\lib\app_auth\plugins\authShop())::checkAuth($url, $prefix);
                    } elseif($module == 'plugins\logistics') {
                        //权限验证-薪酬
                        $auth = (new \core\lib\app_auth\plugins\authLogistics())::checkAuth($url, $prefix);
                    }
                }
            }
            define('API_ROUTE',$url); //请求路由
            include  CORE.'/common/verify.php';
            $ctrl->$action();
        } else {
            throw new \Exception("找不到控制器".$ctrlClass);
        }
    }

    static public function load($class)
    {

        $class = str_replace('\\', '/', $class);
        if (isset(self::$classMap[$class])) {
            return true;
        } else {
            $class_file = SELF_FK.'/'.$class.'.php';
            if (is_file($class_file)) {
                self::$classMap[] = $class;
                include $class_file;
            } else {
                return false;
            }
        }
    }
}
