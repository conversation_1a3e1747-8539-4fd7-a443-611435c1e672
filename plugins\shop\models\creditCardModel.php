<?php

namespace plugins\shop\models;

class creditCardModel extends creditCardBaseModel
{
    // 实体卡特有字段
    public static array $paras_list = [
        // 基础字段
        "card_number"                => "卡号|required",
        "validity_period"            => "有效期|required",
        "security_code"              => "安全码",
        "remark"                     => "备注",
        // 实体卡特有字段
        "activation_bank"            => "开卡银行|required",
        "card_type"                  => "类型|required",
        "cardholder_name"            => "开户人姓名|required",
        "credit_card_status"         => "信用卡状态|required",
        "activation_status"          => "激活情况|required",
        "storage_location"           => "存储位置|required",
        "usage_group"                => "使用对接群",
        "fee_group"                  => "费用对接群",
        "dep_id"                     => "部门ID",
        "job_status"                 => "工作情况",
        "record_keeper"              => "登记负责人ID|required",
        "is_use_self"                => "是否自用|required",
        "annual_fee_amount"          => "年费金额|required",
        "annual_fee_standard"        => "年费达标标准|required",
        "usage_fee_standard"         => "使用费标准|required",
        "annual_fee_deduction_date"  => "扣除年费日期|required",
        "cardholder_receive_account" => "持卡人收款账户|required",
        "city"                       => "城市",
        "billing_date"               => "还款日|required",
        "application_date"           => "提供日期|required",
    ];

    const STATUS_USING = 1; // 正常
    const STATUS_CANCELED = 2; // 已注销
    const STATUS_PENDING_CANCEL = 3; // 待注销
    const STATUS_LOST = 4; // 已挂失
    const STATUS_REISSUE = 5; // 补办中
    const STATUS_PENDING_REISSUE = 6; // 待补办
    const STATUS_WAIT_LOST = 7; // 待挂失
    const STATUS_FEE = 8; //费用申请中

    // 获取卡类型 - 实体卡
    protected function getCardType(): int
    {
        return 1;
    }

    // 获取特定类型的查询条件
    protected function getSpecificListCondition($param)
    {
        // 开户人、登记人
        if (!empty($param['cardholder_name'])) {
            $this->db->andWhere('(cardholder_name LIKE :cardholder_name or u.wname like :cardholder_name)', ['cardholder_name' => '%' . $param['cardholder_name'] . '%']);
        }
        // 开户银行
        if (!empty($param['bank'])) {
            $this->db->andWhere('activation_bank like :activation_bank', ['activation_bank' => $param['bank']]);
        }
        // 激活状态
        if (!empty($param['activation_status'])) {
            $this->db->andWhere('activation_status = :activation_status', ['activation_status' => $param['activation_status']]);
        }
        // 信用卡类型
        if (!empty($param['card_type'])) {
            $this->db->whereIn('card_type', $param['card_type']);
        }
        // 存储位置
        if (!empty($param['storage_location'])) {
            $this->db->andWhere('storage_location like :storage_location', ['storage_location' => '%' . $param['storage_location'] . '%']);
        }
        // 使用对接群
        if (!empty($param['usage_group'])) {
            $this->db->andWhere('usage_group like :usage_group', ['usage_group' => '%' . $param['usage_group'] . '%']);
        }
        // 费用对接群
        if (!empty($param['fee_group'])) {
            $this->db->andWhere('fee_group like :fee_group', ['fee_group' => '%' . $param['fee_group'] . '%']);
        }
        // 部门
        if (!empty($param['dep_id'])) {
            $this->db->whereIn('c.dep_id', $param['dep_id']);
        }
        // 在职情况
        if (!empty($param['job_status'])) {
            $this->db->andWhere('job_status = :job_status', ['job_status' => $param['job_status']]);
        }
        // 是否自用
        if (isset($param['is_use_self']) && $param['is_use_self'] !== '') {
            $this->db->andWhere('is_use_self = :is_use_self', ['is_use_self' => $param['is_use_self']]);
        }
        if (!empty($param['legal_person_shop'])) {
            if ($param['legal_person_shop'] == '是') {
                $this->db->andWhere("lp.type = '自有'");
            } else if ($param['legal_person_shop'] == '否') {
                $this->db->andWhere("lp.type != '自有'");
            }
        }
        // 还款日
        if (!empty($param['billing_date'])) {
            $this->db->andWhere("billing_date = :billing_date", [
                'billing_date' => $param['billing_date']
            ]);
        }
        // 开户日期
        if (!empty($param['application_date'])) {
            $this->db->andWhere('application_date >= :application_date_start and application_date <= :application_date_end', [
                'application_date_start' => $param['application_date'][0],
                'application_date_end'   => $param['application_date'][1],
            ]);
        }
        // 年费扣除日期
        if (!empty($param['annual_fee_deduction_date']) && is_array($param['annual_fee_deduction_date'])) {
            $this->db->andWhere("(CAST(LEFT(annual_fee_deduction_date, LOCATE('月', annual_fee_deduction_date) - 1) AS UNSIGNED) 
            BETWEEN :annual_fee_deduction_date_start AND :annual_fee_deduction_date_end)", [
                'annual_fee_deduction_date_start' => $param['annual_fee_deduction_date'][0],
                'annual_fee_deduction_date_end'   => $param['annual_fee_deduction_date'][1],
            ]);
        }
        // 
        if (!empty($param['is_annual_fee'])) {
            if ($param['is_annual_fee'] == '申请中') {
                $this->db->andWhere('c.fee_id > 0');
            } elseif ($param['is_annual_fee'] == '未申请') {
                $this->db->andWhere('(c.fee_id = 0 or c.fee_id is null)');
            }
        }
        // 用户邮箱
        if (!empty($param['user_email'])) {
            $this->db->andWhere('user_email LIKE :user_email', ['user_email' => '%' . $param['user_email'] . '%']);
        }
    }
}
