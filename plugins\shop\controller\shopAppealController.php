<?php
namespace plugins\shop\controller;

use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use plugins\shop\form\messagesFrom;
use plugins\shop\models\configModel;
use plugins\shop\models\shopAppealModel;
use Exception;
use plugins\shop\models\shopModel;
use plugins\shop\models\userModel;

class shopAppealController extends baseController
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取申诉列表
     */
    public function getList()
    {
        try {
            $param = array_intersect_key($_GET, [
                'shop_number' => '店铺编号',
                'dep_id' => '部门',
                'company_name' => '公司',
                'issue_type' => '问题类型',
                'status' => '申诉状态',
                'shop_status' => '店铺状态',
                'complaint_type' => '申诉类型',
                'follower_name' => '申诉负责人',
                'page' => 1,
                'page_size' => 1
            ]);

            $model = new shopAppealModel();
            
            $data = $model->getList($param);
            returnSuccess($data);
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 创建申诉
     */
    public function apply()
    {
        $paras_list = shopAppealModel::$paras_list;
        $param = array_intersect_key($_POST, array_flip(array_keys($paras_list)));

        try {
            $appealModel = new shopAppealModel();
            $id = $appealModel->add($param);
            $shop = (new shopModel())->getById($param['shop_id']);
            $user_name = userModel::$wname;

            if ($param['complaint_type'] == $appealModel::COMPLAINT_TYPE_SELF) {
                $users = configModel::noticeUser('appeal', 'self', ['dep_id' => $shop['dep_id']]);
                $title = '店铺自申诉';
                $content = "【{$user_name}】提交了【{$shop['shop_number']}】的【{$param['issue_type']}】自申诉";
            } elseif ($param['complaint_type'] == $appealModel:: COMPLAINT_TYPE_SUPPLIER) {
                $users = configModel::noticeUser('appeal', 'supplier', ['dep_id' => $shop['dep_id']]);
                $title = '店铺服务商申诉';
                $content = "【{$user_name}】提交了【{$shop['shop_number']}】的【{$param['issue_type']}】服务商申诉，请您及时配置跟进人";
            }
            if (!empty($users)) {
                messagesFrom::senMeg($users, 1 , $content, $id, '', $title);
            }
            returnSuccess(['id' => $id], '申诉成功');
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 撤销申请
     */
    public function cancel()
    {
        try {
            $id = intval($_POST['id'] ?? 0);
            if (!$id) {
                returnError('参数错误');
            }

            $model = new shopAppealModel();

            $model->cancel($id);
            returnSuccess();
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 配置跟进人
     */
    public function assignFollower()
    {
        try {
            $id = intval($_POST['id'] ?? 0);
            $follower_id = intval($_POST['follower_id'] ?? 0);
            $remark = $_POST['remark'] ?? '';
            if (!$id || !$follower_id) {
                returnError('参数错误');
            }

            $model = new shopAppealModel();

            $model->assignFollower($id, $follower_id, $remark);
            returnSuccess();
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 跟进
     */
    public function follow()
    {
        $paras_list = ['id', 'is_end', 'appeal_file', 'appeal_result', 'remark'];
        $param = array_intersect_key($_POST, array_flip($paras_list));
        try {

            $model = new shopAppealModel();
            
            $model->follow($param['id'], $param);
            returnSuccess();
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 处理申诉
     */
    public function process()
    {
        $paras_list = ['id', 'appeal_supplier', 'appeal_fee', 'pay_date', 'phone_number', 'appeal_file', 'remark'];
        $param = array_intersect_key($_POST, array_flip($paras_list));
        try {

            $model = new shopAppealModel();

            $model->process($param['id'], $param);
            returnSuccess();
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 提醒
     */
    public function remind()
    {
        try {
            $id = intval($_POST['id'] ?? 0);
            if (!$id) {
                returnError('参数错误');
            }

            $model = new shopAppealModel();
            returnSuccess();
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 获取详情
     */
    public function getDetail()
    {
        try {
            $id = intval($_GET['id'] ?? 0);
            if (!$id) {
                returnError('参数错误');
            }

            $model = new shopAppealModel();

            $data = $model->getDetail($id);
            if (!$data) {
                returnError('申诉记录不存在');
            }

            returnSuccess($data);
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    // 获取日志
    public static function getLog()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $db = dbShopMysql::getInstance();
        $list = $db->table('operation_log')->where('where table_name = :table_name and table_id = :table_id',
            ['table_name' => 'shop_appeal', 'table_id' => $id])->order('id desc')->list();

        $users =  redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        $model = new shopAppealModel();
        $maps = $model::getMaps();

        foreach ($list as &$item) {
            $item['operator_name'] = $users[$item['operator']] ?? '';
            $attach = json_decode($item['attach'], true);
            $item['before'] = $attach['before'] ?? null;
            $item['before'] = $model->formatItem($item['before'], $maps);
            $item['after'] = $attach['after'] ?? null;
            $item['after'] = $model->formatItem($item['after'], $maps);
            $item['appeal_file'] = $attach['appeal_file'] ?? null;
            unset($item['attach']);
        }
        returnSuccess($list);
    }
}
