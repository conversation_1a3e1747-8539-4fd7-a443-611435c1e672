<?php

namespace admin\form;

use admin\models\userModel;
use core\lib\db\dbMysql;

class userForm
{
    //拉取用户列表
    public static function getUserList($param)
    {
        $db = dbMysql::getInstance();
        $user_ids = [];
        if (is_array($param['roles_id']) && !empty($param['roles_id']) && count($param['roles_id'])) {
            $user_ids = [];
            $user_roles = $db->table('sys_user_roles')
                ->where('where is_delete = 0')
                ->whereIn('role_id',$param['roles_id'])
                ->field('user_id')
                ->list();
            if (count($user_roles)) {
                $user_ids = array_column($user_roles,'user_id');
            }
        }

        $db->table('qwuser','a')
            ->leftJoin('user','b','b.qw_id = a.id')
            ->leftJoin('sys_user_mac','c','c.qw_userid = a.id')
            ->field('a.id,a.wid,a.wname,a.wphone,a.wdepartment_ids,a.wstatus,a.updated_at,a.is_manage,a.avatar,a.position,b.status,c.mac')
            ->where('(b.is_delete != 1 OR b.is_delete IS NULL)');
        $order_str = '';
        if (!empty($param['order_by'])) {
            $order_by = json_decode($param['order_by'],true);
            $order_str = '';
            foreach ($order_by as $k=>$ord){
                $order_str .= 'a.'.$k.' '.$ord.',';
            }
        }
        if(empty($order_str)) {
            $db->order('a.id desc');
        } else {
            $db->order(trim($order_str,','));
        }
        if (!empty($param['wname'])) {
            $db->andWhere('and a.wname like :wname',['wname'=>'%'.$param['wname'].'%']);
        }
        if (!empty(trim($param['qw_partment_id']))) {
            $qw_partment_ids = explode(',',$param['qw_partment_id']);
            $db->andWhere('and JSON_CONTAINS(a.wdepartment_ids,:qw_partment_id)',['qw_partment_id'=>json_encode($qw_partment_ids,JSON_NUMERIC_CHECK)]);
        }
        if (!empty($param['wstatus'])) {
            $db->andWhere('a.wstatus = :wstatus',['wstatus'=>$param['wstatus']]);
        }
        if (!empty($param['status'])) {
            //1未激活2启用3禁用
            if ($param['status'] == 1){
                $db->andWhere('(b.status = 1 OR b.status IS NULL)');
            }
            if ($param['status'] == 2){
                $db->andWhere('b.status = 2');
            }
            if ($param['status'] == 3){
                $db->andWhere('b.status = 3');
            }
        }
        if (count($user_ids)) {
            $db->whereIn('a.id',$user_ids);
        }
        $data = $db->pages($param['page'], $param['page_size']);
        $data['list'] = userModel::getUserListRole($data['list']);
        return $data;
    }
    //激活用户
    public static function activeUser($param)
    {
        $db = dbMysql::getInstance();
        //获取qw信息
        $qw_user_info = $db->table('qwuser')
            ->where('id = :id',['id'=>$param['qw_userid']])
            ->one();
        //先去判断该用户是否已经激活
        $user_info = $db->table('user')
            ->where('qw_id = :qw_id',['qw_id'=>$param['qw_userid']])
            ->one();
        if (!empty($user_info)){
            if ($user_info['status'] != 1) {
                returnError('用户已激活');
            }
        }
        //处理密码
        $unique_id = getuniqId('oa');
        $pwd = getPwdMd5($param['password'],$unique_id);
        //处理角色
        $role_ids = json_decode($param['role_ids'],true);
        //开启事务
        $db->beginTransaction();
        //先创建用户
        $db->table('user')
            ->insert([
                'name'=>$qw_user_info['wname'],
                'qw_userid'=>$qw_user_info['wid'],
                'qw_id'=>$param['qw_userid'],
                'uniqueid'=>$unique_id,
                'pwd'=>$pwd,
                'status'=>2,
                'created_at' => time(),
                'updated_at' => time()
            ]);
        //为用户添加角色
        foreach ($role_ids as $role_id){
            $db->table('sys_user_roles')
                ->insert([
                    'qwuser_id'=>$param['qw_userid'],
                    'role_id'=>$role_id,
                    'status'=>1,
                    'created_at' => date('Y-m-d H:i:s'),
                ]);
        }
        $db->commit();
    }
    //禁用启用用户
    public static function changeUserStatus($param)
    {
        $db = dbMysql::getInstance();

        // 查询当前用户状态，表中使用 qw_userid 作为唯一标识
        $userData = $db->table('user')
            ->where('qw_id = :qw_id', ['qw_id' => $param['qw_userid']])
            ->field('status')
            ->one();

        if (empty($userData)) {
            returnError('未找到用户');
        }

        if ($userData['status'] == 2) {
            $newStatus = 3;
        } elseif ($userData['status'] == 3) {
            $newStatus = 2;
        }


        // 更新用户状态及更新时间（使用时间戳）
        $db->table('user')
            ->where('qw_id = :qw_id', ['qw_id' => $param['qw_userid']])
            ->update([
                'status'     => $newStatus,
                'updated_at' => time()
            ]);
    }
    //修改密码
    public static function changePwd($param)
    {
        $db = dbMysql::getInstance();
        //先去查询用户的$unique_id
        $unique_id = $db->table('user')
            ->where('qw_id = :qw_id', ['qw_id' => $param['qw_userid']])
            ->field('uniqueid')
            ->one();
        $unique_id = $unique_id['uniqueid'];
        $pwd = getPwdMd5($param['password'],$unique_id);
        $db->table('user')
            ->where('qw_id = :qw_id', ['qw_id' => $param['qw_userid']])
            ->update([
                'pwd'     => $pwd,
                'uniqueid'=>$unique_id,
                'updated_at' => time()
            ]);
    }
    //个人修改密码
    public static function updateUserPwd($param){
        $db = dbMysql::getInstance();
        //先去查询用户的$unique_id
        $unique_id = $db->table('user')
            ->where('qw_id = :qw_id', ['qw_id' => $param['qw_userid']])
            ->field('uniqueid')
            ->one();
        $unique_id = $unique_id['uniqueid'];
        $pwd = getPwdMd5($param['password'],$unique_id);
        $db->table('user')
            ->where('qw_id = :qw_id', ['qw_id' => $param['qw_userid']])
            ->update([
                'pwd'     => $pwd,
                'uniqueid'=>$unique_id,
                'updated_at' => time()
            ]);
    }
    //管理mac地址
    public static function manageMac($param)
    {
        $db = dbMysql::getInstance();

        // 检查指定 qw_id 是否存在记录
        $record = $db->table('sys_user_mac')
            ->where('qw_userid =:qw_userid', ['qw_userid'=>$param['qw_id']])
            ->one();
        if ($record) {
            // 存在记录时更新 mac 字段
            $updateData = [
                'mac' => $param['mac_ids'],  // MAC地址集合
            ];
            $db->table('sys_user_mac')
                ->where('qw_userid =:qw_userid', ['qw_userid'=>$param['qw_id']])
                ->update($updateData);
        } else {
            // 不存在记录时新建数据
            $insertData = [
                'qw_userid' => $param['qw_id'],  // 企业微信用户ID
                'mac'    => $param['mac_ids'] // MAC地址集合
            ];
            $db->table('sys_user_mac')
                ->insert($insertData);
        }
    }
    //详情
    public static function getUserdetails($param)
    {
        $db = dbMysql::getInstance();
        $data = $db->table('qwuser','a')
            ->leftJoin('user','b','b.qw_userid = a.id')
            ->leftJoin('sys_user_mac','c','a.id = c.qw_userid')
            ->where('a.id =:id',['id'=>$param['qw_userid']])
            ->field('a.id,a.wname,a.wphone,a.wstatus,b.status,c.mac')
            ->one();
        $data['mac'] = json_decode($data['mac'],true);
        return $data;
    }
    //获取常用用户
    public static function setOftenUsedUser($param)
    {
        if (!empty($param['qwuser_ids'])) {
            $qwuser_ids = $need_add_ids = json_decode($param['qwuser_ids']);
            $db = dbMysql::getInstance();
            //查询这些用户是否在数据中
            $db->table('user_often_used');
            $db->where('where qwuser_id=:qwuser_id', ['qwuser_id' => \plugins\assessment\models\userModel::$qwuser_id]);
            $db->field('used_qwuser_id');
            $db->whereIn('used_qwuser_id', $qwuser_ids);
            $old_user = $db->list();
            //修改
            $db->update(['updated_at' => time()]);

            if (count($old_user)) {
                $old_used_qwuser_ids = array_column($old_user, 'used_qwuser_id');
                $need_add_ids = array_diff($qwuser_ids, $old_used_qwuser_ids);
            }
            if (count($need_add_ids)) {
                $insert_sql = 'insert into oa_user_often_used (qwuser_id,used_qwuser_id,updated_at) values ';
                $insert_data = ['qwuser_id' => userModel::$qwuser_id, 'updated_at' => time()];
                foreach ($need_add_ids as $k => $v) {
                    $insert_sql .= "(:qwuser_id,:used_qwuser_id_$k,:updated_at),";
                    $insert_data["used_qwuser_id_$k"] = $v;
                }
                $insert_sql = trim($insert_sql, ',');
                $db->query($insert_sql, $insert_data);
            }
            returnSuccess('', '');
        }
    }


}