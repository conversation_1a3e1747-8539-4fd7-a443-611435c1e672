<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/28 10:46
 */

namespace financial\controller;

use core\jobs\costSharingJobs;
use core\lib\config;
use financial\form\costSharingForm;
use financial\models\checkoutModel;
use financial\models\costSharingModel;
use Rap2hpoutre\FastExcel\FastExcel;

class costSharingController
{
    //列表获取
    public function getList() {
        $paras_list = array('date_array','user_id','status','data_status','page_size','page');
        $request_list = ['status'=>'分摊状态','date_array'=>'时间段'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        returnSuccess(costSharingForm::getList($param));
    }
    //下载模板 模板
    public function downLoadTeml() {
        $paras_list = array('dimension');
        $request_list = ['dimension'=>'分摊维度'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        if (empty($param['dimension']) || $param['dimension'] == '[]') {
            returnError('请选择分摊维度');
        }
        $dimension = json_decode($param['dimension']);
        $url = costSharingForm::downLoadTpl($dimension);
        returnSuccess(['url'=>$url]);
    }
    //费用上传
    public function shareExpenses() {
        $paras_list = array('m_date','dimension','excel_name','excel_src');
        $request_list = ['m_date'=>'分摊时间','dimension'=>'分摊维度','excel_name'=>'表格名称','excel_path'=>'表格连接'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $excel_url = SELF_FK.$param['excel_src'];
        if (empty($param['dimension']) || $param['dimension'] == '[]') {
            returnError('请选择分摊维度');
        }
        $m_date = date('Y-m',strtotime($param['m_date']));
        $dimension = json_decode($param['dimension']);
        //验证数据是否可以上传
        checkoutModel::verifyLock([$m_date]);
        //验证是否有分摊在跑
        costSharingForm::verifyQueue();
        //获取表数据
        $excel_data = (new FastExcel())->import($excel_url);
        $data = $excel_data->toArray();
        $count_num = count($data);
        if (!$count_num) {
            returnError('表格内不存在数据');
        }
        if ($count_num > 10) {
            returnError('表格单次限制导入数据10行');
        }
        //验证表头和表数据
        $data = costSharingModel::verifyExcel($data,$dimension,$m_date);
        //保存表头和表数据
        $import_data = costSharingForm::saveShareExcel($data,$param['excel_src'],$param['excel_name'],$m_date,$dimension);
        //异步分摊
        costSharingForm::setTask($import_data['import_data_id'],[$import_data['import_id']],$m_date);
        returnSuccess('','正在分摊计算');
    }
    //作废
    public function abolishShare() {
        $paras_list = array('data_ids');
        $request_list = ['data_ids'=>'数据id'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        if ($param['data_ids'] == '[]') {
            returnError('请选择要作废的数据');
        }
        //验证数据是否可以上传
        costSharingForm::verifyQueue();
        //获取可作废的数据(验证是否可做费)
        $list = costSharingForm::getAllowList(json_decode($param['data_ids']),1);
        if (!count($list)) {
            returnError('你选择的数据没有满足可废除的数据');
        }
        $ids = array_column($list,'id');
        $import_ids = array_unique(array_column($list,'import_id'));
        costSharingForm::setAbolishTask($ids,$import_ids,1);
        returnSuccess('','正在废除');

    }
    //更新计算(根据每列的id)
    public function shareAgainByid() {
        $paras_list = array('data_ids');
        $request_list = ['data_ids'=>'数据id'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        if ($param['data_ids'] == '[]') {
            returnError('请选择要重新计算的数据');
        }
        $data_ids = json_decode($param['data_ids']);
        //验证是否有分摊在跑
        costSharingForm::verifyQueue();
        //重新整理计算数据（获取可更新的数据）
        //获取可作废的数据(验证是否可做费)
        $list = costSharingForm::getAllowList($data_ids,2);
        if (!count($list)) {
            returnError('你选择的数据没有满足可重新计算除的数据');
        }
        //进入任务
        $ids = array_column($list,'id');
        $import_ids = array_unique(array_column($list,'import_id'));
        costSharingForm::setAbolishTask($ids,$import_ids,2);
        returnSuccess('','正在重新分摊计算');
    }
    //导出
    public function export() {
        $paras_list = array('date_array','user_id','status','data_status','import_ids','import_data_ids','type','export_list');
        $request_list = ['status'=>'分摊状态','date_array'=>'时间段'];
        $param = arrangeParam($_POST, $paras_list,$request_list);
        $zip_path = costSharingForm::export($param);
        returnSuccess(['path'=>$zip_path]);
    }
}