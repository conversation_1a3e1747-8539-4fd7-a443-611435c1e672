<?php

namespace plugins\checkin\models;

use core\lib\db\dbCMysql;

class approvelModel
{
    public static int $id;
    public static string $sp_no;
    public static string $sp_name;
    public static int $sp_status; // 申请单状态：1-审批中；2-已通过；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付
    public static string $qw_user_id; // 申请人
    public static string $template_id; // 审批模板id
    public static int $apply_time; // 申请时间
    public static string $applyer; // 申请人信息
    public static string $batch_applyer; // 批量申请人信息
    public static string $sp_record; // 审批流程信息
    public static string $notifyer; // 抄送信息
    public static string $apply_data; // 审批申请数据
    public static string $comments; // 审批申请备注信息
    public static string $ori_data; // 审批申请备注信息

    private $user_template_id = [
        "3TmmciPWC9yqKH3PEaPKW5nkTYp899ovm2QxsaCx", // 成都请假
        "Bs7uczbiSALR5afcNsZQLCRUa1JbwGhA5SizQj5Z2", // 深圳请假
        "3TmACnDrGDY8ipW91ssqnarVuLZ5pXX2PdqekCaf", // 成都加班
        "C4RYyf8DKd8a9ftbEhCENBkWooDxVid34g6UooTDb", // 深圳加班
        "BsAbKmQWjXVNbNuGNqg6XrwDa3sGar639NyUFMU5d", // 补卡
    ];


    // 格式化审批单
    public static function formatApprovel($list)
    {
        $user_approvel = [];
        $user_mend_card = [];
        $user_absence = [];
        $vacation = [];
        foreach ($list as &$item) {
            $item['apply_data'] = json_decode($item['apply_data'], true);
            switch ($item['template_id']) {
                case '3TmACnDrGDY8ipW91ssqnarVuLZ5pXX2PdqekCaf': // 成都加班
                case 'C4RYyf8DKd8a9ftbEhCENBkWooDxVid34g6UooTDb': // 深圳加班
                    $start = 0; // 加班开始时间
                    $end = 0; // 加班结束时间
                    $duration = 0; // 加班时长
                    $perday_duration = 0; // 每日时长

                    foreach ($item['apply_data']['contents'] as $content) {
                        if ($content['control'] == 'DateRange') {
                            $start = $content['value']['date_range']['new_begin'];
                            $end = $content['value']['date_range']['new_end'];
                            $duration = $content['value']['date_range']['new_duration'];
                            $perday_duration = $content['value']['date_range']['perday_duration'];
                            break;
                        }
                    }
                    if (strtotime(date('Y-m-d', $start)) != strtotime(date('Y-m-d', $end))) { // 不是同一天
                        while ($start <= $end) {
                            $date = date('Y-m-d', $start);
                            $item['overtime_date'] = $date;
                            // 最后一天用结束时间计算
                            $new_end = ($start + 86400 > $end) ? $end : $start + 86400;

                            $item['overtime_duration'] = $new_end - $start;
                            $item['overtime_perday_duration'] = $perday_duration;
                            $item['overtime_day'] = ($new_end - $start) / $perday_duration;
                            // 不是整天加班，需要判断是上午加班还是下午加班
                            if ($item['overtime_day'] != 1) {
                                $item['overtime_type'] = $new_end == strtotime($date) ? 1 : 2; // 1-上午加班；2-下午加班
                            }
                            // 拼接用户加班审批单
                            $key = $item['qw_user_id'] . '-' . $date;
                            if (!isset($user_approvel[$key])) $user_approvel[$key] = [];
                            $user_approvel[$key][] = $item;
                            $start = strtotime($date) + 86400;
                        }
                    }
                    else {
                        $date = date('Y-m-d', $start);
                        $item['overtime_date'] = $date;
                        $item['overtime_duration'] = $duration;
                        $item['overtime_perday_duration'] = $perday_duration;
                        $item['overtime_day'] = $duration / $perday_duration;
                        // 不是整天加班，需要判断是上午加班还是下午加班
                        if ($item['overtime_day'] != 1) {
                            $item['overtime_type'] = $end == strtotime($date) ? 1 : 2; // 1-上午加班；2-下午加班
                        }
                        // 拼接用户加班审批单
                        $key = $item['qw_user_id'] . '-' . $date;
                        if (!isset($user_approvel[$key])) $user_approvel[$key] = [];
                        $user_approvel[$key][] = $item;
                    }
                    break;
                case 'BsAbKmQWjXVNbNuGNqg6XrwDa3sGar639NyUFMU5d': // 补卡
                    foreach ($item['apply_data']['contents'] as $content) {
                        if ($content['control'] == 'PunchCorrection') {
                            $checkin_time = $content['value']['punch_correction']['time'];
                            $item['checkin_date'] = date('Y-m-d', $checkin_time);
                            if (strpos($content['value']['punch_correction']['state'], '上班') !== false) {
                                $checkin_type = 1;
                            } elseif (strpos($content['value']['punch_correction']['state'], '下班') !== false) {
                                $checkin_type = 2;
                            }
                            if (!isset($checkin_type)) {
                                // TODO 目前监测到老的审批单，组件取不到数据，先不予处理
                                break;
                            }
                            $item['checkin_type'] = $checkin_type;
                            // 拼接用户补卡审批单
                            $key = $item['qw_user_id'] . '-' . $item['checkin_date'];
                            if (!isset($user_mend_card[$key])) $user_mend_card[$key] = [];
                            $user_mend_card[$key][] = $item;
                            break;
                        }
                    }
                    break;
                case '3TmmciPWC9yqKH3PEaPKW5nkTYp899ovm2QxsaCx': // 成都请假
                case 'Bs7uczbiSALR5afcNsZQLCRUa1JbwGhA5SizQj5Z2': // 深圳请假
                    $start = 0; // 请假开始时间
                    $end = 0; // 请假结束时间
                    $duration = 0; // 请假时长
                    $perday_duration = 0; // 每日时长
                foreach ($item['apply_data']['contents'] as $content) {
                    if ($content['control'] == 'Vacation') {
                        $vacation_name = $content['value']['vacation']['selector']['options'][0]['value'][0]['text'];
                        $item['vacation_id'] = $content['value']['vacation']['selector']['options'][0]['key'];
                        $item['vacation_name'] = $vacation_name;
                        // 把所有请假的假期存下来
                        $vacation[$item['vacation_id']] = [
                            'vacation_id' => $item['vacation_id'],
                            'vacation_name' => $vacation_name,
                        ];

                        $start = $content['value']['vacation']['attendance']['date_range']['new_begin'];
                        $end = $content['value']['vacation']['attendance']['date_range']['new_end'];
                        $duration = $content['value']['vacation']['attendance']['date_range']['new_duration'];

                        if (strtotime(date('Y-m-d', $start)) != strtotime(date('Y-m-d', $end))) { // 请假不是同一天
                            while ($start <= $end) {
                                $date = date('Y-m-d', $start);
                                $item['checkin_date'] = $date;
                                // 最后一天用结束时间计算
                                $new_end = (($start + 86400) > $end) ? $end : ($start + 86400);
                                $item['absence_start'] = $start;
                                $item['absence_end'] = $new_end;
                                $item['absence_duration'] = $new_end - $start;


                                // 拼接用户请假审批单
                                $key = $item['qw_user_id'] . '-' . $item['checkin_date'];
                                if (!isset($user_absence[$key])) $user_absence[$key] = [];
                                $user_absence[$key][] = $item;
                                $start = strtotime($date) + 86400;
                            }
                        }
                        else {
                            $date = date('Y-m-d', $start);
                            $item['checkin_date'] = $date;
                            $item['absence_duration'] = $duration;
                            $item['absence_start'] = $start;
                            $item['absence_end'] = $start + 86400;
                            // 拼接用户请假审批单
                            $key = $item['qw_user_id'] . '-' . $date;
                            if (!isset($user_absence[$key])) $user_absence[$key] = [];
                            $user_absence[$key][] = $item;

                        }
                        break;
                    }
                }
                    break;
            }

        }
        return [
            'list' => $list,
            'user_approvel' => $user_approvel,
            'user_mend_card' => $user_mend_card,
            'user_absence' => $user_absence,
            'user_vacation' => $vacation
        ];
    }

}