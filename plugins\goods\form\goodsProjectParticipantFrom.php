<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/15 11:59
 */

namespace  plugins\goods\form;

use plugins\goods\models\userModel;
use core\lib\db\dbMysql;

class goodsProjectParticipantFrom
{
    /**
     * @param $tpl_data
     * @param $goods_id
     * @param $project_id
     * @return void
     * @throws \core\lib\ExceptionError   设置产品流程相关人员
     */
    public static function setParticipant($tpl_data,$goods_id,$project_id){
        $db = dbMysql::getInstance();
        $ids = [];
        foreach ($tpl_data as $v) {
            foreach ($v as $v2) {
                //节点
                $node_manage = json_decode($v2['manage_info'],true);
                $ids = array_merge(array_column($node_manage,'id'),$ids);
                foreach ($v2['event_detail'] as $event_list) {
                    foreach ($event_list as $event) {
                        $event_manage = json_decode($event['manage_info'],true);
                        $ids = array_merge(array_column($event_manage,'id'),$ids);
                    }
                }
            }
        }
        $ids = array_unique($ids);
        $db->table('goods_project_participant');
        $db->where('where goods_id=:goods_id and project_id=:project_id',['goods_id'=>$goods_id,'project_id'=>$project_id]);
        $list = $db->list();
        $old_ids = array_column($list,'qwuser_id');
        $new_ids = array_diff($ids,$old_ids);
        if (count($new_ids)) {
            $db->table('goods_project_participant');
            foreach ($new_ids as $v) {
                $db->insert([
                    'goods_id'=>$goods_id,
                    'project_id'=>$project_id,
                    'qwuser_id'=>$v,
                    'created_time'=>date('Y-m-d H:i:s'),
                ]);
            }
        }
    }

    /**
     * @param $goods_id
     * @return void  查询此用户是否为产品流程参与人   不可循环调用
     */
    public static function isProjectParticipant($goods_id) {
        $db = dbMysql::getInstance();
        $db->table('goods_project_participant');
        $db->where('where goods_id=:goods_id and qwuser_id=:qwuser_id',['goods_id'=>$goods_id,'qwuser_id'=>userModel::$qwuser_id]);
        if ($db->count()) {
            return 1;
        } else {
            return 0;
        }
    }
}