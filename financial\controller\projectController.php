<?php

namespace financial\controller;


use core\lib\db\dbFMysql;
use financial\form\projectForm;
use financial\models\userModel;
use Rap2hpoutre\FastExcel\FastExcel;
use core\lib\db\dbMysql;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;

require 'vendor/autoload.php'; // Composer 自动加载文件



class projectController
{
    public static function getList()
    {
        // 定义所需参数列表
        $paras_list = array('project_name','type');
        // 组织参数
        $param = arrangeParam($_POST, $paras_list);
        // 获取数据库实例
        $db = dbFMysql::getInstance();
        $dbs = dbMysql::getInstance();
        // 初始化结果数组
        $projectlist = [];
        // 否则获取所有项目数据
        $db->table('project')
            ->where('is_delete = 0');
        if (!empty($param['project_name'])) {
            $db->andWhere("(level in ('1','2') or (level = 3 and project_name like :project_name))",['project_name'=>'%' .$param['project_name']. '%']);
        }
        $projects = $db
            ->order('id asc,Project_Code asc')
            ->list();
        //用户id获取
        if (count($projects)) {
            $user_ids = [];
            foreach ($projects as $k=>$v) {
                //运营
                if (!empty($v['user_ids'])) {
                    $user_ids_ = explode(',',trim($v['user_ids'],','));
                    $user_ids = array_merge($user_ids,$user_ids_);
                } else {
                    $user_ids_ = [];
                }
                $projects[$k]['user_ids'] = $user_ids_;
                //运营组长关系
                if (!empty($v['Leader'])) {
                    $Leader = json_decode($v['Leader'],true);
                    foreach ($Leader as $k2=>$v2) {
                        $user_ids[] = $k2;
                        $user_ids = array_merge($user_ids,$v2);
                    }
                } else {
                    $Leader = [];
                }
                //负责人
                $user_ids[] = $v['user_id'];
                //其他负责人
                $other_user_ids = json_decode($v['other_user_ids']);
                $user_ids = array_merge($user_ids,$other_user_ids);
                $projects[$k]['other_user_ids'] = $other_user_ids;
                $projects[$k]['Leader'] = $Leader;
            }
            $user_ids = array_unique($user_ids);
            $user_list = $dbs->table('qwuser')
                ->whereIn('id',$user_ids)
                ->field('id,avatar,wname')
                ->list();
            $user_l = [];
            foreach ($user_list as $v) {
                $user_l[$v['id']] = $v;
            }
            //项目数据整理
            foreach ($projects as $k=>$project) {
                if ($project['user_id']) {
                    $project['user'] = $user_l[$project['user_id']]?[$user_l[$project['user_id']]]:[];
                }else{
                    $project['user'] = [];
                }
                if (count($project['user_ids'])) {
                    // 处理 user_ids
                    $userIds = array_flip($project['user_ids']);
                    $project['user_ids'] = array_values(array_intersect_key($user_l,$userIds));
                } else {
                    $project['user_ids'] = [];
                }
                //其他负责人
                $project['other_fz_user'] = array_values(array_intersect_key($user_l,array_flip($project['other_user_ids'])));
                //处理组长组员
                if (count($project['Leader'])) {
                    $leader = [];
                    foreach ($project['Leader'] as $k2=>$v2) {
                        if (isset($user_l[$k2])) {
                            $item_ = $user_l[$k2];
                            $user_l_ids = array_flip($v2);
                            $item_['childs'] = array_values(array_intersect_key($user_l,$user_l_ids));
                            $leader[] = $item_;
                        }
                    }
                    $project['Leader'] = $leader;
                }
                $projects[$k] = $project;
            }

            $projects = self::buildTree($projects, 0);

            $projects = self::sortInnerChildrenByCode($projects);

            returnSuccess($projects, "获取项目列表成功");
        }
    }
    /**
     * 对最里层的 children 按照 Project_Code 进行排序
     *
     * @param array $data 项目数据
     * @return array 排序后的项目数据
     */
    public static function sortInnerChildrenByCode($data)
    {
        // 对每个第一层项目进行遍历
        foreach ($data as &$firstLevelProject) {
            // 对每个第二层项目进行遍历
            if (!empty($firstLevelProject['children'])) {
                foreach ($firstLevelProject['children'] as &$secondLevelProject) {
                    // 对每个第三层项目的 children 按照 Project_Code 排序
                    if (!empty($secondLevelProject['children'])) {
                        usort($secondLevelProject['children'], function($a, $b) {
                            // 根据 Project_Code 字段进行排序，空的 Project_Code 也会被处理
                            $a_code = isset($a['Project_Code'])? intval($a['Project_Code']) : 0;
                            $b_code = isset($b['Project_Code'])? intval($b['Project_Code']) : 0;
                            if ($a_code < $b_code) {
                                return -1;
                            } elseif ($a_code > $b_code) {
                                return 1;
                            } else {
                                return 0;
                            }
                        });
                    }
                }
            }
        }

        return $data; // 返回排序后的数据
    }

    //构建树状结构的函数
    private static function buildTree(array &$projects, $parentId = 0)
    {
        $tree = [];
        foreach ($projects as $key => &$project) {
            if ($project['p_id'] == $parentId) {
                // 递归查找子项目
                $children = self::buildTree($projects, $project['id']);
                if ($children) {
                    $project['children'] = $children;
                }
                $tree[] = $project;
                // 从原始数组中移除已经处理的项目
                unset($projects[$key]);
            }
        }
        return $tree;
    }
    // 新增
    public static function add()
    {
        // 定义所需参数列表
        $paras_list = ['p_id', 'project_name', 'user_id', 'level', 'Project_Code','user_ids'];
        $request_list = ['project_name' => '项目名称', 'level' => '项目级别'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $db = dbFMysql::getInstance();
        //名字重复验证
        $p_id = (int)$param['p_id']??0;
        $level = (int)$param['level']??0;
        //项目等级验证
        if ($p_id == 0) {
            if ($level != 1) {
                returnError('p_id和level不匹配');
            }
        } else {
            $last_project = $db->table('project')
                ->where('id = :id',['id'=>$p_id])
                ->one();
            if (!$last_project) {
                returnError('未找到上级数据');
            }
            if ($last_project['level'] != ($level - 1)) {
                returnError('p_id和level不匹配');
            }
        }
        self::varifyProjectName($db,0,$p_id,$param['project_name'],$param['level']);
        // 验证项目级别为3时需要提供项目编号和项目项目负责人
        $user_ids = $param['user_ids']??'[]';
        $user_ids = json_decode($user_ids);
        if ($param['level'] == 3) {
            if (empty($param['user_id'])) {
                returnError("请选择项目负责人");
            }
            if (count($user_ids) > 3) {
                returnError('其他负责人不得大于3个');
            }
            //项目编码验证
            if (empty($param['Project_Code'])) {
                returnError('项目编码必传');
            }
            self::varifyProjectCode($db,0,$param['Project_Code']);
        }
        // 新增项目
        $id = $db->table('project')->insert([
            'p_id'=>$p_id,
            'level'=>$level,
            'project_name'=>$param['project_name'],
            'user_id'=>$param['user_id']??0,
            'other_user_ids'=>json_encode($user_ids),
            'created_time'=>date('Y-m-d H:i:s'),
            'Project_Code'=>$param['Project_Code']??'',
        ]);

        returnSuccess([], "新增项目成功，项目ID为 {$id}");
    }
    //修改
    public static function update()
    {
        $paras_list = [
            'id', 'project_name', 'user_id','Project_Code','user_ids'
        ];
        $request_list = [
            'id' => '项目ID',
            'project_name' => '项目名称'
        ];
        // 组织参数
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $db = dbFMysql::getInstance();
        $project = $db->table('project')
            ->where('where id = :id', ['id' => $param['id']])
            ->field('Project_Code')
            ->one();
        if (!$project) {
            returnError('未找到要更新的记录');
            return;
        }
        //名字重复验证
        self::varifyProjectName($db,$project['id'],$param['p_id'],$param['project_name'],$project['level']);
        //项目编验证
        if ($project['level'] == 3) {
            if (empty($param['Project_Code'])) {
                returnError('项目编码必传');
            }
            self::varifyProjectCode($db,$project['id'],$param['Project_Code']);
        }
        $user_ids = $param['user_ids']??'[]';
        $user_ids = json_decode($user_ids);
        if ($project['level'] == 3 ){
            if (!($param['Project_Code'])){
                returnError("操作失败: 项目级别为3时必须提供项目编号");
            }
            if (count($user_ids) > 3) {
                returnError('其他负责人不得大于3个');
            }
        }
        // 构建更新数据，过滤掉没有值的字段
        $updateData = [
            'project_name'=>$param['project_name'],
            'Project_Code'=>$param['Project_Code']??'',
            'user_id'=>$param['user_id']??0,
            'other_user_ids'=>json_encode($user_ids)
        ];
         $db->table('project')
            ->where('where id = :id', ['id' => $param['id']])
            ->update($updateData);
         returnSuccess('','修改成功');
    }
    //下载导入模板
    public static function downloadTemplate()
    {
        try {
            // 使用项目根目录动态构建模板路径
            $projectRoot = dirname(__DIR__, 2); // 假设控制器位于 'project_root/financial/controller' 目录下
            $templatePath = $projectRoot . '/public_financial/template';

            // 检查并创建文件夹
            $exportDir = dirname($templatePath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            if (file_exists($templatePath)) {
                returnSuccess(['download_path' => $templatePath], '模板下载路径获取成功');
            } else {
                returnError('模板文件不存在');
            }
        } catch (\Exception $e) {
            returnError('获取模板下载路径失败: ' . $e->getMessage());
        }
    }
    //上传并处理导入模板接口
    public static function uploadTemplate()
    {
        $paras_list = ['excel_src'];
        $request_list = ['excel_src' => '导入表格地址'];

        // 组织参数
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $excel_url = SELF_FK . $param['excel_src'];

        // 检查 Excel 文件是否存在
        if (!file_exists($excel_url)) {
            returnError('表格不存在');
        }

        // 使用 FastExcel 库导入 Excel 文件内容
        $excel_data = (new FastExcel)->import($excel_url);
        $data = $excel_data->toArray();

        // 检查 Excel 文件是否为空
        if (empty($data)) {
            returnError('表格内不存在数据');
        }

        // 验证 Excel 文件的表头
        $required_columns = ['项目编码', '一级', '二级', '项目名称', '项目负责人'];
        foreach ($required_columns as $column) {
            if (!array_key_exists($column, $data[0])) {
                returnError('表头错误: 缺少 ' . $column);
            }
        }

        // 检查数据出现的错误
        $errors = projectForm::importExcelData($data);

        if (!empty($errors['failed_data']) && empty($errors['successful_data'])) {
            returnSuccess($errors);
        }

        $data = $errors['successful_data'];
        $error = $errors['failed_data'];
        $link = $errors['failed_file_download_path'] ?? 'default_value';

        // 获取数据库实例
        $dbf = dbFMysql::getInstance();
        $dby = dbMysql::getInstance();
        $currentTime = date('Y-m-d H:i:s');

        foreach ($data as $row) {
            $projectCode = $row['项目编码'] ?? null;
            $level1 = $row['一级'] ?? null;
            $level2 = $row['二级'] ?? null;
            $projectName = $row['项目名称'] ?? null;
            $responsible = trim($row['项目负责人'] ?? '');


            // 检查一级项目
            if ($level1) {
                $level1Project = $dbf->table('project')
                    ->where('where level = 1 AND project_name = :project_name', ['project_name' => $level1])
                    ->one();

                if (!$level1Project) {
                    $level1ProjectId = $dbf->table('project')->insert([
                        'p_id' => 0,
                        'level' => 1,
                        'project_code' => $projectCode,
                        'project_name' => $level1,
                        'created_time' => $currentTime
                    ]);
                } else {
                    $level1ProjectId = $level1Project['id'];
                }
            }

            // 检查二级项目
            if ($level1 && $level2) {
                $level2Project = $dbf->table('project')
                    ->where('where level = 2 AND project_name = :project_name AND p_id = :p_id', [
                        'project_name' => $level2,
                        'p_id' => $level1ProjectId
                    ])->one();

                if (!$level2Project) {
                    $level2ProjectId = $dbf->table('project')->insert([
                        'p_id' => $level1ProjectId,
                        'level' => 2,
                        'project_code' => $projectCode,
                        'project_name' => $level2,
                        'created_time' => $currentTime
                    ]);
                } else {
                    $level2ProjectId = $level2Project['id'];
                }
            }

            // 检查三级项目
            if ($level1 && $level2 && $projectName) {
                $existingProject = $dbf->table('project')
                    ->where('where level = 3 AND project_name = :project_name AND p_id = :p_id', [
                        'project_name' => $projectName,
                        'p_id' => $level2ProjectId
                    ])->one();

                if (!$existingProject) {
                    $user = $dby->table('qwuser')
                        ->where('where wname = :wname', ['wname' => $responsible])
                        ->one();

                    if (!$user) {
                        continue;  // 如果找不到项目负责人，跳过这个记录
                    }

                    $dbf->table('project')->insert([
                        'p_id' => $level2ProjectId,
                        'level' => 3,
                        'project_code' => $projectCode,
                        'project_name' => $projectName,
                        'created_time' => $currentTime,
                        'user_id' => $user['id'],
                        'user_ids' => $P_members,
                    ]);
                }
            }
        }

        returnSuccess(['error' => $error, 'link' => $link], '导入成功');
    }
    //导出数据
    public function exportTemplate()
    {
        // Excel 列表头
        $excelData = [
            ['项目编码' => '项目编码', '一级' => '一级', '二级' => '二级', '项目名称' => '项目名称', '项目项目负责人' => '项目负责人'],
        ];

        // 获取数据库实例
        $dbf = dbFMysql::getInstance();
        $dby = dbMysql::getInstance();

        // 获取所有项目和用户数据
        $projects = $dbf->table('project')
            ->field('id, p_id, level, project_name, user_id, user_ids, Project_Code')
            ->order('level ASC')
            ->list();

        $users = $dby->table('qwuser')
            ->field('id, wname')
            ->list();

        // 用户 ID 映射到用户名
        $userMap = array_column($users, 'wname', 'id');

        // 组织项目信息
        $projectMap = [];
        foreach ($projects as $project) {
            $responsible = $userMap[$project['user_id']] ?? '';
            $operationsStr = implode(', ', array_filter(array_map(function ($id) use ($userMap) {
                return $userMap[$id] ?? '';
            }, explode(',', $project['user_ids']))));

            $projectMap[$project['id']] = [
                'level' => $project['level'],
                'project_name' => $project['project_name'],
                'parent_id' => $project['p_id'],
                'responsible' => $responsible,
                'operations' => $operationsStr,
                'project_code' => $project['Project_Code'],
            ];
        }

        // 根据层级整理数据到 Excel
        foreach ($projectMap as $info) {
            if ($info['level'] == 3) {
                $parent = $projectMap[$info['parent_id']] ?? null;
                $grandparent = $parent ? $projectMap[$parent['parent_id']] ?? null : null;

                if ($parent && $grandparent) {
                    $excelData[] = [
                        '项目编码' => $info['project_code'],
                        '一级' => $grandparent['project_name'],
                        '二级' => $parent['project_name'],
                        '项目名称' => $info['project_name'],
                        '项目负责人' => $info['responsible']
                    ];
                }
            }
        }

        // 使用项目根目录动态构建导出文件路径
        $projectRoot = dirname(__DIR__, 2);
        $exportPath = $projectRoot . '/public_financial/temp/project/data/userexported_projects.xlsx';

        // 检查并创建文件夹
        if (!file_exists(dirname($exportPath))) {
            mkdir(dirname($exportPath), 0777, true);
        }

        // 删除 $excelData 的第一行（如果不需要标题行）
        if (count($excelData) > 1) {
            array_shift($excelData); // 删除第一行标题
        }

        // 使用 FastExcel 导出数据
        (new FastExcel($excelData))->export($exportPath);

        // 返回导出文件路径
        returnSuccess('/public_financial/temp/project/data/userexported_projects.xlsx');
    }
    //异常详情
    public function abnormal(){
        $paras_list = array('page', 'page_size');
        $param = arrangeParam($_POST, $paras_list);

        // 设置默认分页参数
        $page = !empty($param['page']) ? $param['page'] : 1;
        $page_size = !empty($param['page_size']) ? $param['page_size'] : 10;

        $db = dbFMysql::getInstance();
        $data = $db->table('project_syn_log')
                ->where('where reson_txt is not null and is_delete = 0')
                ->field('reson_txt,principal')
                ->pages($page, $page_size);

        foreach ($data['list'] as &$item) {
            if (empty($item['principal']) || $item['principal'] = 'null') {
                $joinedNames = '空';
            } else {
                $principals = json_decode($item['principal'], true); // 将 JSON 字符串转换为 PHP 数组
                $names = array_column($principals,'principal_name');
                $joinedNames = implode('/', $names); // 使用 / 拼接名字
            }
            $item['principal'] = $joinedNames;
        }
        returnSuccess($data,'获取成功');
    }
    //配置项目组长或组员
    public function configleader() {
        // 定义允许的参数列表和请求描述
        $paras_list = array('id', 'Leader');
        $request_list = ['id' => '项目id', 'Leader' => '组长组员信息'];

        // 处理传入的参数
        $param = arrangeParam($_POST, $paras_list, $request_list);

        $dbs = dbMysql::getInstance();


        self::updateProjectLeaderx($param['Leader'],$dbs);

        // 解析 Leader 字段
        $leaderData = json_decode($param['Leader'], true);

        // 初始化存储用户 ID 的数组
        $user_ids = [];

        // 遍历 Leader 数据，提取所有的用户 ID
        foreach ($leaderData as $mainId => $subIds) {
            // 添加子用户 ID 到用户 ID 列表
            $user_ids = array_merge($user_ids, $subIds);
        }

        // 去重用户 ID 列表
        $user_ids = array_unique($user_ids);

        // 格式化为 JSON 字符串
        $user_ids_json = json_encode(array_values($user_ids));

        // 去掉方括号并替换为逗号分隔的字符串
        $user_ids_json = implode(',', json_decode($user_ids_json));

        // 在前后添加逗号
        $user_ids_json = ',' . $user_ids_json . ',';

        // 获取数据库实例
        $db = dbFMysql::getInstance();

        // 更新项目数据
        $le = $db->table('project')
            ->where('where id = :id', ['id' => $param['id']])
            ->one();
        if ($le['level'] != 3){
            returnError('仅三级项目可配置');
        }
        // 更新项目数据
        $db->table('project')
            ->where('where id = :id', ['id' => $param['id']])
            ->update([
                'Leader' => $param['Leader']]);
        // 返回成功响应
        returnSuccess('', '配置成功');
    }
    //新增用
    private static function updateProjectLeaderx( $project, $dbs) {

        // 解析 JSON 字符串为 PHP 数组
        $userIds = json_decode($project, true);

        // 初始化存储所有用户 ID
        $allUserIds = [];
        foreach ($userIds as $subIds) {
            $allUserIds = array_merge($allUserIds, $subIds);
        }

        // 添加主键到用户 ID 列表中
        $allUserIds = array_unique(array_merge($allUserIds, array_keys($userIds)));

        // 如果有用户 ID 需要查询
        if (!empty($allUserIds)) {
            // 构建 IN 查询条件
            $placeholders = implode(',', array_fill(0, count($allUserIds), '?'));
            $whereInCondition = "id IN ($placeholders)";

            // 查询用户数据
            $userRecords = $dbs->table('qwuser')
                ->where("where is_delete = 0 AND $whereInCondition", $allUserIds)
                ->list(); // 假设 list 方法返回查询结果

            // 创建一个以用户 ID 为键的关联数组
            $users = [];
            foreach ($userRecords as $userRecord) {
                $users[$userRecord['id']] = $userRecord;
            }

            // 创建一个以主键为键的用户信息
            $updatedLeader = [];
            foreach ($userIds as $userId => $subIds) {
                if (isset($users[$userId])) {
                    $mainUserInfo = $users[$userId];
                    $mainUserInfo['childs'] = []; // 初始化子用户信息数组

                    // 查找与主键相关的子用户信息
                    foreach ($subIds as $subId) {
                        if (isset($users[$subId])) {
                            $mainUserInfo['childs'][] = $users[$subId];
                        }
                    }
                    $updatedLeader[$userId] = $mainUserInfo;
                }
            }


            $project = $updatedLeader;
        }
        $data = self::validateLeaders($project);
        if ($data['status'] == 'error'){
            returnError($data['message']);
        }
        return $project;
    }
    //检测运营
    public static function validateLeaders($data) {
        // 存储每个运营人员的组长 ID
        $operatorAssignments = [];

        // 遍历每个组长
        foreach ($data as $leaderId => $leaderInfo) {
            // 检查是否有子人员
            if (isset($leaderInfo['childs']) && is_array($leaderInfo['childs'])) {
                // 遍历子人员
                foreach ($leaderInfo['childs'] as $child) {
                    if ($child['position'] === '运营') {
                        // 如果该运营人员已经存在于 $operatorAssignments 中，则检查组长是否一致
                        if (isset($operatorAssignments[$child['id']])) {
                            if ($operatorAssignments[$child['id']] !== $leaderId) {
                                // 记录运营人员重复归属的情况
                                return [
                                    'status' => 'error',
                                    'message' => '运营人员 ' . $child['wname'] . ' 被分配到多个组长下。',
                                    'details' => [
                                        'previous_leader' => $operatorAssignments[$child['id']],
                                        'current_leader' => $leaderId,
                                        'operator_id' => $child['id']
                                    ]
                                ];
                            }
                        } else {
                            // 记录当前组长下的运营人员
                            $operatorAssignments[$child['id']] = $leaderId;
                        }
                    }
                }
            }
        }

        // 如果没有发现问题，则返回成功
        return [
            'status' => 'success',
            'message' => '所有运营人员都只归属于一个组长。'
        ];
    }
    //清除组长和组员关系
    public function cleanLeader() {
        $ids = $_POST['ids']??'[]';
        $ids = json_decode($ids);
        if (!count($ids)) {
            returnError('请勾选要清除的列');
        }
        projectForm::cleanLeader($ids);
        returnSuccess('操做成功');
    }
    //验证项目名是否重复
    public static function getLevelarray($db, $p_id, $p_name) {
        if ($p_id == 2){
            $v1 = $db->table('project')
                ->where('where id = :id', ['id' => $p_id])
                ->one();
            $v3 = $db->table('project')
                ->where('p_id = :p_id and project_name = :project_name',['p_id' => $v1['id'],'project_name'=>$p_name])
                ->one();
            if (!empty($v3)){
                returnError('项目名重复');
            }
        }
    }
    //修改验证重复
    public static function varifyProjectName($db,$id, $p_id, $p_name, $level) {
        $proj = '';
        if ($level == 1) {
            $proj = $db->table('project')
                ->where('p_id = 0 and level = 1 and id <> :id and project_name = :project_name',['project_name'=>$p_name,'id'=>$id])
                ->field('id')
                ->one();
        }
        if ($level == 2) {
            $proj = $db->table('project')
                ->where('p_id = :p_id and level = 2 and id <> :id and project_name = :project_name',['p_id'=>$p_id,'project_name'=>$p_name,'id'=>$id])
                ->field('id')
                ->one();
        }
        if ($level == 3) {
            $proj = $db->table('project')
                ->where('p_id = :p_id and level = 3 and id <> :id and project_name = :project_name',['p_id'=>$p_id,'project_name'=>$p_name,'id'=>$id])
                ->field('id')
                ->one();
        }
        if ($proj) {
            returnError('项目名称重复');
        }
    }
    //项目code验证
    public static function varifyProjectCode($db,$id,$project_code) {
        $proj = $db->table('project')
            ->where('Project_Code = :Project_Code and id <> :id and level=3',['Project_Code'=>$project_code,'id'=>$id])
            ->field('id')
            ->one();
        if ($proj) {
            returnError('项目编码重复');
        }
    }

}
