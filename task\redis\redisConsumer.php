<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/25 17:48
 */
function dd($data){
    print_r($data);die;
}
define('SYS_ENV','develop'); //生产环境：develop-开发，produce-生产
$current_path = dirname(__FILE__);
include_once $current_path.'/redisConnect.php';
include_once $current_path.'/logs.php';
$log = new logs();
$config_dev = SYS_ENV == 'develop'?'config_develop':'config';
$qw_config = require_once $current_path.'/../../core/'.$config_dev.'/qw.php';
$key="qw_message";
$group_name="qw_message_g";
global $redis;

if (!initGroup()) {
    $redis->xGroup('CREATE', $key, $group_name,  0, true);
}

while(true){
    global $qw_config;
    //之前的数据处理-非阻塞
    $getResult = $redis->xReadGroup($group_name,"c1",[$key=>"0"],1000);
    //TODO 需要优化，容易引起死信
    if ($getResult && isset($getResult[$key]) && count($getResult[$key])) {
        foreach ($getResult[$key] as $id=>$info) {
            if (empty($info)) {
                $redis->xAck($key, $group_name, [$id]);
                $redis->xDel($key,[$id]);
                continue;
            }
            //消息发送
            $log::sendMessageToQwLog(json_encode($info,JSON_UNESCAPED_UNICODE),'发送');
            $res = sendMessage($info);
            if ($res) {
                //确认消息
                $redis->xAck($key, $group_name, [$id]);
                //删除数据
                $redis->xDel($key,[$id]);
            } else {
                $redis->xAck($key, $group_name, [$id]);
                //删除数据
                $redis->xDel($key,[$id]);
                //重新加到末尾
                $redis->xAdd($key,"*",$info,1000);
            }
            usleep(500*1000);//休眠 500毫秒
        }
        continue;
    } else {
        $getResult = $redis->xReadGroup($group_name,"c1",[$key=>">"],1, 10*1000);
        if ($getResult && isset($getResult[$key]) && count($getResult[$key])) {
            foreach ($getResult[$key] as $id=>$info) {
                if (empty($info)) {
                    $redis->xAck($key, $group_name, [$id]);
                    $redis->xDel($key,[$id]);
                    continue;
                }
                //消息发送
                $log::sendMessageToQwLog(json_encode($info,JSON_UNESCAPED_UNICODE),'发送');
                $res = sendMessage($info);
                if ($res) {
                    //确认消息
                    $redis->xAck($key, $group_name, [$id]);
                    //删除数据
                    $redis->xDel($key,[$id]);
                } else {
                    $redis->xAck($key, $group_name, [$id]);
                    //删除数据
                    $redis->xDel($key,[$id]);
                    //重新加到末尾
                    $redis->xAdd($key,"*",$info,1000);
                }
            }
        }
        usleep(500*1000);//休眠 500毫秒
        continue;
    }
    echo '中断';break;
}


function initGroup() { //初始化组
    global $redis;
    global $key;
    global $group_name;
    //首先查看redis当中是否已经创建好了stream的组！ 获取组的信息出来！
    $group_list = $redis->xInfo('GROUPS',$key);//这里得到一个数组
    if ($group_list && count($group_list)) {
        foreach ($group_list as $group) {
            if ($group['name'] == $group_name) {
                return true;
            }
        }
    }
    return false;
}
//发送应用消息
function sendMessage($msg){
    global $qw_config;
    global $log;
    $token = getToken();
    if (!$token) {
        return false;
    }
    $url = $qw_config['send_msg_url'].'?access_token='.$token;
    //获取跳转地址
    $msg_url = $qw_config['mag_url'];
    if (isset($msg['system_type'])) {
        if ($msg['system_type'] == 2) {
            $msg_url = $qw_config['mag_url_fiancial'];
        } elseif ($msg['system_type'] == 3) {
            $msg_url = $qw_config['mag_url_aftersale'];
        } elseif ($msg['system_type'] == 4) {
            $msg_url = $qw_config['mag_url_assessment'];
        } elseif ($msg['system_type'] == 5) {
            $msg_url = $qw_config['mag_url_checkin'];
        } elseif ($msg['system_type'] == 6) {
            $msg_url = $qw_config['mag_url_salary'];
        } elseif ($msg['system_type'] == 7) {
            $msg_url = $qw_config['mag_url_shop'];
        }
    }
    $msg_data = json_decode($msg['data'],true);
    $msg_data['system_type'] = $msg['system_type'];
    $msg['data'] = json_encode($msg_data);
    if (!empty($msg['data'])) {
        $msg_url .= ('?data='.getSecretCode($msg['data']));
    }
    //获取数据
    $data = [
        'touser'=>$msg['qw_userid'],
        'msgtype'=>$msg['type']??'text',
        'agentid'=> $qw_config['agentid'],//企业应用的id
    ];
    if ($data['msgtype'] == 'text') {
        $data['text'] = ['content'=>$msg['msg']];
    } elseif ($data['msgtype'] == 'textcard') {
        $data['textcard'] = [
            'title'=>$msg['title'],
            'description'=>!empty($msg['msg'])?$msg['msg']:'无内容',
            'url'=>$msg_url,
        ];
    }
    //发送消息
    $res = requestHttp($url, 'post',json_encode($data));

    if (!empty($res)) {
        //日志记录(不管成功与否)
        if ($res['errcode'] != 0) {
            $log::sendMessageToQwLog(json_encode($res),'发送失败');
            if ($res['errcode'] == 81013) {
                return true;
            } else {
                return false;
            }
        } else {
            $log::sendMessageToQwLog(json_encode($res),'发送成功');
            return true;
        }
    } else {
        return false;
    }

}
function getToken(){
    global $redis;
    global $qw_config;
    global $log;
//    $redis->del('qy_token');
    $token = $redis->get('qy_token');
    if ($token) {
        return $token;
    } else {
        //内部应用
        if (SYS_ENV == 'develop'){
            $token_url = $qw_config['token_url_nei'];
            $data = requestHttp($token_url,'get');
            if ($data && $data['data']) {
                $redis->set('qy_token', $data['data']);
                $redis->expire('qy_token',5*60);
                return$data['data'];
            } else {
                $log::sendMessageToQwLog('企微：获取token失败','token获取');
                return false;
            }
        } else {
            $token_url = $qw_config['token_url'].'?corpid='.$qw_config['corpid'].'&corpsecret='.$qw_config['secret'];
            $data = requestHttp($token_url,'get');
            if ($data && $data['errcode']==0) {
                $redis->set('qy_token', $data['access_token']);
                $redis->expire('qy_token',5*60);
                return $data['access_token'];
            } else {
                $log::sendMessageToQwLog('企微：获取token失败','token获取');
                return false;
            }
        }
    }
}
function requestHttp($url, $type="get", $data='{}'){
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    if ($type == 'post') {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    } else {
        curl_setopt($ch, CURLOPT_POST, false);
    }
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);//设置为FALSE 禁止 cURL 验证对等证书
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT_MS, 5000);
    $response = curl_exec($ch);
    $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    if ($code) {
        return json_decode($response,true);
    } else {
        return '';
    }
}

function getSecretCode($data){
    $method = "AES-256-CBC"; // 加密算法和模式
    $key = "redis_qw_message_url_data"; // 加密密钥
    $salt = openssl_random_pseudo_bytes(8); // 随机生成盐值
    $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length($method)); // 生成初始化向量

    $encrypted = openssl_encrypt($data, $method, $key, OPENSSL_RAW_DATA, $iv);

    // 将盐值、初始化向量和加密数据拼接在一起，以便后续解密时使用
    $encryptedData = base64_encode($salt . $iv . $encrypted);
    return $encryptedData;
}

