-- 海外仓备货单明细表
-- 用于存储按SKU拆分后的备货单明细数据
-- 每条记录代表一个备货单中的一个SKU

CREATE TABLE `overseas_inbound_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `overseas_order_no` varchar(100) NOT NULL DEFAULT '' COMMENT '海外仓备货单号',
  `sku` varchar(100) NOT NULL DEFAULT '' COMMENT 'SKU代码',
  `product_name` varchar(200) NOT NULL DEFAULT '' COMMENT '产品名称',
  `shop_code` varchar(100) NOT NULL DEFAULT '' COMMENT '店铺代码',
  `fnsku` varchar(200) NOT NULL DEFAULT '' COMMENT 'FNSKU',
  `quantity` int(11) NOT NULL DEFAULT '0' COMMENT '数量',
  `box_count` int(11) NOT NULL DEFAULT '0' COMMENT '箱数',
  `warehouse_code` varchar(100) NOT NULL DEFAULT '' COMMENT '入仓编号',
  `transparent_label` varchar(200) NOT NULL DEFAULT '' COMMENT '透明标',
  `logistics_method` varchar(200) NOT NULL DEFAULT '' COMMENT '物流方式',
  `target_warehouse` varchar(200) NOT NULL DEFAULT '' COMMENT '发往仓库',
  `plan_time` datetime DEFAULT NULL COMMENT '计划时间',
  `ship_time` datetime DEFAULT NULL COMMENT '发货时间',
  `shipping_status` varchar(50) NOT NULL DEFAULT '' COMMENT '发货在途状态',
  `warehouse_arrival` datetime DEFAULT NULL COMMENT '仓库到货时间',
  `receive_difference` int(11) NOT NULL DEFAULT '0' COMMENT '收货差异',
  `remaining_available` int(11) NOT NULL DEFAULT '0' COMMENT '剩余可用数量',
  `shipping_remark` text COMMENT '发货备注',
  `other_remark` text COMMENT '其他备注',
  
  -- 扩展字段，用于关联和追溯
  `original_order_id` int(11) NOT NULL DEFAULT '0' COMMENT '原始备货单记录ID',
  `product_id` int(11) NOT NULL DEFAULT '0' COMMENT '产品ID',
  `batch_no` varchar(100) NOT NULL DEFAULT '' COMMENT '批次号',
  `unit_cost` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '单位成本',
  `total_cost` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总成本',
  
  -- 系统字段
  `sync_date` date NOT NULL COMMENT '同步日期',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-否 1-是',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_sku` (`overseas_order_no`, `sku`, `is_deleted`) COMMENT '备货单号+SKU唯一索引',
  KEY `idx_overseas_order_no` (`overseas_order_no`) COMMENT '备货单号索引',
  KEY `idx_sku` (`sku`) COMMENT 'SKU索引',
  KEY `idx_shop_code` (`shop_code`) COMMENT '店铺代码索引',
  KEY `idx_sync_date` (`sync_date`) COMMENT '同步日期索引',
  KEY `idx_original_order_id` (`original_order_id`) COMMENT '原始订单ID索引',
  KEY `idx_created_at` (`created_at`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='海外仓备货单明细表';

