<?php

namespace plugins\assessment\controller;

use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\db\dbAMysql;
use core\lib\db\dbMysql;
use plugins\assessment\models\assessmentModel;
use plugins\assessment\models\assessmentSchemesModel;
use plugins\assessment\models\commissionRulesModel;

class CommissionRulesController
{
    // 获取提成比例列表
    public function getCommissionRulesList()
    {
        $paras_list = array('rule_name', 'qw_department_ids', 'columns', 'page_size', 'page');
        $param = arrangeParam($_POST, $paras_list);
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        $department_ids = !empty($param['qw_department_ids']) ? json_decode($param['qw_department_ids'], true) : null;
        $columns = !empty($param['columns']) ? json_decode($param['columns'], true) : null;
        $db =  dbMysql::getInstance();
        $adb = dbAMysql::getInstance();
        $fdb = dbFMysql::getInstance();

        $adb->table('commission_rules');
        $adb->where('where is_delete = 0');

        // 筛选条件
        if ($param['rule_name']) {
            $adb->andWhere('rule_name like :rule_name', ['rule_name' => '%' . $param['rule_name'] . '%']);
        }
        if ($department_ids) {
            $adb->whereIn('qw_department_id', $department_ids);
        }
        if ($columns) {
            $adb->whereIn('column_id', $columns);
        }

        $adb->order('id desc');
        $data = $adb->pages($page, $limit);

        // 企微部门
        $db->table('qwdepartment');
        $db->field('id,wp_id,name,qw_parentid,sort');
        $db->order('qw_parentid asc,`sort` desc');
        $departments = $db->list();
        $departmentsMap = array_column($departments, 'name', 'wp_id');

        // 业绩指标
        $assessment_targets_source = config::get('assessment_targets_source','data_assessment');
        $assessment_targets_source = array_column($assessment_targets_source, 'list', 'id');
        $columns = $assessment_targets_source['1'];
        $columnsMap = array_column($columns, 'column_name', 'id');

        foreach ($data['list'] as &$item) {
            $item['qw_department_name'] = $departmentsMap[$item['qw_department_id']] ?? '';
            $item['column_name'] = $columnsMap[$item['column_id']] ?? '';
            $item['rules'] = json_decode($item['rules'], true);
            $item['prize_rules'] = json_decode($item['prize_rules'], true);
            $item['rule_text'] = commissionRulesModel::getRuleText($item['rules'], $item['column_name']);
        }

        returnSuccess($data);
    }

    // 新增/编辑提成比例
    public function addCommissionRules()
    {
        $paras_list = array('id', 'rule_name', 'qw_department_id', 'rules', 'column_id', 'currency', 'prize_rules');
        $length_data = ['rule_name' => ['name' => '规则名称', 'length' => 50]];
        $request_list = ['rule_name' => '规则名称', 'qw_department_id' => '企微部门ID', 'column_id' => '业绩指标', 'rules' => '规则', 'currency' => '币种'];
        $param = arrangeParam($_POST, $paras_list, $request_list, $length_data);

        $adb = dbAMysql::getInstance();

        // 判断规则是否存在
        if ($param['id']) {
            $adb->table('commission_rules');
            $adb->where('where id = :id and is_delete = 0', ['id' => $param['id']]);
            $is_exist = $adb->one();
            if (!$is_exist) returnError('规则不存在');

            // 判断是否有正在进行中的绩效考核在使用该规则
            if (in_array($param['id'], assessmentModel::getRunningAssessmentDetail('rule'))) {
                returnError('有进行中的绩效考核在使用该规则，不允许修改');
            }
        }

        // 判断规则名称是否重复
        $adb->table('commission_rules');
        $adb->where('where rule_name = :rule_name and is_delete = 0', ['rule_name' => $param['rule_name']]);
        if ($param['id']) {
            $adb->andWhere('id != :id', ['id' => $param['id']]);
        }
        $is_exist = $adb->one();
        if ($is_exist) returnError('规则名称已存在');

        try {
            $ruleCheck = commissionRulesModel::checkRules(json_decode($param['rules'], true));
            !empty($param['prize_rules']) && commissionRulesModel::checkPrizeRules(json_decode($param['prize_rules'], true));
        } catch (\Throwable $error) {
            returnError($error->getMessage());
        }

        $data = [
            'rule_name'        => $param['rule_name'],
            'qw_department_id' => $param['qw_department_id'],
            'column_id'        => $param['column_id'],
            'rules'            => $param['rules'],
            'currency'         => $param['currency'],
            'prize_rules'      => $param['prize_rules']
        ];
        $adb->table('commission_rules');
        if ($param['id']) {
            $adb->where('where id = :id and is_delete = 0', ['id' => $param['id']]);
            $res = $adb->update($data);
        } else {
            $res = $adb->insert($data);
            if (!$res) {
                returnError('fail');
            }
        }
        returnSuccess([], 'success');
    }

    // 删除提成比例
    public function delCommissionRules()
    {
        $id = $_POST['id'];
        if (!$id) returnError('id不能为空');
        $adb = dbAMysql::getInstance();
        $adb->table('commission_rules');
        $adb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $is_exist = $adb->one();
        if (!$is_exist) returnError('规则不存在');

        // 是否有方案引用该规则
        if (in_array($id, assessmentSchemesModel::getSchemeDetail('rule'))) {
            returnError('有方案在使用该规则，不允许删除');
        }

        // 判断是否有正在进行中的绩效考核在使用该规则
        if (in_array($id, assessmentModel::getRunningAssessmentDetail('rule'))) {
            returnError('有进行中的绩效考核在使用该规则，不允许删除');
        }

        $data = [
            'is_delete' => 1,
        ];
        $adb->table('commission_rules');
        $adb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $res = $adb->update($data);
        if ($res) {
            returnSuccess([], 'success');
        } else {
            returnError('fail');
        }
    }

    // 获取提成比例详情
    public function getCommissionRulesDetail()
    {
        $id = $_POST['id'];
        if (!$id) returnError('id不能为空');
        $db = dbMysql::getInstance();
        $adb = dbAMysql::getInstance();
        $fdb = dbFMysql::getInstance();
        $adb->table('commission_rules');
        $adb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $detail = $adb->one();
        if (!$detail) returnError('规则不存在');

        // 企微部门
        $db->table('qwdepartment');
        $db->field('id,wp_id,name,qw_parentid,sort');
        $db->order('qw_parentid asc,`sort` desc');
        $departments = $db->list();
        $departmentsMap = array_column($departments, 'name', 'wp_id');

        // 业绩指标
        $assessment_targets_source = config::get('assessment_targets_source','data_assessment');
        $assessment_targets_source = array_column($assessment_targets_source, 'list', 'id');
        $columns = $assessment_targets_source['1'];
        $columnsMap = array_column($columns, 'column_name', 'id');
        $detail['qw_department_name'] = $departmentsMap[$detail['qw_department_id']] ?? '';
        getDepartmentRoute($departments, $detail['qw_department_id'],$detail['qw_department_route']);
        // 判断数组是否为空 array_reverse
        $detail['qw_department_route'] = $detail['qw_department_route'] ? array_reverse($detail['qw_department_route']) : [];
        $detail['column_name'] = $columnsMap[$detail['column_id']] ?? '';
        $detail['rules'] = json_decode($detail['rules'], true);
        $detail['prize_rules'] = json_decode($detail['prize_rules'], true);
        $detail['rule_text'] = commissionRulesModel::getRuleText($detail['rules'], $detail['column_name']);

        returnSuccess($detail);
    }

}