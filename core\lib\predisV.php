<?php

namespace core\lib;

/**
 * @author: zhangguoming
 * @Time: 2023/12/7 16:15
 */
use core\lib\config;

class predisV
{
    static object|null $client = null;

    public function __construct()
    {
        if (!self::$client) {
            $redis_config = config::all('redis');
            try{
                $redis = new \Redis();
                $redis->connect((string)$redis_config['host'], (int)$redis_config['port']);
                if (!empty($redis_config['pwd'])) {
                    $redis->auth($redis_config['pwd']);
                }
                self::$client = $redis;
            }catch(\core\lib\ExceptionError $e){
                throw new \core\lib\ExceptionError('redis连接失败');
            }
        }
    }

    //消息推入队列(阻断模式)
    public static function redisQueue($data){
        self::$client->xAdd('qw_message',"*",$data,1000);
    }
    //延时队列（非阻断模式，阻断不合适，历史问题无发定位）

    /**
     * @param $class
     * @param $delay_time 延时时间，队列执行的具体时间，单位毫秒
     * @param $option
     * @return void
     */
    public static function setDelayQueue($task, $delay_time, $option = []) {
        $queue_key = config::get('delay_queue_key', 'app');
        self::$client->zAdd($queue_key, [], $delay_time, serialize($task));
    }
}