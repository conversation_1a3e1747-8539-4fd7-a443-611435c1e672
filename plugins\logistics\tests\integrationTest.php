<?php
/**
 * 海外仓备货单SKU拆分集成测试
 * @purpose 测试完整的数据处理流程
 * @Author: System
 * @Time: 2025/07/01
 */

require_once __DIR__ . '/../../../core/fk.php';

use plugins\logistics\models\overseasInboundDetailModel;
use plugins\logistics\models\overseasInboundModel;

class integrationTest
{
    private $detailModel;
    private $inboundModel;

    public function setUp()
    {
        $this->detailModel = new overseasInboundDetailModel();
        $this->inboundModel = new overseasInboundModel();
    }

    /**
     * 测试完整的数据流程
     */
    public function testCompleteDataFlow()
    {
        echo "测试完整数据流程...\n";
        
        // 1. 模拟原始备货单数据
        $testOrderData = [
            'overseas_order_no' => 'INTEGRATION_TEST_001',
            'products' => '[
                {
                    "product_id": 99999,
                    "sku": "INT_TEST_SKU_001",
                    "product_name": "集成测试产品1",
                    "fnsku": "INT_FNSKU_001",
                    "stock_num": 100,
                    "receive_num": 100,
                    "s_wname": "测试仓库1",
                    "batch_record_list": [
                        {
                            "batch_no": "INT_BATCH_001",
                            "good_num": 100,
                            "batch_order_sn": "INT_ORDER_001",
                            "unit_cost": "25.50"
                        }
                    ]
                },
                {
                    "product_id": 99998,
                    "sku": "INT_TEST_SKU_002", 
                    "product_name": "集成测试产品2",
                    "fnsku": "INT_FNSKU_002",
                    "stock_num": 50,
                    "receive_num": 45,
                    "s_wname": "测试仓库2",
                    "batch_record_list": [
                        {
                            "batch_no": "INT_BATCH_002",
                            "good_num": 50,
                            "batch_order_sn": "INT_ORDER_002",
                            "unit_cost": "15.80"
                        }
                    ]
                }
            ]',
            'logistics' => '{"method": "集成测试物流"}',
            'shop_code' => 'INT_SHOP_001',
            'plan_time' => '2024-12-01 10:00:00',
            'ship_time' => '2024-12-02 10:00:00',
            'warehouse_arrival' => '2024-12-10 10:00:00',
            'shipping_remark' => '集成测试发货备注',
            'other_remark' => '集成测试其他备注'
        ];

        // 2. 执行SKU拆分
        $details = $this->detailModel->splitOrderBySku($testOrderData);
        
        if (count($details) !== 2) {
            throw new Exception("拆分失败：期望2个SKU，实际" . count($details) . "个");
        }

        // 3. 验证拆分结果
        $this->validateSplitResults($details, $testOrderData);

        // 4. 测试批量插入
        $insertResult = $this->detailModel->batchInsertDetails($details);
        if (!$insertResult) {
            throw new Exception("批量插入失败");
        }

        // 5. 验证数据库中的数据
        $this->validateDatabaseData($testOrderData['overseas_order_no']);

        // 6. 测试导出功能
        $exportData = $this->detailModel->getDetailForExport(date('Y-m-d'), $testOrderData['overseas_order_no']);
        if (count($exportData) !== 2) {
            throw new Exception("导出数据数量错误：期望2条，实际" . count($exportData) . "条");
        }

        // 7. 测试统计功能
        $stats = $this->detailModel->getDetailStats(date('Y-m-d'));
        if ($stats['total_count'] < 2) {
            throw new Exception("统计数据错误：总数应该至少包含测试的2条数据");
        }

        // 8. 清理测试数据
        $this->cleanupTestData($testOrderData['overseas_order_no']);

        echo "✅ 完整数据流程测试通过\n";
    }

    /**
     * 验证拆分结果
     */
    private function validateSplitResults($details, $originalOrder)
    {
        echo "验证拆分结果...\n";
        
        $expectedSkus = ['INT_TEST_SKU_001', 'INT_TEST_SKU_002'];
        $actualSkus = array_column($details, 'sku');
        
        foreach ($expectedSkus as $expectedSku) {
            if (!in_array($expectedSku, $actualSkus)) {
                throw new Exception("拆分结果缺少SKU: {$expectedSku}");
            }
        }

        foreach ($details as $detail) {
            // 验证继承字段
            if ($detail['overseas_order_no'] !== $originalOrder['overseas_order_no']) {
                throw new Exception("备货单号继承错误");
            }
            
            if ($detail['shop_code'] !== $originalOrder['shop_code']) {
                throw new Exception("店铺代码继承错误");
            }

            // 验证SKU特有字段
            if (empty($detail['sku'])) {
                throw new Exception("SKU字段为空");
            }
            
            if (empty($detail['product_name'])) {
                throw new Exception("产品名称字段为空");
            }

            // 验证数值字段
            if (!is_int($detail['quantity']) || $detail['quantity'] <= 0) {
                throw new Exception("数量字段错误");
            }
        }

        echo "✅ 拆分结果验证通过\n";
    }

    /**
     * 验证数据库中的数据
     */
    private function validateDatabaseData($orderNo)
    {
        echo "验证数据库数据...\n";
        
        $dbData = $this->detailModel->getDetailForExport(date('Y-m-d'), $orderNo);
        
        if (empty($dbData)) {
            throw new Exception("数据库中没有找到测试数据");
        }

        foreach ($dbData as $record) {
            // 验证必填字段
            $requiredFields = ['overseas_order_no', 'sku', 'product_name'];
            foreach ($requiredFields as $field) {
                if (empty($record[$field])) {
                    throw new Exception("数据库记录缺少必填字段: {$field}");
                }
            }

            // 验证时间字段格式
            if (!empty($record['created_at'])) {
                $timestamp = strtotime($record['created_at']);
                if (!$timestamp) {
                    throw new Exception("创建时间格式错误");
                }
            }
        }

        echo "✅ 数据库数据验证通过\n";
    }

    /**
     * 测试性能
     */
    public function testPerformance()
    {
        echo "测试性能...\n";
        
        $startTime = microtime(true);
        
        // 模拟大量数据的处理
        $largeOrderData = [
            'overseas_order_no' => 'PERF_TEST_001',
            'products' => $this->generateLargeProductsJson(100), // 100个SKU
            'logistics' => '{"method": "性能测试物流"}',
            'shop_code' => 'PERF_SHOP',
            'plan_time' => '2024-12-01 10:00:00'
        ];

        // 执行拆分
        $details = $this->detailModel->splitOrderBySku($largeOrderData);
        
        $splitTime = microtime(true);
        $splitDuration = $splitTime - $startTime;

        if (count($details) !== 100) {
            throw new Exception("性能测试失败：期望100个SKU，实际" . count($details) . "个");
        }

        // 测试批量插入性能
        $insertResult = $this->detailModel->batchInsertDetails($details);
        
        $endTime = microtime(true);
        $totalDuration = $endTime - $startTime;
        $insertDuration = $endTime - $splitTime;

        if (!$insertResult) {
            throw new Exception("性能测试批量插入失败");
        }

        echo "性能测试结果:\n";
        echo "- 拆分100个SKU耗时: " . round($splitDuration, 3) . "秒\n";
        echo "- 批量插入100条记录耗时: " . round($insertDuration, 3) . "秒\n";
        echo "- 总耗时: " . round($totalDuration, 3) . "秒\n";

        // 性能要求：处理100个SKU应该在5秒内完成
        if ($totalDuration > 5) {
            echo "⚠️ 性能警告：处理时间超过5秒\n";
        } else {
            echo "✅ 性能测试通过\n";
        }

        // 清理测试数据
        $this->cleanupTestData('PERF_TEST_001');
    }

    /**
     * 生成大量产品JSON数据
     */
    private function generateLargeProductsJson($count)
    {
        $products = [];
        for ($i = 1; $i <= $count; $i++) {
            $products[] = [
                'product_id' => 90000 + $i,
                'sku' => "PERF_SKU_" . str_pad($i, 3, '0', STR_PAD_LEFT),
                'product_name' => "性能测试产品{$i}",
                'fnsku' => "PERF_FNSKU_{$i}",
                'stock_num' => rand(10, 100),
                'receive_num' => rand(10, 100),
                's_wname' => "性能测试仓库{$i}",
                'batch_record_list' => [
                    [
                        'batch_no' => "PERF_BATCH_{$i}",
                        'good_num' => rand(10, 100),
                        'batch_order_sn' => "PERF_ORDER_{$i}",
                        'unit_cost' => rand(10, 50) . '.00'
                    ]
                ]
            ];
        }
        return json_encode($products);
    }

    /**
     * 清理测试数据
     */
    private function cleanupTestData($orderNo)
    {
        try {
            // 软删除测试数据
            $sql = "UPDATE overseas_inbound_detail 
                    SET is_deleted = 1 
                    WHERE overseas_order_no = :order_no";
            // 这里需要实际的数据库操作，暂时跳过
            echo "清理测试数据: {$orderNo}\n";
        } catch (Exception $e) {
            echo "清理测试数据失败: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 运行所有集成测试
     */
    public static function runAllTests()
    {
        echo "\n=== 海外仓备货单SKU拆分集成测试开始 ===\n";
        
        $test = new self();
        $test->setUp();
        
        try {
            $test->testCompleteDataFlow();
            $test->testPerformance();
            
            echo "\n✅ 所有集成测试通过！\n";
        } catch (Exception $e) {
            echo "\n❌ 集成测试失败: " . $e->getMessage() . "\n";
            echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
        }
        
        echo "=== 海外仓备货单SKU拆分集成测试结束 ===\n\n";
    }
}

// 如果直接运行此文件，执行所有测试
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    integrationTest::runAllTests();
}
