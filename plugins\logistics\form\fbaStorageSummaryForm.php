<?php
/**
 * FBA库存汇总统计表单验证
 * @purpose FBA库存汇总统计数据验证和过滤
 * @Author: System
 * @Time: 2025/06/20
 */

namespace plugins\logistics\form;

use core\lib\validator;

class fbaStorageSummaryForm
{
    /**
     * 验证列表查询参数
     * @param array $params
     * @return array
     */
    public function validateListParams($params)
    {
        $rules = [
            'page' => 'integer|min:1',
            'page_size' => 'integer|min:1|max:100',
            'asin' => 'string|max:50',
            'sku' => 'string|max:100',
            'country_code' => 'string|max:10',
            'sid' => 'integer|min:1',
            'product_stage' => 'integer|in:1,2,3,4,5',
            'stock_positioning' => 'integer|in:1,2,3',
            'product_positioning' => 'integer|in:1,2,3,4',
            'sync_date' => 'date_format:Y-m-d'
        ];
        
        $messages = [
            'page.integer' => '页码必须为整数',
            'page.min' => '页码必须大于0',
            'page_size.integer' => '每页数量必须为整数',
            'page_size.min' => '每页数量必须大于0',
            'page_size.max' => '每页数量不能超过100',
            'level_type.integer' => '汇总层级必须为整数',
            'level_type.in' => '汇总层级值无效',
            'asin.string' => 'ASIN必须为字符串',
            'asin.max' => 'ASIN长度不能超过50',
            'sku.string' => 'SKU必须为字符串',
            'sku.max' => 'SKU长度不能超过100',
            'country_code.string' => '站点代码必须为字符串',
            'country_code.max' => '站点代码长度不能超过10',
            'sid.integer' => '店铺ID必须为整数',
            'sid.min' => '店铺ID必须大于0',
            'product_stage.integer' => '产品阶段必须为整数',
            'product_stage.in' => '产品阶段值无效',
            'stock_positioning.integer' => '备货定位必须为整数',
            'stock_positioning.in' => '备货定位值无效',
            'product_positioning.integer' => '产品定位必须为整数',
            'product_positioning.in' => '产品定位值无效',
            'sync_date.date_format' => '同步日期格式错误，请使用Y-m-d格式'
        ];
        
        return $this->validate($params, $rules, $messages);
    }

    /**
     * 验证编辑参数
     * @param array $params
     * @return array
     */
    public function validateEditParams($params)
    {
        $rules = [
            'planned_purchase_qty' => 'integer|min:0',
            'purchase_pending_qty' => 'integer|min:0'
        ];
        
        $messages = [
            'planned_purchase_qty.integer' => '计划采购数量必须为整数',
            'planned_purchase_qty.min' => '计划采购数量不能小于0',
            'purchase_pending_qty.integer' => '采购未交数量必须为整数',
            'purchase_pending_qty.min' => '采购未交数量不能小于0'
        ];
        
        return $this->validate($params, $rules, $messages);
    }

    /**
     * 执行验证
     * @param array $params 待验证参数
     * @param array $rules 验证规则
     * @param array $messages 错误消息
     * @return array
     * @throws \Exception
     */
    private function validate($params, $rules, $messages)
    {
        $validatedData = [];
        
        foreach ($rules as $field => $rule) {
            if (!isset($params[$field])) {
                continue;
            }
            
            $value = $params[$field];
            $ruleList = explode('|', $rule);
            
            foreach ($ruleList as $singleRule) {
                $this->validateSingleRule($field, $value, $singleRule, $messages);
            }
            
            $validatedData[$field] = $value;
        }
        
        return $validatedData;
    }

    /**
     * 验证单个规则
     * @param string $field 字段名
     * @param mixed $value 字段值
     * @param string $rule 规则
     * @param array $messages 错误消息
     * @throws \Exception
     */
    private function validateSingleRule($field, $value, $rule, $messages)
    {
        $ruleParts = explode(':', $rule);
        $ruleName = $ruleParts[0];
        $ruleValue = $ruleParts[1] ?? null;
        
        switch ($ruleName) {
            case 'integer':
                if (!is_numeric($value) || !is_int($value + 0)) {
                    throw new \Exception($messages["{$field}.integer"] ?? "{$field}必须为整数");
                }
                break;
                
            case 'string':
                if (!is_string($value)) {
                    throw new \Exception($messages["{$field}.string"] ?? "{$field}必须为字符串");
                }
                break;
                
            case 'min':
                if (is_numeric($value) && $value < (int)$ruleValue) {
                    throw new \Exception($messages["{$field}.min"] ?? "{$field}不能小于{$ruleValue}");
                }
                break;
                
            case 'max':
                if (is_numeric($value) && $value > (int)$ruleValue) {
                    throw new \Exception($messages["{$field}.max"] ?? "{$field}不能大于{$ruleValue}");
                } elseif (is_string($value) && strlen($value) > (int)$ruleValue) {
                    throw new \Exception($messages["{$field}.max"] ?? "{$field}长度不能超过{$ruleValue}");
                }
                break;
                
            case 'in':
                $allowedValues = explode(',', $ruleValue);
                if (!in_array($value, $allowedValues)) {
                    throw new \Exception($messages["{$field}.in"] ?? "{$field}值无效");
                }
                break;
                
            case 'date_format':
                if (!$this->validateDateFormat($value, $ruleValue)) {
                    throw new \Exception($messages["{$field}.date_format"] ?? "{$field}日期格式错误");
                }
                break;
        }
    }

    /**
     * 验证日期格式
     * @param string $value 日期值
     * @param string $format 日期格式
     * @return bool
     */
    private function validateDateFormat($value, $format)
    {
        $date = \DateTime::createFromFormat($format, $value);
        return $date && $date->format($format) === $value;
    }
}
