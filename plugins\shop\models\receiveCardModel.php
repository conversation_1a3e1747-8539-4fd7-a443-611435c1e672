<?php

namespace plugins\shop\models;

use core\lib\redisCached;
use Exception;

class receiveCardModel extends baseModel
{
    public string $table = 'receive_card';

    public static array $paras_list = [
        // 基本信息
        "card_number"             => "收款卡号|required",
        "bank_name"               => "收款银行|required",
        "receive_platform"        => "收款平台|required",
        "main_receive_account_id" => "主账户ID|required",
        "receive_account_id"      => "子账户ID|required",
        "currency"                => "收款币种|required",
        "use_platform"            => "使用平台",
        "card_status"             => "状态|required",
        "remark"                  => "备注",
    ];

    public function __construct()
    {
        parent::__construct();
    }


    // 数据校验
    public function dataValidCheck(array $data, array $param_list = [], $is_throw = true, array $error = []): array
    {
        if (empty($param_list)) $param_list = self::$paras_list;
        return parent::dataValidCheck($data, $param_list);
    }

    // 获取列表
    public function getList($param, $order = 'id desc', $is_export = false)
    {
        $this->db->table($this->table, 'rc')
            ->field('rc.*, s.id as shop_id, s.shop_number')
            ->leftJoin('relations', 'r', "r.from_table = 'shop' AND r.to_table = 'receive_card' and r.to_id = rc.id")
            ->leftJoin('shop', 's', 's.id = r.from_id')
            ->where('1=1');

        // 固定筛选
        if (!empty($param['ids'])) {
            $this->db->whereIn('rc.id', $param['ids']);
        }
        // 卡号
        if (!empty($param['card_number'])) {
            $this->db->andWhere('rc.card_number = :card_number', ['card_number' => $param['card_number']]);
        }
        // 银行名称
        if (!empty($param['bank_name'])) {
            $this->db->andWhere('rc.bank_name = :bank_name', ['bank_name' => $param['bank_name']]);
        }
        // 收款平台
        if (!empty($param['receive_platform'])) {
            $this->db->andWhere('rc.receive_platform = :receive_platform', ['receive_platform' => $param['receive_platform']]);
        }
        // 状态
        if (isset($param['card_status'])) {
            $this->db->andWhere('rc.card_status = :card_status', ['card_status' => $param['card_status']]);
        }
        // 主账户
        if (!empty($param['main_receive_account_id'])) {
            $this->db->andWhere('rc.main_receive_account_id = :main_receive_account_id', ['main_receive_account_id' => $param['main_receive_account_id']]);
        }
        // 子账户
        if (!empty($param['receive_account_id'])) {
            $this->db->andWhere('rc.receive_account_id = :receive_account_id', ['receive_account_id' => $param['receive_account_id']]);
        }
        // 币种
        if (!empty($param['currency'])) {
            $this->db->andWhere('rc.currency = :currency', ['currency' => $param['currency']]);
        }
        // 使用平台
        if (!empty($param['use_platform'])) {
            $this->db->andWhere('rc.use_platform = :use_platform', ['use_platform' => $param['use_platform']]);
        }
        // 操作时间
        if (!empty($param['update_time'])) {
            $this->db->andWhere('rc.updated_at >= :update_time_start and rc.updated_at <= :update_time_end', [
                'update_time_start' => $param['update_time'][0],
                'update_time_end'   => $param['update_time'][1],
            ]);
        }

        $this->db->order($order);

        if (!$is_export && !empty($param['page']) && !empty($param['page_size'])) {
            $list = $this->db->pages($param['page'], $param['page_size']);
            $maps = self::getMaps();
            foreach ($list['list'] as &$item) {
                $item = $this->formatItem($item, $maps);
            }
            return $list;
        } else {
            $list = $this->db->list();
            if ($is_export) {
                $export_data = [];
                $maps = self::getMaps();
                $paras_list = static::$paras_list;
                $paras_list['main_receive_account_name'] = '主账户';
                $paras_list['receive_account_name'] = '子账户';
                foreach ($list as $item) {
                    $item = $this->formatItem($item, $maps);
                    // 使用参数对应的中文作为键名返回
                    $export_data[] = self::changeToCnKey($item, $paras_list);
                }
                return $export_data;
            }

            return $list;
        }
    }

    public function formatItem($item, $maps = [])
    {
        $users = $maps['users'] ?? [];
        if (empty($maps['users'])) {
            $users = redisCached::getUserInfo();
            $users = array_column($users, 'user_name', 'user_id');
        }

        $receive_account = $maps['receive_account'] ?? [];
        if (empty($maps['receive_account'])) {
            $receive_account = redisCached::getReceiveAccountAll();
            $receive_account = array_column($receive_account, 'account_name', 'id');
        }

        $maps = [
            ['name' => 'operator_name', 'maps' => $users, 'key' => 'operator'],
            ['name' => 'receive_account_name', 'maps' => $receive_account, 'key' => 'receive_account_id'],
            ['name' => 'main_receive_account_name', 'maps' => $receive_account, 'key' => 'main_receive_account_id'],
        ];
        return parent::formatItem($item, $maps);
    }

    public static function getMaps()
    {
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        $receive_account = redisCached::getReceiveAccountAll();
        $receive_account = array_column($receive_account, 'account_name', 'id');

        return [
            'users' => $users,
            'receive_account' => $receive_account,
        ];

    }

    // 根据ID获取数据
    public function getById($id)
    {
        if (empty($id)) {
            return null;
        }
        
        $item = $this->db->table($this->table)
            ->where('id = :id', ['id' => $id])
            ->one();
            
        return $this->formatItem($item);
    }
    
    // 根据卡号获取数据
    public function getByCardNumber($card_number, $id = null)
    {
        if (empty($card_number)) {
            return null;
        }
        
        $this->db->table($this->table)
            ->where('card_number = :card_number', ['card_number' => $card_number]);

        if ($id) {
            $this->db->andWhere('id != :id', ['id' => $id]);
        }
        $item = $this->db->one();
            
        return $item;
    }
    
    // 添加数据
    public function add($data, $type = '新增')
    {
        $receiveAccountId = $data['receive_account_id'] ?? null;
        $id = parent::add($data, $type);

        // 创建关联关系
        $relationModel = new relationModel();
        if ($receiveAccountId) {
            $relationModel->updateRelation($this->table, $id, 'receive_account', $receiveAccountId);
        }
        return $id;
    }
    
    // 编辑数据
    public function edit($data, $id, $old_data = [], $type = '编辑', $remark = '', $result = '', $other_attach = [])
    {
        $receiveAccountId = $data['receive_account_id'] ?? null;

        // 创建关联关系
        $relationModel = new relationModel();
        if ($receiveAccountId) {
            $relationModel->updateRelation($this->table, $id, 'receive_account', $receiveAccountId);
        }

        parent::edit($data, $id, $old_data, $type, $remark, $result, $other_attach);
    }
        
    // 批量编辑
    public function editBatch($data)
    {
        $db = $this->db->table($this->table);
        $ids = array_column($data, 'id');
        $db->whereIn('id', $ids);
        $list = $db->list();
        $list = array_column($list, null, 'id');

        $error_ids = [];
        foreach ($data as $item) {
            try {
                $item_id = $item['id'];
                unset($item['id']);
                $this->dataValidCheck($item);
                // 唯一性校验
                $detail = $this->getByCardNumber($item['card_number'], $item_id);
                if ($detail) {
                    throw new Exception('收款卡号已存在');
                }
                self::edit($item, $item_id, $list[$item_id], '批量编辑');
            } catch (Exception $e) {
                $error_ids[] = [
                    'id' => $item_id,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $error_ids;
    }
}
