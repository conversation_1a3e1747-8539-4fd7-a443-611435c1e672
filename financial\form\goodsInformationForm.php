<?php


namespace financial\form;

use core\lib\db\dbFMysql;

class goodsInformationForm
{
    public static array $goods_cate_list = [];

    /**
     * 获取分类名称
     * @param array $cate_ids 分类ID数组
     * @param int $type 类型，0表示递归获取，1表示不递归
     * @return array 返回包含分类信息的数组
     * @throws \core\lib\ExceptionError
     */
    public static function getGoodsCate(array $cate_ids, $type = 0) {
        if (!count($cate_ids)) {
            return [];
        }
        if (count(self::$goods_cate_list) == 0) {
            $db = dbFMysql::getInstance();
            $all_cate = $db->table('goods_category')
                            ->where('where is_delete = 0')
                            ->list();
            self::$goods_cate_list = $all_cate;
        } else {
            $all_cate = self::$goods_cate_list;
        }

        $data = [];
        foreach ($cate_ids as $v) {
            foreach ($all_cate as $v1) {
                if ($v1['cid'] == $v) {
                    $data[] = $v1;
                }
            }
        }

        $result = [];
        foreach ($data as $v) {
            // 递归获取分类名称
            if (!$type) {
                $name_array = self::getCateAllName($all_cate, $v['parent_cid'], [$v['title']]);
            } else {
                $name_array = [$v['title']];
            }
            $result[$v['cid']] = [
                'id' => $v['id'],
                'cid' => $v['cid'],
                'parent_cid' => $v['parent_cid'],
                'name' => implode('/', array_reverse($name_array))
            ];
        }
        return $result;
    }

    // 递归获取完整分类名称
    private static function getCateAllName($all_cate, $pid, $name_list) {
        if ($pid > 0) {
            foreach ($all_cate as $cate) {
                if ($cate['cid'] == $pid) {
                    $name_list[] = $cate['title'];
                    if ($cate['parent_cid'] > 0) {
                        $name_list = self::getCateAllName($all_cate, $cate['parent_cid'], $name_list);
                    }
                }
            }
        }
        return $name_list;
    }
}