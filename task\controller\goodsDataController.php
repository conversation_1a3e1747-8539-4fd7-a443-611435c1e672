<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/12 14:09
 */

namespace task\controller;

use core\lib\db\dbFMysql;
use core\lib\rediskeys;
use financial\common\lingXingApiBase;
use financial\form\customColumnForm;
use financial\form\tableDataForm;
use financial\models\checkoutModel;
use task\form\goodsDataForm;

class goodsDataController extends lingXingApiBase
{
    public function __construct()
    {
        $token = $_POST['token']??'';
        if ($token != '01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0') {
            returnSuccess($_POST,'token验证失败');
        }
    }
    //增量数据重新匹配
    public function countAginGoodsData() {
        $date = $_POST['date'] ?? '';
        if (!$date) {
            SetReturn(2,'请设置月份');
        }
        $year = date('Y',strtotime($date.'-01'));
        $redis_key = rediskeys::$oa_count_goods_stock.$date;
        checkoutModel::verifyLockShellNo([$date]);
        $redis = (new \core\lib\predisV())::$client;
//        $redis->del($redis_key);
//        SetReturn(2,'同步成功');
        if ($redis->exists($redis_key)) {
            $r_data = json_decode($redis->get($redis_key),true);
            $page = $r_data['page'] + 1;
        } else {
            $page = 1;
        }
        $offset = ($page - 1)*500;
        $dbF = dbFMysql::getInstance();
        $goods_list = $dbF->table('goods_data_'.$year)
            ->where('where m_date=:m_date',['m_date'=>$date])
            ->field('id,import_id,store_name,project_name,yunying,country,asin,p_asin,sku,goods_name,row_num,key1,key9,key20,key21,key7')
            ->pages($page,500);
        $list = $goods_list['list'];
        if (!count($list)) {
            $redis->del($redis_key);
            if ($page == 1) {
                SetReturn(2,'本月还没增量数据导入');
            } else {
                SetReturn(2,'增量数据：重新匹配成功');
            }
        } else {
            if ($page == 1) {
                //清除之前匹配的结果
                $goods_data_ = $dbF->table('goods_data_import')
                    ->where('where report_date = :m_date',['m_date'=>$date])
                    ->field('id')
                    ->list();
                $import_id_ids = array_column($goods_data_,'id');
                $dbF->table('goods_data_'.$year)
                    ->where('where m_date = :m_date',['m_date'=>$date])
                    ->update(['is_error'=>1,'base_id'=>0,'yunying_id'=>0,'project_id'=>0,'country_code'=>'','sid'=>'']);
                $dbF->table('goods_data_import_error_log')
                    ->whereIn('import_id',$import_id_ids)
                    ->delete();
            }
            goodsDataForm::countAginGoodsData($list,$date,$year);
            $offset = ($page - 1)*500 + count($list);
            $msg = "增量数据重新配（{$date}月）：已匹配{$offset}条，总共{$goods_list['total']}条";
            if ($offset > $goods_list['total']) {
                $redis->del($redis_key);
                SetReturn(2,$msg);
            } else {
                $r_data = [
                    'page'=>$page,
                    'total'=>$goods_list['total'],
                    'm_date'=>$date,
                    'message'=>$msg
                ];
                $redis->set($redis_key,json_encode($r_data));
                SetReturn(0,$msg);
            }
        }
    }
    //库存数据重新匹配
    public function countAginGoodsStock() {
        $date = $_POST['date'] ?? '';
        if (!$date) {
            SetReturn(2,'请设置月份');
        }
        $year = date('Y',strtotime($date.'-01'));
        checkoutModel::verifyLockShellNo([$date]);
        $redis_key = rediskeys::$oa_count_goods_stock.$date;
        $redis = (new \core\lib\predisV())::$client;
        if ($redis->exists($redis_key)) {
            $r_data = json_decode($redis->get($redis_key),true);
            $page = $r_data['page'] + 1;
        } else {
            $page = 1;
        }
        $dbF = dbFMysql::getInstance();
        $goods_list = $dbF->table('goods_stock_'.$year)
            ->where('where m_date=:m_date',['m_date'=>$date])
            ->field('id,import_id,store_name,project_name,yunying,country,asin,p_asin,sku,goods_name,row_num,fbm_local_num,fbm_local_overseas_num,fbm_local_overseas_price')
            ->pages($page,500);
        $list = $goods_list['list'];
        if (!count($list)) {
            $redis->del($redis_key);
            if ($page == 1) {
                SetReturn(2,'本月还没库存数据导入');
            } else {
                SetReturn(2,'库存数据（{$date}月）：重新匹配成功');
            }
        } else {
            if ($page == 1) {
                //清除之前匹配的结果
                $goods_data_ = $dbF->table('goods_stock_import')
                    ->where('where report_date = :m_date',['m_date'=>$date])
                    ->field('id')
                    ->list();
                $import_id_ids = array_column($goods_data_,'id');
                $dbF->table('goods_stock_'.$year)
                    ->where('where m_date = :m_date',['m_date'=>$date])
                    ->update(['is_error'=>1,'base_id'=>0,'yunying_id'=>0,'project_id'=>0,'country_code'=>'','sid'=>'']);
                $dbF->table('goods_stock_import_error_log')
                    ->whereIn('import_id',$import_id_ids)
                    ->delete();
            }
            goodsDataForm::countAginGoodsStock($list,$date,$year);
            $offset = ($page - 1)*500 + count($list);
            $msg = "库存数据重新配（{$date}月）：已匹配{$offset}条，总共{$goods_list['total']}条";
            if ($offset > $goods_list['total']) {
                $redis->del($redis_key);
                SetReturn(2,$msg);
            } else {
                $r_data = [
                    'page'=>$page,
                    'total'=>$goods_list['total'],
                    'm_date'=>$date,
                    'message'=>$msg
                ];
                $redis->set($redis_key,json_encode($r_data));
                SetReturn(0,$msg);
            }
        }
    }
    //统计生成领星最小维度数据
    public function countMskuMonthData() {
        ini_set('memory_limit', '500M');
        $redis_key = rediskeys::$oa_count_msku_month;
        $redis = (new \core\lib\predisV())::$client;
        if (!$redis->exists($redis_key)) {
            SetReturn(2,'结账已完成');
        }
        $r_data = json_decode($redis->get($redis_key),true);
        $date = $r_data['month']??'';
        if (!$date) {
            SetReturn(2,'请设置月份');
        }
        $year = date('Y',strtotime($date.'-01'));
        checkoutModel::verifyLockShell([$date]);
        $from = new tableDataForm();
        //领星数据
        $field = ['sid','asin','msku','parentAsin','localSku','countryCode','project_id','yunying_id','supplier_id'];
        $group = ['sid','asin','msku','parentAsin','localSku','countryCode','project_id','yunying_id','supplier_id'];
        $lx_list = $from::getLxData($field,$group,$date,$year);
        if (!count($lx_list)) {
            SetReturn(2,'没有报表数据');
        }
        //自定义数据
        $from::arrangeList($lx_list,$date,$year);
        $r_data['message'] = $date.'月数据结账中。当前进度：自定义字段计算中';
        $redis->set($redis_key,json_encode($r_data));
        $redis->expire($redis_key,4*60*60);
        //计算自定义字段
        customColumnForm::recalculate(1,[],$date);
        SetReturn(2,'计算完成');
    }
    //统计生成领星最小维度数据(原始数据)
    public function countMskuMonthOriginalData() {
        $date = $_POST['m_date']??'';
        if (!$date) {
            SetReturn(2,'请设置月份');
        }
        $year = date('Y',strtotime($date.'-01'));
        //领星数据
        tableDataForm::oldGetLxData($date,$year);
        SetReturn(2,'计算完成');
    }
}