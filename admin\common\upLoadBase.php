<?php
/**
 * @author: zhangguoming
 * @Time: 2024/3/4 9:38
 */

namespace admin\common;

use FFMpeg\FFMpeg;

class upLoadBase
{
    //允许上传的文件
    public static array $allow_suffix = ['jpg','jpeg', 'png', 'gif', 'pdf', 'mp3', 'mp4', 'xls', 'xlsx', 'doc', 'docx', 'ppt', 'pptx', 'txt', 'stp', 'bip', 'psd', 'ai','avi', 'max', 'fbx', 'obj', 'tiff', '3ds', 'dxf', 'bmp', 'rar', 'zip','step','stl'];
    //图片
    public static array $img_suffix = ['jpg','jpeg', 'png', 'gif'];
    //视频
    public static array $video_suffix = ['mp4'];
    //其他
    public static array $other_suffix = ['pdf', 'mp3', 'xls', 'xlsx', 'doc', 'docx', 'ppt', 'pptx', 'txt'];
    //文件大小验证
    public static function varifyFileSize ($ext_suffix, $size) {
        if (in_array($ext_suffix, self::$img_suffix)) {
            //图片
            if ($size > 50 * 1024 * 1024) {
                SetReturn(-1, '文件大小不能超过50M');
            }
        } elseif (in_array($ext_suffix, self::$video_suffix)) {
            //视频
            if ($size > 50 * 1024 * 1024) {
                SetReturn(-1, '文件大小不能超过50M');
            }
        }
    }
    //压缩，缩放土图片
    public static function getThumbImg($absolute_path, $ext_suffix) {
        if (!in_array($ext_suffix, self::$img_suffix)) {
            return '';
        }
        //判断真实的格式是否同后缀一样
        $imageType = mime_content_type($absolute_path);
        $thumb_src = '/public/upload/thumb/images/'.date('Ymd');
        $thumb_path = SELF_FK.$thumb_src;
        if (!file_exists($thumb_path)) {
            mkdir($thumb_path, 0777, true);
        }
        $thumb_src .= ('/'.date('YmdHis', time()) . rand(100, 1000).'.'.$ext_suffix);
        $thumb_url = SELF_FK .$thumb_src;
        // 加载原始图像
        switch ($ext_suffix) {
            case 'png':
                $original = imagecreatefrompng($absolute_path);
                break;
            case 'gif':
                $original = imagecreatefromgif($absolute_path);
                break;
            case 'jpg':
            case 'jpeg':
                $original = imagecreatefromjpeg($absolute_path);
                break;
            default:
                return '';
        }

        // 获取原始图像的宽度和高度
        $originalWidth = imagesx($original);
        $originalHeight = imagesy($original);
        // 缩略图宽度和高度(等比例缩放)
        $thumbnailWidth = 200;
        $scale = $thumbnailWidth / $originalWidth;
        // 计算缩略图的缩放比例
        //$scale = min($thumbnailWidth / $originalWidth, $thumbnailHeight / $originalHeight);
//        // 计算缩放后的宽度和高度
        $thumbnailWidth = $originalWidth * $scale;
        $thumbnailHeight = $originalHeight * $scale;

        // 创建缩略图图像
        $thumbnail = imagecreatetruecolor($thumbnailWidth, $thumbnailHeight);

        // 缩放并复制图像到缩略图
        imagecopyresampled($thumbnail, $original, 0, 0, 0, 0, $thumbnailWidth, $thumbnailHeight, $originalWidth, $originalHeight);

        // 保存缩略图到文件
        switch ($ext_suffix) {
            case 'jpg':
            case 'jpeg':
                imagejpeg($thumbnail,$thumb_url);
                break;
            case 'png':
                imagepng($thumbnail,$thumb_url);
                break;
            case 'gif':
                imagegif($thumbnail, $thumb_url);
                break;
            default:
                return '';
        }
        // 释放内存
        imagedestroy($original);
        imagedestroy($thumbnail);
        return [
            'thumb_url'=>$thumb_src,
        ];
    }
    //压缩，缩放视频，提取视频封面图
    public static function getThumbVideo($path,$suffix){
//        $ffmpeg = FFMpeg::create([
//            'ffmpeg.binaries'  => 'D:\app\ffmpeg\bin\ffmpeg.exe',
//            'ffprobe.binaries' => 'D:\app\ffmpeg\bin\ffprobe.exe',
//        ]);
        $ffmpeg = FFMpeg::create();
        $video = $ffmpeg->open($path);
        //获取第几帧图片
        $frame = $video->frame(\FFMpeg\Coordinate\TimeCode::fromSeconds(4));
        $cover_src = '/public/upload/video_cover/'.date('Ymd');
        $cover_path = SELF_FK.$cover_src;
        if (!file_exists($cover_path)) {
            mkdir($cover_path, 0777, true);
        }
        $file_name = date('YmdHis', time()) . rand(100, 1000);
        $cover_src = $cover_src.'/'.$file_name.'.jpg';
        $absolute_path = SELF_FK.$cover_src;
        $frame->save($absolute_path);

        //缩放视频
        $dimension = new \FFMpeg\Coordinate\Dimension(1280, 720);
        $mode =  \FFMpeg\Filters\Video\ResizeFilter::RESIZEMODE_INSET;
        $video->filters()->resize($dimension, $mode);

        $thumb_src = '/public/upload/thumb/video/'.date('Ymd');
        $thumb_path = SELF_FK.$thumb_src;
        if (!file_exists($thumb_path)) {
            mkdir($thumb_path, 0777, true);
        }
        $thumb_src .= ('/'.date('YmdHis', time()) . rand(100, 1000).'.'.$suffix);
        $absolute_path_ = SELF_FK .$thumb_src;

        $video->save(new \FFMpeg\Format\Video\X264(), $absolute_path_);
        return [
            'cover_image'=>$cover_src,
            'thumb_url'=>$thumb_src,
        ];
    }

}