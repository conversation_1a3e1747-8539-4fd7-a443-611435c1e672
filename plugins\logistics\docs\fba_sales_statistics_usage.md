# FBA销量统计功能使用说明

## 功能概述

FBA销量统计功能用于从利润统计数据计算销量并更新到FBA库存汇总表，实现销量数据的自动统计和配置驱动的日均销量计算。

## 核心功能

### 1. 销量数据计算
- **7天销量**: 计算过去7天的销量均值，然后乘以7得到总销量
- **14天销量**: 计算过去14天的销量均值，然后乘以14得到总销量  
- **30天销量**: 计算过去30天的销量均值，然后乘以30得到总销量
- **前10天明细**: 按日期存储JSON格式的销量明细
- **日均销量**: 根据产品阶段配置的权重计算加权平均值

### 2. 配置驱动计算
- 支持多产品阶段的差异化权重配置
- 根据ASIN+国家获取产品阶段信息
- 灵活的权重配置系统

### 3. 数据更新
- 批量更新FBA库存汇总表的销量字段
- 支持多维度数据聚合（店铺级、站点级、SKU级、ASIN级）
- 完整的错误处理和日志记录

## 数据流程

```
利润统计数据 → 销量计算 → 配置权重 → 日均销量 → FBA库存汇总表
     ↓              ↓          ↓          ↓           ↓
lingxing_profit → 7/14/30天 → normal_ → 加权平均 → fba_storage_
statistics_msku   销量均值    prepare    销量      summary
```

## API接口

### 更新FBA销量数据

**接口地址：** `task/controller/logisticsController::updateFbaSalesData`

**请求方式：** POST

**请求参数：**
```json
{
    "sync_date": "2025-07-03",    // 可选，同步日期，默认为今天
    "batch_size": 100             // 可选，批处理大小，默认为100
}
```

**响应示例：**
```json
{
    "code": 0,
    "message": "销量数据更新完成，成功处理 150 条记录",
    "data": {
        "sync_date": "2025-07-03",
        "success_count": 150,
        "error_count": 0,
        "error_list": []
    }
}
```

## 配置管理

### normal_prepare配置结构

```json
[
  {
    "type": "1",
    "sales_config": {
      "sales_7": "60",
      "sales_14": "20", 
      "sales_30": "20"
    },
    "data_range": [
      {"country": "US", "min_value": 1, "max_value": 3, "normal_value": 2}
    ]
  }
]
```

### 配置字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| type | string | 产品阶段：1新品期，2成长期，3稳定期，4衰退期，5清货 |
| sales_config | object | 销量权重配置 |
| sales_7 | string | 7天销量权重（百分比） |
| sales_14 | string | 14天销量权重（百分比） |
| sales_30 | string | 30天销量权重（百分比） |
| data_range | array | 国家范围配置（预留字段） |

### 日均销量计算公式

```
日均销量 = (7天销量均值 × sales_7权重) + (14天销量均值 × sales_14权重) + (30天销量均值 × sales_30权重)
```

### 各产品阶段权重策略

- **新品期(1)**: 重视近期销量 (7天60%, 14天20%, 30天20%)
- **成长期(2)**: 平衡近期和中期 (7天50%, 14天25%, 30天25%)  
- **稳定期(3)**: 更平衡的权重 (7天40%, 14天30%, 30天30%)
- **衰退期(4)**: 重视最近趋势 (7天70%, 14天15%, 30天15%)
- **清货期(5)**: 重视长期趋势 (7天30%, 14天35%, 30天35%)

## 数据库表结构

### 更新的字段

FBA库存汇总表 (`fba_storage_summary`) 中的销量统计字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| sales_7days_qty | int(11) | 7天销量 |
| sales_14days_qty | int(11) | 14天销量 |
| sales_30days_qty | int(11) | 30天销量 |
| sales_10days_detail | text | 前10天销量明细JSON |
| daily_avg_sales_qty | decimal(8,2) | 日均销量 |

### 前10天销量明细JSON格式

```json
[
  {"date": "2025-07-02", "quantity": 12},
  {"date": "2025-07-01", "quantity": 8},
  {"date": "2025-06-30", "quantity": 15}
]
```

## 使用方法

### 1. 初始化配置

```sql
-- 执行配置初始化脚本
source plugins/logistics/sql/init_normal_prepare_config.sql;
```

### 2. 手动执行

```php
// 调用控制器方法
$controller = new logisticsController();
$result = $controller->updateFbaSalesData();
```

### 3. 定时任务

建议设置每日定时执行：

```bash
# 添加到crontab，每日凌晨3点执行
0 3 * * * /usr/bin/php /path/to/oa-api/task/controller/logisticsController.php updateFbaSalesData
```

### 4. 批量处理

对于大量数据，建议分批处理：

```php
$batchSize = 100;
$syncDate = '2025-07-03';

$result = $controller->updateFbaSalesData([
    'sync_date' => $syncDate,
    'batch_size' => $batchSize
]);
```

## 监控和日志

### 日志记录

系统会记录详细的处理日志：

- 处理开始和结束时间
- 成功和失败的记录数量
- 详细的错误信息
- 计算过程的调试信息

### 性能监控

- 批处理大小可调节（建议100-500条）
- 内存使用监控
- 处理时间统计

## 故障排除

### 常见问题

1. **配置未找到**
   - 检查 `oa_l_config` 表中是否存在 `normal_prepare` 配置
   - 执行配置初始化脚本

2. **数据关联失败**
   - 检查 `listing_data` 表中的 `product_stage` 字段
   - 验证ASIN和国家代码的匹配

3. **计算结果异常**
   - 检查利润统计数据的完整性
   - 验证日期范围和查询条件

### 调试方法

```php
// 启用调试日志
log::lingXingApi('FbaSalesStatistics')->debug("调试信息");

// 检查配置
$model = new fbaSalesStatisticsModel();
$config = $model->getNormalPrepareConfig();
var_dump($config);
```

## 扩展功能

### 自定义权重配置

可以通过修改 `normal_prepare` 配置来调整不同产品阶段的权重策略。

### 新增产品阶段

在配置中添加新的 `type` 即可支持新的产品阶段。

### 国家特定配置

`data_range` 字段预留了国家特定的配置能力，可用于未来的功能扩展。
