<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/15 14:11
 */
namespace  plugins\goods\controller;

use plugins\goods\common\authenticationCommon;
use plugins\goods\form\configFrom;
use plugins\goods\form\goodsAbnormalFrom;
use plugins\goods\form\goodsAbnormalLogFrom;
use plugins\goods\form\goodsCateFrom;
use plugins\goods\form\goodsColorRelationFrom;
use plugins\goods\form\goodsFunctionFrom;
use plugins\goods\form\goodsImgsFrom;
use plugins\goods\form\goodsMattersFrom;
use plugins\goods\form\goodsNewFrom;
use plugins\goods\form\goodsProjectFrom;
use plugins\goods\form\goodsProjectParticipantFrom;
use plugins\goods\form\messagesFrom;
use plugins\goods\form\templateFrom;
use plugins\goods\models\goodsAttachmentModel;
use plugins\goods\models\goodsColorRelationModel;
use plugins\goods\models\goodsPurchaseModel;
use plugins\goods\models\userModel;
use core\lib\db\dbMysql;
use plugins\goods\models\goodsNewModel;
use core\lib\ExceptionError;
use core\lib\log;

class goodsNewController
{
    //获取新品列表
    public function getList(){
        $paras_list = array('goods_name', 'manage_wid','manage_name', 'supplier_id', 'function_id', 'cat_id', 'is_app', 'order_by', 'page', 'page_size');
        $param = arrangeParam($_POST, $paras_list);
        $where_str = 'where a.is_delete=0';
        $where_array = [];
        if (!empty($param['goods_name'])) {
            $where_str .= ' and a.goods_name like :goods_name';
            $where_array['goods_name'] = '%'.$param['goods_name'].'%';
        }
        if (!empty($param['manage_name'])) {
            $where_str .= " and a.manage_info->'$[*].wname' like :manage_name";
            $where_array['manage_name'] = '%'.$param['manage_name'].'%';
        }
//        if (!empty($param['manage_wid'])) {
//            $where_str .= " and a.manage_info->'$[*].wid' like :manage_wid";
//            $where_array['manage_wid'] = '%'.$param['manage_wid'].'%';
//        }
        if (!empty($param['supplier_id'])) {
            $where_str .= " and a.supplier_id=:supplier_id";
            $where_array['supplier_id'] = $param['supplier_id'];
        }
        if (!empty($param['function_id'])) {
            $function_id = explode(',',$param['function_id']);
            $where_str .= ' and JSON_CONTAINS(a.function_id, :function_id)';
            $where_array['function_id'] = json_encode($function_id);
        }
        if (!empty($param['cat_id'])) {
            $cat_ids = json_decode($param['cat_id']);
            if (count($cat_ids)) {
                $where_str .= " and (";
                $cat_where = "";
                foreach ($cat_ids as $k=>$v) {
                    $cat_where .= "JSON_CONTAINS(a.cat_id, :cat_id_$k) or ";
                    $where_array["cat_id_$k"] = json_encode([$v]);
                }
                $cat_where = rtrim($cat_where,'or ');
                $where_str .= "$cat_where)";
            }
        }
        if ((int)$param['is_app'] > -1) {
            $where_str .= ' and a.is_app = :is_app';
            $where_array['is_app'] = $param['is_app'];
        }
        //超管才能窜下管理员和运维
        if (!userModel::isSupreOrManage()) {
            if (userModel::isKaifaOrYunying()) {
                $where_str .= " and (a.manage_info like :current_user_id or a.operator_info like :current_user_id)";
                $where_array['current_user_id'] = '%"id":"'.userModel::$qwuser_id.'",%';
            }
        }
        $db = dbMysql::getInstance();
        $db->table('goods_new','a');
        $db->leftJoin('goods_code', 'ac','ac.goods_id = a.id');
        $db->leftJoin('goods_supplier', 'gs','gs.id = a.supplier_id');
        $db->where($where_str, $where_array);
        //查看绑定的模板
        $db->field('a.id,a.goods_img,a.thumb_src,a.goods_name,a.e_name,a.is_app,a.bluetooth,a.operator_info,a.manage_info,a.created_at,a.cat_id,ac.code,ac.protocol_code,gs.supplier_name,a.function_id');
        //非超管，不显示供应商
        if (!userModel::isManageDeveloper()) {
            $db->field('a.id,a.goods_img,a.thumb_src,a.goods_name,a.e_name,a.is_app,a.bluetooth,a.operator_info,a.manage_info,a.created_at,a.cat_id,ac.code,ac.protocol_code,"-" as supplier_name,a.function_id');
        }
        $order_str = '';
        if (!empty($param['order_by'])) {
            $order_by = json_decode($param['order_by'],true);
            $order_str = '';
            foreach ($order_by as $k=>$ord){
                $order_str .= 'a.'.$k.' '.$ord.',';
            }
        }
        if(empty($order_str)) {
            $db->order('a.id desc');
        } else {
            $db->order(trim($order_str,','));
        }
        $data = $db->pages($param['page'], $param['page_size']);

        foreach ($data['list'] as &$v) {
            //根据分id查询分类名称
            $v['cat_data'] = goodsCateFrom::getGoodsCate(json_decode($v['cat_id']),1);
            //当前用户是否为产品的操作员
            $v['is_operator'] = 0;
            if ($v['operator_info']) {
                $operator_info_wid = array_column(json_decode($v['operator_info'],true),'wid');
                if (in_array(userModel::$wid,$operator_info_wid)) {
                    $v['is_operator'] = 1;
                }
            }
        }
        //获取颜色列表
        $data['list'] = goodsColorRelationFrom::getImgsGoodsListColor($data['list']);
        //获取列表的图片
        $data['list'] = goodsImgsFrom::getGoodsImagesForList($data['list']);
        //功能
        $data['list'] = goodsImgsFrom::getGoodsFunctionForList($data['list']);
        returnSuccess($data);
    }
    //获取新品列表
    public function getCreatedList(){
        $db = dbMysql::getInstance();
        $db->table('goods_new');
        $db->with('goods_color_relation as a',
            [['a.goods_id','id']],
            'a.id,a.color_id,a.has_handset,b.color_name,b.color_name_en','a.is_delete = 0',
            'goods_color',
            [['table'=>'goods_color as b','join'=>'left join','on'=>'b.id=a.color_id']]);
        $db->where("where is_delete = 0 and goods_name <> ''");
        $db->order('id desc');
        $db->field('id,goods_name');
        $data = $db->list();
        returnSuccess($data);
    }
    //新增,修改新品
    public function editNewGoods()
    {
        $paras_list = ['id', 'goods_name', 'alias_name', 'is_app', 'is_electriferous', 'goods_img', 'cat_id', 'color_id', 'function_id', 'description','operator_info','manage_info','supplier_id','goods_purchase','goods_attachment','thumb_src','handset_color_id','handset_types','goods_size'];
        $request_list = ['goods_img' => '产品图片',  'operator_info' => '运营人员', 'color_id'=>'产品颜色','function_id'=>'功能','supplier_id'=>'供应商','cat_id'=>'分类','manage_wid'=>'产品开发','thumb_src'=>'产品缩略图','handset_types'=>'遥控颜色类型','handset_color_id'=>'带遥控产品颜色'];
        //产品基本信息验证
        $length_data = ['description'=>['name'=>'产品描述','length'=>500],'alias_name'=>['name'=>'别名','length'=>50]];
        $param = arrangeParam($_POST, $paras_list, $request_list,$length_data);
        $is_app = (int)$param['is_app'];
        $param['is_app'] = $is_app;
        if ($is_app == 0) {
            if (empty($param['goods_name'])) {
                SetReturn(-1, '产品中文名称必填');
            }
        }
        $operator_info = json_decode($param['operator_info']);
        if (count($operator_info) == 0) {
            SetReturn(-1, '请选择运营人员');
        }
        $db = dbMysql::getInstance();
        //分产品开发必须上传开发信息
        if (empty($param['manage_info']) || $param['manage_info'] == '[]' || $param['manage_info'] == '[{}]') {
            $param['manage_info'] = json_encode([[
                'id'=>userModel::$qwuser_id,
                'wid'=>userModel::$wid,
                'wname'=>userModel::$wname,
//                'avatar'=>userModel::$avatar
            ]],JSON_UNESCAPED_UNICODE);
        } else {
            $manage_info = json_decode($param['manage_info'],true);
            $param['manage_info'] = json_encode($db->table('qwuser')
                ->where('where id=:id',['id'=>$manage_info[0]['id']])
                ->field('id,wid,wname,avatar')
                ->list(),JSON_UNESCAPED_UNICODE);
        }
        $handset_types = json_decode($param['handset_types'],true);
        $goods_color_ids = json_decode($param['color_id'],true);
        $handset_color_ids = json_decode($param['handset_color_id'],true);
        if (!count($handset_types)) {
            SetReturn(-1,'请选择遥控器颜色类型');
        }
        if (in_array(0,$handset_types) && !count($goods_color_ids)) {
            SetReturn(-1,'不带遥控器颜色必选');
        }
        if (in_array(1,$handset_types) && !count($handset_color_ids)) {
            SetReturn(-1,'带遥控器颜色必选');
        }
        foreach ($handset_types as $color_type) {
            if ($color_type == 0) {
                if (!count($goods_color_ids)) {
                    SetReturn(-1,'请选择不带遥控的产品颜色');
                }
            } elseif ($color_type == 1) {
                if (!count($handset_color_ids)) {
                    SetReturn(-1,'请选择带遥控的产品颜色');
                }
            } else {
                SetReturn(-1,'遥控器颜色类型错误');
            }
        }

        //产品附件验证
        $goods_attachment = goodsNewFrom::varifyAttachmentSubmit($param['goods_attachment'],$param['is_electriferous'],$param['cat_id']);
        //采购信息
        $goods_purchase = json_decode($param['goods_purchase'],true);
        $db->beginTransaction();
        try {
            //新品基础信息更新
            $goods_id = goodsNewModel::setGoodsInfo($param);
            //更新颜色信息
            goodsColorRelationModel::setGoodsColorRelation($goods_id,$handset_types,$goods_color_ids,$handset_color_ids);
            //更新采购信息
            goodsPurchaseModel::setGoodsPurchase($goods_id, $goods_purchase);
            //更新附件
            goodsAttachmentModel::setGoodsAttachment($goods_id,$goods_attachment);

        } catch (ExceptionError $e) {
            $db->rollBack();
            throw new ExceptionError($e->getMessage());
        }
        //日志记录
        $id = (int)$param['id']??0;
        \core\lib\log::setGoodsInfoLog($goods_id,$id==0?0:1);
        //待办事项事项管理
        if (!$id) {
            if ($is_app == 1) {
                $goods = goodsNewModel::$goods_info;
                if (empty($goods['e_name'])) {
                    //将suk滞空
                    goodsColorRelationModel::setColorSkuEmpty($goods_id);
                    goodsMattersFrom::addGoodsEnameMatter($goods_id);
                } elseif (empty($goods['goods_name'])) {
                    goodsMattersFrom::addGoodsNameMatter($goods_id,$goods['manage_info']);
                }
            }
        }
        $db->commit();

        SetReturn(0, '保存成功');
    }
    //设置英文名称
    public function setGoodsEname() {
        $paras_list = ['e_name', 'goods_id'];
        $request_list = ['e_name' => '英文名', 'goods_id' => '新品ID'];
        $length_data = ['e_name'=>['name'=>'英文名','length'=>50]];
        $param = arrangeParam($_POST, $paras_list, $request_list,$length_data);
        $goods_id = (int)$param['goods_id'];
        authenticationCommon::authGoodsMattersVatify($goods_id,1);
        $e_name = trim($param['e_name']);
        $e_name_ = str_replace(' ','',$e_name);//去除空格
        //蓝牙
        $bluetooth = 'J-'.$e_name_;
        $db = dbMysql::getInstance();
        $goods = $db->query('select id,manage_info,e_name,is_app from oa_goods_new where id=:goods_id',['goods_id'=>$goods_id]);
        if ($goods['is_app'] == 0) {
            SetReturn(-1,'非app产品不用取英文名');
        }
        //验证重复名称
        $goods_other = $db->query('select id from oa_goods_new where is_delete = 0 and e_name = :e_name and id <> :id',['id'=>$goods_id,'e_name'=>$e_name]);
        if ($goods_other) {
            SetReturn(-1, '该产品英文名称已存在');
        }
        $db->beginTransaction();
        try {
            //修改商品信息
            $update_data = [
                'e_name'=>$e_name,
                'goods_id'=>$goods_id,
                'bluetooth'=>$bluetooth,
                'updated_at'=>date('Y-m-d H:i:s'),
            ];
            $db->query('update oa_goods_new set e_name=:e_name,updated_at=:updated_at,bluetooth=:bluetooth where id=:goods_id',$update_data);
            //给开发待办事项
            if (empty($goods['e_name'])) {
                goodsMattersFrom::addGoodsNameMatter($goods_id,$goods['manage_info']);
            }
            //修改待办事件为已完成
            $db->query('update oa_goods_matters set status=1,completion_time=:completion_time where goods_id=:goods_id and type=1',['completion_time'=>time(),'goods_id'=>$goods_id]);
            if ($goods['e_name'] != $e_name) {
                //修改sku
                goodsColorRelationModel::setColorSku($goods_id,$e_name_);
                //日志记录
                goodsNewFrom::$update_log_data['goods_info'][] = [
                    'key_name'=>'产品英文名',
                    'data'=>(!empty($goods['e_name'])?"{$goods['e_name']}->$e_name":"{$e_name}"),
                ];
                \core\lib\log::setGoodsInfoLog($goods_id,1);
            }
            $db->commit();
            returnSuccess([],'设置英文名成功');
        }  catch (ExceptionError $e) {
            $db->rollBack();
            throw new ExceptionError($e->getMessage());
        }

    }
    //设置中文名称
    public function setGoodsName() {
        $paras_list = ['goods_name', 'goods_id'];
        $request_list = ['goods_name' => '中文名', 'goods_id' => '新品ID'];
        $length_data = ['goods_name'=>['name'=>'中文名','length'=>50]];
        $param = arrangeParam($_POST, $paras_list, $request_list, $length_data);
        $goods_id = (int)$param['goods_id'];
        authenticationCommon::authGoodsMattersVatify($goods_id,2);
        $goods_name = trim($param['goods_name']);
        $db = dbMysql::getInstance();
        $goods = $db->query('select id,goods_name,manage_info,is_app from oa_goods_new where id=:goods_id',['goods_id'=>$goods_id]);
        if ($goods['is_app'] == 0) {
            SetReturn(-1,'非app产品已有中文名');
        }
        $db->beginTransaction();
        try {
            //验证重复名称
            $goods_other = $db->query('select id from oa_goods_new where is_delete = 0 and goods_name = :goods_name and id <> :id',['id'=>$goods_id,'goods_name'=>$goods_name]);
            if ($goods_other) {
                SetReturn(-1, '该产品名称已存在');
            }
            //修改商品信息
            $update_data = [
                'goods_name'=>$goods_name,
                'goods_id'=>$goods_id,
                'updated_at'=>date('Y-m-d H:i:s'),
            ];
            $db->query('update oa_goods_new set goods_name=:goods_name,updated_at=:updated_at where id=:goods_id',$update_data);
            //修改待办事件为已完成
            $db->query('update oa_goods_matters set status=1,completion_time=:completion_time where goods_id=:goods_id and type=2',['completion_time'=>time(),'goods_id'=>$goods_id]);
            if ($goods['goods_name'] != $goods_name) {
                //修改收货编码

                //日志
                goodsNewFrom::$update_log_data['goods_info'][] = [
                    'key_name'=>'产品中文名',
                    'data'=>(!empty($goods['goods_name'])?"{$goods['goods_name']}->$goods_name":"{$goods_name}"),
                ];
                \core\lib\log::setGoodsInfoLog($goods_id,1);
            }
            $db->commit();
            returnSuccess([],'录入中文名成功');
        }  catch (ExceptionError $e) {
            $db->rollBack();
            throw new ExceptionError($e->getMessage());
        }

    }
    //获取商品详情
    public function getGoodsDetail(){
        $id = (int)$_GET['id'];
        $color_relation_id = (int)$_GET['color_relation_id'];
        if (!$id) {
            SetReturn(-1,'参数有误');
        }
        $db = dbMysql::getInstance();
        $field = 'id,bluetooth,goods_name,e_name,alias_name,is_app,goods_img,thumb_src,cat_id,manage_info,function_id,supplier_id,description,operator_info,is_electriferous,goods_size,handset_types';

        $data = $db->query('select '.$field.' from oa_goods_new where id=:id and is_delete = 0',['id'=>$id]);
        if (!$data) {
            SetReturn(-1,'新品信息不存在');
        }
        //蓝牙编码
        $goods_code = $db->query('select code,protocol_code from oa_goods_code where goods_id=:id',['id'=>$id]);
        $data['code'] = $goods_code['code'];
        $data['protocol_code'] = $goods_code['protocol_code'];
        //商品采购信息
        $goods_purchase = $db->query('select goods_id,outer_box_info,outer_box_unit,pack_info,pack_unit,single_weight,number,weight from oa_goods_purchase where goods_id=:id',['id'=>$id]);
        $data['goods_purchase'] = $goods_purchase;
        //附件
        $is_goods_manage = goodsNewFrom::isGoodsMnage($data['manage_info']);
        $is_goods_operator = goodsNewFrom::isGoodsMnage($data['operator_info']);
        $is_project_participant = goodsProjectParticipantFrom::isProjectParticipant($id);
        $data['goods_attachment'] = goodsNewFrom::getGoodsAttachmentInfo($id,$is_goods_manage,$is_goods_operator,$is_project_participant);
        //项目文件
        if ($is_project_participant) {
            $data['project_file'] = goodsNewFrom::getProjectFileInfo($id);
        } else {
            $data['project_file'] = [];
        }

        //分类
        $data['cate_list'] = goodsCateFrom::getGoodsCate(json_decode($data['cat_id'],true));
        //功能
        $data['function_list'] = goodsFunctionFrom::getGoodsFunction(json_decode($data['function_id'],true));
        //颜色
        if ($color_relation_id) {
            $data['color'] = goodsColorRelationFrom::getGoodsColor($id,[$color_relation_id]);
        } else {
            $data['color'] = goodsColorRelationFrom::getGoodsColor($id);
        }
        //遥控类型
        $data['handset_types'] = json_decode($data['handset_types']);

        //图片
        $data['images'] = goodsImgsFrom::getGoodsImages($id,[$color_relation_id]);
        if (count($data['images'])) {
            $data['goods_img'] = $data['images'][0]['img_url'];
        }
        returnSuccess($data);
    }
    //删除商品
    public function delGoods(){
        $paras_list = ['color_relation_id', 'goods_id'];
        $request_list = ['color_relation_id' => "颜色关系ID", 'goods_id' => '新品ID'];
        $param = arrangeParam($_POST, $paras_list, $request_list,);
        $goods_id = (int)$param['goods_id'];
        $color_relation_id = (int)$param['color_relation_id'];
        if (!$goods_id || !$color_relation_id) {
            SetReturn(-1,'参数有误');
        }
        $db = dbMysql::getInstance();
        $goods_project = $db->query('select id from oa_goods_project where goods_id=:goods_id and status <> 4 limit 1',['goods_id'=>$goods_id]);
        if ($goods_project) {
            SetReturn(-1,'该产品已经有项目流程在办理，不可删除');
        } else {
            $db->beginTransaction();
            try {
                //删除商品颜色
                $db->table('goods_color_relation')
                    ->where('where id=:id and goods_id=:goods_id',['id'=>$color_relation_id,'goods_id'=>$goods_id])
                    ->update(['is_delete'=>1]);
                //查询是否存在颜色
                $color_count = $db->table('goods_color_relation')
                    ->where('where goods_id=:goods_id and is_delete=0',['goods_id'=>$goods_id])
                    ->count();
                if (!$color_count) {
                    //删除编号
                    $db->table('goods_code')
                        ->where('where goods_id=:goods_id_',['goods_id_'=>$goods_id])
                        ->update(['goods_id'=>0]);
                    //删除商品
                    $db->table('goods_new')->where('where id=:goods_id',['goods_id'=>$goods_id]);
                    $db->update(['is_delete'=>1,'updated_at'=>date('Y-m-d H:i:s')]);
                    //待办事件关闭
                    $db->table('goods_matters')
                        ->where('where goods_id=:goods_id and status=0 and (type = 1 or type=2)',['goods_id'=>$goods_id])
                        ->update(['status'=>3,'close_time'=>date('Y-m-d H:i:s'),'close_reason'=>'产品删除']);
                }
                $db->commit();
                returnSuccess('','删除成功');
            } catch (ExceptionError $e) {
                $db->rollBack();
                throw new ExceptionError($e->getMessage());
            }

        }
    }
    //获取修改信息
    public function getEditData() {
        $id = (int)$_GET['id'];
        if (!$id) {
            SetReturn(-1,'参数有误');
        }

        $db = dbMysql::getInstance();
        $field = 'id,goods_name,e_name,alias_name,thumb_src,is_app,goods_img,cat_id,manage_info,function_id,supplier_id,description,operator_info,is_electriferous,bluetooth,handset_types,goods_size';

        $data = $db->query('select '.$field.' from oa_goods_new where id=:id',['id'=>$id]);
        if (!$data) {
            SetReturn(-1,'新品信息不存在');
        }
        //蓝牙编码
        $goods_code = $db->query('select code,protocol_code from oa_goods_code where goods_id=:id',['id'=>$id]);
        $data['code'] = $goods_code['code'];
        $data['protocol_code'] = $goods_code['protocol_code'];
        //商品采购信息
        $goods_purchase = $db->query('select goods_id,outer_box_info,outer_box_unit,pack_info,pack_unit,single_weight,number,weight from oa_goods_purchase where goods_id=:id',['id'=>$id]);
        $data['goods_purchase'] = $goods_purchase;
        $data['goods_purchase']['outer_box_info'] = explode('*',$goods_purchase['outer_box_info']);
        $data['goods_purchase']['pack_info'] = explode('*',$goods_purchase['pack_info']);
        //附件
        $goods_attachment = $db->queryAll('select id,type,url,file_name from oa_goods_attachment where goods_id=:id and is_delete = 0',['id'=>$id]);
        $data['goods_attachment'] = $goods_attachment;
        //颜色
        $color_id = $db->queryAll('select color_id from oa_goods_color_relation where goods_id=:id and is_delete = 0',['id'=>$id]);
        $data['color_id'] = array_column($color_id,'color_id');
        //功能和分类
        $data['cat_id'] = json_decode($data['cat_id']);
        $data['function_id'] = json_decode($data['function_id']);
        //颜色
        $data['color'] = goodsColorRelationFrom::getGoodsColor($id);
        //图片
        $data['images'] = goodsImgsFrom::getGoodsImages($id);
        //遥控类型
        $data['handset_types'] = json_decode($data['handset_types']);
        returnSuccess($data);
    }
    //获取问题记录
    public function getGoodsAbnormaLog() {
        $goods_id = (int)$_GET['goods_id'];
        //是否系统管理员和开发 -其他人员无权查看
        if (!userModel::allowGoodsAbnormal()) {
            returnSuccess([]);
        }
        $db = dbMysql::getInstance();
        $db->table('goods_abnormal','a');
        $db->leftJoin('qwuser','b','b.id = a.user_id');
        $db->with('goods_abnormal_log',[['abnormal_id','id']],'wname,remark,is_handled,created_at');
        $db->where('where a.goods_id = :goods_id',['goods_id'=>$goods_id]);
        $db->field('a.id,a.manage_info,a.level,a.title,a.abnormal_type,a.created_at,a.updated_at,a.is_handled,b.wname as create_wname');
        $data = $db->list();
        returnSuccess($data);
    }
    //设置问题记录
    public function setGoodsAbnormal() {
        $paras_list = ['goods_id', 'abnormal_type', 'content', 'level'];
        $request_list = ['goods_id' => '产品ID',  'abnormal_type' => '问题类型', 'content'=>'问题内容'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $abnormal_type = (int)$param['abnormal_type'];

        $db = dbMysql::getInstance();
        $goods_info =  $db->query('select id,manage_info,goods_name from oa_goods_new where id=:goods_id',['goods_id'=>$param['goods_id']]);
        $db->beginTransaction();
        try {
            $node_name = $abnormal_type == 1?'软件异常':'硬件异常';
            //保存异常数据
            if ($abnormal_type == 1) {
                $manage_info = configFrom::getConfigByName('goods_soft_manage');
                if (empty($manage_info)) {
                    SetReturn(1,'软件处理人尚未配置');
                }
            } else {
                $manage_info = $goods_info['manage_info'];
            }
            //新增待办事项
            $abnormal_id = goodsAbnormalFrom::setGoodsAbnormal((int)$param['goods_id'],0,(int)$param['level'],$abnormal_type,$param['content'],$manage_info);
            $manage_info = json_decode($manage_info,true);
            //添加待办
            goodsMattersFrom::addCreateMatter($goods_info['goods_name'].'_问题反馈',$goods_info['id'],$node_name,3,6,$manage_info[0]['id'],$abnormal_id,0);
            //发送信息给异常处理人
            if (userModel::$wid != $manage_info[0]['wid']) {
                $msg = messagesFrom::getMsgTxt(3,$goods_info['goods_name'],'','');
                $other_data = [
                    'user_id'=>userModel::$qwuser_id,
                    'model_id'=>$goods_info['id'],
                    'msg_type'=>3
                ];
                messagesFrom::senMeg([$manage_info[0]['wid']],$msg,$other_data);
            }
            $db->commit();
            returnSuccess('','反馈成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }

    }
    //处理问题
    public function setAbnormalStatus() {
        $paras_list = ['id', 'is_handled', 'remark'];
        $request_list = ['id' => '问题ID',  'is_handled' => '处理状态', 'remark'=>'解决方案'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $db = dbMysql::getInstance();
        $db->beginTransaction();
        $abnormal = $db->table('goods_abnormal')
            ->where('where id=:id',['id'=>(int)$param['id']])
            ->one();
        if (!$abnormal) {
            SetReturn(-1,'未查询到对应问题');
        }
        //验证是否可处理
        try {
            //处理记录
            goodsAbnormalLogFrom::setData((int)$param['id'],(int)$param['is_handled'],$param['remark']);
            //修改问题状态
            goodsAbnormalFrom::setStatus((int)$param['id'],(int)$param['is_handled']);
            if ((int)$param['is_handled'] == 1) {
                //修改待办事件已完成
                $db->table('goods_matters')
                    ->where('where create_type=3 and type=6 and goods_id=:goods_id and model_id=:model_id',['goods_id'=>$abnormal['goods_id'],'model_id'=>(int)$param['id']])
                    ->update(['status'=>1]);
            }
            $db->commit();
            returnSuccess('','处理成功');
        } catch (ExceptionError $e) {
            $db->rollBack();
            throw new ExceptionError($e->getMessage());
        }
    }
    //绑定模板(申请流程,出货)
    public function applyFlow() {
        $paras_list = ['goods_id','sample_batch','remarks'];
        $request_list = ['goods_id' => '新品ID','sample_batch'=>'批次'];
        $length_data = ['remarks'=>['name'=>'备注','length'=>225]];
        $param = arrangeParam($_POST, $paras_list, $request_list,$length_data);
        $goods_id = (int)$param['goods_id'];
        $sample_batch = empty($param['sample_batch'])?1:(int)$param['sample_batch'];
        $flow_path_id = 2;
        $db = dbMysql::getInstance();
        $goods_info = $db->query('select id,goods_name,manage_info from oa_goods_new where id ='.$goods_id);
        if (!$goods_info) {
            SetReturn(-1, '新品不存在');
        }
        $goods_project = $db->table('goods_project')
            ->where('where is_delete = 0 and goods_id=:goods_id and flow_path_id=2',['goods_id'=>$goods_id])
            ->field('id,status,sample_batch')
            ->list();
        $abolish_count = 0;//首批废除数量
        $has_batch_count1 = 0;//书否存在未废除的首批
        $batch_count2 = 0;//次批的数量
        foreach ($goods_project as $project) {
            //首批
            if ($project['sample_batch'] == 1) {
                //首批废除数
                if ($project['status'] == 4) {
                    $abolish_count++;
                } else {
                    $has_batch_count1 = 1;
                }
            } else {
                //次批
                $batch_count2++;
            }
        }
        if ($has_batch_count1  && $sample_batch==1) {
            SetReturn(-1, '首批已存在，请选择次批申请');
        }
        if ($has_batch_count1 == 0 && $sample_batch == 2) {
            SetReturn(-1, '该商品出货样次批不存在，请选择首批申请');
        }
        if ($sample_batch==1) {
            $batch_num = ($abolish_count + 1);
        } else {
            $batch_num = ($batch_count2 + 1);
        }
        $template_data = templateFrom::getTplDataByFlowId($flow_path_id,1,$goods_info['manage_info']);
        //流程模板绑定
        $matter_name = goodsNewFrom::getMatterName2($goods_info['goods_name'],$template_data['tpl_name'],$sample_batch,$batch_num);
        $goods_info['matter_name'] = $matter_name;
        $db->beginTransaction();
        try {
            //完成申请节点
            $project_data = goodsProjectFrom::addGoodsProjectFor2or3($goods_id,$matter_name,$template_data,$sample_batch,$batch_num);
            $next_node = $project_data['next_node'];
            $next_node_index = $project_data['next_node_index'];
            $project_id = $project_data['project_id'];
            //下个节点生成待办
            goodsMattersFrom::addNextNodeMatter($project_id,$next_node,$goods_info,$next_node_index);
            //日志记录
            log::setGoodsProjectLog($goods_id,$project_id,"完成收样", $goods_info['matter_name'],9);
            //消息发送
            messagesFrom::sendNextNodeInfo($param['remarks']);
            $db->commit();
            //TODO 通知第一个节点的负责人+待办事项处理
            returnSuccess('','申请成功');
        } catch (ExceptionError $e) {
            $db->rollBack();
            throw new ExceptionError($e->getMessage());
        }

    }
    //申请抽货
    public function applyChouFlow() {
        $paras_list = ['goods_id','sample_batch','remarks','tpl_id'];
        $request_list = ['goods_id' => '新品ID'];
        $length_data = ['remarks'=>['name'=>'备注','length'=>225]];
        $param = arrangeParam($_POST, $paras_list, $request_list,$length_data);
        $goods_id = (int)$param['goods_id'];
        $sample_batch = empty($param['sample_batch'])?1:(int)$param['sample_batch'];
        $flow_path_id = 3;
        $tpl_id = (int)$param['tpl_id'];
        if (!$tpl_id) {
            SetReturn(-1,'抽货申请，请选择流程模板');
        }
        $db = dbMysql::getInstance();
        $goods_info = $db->query('select id,goods_name,manage_info from oa_goods_new where id ='.$goods_id);
        if (!$goods_info) {
            SetReturn(-1, '新品不存在');
        }
        $goods_project_count = $db->table('goods_project')
            ->where('where is_delete = 0 and goods_id=:goods_id and flow_path_id=:flow_path_id',['goods_id'=>$goods_id,'flow_path_id'=>$flow_path_id])
            ->field('id')
            ->count();
        if ($goods_project_count && $sample_batch==1) {
            SetReturn(-1, '首批已存在，请选择次批申请');
        }
        if ($goods_project_count == 0 && $sample_batch == 2) {
            SetReturn(-1, '次批不存在，请选择首批申请');
        }
        $template_data = $db->table('template')
            ->where('where id = :tpl_id',['tpl_id'=>$tpl_id])
            ->one();
        $tpl_data_array = templateFrom::changeTplData(json_decode($template_data['tpl_data'],true),$goods_info['manage_info']);
        $template_data['tpl_data'] = json_encode($tpl_data_array,JSON_UNESCAPED_UNICODE);
        if (!$template_data) {
            SetReturn('未找到对应目模板');
        }
        if ($sample_batch == 1) {
            $batch_num = 1;
        } else {
            $batch_num = $goods_project_count;
        }
        //流程模板绑定
        $matter_name = goodsNewFrom::getMatterName3($goods_info['goods_name'],$template_data['tpl_name'],$sample_batch,$batch_num);
        $goods_info['matter_name'] = $matter_name;
        $db->beginTransaction();
        try {
            //完成申请节点，
            $project_data = goodsProjectFrom::addGoodsProjectFor2or3($goods_id,$matter_name,$template_data,$sample_batch,$batch_num);
            $next_node = $project_data['next_node'];
            $next_node_index = $project_data['next_node_index'];
            $project_id = $project_data['project_id'];
            //下个节点生成待办
            goodsMattersFrom::addNextNodeMatter($project_id,$next_node,$goods_info,$next_node_index);
            //日志记录
            log::setGoodsProjectLog($goods_id,$project_id,"完成收样", $goods_info['matter_name'],9);
            //消息发送
            messagesFrom::sendNextNodeInfo($param['remarks']);
            $db->commit();
            //TODO 通知第一个节点的负责人+待办事项处理
            returnSuccess('','申请成功');
        } catch (ExceptionError $e) {
            $db->rollBack();
            throw new ExceptionError($e->getMessage());
        }

    }





}