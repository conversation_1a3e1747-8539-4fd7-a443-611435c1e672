<?php

/**
 * @author: zhangguoming
 * @Time: 2024/3/27 9:02
 */

namespace plugins\assessment\models;

use core\lib\config;
use core\lib\db\dbAMysql;

class commissionRulesModel
{
    public static int $id;
    public static string $rule_name; // 规则名称
    public static int $qw_department_id; // 组别，财务管理-项目管理，lv3
    public static int $column_id; // 业绩指标，财务管理-业绩报表数据
    public static string $rules; // 提成比例
    /** 实例 rules
     * rate 提成比例
     * rules 提成规则，数组 symbol 条件连接符号 1或 2且 value 提成规则值 commission_rules_symbol 提成规则符号 1以内（不包含） 2以上（包含）
     * [
     *      {"rate":"10","rules":[{"commission_rules_symbol":1,"value":"3000","symbol":""},{"commission_rules_symbol":2,"value":"5000",,"symbol":"2"}]},
     *      {"rate":"30","rules":[{"commission_rules_symbol":2,"value":"5000"}]}
     * ]
     * */
    public static bool $is_delete; // 1删除 0未删除

     /*
      * 提成比例规则校验
      */
    public static function checkRules($rules) : bool{
        empty($rules) && returnError('提成规则不能为空');
        foreach($rules as $rule) {
            !isset($rule['rate']) && returnError('提成比例不能为空');
            $rule['rate'] < 0 && returnError('提成比例不能小于0');
            empty($rule['rules']) && returnError('提成规则不能为空');
            $idx = 0;
            foreach($rule['rules'] as $r) {
                //$idx > 0 时，symbol不能为空
                $idx > 0 && empty($r['symbol']) && returnError('连接符号不能为空');
                empty($r['commission_rules_symbol']) && returnError('提成规则符号不能为空');
                !isset($r['value']) && returnError('业绩指标规则值不能为空');
                $r['value'] < 0 && returnError('业绩指标规则值不能小于0');
                $idx++;
            }
        }
        return true;
    }

     /*
      * 额外奖励规则校验
      */
      public static function checkPrizeRules($rules) : bool{
        empty($rules) && returnError('奖励规则不能为空');
        foreach($rules as $rule) {
            empty($rule['column_id']) && returnError('业绩指标不能为空');
            empty($rule['formula']) && returnError('奖励公式不能为空');
            foreach ($rule['formula'] as $item) {
                !isset($item['prize']) && returnError('奖励值不能为空');
                $item['prize'] < 0 && returnError('奖励值不能小于0');
                empty($item['rules']) && returnError('奖励规则不能为空');
                $idx = 0;
                foreach($item['rules'] as $r) {
                    //$idx > 0 时，symbol不能为空
                    $idx > 0 && empty($r['symbol']) && returnError('连接符号不能为空');
                    empty($r['commission_rules_symbol']) && returnError('奖励规则符号不能为空');
                    !isset($r['value']) && returnError('业绩指标规则值不能为空');
                    $r['value'] < 0 && returnError('业绩指标规则值不能小于0');
                    $idx++;
                }
            }
        }
        return true;
    }

    // 提成比例规则解析
    public static function getRuleText($commission_rules, $column_name)
    {
        $rules = [];
        $commission_rules_symbol = config::get('commission_rules_symbol', 'data_assessment');
        $commission_rules_symbol_map = array_column($commission_rules_symbol, 'name', 'id');

        $link_symbol = config::get('link_symbol', 'data_assessment');
        $link_symbol_map = array_column($link_symbol, 'name', 'id');

        if (!is_array($commission_rules) && empty($commission_rules) && !$column_name) return '';
        foreach ($commission_rules as $value) {
            $item = '';
            $idx = 0;
            foreach ($value['rules'] as $rule) {
                if ($idx) {
                    $link_symbol = $link_symbol_map[$rule['symbol']];
                    $item .= $idx > 0 ? " {$link_symbol} " : '';
                }
                if (in_array($column_name, ['毛利率', '销售额环比'])) {
                    $rule['value'] = $rule['value'] * 100 . '%';
                }
                $item .= "【{$column_name}】{$commission_rules_symbol_map[$rule['commission_rules_symbol']]}{$rule['value']}";
                $idx++;
            }
            $rules[] = "{$item}，则提成比例为{$value['rate']}%";
        }
        return implode('；或', $rules);
    }

    // 提成比例结果
    public static function getResult($commission_rules, $column_value)
    {
        $commission_rules = json_decode($commission_rules, true);
        $commission_rules_symbol = config::get('commission_rules_symbol', 'data_assessment');
        $commission_rules_symbol_map = array_column($commission_rules_symbol, 'value', 'id');

        $link_symbol = config::get('link_symbol', 'data_assessment');
        $link_symbol_map = array_column($link_symbol, 'value', 'id');

        $result = 0;

        if (!is_array($commission_rules) && empty($commission_rules) && !$column_value) return 0;
        foreach ($commission_rules as $value) {
            $expression = '';
            $idx = 0;
            foreach ($value['rules'] as $rule) {
                if ($idx) $expression .= $link_symbol_map[$rule['symbol']];
                $item_expression = $column_value.$commission_rules_symbol_map[$rule['commission_rules_symbol']].$rule['value'];
                $expression .= eval("return $item_expression;") ? "true": "false";
                $idx++;
            }
            $flag = eval("return $expression;");
            if (!$flag) continue;
            // 循环找出最大的提成比例
            $result = max($result, $value['rate']);
        }
        return $result / 100;
    }

    // 提成比例奖励
    public static function getPrize($prize_commission_rule, $column_value)
    {
        $currency = $prize_commission_rule['currency'];
        $prize = 0;
        $link_symbol = config::get('link_symbol', 'data_assessment');
        $link_symbol_map = array_column($link_symbol, 'value', 'id');
        $commission_rules_symbol = config::get('commission_rules_symbol', 'data_assessment');
        $commission_rules_symbol_map = array_column($commission_rules_symbol, 'value', 'id');
        $prize_commission_rule['prize_rules'] = json_decode($prize_commission_rule['prize_rules'], true);
        if (empty($prize_commission_rule['prize_rules'])) return $prize;
        foreach ($prize_commission_rule['prize_rules'] as $rule) {
            $column_id = $rule['column_id'];
            $column_value_currency = $column_value[$column_id][$currency];
            $item_prize = 0; // 每一项业绩指标一个奖励
            foreach ($rule['formula'] as $formula) {
                $idx = 0;
                $expression = '';
                foreach ($formula['rules'] as $r) {
                    if ($idx) $expression .= $link_symbol_map[$r['symbol']];
                    $item_expression = $column_value_currency.$commission_rules_symbol_map[$r['commission_rules_symbol']].$r['value'];
                    $expression .= eval("return $item_expression;") ? "true": "false";
                    $idx++;
                }
                $flag = eval("return $expression;");
                if (!$flag) continue;
                $item_prize = max($item_prize, $formula['prize']);
            }
            $prize += $item_prize;
        }
        return $prize;
    }


}