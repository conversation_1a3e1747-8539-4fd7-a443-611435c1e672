<?php
/**
 * @author: zhangguoming
 * @Time: 2024/3/29 16:59
 */

namespace  plugins\goods\controller;

use plugins\goods\models\userModel;
use core\lib\db\dbMysql;

class goodsSupplierController
{
    public function getList() {
        if (!userModel::isManageDeveloper()) {
            returnSuccess(['total' => 0, 'page' => 0, 'list' => []]);
        }
        $paras_list = ['page','page_size'];
        $param = arrangeParam($_GET, $paras_list);
        $db = dbMysql::getInstance();
        $db->table('goods_supplier');
        $db->where('where is_delete = 0');
        $db->field('id,level,supplier_name,created_time');
        $data = $db->pages($param['page'], $param['page_size']);
        returnSuccess($data);
    }
    public function editSupplier() {
        $paras_list = ['id','supplier_name','level'];
        $request_list = ['supplier_name' => '供应商名称','level' => '供应商等级'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $id = (int)$param['id'];
        $db = dbMysql::getInstance();
        $supplier = $db->query('select id from oa_goods_supplier where id = :id',['id'=>$id]);
        if ($supplier) {
            $update_data = [
                'supplier_name' => $param['supplier_name'],
                'level' => $param['level'],
                'updated_time' => date('Y-m-d H:i:s'),
                'id'=>$id
            ];
            $db->query('update oa_goods_supplier set supplier_name=:supplier_name,level=:level,updated_time=:updated_time where id=:id', $update_data);
            return returnSuccess('','修改成功');
        } else {
            $insert_data = [
                'supplier_name'=>$param['supplier_name'],
                'user_id'=>userModel::$qwuser_id,
                'level'=>$param['level'],
                'created_time'=>date('Y-m-d H:i:s'),
            ];
            $db->query('insert into oa_goods_supplier (user_id,level,supplier_name,created_time) values (:user_id,:level,:supplier_name,:created_time)',$insert_data);
            return returnSuccess('','添加成功');
        }
    }
}