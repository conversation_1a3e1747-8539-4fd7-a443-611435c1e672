shop_register:
  name: 店铺注册表
  columns:
    id:
      type: int
      length: 11
      auto_increment: true
      primary_key: true
      comment: '主键id'
    
    register_type:
      type: varchar
      length: 50
      not_null: true
      comment: '注册类型'
    
    shop_code:
      type: varchar
      length: 50
      not_null: true
      comment: '店铺编号'
    
    company_id:
      type: int
      length: 11
      not_null: true
      comment: '公司id'
    
    account_type:
      type: varchar
      length: 50
      not_null: true
      comment: '账号类型'
    
    contact_person:
      type: varchar
      length: 50
      not_null: true
      comment: '对接人'
    
    shop_site:
      type: varchar
      length: 50
      not_null: true
      comment: '店铺站点'
    
    business_manager:
      type: varchar
      length: 50
      comment: '招商经理'
    
    receive_card_id:
      type: int
      length: 11
      comment: '收款卡号id'
    
    contact_group:
      type: varchar
      length: 100
      comment: '对接群'
    
    register_device:
      type: varchar
      length: 100
      comment: '注册设备'
    
    register_phone_id:
      type: int
      length: 11
      comment: '注册手机号id'
    
    register_email_id:
      type: int
      length: 11
      comment: '注册邮箱id'
    
    credit_card_id:
      type: int
      length: 11
      comment: '信用卡id'
    
    shop_password:
      type: varchar
      length: 100
      comment: '店铺密码'
    
    vat_status:
      type: varchar
      length: 50
      comment: 'VAT注册情况'
    
    erp_status:
      type: varchar
      length: 50
      comment: 'ERP注册情况'
    
    status:
      type: tinyint
      length: 4
      not_null: true
      default: 0
      comment: '状态：0待处理,1处理中,2注册成功,3注册失败,4已暂停'
    
    is_end:
      type: tinyint
      length: 1
      default: 0
      comment: '是否结束跟进：0否,1是'
    
    register_result:
      type: tinyint
      length: 1
      comment: '注册结果：0失败,1成功'
    
    register_date:
      type: date
      comment: '店铺注册日期'
    
    apply_id:
      type: int
      length: 11
      comment: '关联店铺申请id'
    
    follow_progress:
      type: text
      comment: '注册进展'
    
    remark:
      type: text
      comment: '备注'
    
    urge_count:
      type: int
      length: 11
      default: 0
      comment: '催办次数'
    
    last_urge_at:
      type: datetime
      comment: '最后催办时间'
    
    urge_by:
      type: int
      length: 11
      comment: '最后催办人id'
    
    created_at:
      type: datetime
      not_null: true
      default: CURRENT_TIMESTAMP
      comment: '创建时间'
    
    created_by:
      type: int
      length: 11
      not_null: true
      comment: '创建人id'
    
    updated_at:
      type: datetime
      on_update: CURRENT_TIMESTAMP
      comment: '更新时间'
    
    updated_by:
      type: int
      length: 11
      comment: '更新人id'

  indexes:
    - name: idx_status
      columns: [status]
    - name: idx_shop_code
      columns: [shop_code]
    - name: idx_company_id
      columns: [company_id]
    - name: idx_apply_id
      columns: [apply_id]
    - name: idx_created_at
      columns: [created_at]
