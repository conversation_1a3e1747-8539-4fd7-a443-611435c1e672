<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/4 10:36
 */

namespace financial\models;

use core\lib\db\dbFMysql;
use function Symfony\Component\Translation\t;

class columnModel
{
    //获取所有列
    public static function getAllKeys() {
        $db = dbFMysql::getInstance();
        $list = $db->table('column')
            ->where('where is_delete=0 and show_type > 0')
            ->field('key_name')
            ->list();
        return array_column($list,'key_name');
    }
    //合并字段获取
    public static function getTableSearchKeys($aggregation_keys, $table_auth_key) {
        $default_aggregation_keys = ['key8','key6','key13','key14'];
        $auth_keys = json_decode(userModel::$auth,true);
        $auth_aggregation_keys = [];
        //各个表对应的权限合并计算字段
        switch ($table_auth_key) {
            case 'board':
                $auth_aggregation_keys = [
                    'board_search_goods_key14',
                    'board_search_goods_key13',
                    'board_search_goods_key6',
                    'board_search_goods_key8',
                ];
                break;
            case 'asin_table':
                $auth_aggregation_keys = [
                    'asin_search_goods_key14',
                    'asin_search_goods_key13',
                    'asin_search_goods_key6',
                    'asin_search_goods_key8',
                ];
                break;
            case 'pasin_table':
                $auth_aggregation_keys = [
                    'pasin_search_goods_key14',
                    'pasin_search_goods_key13',
                    'pasin_search_goods_key6',
                    'pasin_search_goods_key8',
                ];
                break;
            case 'sku_table':
                $auth_aggregation_keys = [
                    'sku_search_goods_key14',
                    'sku_search_goods_key13',
                    'sku_search_goods_key6',
                    'sku_search_goods_key8',
                ];
                break;
            case 'yunying_table':
                $auth_aggregation_keys = [
                    'yunying_search_goods_key14',
                    'yunying_search_goods_key13',
                    'yunying_search_goods_key6',
                    'yunying_search_goods_key8',
                ];
                break;
            case 'store':
                $auth_aggregation_keys = [
                    'sid_search_goods_key14',
                    'sid_search_goods_key13',
                    'sid_search_goods_key6',
                    'sid_search_goods_key8',
                ];
                break;
            case 'month_total':
                $auth_aggregation_keys = [
                    'month_search_goods_key14',
                    'month_search_goods_key13',
                    'month_search_goods_key6',
                    'month_search_goods_key8',
                ];
                break;
            case 'hot':
                $auth_aggregation_keys = [
                    'hot_search_goods_key14',
                    'hot_search_goods_key13',
                    'hot_search_goods_key6',
                    'hot_search_goods_key8',
                ];
                break;
            case 'qingcang':
                $auth_aggregation_keys = [
                    'clear_search_goods_key14',
                    'clear_search_goods_key13',
                    'clear_search_goods_key6',
                    'clear_search_goods_key8',
                ];
                break;
            case 'waring':
                $auth_aggregation_keys = [
                    'waring_search_goods_key14',
                    'waring_search_goods_key13',
                    'waring_search_goods_key6',
                    'waring_search_goods_key8',
                ];
                break;
            case 'supplier_gross_table':
                $auth_aggregation_keys = [
                    'supplie_search_goods_key14',
                    'supplie_search_goods_key13',
                    'supplie_search_goods_key6',
                    'supplie_search_goods_key8',
                ];
                break;
            case 'goods_gross_table':
                $auth_aggregation_keys = [
                    'goods_search_goods_key14',
                    'goods_search_goods_key13',
                    'goods_search_goods_key6',
                    'goods_search_goods_key8',
                ];
                break;
        }
        //权限包含的合并字段
        $auth_aggregation = array_intersect($auth_keys,$auth_aggregation_keys);
        $auth_aggregation_keys = [];
        foreach ($auth_aggregation as $v) {
            $slic = explode('_',$v);
            $auth_aggregation_keys[] = end($slic);
        }
        //权限中没有的合并字段必须计算在内
        $diff_keys = array_diff($default_aggregation_keys,$auth_aggregation_keys);
        //合并上用户选择的字段
        $res_kes = array_merge($aggregation_keys,$diff_keys);
        $res_kes = array_unique($res_kes);
        return getTableSearchAggregationKeys($res_kes);
    }
}