#!/bin/bash

# FBA货件明细数据同步脚本
# 实现两阶段数据同步：领星API → ERP数据库 → Logistics数据库

# 配置参数
token='01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0'
qwUrl='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=03a8032d-65c0-4390-a588-9a6b2cf11336'
fsUrl='https://open.feishu.cn/open-apis/bot/v2/hook/2b63c602-bb10-465f-bc9a-af4dc1f02233'
qWFlag=0
fsFlag=0

echo "开始FBA货件明细数据同步..."
start_time=$(date +"%Y-%m-%d %H:%M:%S")

# 第一阶段：从领星API同步到ERP数据库
echo "=========================================="
echo "阶段1: 同步领星API数据到ERP数据库"
echo "=========================================="

api_start_time=$(date +"%Y-%m-%d %H:%M:%S")
echo "开始时间: $api_start_time"

# while true; do
#     echo "调用领星API同步接口..."
#     api_response=$(curl -s -X POST -d "token=$token&batch_size=100" 'http://oa.ywx.com/task/lingXingApi/synFbaInboundShipment')
#     echo "API响应: $api_response"

#     # 检查响应是否为空
#     if [ -z "$api_response" ]; then
#         echo "❌ API响应为空，请检查网络连接或服务状态"
#         continue
#     fi

#     # 从API响应中提取code字段的值
#     api_code=$(echo "$api_response" | grep -oP '"code":\K-?\d+')
#     # 从API响应中提取msg字段的值
#     api_msg=$(echo "$api_response" | grep -oP '"message"\s*:\s*"\K[^"]+')

#     # 如果无法提取code，设置默认值
#     if [ -z "$api_code" ]; then
#         api_code=-999
#         api_msg="无法解析API响应"
#     fi

#     echo "API同步结果: code=$api_code, msg=$api_msg"

#     if [ "$api_code" -eq 2 ]; then
#         api_end_time=$(date +"%Y-%m-%d %H:%M:%S")
#         echo "领星API数据同步完成，结束时间: $api_end_time"
#         break
#     else
#         echo "继续同步领星数据，code=$api_code"
#     fi
# done

# 第二阶段：从ERP数据库转换到Logistics数据库
echo "=========================================="
echo "阶段2: 转换数据到Logistics数据库"
echo "=========================================="

transform_start_time=$(date +"%Y-%m-%d %H:%M:%S")
echo "开始时间: $transform_start_time"

while true; do
echo "调用数据转换接口..."
transform_response=$(curl -s -X POST -d "token=$token&batch_size=100" 'http://oa.ywx.com/task/logistics/transformFbaInboundShipmentData')
    echo "API响应: $transform_response"

    # 检查响应是否为空
    if [ -z "$transform_response" ]; then
        echo "❌ API响应为空，请检查网络连接或服务状态"
        continue
    fi

    # 从API响应中提取code字段的值
    transform_code=$(echo "$transform_response" | grep -oP '"code":\K-?\d+')
    # 从API响应中提取msg字段的值
    transform_msg=$(echo "$transform_response" | grep -oP '"message"\s*:\s*"\K[^"]+')

    # 如果无法提取code，设置默认值
    if [ -z "$transform_code" ]; then
        transform_code=-999
        transform_msg="无法解析API响应"
    fi

    if [ "$transform_code" -eq 2 ]; then
        transform_msg=$(date +"%Y-%m-%d %H:%M:%S")
        echo "领星API数据同步完成，结束时间: $api_end_time"
        break
    else
        echo "继续同步领星数据，code=$transform_code"
    fi
done

