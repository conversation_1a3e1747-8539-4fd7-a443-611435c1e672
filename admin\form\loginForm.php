<?php
/**
 * @author: zhangguoming
 * @Time: 2025/3/26 10:01
 */

namespace admin\form;

use core\lib\config;
use core\lib\db\dbMysql;
use core\lib\predisV;

class loginForm
{
    /**
     * @param $user_id
     * @return void * 总系统登录时，更新新登录状态
     * @throws \RedisException
     * @throws \core\lib\ExceptionError
     */
    public static function updateTokenTime($user_id) {
        $db = dbMysql::getInstance();
        $code_data = $db->table('login_code')
            ->where('user_id = :user_id',['user_id'=>$user_id])
            ->field('token1,token2,token3')
            ->one();
        //将三个系统状态更新为4小时后过期
        if ($code_data) {
            $redis = (new predisV())::$client;
            if (!empty($code_data['token1']) && $redis->exists($code_data['token1'])) {
                $redis->expire($code_data['token1'],4*60*60);
            }
            if (!empty($code_data['token2']) && $redis->exists($code_data['token2'])) {
                $redis->expire($code_data['token2'],4*60*60);
            }
            if (!empty($code_data['token3']) && $redis->exists($code_data['token3'])) {
                $redis->expire($code_data['token3'],4*60*60);
            }
        }
    }

    /**
     * @param $user_id
     * @return void 退出登录时候清除三个系统的token
     * @throws \RedisException
     * @throws \core\lib\ExceptionError
     */
    public static function cleanUpToken($user_id) {
        $db = dbMysql::getInstance();
        $code_data = $db->table('login_code')
            ->where('user_id = :user_id',['user_id'=>$user_id])
            ->field('code,token1,token2,token3')
            ->one();
        if ($code_data) {
            $db->table('login_code')
                ->where('code = :code',['code'=>$code_data['code']])
                ->update([
                    'token1'=>'',
                    'token2'=>'',
                    'token3'=>''
                ]);
            $redis = (new predisV())::$client;
            if (!empty($code_data['token1'])) {
                $prefix = config::get('token_key_prefix', 'app');
                $key = $prefix.$code_data['token1'];
                if ($redis->exists($key)) {
                    $redis->del($key);
                }
            }
            if (!empty($code_data['token2']) && $redis->exists($code_data['token2'])) {
                $prefix = config::get('financial_token_key_prefix', 'app');
                $key = $prefix.$code_data['token2'];
                if ($redis->exists($key)) {
                    $redis->del($key);
                }
            }
            if (!empty($code_data['token3']) && $redis->exists($code_data['token3'])) {
                $key = 'oa_aftersales_token_'.$code_data['token3'];
                if ($redis->exists($key)) {
                    $redis->del($key);
                }
            }
        }
    }
}