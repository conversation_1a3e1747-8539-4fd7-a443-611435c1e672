<?php
namespace plugins\shop\controller;

use core\lib\db\dbAfMysql;
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use core\lib\db\dbShopMysql;
use core\lib\redisCached;

class shopStatisticController extends baseController
{
    public function getShopStatistic()
    {
        $param = array_intersect_key($_GET, [
            'dep_id' => 1,
            'date' => 1,
            'category' => 1,
            'country' => 1,
        ]);

        $sidSelected = [];
        if (isset($param['category']) && !empty($param['category'])) {
            $sidSelected = self::getSidsByCateIds($param['category']);
        }

        $shopDB = dbShopMysql::getInstance();

        $shopDB->table('shop')->where('1=1');
        if (!empty($sidSelected)) {
            $shopDB->whereIn('lx_shop_id', $sidSelected);
        }

        if (isset($param['dep_id']) && !empty($param['dep_id']) && is_array($param['dep_id'])) {
            $shopDB->whereIn('dep_id', $param['dep_id']);
        }
        if (isset($param['date']) && !empty($param['date']) && is_array($param['date'])) {
            $shopDB->andWhere('register_date >= :start and register_date <= :end', [
                'start' => $param['date'][0].'-01',
                'end' => $param['date'][1].'-'.date('t', strtotime($param['date'][1]))
            ]);
        }
        if (isset($param['country']) && !empty($param['country']) && is_array($param['country'])) {
            $shopDB->whereIn('shop_site', $param['country']);
        }

        $shopList = $shopDB->list();

        $ret = [
            'account_purpose' => [
                'total' => null,
                'dep' => null,
                'category' => null
            ],
            'use_status' => [
                'total' => null,
                'dep' => null,
                'category' => null,
                'trademark' => null,
            ],
            'appeal_issue_type' => [
                'total' => null,
                'category' => null
            ]
        ];

        $sid = array_column($shopList, 'lx_shop_id');
        $sid = array_values(array_filter(array_unique($sid)));

        $sid_category_map = [];
        // 1、从lingxing_listing里获取sid对应的sku
        $afdb = dbAfMysql::getInstance();
        $fdb = dbFMysql::getInstance();

        $sid_sku = []; // sid => sku
        if (!empty($sid)) {
            // 先查询所有类目
            $goods_category = $fdb->table('goods_category')
                ->field('cid, parent_cid, title')
                ->where('is_delete = 0')
                ->list();

            foreach ($goods_category as &$cate) {
                $route = [];
                shopController::getCategoryRoute($goods_category, $cate['cid'], $route);
                $route = array_reverse($route);
                $cate['level_2'] = isset($route[1]) ? [
                    'cid' => $route[1]['cid'],
                    'title' => $route[1]['title'],
                    'parent_cid' => $route[1]['parent_cid'],
                ] : [];
            }
            unset($cate);
            $goods_category = array_column($goods_category, null,'cid');

            $listing = $afdb->table('lingxing_listing')
                ->whereIn('sid', $sid)
                ->where('status = 1')
                ->field('local_sku, sid')
                ->list();
            $sku = array_column($listing, 'local_sku');
            $sku = array_values(array_filter(array_unique($sku)));

            foreach ($listing as $item) {
                if (empty($item['sid']) || empty($item['local_sku'])) continue;
                !isset($sid_sku[$item['sid']]['sku']) && $sid_sku[$item['sid']]['sku'] = [];
                !in_array($item['local_sku'], $sid_sku[$item['sid']]['sku']) && $sid_sku[$item['sid']]['sku'][] = $item['local_sku'];
            }

            // 在财务goods表中查询sku对应的
            $goods = $fdb->table('goods')
                ->field('lingxing_id, product_name, cid, sku')
                ->where('status != 0')
                ->whereIn('sku', $sku)
                ->list();
            $goods = array_column($goods, 'cid', 'sku');

            foreach ($sid_sku as $sid => &$item) {
                $item['cid'] = [];
                $item['category'] = [];
                foreach ($item['sku'] as $sku) {
                    if (!isset($goods[$sku])) continue;
                    $item['cid'][] = $goods[$sku];
                    if (!isset($goods_category[$goods[$sku]])) continue;
                    $item_category = $goods_category[$goods[$sku]];
                    if (!empty($item_category['level_2'])) {
                        $item['category'][$item_category['level_2']['cid']] = $item_category['level_2'];
                    }
                }
                $item['cid'] = array_values(array_filter(array_unique($item['cid'])));
            }
            unset($item);
        }

        // 构造lx_shop获取类目

        $deps = redisCached::getDepartment();
        $deps = array_column($deps, 'name', 'id');

        $trademarks = redisCached::getTrademark();
        $trademarks = array_column($trademarks, 'brand_name', 'id');

        foreach ($shopList as $shop) {
            // 筛选
            $dep_name = $deps[$shop['dep_id']];

            $category = $sid_sku[$shop['lx_shop_id']]['category'] ?? [];

            // 用途统计
            if ($shop['account_purpose']) {
                !isset($ret['account_purpose']['total'][$shop['account_purpose']]) && $ret['account_purpose']['total'][$shop['account_purpose']] = 0;
                !isset($ret['account_purpose']['dep'][$dep_name][$shop['account_purpose']]) && $ret['account_purpose']['dep'][$dep_name][$shop['account_purpose']] = 0;
                $ret['account_purpose']['total'][$shop['account_purpose']] += 1;
                $ret['account_purpose']['dep'][$dep_name][$shop['account_purpose']] += 1;
                foreach ($category as $item) {
                    !isset($ret['account_purpose']['category'][$item['title']][$shop['account_purpose']]) && $ret['account_purpose']['category'][$item['title']][$shop['account_purpose']] = 0;
                    $ret['account_purpose']['category'][$item['title']][$shop['account_purpose']] += 1;
                }
            }

            // 使用状态统计
            if ($shop['use_status']) {
                $trademark = json_decode($shop['trademark_id'], true) ?? [];
                foreach ($trademark as $trade) {
                    $trade_name = $trademarks[$trade] ?? '';
                    !isset($ret['use_status']['trademark'][$trade_name][$shop['use_status']]) && $ret['use_status']['trademark'][$trade_name][$shop['use_status']] = 0;
                    $ret['use_status']['trademark'][$trade_name][$shop['use_status']] += 1;
                }

                !isset($ret['use_status']['total'][$shop['use_status']]) && $ret['use_status']['total'][$shop['use_status']] = 0;
                $ret['use_status']['total'][$shop['use_status']] += 1;
                !isset($ret['use_status']['dep'][$dep_name][$shop['use_status']]) && $ret['use_status']['dep'][$dep_name][$shop['use_status']] = 0;
                $ret['use_status']['dep'][$dep_name][$shop['use_status']] += 1;
                foreach ($category as $item) {
                    !isset($ret['use_status']['category'][$item['title']][$shop['use_status']]) && $ret['use_status']['category'][$item['title']][$shop['use_status']] = 0;
                    $ret['use_status']['category'][$item['title']][$shop['use_status']] += 1;
                }
            }
        }

        // 店铺风险
        $shopDB->table('shop_appeal', 'sa')
            ->field('sa.*, s.lx_shop_id')
            ->leftJoin('shop','s', 's.id = sa.shop_id')
            ->where('1=1');

        if (!empty($sidSelected)) {
            $shopDB->whereIn('s.lx_shop_id', $sidSelected);
        }

        if (isset($param['dep_id']) && !empty($param['dep_id']) && is_array($param['dep_id'])) {
            $shopDB->whereIn('s.dep_id', $param['dep_id']);
        }
        if (isset($param['date']) && !empty($param['date']) && is_array($param['date'])) {
            $shopDB->andWhere('s.register_date >= :start and s.register_date <= :end', [
                'start' => $param['date'][0].'-01',
                'end' => $param['date'][1].'-'.date('t', strtotime($param['date'][1]))
            ]);
        }
        if (isset($param['country']) && !empty($param['country']) && is_array($param['country'])) {
            $shopDB->whereIn('s.shop_site', $param['country']);
        }

        $list = $shopDB->list();
        foreach ($list as $shop_appeal) {
            $category = $sid_sku[$shop_appeal['lx_shop_id']]['category'] ?? [];
            foreach ($category as $item) {
                !isset($ret['appeal_issue_type']['category'][$item['title']][$shop_appeal['issue_type']]) && $ret['appeal_issue_type']['category'][$item['title']][$shop_appeal['issue_type']] = 0;
                $ret['appeal_issue_type']['category'][$item['title']][$shop_appeal['issue_type']] += 1;
            }
            !isset($ret['appeal_issue_type']['total'][$shop_appeal['issue_type']]) && $ret['appeal_issue_type']['total'][$shop_appeal['issue_type']] = 0;
            $ret['appeal_issue_type']['total'][$shop_appeal['issue_type']] += 1;
        }

        returnSuccess($ret);

    }

    public static function getSidsByCateIds($cate_cids) {
        $dbF = dbFMysql::getInstance();
        //查询分类及其子分类
        $cate_cids = self::getChildsId($cate_cids);
        $goods_list = $dbF->table('goods')
            ->whereIn('cid',$cate_cids)
            ->field('cid,sku')
            ->list();
        $sid = [];
        if (count($goods_list)) {
            $skus = array_column($goods_list,'sku');
            $afdb = dbAfMysql::getInstance();
            $sid = $afdb->table('lingxing_listing')
                ->where('status = 1 and asin <> \'\' and marketplace <> \'\'')
                ->whereIn('local_sku',$skus)
                ->field('sid')
                ->list();
        }
        return array_column($sid, 'sid');


    }

    public static function getChildsId($cate_ids) {
        $db = dbFMysql::getInstance();
        //第二层
        $child = $db->table('goods_category')
            ->where('is_delete = 0')
            ->whereIn('parent_cid',$cate_ids)
            ->field('cid')
            ->list();
        if (count($child)) {
            $cate_ids_ = array_column($child,'cid');
            //第三次
            $child1 = $db->table('goods_category')
                ->where('is_delete = 0')
                ->whereIn('parent_cid',$cate_ids_)
                ->field('cid')
                ->list();
            $child = array_merge($child,$child1);
        }
        $new_cate_ids = array_merge($cate_ids,array_column($child,'cid'));
        return $new_cate_ids;
    }
}
