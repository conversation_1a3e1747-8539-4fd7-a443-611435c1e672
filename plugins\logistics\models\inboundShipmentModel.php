<?php
/**
 * FBA发货单模型（简化版）
 * @purpose FBA发货单数据处理，使用单表存储，array字段存储为JSON
 * @Author: System
 * @Time: 2025/06/23
 */

namespace plugins\logistics\models;

use core\lib\db\dbErpMysql;

class inboundShipmentModel
{
    private $db;
    
    public function __construct()
    {
        $this->db = dbErpMysql::getInstance();
    }

    /**
     * 保存发货单列表数据
     * @param array $shipmentList 发货单列表数据
     * @param string $syncDate 同步日期
     * @return int 保存成功数量
     */
    public function saveShipmentList($shipmentList, $syncDate = null)
    {
        if (empty($shipmentList)) {
            return 0;
        }
        
        if (empty($syncDate)) {
            $syncDate = date('Y-m-d');
        }
        
        $successCount = 0;
        
        try {
            $this->db->beginTransaction();
            
            foreach ($shipmentList as $shipment) {
                // 保存主表数据
                $shipmentId = $this->saveShipmentMain($shipment, $syncDate);
                
                if ($shipmentId > 0) {
                    $successCount++;
                }
            }
            
            $this->db->commit();
            return $successCount;
            
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * 保存发货单主表数据（包含所有array字段）
     * @param array $shipment 发货单数据
     * @param string $syncDate 同步日期
     * @return int 插入的ID
     */
    private function saveShipmentMain($shipment, $syncDate)
    {
        // 检查是否已存在
        $existing = $this->db->table('lingxing_inbound_shipment')
            ->where('shipment_id = :shipment_id AND sync_date = :sync_date', [
                'shipment_id' => $shipment['id'],
                'sync_date' => $syncDate
            ])
            ->one();
            
        $data = [
            'shipment_id' => $shipment['id'],
            'shipment_sn' => $shipment['shipment_sn'] ?? '',
            'status' => $shipment['status'] ?? 0,
            'shipment_time' => !empty($shipment['shipment_time']) ? $shipment['shipment_time'] : null,
            'shipment_time_second' => !empty($shipment['shipment_time_second']) ? $shipment['shipment_time_second'] : null,
            'wid' => $shipment['wid'] ?? 0,
            'wname' => $shipment['wname'] ?? '',
            'create_user' => $shipment['create_user'] ?? '',
            'logistics_provider_id' => $shipment['logistics_provider_id'] ?? '',
            'logistics_provider_name' => $shipment['logistics_provider_name'] ?? '',
            'logistics_channel_name' => $shipment['logistics_channel_name'] ?? '',
            'expected_arrival_date' => !empty($shipment['expected_arrival_date']) ? $shipment['expected_arrival_date'] : null,
            'actual_shipment_time' => !empty($shipment['actual_shipment_time']) ? $shipment['actual_shipment_time'] : null,
            'etd_date' => !empty($shipment['etd_date']) ? $shipment['etd_date'] : null,
            'eta_date' => !empty($shipment['eta_date']) ? $shipment['eta_date'] : null,
            'delivery_date' => !empty($shipment['delivery_date']) ? $shipment['delivery_date'] : null,
            'gmt_create' => !empty($shipment['gmt_create']) ? $shipment['gmt_create'] : null,
            'is_pick' => $shipment['is_pick'] ?? 0,
            'is_print' => $shipment['is_print'] ?? 0,
            'pick_time' => !empty($shipment['pick_time']) ? $shipment['pick_time'] : null,
            'print_num' => $shipment['print_num'] ?? 0,
            'head_fee_type' => $shipment['head_fee_type'] ?? 0,
            'head_fee_type_name' => $shipment['head_fee_type_name'] ?? '',
            'head_fee_type_name_new' => $shipment['head_fee_type_name_new'] ?? '',
            'file_id' => $shipment['file_id'] ?? '',
            'update_time' => !empty($shipment['update_time']) ? $shipment['update_time'] : null,
            'remark' => $shipment['remark'] ?? '',
            'is_return_stock' => $shipment['is_return_stock'] ?? 0,
            'pay_status' => $shipment['pay_status'] ?? 0,
            'audit_status' => $shipment['audit_status'] ?? 0,
            'shipment_user' => $shipment['shipment_user'] ?? '',
            'is_exist_declaration' => $shipment['is_exist_declaration'] ?? 0,
            'is_exist_clearance' => $shipment['is_exist_clearance'] ?? 0,
            'third_party_order_mode' => $shipment['third_party_order_mode'] ?? 0,
            'third_party_order_status' => $shipment['third_party_order_status'] ?? 0,
            'vat_code' => $shipment['vat_code'] ?? '',
            'method_id' => $shipment['method_id'] ?? '',
            'method_name' => $shipment['method_name'] ?? '',
            'is_custom_shipment_time' => $shipment['is_custom_shipment_time'] ?? 0,
            'destination_fulfillment_center_id' => $shipment['destination_fulfillment_center_id'] ?? '',
            'status_name' => $shipment['status_name'] ?? '',
            'is_delete' => $shipment['is_delete'] ?? 0,
            'sync_date' => $syncDate,
            
            // Array字段直接存储为JSON
            'logistics_data' => $this->encodeJsonData($shipment['logistics'] ?? []),
            'relate_list_data' => $this->encodeJsonData($shipment['relate_list'] ?? []),
            'not_relate_list_data' => $this->encodeJsonData($shipment['not_relate_list'] ?? []),
            'file_list_data' => $this->encodeJsonData($shipment['fileList'] ?? [])
        ];
        
        if ($existing) {
            // 更新
            $this->db->table('lingxing_inbound_shipment')
                ->where('id = :id', ['id' => $existing['id']])
                ->update($data);
            return $existing['id'];
        } else {
            // 插入
            return $this->db->table('lingxing_inbound_shipment')->insert($data);
        }
    }

    /**
     * 编码JSON数据
     * @param mixed $data 要编码的数据
     * @return string JSON字符串
     */
    private function encodeJsonData($data)
    {
        if (empty($data)) {
            return '';
        }
        return json_encode($data, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 解码JSON数据
     * @param string $jsonString JSON字符串
     * @return array 解码后的数据
     */
    private function decodeJsonData($jsonString)
    {
        if (empty($jsonString)) {
            return [];
        }
        $decoded = json_decode($jsonString, true);
        return is_array($decoded) ? $decoded : [];
    }

    /**
     * 获取发货单列表
     * @param array $params 查询参数
     * @return array
     */
    public function getShipmentList($params = [])
    {
        $where = 'is_deleted = 0';
        $whereData = [];
        
        // 发货单号筛选
        if (!empty($params['shipment_sn'])) {
            $where .= ' AND shipment_sn LIKE :shipment_sn';
            $whereData['shipment_sn'] = '%' . $params['shipment_sn'] . '%';
        }
        
        // 状态筛选
        if (isset($params['status']) && $params['status'] !== '') {
            $where .= ' AND status = :status';
            $whereData['status'] = $params['status'];
        }
        
        // 仓库筛选
        if (!empty($params['wid'])) {
            $where .= ' AND wid = :wid';
            $whereData['wid'] = $params['wid'];
        }
        
        // 时间筛选
        if (!empty($params['start_date'])) {
            $where .= ' AND DATE(gmt_create) >= :start_date';
            $whereData['start_date'] = $params['start_date'];
        }
        
        if (!empty($params['end_date'])) {
            $where .= ' AND DATE(gmt_create) <= :end_date';
            $whereData['end_date'] = $params['end_date'];
        }
        
        // 同步日期筛选
        if (!empty($params['sync_date'])) {
            $where .= ' AND sync_date = :sync_date';
            $whereData['sync_date'] = $params['sync_date'];
        }
        
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 20;
        
        $result = $this->db->table('lingxing_inbound_shipment')
            ->field('*')
            ->where($where, $whereData)
            ->order('id DESC')
            ->pages($page, $pageSize);
            
        // 解码JSON数据
        if (!empty($result['list'])) {
            foreach ($result['list'] as &$item) {
                $item['logistics'] = $this->decodeJsonData($item['logistics_data']);
                $item['relate_list'] = $this->decodeJsonData($item['relate_list_data']);
                $item['not_relate_list'] = $this->decodeJsonData($item['not_relate_list_data']);
                $item['fileList'] = $this->decodeJsonData($item['file_list_data']);
                
                // 移除原始JSON字段，避免重复
                unset($item['logistics_data'], $item['relate_list_data'], 
                     $item['not_relate_list_data'], $item['file_list_data']);
            }
        }
        
        return $result;
    }

    /**
     * 获取发货单详情
     * @param int $id
     * @return array|false
     */
    public function getShipmentDetail($id)
    {
        $shipment = $this->db->table('lingxing_inbound_shipment')
            ->where('id = :id AND is_deleted = 0', ['id' => $id])
            ->one();
            
        if (!$shipment) {
            return false;
        }
        
        // 解码JSON数据
        $shipment['logistics'] = $this->decodeJsonData($shipment['logistics_data']);
        $shipment['relate_list'] = $this->decodeJsonData($shipment['relate_list_data']);
        $shipment['not_relate_list'] = $this->decodeJsonData($shipment['not_relate_list_data']);
        $shipment['fileList'] = $this->decodeJsonData($shipment['file_list_data']);
        
        // 移除原始JSON字段
        unset($shipment['logistics_data'], $shipment['relate_list_data'], 
             $shipment['not_relate_list_data'], $shipment['file_list_data']);
             
        return $shipment;
    }

    /**
     * 根据发货单号搜索
     * @param string $shipmentSn 发货单号
     * @return array
     */
    public function searchByShipmentSn($shipmentSn)
    {
        return $this->getShipmentList(['shipment_sn' => $shipmentSn]);
    }

    /**
     * 根据SKU搜索关联货件
     * @param string $sku SKU
     * @return array
     */
    public function searchBySku($sku)
    {
        $result = $this->db->table('lingxing_inbound_shipment')
            ->field('*')
            ->where('is_deleted = 0 AND relate_list_data LIKE :sku', ['sku' => '%"sku":"' . $sku . '"%'])
            ->order('id DESC')
            ->list();
            
        // 解码JSON数据
        foreach ($result as &$item) {
            $item['logistics'] = $this->decodeJsonData($item['logistics_data']);
            $item['relate_list'] = $this->decodeJsonData($item['relate_list_data']);
            $item['not_relate_list'] = $this->decodeJsonData($item['not_relate_list_data']);
            $item['fileList'] = $this->decodeJsonData($item['file_list_data']);
            
            // 移除原始JSON字段
            unset($item['logistics_data'], $item['relate_list_data'], 
                 $item['not_relate_list_data'], $item['file_list_data']);
        }
        
        return $result;
    }

    /**
     * 根据ASIN搜索关联货件
     * @param string $asin ASIN
     * @return array
     */
    public function searchByAsin($asin)
    {
        $result = $this->db->table('lingxing_inbound_shipment')
            ->field('*')
            ->where('is_deleted = 0 AND relate_list_data LIKE :asin', ['asin' => '%"asin":"' . $asin . '"%'])
            ->order('id DESC')
            ->list();
            
        // 解码JSON数据
        foreach ($result as &$item) {
            $item['logistics'] = $this->decodeJsonData($item['logistics_data']);
            $item['relate_list'] = $this->decodeJsonData($item['relate_list_data']);
            $item['not_relate_list'] = $this->decodeJsonData($item['not_relate_list_data']);
            $item['fileList'] = $this->decodeJsonData($item['file_list_data']);
            
            // 移除原始JSON字段
            unset($item['logistics_data'], $item['relate_list_data'], 
                 $item['not_relate_list_data'], $item['file_list_data']);
        }
        
        return $result;
    }

    /**
     * 获取发货单统计信息
     * @param array $params 筛选参数
     * @return array
     */
    public function getShipmentStatistics($params = [])
    {
        $where = 'is_deleted = 0';
        $whereData = [];
        
        // 添加筛选条件
        if (!empty($params['sync_date'])) {
            $where .= ' AND sync_date = :sync_date';
            $whereData['sync_date'] = $params['sync_date'];
        }
        
        if (!empty($params['start_date'])) {
            $where .= ' AND DATE(gmt_create) >= :start_date';
            $whereData['start_date'] = $params['start_date'];
        }
        
        if (!empty($params['end_date'])) {
            $where .= ' AND DATE(gmt_create) <= :end_date';
            $whereData['end_date'] = $params['end_date'];
        }
        
        // 统计各状态的发货单数量
        $statistics = [];
        $statusMap = $this->getStatusMap();
        
        foreach ($statusMap as $statusCode => $statusName) {
            $count = $this->db->table('lingxing_inbound_shipment')
                ->where($where . ' AND status = :status', array_merge($whereData, ['status' => $statusCode]))
                ->count();
            
            $statistics[$statusCode] = [
                'status_name' => $statusName,
                'count' => $count
            ];
        }
        
        // 总数统计
        $total = $this->db->table('lingxing_inbound_shipment')
            ->where($where, $whereData)
            ->count();
            
        return [
            'total' => $total,
            'by_status' => $statistics
        ];
    }

    /**
     * 获取状态映射
     * @return array
     */
    public function getStatusMap()
    {
        return [
            -1 => '待配货',
            0 => '待发货',
            1 => '已发货',
            2 => '已完成',
            3 => '已作废'
        ];
    }

    /**
     * 获取头程费分配方式映射
     * @return array
     */
    public function getHeadFeeTypeMap()
    {
        return [
            0 => '按计费重',
            1 => '按实重',
            2 => '按体积重',
            3 => '按SKU数量',
            4 => '自定义',
            5 => '按箱子体积'
        ];
    }

    /**
     * 获取拣货状态映射
     * @return array
     */
    public function getPickStatusMap()
    {
        return [
            0 => '未拣货',
            1 => '已拣货'
        ];
    }

    /**
     * 获取打印状态映射
     * @return array
     */
    public function getPrintStatusMap()
    {
        return [
            0 => '未打印',
            1 => '已打印'
        ];
    }
}
