<?php
namespace plugins\shop\controller;

use core\lib\db\dbAfMysql;
use core\lib\db\dbFMysql;
use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use plugins\shop\form\messagesFrom;
use plugins\shop\models\companyModel;
use plugins\shop\models\configModel;
use plugins\shop\models\emailModel;
use plugins\shop\models\shopCheckModel;
use plugins\shop\models\shopModel;
use plugins\shop\models\shopPasswordApplyModel;
use plugins\shop\models\shopAppealModel;
use plugins\shop\models\shopFeeModel;
use plugins\shop\models\userModel;
use Rap2hpoutre\FastExcel\FastExcel;

class shopController extends baseController
{

    /**
     * 获取店铺列表
     */
    public function getList()
    {
        $param = array_intersect_key($_GET, array_flip([
            'shop_number', 'phone_card', 'email','email_safe_phone_number','email_assistant_email','credit_card','receive_card',
            'company', 'legal_person', 'coordinator', 'shop_name',
            'business_manager', 'shop_site', 'register_type', 'company_status',
            'account_type', 'vat_register_status', 'epr_register_status', 'is_legal_person_credit_card',
            'activation_date','register_date','page', 'page_size'
        ]));

        $model = new shopModel();
        $list = $model->getList($param);

        $lx_shop_ids = array_column($list['list'], 'lx_shop_id');

        // 增加领星店铺名称
        if (!empty($lx_shop_ids)) {
            $afdb = dbAfMysql::getInstance();
            $afdb->table('shop_list')->field('sid, nick_name, lx_name');
            $afdb->whereIn('sid', $lx_shop_ids);
            $shop_list = $afdb->list();
            $shop_list = array_column($shop_list, null, 'sid');
        }

        foreach ($list['list'] as &$item) {
            $item['lx_be_name'] = $shop_list[$item['lx_shop_id']]['lx_name'] ?? '';
            $item['lx_fe_name'] = $shop_list[$item['lx_shop_id']]['nick_name'] ?? '';
        }

        returnSuccess($list);
    }

    /**
     * 获取店铺详情
     */
    public function getDetail()
    {
        $id = intval($_GET['id'] ?? 0);
        if (empty($id)) {
            returnError('ID不能为空');
        }

        $model = new shopModel();
        $detail = $model->getDetail($id);
        if (!$detail) {
            returnError('数据不存在');
        }

        $detail = $model->formatItem($detail);
        $detail['lx_shop_name'] = null;
        $detail['goods'] = [];

        // 增加注册任务
        $shop_db = dbShopMysql::getInstance();
        $register = $shop_db->table('shop_register')->field('id')->where('shop_id = :shop_id', ['shop_id' => $id])->one();
        $detail['shop_register_id'] = $register['id'] ?? null;

        // 增加领星店铺名称
        if ($detail['lx_shop_id']) {
            $sid = $detail['lx_shop_id'];
            $afdb = dbAfMysql::getInstance();
            $fdb = dbFMysql::getInstance();
            $afdb->table('shop_list')->field('sid, nick_name, lx_name');
            $afdb->andWhere('sid = :sid', ['sid' => $detail['lx_shop_id']]);
            $one = $afdb->one();
            $detail['lx_be_name'] = $one['lx_name'];
            $detail['lx_fe_name'] = $one['nick_name'];

            // 1、从lingxing_listing里获取sid对应的sku和asin
            $listing = $afdb->table('lingxing_listing')
                ->where('sid = :sid and status = 1',['sid'=>$sid])
                ->field('asin,local_sku')
                ->list();
            $sku = array_column($listing, 'local_sku');
            $sku = array_values(array_filter(array_unique($sku)));


            // 在财务goods表中查询sku对应的
            $goods = $fdb->table('goods')
                ->field('lingxing_id, product_name, cid')
                ->where('where status = 1')
                ->whereIn('sku', $sku)
                ->list();

            $product_name = array_column($goods, 'product_name');
            $product_name = array_values(array_filter(array_unique($product_name)));
            $detail['goods'] = $product_name;
        }

        returnSuccess($detail);
    }

    /**
     * 新增店铺
     */
    public function add()
    {
        $paras_list = shopModel::$paras_list;
        $paras_list['legal_person'] = ['法人信息'];
        $paras_list['company'] = ['公司信息'];
        $param = array_intersect_key($_POST, array_flip(array_keys($paras_list)));
        $id = $_POST['id'] ?? 0;
        $id && $param['id'] = $id;

        $model = new shopModel();

        // 唯一性校验
        $detail = $model->getByShopNumber($param['shop_number'], $id);
        if ($detail) {
            returnError('店铺编号已存在');
        }

        try {
            $params = $model->dataValidCheck($param, shopModel::$paras_list);

            if ($id) {
                // 验证数据正确性
                $detail = $model->getById($id);
                if (!$detail) {
                    returnError('数据不存在');
                }
                $model->edit($param, $id, $detail);
                returnSuccess([], '编辑成功');
            } else {
                $result = $model->add($params);
                returnSuccess(['id' => $result], '新增成功');
            }
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }
    }

    public function editUseInfo()
    {
        $paras_list = shopModel::$use_paras_list;
        $param = array_intersect_key($_POST, array_flip(array_keys($paras_list)));
        $id = $_POST['id'] ?? 0;

        try {
            $model = new shopModel();
            $params = $model->useDataValidCheck($param, shopModel::$use_paras_list);

            // 验证数据正确性
            $detail = $model->getById($id);
            if (!$detail) {
                returnError('数据不存在');
            }
            $model->edit($param, $id, $detail);
            returnSuccess([], '编辑成功');
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 日检记录相关接口
     */
    
    public function getDailyCheckList()
    {
        $page = intval($_GET['page'] ?? 1);
        $pageSize = intval($_GET['page_size'] ?? 20);
        $shop_id = intval($_GET['shop_id'] ?? 0);
        if (empty($shop_id)) {
            returnError('店铺ID不能为空');
        }
        $checkModel = new shopCheckModel();
        $result = $checkModel->getList([
            'shop_id' => $shop_id,
            'check_type' => '1',
            'page' => $page,
            'page_size' => $pageSize,
        ]);
        returnSuccess($result);
    }

    public function getDailyCheckDetail()
    {
        $shop_id = intval($_GET['shop_id'] ?? 0);
        $checkModel = new shopCheckModel();
        $check_date = date('Y-m-d', time());
        $detail = $checkModel->getByCheckDate($shop_id, $check_date, '1');
        $detail = $checkModel->formatItem($detail);
        returnSuccess($detail);
    }

    public function createDailyCheck()
    {
        $checkModel = new shopCheckModel();
        $param = array_intersect_key($_POST, array_flip(array_keys($checkModel::$paras_list)));
        $param['check_date'] = date('Y-m-d', time());

        $model = new shopModel();
        $detail = $model->getById($param['shop_id']);

        // 查询是否存在当天的记录
        $dailyData = $checkModel->getByCheckDate($param['shop_id'], $param['check_date'], '1');

        if ($dailyData) {
            $checkModel->edit($param, $dailyData['id'], $dailyData);
        } else {
            $param['check_type'] = '1';
            $checkModel->add($param);
        }

        $error_msg = [];
        $param['check_detail']['transfer'] != '是' && $error_msg[] = '【是否转账】';
        $param['check_detail']['assessment'] == '有' && $error_msg[] = '【有无绩效】';
        $param['check_detail']['all_site'] == '否' && $error_msg[] = '【是否开通全站点】';


        if (!empty($error_msg)) {
            $error_msg = implode('、', $error_msg);
            $users = configModel::noticeUser('shop', 'day_check', [
                'shop_id' => $detail['id'],
                'shop_coordinator' => $detail['coordinator']
            ]);
            if (!empty($users)) {
                messagesFrom::senMeg($users, 1 , "【{$detail['shop_number']}】日检发现{$error_msg}异常，请及时关注", $param['shop_id'], '', '店铺日检异常');
            }
        }
        returnSuccess([], '店铺日检成功');
    }

    /**
     * 周检记录相关接口
     */
    
    public function getWeeklyCheckList()
    {
        $page = intval($_GET['page'] ?? 1);
        $pageSize = intval($_GET['page_size'] ?? 20);
        $shop_id = intval($_GET['shop_id'] ?? 0);
        if (empty($shop_id)) {
            returnError('店铺ID不能为空');
        }
        $checkModel = new shopCheckModel();
        $result = $checkModel->getList([
            'shop_id' => $shop_id,
            'check_type' => '2',
            'page' => $page,
            'page_size' => $pageSize,
        ]);
        returnSuccess($result);
    }

    public function getWeeklyCheckDetail()
    {
        $shop_id = intval($_GET['shop_id'] ?? 0);
        $checkModel = new shopCheckModel();
        $check_date = date('Y-m-d', time());
        $detail = $checkModel->getByCheckDate($shop_id, $check_date, '2');
        $detail = $checkModel->formatItem($detail);
        returnSuccess($detail);
    }

    public function createWeeklyCheck()
    {
        $checkModel = new shopCheckModel();
        $param = array_intersect_key($_POST, array_flip(array_keys($checkModel::$paras_list)));
        $param['check_date'] = date('Y-m-d', time());

        $model = new shopModel();
        $detail = $model->getById($param['shop_id']);

        // 查询是否存在当天的记录
        $weekData = $checkModel->getByCheckDate($param['shop_id'], $param['check_date'], '2');

        if ($weekData) {
            $checkModel->edit($param, $weekData['id'], $weekData);
        } else {
            $param['check_type'] = '2';
            $checkModel->add($param);
        }

        $error_msg = [];
        $param['check_detail']['feedback'] == '无' && $error_msg[] = '【Feedback】';
        $param['check_detail']['seller'] == '无' && $error_msg[] = '【卖家账户信息】';
        $param['check_detail']['assessment'] == '无' && $error_msg[] = '【货件绩效】';
        $param['check_detail']['inventory'] == '无' && $error_msg[] = '【在售信息库存】';
        $param['check_detail']['email'] == '无' && $error_msg[] = '【邮件】';
        $param['check_detail']['case'] == '无' && $error_msg[] = '【Case】';

        if (!empty($error_msg)) {
            $error_msg = implode('、', $error_msg);
            $users = configModel::noticeUser('shop', 'week_check',[
                'shop_id' => $detail['id'],
                'shop_coordinator' => $detail['coordinator']
            ]);
            if (!empty($users)) {
                messagesFrom::senMeg($users, 1 , "【{$detail['shop_number']}】周内定检发现{$error_msg}异常，请及时关注", $param['shop_id'], '', '店铺周内定检异常');
            }
        }
        returnSuccess([], '店铺周检成功');
    }

    /**
     * 申诉相关接口
     */
    
    public function getAppealList()
    {
        $param = array_intersect_key($_GET, array_flip(['page', 'page_size', 'shop_id']));
        if (empty($param['shop_id'])) {
            returnError('店铺ID不能为空');
        }
        $param['page'] = intval($param['page']) ?: 1;
        $param['page_size'] = intval($param['page_size']) ?: 20;
        $appealModel = new shopAppealModel();
        $result = $appealModel->getList($param);
        returnSuccess($result);
    }

    // 申诉
    public function createAppeal()
    {
        $paras_list = shopAppealModel::$paras_list;
        $param = array_intersect_key($_POST, array_flip(array_keys($paras_list)));

        try {
            $model = new shopModel();
            $detail = $model->getById($param['shop_id']);

            $appealModel = new shopAppealModel();
            $id = $appealModel->add($param);
            $user_name = userModel::$wname;

            if ($param['complaint_type'] == $appealModel::COMPLAINT_TYPE_SELF) {
                $users = configModel::noticeUser('appeal', 'self', ['dep_id' => $detail['dep_id']]);
                $title = '店铺自申诉';
                $content = "【{$user_name}】提交了【{$detail['shop_number']}】的【{$param['issue_type']}】自申诉";
            } elseif ($param['complaint_type'] == $appealModel:: COMPLAINT_TYPE_SUPPLIER) {
                $users = configModel::noticeUser('appeal', 'supplier', ['dep_id' => $detail['dep_id']]);
                $title = '店铺服务商申诉';
                $content = "【{$user_name}】提交了【{$detail['shop_number']}】的【{$param['issue_type']}】服务商申诉，请您及时配置跟进人";
            }
            if (!empty($users)) {
                messagesFrom::senMeg($users, 1 , $content, $id, '', $title);
            }
            returnSuccess(['id' => $id], '申诉成功');
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }

    }

    // 申请查看密码
    public function applyPasswordView()
    {
        $shopPasswordModel = new shopPasswordApplyModel();

        $param = array_intersect_key($_POST, array_flip(['shop_id', 'apply_type', 'apply_reason']));

        $model = new shopModel();
        $detail = $model->getById($param['shop_id']);
        try {
            $id = $shopPasswordModel->createApply($param);
        }  catch (\Exception $e) {
            returnError($e->getMessage());
        }
        if (!$id) {
            returnError('申请失败');
        }
        $user_name = userModel::$wname;
        $password_type = [];
        if (in_array('1', $param['apply_type'])) $password_type[] = '邮箱密码';
        if (in_array('2', $param['apply_type'])) $password_type[] = '店铺密码';
        $password_type = implode('、', $password_type);

        $users = configModel::noticeUser('shop', 'password', [
            'shop_id' => $detail['id'],
            'shop_coordinator' => $detail['coordinator']
        ]);
        messagesFrom::senMeg($users, 1 , "【{$user_name}】提交了【{$detail['shop_number']}】的【{$password_type}】查看申请，请您及时审核该申请", $id, '', '密码查看申请');

        returnSuccess(['id' => $id], '申请成功');
    }

    public function getApplyPasswordList() {
        $shop_id = intval($_GET['shop_id'] ?? 0);
        if (empty($shop_id)) {
            returnError('店铺ID不能为空');
        }
        $shopPasswordModel = new shopPasswordApplyModel();
        $result = $shopPasswordModel->getList([
            'shop_id' => $shop_id,
            'status' => $shopPasswordModel::STATUS_PENDING
        ]);

        foreach ($result as &$item) {
            $item = $shopPasswordModel->formatItem($item);
        }
        returnSuccess($result);
    }

    public function approvePasswordView()
    {
        $data = $_POST;
        if (empty($data['data'])) {
            returnError('data不能为空');
        }

        foreach ($data['data'] as $item) {
            if (empty($item['id']) || !isset($item['audit_status'])) {
                returnError('参数错误');
            }
            // 审批通过
            if ($item['audit_status'] == 1) {
                if (empty($item['view_count']) || empty($item['view_minutes'])) {
                    returnError('查看次数和查看时长不能为空');
                }
            }
        }
        $shopPasswordModel = new shopPasswordApplyModel();
        $ids = array_column($data['data'], 'id');
        $list = $shopPasswordModel->getList([
            'id' => $ids,
            'status' => $shopPasswordModel::STATUS_PENDING
        ]);
        $list = array_column($list, null ,'id');

        $update_data = [];

        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_wid', 'user_id');

        $shops = redisCached::getShop();
        $shops = array_column($shops, 'shop_number', 'id');

        foreach ($data['data'] as $item) {
            $item_data = $list[$item['id']] ?? [];
            $item_data['apply_type'] = json_decode($item_data['apply_type'], true) ?? [];
            if (empty($item_data)) continue;
            $update_data[] = [
                'id' => $item['id'],
                'status' => $item['audit_status'] == 1 ? $shopPasswordModel::STATUS_APPROVED : $shopPasswordModel::STATUS_REJECTED,
                'view_count' => $item['view_count'] ?? 0,
                'view_minutes' => $item['view_minutes'] ?? 0,
                'audit_remark' => $item['audit_remark'] ?? '',
                'audit_user_id' => userModel::$qwuser_id ?? 0,
            ];
            $password_type = [];
            if (in_array('1', $item_data['apply_type'])) $password_type[] = '邮箱密码';
            if (in_array('2', $item_data['apply_type'])) $password_type[] = '店铺密码';
            $password_type = implode('、', $password_type);
            $shop_number = $shops[$item_data['shop_id']] ?? '';
            if ($item['audit_status'] == 1) {
                messagesFrom::senMeg([$users[$item_data['user_id']]], 1 , "您的【{$shop_number}】的【{$password_type}】查看申请已审批通过", $item['id'], '', '密码审批通过');
            } else {
                messagesFrom::senMeg([$users[$item_data['user_id']]], 1 , "您的【{$shop_number}】的【{$password_type}】查看申请审批未通过，不可查看该密码", $item['id'], '', '密码审批不通过');
            }
        }

        if (!empty($update_data)) {
            $shopPasswordModel->updateBatch($update_data);
        }


        returnSuccess([], '审批成功');
    }

    public function viewPassword()
    {
        $shopId = intval($_GET['shop_id'] ?? 0);
        if (empty($shopId)) {
            returnError('店铺ID不能为空');
        }

        $userId = $_SESSION['user_id'] ?? 0;

        $shopPasswordModel = new shopPasswordApplyModel();
        $list = $shopPasswordModel->getList([
            'shop_id' => $shopId,
            'user_id' => $userId,
            'status' => $shopPasswordModel::STATUS_APPROVED
        ]);

        $apply_type = [];
        $update_data = [];
        foreach ($list as $item) {
            if ($item['view_count'] <= 0) continue;
            $item['apply_type'] = json_decode($item['apply_type'], true);
            foreach ($item['apply_type'] as $type) {
                $apply_type[$type] = [
                    'view_count' => $item['view_count'],
                    'view_minutes' => $item['view_minutes'],
                ];
            }
            $update_data[] = [
                'id' => $item['id'],
                'view_count' => $item['view_count'] - 1,
            ];
        }
        if (!empty($update_data)) {
            $shopPasswordModel->updateBatch($update_data);
        }
        $ret = [];

        $shpModel = new shopModel();
        $shopInfo = $shpModel->getById($shopId);
        if (array_key_exists('1', $apply_type)) {
            $email = (new emailModel())->getById($shopInfo['email_id']);
            $ret['email'] = [
                'email_password' => $email['email_password'],
                'view_count' => $apply_type['1']['view_count'],
                'view_minutes' => $apply_type['1']['view_minutes'],
            ];
        }
        if (array_key_exists('2', $apply_type)) {
            $ret['shop'] = [
                'shop_password' => $shopInfo['shop_password'],
                'view_count' => $apply_type['2']['view_count'],
                'view_minutes' => $apply_type['2']['view_minutes'],
            ];
        }

        returnSuccess($ret);
    }

    public function getLxShopList()
    {
        $param = array_intersect_key($_GET, array_flip(['page', 'page_size', 'shop_name']));

        // shop绑定过的lx_shop_id
        $lx_shop_ids = dbShopMysql::getInstance()->table('shop')
            ->field('lx_shop_id')
            ->list();
        $lx_shop_ids = array_column($lx_shop_ids, 'lx_shop_id');
        $lx_shop_ids = array_values(array_unique(array_filter($lx_shop_ids)));

        $page = intval($param['page']) ?: 1;
        $page_size = intval($param['page_size']) ?: 20;
        $afdb = dbAfMysql::getInstance();
        $afdb->table('shop_list')->field('sid, nick_name, lx_name');
        
        if (!empty($lx_shop_ids)) {
            $lx_shop_ids = implode(',', $lx_shop_ids);
            $afdb->andWhere('sid NOT IN (:lx_shop_ids)', ['lx_shop_ids' => $lx_shop_ids]);
        }

        if (isset($param['shop_name']) && !empty($param['shop_name'])) {
            $afdb->andWhere('lx_name LIKE :shop_name', ['shop_name' => '%' . $param['shop_name'] . '%']);
        }
        $list = $afdb->pages($page, $page_size);
        returnSuccess($list);

    }

    public function getLxShop()
    {
        $sid = $_GET['sid'] ?? 0;
        if (empty($sid)) returnError('ID必填');

        $afdb = dbAfMysql::getInstance();
        $fdb = dbFMysql::getInstance();
        $shopInfo = $afdb->table('shop_list')->field('id, sid, nick_name, lx_name')->where('sid = :sid', ['sid' => $sid])->one();

        // 1、从lingxing_listing里获取sid对应的sku和asin
        $listing = $afdb->table('lingxing_listing')
            ->where('sid = :sid and status = 1',['sid'=>$sid])
            ->field('asin,local_sku')
            ->list();
        $asin = array_column($listing, 'asin');
        $asin = array_values(array_filter(array_unique($asin)));
        $sku = array_column($listing, 'local_sku');
        $sku = array_values(array_filter(array_unique($sku)));

        // 从listing_data中获取asin对应的负责人
        $listing_data = $afdb->table('listing_data')
            ->field('asin, yunying_ids, leader_ids')
            ->whereIn('asin', $asin)
            ->list();
        $leader_ids = [];
        $yunying_ids = [];
        foreach ($listing_data as $item) {
            $item['yunying_ids'] = json_decode($item['yunying_ids'], true);
            $yunying_ids = array_merge($yunying_ids, $item['yunying_ids']);
            $item['leader_ids'] = json_decode($item['leader_ids'], true);
            $leader_ids = array_merge($leader_ids, $item['leader_ids']);
        }
        // 去重
        $leader_ids = array_values(array_filter(array_unique($leader_ids)));
        $yunying_ids = array_values(array_filter(array_unique($yunying_ids)));

        // 先查询所有类目
        $goods_category = $fdb->table('goods_category')
            ->field('cid, parent_cid, title')
            ->where('where is_delete = 0')
            ->list();

        foreach ($goods_category as &$cate) {
            $route = [];
            self::getCategoryRoute($goods_category, $cate['cid'], $route);
            $route = array_reverse($route);
            $cate['level_2'] = isset($route[1]) ? [
                'cid' => $route[1]['cid'],
                'title' => $route[1]['title'],
                'parent_cid' => $route[1]['parent_cid'],
            ] : [];
        }
        $goods_category = array_column($goods_category, null,'cid');

        // 在财务goods表中查询sku对应的
        $goods = $fdb->table('goods')
            ->field('lingxing_id, product_name, cid')
            ->where('where status = 1')
            ->whereIn('sku', $sku)
            ->list();

        $product_name = array_column($goods, 'product_name');
        $product_name = array_values(array_filter(array_unique($product_name)));
        $cids = array_column($goods, 'cid');
        $cids = array_values(array_filter(array_unique($cids)));

        $category = [];
        foreach ($cids as $c) {
            $item = $goods_category[$c];
            if (!empty($item['level_2'])) {
                $category[$item['level_2']['cid']] = $item['level_2'];
            }
        }

        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');
        $leaders = [];
        foreach ($leader_ids as $leader_id) {
            $leaders[] = [
                'id' => $leader_id,
                'name' => $users[$leader_id],
            ];
        }

        $yunyings = [];
        foreach ($yunying_ids as $yunying_id) {
            $yunyings[] = [
                'id' => $yunying_id,
                'name' => $users[$yunying_id],
            ];
        }

        returnSuccess([
            'sid' => $sid,
            'nick_name' => $shopInfo['nick_name'] ?? '',
            'lx_name' => $shopInfo['lx_name'] ?? '',
            'goods' => $product_name,
            'category' => array_values($category),
            'leaders' => $leaders,
            'yunyings' => $yunyings
        ]);

    }

    // 获取日志
    public static function getLog()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $db = dbShopMysql::getInstance();
        $list = $db->table('operation_log')->where('where table_name = :table_name and table_id = :table_id',
            ['table_name' => 'domain', 'table_id' => $id])->order('updated_at desc')->list();

        $users =  redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        $model = new shopModel();

        foreach ($list as &$item) {
            $item['operator_name'] = $users[$item['operator']] ?? '';
            $attach = json_decode($item['attach'], true);
            $item['before'] = $attach['before'] ?? null;
            $item['before'] = $model->formatItem($item['before']);
            $item['after'] = $attach['after'] ?? null;
            $item['after'] = $model->formatItem($item['after']);
            unset($item['attach']);
        }
        returnSuccess($list);
    }

    /**
     * 批量导入
     */
    public function import()
    {
        $paras_list = array('excel_src');
        $request_list = ['excel_src' => '表格链接'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

        if (empty($param['excel_src'])) {
            returnError('表格链接不能为空');
        }

        $excel_url = SELF_FK . $param['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格文件不存在');
        }

        $data = (new FastExcel())->import($excel_url, function ($row) {
            // 将每一项 trim 去空格，再判断整行是否为空
            $cleaned = collect($row)->map(function ($value) {
                if (is_string($value)) {
                    return trim((string)$value);
                }
                return $value;
            });
            // 全部为空则跳过
            if ($cleaned->filter()->isEmpty()) {
                return null;
            }
            return $cleaned->toArray(); // 返回处理后的数据
        });
        $first = $data['0'];
        if (
            //法人
            empty($first['法人名字']) || empty($first['注册公司']) || empty($first['法人手机号']) || empty($first['是否可用于注册亚马逊']) ||
            empty($first['身份证号']) || empty($first['性别']) || empty($first['出生日期']) ||
            empty($first['身份证地址']) || empty($first['身份证有效起始日期']) || empty($first['身份证有效结束日期']) ||
            !isset($first['常驻城市']) || !isset($first['工作情况']) || !isset($first['学历']) ||
            !isset($first['推荐渠道']) || !isset($first['推荐人']) || !isset($first['关系说明']) || empty($first['报名日期']) ||
            empty($first['法人的对接人-公司内部（也就是店铺费用支付对接人）']) || !isset($first['法人配合度']) ||
            empty($first['信用卡情况']) || !isset($first['法人信用卡']) || !isset($first['法人信用卡有效开始日期']) || !isset($first['法人信用卡有效结束日期']) ||
            // 公司
            empty($first['公司名称']) || empty($first['公司状态']) ||
            empty($first['公司国家']) || empty($first['公司注册城市']) || empty($first['注册类型（法人自注册、公司注册）']) ||
            empty($first['纳税人识别号']) || empty($first['公司注册对接人']) || empty($first['公司注册日期']) || empty($first['注册公司地址']) ||
            !isset($first['房屋编码（选填）']) || empty($first['营业执照保管方']) || !isset($first['银行公户账号']) || empty($first['记账报税对接人']) ||
            // 店铺
            empty($first['注册类型']) || empty($first['激活（交付）日期']) || empty($first['店铺编号']) || empty($first['账号类型']) ||
            empty($first['站点']) || !isset($first['价格（公司自注册类型不用填写）']) || !isset($first['币种']) ||
            empty($first['对接人']) || empty($first['招商经理']) || empty($first['店铺注册日期']) || empty($first['注册设备']) ||
            !isset($first['注册手机号']) || !isset($first['手机号持有人']) || !isset($first['注册邮箱']) ||
            !isset($first['邮箱密码']) || !isset($first['邮箱安全手机']) || !isset($first['邮箱辅助邮箱']) ||
            empty($first['店铺密码']) || empty($first['VAT注册情况']) || empty($first['EPR注册情况']) ||
            empty($first['信用卡']) || empty($first['信用卡有效开始日期']) || empty($first['信用卡有效结束日期']) ||
            empty($first['是否法人卡']) || empty($first['收款账号']) || empty($first['收款平台']) || empty($first['备注']) )
        {
            returnError('表头错误');
        }

        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_id', 'user_name');

        $credit_card = redisCached::getCreditCard();
        $credit_card = array_column($credit_card, null, 'card_number');

        $receive_card = redisCached::getReceiveCard();
        $receive_card = array_column($receive_card, 'id', 'card_number');

        $phone_card = redisCached::getPhoneCard();
        $phone_card = array_column($phone_card, null, 'phone_number');

        $email = redisCached::getEmail();
        $email = array_column($email, null, 'email_account');

        $model = new shopModel();
        $paras_list = $model::$paras_list;
        $import_data = [];
        $error_data = [];

        foreach ($data as $row) {
            $error_msg = [];

            empty($row['法人名字']) && $error_msg[] = '法人名字不能为空';
            empty($row['注册公司']) && $error_msg[] = '注册公司不能为空';
            empty($row['法人手机号']) && $error_msg[] = '法人手机号不能为空';
            empty($row['是否可用于注册亚马逊']) && $error_msg[] = '是否可用于注册亚马逊不能为空';
            empty($row['身份证号']) && $error_msg[] = '身份证号不能为空';
            empty($row['性别']) && $error_msg[] = '性别不能为空';
            empty($row['出生日期']) && $error_msg[] = '出生日期不能为空';
            $birth_date = null;
            try {
                $birth_date = $row['出生日期']->format('Y-m-d');
                if (empty($row['出生日期']) || strtotime($birth_date) === false) {
                    $error_msg[] = '出生日期格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '出生日期格式错误';
            }
            empty($row['身份证地址']) && $error_msg[] = '身份证地址不能为空';
            empty($row['身份证有效起始日期']) && $error_msg[] = '身份证有效起始日期不能为空';
            $start_date = null;
            try {
                $start_date = $row['身份证有效起始日期']->format('Y-m-d');
                if (empty($row['身份证有效起始日期']) || strtotime($start_date) === false) {
                    $error_msg[] = '身份证有效起始日期格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '身份证有效起始日期格式错误';
            }
            empty($row['身份证有效结束日期']) && $error_msg[] = '身份证有效结束日期不能为空';
            $end_date = null;
            try {
                $end_date = $row['身份证有效结束日期']->format('Y-m-d');
                if (empty($row['身份证有效结束日期']) || strtotime($end_date) === false) {
                    $error_msg[] = '身份证有效结束日期格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '身份证有效结束日期格式错误';
            }
            empty($row['报名日期']) && $error_msg[] = '报名日期不能为空';
            $register_date = null;
            try {
                $register_date = $row['报名日期']->format('Y-m-d');
                if (empty($row['报名日期']) || strtotime($register_date) === false) {
                    $error_msg[] = '报名日期格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '报名日期格式错误';
            }
            empty($row['法人的对接人-公司内部（也就是店铺费用支付对接人）']) && $error_msg[] = '法人的对接人-公司内部（也就是店铺费用支付对接人）不能为空';
            $internal_coordinator = null;
            if (!isset($users[$row['法人的对接人-公司内部（也就是店铺费用支付对接人）']])) {
                $error_msg[] = '法人的对接人不存在';
            } else {
                $internal_coordinator = $users[$row['法人的对接人-公司内部（也就是店铺费用支付对接人）']];
            }
            empty($row['信用卡情况']) && $error_msg[] = '信用卡情况不能为空';
            $credit_card_id = null;
            if (!empty($row['法人信用卡'])) {
                if (!isset($credit_card[$row['法人信用卡']])) {
                    $error_msg[] = '法人信用卡不存在';
                } else {
                    $credit_card_id = $credit_card[$row['法人信用卡']];
                }
            }
            $legal_person = [
                'name' => $row['法人名字'],
                'is_register_company' => $row['注册公司'] == '是' ? 1 : 0,
                'phone' => $row['法人手机号'],
                'amazon_available' => $row['是否可用于注册亚马逊'],
                'id_card' => $row['身份证号'],
                'gender' => $row['性别'],
                'birth_date' => $birth_date,
                'id_card_address' => $row['身份证地址'],
                'id_card_expire' => ($start_date && $end_date) ? [
                    $start_date,
                    $end_date
                ] : null,
                'city' => $row['常驻城市'] ?? '',
                'work_status' => $row['工作情况'] ?? '',
                'education' => $row['学历'] ?? '',
                'recommend_channel' => $row['推荐渠道'] ?? '',
                'recommender' => $row['推荐人'] ?? '',
                'relation_with_recommender' => $row['关系说明'] ?? '',
                'register_date' => $register_date,
                'internal_coordinator' => $internal_coordinator,
                'cooperation_level' => $row['法人配合度'] ?? '',
                'credit_card_status' => $row['信用卡情况'] ?? '',
                'credit_card_id' => $credit_card_id,
            ];
            empty($row['公司名称']) && $error_msg[] = '公司名称不能为空';
            empty($row['公司状态']) && $error_msg[] = '公司状态不能为空';
            empty($row['公司国家']) && $error_msg[] = '公司国家不能为空';
            empty($row['公司注册城市']) && $error_msg[] = '公司注册城市不能为空';
            empty($row['注册类型（法人自注册、公司注册）']) && $error_msg[] = '注册类型不能为空';
            empty($row['纳税人识别号']) && $error_msg[] = '纳税人识别号不能为空';
            empty($row['公司注册对接人']) && $error_msg[] = '公司注册对接人不能为空';
            empty($row['公司注册日期']) && $error_msg[] = '公司注册日期不能为空';
            $register_date = null;
            try {
                $register_date = $row['公司注册日期']->format('Y-m-d');
                if (empty($row['公司注册日期']) || strtotime($register_date) === false) {
                    $error_msg[] = '公司注册日期格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '公司注册日期格式错误';
            }
            empty($row['注册公司地址']) && $error_msg[] = '注册公司地址不能为空';
            empty($row['营业执照保管方']) && $error_msg[] = '营业执照保管方不能为空';
            $receive_card_id = null;
            if (!empty($row['银行公户账号'])) {
                if (!isset($receive_card[$row['银行公户账号']])) {
                    $error_msg[] = '银行公户账号不存在';
                } else {
                    $receive_card_id = $receive_card[$row['银行公户账号']];
                }
            }
            empty($row['记账报税对接人']) && $error_msg[] = '记账报税对接人不能为空';
            $accountants = [];
            if (!empty($row['记账报税对接人'])) {
                $u = explode(';', $row['记账报税对接人']);
                foreach ($u as $accountant) {
                    if (!isset($users[$accountant])) {
                        $error_msg[] = '记账报税对接人'.$accountant.'不存在';
                    } else {
                        $accountants[] = $users[$accountant];
                    }
                }
            }

            $company = [
                'company_name' => $row['公司名称'],
                'company_status' => $row['公司状态'],
                'register_country' => $row['公司国家'],
                'register_city' => $row['公司注册城市'],
                'register_type' => $row['注册类型（法人自注册、公司注册）'],
                'tax_number' => $row['纳税人识别号'],
                'register_coordinator' => $row['公司注册对接人'],
                'register_date' => $register_date,
                'register_address' => $row['注册公司地址'],
                'house_number' => $row['房屋编码（选填）'] ?? '',
                'license_keeper' => $row['营业执照保管方'],
                'receive_card_id' => $receive_card_id,
                'accountants' => $accountants,
                'remark' => $row['备注'] ?? '',
                'register_status' => companyModel::REGISTER_STATUS_SUCCESS
            ];

            empty($row['注册类型']) && $error_msg[] = '注册类型不能为空';
            $row['注册类型'] != '现号' && $error_msg[] = '注册类型只能是现号';
            empty($row['激活（交付）日期']) && $error_msg[] = '激活（交付）日期不能为空';
            $activation_date = null;
            try {
                $activation_date = $row['激活（交付）日期']->format('Y-m-d');
                if (empty($row['激活（交付）日期']) || strtotime($activation_date) === false) {
                    $error_msg[] = '激活（交付）日期格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '激活（交付）日期格式错误';
            }
            empty($row['店铺编号']) && $error_msg[] = '店铺编号不能为空';
            empty($row['账号类型']) && $error_msg[] = '账号类型不能为空';
            empty($row['站点']) && $error_msg[] = '站点不能为空';
            empty($row['对接人']) && $error_msg[] = '对接人不能为空';
            $coordinator = null;
            if (!isset($users[$row['对接人']])) {
                $error_msg[] = '对接人不存在';
            } else {
                $coordinator = $users[$row['对接人']];
            }
            empty($row['招商经理']) && $error_msg[] = '招商经理不能为空';
            empty($row['店铺注册日期']) && $error_msg[] = '店铺注册日期不能为空';
            $shop_register_date = null;
            try {
                $shop_register_date = $row['店铺注册日期']->format('Y-m-d');
                if (empty($row['店铺注册日期']) || strtotime($shop_register_date) === false) {
                    $error_msg[] = '店铺注册日期格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '店铺注册日期格式错误';
            }
            empty($row['注册设备']) && $error_msg[] = '注册设备不能为空';
            $phone_card_id = null;
            $email_id = null;
            if ($row['注册设备'] == '手机号') {
                empty($row['注册手机号']) && $error_msg[] = '注册手机号不能为空';
                empty($row['手机号持有人']) && $error_msg[] = '手机号持有人不能为空';
                if (!isset($phone_card[$row['注册手机号']])) {
                    $error_msg[] = '注册手机号不存在';
                } else {
                    $phone_card_id = $phone_card[$row['注册手机号']]['id'] ?? 0;
                    if ($row['手机号持有人'] != $phone_card[$row['注册手机号']]['user_name']) {
                        $error_msg[] = '手机号持有人不一致';
                    }
                }
            } elseif ($row['注册设备'] == '邮箱') {
                $email_id = $email[$row['注册邮箱']]['id'] ?? 0;
                if ($row['邮箱安全手机'] !=  $email[$row['注册邮箱']]['email_safe_phone_number']) {
                    $error_msg[] = '邮箱安全手机不一致';
                }
                if ($row['邮箱辅助邮箱'] !=  $email[$row['注册邮箱']]['email_assistant_email']) {
                    $error_msg[] = '邮箱辅助邮箱不一致';
                }
            }
            empty($row['店铺密码']) && $error_msg[] = '店铺密码不能为空';
            empty($row['VAT注册情况']) && $error_msg[] = 'VAT注册情况不能为空';
            empty($row['EPR注册情况']) && $error_msg[] = 'EPR注册情况不能为空';
            empty($row['信用卡']) && $error_msg[] = '信用卡不能为空';
            $credit_card_id = null;
            if (!empty($row['信用卡'])) {
                if (!isset($credit_card[$row['信用卡']])) {
                    $error_msg[] = '信用卡不存在';
                } else {
                    $credit_card_id = $credit_card[$row['信用卡']]['id'];
                }
            }
            empty($row['信用卡有效开始日期']) && $error_msg[] = '信用卡有效开始日期不能为空';
            $credit_card_start_date = null;
            try {
                $credit_card_start_date = $row['信用卡有效开始日期']->format('Y-m-d');
                if (empty($row['信用卡有效开始日期']) || strtotime($credit_card_start_date) === false) {
                    $error_msg[] = '信用卡有效开始日期格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '信用卡有效开始日期格式错误';
            }
            empty($row['信用卡有效结束日期']) && $error_msg[] = '信用卡有效结束日期不能为空';
            $credit_card_end_date = null;
            try {
                $credit_card_end_date = $row['信用卡有效结束日期']->format('Y-m-d');
                if (empty($row['信用卡有效结束日期']) || strtotime($credit_card_end_date) === false) {
                    $error_msg[] = '信用卡有效结束日期格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '信用卡有效结束日期格式错误';
            }
            empty($row['是否法人卡']) && $error_msg[] = '是否法人卡不能为空';
            empty($row['收款账号']) && $error_msg[] = '收款账号不能为空';
            $receive_card_id = null;
            if (!empty($row['收款账号'])) {
                $rc = explode(';', $row['收款账号']);
                foreach ($rc as $r) {
                    if (!isset($receive_card[$r])) {
                        $error_msg[] = '收款账号【'.$r.'】不存在';
                    } else {
                        $receive_card_id[] = $receive_card[$r];
                    }
                }
            }
            empty($row['收款平台']) && $error_msg[] = '收款平台不能为空';

            $item_data = [
                'legal_person' => $legal_person,
                'company' => $company,
                'register_type' => $row['注册类型'],
                'activation_date' => $activation_date,
                'shop_number' => $row['店铺编号'],
                'account_type' => $row['账号类型'],
                'shop_site' => $row['站点'],
                'price' => $row['价格（公司自注册类型不用填写）'] ?? '',
                'currency' => $row['币种'] ?? '',
                'coordinator' => $coordinator,
                'business_manager' => $row['招商经理'],
                'register_date' => $shop_register_date,
                'register_device' => $row['注册设备'],
                'phone_card_id' => $phone_card_id ?? null,
                'email_id' => $email_id ?? null,
                'credit_card_id' => $credit_card_id ?? null,
                'receive_card_id' => $receive_card_id ?? null,
                'shop_password' => $row['店铺密码'],
                'vat_register_status' => $row['VAT注册情况'],
                'epr_register_status' => $row['EPR注册情况'],
                'remark' => $row['备注'] ?? '',
            ];


            $check_row = $model->dataValidCheck($item_data, $paras_list, false);
            if (!empty($check_row['error'])) {
                $error_msg = array_merge($error_msg, $check_row['error']);
            }

            if (!empty($error_msg)) {
                $row['失败原因'] = implode(';', $error_msg);
                $error_data[] = $row;
                continue;
            }

            $import_data[] = $item_data;
            try {
                $id = $model->add($item_data, '导入');
            } catch (\Exception $e) {
                $error_msg[] = $e->getMessage();
            }
        }

        if (count($error_data) > 0) {
            // 使用项目根目录动态构建导出文件路径
            $projectRoot = SELF_FK;
            $src = '/public/shop/temp/error/user_import_errors' . time() . rand(100, 1000) . '.xlsx';
            $exportPath = $projectRoot . $src;

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径

            returnSuccess(['error_file' => $src, 'error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $error_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $import_data], '导入成功');
        }

    }

    /**
     * 导出
     */
    public function export()
    {
        $param = array_intersect_key($_POST, array_flip(['shop_number', 'phone_card', 'email','email_safe_phone_number','email_assistant_email','credit_card','receive_card',
            'company', 'legal_person', 'coordinator', 'shop_name',
            'business_manager', 'shop_site', 'register_type', 'company_status',
            'account_type', 'vat_register_status', 'epr_register_status', 'is_legal_person_credit_card',
            'activation_date','register_date',
            'keys', 'ids',
        ]));

        $model = new shopModel();
        $data = $model->getList($param, 'id desc', true);

        if (empty($data)) {
            returnError('没有可导出的数据');
        }

        $export_data = [];
        foreach ($data as $item) {
            $keys = [
                '注册日期', '店铺编号', '账号类型', '店铺站点', '价格',
                '币种', '对接人', '招商经理', '店铺注册日期', '注册设备',
                '注册手机号', '手机号持有人', '注册邮箱', '邮箱密码', '邮箱安全手机','邮箱辅助邮箱',
                '店铺密码', 'VAT注册情况', 'EPR注册情况',
                '信用卡', '信用卡有效开始日期', '信用卡有效结束日期', '是否法人卡', '收款账号', '收款平台', '备注',
                // 使用信息
                '使用情况', '店铺状态', '账号用途', '账号状况保障计划', '账户状况保障计划电话',
                '品牌', '品牌备案情况', '账号品牌权限', '销售权益', 'Listing编辑权', '产品使用规划',
                '产品分布情况', '卖家ID', '公司状态', '公司状态更新时间', '店铺后台名', '店铺前台名',
                'Identity verification', 'Bank Account verification', 'Address verification',
                'Phone Number', 'Tax ID Number', '消费者告知法案', '视频验证', '购买保险',
                '保险期限', '亚马逊物流新品入仓优惠计划', 'KYC', '真实性经营审核', 'WEEE', 'EPR',
                '欧带信息', '账号类型(使用信息)',

            ];
            $sortedItem = [];
            foreach ($keys as $key) {
                if (!empty($param['keys']) && !in_array($key, $param['keys'])) continue;
                if ($key == '信用卡有效开始日期') {
                    $sortedItem[$key] = $item['信用卡有效期'][0] ?? '';
                } elseif ($key == '信用卡有效结束日期') {
                    $sortedItem[$key] = $item['信用卡有效期'][1] ?? '';
                } elseif ($key == '收款账号') {
                    if (!empty($item['收款账号'])) {
                        $sortedItem[$key] = implode(';', array_column($item['收款账号'], 'card_number'));
                    } else {
                        $sortedItem[$key] = '';
                    }
                } elseif ($key == '收款平台') {
                    if (!empty($item['收款账号'])) {
                        $sortedItem[$key] = implode(';', array_column($item['收款账号'], 'receive_platform'));
                    } else {
                        $sortedItem[$key] = '';
                    }
                } elseif ($key == '品牌') {
                    if (!empty($item[$key])) {
                        $sortedItem[$key] = implode(';', array_column($item[$key], 'brand_name'));
                    } else {
                        $sortedItem[$key] = '';
                    }
                } elseif ($key == '消费者告知法案') { // 这是文件对象数组
                    continue;
                }  elseif ($key == '保险期限') { // 这是文件对象数组
                    if (!empty($item[$key])) {
                        $sortedItem[$key] = implode(';', $item[$key]);
                    } else {
                        $sortedItem[$key] = '';
                    }
                } else {
                    $sortedItem[$key] = $item[$key] ?? '';
                }
            }
            $export_data[] = $sortedItem;
        }

        $filePath = '/public/shop/temp/company_export_' . date('YmdHis') . '.xlsx';
        (new FastExcel($export_data))->export(SELF_FK.$filePath);

        // 导出数据
        returnSuccess(['src' => $filePath], '导出成功');

    }

    public function editBatch()
    {
        $paras_list = array('data');
        $request_list = ['data' => '编辑数据'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

        if (count($param['data']) > 500) {
            returnError('批量编辑数据不能超过500条');
        }

        $shop_numbers = array_column($param['data'], 'shop_number');
        $shop_numbers = array_unique($shop_numbers);
        if (count($shop_numbers) != count($param['data'])) {
            returnError('批量编辑数据不能有重复的店铺编号');
        }

        $model = new shopModel();
        $result = $model->editBatch($param['data']);

        returnSuccess($result);

    }

    public static function getCategoryRoute($category, $id, &$route = []) : bool
    {
        foreach ($category as $cate) {
            if ($cate['cid'] == $id) {
                $route[] = $cate;
                self::getCategoryRoute($category, $cate['parent_cid'], $route);
            }
        }
        return true;
    }

}
