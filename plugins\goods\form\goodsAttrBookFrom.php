<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/25 17:31
 */

namespace  plugins\goods\form;

use core\lib\db\dbMysql;

class goodsAttrBookFrom
{
    public static function getAtrrData(int $goods_id,int $attr_book_id) {
        $db = dbMysql::getInstance();
        $file = $db->table('goods_attachment')
            ->where('where goods_id=:goods_id and is_delete=0 and type = 2',['goods_id'=>$goods_id])
            ->one();
        if (!$file) {
            SetReturn(-1,'该产品未上传规格书');
        }
        //识别规格书
        $url = SELF_FK.$file['url'];
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($url);
        // 获取第一个工作表
        $worksheet = $spreadsheet->getSheet(0);
        $highestRow = $worksheet->getHighestRow();
        $data = [];
        for ($row = 4; $row <= $highestRow; $row++) {
            $item = [];
            $item['id'] = $worksheet->getCell('A' . $row)->getValue();
            if (empty($item['id'])) {
                continue;
            }
            $item['row_name'] = $worksheet->getCell('B' . $row)->getValue();
            $item['data'] = ($worksheet->getCell('C' . $row)->getValue())??'';
            $data[] = $item;
        }
        if (!count($data)) {
            SetReturn(-1,'未识别到产品规格书中的数据');
        }
        //保存
        $data_json = json_encode($data,JSON_UNESCAPED_UNICODE);
        if ($attr_book_id) {
            $db->table('goods_attr_book')
                ->where('where id=:attr_book_id',['attr_book_id'=>$attr_book_id])
                ->update([
                    'attr_data'=>$data_json,
                    'updated_time'=>date('Y-m-d H:i:s')
                ]);
        } else {
            $db->table('goods_attr_book')
                ->insert([
                    'goods_id'=>$goods_id,
                    'attr_data'=>$data_json,
                    'created_time'=>date('Y-m-d H:i:s')
                ]);
        }
        return $data;
    }
}