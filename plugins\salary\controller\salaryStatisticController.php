<?php

namespace plugins\salary\Controller;

use core\lib\db\dbMysql;
use core\lib\db\dbSMysql;
use plugins\salary\models\salaryCalculationModel;

class salaryStatisticController
{

    // 列表
    public function getDetail() {
        $paras_list = array('corp_id', 'dep_id', 'month');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $month = json_decode($param['month'], true) ?? null;
        if (empty($month)) returnError('月份不能为空');

        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();

        $sdb->table('salary_calculation_user', 'scu')
            ->field('scu.result, sc.month, scu.user_id')
            ->leftJoin('salary_calculation', 'sc', 'sc.id = scu.calc_id')
            ->where('where sc.status = :status and scu.status = :status', ['status' => salaryCalculationModel::STATUS_FINISHED]);
        if ($param['month']) {
            $month = json_decode($param['month'], true);
            if (!empty($month)) {
                $sdb->andWhere('sc.month >= :start_month and sc.month <= :end_month', ['start_month' => $month[0], 'end_month' => $month[1]]);
            }

        }
        if (isset($param['corp_id'])) {
            $corp_id = json_decode($param['corp_id'], true);
            if (!empty($corp_id)) {
                $sdb->whereIn("JSON_EXTRACT(scu.result, '$.user_info.corp_id')", $corp_id);
            }
        }
        if (isset($param['dep_id'])) {
            $dep_id = json_decode($param['dep_id'], true);
            if (!empty($dep_id)) {
                $sdb->whereIn("JSON_EXTRACT(scu.result, '$.user_info.user_wmain_department')", $dep_id);
            }
        }
        $list = $sdb->list();

        // 公司信息
        $corps = $sdb->table('corp')->list();
        $corps = array_column($corps, 'name', 'id');

        // 部门信息
        $department = $db->table('qwdepartment')->field('wp_id, name')->list();
        $department = array_column($department, 'name', 'wp_id');

        // 总计
        $total_list = [
            'should_salary' => 0,
            'real_salary_total' => 0,
            'total_corp' => 0,
            'performance' => 0,
            'total' => 0,
        ];
        $corp_list = []; // 公司统计
        $dep_list = []; // 部门总计
        $project_list = []; // 项目总计
        $user_list = [];
        $month_list = [];

        $is_single_month = $month[0] == $month[1];

        // 初始化月份
        if (!$is_single_month) {
            $date = $month[0];
            while (strtotime($date) <= strtotime($month[1])) {
                $month_list[$date] = 0;
                $date = date('Y-m', strtotime($date . '-01 +1 month'));
            }
        }

        foreach ($list as &$item) {
            $result = json_decode($item['result'], true) ?? null;
            $item['result'] = $result;
            if (!in_array($item['user_id'], $user_list)) {
                $user_list[] = $item['user_id'];
            }
            // 应发工资
            $should_salary = $result['should_salary'] ?? 0;
            // 实发工资
            $real_salary_total = $result['real_salary_total'] ?? 0;
            // 五险一金公司部分
            $total_corp = $result['total_corp'] ?? 0;
            // 个人社保
            $insurance_total_personal = $result['insurance_fund_tax']['user_social_insurance']['total_personal'] ?? 0;
            // 个人公积金
            $housing_fund_total_personal = $result['insurance_fund_tax']['user_housing_fund']['total_personal'] ?? 0;
            // 绩效提成
            $performance = $result['assessment']['performance'] ?? 0;
            // 个税
            $total_tax_refund = $result['insurance_fund_tax']['user_tax']['total_tax_refund'] ?? 0;
            // 人力成本总计 【员工实发合计工资】+【个人社保】+【个人公积金】+【企业缴纳社保部分】+【企业缴纳公积金部分】+【个税】
            $total = $real_salary_total + $total_corp + $insurance_total_personal + $housing_fund_total_personal + $total_tax_refund;

            $total_list['should_salary'] += $should_salary;
            $total_list['real_salary_total'] += $real_salary_total;
            $total_list['total_corp'] += $total_corp;
            $total_list['performance'] += $performance;
            $total_list['total'] += $total;

            // 单月，详细统计
            if ($is_single_month) {
                // 公司统计
                self::calculate($result, $corp_list, 'corp_id');
                // 部门统计
                self::calculate($result, $dep_list, 'user_wmain_department');
                // 项目统计
                self::calculate($result, $project_list, 'project');
            } else {
                // 公司统计
                self::calculateByMonth($result, $item['month'], $corp_list, $month_list, 'corp_id');
                // 部门统计
                self::calculateByMonth($result, $item['month'], $dep_list, $month_list, 'user_wmain_department');
                // 项目统计
                self::calculateByMonth($result, $item['month'], $project_list, $month_list, 'project');
            }
        }

        $total_list['user_count'] = count($user_list);
        $total_list['user_avg'] = $total_list['user_count'] ? round($total_list['total'] / $total_list['user_count'], 2) : 0;

        $total_list = array_map(function ($item) {
            return round($item, 2);
        }, $total_list);

        returnSuccess([
            'list' => $list,
            'total' => $total_list,
            'corp_list' => self::formatData($corp_list, $is_single_month, $corps),
            'dep_list' => self::formatData($dep_list, $is_single_month, $department),
            'project_list' => self::formatData($project_list, $is_single_month),
        ]);
    }

    // 计算
    private static function calculate($result, &$sum, $type)
    {
        if (!isset($result['user_info'][$type])) return;
        $key = $result['user_info'][$type];
        // 实发1
        $real_salary1 = $result['real_salary1'] ?? 0;
        // 实发2
        $real_salary2 = $result['real_salary2'] ?? 0;
        // 公司承担社保
        $insurance_total_corp = $result['insurance_fund_tax']['user_social_insurance']['total_corp'] ?? 0;
        // 公司承担公积金
        $housing_fund_total_corp = $result['insurance_fund_tax']['user_housing_fund']['total_corp'] ?? 0;
        // 个税
        $total_tax_refund = $result['insurance_fund_tax']['user_tax']['total_tax_refund'] ?? 0;
        // 个人承担社保
        $insurance_total_personal = $result['insurance_fund_tax']['user_social_insurance']['total_personal'] ?? 0;
        // 个人承担公积金
        $housing_fund_total_personal = $result['insurance_fund_tax']['user_housing_fund']['total_personal'] ?? 0;

        // 合计
        $total = $real_salary1 + $real_salary2 + $insurance_total_corp + $housing_fund_total_corp + $total_tax_refund + $insurance_total_personal + $housing_fund_total_personal;

        if (!isset($sum[$key])) {
            $sum[$key] = [
                'real_salary1' => 0,
                'real_salary2' => 0,
                'insurance_total_corp' => 0,
                'housing_fund_total_corp' => 0,
                'total_tax_refund' => 0,
                'insurance_total_personal' => 0,
                'housing_fund_total_personal' => 0,
                'total' => 0,
                'user_count' => 0
            ];
        }
        $sum[$key]['real_salary1'] += $real_salary1;
        $sum[$key]['real_salary2'] += $real_salary2;
        $sum[$key]['insurance_total_corp'] += $insurance_total_corp;
        $sum[$key]['housing_fund_total_corp'] += $housing_fund_total_corp;
        $sum[$key]['total_tax_refund'] += $total_tax_refund;
        $sum[$key]['insurance_total_personal'] += $insurance_total_personal;
        $sum[$key]['housing_fund_total_personal'] += $housing_fund_total_personal;
        $sum[$key]['total'] += $total;
        $sum[$key]['user_count'] += 1;
        return;
    }

    // 按月计算
    private static function calculateByMonth($result, $month, &$sum, $month_list, $type)
    {
        if (!isset($result['user_info'][$type])) return;
        $key = $result['user_info'][$type];
        // 实发1
        $real_salary1 = $result['real_salary1'] ?? 0;
        // 实发2
        $real_salary2 = $result['real_salary2'] ?? 0;
        // 公司承担社保
        $insurance_total_corp = $result['insurance_fund_tax']['user_social_insurance']['total_corp'] ?? 0;
        // 公司承担公积金
        $housing_fund_total_corp = $result['insurance_fund_tax']['user_housing_fund']['total_corp'] ?? 0;
        // 个税
        $total_tax_refund = $result['insurance_fund_tax']['user_tax']['total_tax_refund'] ?? 0;

        $total = $real_salary1 + $real_salary2 + $insurance_total_corp + $housing_fund_total_corp + $total_tax_refund;

        // 初始化月份
        if (!isset($sum[$key])) {
            $sum[$key] = $month_list;
        }
        $sum[$key][$month] += $total;
        return;
    }

    // 计算总计
    private static function formatData($sum, $is_single_month = true, $map = [])
    {
        $ret = [];
        $all = 0;
        foreach ($sum as $key => &$list) {
            if (!$is_single_month) {
                foreach ($list as $month => &$num) {
                    $num = round($num, 2);
                    $all += $num;
                }
                $list['total'] = round($all, 2);
            }
            $item = [
                'key_id' => $key,
                'key_name' => $map[$key] ?? $key,
            ];
            $item = array_merge($item, $list);
            $ret[] = $item;
        }
        return array_map(function ($item) {
            if (!is_numeric($item)) return $item;
            return round($item, 2);
        }, $ret);
    }


}