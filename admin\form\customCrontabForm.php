<?php
/**
 * @author: zhangguoming
 * @Time: 2025/4/10 9:07
 */

namespace admin\form;

//定时任务数据生成
use admin\models\customCrontabModel;
use core\lib\db\dbMysql;

class customCrontabForm
{
    /**
     * @param $link_id 数据id
     * @param $link_type 类型
     * @param $link_module 模块
     * @param $runtime 执行时间
     * @param $runtime 是否周期性任务 0否，1是
     * @return void 保存定时任务
     */
    public static function saveTask(int $link_id,int $link_type,int $link_module,int $runtime,int $is_crontab_task = 0) {
        $db = dbMysql::getInstance();
        $task = $db->table('custom_crontab')
            ->where('link_id = :link_id and link_type = :link_type and link_module = :link_module',[
                'link_id'=>$link_id,
                'link_type'=>$link_type,
                'link_module'=>$link_module,
            ])->one();
        if ($task) {
            $db->table('custom_crontab')
                ->where('id = :id',['id'=>$task['id']])
                ->update([
                    'is_crontab_task'=>$is_crontab_task,
                    'runtime'=>$runtime,
                    'status'=>-1,
                ]);
        } else {
            $db->table('custom_crontab')
                ->insert([
                    'is_crontab_task'=>$is_crontab_task,
                    'link_id'=>$link_id,
                    'link_type'=>$link_type,
                    'link_module'=>$link_module,
                    'runtime'=>$runtime,
                    'create_time'=>date('Y-m-d H:i:s')
                ]);
        }
    }
    //消费
    public static function consumeTask() {
        $db = dbMysql::getInstance();
        $time = time();
        $crontab_list = $db->table('custom_crontab')
            ->where('status = 0 and runtime <= :runtime',['runtime'=>$time])
            ->list(10);
        $next_runtime = $time;
        if (count($crontab_list)) {
            $db->table('custom_crontab')
                ->whereIn('id',array_column($crontab_list,'id'))
                ->update(['status'=>1]);
            foreach ($crontab_list as $v) {
                //res_type: 1已完成-不需再提醒，2循环任务已提醒-还需提醒
                if ($v['link_module'] == 1) {
                    //产品
                    if ($v['link_type'] == 1) {
                        //需求图制作超时提醒
                        list($res_type,$day) = customCrontabModel::goodsRequestTimeOut($v['link_id']);
                        $next_runtime = $time + ($day * 24*60*60);
                    } elseif ($v['link_type'] == 2) {
                        //图片测试结果
                        $res_type = customCrontabModel::goodsRequestTestRes($v['link_id']);
                    } else {
                        $res_type = 0;
                    }
                    if ($res_type) {
                        if ($res_type == 1) {
                            $db->table('custom_crontab')
                                ->where('id = :id',['id'=>$v['id']])
                                ->update([
                                    'status'=>2,
                                    'run_time'=>date('Y-m-y H:i:s'),
                                    'run_num'=>($v['run_num']+1)
                                ]);
                        } elseif ($res_type == 2) {
                            $db->table('custom_crontab')
                                ->where('id = :id',['id'=>$v['id']])
                                ->update([
                                    'status'=>0,
                                    'run_time'=>date('Y-m-y H:i:s'),
                                    'runtime'=>$next_runtime,
                                    'run_num'=>($v['run_num']+1)
                                ]);
                        }
                        continue;
                    }
                }
                //不存在的模块或者模块类型
                $db->table('custom_crontab')
                    ->where('id = :id',['id'=>$v['id']])
                    ->update([
                        'status'=>3,
                        'run_time'=>date('Y-m-y H:i:s'),
                    ]);
            }
            SetReturn(2,'执行'.count($crontab_list).'条');
        } else {
            SetReturn(2,'没有可执行得定时任务');
        }
    }
}