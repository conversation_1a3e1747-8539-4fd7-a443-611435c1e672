<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/22 15:07
 */

namespace  plugins\goods\controller;

use plugins\goods\models\userModel;
use core\lib\db\dbMysql;

class messageController
{
    public function getList()
    {
        $paras_list = array('is_read','page','page_size','type');
        $param = arrangeParam($_GET, $paras_list);
        $is_read = (int)$param['is_read'];
        $type = (int)$param['type'];
        $db = dbMysql::getInstance();
        $db->table('messages')
            ->where('where qw_userid=:qw_userid',['qw_userid'=>userModel::$wid]);
        if ($is_read > -1) {
            $db->andWhere('and is_read=:is_read',['is_read'=>$is_read]);
        }
        if ($type == 2) {
            $db->andWhere('and type=:type',['type'=>$type]);
        }
        $db->field('id,title,qw_userid,text,type,is_read,created_at,remarks');
        $db->order('is_read asc,id desc');
        $list = $db->pages($param['page'],$param['page_size']);
        returnSuccess($list);
    }
    public function getMessageCount() {
        //计数
        $db = dbMysql::getInstance();
        $project_msg_count = $db->table('messages')
            ->where('where qw_userid=:qw_userid and is_read=0',['qw_userid'=>userModel::$wid])
            ->count();
        $project_copy_msg_count = $db->table('messages')
            ->where('where qw_userid=:qw_userid and is_read=0',['qw_userid'=>userModel::$wid])
            ->andWhere('and type=2')
            ->count();
        $list = [
            'project_msg_count'=>$project_msg_count,
            'project_copy_msg_count'=>$project_copy_msg_count,
        ];
        returnSuccess($list);
    }
    public function getDetail()
    {
        $id = (int)$_GET['id'];
        if (!$id) {
            SetReturn(-1, '参数有误');
        }
        $db = dbMysql::getInstance();
        $db->table('messages');
        $db->where('where id = '.$id);
        $db->field('id,title,qw_userid,type,text,model_id,created_at,remarks');
        $data = $db->one();
        $db->update(['is_read'=>1]);
        returnSuccess(['data'=>$data]);
    }

    //全部标记已读
    public function setAllRead()
    {
        $type = (int)$_GET['type'];
        $db = dbMysql::getInstance();
        $db->table('messages');
        $db->where('where qw_userid = :qw_userid and is_read = 0',['qw_userid'=>userModel::$wid]);
        if ($type == 2) {
            $db->andWhere('and type=2');
        }
        $db->update(['is_read'=>1]);
        SetReturn(0, '全部已读');
    }

    //获取企微通知的消息
    public function getMsgDetail() {
        $secret_code = $_POST['data']??'';
        if (empty($secret_code)) {
            SetReturn(-1,'参数有误');
        }
        $data = qwMsgDecryption($secret_code);
        $data  = json_decode($data,true);
        $id = $data['id']??0;
        if (!$id) {
            SetReturn(-1,'参数有误');
        }
        $db = dbMysql::getInstance();
        $db->table('messages');
        $db->where('where id = '.$id);
        $db->field('id,title,qw_userid,type,text,model_id,created_at,remarks');
        $data = $db->one();
        $db->update(['is_read'=>1]);
        returnSuccess(['data'=>$data]);
    }
}