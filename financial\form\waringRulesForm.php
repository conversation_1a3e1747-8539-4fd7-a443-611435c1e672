<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/19 11:23
 */

namespace financial\form;

use core\lib\config;
use core\lib\db\dbFMysql;
use financial\models\userModel;
use Rap2hpoutre\FastExcel\FastExcel;

class waringRulesForm
{
    public static array $column = [
        'waring_name'=>'预警名称',
        'status'=>'状态',
        'level'=>'等级',
        'dimension_name'=>'维度',
        'begin_time'=>'规则月份',
        'receive_name'=>'预警接收人',
        'monitoring_column'=>'监控指标',
        'description'=>'描述',
        'rules'=>'规则',
        'updated_time'=>'修改时间',
        'update_wname'=>'最新修改人',
    ];
    public static array $market_list = [];
    public static array $category_list = [];
    public static array $countryCode;//国家列表
    public static array $ColumnList;//规则列表
    public static array $waring_rules_month;//预警月份
    public static array $waring_rules_symbol;//预警计算符号
    public static array $waring_rules_reference;//预警规则-监控规则对比对象
    public static array $goods;//产品分类
    //整理预警列表数据
    public static function getWaringList($list) {
        foreach ($list as &$v) {
            $v['dimension_name'] = config::getDataName1('data_financial','waring_rules_dimension',$v['dimension']);
            $v['begin_time'] = date('Y-m-d',$v['begin_time']);
            $receive_type = json_decode($v['receive_type']);
            $v['receive_name'] = '';
            if (count($receive_type)) {
                $receive_name = [];
                foreach ($receive_type as $v1) {
                    if ($v1 == 1) {
                        $receive_name[] = '产品运营';
                    }
                    if ($v1 == 2) {
                        $receive_name[] = '运营主管';
                    }
                }
                $v['receive_name'] = implode(',',$receive_name);
            }
        }
        return $list;
    }
    //修改记录
    public static function setUpdateLog($waring_id,$data,$type,$old_data=[]) {
        $db = dbFMysql::getInstance();
        if ($type == 3 || $type == 1) {
            $db->table('waring_rules_log')
                ->insert([
                    'waring_id'=>$waring_id,
                    'user_id'=>userModel::$qwuser_id,
                    'created_time'=>date('Y-m-d H:i:s'),
                    'type'=>$type,
                ]);
        } else {
            $uodate_log = null;
            $keys = ['waring_name','status','dimension','begin_time','receive_type','rules','column_id'];
            foreach ($data as $k=>$v) {
                if (in_array($k,$keys)) {
                    foreach ($old_data as $k2=>$v2) {
                        if ($k == $k2 && $v != $v2) {
                            switch ($k) {
                                case "waring_name":
                                    $uodate_log['预警名称'] = $v;
                                    break;
                                case "status":
                                    $uodate_log['状态'] = $v==1?'启用':'禁用';
                                    break;
                                case "dimension":
                                    $uodate_log['维度'] = config::getDataName1('data_financial','waring_rules_dimension',$v);
                                    break;
                                case "begin_time":
                                    $uodate_log['生效时间'] = date('Y-m',$v);
                                    break;
                                case "receive_type":
                                    $receive_name = [];
                                    $receive_type = json_decode($v);
                                    foreach ($receive_type as $item) {
                                        if ($item == 1) {
                                            $receive_name[] = '产品运营';
                                        }
                                        if ($item == 2) {
                                            $receive_name[] = '运营主管';
                                        }
                                    }
                                    $uodate_log['接收人'] = implode(',',$receive_name);
                                    break;
                                case "column_id":
                                    $column = $db->table('column')
                                        ->where('where id=:id',['id'=>$v])->one();
                                    $uodate_log['监控指标'] = $column['column_name'];
                                case "rules":

                                    $update_rules = [];
//                                    dd($v);
                                    $new_rules = json_decode($v,true);
                                    if (count(self::$category_list) == 0) {
                                        self::$category_list = $db->table('goods_category')
                                            ->field('id,title')
                                            ->list();
                                    }
                                    if (count(self::$market_list) == 0) {
                                        self::$market_list = $db->table('market')
                                            ->field('id,country')
                                            ->list();
                                    }

                                    foreach ($new_rules as $v) {

                                        $item_ = [];
//                                        dd($v['market_id']);

                                        $item_['国家'] = [];  // 初始化为一个数组以存储多个国家

                                        foreach ($v['market_id'] as $market_id) {
                                            foreach (self::$market_list as $m) {
                                                if ($m['id'] == $market_id) {
                                                    $item_['国家'][] = $m['country'];  // 将匹配的国家名称添加到数组中
                                                    break;  // 找到匹配后跳出内层循环，继续查找下一个 market_id
                                                }
                                            }
                                        }

                                        foreach (self::$category_list as $c) {
                                            if (in_array($c['id'],$v['category_id'])) {
                                                $item_['产品分类'][] = $c['title'];
                                            }
                                        }
                                        $condition_array = [];
                                        foreach ($v['condition'] as $k=>$condition) {
//                                            dd($condition);
                                            $condition_string = '';
                                            if ($k > 0) {
                                                $condition_string .= $condition['type']==1?'且':'或';
                                            }
                                            $condition_string .= config::getDataName1('data_financial','waring_rules_month',$condition['month']).$condition['is_absolute_value']==0?'值':'绝对值'.config::getDataName1('data_financial','waring_rules_reference',$condition['reference']) .$condition['Interval_value'].config::getDataName1('data_financial','waring_rules_symbol',$condition['symbol']).$condition['value1'];
                                            if ($condition['symbol'] == 6) {
                                                $condition_string .= '~'.$condition['value1'];
                                            }
                                            $condition_array[] = $condition_string;
                                        }
                                        $item_['规则'] = $condition_array;
                                        $update_rules[] = $item_;
                                    }

                                    $uodate_log['规则'] = $update_rules;
                                default:
                                    break;
                            }

                        }

                    }

                }

            }

            $db->table('waring_rules_log')
                ->insert([
                    'waring_id'=>$waring_id,
                    'updated_data'=>json_encode($uodate_log,JSON_UNESCAPED_UNICODE),
                    'user_id'=>userModel::$qwuser_id,
                    'created_time'=>date('Y-m-d H:i:s'),
                    'type'=>2,
                ]);
        }

    }
    //导出预警列表
    public static function exportWaringList($list) {
        //表头
        $new_data = [];
        //数据
        $xuhao = 1;
        foreach ($list as $v) {
            $item['序号'] = $xuhao;
            foreach ($v as $k=>$v1) {
                if (!isset(self::$column[$k])) {
                    continue;
                }
                $value = $v1;
                //时间转化
                if ($k == 'status') {
                    if ($v1 == 0) {
                        $value = '禁用';
                    } else {
                        $value = '启用';
                    }
                }
                //监控指标
                if ($k == 'monitoring_column') {
                    $db = dbFMysql::getInstance();
                    $column =$db->table('column')
                        ->where('where key_name=:key_name',['key_name'=>$v1])
                        ->field('column_name')
                        ->one();
                    $value  = $column['column_name'];
                }
                //等级
                if ($k == 'level') {
                    if ($v1 == 1) {
                        $value = '一般';
                    } elseif ($v1 == 2) {
                        $value = '中等';
                    } else {
                        $value = '严重';
                    }
                }
                if ($k == 'begin_time') {
                    $value = date('Y-m',$v1);
                }
                //规则
                if ($k == 'rules') {
                    $value = self::translateRules($v1);;
                }
                $item[self::$column[$k]] = $value;
            }
            $new_data[] = $item;
            $xuhao++;
        }

        //保存
        $save_path = "/public_financial/temp/yujing";
        $url = SELF_FK.$save_path;
        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path."/".date('YmdHis').uniqid().'.xlsx';
        $url = SELF_FK.$path;
        if (!file_exists($url)) {
            touch($url);
        }
        (new FastExcel($new_data))->export($url);
        return $path;
    }
    //规则验证
    public static function verifyRules(string $rules,int $show_type) {
        //验证监控指标
        if (empty($rules) || $rules=='[]') {
            returnError('规则不能为空');
        }
        $rules = json_decode($rules,true);
        $market_ids = [];
        foreach ($rules as $v) {
            if (count($v['market_id']) == 0) {
                //全部国家时，不能有多个监控规则集合
                if (count($rules) > 1) {
                    returnError('已选择全部国家，不可设置多个监控规则集合');
                }
            }
            if (empty($v['market_id']) && $v['market_id'] !== []) {
                returnError('请选择监控国家');
            }
            if (empty($v['category_id']) || $v['category_id']=='[]') {
                returnError('请选择产品分类');
            }
            if (empty($v['condition']) || $v['condition']=='[]') {
                returnError('请设置监控条件');
            }
            $condition = $v['condition'];
            $market_ids = array_merge($market_ids,$v['market_id']);
            foreach ($condition as $c) {
                if (empty($c['month'])) {
                    returnError('请设置监控条件中的月份');
                }
                if (!isset($c['is_absolute_value'])) {
                    returnError('请设置监控条件的值');
                }
                if (empty($c['symbol'])) {
                    returnError('请设置监控条件的计算符号');
                }
                if($c['symbol'] == 2){
                    if (empty($c['Interval_value'])) {
                        returnError('请设置区间对比的值');
                    }
                }else {
                    $c['Interval_value'] = '';
                }
                if (empty($c['reference'])) {
                    returnError('请设置监控条件的对比对象');
                }
                //比例字段不可设置环比或同比
                if ($show_type == 2 && ($c['reference'] == 3 || $c['reference'] == 4)) {
                    returnError('监控字段为比例字段时，不可设置环比或同比');
                }
                if (!isset($c['value1'])) {
                    returnError('请设置监控条件对比的值');
                }
                if (!isset($c['value2'])) {
                    returnError('请设置监控条件对比的区间值');
                }
            }
        }
        if (count($market_ids)) {
            $market_ids = array_unique($market_ids);
            $db = dbFMysql::getInstance();
            $country_list = $db->table('market')
                ->whereIn('id',$market_ids)
                ->field('id')->list();
            if (count($country_list) != count($market_ids)) {
                returnError('国家数据存在问题');
            }
        }

        return $rules;
    }
    //翻译规则
    public static function translateRules($rules) {
        self::countryCode();
        self::ColumnList();
        self::goods();

        // 将返回的数组赋值给 $config
        $config = config::all('data_financial');
        // 访问配置数组
        self::$waring_rules_month = $config['waring_rules_month'];
        self::$waring_rules_symbol = $config['waring_rules_symbol'];
        self::$waring_rules_reference = $config['waring_rules_reference'];

        // 创建映射数组，以便快速查找
        $monthMap = array_column(self::$waring_rules_month, 'name', 'id');
        $symbolMap = array_column(self::$waring_rules_symbol, 'name', 'id');
        $referenceMap = array_column(self::$waring_rules_reference, 'name', 'id');

        // 解码规则 JSON
        $rules = json_decode($rules, true);
        foreach ($rules as $k => &$v) {
            // 处理 market_id
            if (isset($v['market_id'])) {
                $market_id = $v['market_id'];

                // 如果 market_id 是空数组或等于-1，则设置为 "全部国家"
                if (empty($market_id) || $market_id == -1) {
                    $v['market_id'] = '全部国家';
                } else {
                    // 如果 market_id 不是数组，转为数组
                    if (!is_array($market_id)) {
                        $market_id = [$market_id];
                    }

                    $country_name = [];
                    foreach ($market_id as $marketId) {
                        foreach (self::$countryCode as $country) {
                            if ($marketId == $country['id']) {
                                $country_name[] = $country['country'];
                            }
                        }
                    }
                    $v['market_id'] = implode(',', $country_name);
                }
            }

            // 处理 category_id (产品分类)
            if (isset($v['category_id'])) {
                $category_name = [];
                foreach ($v['category_id'] as $categoryId) {
                    foreach (self::$goods as $good) {
                        if ($categoryId == $good['id']) {
                            $category_name[] = $good['title'];
                        }
                    }
                }
                $v['category_id'] = implode(',', $category_name);
            }

            // 处理 condition (条件)
            if (isset($v['condition'])) {
                foreach ($v['condition'] as &$condition) {
                    // 遍历每个条件数组的字段
                    foreach ($condition as $conditionKey => &$conditionValue) {
                        // 处理类型字段
                        if ($conditionKey == 'type') {
                            $conditionValue = $conditionValue == 1 ? '且' : '或';
                        }
                        // 处理月份字段
                        elseif ($conditionKey == 'month') {
                            $conditionValue = $monthMap[$conditionValue] ?? '未知月份';
                        }
                        // 处理符号字段
                        elseif ($conditionKey == 'symbol') {
                            $conditionValue = $symbolMap[$conditionValue] ?? '未知符号';
                        }
                        // 处理参考值字段
                        elseif ($conditionKey == 'reference') {
                            $conditionValue = $referenceMap[$conditionValue] ?? '未知参考值';
                        }
                        // 处理绝对值字段
                        elseif ($conditionKey == 'is_absolute_value') {
                            $conditionValue = $conditionValue == 1 ? '绝对值' : '值';
                        }
                        // 处理其他值字段（value1和value2）
                        elseif ($conditionKey == 'value1' || $conditionKey == 'value2') {
                            $conditionValue = $conditionValue;
                        }
                    }
                }
            }
        }
        $rules = self::formatRules($rules);
//        dd($rules);
        return $rules;  // 返回翻译后的规则
    }
    public static function formatRules($rules)
    {
        $formattedRules = [];

        foreach ($rules as $rule) {
            $parts = [];

            // 拼接 market_id
            if (isset($rule['market_id'])) {
                $parts[] = '国家：' . $rule['market_id'];
            }

            // 拼接 category_id
            if (isset($rule['category_id'])) {
                $parts[] = '产品分类：' . $rule['category_id'];
            }

            // 拼接 condition
            if (isset($rule['condition']) && is_array($rule['condition'])) {
                foreach ($rule['condition'] as $condition) {
                    $conditionParts = [];

                    // 拼接月份
                    if (isset($condition['month'])) {
                        $conditionParts[] = '月份：' . $condition['month'];
                    }

                    // 拼接是否是绝对值
                    if (isset($condition['is_absolute_value'])) {
                        $conditionParts[] = '比较值：' . $condition['is_absolute_value'];
                    }

                    // 拼接运算符号
                    if (isset($condition['symbol'])) {
                        $conditionParts[] = '运算符号：' . $condition['symbol'];
                    }

                    // 拼接 value1
                    if (isset($condition['value1'])) {
                        $conditionParts[] = '比较值：' . $condition['value1'];
                    }

                    // 拼接 value2
                    if (isset($condition['value2'])) {
                        $conditionParts[] = '比较值2：' . $condition['value2'];
                    }

                    // 拼接 type
                    if (isset($condition['type'])) {
                        $conditionParts[] = '条件类型：' . $condition['type'];
                    }

                    // 拼接 reference
                    if (isset($condition['reference'])) {
                        $conditionParts[] = '对比项：' . $condition['reference'];
                    }

                    // 拼接 Interval_value
                    if (isset($condition['Interval_value'])) {
                        $conditionParts[] = '区间值：' . $condition['Interval_value'];
                    }

                    // 将所有条件部分拼接起来
                    $parts[] = implode('，', $conditionParts);
                }
            }

            // 将所有部分拼接成一个字符串
            $formattedRules[] = implode('；', $parts);
        }

        return implode("\n", $formattedRules);
    }



    //获取规则列表
    public static function ColumnList()
    {
        // 获取数据库实例
        $db = dbFMysql::getInstance();
        // 执行查询
        self::$ColumnList = $db->table('column')
            ->field('id, column_name')
            ->list();
    }
    //获取国家列表
    public static function countryCode()
    {
        // 获取数据库实例
        $db = dbFMysql::getInstance();
        // 执行查询
        self::$countryCode = $db->table('market')
            ->field('id, country')
            ->list();
    }
    //产品分类
    public static function goods()
    {
        // 获取数据库实例
        $db = dbFMysql::getInstance();
        // 执行查询
        self::$goods = $db->table('goods_category')
            ->field('id, title')
            ->list();
    }

}