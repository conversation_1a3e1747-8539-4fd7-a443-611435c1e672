<?php

namespace plugins\logistics\models;

use core\lib\db\dbAfMysql;
use core\lib\db\dbFMysql;
use core\lib\db\dbLMysql;
use Exception;

class specialPurchaseProductModel
{
    public static $paras_list = [
        'sku' => '商品sku',
        'special_days' => '特殊天数',
    ];

    public static $json_key = [
        'special_days',
    ];

    // 处理json字段
    public function handleJsonFields($param, $encode = true)
    {
        foreach (self::$json_key as $key) {
            if (isset($param[$key])) {
                if ($encode) {
                    $param[$key] = json_encode($param[$key], JSON_UNESCAPED_UNICODE);
                } else {
                    $param[$key] = json_decode($param[$key], true);
                }
            }
        }
        return $param;
    }

    /**
     * 获取特殊产品列表
     */
    public function getList($param)
    {
        $country_code = $param['country_code'];
        $listing_stage = $param['listing_stage'] ?? '';
        $page = intval($param['page'] ?? 1);
        $page_size = intval($param['page_size'] ?? 10);

        // 1. 从listing表中获取符合条件的asin
        $afdb = dbAfMysql::getInstance();
        $listing = $afdb->table('listing_data')
            // ->where('country = :country and stage = :stage', [
            ->where('country_code = :country ', [
                'country' => $country_code,
//                'stage' => $listing_stage
            ])
            ->field('asin, country')
            ->list();

        if (empty($listing)) {
            return [
                'list' => [],
                'count' => 0,
                'page' => $page,
                'page_size' => $page_size
            ];
        }

        //国家数据
        $fdb = dbFMysql::getInstance();
        $country_list = $fdb->table('market')
            ->where('where is_delete = 0')
            ->field('country,code,ct_id')
            ->list();
        $country_ = array_column($country_list,'country','code');

        // 2. 从lingxing_listing中获取对应的local_sku
        $asin_list = array_column($listing, 'asin');
        $lingxing_listing = $afdb->table('lingxing_listing')
            ->whereIn('asin', $asin_list)
            ->where('marketplace = :marketplace and status = 1', ['marketplace' => $country_[$country_code]])
            ->field('local_sku')
            ->list();

        if (empty($lingxing_listing)) {
            return [
                'list' => [],
                'count' => 0,
                'page' => $page,
                'page_size' => $page_size
            ];
        }

        $sku_list = array_column($lingxing_listing, 'local_sku');
        $sku_list = array_values(array_filter(array_unique($sku_list)));
        // 排序
        $db = dbLMysql::getInstance();
        $db->table('special_product', 'a')
            ->field('a.*, g.product_name, g.category_name, g.category_name_all, g.pic_url')
            ->leftjoinOut('financial', 'goods',  'g', 'a.sku = g.sku')
            ->whereIn('a.sku', $sku_list);
        $db->order('a.id desc');

        // 分页
        $page = intval($param['page']);
        $page_size = intval($param['page_size']);
        return $db->pages($page, $page_size);
    }

    /**
     * 根据ID获取特殊产品
     */
    public function getById($id)
    {
        $db = dbLMysql::getInstance();
        return $db->table('special_product')->where('id = :id', ['id' => $id])->one();
    }

    /**
     * 根据SKU获取特殊产品
     */
    public function getBySku($sku)
    {
        $db = dbLMysql::getInstance();
        return $db->table('special_product')->where('sku = :sku', ['sku' => $sku])->one();
    }

    /**
     * 添加特殊产品
     */
    public function add($param)
    {
        $db = dbLMysql::getInstance();
        $param = $this->handleJsonFields($param);
        return $db->table('special_product')->insert($param);
    }

    /**
     * 编辑特殊产品
     */
    public function edit($param, $id, $detail)
    {
        $db = dbLMysql::getInstance();
        $param = $this->handleJsonFields($param);
        $db->table('special_product')->where('id = :id', ['id' => $id])->update($param);
        
        return true;
    }

    /**
     * 删除特殊产品
     */
    public function delete($id)
    {
        $db = dbLMysql::getInstance();
        $detail = $this->getById($id);
        
        if (!$detail) {
            throw new Exception('数据不存在');
        }
        
        $db->table('special_product')->where('id = :id', ['id' => $id])->delete();
        
        return true;
    }

    /**
     * 获取可选商品列表（根据站点和listing阶段筛选）
     */
    public function getAvailableProducts($param)
    {
        $country_code = $param['country_code'];
        $listing_stage = $param['listing_stage'];
        $product_name = $param['product_name'] ?? '';
        $sku = $param['sku'] ?? '';
        $page = intval($param['page']);
        $page_size = intval($param['page_size']);

        //国家数据
        $fdb = dbFMysql::getInstance();
        $country_list = $fdb->table('market')
            ->where('where is_delete = 0')
            ->field('country,code,ct_id')
            ->list();
        $country_ = array_column($country_list,'country','code');
        
        // 1. 从listing表中获取符合条件的asin
        $afdb = dbAfMysql::getInstance();
        $listing = $afdb->table('listing_data')
            // ->where('country = :country and stage = :stage', [
            ->where('country_code = :country ', [
                'country' => $country_code,
//                'stage' => $listing_stage
            ])
            ->field('asin, country')
            ->list();

        if (empty($listing)) {
            return [
                'list' => [],
                'count' => 0,
                'page' => $page,
                'page_size' => $page_size
            ];
        }
        
        // 2. 从lingxing_listing中获取对应的local_sku
        $asin_list = array_column($listing, 'asin');
        $lingxing_listing = $afdb->table('lingxing_listing')
            ->whereIn('asin', $asin_list)
            ->where('marketplace = :marketplace and status = 1', ['marketplace' => $country_[$country_code]])
            ->field('local_sku')
            ->list();

        if (empty($lingxing_listing)) {
            return [
                'list' => [],
                'count' => 0,
                'page' => $page,
                'page_size' => $page_size
            ];
        }
        
        $sku_list = array_column($lingxing_listing, 'local_sku');
        $sku_list = array_values(array_filter(array_unique($sku_list)));

        // 查询已经被添加过的产品
        $db = dbLMysql::getInstance();
        $exist_list = $db->table('special_product', 'a')
            ->field('a.sku')
            ->list();
        $exist_sku_list = array_column($exist_list, 'sku');

        // 3. 在财务goods表中查询sku对应的商品
        $fdb = dbFMysql::getInstance();
        $fdb->table('goods')
            ->where('status = 1')
            ->whereIn('sku', $sku_list)
            ->andWhere('sku not in (:exist_sku)', ['exist_sku' => implode("','", $exist_sku_list)]);

        if (!empty($product_name)) {
            $fdb->andWhere('and product_name like :product_name', ['product_name' => '%' . $product_name . '%']);
        }
        
        // 排序
        $fdb->order('id desc');

        // 分页
        $list = $fdb->pages($page, $page_size);
        
       return $list;
    }
}