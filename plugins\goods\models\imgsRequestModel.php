<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/24 9:07
 */

namespace  plugins\goods\models;

use admin\form\qwChatForm;
use core\lib\db\dbMysql;
use plugins\goods\form\messagesFrom;

class imgsRequestModel
{
    //获取需求数据
    public static function getRequest($db,$id) {
        $imgs_request = $db->table('imgs_request')
            ->where('id = :id',['id'=>$id])
            ->one();
        if (!$imgs_request) {
            returnError('此需求不存在');
        }
        if ($imgs_request['status'] == 8) {
            returnError('此需求已暂停，不可操做');
        }
        return $imgs_request;
    }
    //需求群生成
    public static function createdChat($db,array $imgs_reques) {
        $user_list = $db->table('imgs_request_participant','a')
            ->leftJoin('qwuser','b','b.id = a.user_id')
            ->where('a.request_id = :request_id',['request_id'=>$imgs_reques['id']])
            ->field('b.wid')
            ->list();
        $wids_test = ["ZhangGuoMing","LiaoLiangJing","SongXiWen",'WangMingShou',"ZhangYunYing","MaoYuHang","ChenSuZhen","WeiXueYun","ZhouMingJie","HeSongLin","YangChenLu","LinYuYang"];
        //测试建群只允许建测试人
        $wids = array_column($user_list,'wid');
        $wids = array_unique(array_intersect($wids,$wids_test));
        $goods_new = $db->table('goods_new')
            ->where('id = :id',['id'=>$imgs_reques['goods_id']])
            ->field('goods_name,e_name')
            ->one();
        $chat_name = !empty($goods_new['goods_name']) ? $goods_new['goods_name']:$goods_new['e_name'];
        //组别-部门
        $partment = $db->table('qwdepartment')
            ->where('wp_id = :wp_id',['wp_id'=>$imgs_reques['wp_id']])
            ->one();
        if ($partment) {
            $chat_name .=  ('-'.$partment['name']);
        }
        $chat_name .= '-作图交流群';
        if (count($wids) > 2) {
            qwChatForm::saveChat($db,1,$imgs_reques['id'],$wids[0],$chat_name,$wids);
        }
        return [$chat_name];
    }
    //日志记录
    public static function setLog($db,$request_id,$type,$remarks,$content='') {
//        dd(substr($remarks,0,500));
        $db->table('imgs_request_log')
            ->insert([
                'request_id'=>$request_id,
                'user_id'=>userModel::$qwuser_id,
                'created_time'=>date('Y-m-d H:i:s'),
                'content'=>$content,
                'type'=>$type,
                'remarks'=>mb_substr($remarks,0,500)
            ]);
    }
    //任务完成消息发送
    public static function sendMsgToParticipant($db,$request_id,$msg_text){
        $participants = $db->table('imgs_request_participant')
            ->where('request_id = :request_id',['request_id'=>$request_id])
            ->field('user_id')
            ->list();
        if (count($participants)) {
            $user_ids = array_column($participants,'user_id');
            $user_list = $db->table('qwuser')
                ->whereIn('id',$user_ids)
                ->field('wid')
                ->list();
            //通知
            $wids = array_column($user_list,'wid');
            messagesFrom::senMessage($wids,$msg_text,userModel::$qwuser_id,$request_id,16);
        }
    }
    //通知信息获取
    public static function getMessage($status,$request_name,$goods_name) {
        $msg = '';
        switch ($status) {
            case 4://sop审核
                $user_name = userModel::$wname;
                if ($goods_name) {
                    $msg = "用户【{$user_name}】提交了【{$goods_name}】【{$request_name}】的图片需求。请您及时审核SOP";
                } else {
                    $msg = "用户【{$user_name}】提交了【{$request_name}】的图片需求。请您及时审核SOP";
                }
                break;
            case 0://待配置美工
                if ($goods_name) {
                    $msg = "请您及时为【{$goods_name}】的图片需求【{$request_name}】需求配置作图美工";
                } else {
                    $msg = "请您及时为图片需求【{$request_name}】需求配置作图美工";
                }
                break;
            case 5:
                $msg = "您有新的作图需求，请您及时接收，开启任务。";
                break;
            case 1:
                $msg = "用户【".userModel::$wname."】提醒您，请及时上传【{$request_name}】";
                break;
            case 2:
                if ($goods_name) {
                    $msg = "您提交的【{$goods_name}】【{$request_name}】的SOP审核不通过，请重新修改后提交";
                } else {
                    $msg = "您提交的【{$request_name}】的SOP审核不通过，请重新修改后提交";
                }
                break;
        }
        return $msg;

    }

}