<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/13 13:59
 */
namespace  plugins\goods\controller;

use core\lib\app_auth\plugins\authGoods;
use core\lib\db\dbMysql;


class roleController
{
    public function getList(){
        $paras_list = array('role_name', 'page', 'page_size');
        $param = arrangeParam($_GET, $paras_list);

        $db = dbMysql::getInstance();
        $db->table('role');
        $where_str = 'where 1=1';
        $where_data = [];
        if (!empty($param['role_name'])) {
            $where_str .= ' and role_name like :role_name';
            $where_data['role_name'] = '%'.$param['role_name'].'%';
        }
        $db->where($where_str, $where_data);
        $db->field('id,role_name,created_at');
        $db->order('id desc');
        $db->pages($param['page'], 100);
        $list = $db->list();
        $data = [
            'list'=>$list
        ];
        returnSuccess($data);
    }
    public function editRole(){
        $paras_list = array('id', 'role_name', 'auth');
        $param = arrangeParam($_POST, $paras_list);
        if (empty($param['role_name'])) {
            SetReturn(-1,'角色名称必填');
        }
        $db = dbMysql::getInstance();
        $db->table('role');
        $role_data = [
            'role_name'=>$param['role_name'],
            'auth'=>$param['auth']
        ];
        if (!$param['id']) {
            //新增
            $role_data['created_at'] = date('Y-m-d H:i:s');
            if($db->insert($role_data)) {
                SetReturn(0,'保存成功');
            } else {
                SetReturn(-1,'保存失败');
            }
        } else {
            $role_data['updated_at'] = date('Y-m-d H:i:s');
            $db->where(' where id = :id', ['id'=>$param['id']]);
            if($db->update($role_data)) {
                SetReturn(0,'保存成功');
            } else {
                SetReturn(-1,'保存失败');
            }
        }

    }
    public function getDetail(){
        $paras_list = array('id');
        $param = arrangeParam($_GET, $paras_list);
        $id = (int)$param['id'];
        if (!$id) {
            SetReturn(-1,'缺少必传参数');
        }
        $db = dbMysql::getInstance();
        $db->table('role');
        $role = $db->query('select * from oa_role where id = '.$id);
        returnSuccess(['data'=>$role]);
    }

    public function getAuthList(){
        returnSuccess(authGoods::$auth_list);
    }
}