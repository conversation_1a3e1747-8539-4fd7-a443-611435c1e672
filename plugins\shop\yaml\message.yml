openapi: 3.0.0
info:
  title: 消息管理API
  version: 1.0.0
  description: 提供消息管理的相关接口
paths:
  /shop/message/getList:
    get:
      tags:
        - Message
      summary: 获取消息列表
      description: 根据条件筛选获取消息列表
      parameters:
        - name: is_read
          in: query
          description: 是否已读(-1-全部, 0-未读, 1-已读)
          required: false
          schema:
            type: integer
            default: -1
        - name: type
          in: query
          description: 消息类型(2-抄送消息)
          required: false
          schema:
            type: integer
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/Message'
                      total:
                        type: integer
                        description: 总记录数
                      page:
                        type: integer
                        description: 当前页码
                      page_size:
                        type: integer
                        description: 每页条数

  /shop/message/getMessageCount:
    get:
      tags:
        - Message
      summary: 获取消息数量
      description: 获取不同类型的消息数量统计
      responses:
        '200':
          description: 成功返回消息数量
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      project_msg_count:
                        type: integer
                        description: 项目消息数量
                      project_copy_msg_count:
                        type: integer
                        description: 项目抄送消息数量

  /shop/message/getDetail:
    get:
      tags:
        - Message
      summary: 获取消息详情
      description: 根据ID获取消息详细信息
      parameters:
        - name: id
          in: query
          description: 消息ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回详情数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    $ref: '#/components/schemas/MessageDetail'

  /shop/message/setAllRead:
    get:
      tags:
        - Message
      summary: 全部标记已读
      description: 将指定类型的所有消息标记为已读
      parameters:
        - name: type
          in: query
          description: 消息类型(2-抄送消息)
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: 标记成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 标记成功

  /shop/message/getMsgDetail:
    post:
      tags:
        - Message
      summary: 获取企微通知的消息详情
      description: 解密并获取企微通知的消息详情
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - data
              properties:
                data:
                  type: string
                  description: 加密的消息数据
      responses:
        '200':
          description: 成功返回详情数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    $ref: '#/components/schemas/Message'

components:
  schemas:
    Message:
      type: object
      properties:
        id:
          type: integer
          description: ID
        title:
          type: string
          description: 标题
        text:
          type: string
          description: 消息内容
        type:
          type: integer
          description: 消息类型
        is_read:
          type: integer
          description: 是否已读
        created_at:
          type: string
          description: 创建时间
          format: date-time
        remarks:
          type: string
          description: 备注

    MessageDetail:
      type: object
      properties:
        id:
          type: integer
          description: ID
        title:
          type: string
          description: 标题
        text:
          type: string
          description: 消息内容
        type:
          type: integer
          description: 消息类型
        model_id:
          type: integer
          description: 模型ID
        created_at:
          type: string
          description: 创建时间
          format: date-time
        remarks:
          type: string
          description: 备注
