<?php
/**
 * 节日活动模型
 * @author: AI Assistant
 * @Time: 2025/5/29
 */

namespace plugins\logistics\models;

use core\lib\db\dbLMysql;

class festivalActivitiesModel
{
    private $db;
    private $table = 'festival_activity';

    public function __construct()
    {
        $this->db = dbLMysql::getInstance();
    }

    /**
     * 获取节日活动列表
     * @param array $params 查询参数
     * @return array
     */
    public function getList($params = [])
    {
        // 使用链式调用构建查询
        $this->db->table($this->table)->where('is_delete = 0');

        // 按节日类型筛选
        if (!empty($params['festival_type'])) {
            $this->db->andWhere('festival_type = :festival_type', ['festival_type' => $params['festival_type']]);
        }

        // 按状态筛选
        if (isset($params['status']) && $params['status'] !== '') {
            $this->db->andWhere('status = :status', ['status' => $params['status']]);
        }

        // 按日期范围筛选
        if (!empty($params['start_date'])) {
            $this->db->andWhere('start_date >= :start_date', ['start_date' => $params['start_date']]);
        }
        if (!empty($params['end_date'])) {
            $this->db->andWhere('end_date <= :end_date', ['end_date' => $params['end_date']]);
        }

        // 按名称搜索
        if (!empty($params['name'])) {
            $this->db->andWhere('name LIKE :name', ['name' => '%' . $params['name'] . '%']);
        }

        // 分页参数
        $page = isset($params['page']) ? (int)$params['page'] : 1;
        $page_size = isset($params['lpage_sizeimit']) ? (int)$params['page_size'] : 20;

        // 排序
        $orderBy = 'created_at DESC';
        if (!empty($params['order_by'])) {
            $orderBy = $params['order_by'];
        }
        $this->db->order($orderBy);

        return $this->db->pages($page, $page_size);
    }

    /**
     * 根据ID获取节日活动详情
     * @param int $id
     * @return array|null
     */
    public function getById($id)
    {
        return $this->db->table($this->table)
            ->where('id = :id and is_delete = 0', ['id' => $id])
            ->one();
    }

    /**
     * 创建节日活动
     * @param array $data
     * @return int 新创建的ID
     */
    public function create($data)
    {
        // 处理JSON字段
        if (isset($data['sites_config']) && is_array($data['sites_config'])) {
            $data['sites_config'] = json_encode($data['sites_config'], JSON_UNESCAPED_UNICODE);
        }
        if (isset($data['stock_rules']) && is_array($data['stock_rules'])) {
            $data['stock_rules'] = json_encode($data['stock_rules'], JSON_UNESCAPED_UNICODE);
        }

        return $this->db->table($this->table)->insert($data);
    }

    /**
     * 更新节日活动
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update($id, $data)
    {
        // 处理JSON字段
        if (isset($data['sites_config']) && is_array($data['sites_config'])) {
            $data['sites_config'] = json_encode($data['sites_config'], JSON_UNESCAPED_UNICODE);
        }
        if (isset($data['stock_rules']) && is_array($data['stock_rules'])) {
            $data['stock_rules'] = json_encode($data['stock_rules'], JSON_UNESCAPED_UNICODE);
        }

        return $this->db->table($this->table)
            ->where('id = :id', ['id' => $id])
            ->update($data);
    }

    /**
     * 删除节日活动
     * @param int $id
     * @return bool
     */
    public function delete($id)
    {
        return $this->db->table($this->table)
            ->where('id = :id', ['id' => $id])
            ->update(['is_delete' => 1]);
    }

    /**
     * 更新状态
     * @param int $id
     * @param int $status
     * @return bool
     */
    public function updateStatus($id, $status)
    {
        return $this->db->table($this->table)
            ->where('id = :id', ['id' => $id])
            ->update(['status' => $status]);
    }

    /**
     * 获取当前有效的节日活动
     * @param string $date 指定日期，默认为今天
     * @return array
     */
    public function getActiveActivities($date = null)
    {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        return $this->db->table($this->table)
            ->where('status = :status AND start_date <= :start_date AND end_date >= :end_date', [
                'status' => 1,
                'start_date' => $date,
                'end_date' => $date
            ])
            ->order('created_at DESC')
            ->list();
    }

    /**
     * 根据站点获取节日活动配置
     * @param string $site 站点代码
     * @param string $date 指定日期
     * @return array
     */
    public function getActivitiesBySite($site, $date = null)
    {
        $activities = $this->getActiveActivities($date);
        $result = [];

        foreach ($activities as $activity) {
            if (!empty($activity['sites_config'])) {
                $sitesConfig = json_decode($activity['sites_config'], true);
                if (isset($sitesConfig[$site])) {
                    $activity['site_config'] = $sitesConfig[$site];
                    $activity['stock_rules'] = json_decode($activity['stock_rules'], true);
                    $result[] = $activity;
                }
            }
        }

        return $result;
    }

    /**
     * 检查活动名称是否重复
     * @param string $name
     * @param string $startDate 开始日期
     * @param int $excludeId 排除的ID（用于更新时）
     * @return bool
     */
    public function isNameExists($name, $startDate,$excludeId = null)
    {

        // 同一年内，不可存在重复的节日或活动名称
        $this->db->table($this->table)
            ->where('name = :name and YEAR(start_date) = YEAR(:start_date)', 
            ['name' => $name, 'start_date' => $startDate]);

        if ($excludeId) {
            $this->db->andwhere('id != :exclude_id', ['exclude_id' => $excludeId]);
        }

        
        $count = $this->db->count();
        return $count > 0;
    }

    /**
     * 验证日期格式
     * @param string $date
     * @return bool
     */
    private function isValidDate($date)
    {
        $d = \DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }
}
