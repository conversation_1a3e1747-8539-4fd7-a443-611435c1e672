<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/1 10:30
 */

namespace  plugins\goods\models;

use plugins\goods\form\goodsNewFrom;
use core\lib\config;
use core\lib\db\dbMysql;

class goodsAttachmentModel
{
    public static function setGoodsAttachment($goods_id, $new_attachment) {
        $db = dbMysql::getInstance();
        $old_attachment = $db->queryAll('select id,`type`,url,file_name from oa_goods_attachment where goods_id = :goods_id and is_delete = 0',['goods_id'=>$goods_id]);
        $attachment_urls = array_column($old_attachment,'url');

        $insert_sql = 'insert into oa_goods_attachment (user_id,goods_id,`type`,url,file_name,created_time) values ';
        $insert_data = [];
        $goods_update_log_data = [];
        foreach ($new_attachment as $k=>$v) {
            if (!in_array($v['url'],$attachment_urls)) {
                //新增sql拼接
                $insert_sql.="(:user_id,:goods_id,:type_$k,:url_$k,:file_name_$k,:created_time),";
                $insert_data["type_$k"] = $v['type'];
                $insert_data["url_$k"] = $v['url'];
                $insert_data["file_name_$k"] = $v['file_name'];
                $goods_update_log_data[$v['type']]['add'][] = $v['file_name'];
            }
        }
        //新增动作
        if (count($insert_data)) {
            $insert_data['user_id'] = userModel::$qwuser_id;
            $insert_data['goods_id'] = $goods_id;
            $insert_data['created_time'] = date('Y-m-d H:i:s');
            $insert_sql = trim($insert_sql,',');
            $db->query($insert_sql, $insert_data);
        }
        //删除动作
        $new_attachment_urls = array_column($new_attachment,'url');
        $del_list = array_diff($attachment_urls,$new_attachment_urls);
        if (count($del_list)) {
            foreach ($old_attachment as $v) {
                if (in_array($v['url'],$del_list)) {
                    $db->query('update oa_goods_attachment set is_delete = 1 where id=:id', ['id'=>$v['id']]);
                    $goods_update_log_data[$v['type']]['del'][] = $v['file_name'];
                }
            }
        }
        //新增日志
        if (count($goods_update_log_data) && count($old_attachment)) {
            $update_data = [];
            foreach ($goods_update_log_data as $k=>$v) {
                $key_name = config::getDataName('goods_attachment_type',$k);
                $msg = '';
                if (!empty($v['add'])) {
                    $msg .= '新增：'.implode(',',$v['add']).'；';
                }
                if (!empty($v['add'])) {
                    $msg .= '删除：'.implode(',',$v['add']);
                }
                $update_data[] = [
                    'key_name'=>$key_name,
                    'data'=>$msg
                ];
            }
            //设置产品修改信息
            goodsNewFrom::$update_log_data['attachment'] = $update_data;
        }
    }
}