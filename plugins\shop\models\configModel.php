<?php

namespace plugins\shop\models;

use core\lib\db\dbAfMysql;
use core\lib\db\dbMysql;
use core\lib\db\dbShopMysql;
use core\lib\redisCached;

class configModel
{

    const TYPE_CONST_USER = 1; // 常量用户
    const TYPE_DEP_LEADER = 2; // 部门负责人
    const TYPE_CREDIT_CARD_RECORD_KEEPER = 3; // 信用卡登记负责人
    const SHOP_YUNYING = 4; // listing_data 运营
    const SHOP_LEADER = 5; // listing_data 负责
    const SHOP_COORDINATOR = 6; // 店铺注册对接人

    public static function noticeUser($key_name, $key, $data = [])
    {
        $db = dbShopMysql::getInstance();
        $config = $db->table('config')->where('key_name = :key_name', ['key_name' => $key_name])->one();
        if (empty($config)) {
            return false;
        }
        $moduleConfig = json_decode($config['data'], true);
        if (empty($moduleConfig)) {
            return false;
        }
        $config = $moduleConfig[$key];
        if (empty($config)) {
            return false;
        }
        $data['users'] = $data['users'] ?? [];
        if (in_array(self::TYPE_CONST_USER, $config['type'])) {
            $data['users'] = array_merge($config['users'], $data['users']);
        }
        $users = self::getUserByType($config['type'], $data);

        if (empty($users)) {
            return false;
        }
        return $users;


    }

    public static function getUserByType($types, $data)
    {
        $userMap = dbMysql::getInstance()->table('qwuser')->list();
        $userMap = array_column($userMap, 'wid', 'id');

        $departmentMap = dbMysql::getInstance()->table('qwdepartment')->list();
        $departmentMap = array_column($departmentMap, 'department_leader', 'id');
        foreach ($departmentMap as &$department_leader) {
            $department_leader = json_decode($department_leader, true);
        }
        unset($department_leader);

        $users = [];
        if (empty($types)) {
            return [];
        }
        // 指定用户
        if (isset($data['users']) && !empty($data['users'])) {
            foreach ($data['users'] as $user) {
                $users[] = $userMap[$user];
            };
        }
        // 部门负责人
        if (in_array(self::TYPE_DEP_LEADER, $types)) {
            if (isset($data['dep_id']) && !empty($data['dep_id'])) {
                $department_leader = $departmentMap[$data['dep_id']];
                if (!empty($department_leader)) {
                    $users = array_merge($users, $department_leader);
                }
            }
        }

        // 信用卡登记负责人
        if (in_array(self::TYPE_CREDIT_CARD_RECORD_KEEPER, $types)) {
            if (isset($data['credit_card_record_keeper']) && !empty($data['credit_card_record_keeper'])) {
                $users[] = $userMap[$data['credit_card_record_keeper']];
            }
        }

        if (in_array(self::SHOP_YUNYING, $types) || in_array(self::SHOP_LEADER, $types)) {
            if (isset($data['shop_id']) && !empty($data['shop_id'])) {
                $listing = self::getListingByShopId($data['shop_id']);
            }
            // listing_data 运营
            if (in_array(self::SHOP_YUNYING, $types)) {
                foreach ($listing['yunying'] as $uid) {
                    $users[] = $userMap[$uid];
                }
            }
            // listing_data 负责人
            if (in_array(self::SHOP_LEADER, $types)) {
                foreach ($listing['leader'] as $uid) {
                    $users[] = $userMap[$uid];
                }
            }
        }

        // 店铺注册对接人
        if (in_array(self::SHOP_COORDINATOR, $types)) {
            if (isset($data['shop_coordinator']) && !empty($data['shop_coordinator'])) {
                $users[] = $userMap[$data['shop_coordinator']];
            }
        }
        // 去重
        return array_values(array_unique($users));

    }

    public static function getListingByShopId($shop_id)
    {
        if (empty($shop_id)) {
            return [];
        }
        // 获取LxShopId
        $shopList = dbShopMysql::getInstance()->table('shop')->field('lx_shop_id')->where('id = :id', ['id' => $shop_id])->list();
        $sid = array_column($shopList, 'lx_shop_id');

        $afdb = dbAfMysql::getInstance();

        $listing = $afdb->table('lingxing_listing','a')
            ->leftJoin('shop_list','b','b.sid = a.sid')
            ->where('a.status = 1')
            ->whereIn('b.sid', $sid)
            ->field('a.asin,a.marketplace')
            ->groupBy(['asin','marketplace'])
            ->list();

        if (empty($listing)) {
            return [];
        }

        // 查询listing
        $afdb->table('listing_data','a')
            ->leftJoin('market','b','b.code = a.country_code')
            ->leftJoinOut('shop', 'shop_plan', 'sp', 'sp.listing_id = a.id');

        list($sql_str,$sql_data) = shopPlanModel::getSqlInDataClon($listing);
        $afdb->where("(a.asin,b.country) in ($sql_str)",$sql_data);
        $list = $afdb->field('a.id, a.yunying_ids, a.leader_ids')->list();

        $yunying = [];
        $leader = [];

        foreach ($list as &$item) {
            $item['yunying_ids'] = json_decode($item['yunying_ids'], true);
            $yunying = array_merge($yunying, $item['yunying_ids']);
            $item['leader_ids'] = json_decode($item['leader_ids'], true);
            $leader = array_merge($leader, $item['leader_ids']);
        }
        return [
            'yunying' => array_values(array_unique($yunying)),
            'leader'  => array_values(array_unique($leader)),
        ];
    }



}