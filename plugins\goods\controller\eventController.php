<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/13 17:52
 */

namespace  plugins\goods\controller;

use plugins\goods\models\userModel;
use core\lib\db\dbMysql;

class eventController
{
    public function getList() {
        $paras_list = array('event_name', 'event_type', 'status', 'qw_name', 'manage_wid', 'order_by', 'send_copy_wid', 'page', 'page_size');
        $param = arrangeParam($_POST, $paras_list);
        $where_sql = 'where is_delete=0';
        $where_data = [];
        if (!empty($param['event_name'])) {
            $where_sql .= ' and event_name like :event_name';
            $where_data['event_name'] = '%'.$param['event_name'].'%';
        }
        $event_type = (int)$param['event_type'];
        if ($event_type > -1) {
            $where_sql .= ' and event_type = :event_type';
            $where_data['event_type'] = $event_type;
        }
        if($param['status'] > -1) {
            $where_sql .= ' and status = :status';
            $where_data['status'] = $param['status'];
        }
        if (!empty($param['manage_wid'])) {
            $where_sql .= " and manage_info->'$[*].wid' like :manage_wid";
            $where_data['manage_wid'] = '%'.$param['manage_wid'].'%';
        }
        if (!empty($param['send_copy_wid'])) {
            $where_sql .= ' and send_copy_user like :send_copy_wid';
            $where_data['send_copy_wid'] = '%"wid":"'.$param['send_copy_wid'].'"%';
        }
        $db = dbMysql::getInstance();
        $db->table('event');
        $db->field('id,event_name,event_type,manage_info,created_at,status,description,need_check,check_user_info,send_copy_user,is_developer');
        $db->where($where_sql, $where_data);
        $order_str = '';
        if (!empty($param['order_by'])) {
            $order_by = json_decode($param['order_by'],true);
            foreach ($order_by as $k=>$ord){
                $order_str .= ''.$k.' '.$ord.',';
            }
        }
        if (empty($order_str)) {
            $db->order('id desc');
        } else {
            $db->order(trim($order_str,','));
        }
        $list = $db->pages($param['page'], $param['page_size']);
        returnSuccess($list);
    }

    public function editEvent() {
        $paras_list = array('id', 'event_name', 'event_type', 'status', 'manage_info', 'description', 'need_check', 'check_user_info','send_copy_user','expected_day','is_advance_submit','delay_hour','manage_type');
        $length_data = ['event_name'=>['name'=>'事件名称','length'=>20],'description'=>['name'=>'描述','length'=>225]];
        $request_list = ['event_name'=>"事件名称",'event_type'=>"事件类型",'manage_type'=>"负责人类型"];
        $param = arrangeParam($_POST, $paras_list,$request_list,$length_data);
        $id = (int)$param['id'];
        if (empty($param['event_name']) || empty($param['event_type']) || empty($param['manage_info'])) {
            SetReturn(-1, '缺少必传参数');
        }
        $manage_info = json_decode($param['manage_info'], true);
        $event_type = (int)$param['event_type'];
        $manage_type = json_decode($param['manage_type'], true);
        $is_developer = 0;
        if (in_array(2,$manage_type)) {
            $is_developer = 1;
        }
        if (count($manage_info) > 3) {
            SetReturn(-1, '该事件负责人不能超过3人');
        }
        if (count($manage_info) > 1 && $event_type != 4) {
            SetReturn(-1, '该事件负责人只能是一人');
        }
        if (!$is_developer) {
            if (!count($manage_info)) {
                SetReturn(-1, '请选择负责人员');
            }
        }
        foreach ($manage_info as $v) {
                if (empty($v['wid']) || empty($v['wname']) || empty($v['avatar'])) {
                SetReturn(-1, '负责人参数有误');
            }
        }
        $data = [
            'event_name'=>$param['event_name'],
            'event_type'=>(int)$param['event_type'],
            'manage_info'=>json_encode($manage_info),
            'status'=>(int)$param['status'],
            'description'=>$param['description'],
            'need_check'=>(int)$param['need_check'],
            'check_user_info'=>$param['check_user_info']??'',
            'send_copy_user'=>$param['send_copy_user'],
            'expected_day'=>(int)$param['expected_day'],
            'is_advance_submit'=>(int)$param['is_advance_submit'],
            'delay_hour'=>(int)$param['delay_hour'],
            'is_developer'=>$is_developer,
        ];
        $db = dbMysql::getInstance();
        $db->table('event');
        $event = $db->where('where id = :id', ['id'=>$id])->one();
        if (!$event) {
            //新增
            $data['user_id'] = userModel::$qwuser_id;
            $data['created_at'] = date('Y-m-d H:i:s');
            if($db->insert($data)) {
                SetReturn(0, '添加成功');
            } else {
                SetReturn(0, '添加失败');
            }
        } else {
            //修改
            $data['updated_at'] = date('Y-m-d H:i:s');
            $db->where('where id = :id', ['id'=>$event['id']]);
            if($db->update($data)) {
                SetReturn(0, '修改成功');
            } else {
                SetReturn(0, '修改失败');
            }

        }
    }

    public function getDtail() {
        $id = (int)$_GET['id'];
        if (!$id) {
            SetReturn(-1, '参数有有误');
        }
        $db = dbMysql::getInstance();
        $db->table('event');
        $db->field('id,event_name,event_type,manage_info,status,description,need_check, check_user_info,send_copy_user,expected_day,is_advance_submit,delay_hour,is_developer');
        $event = $db->where('where id = :id', ['id'=>$id])->one();
        $event['manage_type'] = [];
        if ($event['is_developer']) {
            $event['manage_type'][] = '2';
        }
        if (!($event['manage_info'] == '[]' || empty($event['manage_info']))) {
            $event['manage_type'][] = '1';
        }
        returnSuccess(['data'=>$event]);
    }

    public function setStatus() {
        $paras_list = array('ids','status');
        $param = arrangeParam($_POST, $paras_list);
        $ids = json_decode($param['ids']);
        $status = (int)$param['status'];
        if (empty($param['ids'])) {
            SetReturn(-1,'请选择要操作的数据');
        }
        //查询需要绑定的模板
        $ids = getArryForType($ids);
        $db = dbMysql::getInstance();
        $db->table('event');
        $db->whereIn('id', $ids);
        $db->update(['status'=>$status, 'updated_at'=>date('Y-m-d H:i:s')]);
        returnSuccess(0,'设置成功');
    }
}