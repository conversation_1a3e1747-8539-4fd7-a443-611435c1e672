<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/20 10:19
 */

namespace financial\form;

use core\lib\db\dbFMysql;
use core\lib\ExceptionError;
use financial\common\importNeedDataBase;
use financial\common\mskuReportBase;
use financial\models\goodsDataModel;
use financial\models\goodsStorkModel;
use financial\models\mskuReportModel;
use financial\models\userModel;
use Rap2hpoutre\FastExcel\FastExcel;

class mskuReportForm extends mskuReportBase
{
    //列表查询需要返回的数据：0列表数据，1base_id,2$db(sql)
    public static int $list_type = 0;
    //获取数据整月数据
    public  function getList($param) {
        $project_ids = json_decode($param['project_ids']);
        $date_time = $param['date_time'];
        $db = dbFMysql::getInstance();
        //导入时间
        $use_import_id = false;
        $import_ids = [];
        if (!empty($param['import_time']) && $param['import_time'] != '[]') {
            $use_import_id = true;
            $import_time = json_decode($param['import_time']);
            $msku_report_import = $db->table('msku_report_import')
                ->where('report_date = :date_time',['date_time'=>$date_time])
                ->andWhere('created_time >= :begin_time and created_time <= :end_time',['begin_time'=>$import_time[0],'end_time'=>$import_time[1]])
                ->field('id')
                ->list();
            $import_ids = array_column($msku_report_import,'id');
        }
        $db->table($this->table_name,'a')
            ->leftJoin('project','f','f.id=a.project_id')
            ->leftJoin('supplier','su','su.id=a.supplier_id')
            ->leftJoin('msku_report_import','mri','mri.id=a.import_id')
            ->leftJoinOut('db','qwuser','e','e.id=a.yunying_id')
            ->where('where a.is_delete = 0 and a.reportDateMonth=:date_time and import_id > 0',['date_time'=>$date_time])
            ->field('f.project_name as projectName,e.wname as yunying,su.supplier_name,mri.created_time as import_time,a.*');
        if (!empty($param['sid'])) {
            $db->andWhere('a.sid=:sid',['sid'=>$param['sid']]);
        }
        if (count($project_ids)) {
            $db->whereIn('a.project_id',$project_ids);
        }
        if (!empty($param['search_value']) && $param['search_type'] > 0) {
            if ($param['search_type'] == 'goods_name') {
                $db->andWhere('a.localName like :goods_name',['goods_name'=>"%{$param['search_value']}%"]);
            } elseif ($param['search_type'] == 'asin') {
                $db->andWhere('a.asin like :asin',['asin'=>"%{$param['search_value']}%"]);
            } elseif ($param['search_type'] == 'sku') {
                $db->andWhere('a.localSku like :sku',['sku'=>"%{$param['search_value']}%"]);
            } elseif ($param['search_type'] == 'parentAsin') {
                $db->andWhere('a.parentAsin like :parentAsin',['parentAsin'=>"%{$param['search_value']}%"]);
            }
        }
        if ($use_import_id) {
            $db->whereIn('import_id',$import_ids);
        }
        if (self::$list_type == 1) {
            $list = $db->field('a.id')
                ->andWhere('import_id > 0')
                ->list();
            return $list;
        } elseif (self::$list_type == 2) {
            return $db;
        } else {
            //导出时有ids就按此导出
            if (isset($param['ids'])) {
                $db->whereIn('a.id',json_decode($param['ids']));
            }
            //列表
            $list = $db->order('a.id desc')
                ->pages($param['page'],$param['page_size']);
            foreach ($list['list'] as $k=>$v) {
                $categoryName = explode('/',$v['categoryName']);
                $list['list'][$k]['categoryName1'] = $categoryName[0];
                $list['list'][$k]['categoryName2'] = $categoryName[1];
                $list['list'][$k]['categoryName3'] = $categoryName[2];
            }
            //总计
            return $list;
        }
    }
    //获取导入表格错误状态
    public static function getLogErrorList($list) {
        $import_list = array_column($list,'id');
        $db = dbFMysql::getInstance();
        $error_list = $db->table('msku_report_import_error_log')
            ->whereIn('import_id',$import_list)
            ->groupBy(['import_id'])
            ->field('MIN(id) AS id,import_id')
            ->list();
        foreach ($list as &$v) {
            $v['has_error'] = 0;
            foreach ($error_list as $v1) {
                if ($v1['import_id'] == $v['id']) {
                    $v['has_error'] = 1;
                }
            }
        }
        return $list;
    }
    //全量数据批量删除-勾选删除
    public function delById($ids,$date_time) {
        goodsDataModel::creatGoodsDataTable($date_time);
        goodsStorkModel::creatGoodsStockTable($date_time);
        $db = dbFMysql::getInstance();
        $db->beginTransaction();
        try {
            //删除选择的数据
            $db->table($this->table_name)
                ->where('is_delete = 0')
                ->whereIn('id',$ids)
                ->update([
                    'is_delete'=>1,
                    'del_user_id'=>userModel::$qwuser_id,
                    'del_time'=>date('Y-m-d H:i:s')
                ]);
            //删除库存数据
            $db->table('goods_stock_'.$this->year)
                ->whereIn('base_id',$ids)
                ->update(['is_delete'=>1]);
            //删除增量数据
            $db->table('goods_data_'.$this->year)
                ->whereIn('base_id',$ids)
                ->update(['is_delete'=>1]);
            $db->commit();
        } catch (ExceptionError $error){
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //全量数据批量删除-整月删除
    public function delByMdata($date_time) {
        goodsDataModel::creatGoodsDataTable($date_time);
        goodsStorkModel::creatGoodsStockTable($date_time);
        $db = dbFMysql::getInstance();
        $db->beginTransaction();
        try {
            //删除选择的数据
            $db->table($this->table_name)
                ->where('lingxing_id = 0 and is_delete = 0 and reportDateMonth = :m_date',['m_date'=>$date_time])
                ->update([
                    'is_delete'=>1,
                    'del_user_id'=>userModel::$qwuser_id,
                    'del_time'=>date('Y-m-d H:i:s')
                ]);
            //删除库存数据
            $db->table('goods_stock_'.$this->year)
                ->where('m_date = :m_date',['m_date'=>$date_time])
                ->update(['is_delete'=>1]);
            //删除增量数据
            $db->table('goods_data_'.$this->year)
                ->where('m_date = :m_date',['m_date'=>$date_time])
                ->update(['is_delete'=>1]);
            $db->commit();
        } catch (ExceptionError $error){
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }

    //通过sid删除店的数据
    public function delSellerData($sids,$date) {
        goodsDataModel::creatGoodsDataTable($date);
        goodsStorkModel::creatGoodsStockTable($date);
        $db = dbFMysql::getInstance();
        $db->beginTransaction();
        //删除本月数据
        try {
            $db->table($this->table_name)
                ->where('where reportDateMonth=:dateM',['dateM'=>$date])
                ->whereIn('sid',$sids)
                ->update([
                    'is_delete'=>1,
                    'del_user_id'=>userModel::$qwuser_id,
                    'del_time'=>date('Y-m-d H:i:s')
                ]);
            //删除已计算的本月数据
            $year = date('Y',strtotime($date));
            $db->table("custom_val_$year")
                ->whereIn('sid',$sids)
                ->where('where m_date=:m_date',['m_date'=>$date])
                ->delete();
            //删除库存数据
            $db->table('goods_stock_'.$this->year)
                ->whereIn('sid',$sids)
                ->update(['is_delete'=>1]);
            //删除增量数据
            $db->table('goods_data_'.$this->year)
                ->whereIn('sid',$sids)
                ->update(['is_delete'=>1]);
            //删除统计的原始数据
            $db->table("msku_original_data_$year")
                ->whereIn('sid',$sids)
                ->update(['is_delete'=>1]);
            //删除记录
            foreach ($sids as $sid) {
                $db->table("msku_original_data_$year")
                    ->insert([
                        'user_id'=>userModel::$qwuser_id,
                        'sid'=>$sid,
                        'm_date'=>$date,
                        'created_time'=>date('Y-m-d H:i:s')
                    ]);
            }
            $db->commit();
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //导入表头判断
    public static function verifyExcelHead($row) {
        $import_key_list = mskuReportModel::$import_key_list;
        $diff = array_diff_key($import_key_list,$row);
        if (count($diff)) {
            $diif_name = array_keys($diff);
            returnError('模板缺少列【'.implode('，',$diif_name).'】');
        }
    }
    //导入数据
    public function importSellerReportData($list,$date,$import_id) {
        $db = dbFMysql::getInstance();
        $year = date('Y',strtotime($date.'-01'));
        //获取导入的店铺
        $sid_name = array_column($list,'storeName');
        $seller_ = importNeedDataBase::getSellerByName($sid_name);
        //国家数据
        $country_name = array_column($list,'country');
        $country_ = importNeedDataBase::getCountryListByName($country_name);
        //币种数据
        $routing_name = array_column($list,'currencyCode');
        $routing_ = importNeedDataBase::getRoutingByCode($routing_name,$date);
        //供应商
        $supplier_list = importNeedDataBase::getSupplierList();
        $supplier_ = [];
        if (count($supplier_list)) {
            foreach ($supplier_list as $col) {
                $supplier_[$col['supplier_name']] = $col['id'];
            }
        }
        //运营
        $yunying_name = array_column($list,'yunying');
        $qwuser_ = importNeedDataBase::qwuserByNameList($yunying_name);
        //项目
        $project_ = importNeedDataBase::getProjectAll();
        //保存数据
        $success_count = 0;
        $error_list = [];
        foreach ($list as $k=>$v) {
            $row_num = $k+1;
            //店铺名
            if (!isset($seller_[$v['storeName']])) {
//                $error_list[] = ['error_reason'=>'未查询到店铺名','data'=>$v,'row_num'=>$row_num];
//                continue;
                $sid = 0;
                $storeName = $v['storeName']??'';
            } else {
                $sid = $seller_[$v['storeName']]['sid'];
                $storeName = $seller_[$v['storeName']]['real_name'];
            }
            //国家
            if (!isset($country_[$v['country']])) {
                $countryCode = '';
//                $error_list[] = ['error_reason'=>'未查询到国家名','data'=>$v,'row_num'=>$row_num];
//                continue;
            } else {
                $countryCode = $country_[$v['country']]['code'];
            }
            //币种
            if (!isset($routing_[$v['currencyCode']])) {
                $currencyIcon = '';
//                $error_list[] = ['error_reason'=>'未查询到该月币种汇率名','data'=>$v,'row_num'=>$row_num];
//                continue;
            } else {
                $currencyIcon = $routing_[$v['currencyCode']]['icon'];
            }
            //供应商
            $supplier_id = isset($supplier_[$v['supplier_name']])?$supplier_[$v['supplier_name']]:0;
            //运营
            $yunying_id = !empty($qwuser_[$v['yunying']])?$qwuser_[$v['yunying']]:0;
            //项目
            if (empty($project_[$v['project_name']])) {
                $error_list[] = ['error_reason'=>'未查询到项目','data'=>$v,'row_num'=>$row_num];
                continue;
            } else {
                $project_id = $project_[$v['project_name']]['id'];
            }
//            if (!$supplier_id) {
//                $error_list[] = ['error_reason'=>'未查询到该供应商','data'=>$v,'row_num'=>$row_num];
//                continue;
//            }
            /**部门验证（按月lisitng匹配）
            $listing_where = [
                'm_date'=>$date,
                'sid'=>$sid,
                'marketplace'=>$v['country'],
                'seller_sku'=>$v['msku'],
                'asin'=>$v['asin'],
                'parent_asin'=>$v['parentAsin'],
                'local_sku'=>$v['localSku'],
            ];
            $listing_data = $db->table('project_listing_'.$year)
                ->where('where m_date=:m_date and sid=:sid and marketplace=:marketplace and seller_sku=:seller_sku and asin=:asin and parent_asin=:parent_asin and local_sku=:local_sku',$listing_where)
                ->one();
            if (!$listing_data) {
                $error_list[] = ['error_reason'=>'未匹配到项目','data'=>$v,'row_num'=>$row_num];
                continue;
            }
            $project_id = $listing_data?$listing_data['project_id']:0;
            $yunying_id = $listing_data?$listing_data['yunying_id']:0;
            **/

            $category_id = 0;
            $categoryName = $v['categoryName1'].'/'. $v['categoryName2'].'/'. $v['categoryName3'];
            $data_ = [
                 'sid'=>$sid,'import_id'=>$import_id,'project_id'=>$project_id,'yunying_id'=>$yunying_id,'category_id'=>$category_id,'reportDateMonth'=>$date,'msku'=>$v['msku'],'asin'=>$v['asin'],'parentAsin'=>$v['parentAsin'],'storeName'=>$storeName,'countryCode'=>$countryCode,'localName'=>$v['localName'],'localSku'=>$v['localSku'],'principalRealname'=>$v['principalRealname'],'categoryName'=>$categoryName,'brandName'=>$v['brandName'],'currencyCode'=>$v['currencyCode'],'currencyIcon'=>$currencyIcon,'country'=>$v['country'],'itemName'=>$v['itemName'],'productDeveloperRealname'=>$v['productDeveloperRealname'],'listingTagIds'=>$v['listingTagIds'],'supplier_id'=>$supplier_id,
            ];
            foreach ($v as $v_key=>$v_val) {
                if (!isset($data_[$v_key])) {
                    $v[$v_key] = floatval($v_val);
                }
            }
            $data1 = ['fbaSalesQuantity'=>$v['fbaSalesQuantity'],'fbmSalesQuantity'=>$v['fbmSalesQuantity'],'reshipFbmProductSalesQuantity'=>$v['reshipFbmProductSalesQuantity'],'reshipFbmProductSaleRefundsQuantity'=>$v['reshipFbmProductSaleRefundsQuantity'],'reshipFbaProductSalesQuantity'=>$v['reshipFbaProductSalesQuantity'],'reshipFbaProductSaleRefundsQuantity'=>$v['reshipFbaProductSaleRefundsQuantity'],'adsSdSales'=>$v['adsSdSales'],'adsSpSales'=>$v['adsSpSales'],'sharedAdsSbSales'=>$v['sharedAdsSbSales'],'sharedAdsSbvSales'=>$v['sharedAdsSbvSales'],'adsSdSalesQuantity'=>$v['adsSdSalesQuantity'],'adsSpSalesQuantity'=>$v['adsSpSalesQuantity'],'sharedAdsSbSalesQuantity'=>$v['sharedAdsSbSalesQuantity'],'sharedAdsSbvSalesQuantity'=>$v['sharedAdsSbvSalesQuantity'],'fbaSaleAmount'=>$v['fbaSaleAmount'],'fbmSaleAmount'=>$v['fbmSaleAmount'],'shippingCredits'=>$v['shippingCredits'],'promotionalRebates'=>$v['promotionalRebates'],'fbaInventoryCredit'=>$v['fbaInventoryCredit'],'cashOnDelivery'=>$v['cashOnDelivery'],'fbaLiquidationProceeds'=>$v['fbaLiquidationProceeds'],'fbaLiquidationProceedsAdjustments'=>$v['fbaLiquidationProceedsAdjustments'],'amazonShippingReimbursement'=>$v['amazonShippingReimbursement'],'mcFbaFulfillmentFeesQuantity'=>$v['mcFbaFulfillmentFeesQuantity'],'safeTReimbursement'=>$v['safeTReimbursement'],'netcoTransaction'=>$v['netcoTransaction'],'reimbursements'=>$v['reimbursements'],'clawbacks'=>$v['clawbacks'],'sharedComminglingVatIncome'=>$v['sharedComminglingVatIncome'],'giftWrapCredits'=>$v['giftWrapCredits'],'guaranteeClaims'=>$v['guaranteeClaims'],'costOfPoIntegersGranted'=>$v['costOfPoIntegersGranted'],'fbaSalesRefunds'=>$v['fbaSalesRefunds'],'fbmSalesRefunds'=>$v['fbmSalesRefunds'],'shippingCreditRefunds'=>$v['shippingCreditRefunds'],'giftWrapCreditRefunds'=>$v['giftWrapCreditRefunds'],'chargebacks'=>$v['chargebacks'],'costOfPoIntegersReturned'=>$v['costOfPoIntegersReturned'],'promotionalRebateRefunds'=>$v['promotionalRebateRefunds'],'sellingFeeRefunds'=>$v['sellingFeeRefunds'],'fbaTransactionFeeRefunds'=>$v['fbaTransactionFeeRefunds'],'refundAdministrationFees'=>$v['refundAdministrationFees'],'otherTransactionFeeRefunds'=>$v['otherTransactionFeeRefunds'],'fbaInventoryCreditQuantity'=>$v['fbaInventoryCreditQuantity'],'mcFbaDeliveryFee'=>$v['mcFbaDeliveryFee']
            ];
            $data2 = [
                'refundForAdvertiser'=>$v['refundForAdvertiser'],'pointsAdjusted'=>$v['pointsAdjusted'],'shippingLabelRefunds'=>$v['shippingLabelRefunds'],'refundsQuantity'=>$v['refundsQuantity'],'refundsRate'=>$v['refundsRate'],'fbaReturnsSaleableQuantity'=>$v['fbaReturnsSaleableQuantity'],'fbaReturnsUnsaleableQuantity'=>$v['fbaReturnsUnsaleableQuantity'],'fbaReturnsQuantityRate'=>$v['fbaReturnsQuantityRate'],'platformFee'=>$v['platformFee'],'fbaDeliveryFee'=>$v['fbaDeliveryFee'],'otherTransactionFees'=>$v['otherTransactionFees'],'adsSpCost'=>$v['adsSpCost'],'adsSbCost'=>$v['adsSbCost'],'adsSbvCost'=>$v['adsSbvCost'],'adsSdCost'=>$v['adsSdCost'],'sharedCostOfAdvertising'=>$v['sharedCostOfAdvertising'],'sharedSubscriptionFee'=>$v['sharedSubscriptionFee'],'sharedLdFee'=>$v['sharedLdFee'],'sharedCouponFee'=>$v['sharedCouponFee'],'sharedEarlyReviewerProgramFee'=>$v['sharedEarlyReviewerProgramFee'],'sharedVineFee'=>$v['sharedVineFee'],'sharedFbaInboundDefectFee'=>$v['sharedFbaInboundDefectFee'],'fbaStorageFee'=>$v['fbaStorageFee'],'sharedFbaStorageFee'=>$v['sharedFbaStorageFee'],'longTermStorageFee'=>$v['longTermStorageFee'],'sharedLongTermStorageFee'=>$v['sharedLongTermStorageFee'],'sharedStorageRenewalBilling'=>$v['sharedStorageRenewalBilling'],'sharedFbaDisposalFee'=>$v['sharedFbaDisposalFee'],'sharedFbaRemovalFee'=>$v['sharedFbaRemovalFee'],'sharedFbaInboundTransportationProgramFee'=>$v['sharedFbaInboundTransportationProgramFee'],'sharedLabelingFee'=>$v['sharedLabelingFee'],'sharedPolybaggingFee'=>$v['sharedPolybaggingFee'],'sharedBubblewrapFee'=>$v['sharedBubblewrapFee'],'sharedTapingFee'=>$v['sharedTapingFee'],'sharedFbaCustomerReturnFee'=>$v['sharedFbaCustomerReturnFee'],'sharedFbaOverageFee'=>$v['sharedFbaOverageFee'],'sharedAmazonPartneredCarrierShipmentFee'=>$v['sharedAmazonPartneredCarrierShipmentFee'],'sharedFbaInboundConvenienceFee'=>$v['sharedFbaInboundConvenienceFee'],'sharedItemFeeAdjustment'=>$v['sharedItemFeeAdjustment'],'sharedOtherFbaInventoryFees'=>$v['sharedOtherFbaInventoryFees'],'fbaStorageFeeAccrual'=>$v['fbaStorageFeeAccrual'],'fbaStorageFeeAccrualDifference'=>$v['fbaStorageFeeAccrualDifference'],'longTermStorageFeeAccrual'=>$v['longTermStorageFeeAccrual'],'longTermStorageFeeAccrualDifference'=>$v['longTermStorageFeeAccrualDifference'],'sharedFbaIntegerernationalInboundFee'=>$v['sharedFbaIntegerernationalInboundFee'],'adjustments'=>$v['adjustments'],'shippingLabelPurchases'=>$v['shippingLabelPurchases'],'disposalQuantity'=>$v['disposalQuantity'],'removalQuantity'=>$v['removalQuantity']
            ];
            $data3 = [
                'sharedCarrierShippingLabelAdjustments'=>$v['sharedCarrierShippingLabelAdjustments'],'sharedLiquidationsFees'=>$v['sharedLiquidationsFees'],'sharedManualProcessingFee'=>$v['sharedManualProcessingFee'],'sharedOtherServiceFees'=>$v['sharedOtherServiceFees'],'sharedMfnPostageFee'=>$v['sharedMfnPostageFee'],'tcsIgstCollected'=>$v['tcsIgstCollected'],'tcsSgstCollected'=>$v['tcsSgstCollected'],'tcsCgstCollected'=>$v['tcsCgstCollected'],'sharedComminglingVatExpenses'=>$v['sharedComminglingVatExpenses'],'taxCollected'=>$v['taxCollected'],'taxCollectedProduct'=>$v['taxCollectedProduct'],'taxCollectedDiscount'=>$v['taxCollectedDiscount'],'taxCollectedShipping'=>$v['taxCollectedShipping'],'taxCollectedGiftWrap'=>$v['taxCollectedGiftWrap'],'sharedTaxAdjustment'=>$v['sharedTaxAdjustment'],'tcsIgstRefunded'=>$v['tcsIgstRefunded'],'tcsSgstRefunded'=>$v['tcsSgstRefunded'],'tcsCgstRefunded'=>$v['tcsCgstRefunded'],'taxRefunded'=>$v['taxRefunded'],'taxRefundedProduct'=>$v['taxRefundedProduct'],'taxRefundedDiscount'=>$v['taxRefundedDiscount'],'taxRefundedShipping'=>$v['taxRefundedShipping'],'taxRefundedGiftWrap'=>$v['taxRefundedGiftWrap'],'salesTaxWithheld'=>$v['salesTaxWithheld'],'refundTaxWithheld'=>$v['refundTaxWithheld'],'tdsSection194ONet'=>$v['tdsSection194ONet'],'cgPriceTotal'=>$v['cgPriceTotal'],
                'cgPriceAbsTotal'=>$v['cgPriceTotal']??abs($v['cgPriceTotal']),'cgUnitPrice'=>$v['cgUnitPrice'],'proportionOfCg'=>$v['proportionOfCg'],'cgTransportCostsTotal'=>$v['cgTransportCostsTotal'],'cgTransportUnitCosts'=>$v['cgTransportUnitCosts'],'proportionOfCgTransport'=>$v['proportionOfCgTransport'],'totalCost'=>$v['totalCost'],'proportionOfTotalCost'=>$v['proportionOfTotalCost'],'cgOtherCostsTotal'=>$v['cgOtherCostsTotal'],'cgOtherUnitCosts'=>$v['cgOtherUnitCosts'],'proportionOfCgOtherCosts'=>$v['proportionOfCgOtherCosts'],'grossProfit'=>$v['grossProfit'],'grossRate'=>$v['grossRate'],'roi'=>$v['roi']
            ];
            $data4 = ['key1'=>$v['key1'],'key2'=>$v['key2'],'key3'=>$v['key3'],'key4'=>$v['key4'],'key5'=>$v['key5'],'key6'=>$v['key6'],'key7'=>$v['key7'],'key8'=>$v['key8'],'key9'=>$v['key9'],'key10'=>$v['key10'],'key11'=>$v['key11'],'key12'=>$v['key12'],'key13'=>$v['key13'],'key14'=>$v['key14'],'key18'=>$v['key18'],'key19'=>$v['key19'],'key20'=>$v['key20'],'key21'=>$v['key21']];
            $update_data = array_merge($data_,$data1,$data2,$data3,$data4);
            $db->table($this->table_name)->insert($update_data);
            $success_count++;
        }
        return ['success_count'=>$success_count,'error_list'=>$error_list];
    }
    //导入记录保存
    public function saveReportLog($error_list,$success_count,$error_count,$import_id) {
        $db = dbFMysql::getInstance();
        //记录导入数据
        $db->table('msku_report_import')
            ->where('where id=:id',['id'=>$import_id])
            ->update([
                'success_count'=>$success_count,
                'fail_count'=>$error_count,
            ]);
        if (count($error_list)) {
            $db->table('msku_report_import_error_log');
            foreach ($error_list as $error) {
                $db->insert([
                    'import_id'=>$import_id,
                    'data'=>json_encode($error['data'],JSON_UNESCAPED_UNICODE),
                    'error_reason'=>$error['error_reason'],
                    'row_num'=>$error['row_num'],
                ]);
            }
        }
    }
    //导出错误数据
    public static function exportErrorData($list) {
        $export_data = [];
        $import_key_list = array_flip(mskuReportModel::$import_key_list);
        foreach ($list as $v) {
            $data = json_decode($v['data'],true);
            $export_item = [];
            foreach ($data as $key=>$val) {
                if (isset($import_key_list[$key])) {
                    $export_item[$import_key_list[$key]] = $val;
                }
            }
            $export_item['失败原因'] = $v['error_reason'];
            $export_data[] = $export_item;
        }
        //保存
        $save_path = "/public_financial/temp/msku_report/error_data";
        $url = SELF_FK.$save_path;
        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path."/".date('YmdHis').uniqid().'.xlsx';
        $url = SELF_FK.$path;
        if (!file_exists($url)) {
            touch($url);
        }
        (new FastExcel($export_data))->export($url);
        return $path;
    }
    //导出店铺报告数据
    public function exportSellerReportData($list,$export_list) {
        //表头
        $new_data = [];
        //数据
        foreach ($list as $v) {
            $item=[];
            foreach ($v as $k=>$v1) {
                if (!in_array($k,$export_list)) {
                    continue;
                }
                if (!isset(mskuReportModel::$export_key_list[$k])) {
                    continue;
                }
                if ($k == 'categoryName') {
                    $categoryName = explode('/',$v1);
                    $item['一级分类'] = $categoryName[0];
                    $item['二级分类'] = $categoryName[1];
                    $item['三级分类'] = $categoryName[2];
                } else {
                    $value = $v1;
                    $item[mskuReportModel::$export_key_list[$k]] = $value;
                }
            }
            $new_data[] = $item;
        }
        //保存
        $save_path = "/public_financial/temp/msku_report/data";
        $url = SELF_FK.$save_path;
        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path."/".date('YmdHis').uniqid().'.xlsx';
        $url = SELF_FK.$path;
        if (!file_exists($url)) {
            touch($url);
        }
        (new FastExcel($new_data))->export($url);
        return $path;
    }
    //报告数据表头导出（模板）
    public static function reportHead() {
        $data[] = mskuReportModel::$import_key_list;
        //保存
        $save_path = "/public_financial/template";
        $url = SELF_FK.$save_path;
        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path.'/店铺数据导入模板.xlsx';
        $url = SELF_FK.$path;
        if (!file_exists($url)) {
            touch($url);
        }
        (new FastExcel($data))->export($url);
        dd($url);
    }
    //更加库存导入信息获取报告数据
    public function getReportDataByTag($project_id,$yunying_id,$currency_code,$asin,$sku) {
        $db = dbFMysql::getInstance();
        $data = $db->table($this->table_name)
            ->where('is_delete = 0')
            ->andWhere('project_id=:project_id and yunying_id=:yunying_id and currencyCode=:currencyCode and asin=:asin and localSku=:sku',['project_id'=>$project_id,'yunying_id'=>$yunying_id,'currencyCode'=>$currency_code,'asin'=>$asin,'sku'=>$sku]);
        return $data;
    }
    //计算列数据和
    public function getCount($param) {
        //获取列
        $export_key_list = array_keys(mskuReportModel::$export_key_list);
        $other_keys = [ "storeName", "supplier_name", "yunying", "msku", "asin", "parentAsin", "country", "localSku", "localName", "itemName", "principalRealname", "productDeveloperRealname", "categoryName", "brandName", "currencyCode", "listingTagIds",];
        $columns = array_diff($export_key_list,$other_keys);
        $column_list = [];
        foreach ($columns as $k=>$v) {
            $column_list[$k] = 'sum('.$v.') as '.$v;
        }
        $field_string = implode(",",$column_list);
        self::$list_type = 2;
        $list = $this->getList($param)
            ->field($field_string)
            ->one();
        if ($list['fbaSalesQuantity'] == '') {
            foreach ($columns as $key_) {
                $list[$key_] = 0;
            }
        }
        return $list;
    }










}