# 利润统计MSKU数据对接功能使用说明

## 功能概述

利润统计MSKU数据对接功能用于从领星API获取每日的销量、销售额等利润统计数据，并保存到本地数据库中，为FBA库存明细提供销量数据支持。

## 核心功能

### 1. 数据同步
- 每日自动同步前一天的利润统计数据
- 支持指定日期的数据同步
- 支持分页获取大量数据
- 自动处理数据去重和更新

### 2. 数据处理
- 完整的数据验证机制
- 支持100+字段的利润统计数据
- 自动计算汇总字段
- 错误处理和日志记录

### 3. 数据存储
- 保存到 `lingxing_profit_statistics_msku_2025` 表
- 支持upsert操作（插入或更新）
- 基于唯一键去重：dataDate + msku + asin + sid + countryCode

## API接口

### 同步利润统计数据

**接口地址：** `task/controller/lingXingApiController::synProfitMsku`

**请求方式：** POST

**请求参数：**
```json
{
    "date": "2025-07-02",    // 可选，数据日期，默认为前一天
    "offset": 0              // 可选，分页偏移量，默认为0
}
```

**响应示例：**
```json
{
    "code": 0,
    "message": "同步成功，处理 150 条数据",
    "data": {
        "offset": 500,
        "total": 1500,
        "success_count": 150,
        "error_count": 0,
        "has_more": true
    }
}
```

## 数据库表结构

### 主要字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| dataDate | date | 数据日期 |
| msku | varchar(100) | MSKU |
| asin | varchar(100) | ASIN |
| sid | varchar(50) | 店铺ID |
| countryCode | varchar(10) | 国家编码 |
| totalSalesQuantity | int | 总销量 |
| totalSalesAmount | decimal(12,2) | 总销售额 |
| fbaSalesQuantity | int | FBA销量 |
| fbmSalesQuantity | int | FBM销量 |
| totalAdsCost | decimal(10,2) | 广告费 |
| grossProfit | decimal(12,2) | 毛利润 |
| grossRate | decimal(12,4) | 毛利率 |

### 完整字段列表

表包含100+字段，涵盖：
- 基础信息：店铺、国家、产品信息
- 销量数据：FBA/FBM销量、退货量等
- 销售额数据：各类收入和退款
- 费用数据：平台费、广告费、仓储费等
- 税费数据：各类税收和退税
- 成本数据：采购成本、头程费用等
- 利润数据：毛利润、毛利率等

## 使用方法

### 1. 手动同步

```bash
# 同步前一天数据
curl -X POST "http://your-domain/task/controller/lingXingApiController/synProfitMsku" \
     -d "token=your-token"

# 同步指定日期数据
curl -X POST "http://your-domain/task/controller/lingXingApiController/synProfitMsku" \
     -d "token=your-token&date=2025-07-01"
```

### 2. 定时任务

建议设置每日凌晨自动执行：

```bash
# 添加到crontab
0 2 * * * /usr/bin/php /path/to/oa-api/task/controller/lingXingApiController.php synProfitMsku
```

### 3. 分页处理

对于大量数据，需要分页处理：

```php
$offset = 0;
$pageSize = 500;

do {
    $result = callApi('synProfitMsku', ['offset' => $offset]);
    $offset = $result['data']['offset'];
    $hasMore = $result['data']['has_more'];
} while ($hasMore);
```

## 数据查询

### 1. 基础查询

```php
use plugins\logistics\models\profitStatisticsMskuModel;

$model = new profitStatisticsMskuModel();

// 查询指定日期的数据
$data = $model->getProfitData('2025-07-02', [
    'msku' => 'YOUR-MSKU',
    'countryCode' => 'US'
]);
```

### 2. 统计查询

```php
// 获取销量统计
$stats = $model->getSalesStatistics('2025-07-01', '2025-07-02', [
    'msku' => 'YOUR-MSKU'
]);
```

## 错误处理

### 常见错误及解决方案

1. **API请求失败**
   - 检查网络连接
   - 验证API密钥和签名
   - 确认请求参数格式

2. **数据验证失败**
   - 检查必填字段：msku, asin, sid, countryCode
   - 验证数据格式和长度

3. **数据库操作失败**
   - 检查数据库连接
   - 验证表结构是否正确
   - 检查字段长度限制

### 日志查看

```bash
# 查看API请求日志
tail -f logs/lingXingApi_ProfitMsku.log

# 查看错误日志
tail -f logs/error.log
```

## 性能优化

### 1. 批量处理
- 每次处理500条数据
- 使用事务确保数据一致性
- 支持断点续传

### 2. 数据去重
- 基于唯一键自动去重
- 支持增量更新
- 避免重复数据

### 3. 内存管理
- 分批处理大量数据
- 及时释放内存
- 避免内存溢出

## 监控和维护

### 1. 数据完整性检查

```sql
-- 检查每日数据量
SELECT dataDate, COUNT(*) as count 
FROM lingxing_profit_statistics_msku_2025 
GROUP BY dataDate 
ORDER BY dataDate DESC;

-- 检查重复数据
SELECT dataDate, msku, asin, sid, countryCode, COUNT(*) as count
FROM lingxing_profit_statistics_msku_2025
GROUP BY dataDate, msku, asin, sid, countryCode
HAVING COUNT(*) > 1;
```

### 2. 性能监控

```sql
-- 检查表大小
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_name = 'lingxing_profit_statistics_msku_2025';
```

## 注意事项

1. **数据时效性**
   - 建议每日同步前一天数据
   - 避免重复同步相同日期数据

2. **API限制**
   - 注意API调用频率限制
   - 合理设置请求间隔

3. **数据备份**
   - 定期备份重要数据
   - 建立数据恢复机制

4. **版本兼容**
   - 注意领星API版本更新
   - 及时更新字段映射

## 联系支持

如有问题，请联系开发团队或查看相关文档。
