<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/2 10:02
 */

namespace financial\models;

use core\lib\db\dbFMysql;

class checkoutModel
{
    //同步数据验证
    public static function verifySyn(string $m_date) {
        $db = dbFMysql::getInstance();
        $checkout = $db->table('checkout')
            ->where('where m_date = :m_date',['m_date'=>$m_date])
            ->one();
        if ($checkout) {
            if ($checkout['is_lock'] == 1) {
                returnError("{$checkout['m_date']}月已结账，不可当前操作");
            }
            if ($checkout['is_lock'] == 2) {
                returnError("{$checkout['m_date']}月正在结帐中，不可当前操作");
            }
        }
    }
    //验证月份数据是否可以操作
    public static function verifyLock(array $m_date) {
        $db = dbFMysql::getInstance();
        $checkout = $db->table('checkout')
            ->whereIn('m_date',$m_date)
            ->one();
        if (!$checkout) {
            returnError("该月数据还未同步");
        } else {
            if ($checkout['is_lock'] == 1) {
                returnError("{$checkout['m_date']}月已结账，不可当前操作");
            }
            if ($checkout['is_lock'] == 2) {
                returnError("{$checkout['m_date']}月正在结帐中，不可当前操作");
            }
        }
    }
    //给脚本用的(结账完成的才能使用)
    public static function verifyLockShell(array $m_date) {
        $db = dbFMysql::getInstance();
        $checkout = $db->table('checkout')
            ->whereIn('m_date',$m_date)
            ->one();
        if (!$checkout) {
            SetReturn(2,"{$checkout['m_date']}该月数据还未同步");
        } else {
            if ($checkout['is_lock'] == 0) {
                SetReturn(2,"{$checkout['m_date']}月还没结账，不可当前操作");
            }
        }
    }
    //给脚本用的(未结账完成的才能使用)
    public static function verifyLockShellNo(array $m_date) {
        $db = dbFMysql::getInstance();
        $checkout = $db->table('checkout')
            ->whereIn('m_date',$m_date)
            ->one();
        if (!$checkout) {
            SetReturn(2,"{$checkout['m_date']}该月数据还未同步");
        } else {
            if ($checkout['is_lock'] != 0) {
                SetReturn(2,"{$checkout['m_date']}月已经结账，不可当前操作");
            }
        }
    }
}