<?php
/**
 * FBA发货单API接口测试 - 优化版本
 * @purpose 全面测试synInboundShipment接口功能，包含性能监控和详细分析
 * @Author: System
 * @Version: 2.0
 * @Time: 2025/06/23
 * @Optimized: 增强配置管理、错误处理、测试覆盖率和性能监控
 */

define('SELF_FK', realpath('')); //当前框架所在的根目录
//define('SELF_FK', $_SERVER['DOCUMENT_ROOT']); //当前框架所在的根目录
define('CORE', SELF_FK.'/core'); //核心文件目录
define('DEBUG', true); //调试模式设置
define('LOG_PATH', SELF_FK.'/log'); //调试模式设置
define('SYS_PUBLISH',false); //系统发版中

define('ROOT', SELF_FK."/");
require_once "environment.php"; //系统环境
require_once ROOT."/vendor/autoload.php";
include  CORE.'/common/function.php';
include  CORE.'/common/return.php';
include  CORE.'/common/phperror.php';
//方法请求
include CORE.'/fk.php';
//自动加载类
spl_autoload_register('\core\fk::load');

class InboundShipmentApiTest
{
    private $config;
    private $testResults = [];
    private $performanceData = [];
    private $logFile;
    private $debugMode;
    
    public function __construct()
    {
        $this->initializeConfig();
        $this->initializeLogging();
        $this->debugMode = $this->config['debug'] ?? false;
    }
    
    /**
     * 初始化配置
     */
    private function initializeConfig()
    {
        // 从环境变量或配置文件加载配置
        $this->config = [
            'base_url' => $_ENV['API_BASE_URL'] ?? 'http://localhost/oa-api',
            'token' => $_ENV['API_TOKEN'] ?? '01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0',
            'timeout' => (int)($_ENV['API_TIMEOUT'] ?? 30),
            'retry_times' => (int)($_ENV['API_RETRY_TIMES'] ?? 3),
            'debug' => filter_var($_ENV['DEBUG_MODE'] ?? false, FILTER_VALIDATE_BOOLEAN),
            'log_requests' => filter_var($_ENV['LOG_REQUESTS'] ?? true, FILTER_VALIDATE_BOOLEAN),
            'performance_mode' => filter_var($_ENV['PERFORMANCE_MODE'] ?? true, FILTER_VALIDATE_BOOLEAN)
        ];
        
        // 验证必要配置
        if (empty($this->config['base_url']) || empty($this->config['token'])) {
            throw new Exception('API基础配置不完整：需要base_url和token');
        }
    }
    
    /**
     * 初始化日志系统
     */
    private function initializeLogging()
    {
        $logDir = dirname(__DIR__) . '/logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        $this->logFile = $logDir . '/api_test_' . date('Y-m-d') . '.log';
    }
    
    /**
     * 写入日志
     */
    private function writeLog($level, $message, $context = [])
    {
        if (!$this->config['log_requests']) return;
        
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = !empty($context) ? ' | Context: ' . json_encode($context, JSON_UNESCAPED_UNICODE) : '';
        $logEntry = "[{$timestamp}] [{$level}] {$message}{$contextStr}\n";
        
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
        
        if ($this->debugMode) {
            echo "[DEBUG] {$logEntry}";
        }
    }
    
    /**
     * 记录性能数据
     */
    private function recordPerformance($testName, $responseTime, $success)
    {
        if (!$this->config['performance_mode']) return;
        
        $this->performanceData[] = [
            'test_name' => $testName,
            'response_time' => $responseTime,
            'success' => $success,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 记录测试结果
     */
    private function recordTestResult($testName, $passed, $message = '')
    {
        $this->testResults[] = [
            'test_name' => $testName,
            'passed' => $passed,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 测试发货单同步接口 - 增强版
     */
    public function testSynInboundShipment()
    {
        $testName = '发货单同步接口测试';
        echo "测试发货单同步接口...\n";
        $this->writeLog('INFO', "开始执行: $testName");
        
        $startTime = microtime(true);
        $url = $this->config['base_url'] . '/task/controller/lingXingApiController.php';
        
        $postData = [
            'token' => $this->config['token'],
            'action' => 'synInboundShipment',
            'offset' => 0,
            'length' => 10,
            'start_date' => date('Y-m-d', strtotime('-30 days')),
            'end_date' => date('Y-m-d'),
            'status' => 0, // 待发货
            'time_type' => 2 // 创建时间
        ];
        
        try {
            $response = $this->makeRequest($url, $postData);
            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000, 2);
            
            if ($response !== false) {
                $data = json_decode($response, true);
                
                // 记录性能数据
                $this->recordPerformance($testName, $responseTime, true);
                
                if ($data && isset($data['code'])) {
                    if ($data['code'] == 2) {
                        echo "✓ API调用成功: {$data['msg']} (响应时间: {$responseTime}ms)\n";
                        $this->writeLog('SUCCESS', "API调用成功", ['response_time' => $responseTime, 'message' => $data['msg']]);
                        
                        // 验证数据结构
                        if (isset($data['data']) && is_array($data['data'])) {
                            echo "  ✓ 数据结构验证通过\n";
                            $this->recordTestResult($testName, true, "API调用成功，数据结构正确");
                            return true;
                        } else {
                            echo "  ⚠ 数据结构异常，但API调用成功\n";
                            $this->recordTestResult($testName, true, "API调用成功，但数据结构需要检查");
                            return true;
                        }
                    } else {
                        echo "✗ API调用失败: {$data['msg']}\n";
                        $this->writeLog('ERROR', "API调用失败", ['message' => $data['msg']]);
                        $this->recordTestResult($testName, false, $data['msg']);
                        return false;
                    }
                } else {
                    echo "✗ API响应格式异常\n";
                    echo "响应内容: $response\n";
                    $this->writeLog('ERROR', "API响应格式异常", ['response' => substr($response, 0, 500)]);
                    $this->recordTestResult($testName, false, "响应格式异常");
                    return false;
                }
            } else {
                $this->recordPerformance($testName, $responseTime, false);
                echo "✗ API请求失败\n";
                $this->writeLog('ERROR', "API请求失败");
                $this->recordTestResult($testName, false, "请求失败");
                return false;
            }
            
        } catch (Exception $e) {
            echo "✗ API测试异常: " . $e->getMessage() . "\n";
            $this->writeLog('ERROR', "API测试异常", ['exception' => $e->getMessage()]);
            $this->recordTestResult($testName, false, $e->getMessage());
            return false;
        }
    }
    
    /**
     * 测试参数验证 - 增强版
     */
    public function testParameterValidation()
    {
        $testName = '参数验证测试';
        echo "测试参数验证...\n";
        $this->writeLog('INFO', "开始执行: $testName");
        
        $url = $this->config['base_url'] . '/task/controller/lingXingApiController.php';
        
        // 测试缺少token
        $postData = [
            'action' => 'synInboundShipment',
            'offset' => 0,
            'length' => 10
        ];
        
        try {
            $startTime = microtime(true);
            $response = $this->makeRequest($url, $postData);
            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000, 2);
            
            if ($response !== false) {
                $data = json_decode($response, true);
                
                if ($data && isset($data['msg']) && strpos($data['msg'], 'token验证失败') !== false) {
                    echo "✓ Token验证功能正常 (响应时间: {$responseTime}ms)\n";
                    $this->writeLog('SUCCESS', "Token验证功能正常", ['response_time' => $responseTime]);
                    $this->recordTestResult($testName, true, "Token验证功能正常");
                    $this->recordPerformance($testName, $responseTime, true);
                    return true;
                } else {
                    echo "✗ Token验证功能异常\n";
                    echo "响应内容: $response\n";
                    $this->writeLog('ERROR', "Token验证功能异常", ['response' => substr($response, 0, 500)]);
                    $this->recordTestResult($testName, false, "Token验证功能异常");
                    return false;
                }
            } else {
                echo "✗ 参数验证测试失败\n";
                $this->writeLog('ERROR', "参数验证测试请求失败");
                $this->recordTestResult($testName, false, "请求失败");
                return false;
            }
            
        } catch (Exception $e) {
            echo "✗ 参数验证测试异常: " . $e->getMessage() . "\n";
            $this->writeLog('ERROR', "参数验证测试异常", ['exception' => $e->getMessage()]);
            $this->recordTestResult($testName, false, $e->getMessage());
            return false;
        }
    }
    
    /**
     * 测试不同状态的查询 - 增强版
     */
    public function testDifferentStatus()
    {
        $testName = '不同状态查询测试';
        echo "测试不同状态查询...\n";
        $this->writeLog('INFO', "开始执行: $testName");
        
        $url = $this->config['base_url'] . '/task/controller/lingXingApiController.php';
        $statuses = [
            -1 => '待配货',
            0 => '待发货', 
            1 => '已发货',
            3 => '已作废'
        ];
        
        $allPassed = true;
        $successCount = 0;
        $totalTests = count($statuses);
        
        foreach ($statuses as $status => $statusName) {
            $postData = [
                'token' => $this->config['token'],
                'action' => 'synInboundShipment',
                'offset' => 0,
                'length' => 5,
                'status' => $status,
                'start_date' => date('Y-m-d', strtotime('-7 days')),
                'end_date' => date('Y-m-d')
            ];
            
            try {
                $startTime = microtime(true);
                $response = $this->makeRequest($url, $postData);
                $endTime = microtime(true);
                $responseTime = round(($endTime - $startTime) * 1000, 2);
                
                if ($response !== false) {
                    $data = json_decode($response, true);
                    
                    if ($data && isset($data['code']) && $data['code'] == 2) {
                        echo "  ✓ 状态 $status ($statusName) 查询成功 (响应时间: {$responseTime}ms)\n";
                        $this->writeLog('SUCCESS', "状态查询成功", ['status' => $status, 'status_name' => $statusName, 'response_time' => $responseTime]);
                        $this->recordPerformance("状态{$status}查询", $responseTime, true);
                        $successCount++;
                    } else {
                        echo "  ✗ 状态 $status ($statusName) 查询失败\n";
                        $this->writeLog('ERROR', "状态查询失败", ['status' => $status, 'status_name' => $statusName, 'response' => substr($response, 0, 200)]);
                        $this->recordPerformance("状态{$status}查询", $responseTime, false);
                        $allPassed = false;
                    }
                } else {
                    echo "  ✗ 状态 $status ($statusName) 请求失败\n";
                    $this->writeLog('ERROR', "状态查询请求失败", ['status' => $status, 'status_name' => $statusName]);
                    $allPassed = false;
                }
                
                // 避免请求过快
                usleep(500000); // 0.5秒
                
            } catch (Exception $e) {
                echo "  ✗ 状态 $status ($statusName) 测试异常: " . $e->getMessage() . "\n";
                $this->writeLog('ERROR', "状态查询测试异常", ['status' => $status, 'status_name' => $statusName, 'exception' => $e->getMessage()]);
                $allPassed = false;
            }
        }
        
        // 记录总体测试结果
        $message = "状态查询测试完成: {$successCount}/{$totalTests} 个状态测试通过";
        $this->recordTestResult($testName, $allPassed, $message);
        echo "  📊 状态查询测试统计: {$successCount}/{$totalTests} 通过\n";
        
        return $allPassed;
    }
    
    /**
     * 发送HTTP请求
     * @param string $url
     * @param array $postData
     * @return string|false
     */
    private function makeRequest($url, $postData)
    {
        $ch = curl_init();
        
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'User-Agent: InboundShipment-API-Test/1.0'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($error) {
            echo "CURL错误: $error\n";
            return false;
        }
        
        if ($httpCode !== 200) {
            echo "HTTP错误: $httpCode\n";
            return false;
        }
        
        return $response;
    }
    
    /**
     * 运行所有API测试 - 增强版
     */
    public function runAllTests()
    {
        $testStartTime = microtime(true);
        echo "=== FBA发货单API接口测试开始 ===\n";
        echo "📅 开始时间: " . date('Y-m-d H:i:s') . "\n";
        echo "🔧 配置信息: {$this->config['base_url']}\n";
        echo "📝 日志文件: {$this->logFile}\n\n";
        
        $this->writeLog('INFO', "API测试开始", ['config' => $this->config]);
        
        $tests = [
            'testParameterValidation' => '参数验证测试',
            'testSynInboundShipment' => '发货单同步接口测试',
            'testDifferentStatus' => '不同状态查询测试'
        ];
        
        $passedTests = 0;
        $totalTests = count($tests);
        
        foreach ($tests as $method => $description) {
            echo "--- $description ---\n";
            $testStart = microtime(true);
            $result = $this->$method();
            $testEnd = microtime(true);
            $testDuration = round(($testEnd - $testStart) * 1000, 2);
            
            if ($result) {
                $passedTests++;
                echo "✅ 测试完成，耗时: {$testDuration}ms\n\n";
            } else {
                echo "❌ 测试失败，耗时: {$testDuration}ms\n\n";
            }
        }
        
        $testEndTime = microtime(true);
        $totalDuration = round(($testEndTime - $testStartTime) * 1000, 2);
        
        echo "=== API测试完成 ===\n";
        echo "⏱️ 总耗时: {$totalDuration}ms\n";
        echo "📊 总测试数: $totalTests\n";
        echo "✅ 通过测试: $passedTests\n";
        echo "❌ 失败测试: " . ($totalTests - $passedTests) . "\n";
        echo "📈 通过率: " . round(($passedTests / $totalTests) * 100, 2) . "%\n\n";
        
        // 生成性能报告
        $this->generatePerformanceReport();
        
        // 生成测试结果报告
        $this->generateTestReport();
        
        if ($passedTests == $totalTests) {
            echo "🎉 所有API测试通过！\n";
            $this->writeLog('SUCCESS', "所有API测试通过", ['pass_rate' => '100%', 'total_duration' => $totalDuration]);
        } else {
            echo "❌ 部分API测试失败，请检查问题\n";
            $this->writeLog('WARNING', "部分API测试失败", [
                'passed' => $passedTests,
                'failed' => $totalTests - $passedTests,
                'pass_rate' => round(($passedTests / $totalTests) * 100, 2) . '%'
            ]);
        }
        
        return $passedTests == $totalTests;
    }
    
    /**
     * 生成性能报告
     */
    private function generatePerformanceReport()
    {
        if (empty($this->performanceData)) {
            return;
        }
        
        echo "🚀 === 性能统计报告 ===\n";
        
        $totalRequests = count($this->performanceData);
        $successfulRequests = array_filter($this->performanceData, function($item) {
            return $item['success'];
        });
        $responseTimes = array_column($this->performanceData, 'response_time');
        
        $avgResponseTime = round(array_sum($responseTimes) / count($responseTimes), 2);
        $minResponseTime = min($responseTimes);
        $maxResponseTime = max($responseTimes);
        $successRate = round(count($successfulRequests) / $totalRequests * 100, 2);
        
        echo "📈 总请求数: {$totalRequests}\n";
        echo "✅ 成功请求: " . count($successfulRequests) . " ({$successRate}%)\n";
        echo "⚡ 平均响应时间: {$avgResponseTime}ms\n";
        echo "🏃 最快响应: {$minResponseTime}ms\n";
        echo "🐌 最慢响应: {$maxResponseTime}ms\n\n";
        
        // 记录性能数据到日志
        $this->writeLog('PERFORMANCE', "性能统计完成", [
            'total_requests' => $totalRequests,
            'success_rate' => $successRate . '%',
            'avg_response_time' => $avgResponseTime . 'ms',
            'min_response_time' => $minResponseTime . 'ms',
            'max_response_time' => $maxResponseTime . 'ms'
        ]);
    }
    
    /**
     * 生成测试结果报告
     */
    private function generateTestReport()
    {
        if (empty($this->testResults)) {
            return;
        }
        
        echo "📋 === 测试结果详情 ===\n";
        
        foreach ($this->testResults as $result) {
            $status = $result['passed'] ? '✅ 通过' : '❌ 失败';
            echo "🔸 {$result['test_name']}: {$status}\n";
            if (!empty($result['message'])) {
                echo "   📝 说明: {$result['message']}\n";
            }
        }
        echo "\n";
        
        // 保存详细报告到文件
        $this->saveDetailedReport();
    }
    
    /**
     * 保存详细测试报告到文件
     */
    private function saveDetailedReport()
    {
        $reportDir = dirname(__DIR__) . '/reports';
        if (!is_dir($reportDir)) {
            mkdir($reportDir, 0755, true);
        }
        
        $reportFile = $reportDir . '/api_test_report_' . date('Y-m-d_H-i-s') . '.json';
        
        $report = [
            'test_time' => date('Y-m-d H:i:s'),
            'config' => $this->config,
            'test_results' => $this->testResults,
            'performance_data' => $this->performanceData,
            'summary' => [
                'total_tests' => count($this->testResults),
                'passed_tests' => count(array_filter($this->testResults, function($r) { return $r['passed']; })),
                'total_requests' => count($this->performanceData),
                'avg_response_time' => !empty($this->performanceData) ? 
                    round(array_sum(array_column($this->performanceData, 'response_time')) / count($this->performanceData), 2) : 0
            ]
        ];
        
        file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "📄 详细报告已保存: {$reportFile}\n\n";
        
        $this->writeLog('INFO', "测试报告已生成", ['report_file' => $reportFile]);
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new InboundShipmentApiTest();
    $test->runAllTests();
} else {
    echo "请在命令行环境下运行此测试\n";
}
