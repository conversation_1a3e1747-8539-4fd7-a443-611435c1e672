<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/26 15:54
 */

namespace financial\form;

use core\lib\db\dbFMysql;
use core\lib\ExceptionError;
use financial\common\importNeedDataBase;
use financial\models\goodsDataModel;
use Rap2hpoutre\FastExcel\FastExcel;

class goodsDataForm
{
    //列表查询需要返回的数据：0列表数据，1 base_id, 2$db(sql)
    public static int $list_type = 0;
    public string $table_name = '';
    public string $table_msku = '';
    public function __construct($date_time)
    {
        $year = date('Y',strtotime($date_time));
        goodsDataModel::creatGoodsDataTable($year);
        $this->table_name = 'goods_data_'.$year;
        $this->table_msku = 'msku_report_data_'.$year;
    }
    //获取列表数据
    public function getList($param) {
        $project_ids = !empty($param['project_ids'])?json_decode($param['project_ids']):[];
        $db = dbFMysql::getInstance();
        $date = date('Y-m',strtotime($param['date_time']));
        $db->table($this->table_name)
            ->where('where m_date=:m_date and is_delete = 0',['m_date'=>$date]);
        if ($param['is_error'] > -1) {
            if ($param['is_error'] == 0) {
                $db->andWhere('is_error = 0');
            } else {
                $db->andWhere('is_error > 0');
            }
        }
        if (count($project_ids)) {
            $db->whereIn('project_id',$project_ids);
        }
        if ($param['search_type'] > 0 && !empty($param['search_value'])) {
            if ($param['search_type'] == 'goods_name') {
                $db->andWhere('goods_name like :goods_name',['goods_name'=>"%{$param['search_value']}%"]);
            } elseif ($param['search_type'] == 'asin') {
                $db->andWhere('asin like :asin',['asin'=>"%{$param['search_value']}%"]);
            } elseif ($param['search_type'] == 'sku') {
                $db->andWhere('sku like :sku',['sku'=>"%{$param['search_value']}%"]);
            }
        }
        //国家
        if (!empty($param['country_code'])) {
            $db->andWhere('country_code = :country_code',['country_code'=>$param['country_code']]);
        }
        //导入时间
        if (!empty($param['import_time']) && $param['import_time'] != '[]') {
            $import_time = json_decode($param['import_time']);
            $db->andWhere('created_time >= :begin_time and created_time <= :end_time',['begin_time'=>$import_time[0],'end_time'=>$import_time[1]]);
        }
        if (self::$list_type == 1) {
            $list = $db->field('id')
                ->andWhere('import_id > 0')
                ->field('id')
                ->list();
            return $list;
        } elseif (self::$list_type == 2) {
            return $db;
        } else {
            //导出时有ids就按此导出
            if (isset($param['ids'])) {
                $db->whereIn('id',$param['ids']);
            }
            $data = $db->order('id desc')
                ->field('*,created_time as import_time')
                ->pages($param['page'],$param['page_size']);
            return $data;
        }

    }
    //删除数据-勾选数据
    public function delById($ids) {
        $db = dbFMysql::getInstance();
        $db->beginTransaction();
        try {
            $data_list = $db->table($this->table_name)
                ->whereIn('id',$ids)
                ->field('base_id,key1,key7,key9,key21,key20')
                ->list();
            $base_ids = array_column($data_list,'base_id');
            $data_ = [];
            foreach ($data_list as $v) {
                $data_[$v['base_id']] = $v;
            }
            $msku_data_list = $db->table($this->table_msku)
                ->whereIn('id',$base_ids)
                ->field('id,key1,key7,key9,key21,key20')
                ->list();
            //删除数据
            $db->table($this->table_name)
                ->whereIn('id',$ids)
                ->update(['is_delete'=>1,'updated_time'=>date('Y-m-d H:i:s')]);
            //清理报告数据
            foreach ($msku_data_list as $v) {
                $import_data = $data_['id'];
                $db->table($this->table_msku)
                    ->whereIn('id',$v['base_id'])
                    ->update([
                        'key1'=>$v['key1'] > $import_data['key1']?($v['key1']*100 - $import_data['key1']*100)/100:0,
                        'key7'=>$v['key7'] > $import_data['key7']?($v['key7']*100 - $import_data['key7']*100)/100:0,
                        'key9'=>$v['key9'] > $import_data['key9']?($v['key9']*100 - $import_data['key9']*100)/100:0,
                        'key20'=>$v['key20'] > $import_data['key20']?($v['key20']*100 - $import_data['key20']*100)/100:0,
                        'key21'=>$v['key21'] > $import_data['key21']?($v['key21']*100 - $import_data['key21']*100)/100:0,
                    ]);
            }
            $db->commit();
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //删除数据-通过查询
    public function delByMdata($m_date) {
        $db = dbFMysql::getInstance();
        $db->beginTransaction();
        try {
            $data_list = $db->table($this->table_name)
                ->where('m_date = :m_date and is_delete = 0',['m_date'=>$m_date])
                ->field('base_id,key1,key7,key9,key21,key20')
                ->list();
            $base_ids = array_column($data_list,'base_id');
            $data_ = [];
            foreach ($data_list as $v) {
                $data_[$v['base_id']] = $v;
            }
            $msku_data_list = $db->table($this->table_msku)
                ->whereIn('id',$base_ids)
                ->field('id,key1,key7,key9,key21,key20')
                ->list();
            //删除数据
            $db->table($this->table_name)
                ->where('m_date = :m_date',['m_date'=>$m_date])
                ->update(['is_delete'=>1,'updated_time'=>date('Y-m-d H:i:s')]);
            //清理报告数据
            foreach ($msku_data_list as $v) {
                $import_data = $data_[$v['id']];
                $db->table($this->table_msku)
                    ->where('id = :id',['id'=>$import_data['base_id']])
                    ->update([
                        'key1'=>$v['key1'] > $import_data['key1']?($v['key1']*100 - $import_data['key1']*100)/100:0,
                        'key7'=>$v['key7'] > $import_data['key7']?($v['key7']*100 - $import_data['key7']*100)/100:0,
                        'key9'=>$v['key9'] > $import_data['key9']?($v['key9']*100 - $import_data['key9']*100)/100:0,
                        'key20'=>$v['key20'] > $import_data['key20']?($v['key20']*100 - $import_data['key20']*100)/100:0,
                        'key21'=>$v['key21'] > $import_data['key21']?($v['key21']*100 - $import_data['key21']*100)/100:0,
                    ]);
            }
            $db->commit();
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //导入数据
    public function importGoodsData($list,$date,$import_id,$export_data) {
        $year = date('Y',strtotime($date.'-01'));
        //国家数据
        $country_name = array_column($list,'country');
        $country_ = importNeedDataBase::getCountryListByName($country_name);
        //运营
        $yunying_name = array_column($list,'yunying');
        $qwuser_ = importNeedDataBase::qwuserByNameList($yunying_name);
        //项目
        $project_ = importNeedDataBase::getProjectAll();
        //店铺
        $store_name = array_column($list,'store_name');
        $store_ = importNeedDataBase::getSellerByName($store_name);
        //保存数据
        $success_count = 0;
        $error_list = [];
        $db = dbFMysql::getInstance();
        $db->beginTransaction();
        foreach ($list as $k=>$v) {
            $row_num = $k+1;
            //验证字段内容
            if (empty($v['country']) || empty($v['store_name']) || empty($v['project_name']) || empty($v['yunying']) || empty($v['asin']) || empty($v['p_asin']) || empty($v['sku']) || empty($v['msku'])) {
                $error_list[] = ['error_reason'=>'运营部门|运营人员|国家|MSKU|ASIN|父ASIN|仓库SKU|店铺，不能为空','data'=>$v,'row_num'=>$row_num];
                continue;
            }
            //运营
//            if (!isset($qwuser_[$v['yunying']])) {
//                $error_list[] = ['error_reason'=>'未查询到该运营人员','data'=>$v,'row_num'=>$row_num];
//                continue;
//            } else {
//                $yunying_id = $qwuser_[$v['yunying']];
//            }
            //国家
            if (!isset($country_[$v['country']])) {
                $error_list[] = ['error_reason'=>'未查询到国家名','data'=>$v,'row_num'=>$row_num];
                continue;
            } else {
                $countryCode = $country_[$v['country']]['code'];
            }
            //店铺
            if (!isset($store_[$v['store_name']])) {
                $error_list[] = ['error_reason'=>'未查询到店铺','data'=>$v,'row_num'=>$row_num];
                continue;
            } else {
                $sid = $store_[$v['store_name']]['sid'];
            }
            $yunying_id = $qwuser_[$v['yunying']]??0;//运营
            if (empty($project_[$v['project_name']])) {
                $error_list[] = ['error_reason'=>'未查询到项目','data'=>$v,'row_num'=>$row_num];
                continue;
            } else {
                $project_id = $project_[$v['project_name']]['id'];
            }
//            //项目及项目中的负责人
//            if (!isset($project_[$v['project_name']])) {
//                $error_list[] = ['error_reason'=>'未查询到项目','data'=>$v,'row_num'=>$row_num];
//                continue;
//            } else {
//                $project_id = $project_[$v['project_name']]['id'];
//                $p_user_ids = $project_[$v['project_name']]['user_ids'];
//                if (!count($p_user_ids)) {
//                    $error_list[] = ['error_reason'=>'项目中未设置运营','data'=>$v,'row_num'=>$row_num];
//                    continue;
//                }
//                if (!in_array($yunying_id,$p_user_ids)) {
//                    $error_list[] = ['error_reason'=>'项目中不存在该运营','data'=>$v,'row_num'=>$row_num];
//                    continue;
//                }
//            }
            /**部门验证（listing验证）
            $listing_where = [
                'm_date'=>$date,
                'sid'=>$sid,
                'marketplace'=>$v['country'],
                'seller_sku'=>$v['msku'],
                'asin'=>$v['asin'],
                'parent_asin'=>$v['p_asin'],
                'local_sku'=>$v['sku'],
            ];
            $listing_data = $db->table('project_listing_'.$year)
                ->where('where m_date=:m_date and sid=:sid and marketplace=:marketplace and seller_sku=:seller_sku and asin=:asin and parent_asin=:parent_asin and local_sku=:local_sku',$listing_where)->one();
            if (!$listing_data) {
                $error_list[] = ['error_reason'=>'未匹配到项目','data'=>$v,'row_num'=>$row_num];
                continue;
            }
            $project_id = $listing_data['project_id'];
            $yunying_id = $listing_data['yunying_id'];
             * */
            //查msku表中是否有数据
            $msku_data = $db->table($this->table_msku)
                ->where('where is_delete=0 and yunying_id=:yunying_id and project_id=:project_id and countryCode=:country_code and asin=:asin and parentAsin=:parentAsin and localSku=:sku and sid=:sid and reportDateMonth=:m_date and msku=:msku',['m_date'=>$date,'yunying_id'=>$yunying_id,'project_id'=>$project_id,'country_code'=>$countryCode,'asin'=>$v['asin'],'sku'=>$v['sku'],'sid'=>$sid,'parentAsin'=>$v['p_asin'],'msku'=>$v['msku']])
                ->field('id,key1,key7,key9,key20,key21')
                ->order('id asc')
                ->one();
            if(!$msku_data) {
                $error_list[] = ['error_reason'=>'未找到对应musk报表数据','data'=>$v,'row_num'=>$row_num];
                continue;
            }
            $goods_data_info = $db->table($this->table_name)
                ->where('where is_delete=0 and yunying=:yunying and country=:country and asin=:asin and sku=:sku and store_name=:store_name and p_asin=:p_asin and m_date=:m_date and msku=:msku',['yunying'=>$v['yunying'],'country'=>$v['country'],'asin'=>$v['asin'],'sku'=>$v['sku'],'store_name'=>$v['store_name'],'p_asin'=>$v['p_asin'],'m_date'=>$date,'msku'=>$v['msku']])
                ->field('id,base_id,key1,key7,key9,key20,key21')
                ->one();

            if ($goods_data_info) {
                $base_id = $goods_data_info['base_id'];
                $update_data = [
                    'is_error'=>0,
                    'is_delete'=>0,
                    'import_id'=>$import_id,
                    'project_id'=>$project_id,
                    'yunying_id'=>$yunying_id,
                    'country_code'=>$countryCode,
                    'yunying'=>$v['yunying'],
                    'country'=>$v['country'],
                    'goods_name'=>$v['goods_name'],
                    'key1'=>($v['key1']*100+$goods_data_info['key1']*100)/100,
                    'key7'=>($v['key7']*100+$goods_data_info['key7']*100)/100,
                    'key9'=>($v['key9']*100+$goods_data_info['key9']*100)/100,
                    'key20'=>($v['key20']*100+$goods_data_info['key20']*100)/100,
                    'key21'=>($v['key21']*100+$goods_data_info['key21']*100)/100,
                    'updated_time'=>date('Y-m-d H:i:s'),
                    'row_num'=>$row_num
                ];
                $db->table($this->table_name)->where('where id=:id',['id'=>$goods_data_info['id']])
                    ->update($update_data);
            } else {
                $base_id = $msku_data['base_id'];
                $update_data = [
                    'm_date'=>$date,
                    'import_id'=>$import_id,
                    'base_id'=>$msku_data['id'],
                    'is_error'=>0,
                    'project_name'=>$v['project_name'],
                    'project_id'=>$project_id,
                    'yunying_id'=>$yunying_id,
                    'yunying'=>$v['yunying'],
                    'country_code'=>$countryCode,
                    'country'=>$v['country'],
                    'msku'=>$v['msku'],
                    'asin'=>$v['asin'],
                    'p_asin'=>$v['p_asin'],
                    'sku'=>$v['sku'],
                    'goods_name'=>$v['goods_name'],
                    'key1'=>floatval($v['key1']),
                    'key9'=>floatval($v['key9']),
                    'key7'=>floatval($v['key7']),
                    'key20'=>floatval($v['key20']),
                    'key21'=>floatval($v['key21']),
                    'created_time'=>date('Y-m-d H:i:s'),
                    'store_name'=>$v['store_name'],
                    'sid'=>$sid,
                    'row_num'=>$row_num
                ];
                $db->table($this->table_name)
                    ->insert($update_data);
            }
            //保存数据到同步数据
            $db->table($this->table_msku)
                ->where('where id=:id',['id'=>$base_id])
                ->update([
                    'key1'=>$update_data['key1'],
                    'key7'=>$update_data['key7'],
                    'key9'=>$update_data['key9'],
                    'key20'=>$update_data['key20'],
                    'key21'=>$update_data['key21']
                ]);
            $success_count++;
        }
        if (count($error_list)) {
            $this->saveImportFailData($error_list,$date,$import_id);
        }
        $success_count = $export_data['success_count'] + $success_count;
        $error_count = $export_data['error_count'] + count($error_list);
        //保存进度
        $this->setImportProgress($import_id,$success_count,$error_count);
        $db->commit();
        return ['success_count'=>$success_count,'error_count'=>$error_count];
    }
    //保存导入失败的数据
    public function saveImportFailData($error_list,$date,$import_id) {
        /**
         *  "站内数据调整"=>"key20",
        "调整的数量"=>"key21",
        "调整的尾程成本"=>"key9",
        "调整的采购成本"=>"key1",
        "调整的头程成本"=>"key7",
         */
        $db = dbFMysql::getInstance();
        foreach ($error_list as $error) {
            $v = $error['data'];
            $goods_data_info = $db->table($this->table_name)
                ->where('where is_delete=0 and yunying=:yunying and country=:country and asin=:asin and sku=:sku and store_name=:store_name and p_asin=:p_asin and m_date=:m_date and msku=:msku',['yunying'=>$v['yunying'],'country'=>$v['country'],'asin'=>$v['asin'],'sku'=>$v['sku'],'store_name'=>$v['store_name'],'p_asin'=>$v['p_asin'],'m_date'=>$date,'msku'=>$v['msku']])
                ->field('id,key1,key7,key9,key20,key21')
                ->one();
            if ($goods_data_info) {
                $db->table($this->table_name)->where('where id=:id',['id'=>$goods_data_info['id']])
                    ->update([
                        'key1'=>floatval($v['key1']+$goods_data_info['key1']),
                        'key7'=>floatval($v['key7']+$goods_data_info['key7']),
                        'key9'=>floatval($v['key9']+$goods_data_info['key9']),
                        'key20'=>floatval($v['key20']+$goods_data_info['key20']),
                        'key21'=>floatval($v['key21']+$goods_data_info['key21']),
                        'updated_time'=>date('Y-m-d H:i:s')
                    ]);
            } else {
                $db->table($this->table_name)
                    ->insert([
                        'is_error'=>1,
                        'm_date'=>$date,
                        'import_id'=>$import_id,
                        'project_name'=>$v['project_name'],
                        'yunying'=>$v['yunying'],
                        'country'=>$v['country'],
                        'msku'=>$v['msku'],
                        'asin'=>$v['asin'],
                        'sku'=>$v['sku'],
                        'p_asin'=>$v['p_asin'],
                        'store_name'=>$v['store_name'],
                        'goods_name'=>$v['goods_name'],
                        'key1'=>floatval($v['key1']),
                        'key7'=>floatval($v['key7']),
                        'key9'=>floatval($v['key9']),
                        'key20'=>floatval($v['key20']),
                        'key21'=>floatval($v['key21']),
                        'created_time'=>date('Y-m-d H:i:s'),
                    ]);
            }
            $db->table('goods_data_import_error_log')
                ->insert([
                    'import_id'=>$import_id,
                    'data'=>json_encode($v,JSON_UNESCAPED_UNICODE),
                    'error_reason'=>$error['error_reason'],
                    'row_num'=>$error['row_num']
                ]);
        }
    }


    //获取到入异常列表
    public static function getImportErrorList($import_id,$page,$page_size) {
        $db = dbFMysql::getInstance();
        $data = $db->table('goods_data_import_error_log')
            ->where('where import_id=:import_id',['import_id'=>$import_id])
            ->field('id,row_num,error_reason')
            ->pages($page,$page_size);
        return $data;
    }
    //获取导入导出历史数据中的是否有异常状态
    public static function getLogErrorList($list) {
        $import_id_list = array_column($list,'id');
        $db = dbFMysql::getInstance();
        $error_list = $db->table('goods_data_import_error_log')
            ->whereIn('import_id',$import_id_list)
            ->groupBy(['import_id'])
            ->field('MIN(id) AS id,import_id')
            ->list();
        foreach ($list as &$v) {
            $v['has_error'] = 0;
            foreach ($error_list as $v1) {
                if ($v1['import_id'] == $v['id']) {
                    $v['has_error'] = 1;
                }
            }
        }
        return $list;
    }
    //导出-导入失败数据
    public static function exportImportErrorData($import_id,$page,$page_size) {
        $db = dbFMysql::getInstance();
        $data = $db->table('goods_data_import_error_log')
            ->where('where import_id=:import_id',['import_id'=>$import_id])
            ->order('id desc')
            ->pages($page,$page_size);
        $list = $data['list'];
        if (count($list)) {
            //导出
            $export_data = [];
            $import_key_list = array_flip(goodsDataModel::$import_key_list);
            foreach ($list as $v) {
                $data = json_decode($v['data'],true);
                $export_item = [];
                foreach ($data as $key=>$val) {
                    if (isset($import_key_list[$key])) {
                        $export_item[$import_key_list[$key]] = $val;
                    }
                }
                $export_item['失败原因'] = $v['error_reason'];
                $export_item['行号'] = $v['row_num'];
                $export_data[] = $export_item;
            }
            //保存
            $save_path = "/public_financial/temp/goods_data/error_data";
            $url = SELF_FK.$save_path;
            if (!file_exists($url)) {
                mkdir($url, 0777, true);
            }
            $path = $save_path."/".date('YmdHis').uniqid().'.xlsx';
            $url = SELF_FK.$path;
            if (!file_exists($url)) {
                touch($url);
            }
            (new FastExcel($export_data))->export($url);
            return ['count'=>count($list),'excel_path'=>$path];
        } else {
            return ['count'=>0,'excel_path'=>''];
        }

    }
    //导出库存数据
    public function exportData($list,$export_list) {
        //表头
        $new_data = [];
        //数据
        foreach ($list as $v) {
            $item=[];
            foreach ($v as $k=>$v1) {
                if (!in_array($k,$export_list)) {
                    continue;
                }
                if (!isset(goodsDataModel::$export_key_list[$k])) {
                    continue;
                }
                $value = $v1;
                $item[goodsDataModel::$export_key_list[$k]] = $value;
            }
            $item['状态'] = $v['is_error'] > 0?"失败":'成功';
            $new_data[] = $item;
        }
        //保存
        $save_path = "/public_financial/temp/goods_data/data";
        $url = SELF_FK.$save_path;
        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path."/".date('YmdHis').uniqid().'.xlsx';
        $url = SELF_FK.$path;
        if (!file_exists($url)) {
            touch($url);
        }
        (new FastExcel($new_data))->export($url);
        return $path;
    }
    //修改数据
    public function editData($param) {
        $m_date = $param['m_date'];
        $db = dbFMysql::getInstance();
        //查询sid
        $seller = $db->table('seller')
            ->where('where real_name=:real_name and is_delete = 0',['real_name'=>$param['store_name']])
            ->field('sid')
            ->one();
        if (!$seller) {
            returnError('该店铺不存在');
        } else {
            $param['sid'] = $seller['sid'];
        }
        //项目id的查询
        $project_id = $param['project_id'];
        $project_3 = $db->table('project')
            ->where('where id =:project_id',['project_id'=>$project_id])
            ->field('p_id,project_name')
            ->one();
        $project_2 = $db->table('project')
            ->where('where id =:project_id',['project_id'=>$project_3['p_id']])
            ->field('id,p_id,project_name')
            ->one();
        $project_1 = $db->table('project')
            ->where('where id =:project_id',['project_id'=>$project_2['p_id']])
            ->field('project_name')
            ->one();
        if (!$project_1 || !$project_2 || !$project_3) {
            returnError('项目异常');
        }
        $project_name = $project_1['project_name'].'/'.$project_2['project_name'].$project_3['project_name'];
        $param['project_name'] = $project_name;
        //重复查询
        $goods_data_info = $db->table($this->table_name)
            ->where('where is_delete=0 and yunying=:yunying and project_name=:project_name and country=:country and asin=:asin and sku=:sku and store_name=:store_name and p_asin=:p_asin and m_date=:m_date',['yunying'=>$param['yunying_name'],'project_name'=>$param['project_name'],'country'=>$param['country'],'asin'=>$param['asin'],'sku'=>$param['sku'],'store_name'=>$param['store_name'],'p_asin'=>$param['p_asin'],'m_date'=>$m_date])
            ->field('id,key1,key7,key9,key20,key21')
            ->one();
        if ($goods_data_info && $goods_data_info['id']!=$param['id']) {
            returnError('该数据已存在');
        }
        //查询该数据
        $data = $db->table($this->table_name)
            ->where('where id=:id',['id'=>$param['id']])
            ->andWhere('is_delete = 0')
           ->field('base_id')
            ->one();
        if (!$data) {
            returnError('未查询到数据');
        }
        //查询msku报表数据
        $msku_param = ['yunying_id'=>$param['yunying_id'],'project_id'=>$param['project_id'],'country_code'=>$param['country_code'],'asin'=>$param['asin'],'sku'=>$param['sku'],'sid'=>$param['sid'],'parentAsin'=>$param['p_asin'],'m_date'=>$m_date];
        $msku_data = $db->table($this->table_msku)
            ->where('where is_delete=0 and yunying_id=:yunying_id and project_id=:project_id and countryCode=:country_code and asin=:asin and parentAsin=:parentAsin and localSku=:sku and sid=:sid and reportDateMonth=:m_date',$msku_param)
            ->field('id')
            ->order('id asc')
            ->one();
        if (!$msku_data) {
            returnError('未找到对应musk报表数据');
        }
        $db->beginTransaction();
        try {
            //数据修改
            $db->table($this->table_name)
                ->where('where id=:id',['id'=>$param['id']])
                ->update([
                    'is_error'=>0,
                    'base_id'=>$msku_data['id'],
                    'project_name'=>$param['project_name'],
                    'yunying'=>$param['yunying_name'],
                    'yunying_id'=>$param['yunying_id'],
                    'project_id'=>$param['project_id'],
                    'country_code'=>$param['country_code'],
                    'country'=>$param['country'],
                    'asin'=>$param['asin'],
                    'p_asin'=>$param['p_asin'],
                    'sku'=>$param['sku'],
                    'goods_name'=>$param['goods_name'],
                    'sid'=>$param['sid'],
                    'store_name'=>$param['store_name'],
                    'key1'=>$param['key1'],
                    'key7'=>$param['key7'],
                    'key9'=>$param['key9'],
                    'key21'=>$param['key21'],
                    'key20'=>$param['key20'],
                    'updated_time'=>date('Y-m-d H:i:s')
                ]);
            //msku书修改
            $db->table($this->table_msku)
                ->where('where id = :base_id',['base_id'=>$msku_data['id']])
                ->update([
                    'key1'=>$param['key1'],
                    'key9'=>$param['key9'],
                    'key20'=>$param['key20'],
                    'key21'=>$param['key21'],
                    'key7'=>$param['key7'],
                ]);
            $db->commit();
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //保存导入的进度
    public function setImportProgress($import_id,$success_count,$error_count) {
        $db = dbFMysql::getInstance();
        $db->table('goods_data_import')
            ->where('where id=:id',['id'=>$import_id])
            ->update(['success_count'=>$success_count,'fail_count'=>$error_count]);
    }
    //计算列数据和
    public function getCount($param) {
        //获取列
        $column_list = ['key1','key7','key9','key20','key21'];
        foreach ($column_list as $k=>$v) {
            $column_list[$k] = 'sum('.$v.') as '.$v;
        }
        $field_string = implode(",",$column_list);
        self::$list_type = 2;
        $list = $this->getList($param)
            ->field($field_string)
            ->one();
        if ($list['key1'] == '') {
            $list = [
                'key1'=>0,
                'key7'=>0,
                'key9'=>0,
                'key20'=>0,
                'key21'=>0,
            ];
        }
        return $list;

    }
}