<?php

namespace  admin\models;

use core\lib\db\dbMysql;

class qwdepartmentModel
{
    public static function getUserDepartment($user_id, $limit_leader = true)
    {
        // 获取部门下的用户
        $db = dbMysql::getInstance();
        $db->table('qwdepartment')
            ->field('id,wp_id,name,department_leader,qw_parentid,sort');
        $deps = $db->list();

        $departments = array_column($deps, null, 'wp_id');
        $departmentTree = buildTree($deps, $deps[0]['qw_parentid'], 'wp_id', 'qw_parentid');
        // 构建部门树

        $db->table('qwuser')
            ->field('id,wid,wname,avatar,wdepartment_ids');
        $users = $db->list();
        $user_map = array_column($users, null, 'wid');


        $user_deps = []; // 用户管理的部门
        $user_deps_users = []; //用户管理的部门下的用户
        $all_user_deps_users = []; //用户管理的部门及其子部门下的用户

        if (!$limit_leader) {
            // 如果不限制部门领导，则直接返回用户所在的部门
            $user_department_ids = json_decode($user_map[$user_id]['wdepartment_ids'], true);
            foreach ($user_department_ids as $user_department_id) {
                $user_deps[$user_department_id] = $departments[$user_department_id];
            }
        } else {
            foreach ($departments as $d_id => $val) {
                $val['department_leader'] = json_decode($val['department_leader']);
                if (in_array($user_id, $val['department_leader'])) {
                    $user_deps[$d_id] = $val;
                }
            }

            // 如果用户没有管理部门，则只返回用户自己
            if (empty($user_deps)) {
                return [
                    'departments' => [],
                    'department_users' => [$user_map[$user_id]],
                    'all_department_ids' => [],
                    'all_department_users' => [$user_map[$user_id]],
                ];
            }
        }

        // 获取用户管理的部门及其子部门
        $all_department_ids = [];
        foreach ($user_deps as $d_id => $department) {
            $all_department_ids = array_merge($all_department_ids, findAllChildren($departmentTree, $d_id, 'wp_id'));
        }
        // 去重
        $all_department_ids = array_values(array_unique($all_department_ids));
        $all_departments = array_intersect_key($departments, array_flip($all_department_ids));


        foreach ($users as $user) {
            $user_wdepartment_ids = json_decode($user['wdepartment_ids']);
            if(empty($user_wdepartment_ids)) continue;
            foreach ($user_wdepartment_ids as $d_id) {
                if (isset($user_deps[$d_id])) {
                    $user_deps[$d_id]['users'][] = $user;
                    $user_deps_users[] = $user;
                }
                if (isset($all_departments[$d_id])) {
                    $all_departments[$d_id]['users'][] = $user;
                    $all_user_deps_users[] = $user;
                }
            }
        }

        return [
            'departments' => array_values($user_deps), // 直接管理的部门
            'department_users' => $user_deps_users, // 直接管理的部门下的用户
            'all_department_ids' => array_values($all_departments), // 管理的部门及其子部门
            'all_department_users' => $all_user_deps_users, // 管理的部门及其子部门下的用户
        ];
    }

}