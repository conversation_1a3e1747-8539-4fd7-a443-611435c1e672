<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/13 13:37
 */

namespace core\lib\app_auth\plugins;

use plugins\goods\form\qwuserFrom;
use plugins\goods\models\userModel;

class authGoods
{
    //公用方法（不用验证权限的接口）
    static array $common_auth = [
        'goods/common/patternList', //字典获取
        'goods/role/getAuthList',  //获取权限列表
        'goods/user/getList',
        //用户
        'goods/user/getUserInfo',
        //消息
        'goods/form/getList',//项目表单
        'goods/project/getProjectAttr',//获取项目规格书,
        'goods/goodsNew/bindGoodsProject',//给新品绑定模板（后续根据创建人设置权限）
        //待办事项
        'goods/goodsMatters/getList',//获取待办事项
        'goods/goodsMatters/getCountList',//获取待办事项
        'goods/goodsMatters/submitMatters',//单个提交待办事项
        //产品入库
        'goods/goodsReceiving/getReceivingToToday',//获取今日入库数量+待入库列表
        'goods/goodsReceiving/getReceivingCode',//获取样品编号
        //获取图片上传列表
        'goods/imgsRequest/getImages', //获取图片需求上传的文件
        'goods/instructionsRequest/getImages',//获取说明书上传文件
        'goods/takePictureRequest/getImages',//获取拍摄图片
        //硬件检测报告下载
        'goods/goodsHardwareTest/downloadHardwarePdf',
        'goods/goodsHardwareTest/submitAfterProject',//补充提交检测报告
        //根据角色获取用户
        'goods/user/getUserByRole',
        //获取产品
        'goods/config/getConfig',
        //产品
        'goods/goodsNew/getList',//查看产品列表
        'goods/goodsNew/getCreatedList',//已创建的产品列表
        //供应商
        'goods/goodsSupplier/getList',//供应商列表
        //分类
        'goods/goodsCate/getList',//分类列表
        //查看功能列表
        'goods/goodsFunction/getList',//查看功能列表
        //颜色
        'goods/goodsColor/getList',//颜色列表
        //角色
        'goods/role/getList',//角色列表
        //获取问题记录
        'goods/goodsNew/getGoodsAbnormaLog',
        'goods/goodsProject/getDetail',//项目详
        //图片需求
        'goods/imgsRequest/imgTestDetail',//图片测试详情
        //保存用户列配置
        'goods/imgsRequest/saveUserColumn',
        //获取用户列配置
        'goods/imgsRequest/getUserColumn',
        //获取终线设置信息(图片审核配置)
        'goods/imageOwnerConfig/getFinalLineCurrent',
        //获取终线设置信息(SOP审核配置)
        'goods/sopReviewer/getFinalLineCurrent',
        //组名列表
        'goods/imgsRequest/getGroupList',
        //获取产品列表
        'goods/imgsRequest/getProductListWithCategoryAndColor',
        //获取品牌列表
        'goods/imgsRequest/getBrandList',
        //国家列表
        'goods/imgsRequest/getCountryList'
    ];
    static array $auth_list = [
        [
            'name'=>'角色管理',
            'key'=>'role',
            'child'=>[
                [
                    'name'=>'查看角色列表',
                    'key'=>'goods/role/getList',
                ],
                [
                    'name'=>'查看角色',
                    'key'=>'goods/role/getDetail',
                ],
                [
                    'name'=>'添加、修改角色',
                    'key'=>'goods/role/editRole',
                ]
            ]
        ],
        [
            'name'=>'成员管理',
            'key'=>'user',
            'child'=>[
                [
                    'name'=>'查看部门列表',
                    'key'=>'goods/qwPartment/getList',
                ],
                [
                    'name'=>'查看成员列表',
                    'key'=>'goods/user/getList',
                ],
                [
                    'name'=>'查看成员详情',
                    'key'=>'goods/user/getDetail',
                ],
                [
                    'name'=>'设置管理员',
                    'key'=>'goods/user/setAdminUser',
                ],
                [
                    'name'=>'移除管理员',
                    'key'=>'goods/user/delAdminUser',
                ],
                [
                    'name'=>'修改成员密码',
                    'key'=>'goods/user/updatePwd',
                ],
                [
                    'name'=>'修改成员角色',
                    'key'=>'goods/user/setRole',
                ],
            ]
        ],
        [
            'name'=>'产品管理',
            'key'=>'new_goods',
            'child'=>[
                [
                    'name'=>'查看产品列表',
                    'key'=>'goods/goodsNew/getList',
                ],
                [
                    'name'=>'删除产品',
                    'key'=>'goods/goodsNew/delGoods',
                ],
                [
                    'name'=>'获取产品详情',
                    'key'=>'goods/goodsNew/getGoodsDetail',
                ],
                [
                    'name'=>'获取产品修改信息',
                    'key'=>'goods/goodsNew/getEditData',
                ],
                [
                    'name'=>'新增、修改产品',
                    'key'=>'goods/goodsNew/editNewGoods',
                ],
                [
                    'name'=>'设置产品英文名',
                    'key'=>'goods/goodsNew/setGoodsEname',
                ],
                [
                    'name'=>'设置产品中文名',
                    'key'=>'goods/goodsNew/setGoodsName',
                ],
                [
                    'name'=>'获取产品问题记录',
                    'key'=>'goods/goodsNew/getGoodsAbnormaLog',
                ],
                [
                    'name'=>'提交产品问题反馈',
                    'key'=>'goods/goodsNew/setGoodsAbnormal',
                ],
                [
                    'name'=>'处理问题反馈',
                    'key'=>'goods/goodsNew/setAbnormalStatus',
                ],
                [
                    'name'=>'申请出货样流程',
                    'key'=>'goods/goodsNew/applyFlow',
                ],
                [
                    'name'=>'申请抽货流程',
                    'key'=>'goods/goodsNew/applyChouFlow',
                ],
            ]
        ],
        [
            'name'=>'供应商',
            'key'=>'goods_supplier',
            'child'=>[
                [
                    'name'=>'供应商列表',
                    'key'=>'goods/goodsSupplier/getList',
                ],
                [
                    'name'=>'添加、修改供应商',
                    'key'=>'goods/goodsSupplier/editSupplier',
                ]
            ]
        ],
        [
            'name'=>'分类管理',
            'key'=>'goods_cate',
            'child'=>[
                [
                    'name'=>'查看分类列表',
                    'key'=>'goods/goodsCate/getList',
                ],
                [
                    'name'=>'添加、修改分类',
                    'key'=>'goods/goodsCate/editCate',
                ],
                [
                    'name'=>'删除分类',
                    'key'=>'goods/goodsCate/delCate',
                ]

            ]
        ],
        [
            'name'=>'功能管理',
            'key'=>'goods_function',
            'child'=>[
                [
                    'name'=>'查看功能列表',
                    'key'=>'goods/goodsFunction/getList',
                ],
                [
                    'name'=>'添加、修改功能',
                    'key'=>'goods/goodsFunction/editFunction',
                ],
                [
                    'name'=>'删除功能',
                    'key'=>'goods/goodsFunction/delFunction',
                ]
            ]
        ],
        [
            'name'=>'样品管理',
            'key'=>'goods_receiving',
            'child'=>[
                [
                    'name'=>'收样列表',
                    'key'=>'goods/goodsReceiving/getList',
                ],
                [
                    'name'=>'入库登记/修改',
                    'key'=>'goods/goodsReceiving/setReceiving',
                ]
            ]
        ],

        [
            'name'=>'流程管理',
            'key'=>'matters',
            'child'=>[
                [
                    'name'=>'催办',
                    'key'=>'goods/goodsMatters/matterAgentRemind',
                ],
                [
                    'name'=>'提交',
                    'key'=>'goods/goodsMatters/submitMatters',
                ],
                [
                    'name'=>'批量提交',
                    'key'=>'goods/goodsMatters/submitMuchMatters',
                ],

                [
                    'name'=>'交办',
                    'key'=>'goods/goodsMatters/changeMatterAgent',
                ],
                [
                    'name'=>'导出待办',
                    'key'=>'goods/goodsMatters/exportList',
                ],
                [
                    'name'=>'全部流程',
                    'key'=>'goods/goodsMatters/getAllProject',
                ],
                [
                    'name'=>'导出流程',
                    'key'=>'goods/goodsMatters/exportAllProject',
                ],
            ]
        ],
        [
            'name'=>'新品开发',
            'key'=>'new_goods_project',
            'child'=>[
                [
                    'name'=>'查看项目列表',
                    'key'=>'goods/goodsProject/getList',
                ],
                [
                    'name'=>'添加异常',
                    'key'=>'goods/goodsProject/addProjectAbnormal',
                ],
                [
                    'name'=>'流程废除',
                    'key'=>'goods/goodsProject/abolish',
                ],
                [
                    'name'=>'暂停流程',
                    'key'=>'goods/goodsProject/stopProject',
                ],
                [
                    'name'=>'查看项目详情',
                    'key'=>'goods/goodsProject/getDetail',
                ],
                [
                    'name'=>'事件提交',
                    'key'=>'goods/goodsProject/submitEvent',
                ],
                [
                    'name'=>'流程催办',
                    'key'=>'goods/goodsProject/nodeAgentRemind',
                ],
                [
                    'name'=>'流程批量催办',
                    'key'=>'goods/goodsProject/nodeAgentRemindMuch',
                ],
                [
                    'name'=>'流程抄送',
                    'key'=>'goods/goodsProject/sendToUser',
                ],
                [
                    'name'=>'交办',
                    'key'=>'goods/goodsProject/changeAgent',
                ],
//                [
//                    'name'=>'节点提交',
//                    'key'=>'goods/goodsProject/submitNode',
//                ],
                [
                    'name'=>'节点审核',
                    'key'=>'goods/goodsProject/checkNode',
                ],
                [
                    'name'=>'异常列表',
                    'key'=>'goods/abnormal/getGoodsProjectAbnormal',
                ],
                [
                    'name'=>'异常处理',
                    'key'=>'goods/abnormal/setHandledForProjectAbnormal',
                ],
                [
                    'name'=>'APP功能测试保存',
                    'key'=>'goods/goodsAppFunctionTest/saveAppTest',
                ],
                [
                    'name'=>'APP功能测试信息获取',
                    'key'=>'goods/goodsAppFunctionTest/getDetail',
                ],
                [
                    'name'=>'硬件检测项获取',
                    'key'=>'goods/goodsHardwareTest/getHardwareRow',
                ],
                [
                    'name'=>'硬件检测项添加',
                    'key'=>'goods/goodsHardwareTest/addTestRow',
                ],
                [
                    'name'=>'硬件检测详情获取',
                    'key'=>'goods/goodsHardwareTest/getHardwareFrom',
                ],
                [
                    'name'=>'硬件检测保存',
                    'key'=>'goods/goodsHardwareTest/saveHardware',
                ],
                [
                    'name'=>'硬件检测审核不通过',
                    'key'=>'goods/goodsHardwareTest/checkHardwareEvent',
                ],
                [
                    'name'=>'硬件检测审批',
                    'key'=>'goods/goodsHardwareTest/checkApproval',
                ],
                [
                    'name'=>'获取产品规格书数据',
                    'key'=>'goods/goodsQualityBook/getAttrData',
                ],
                [
                    'name'=>'检测标准书保存',
                    'key'=>'goods/goodsQualityBook/saveQualityBook',
                ],
                [
                    'name'=>'检测标准书数据获取',
                    'key'=>'goods/goodsQualityBook/getQualityBookDetail',
                ],
                [
                    'name'=>'检测保准书下载',
                    'key'=>'goods/goodsQualityBook/downLoadQualityBook',
                ],
                [
                    'name'=>'事件文件上传',
                    'key'=>'goods/goodsProject/uploadFile',
                ],
                [
                    'name'=>'事件审核',
                    'key'=>'goods/goodsProject/checkEvent',
                ],
                [
                    'name'=>'批量抄送',
                    'key'=>'goods/goodsProject/sendToUserMuch',
                ],
                [
                    'name'=>'批量暂停',
                    'key'=>'goods/goodsProject/stopProjectMuch',
                ],
            ]
        ],
        [
            'name'=>'产品内容管理',
            'key'=>'goods_content',
            'child'=>[
                [
                    'name'=>'图片需求池列表',
                    'key'=>'goods/imgsRequest/getList',
                ],
                [
                    'name'=>'图片资源申请新增、修改',
                    'key'=>'goods/imgsRequest/applyForImageResource',
                ],
                [
                    'name'=>'SOP审核',
                    'key'=>'goods/imgsRequest/audit',
                ],
                [
                    'name'=>'暂停',
                    'key'=>'goods/imgsRequest/pauseed',
                ],
                [
                    'name'=>'图片需求配置',
                    'key'=>'goods/imgsRequest/setAgentUser',
                ],
                [
                    'name'=>'取消暂停 ',
                    'key'=>'goods/imgsRequest/unPauseed',
                ],
                [
                    'name'=>'接收任务',
                    'key'=>'goods/imgsRequest/acceptTask',
                ],
                [
                    'name'=>'图片测试',
                    'key'=>'goods/imgsRequest/imgTest',
                ],
                [
                    'name'=>'图片上传',
                    'key'=>'goods/imgsRequest/uploadImgFile',
                ],
                [
                        'name'=>'图片提交',
                    'key'=>'goods/imgsRequest/submitTmg',
                ],
                [
                    'name'=>'图片审核',
                    'key'=>'goods/imgsRequest/checkImg',
                ],
                [
                    'name'=>'图片催办',
                    'key'=>'goods/imgsRequest/imgRemind',
                ],
                [
                    'name'=>'图片更换美工',
                    'key'=>'goods/imgsRequest/changeAgent',
                ],
                [
                    'name'=>'说明书任务列表',
                    'key'=>'goods/instructionsRequest/getList',
                ],
                [
                    'name'=>'说明书配置',
                    'key'=>'goods/instructionsRequest/setAgentUser',
                ],
                [
                    'name'=>'说明书文件上传',
                    'key'=>'goods/instructionsRequest/uploadImgFile',
                ],
                [
                    'name'=>'说明书催办',
                    'key'=>'goods/instructionsRequest/remind',
                ],
                [
                    'name'=>'说明书提交',
                    'key'=>'goods/instructionsRequest/submitFile',
                ],
                [
                    'name'=>'说明书审核',
                    'key'=>'goods/instructionsRequest/checkFs',
                ],
                [
                    'name'=>'说明书运营审核',
                    'key'=>'goods/instructionsRequest/checkOperator',
                ],
                [
                    'name'=>'说明书更换美工',
                    'key'=>'goods/instructionsRequest/changeAgent',
                ],
                [
                    'name'=>'重新上传说明书',
                    'key'=>'goods/instructionsRequest/reUploadImgFile',
                ],
                [
                    'name'=>'说明书文件下载',
                    'key'=>'goods/instructionsRequest/downLoad',
                ],
                [
                    'name'=>'图片库',
                    'key'=>'goods/imgsRequestCollection/getList',
                ],
                [
                    'name'=>'图片库文件下载',
                    'key'=>'goods/imgsRequestCollection/downLoad',
                ],
                [
                    'name'=>'批量下载需求文件',
                    'key'=>'goods/imgsRequest/getZipUrl',
                ],
                [
                    'name'=>'需求图片下载',
                        'key'=>'goods/imgsRequest/downLoad',
                ],
                [
                    'name'=>'拍摄图片需求列表',
                    'key'=>'goods/takePictureRequest/getList',
                ],
                [
                    'name'=>'拍摄图片申请',
                    'key'=>'goods/takePictureRequest/editRequest',
                ],
                [
                    'name'=>'拍摄图片上传文件',
                    'key'=>'goods/takePictureRequest/uploadFile',
                ],
                [
                    'name'=>'拍摄图片提交',
                    'key'=>'goods/takePictureRequest/submitTmg',
                ],
                [
                    'name'=>'图片拍摄催办',
                    'key'=>'goods/takePictureRequest/imgRemind',
                ],
                [
                    'name'=>'批量下载图片',
                    'key'=>'goods/takePictureRequest/getZipUrl',
                ],
                [
                    'name'=>'拍摄图片下载',
                    'key'=>'goods/takePictureRequest/downLoad',
                ],
                [
                    'name'=>'图片需求删除',
                    'key'=>'goods/imgsRequest/delete',
                ],
                [
                    'name'=>'详情',
                    'key'=>'goods/imgsRequest/getDetail',
                ],
                [
                    'name'=>'更新信息',
                    'key'=>'goods/imgsRequest/update',
                ],
                [
                    'name'=>'下载指定文件',
                    'key'=>'goods/imgsRequest/downLoadFile',
                ],
                [
                    'name'=>'SOP审核列表',
                    'key'=>'goods/sopReviewer/getList',
                ],
                [
                    'name'=>'SOP审核配置删除',
                    'key'=>'goods/sopReviewer/deleteSopReviewer',
                ],
                [
                    'name'=>'SOP审核配置新增编辑',
                    'key'=>'goods/sopReviewer/saveSopReviewer',
                ],
                [
                    'name'=>'SOP审核配置终线设置',
                    'key'=>'goods/sopReviewer/getFinalLine',
                ],
                [
                    'name'=>'更换需求相关人员',
                    'key'=>'goods/imgsRequest/updateParticipant',
                ],
                [
                    'name'=>'更新文件',
                    'key'=>'admin/imgsRequest/updateImgFile',
                ]
            ]
        ],
        [
            'name'=>'事件管理',
            'key'=>'event',
            'child'=>[
                [
                    'name'=>'查看事件列表',
                    'key'=>'goods/event/getList',
                ],
                [
                    'name'=>'新增、修改事件',
                    'key'=>'goods/event/editEvent',
                ],
                [
                    'name'=>'查看事件详情',
                    'key'=>'goods/event/getDtail',
                ],
                [
                    'name'=>'修改事件状态',
                    'key'=>'goods/event/setStatus',
                ]
            ]
        ],
        [
            'name'=>'节点管理',
            'key'=>'node',
            'child'=>[
                [
                    'name'=>'查看节点列表',
                    'key'=>'goods/node/getList',
                ],
                [
                    'name'=>'新增、修改节点',
                    'key'=>'goods/node/editNode',
                ],
                [
                    'name'=>'查看节点详情',
                    'key'=>'goods/node/getDtail',
                ],
                [
                    'name'=>'修改节点状态',
                    'key'=>'goods/node/setStatus',
                ]
            ]
        ],
        [
            'name'=>'模板管理',
            'key'=>'template',
            'child'=>[
                [
                    'name'=>'查看模板列表',
                    'key'=>'goods/template/getList',
                ],
                [
                    'name'=>'新增、修改模板',
                    'key'=>'goods/template/editTemplate',
                ],
                [
                    'name'=>'查看模板详情',
                    'key'=>'goods/template/getDtail',
                ],
                [
                    'name'=>'修改模板状态',
                    'key'=>'goods/template/setStatus',
                ]
            ]
        ],
        [
            'name'=>'文档管理',
            'key'=>'goods_file',
            'child'=>[
                [
                    'name'=>'文档列表',
                    'key'=>'goods/projectFile/getList'
                ],
                [
                    'name'=>'下载文件',
                    'key'=>'goods/projectFile/downLoadFile'
                ],

            ]
        ],
        [
            'name'=>'系统配置',
            'key'=>'config',
            'child'=>[
                [
                    'name'=>'查看系统配置',
                    'key'=>'goods/config/getConfig',
                ],
                [
                    'name'=>'修改系统配置',
                    'key'=>'goods/config/setConfig',
                ],
                [
                    'name'=>'图片审核配置列表',
                    'key'=>'goods/imageOwnerConfig/getImageOwnerList',
                ],
                [
                    'name'=>'图片审核配置删除',
                    'key'=>'goods/imageOwnerConfig/deleteImageOwner',
                ],
                [
                    'name'=>'图片审核配置新增编辑',
                    'key'=>'goods/imageOwnerConfig/saveImageOwner',
                ],
                [
                    'name'=>'图片审核配置终线设置',
                    'key'=>'goods/imageOwnerConfig/getFinalLine',
                ]
            ]
        ],
    ];
    //验证用户请求权限
    public static function checkAuth($url, $module){
        if ($module == 'task') {
            return true;
        } else {
            //先重写userModel中数据
            $data = qwuserFrom::getUserAuth();
            $auth = $data['auth'];
            $role_type = $data['role_type'];
            userModel::$role_type = $role_type;
            userModel::$auth = json_encode($auth);
            //权限验证
            if (in_array($url, self::$common_auth)) {
                return;
            }
            if (userModel::$is_super == 1) {
                return true;
            }
            if (!in_array($url,$auth)) {
                SetReturn(-1,'暂无权限');
            } else {
                return true;
            }
        }
    }

}