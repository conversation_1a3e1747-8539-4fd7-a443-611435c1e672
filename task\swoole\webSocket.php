<?php
/**
 * @author: zhangguoming
 * @Time: 2024/1/4 17:47
 */

use Swoole\WebSocket\Server;
$ws = new Server('0.0.0.0', 9502);

global $user;
//监听WebSocket连接打开事件。
$ws->on('Open', function ($ws, $request) {
    $ws->push($request->fd, "恭喜你，连接成功！\n");
});

//监听WebSocket消息事件。
$ws->on('Message', function ($ws, $frame) {
    global $user;
    $data = json_decode($frame->data,true);
    $id = $data['id'];
    echo "Message: from {$frame->fd}:{$data['msg']},opcode:{$frame->opcode},fin:{$frame->finish}\n";

    // 广播消息到除发送者外的所有客户端
    if ($id == 1) {
        //后台发送
        foreach ($ws->connections as $fd) {
            $ws->push($fd, $data['msg']);
//            if ($fd != $frame->fd) { // 不向发送者发送消息
//
//            }
        }
    } else {
        //前台发送

    }
});


//监听WebSocket连接关闭事件。
$ws->on('Close', function ($ws, $fd) {
    //关闭记录
    echo "client-{$fd} is closed\n";
});

$ws->start();