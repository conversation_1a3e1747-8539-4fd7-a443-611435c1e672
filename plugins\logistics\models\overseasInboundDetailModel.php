<?php

namespace plugins\logistics\models;

use core\lib\db\dbLMysql;
use core\lib\db\dbErpMysql;

/**
 * 海外仓备货单明细模型
 * 处理SKU拆分和明细数据管理
 */
class overseasInboundDetailModel
{
    private $db;
    private $erpDb;

    public function __construct()
    {
        $this->db = dbLMysql::getInstance();
        $this->erpDb = dbErpMysql::getInstance();
    }

    /**
     * 获取待拆分的备货单数据
     * @param int $page 偏移量
     * @param int $page_size 限制数量
     * @return array
     */
    public function getUnprocessedOrders($page = 0, $page_size = 100)
    {
    
        return $this->erpDb->table('lingxing_overseas_inbound')->field('*')->order('id ASC')->pages($page, $page_size);
    }

    /**
     * 获取待拆分的备货单总数
     * @return int
     */
    public function getUnprocessedOrdersCount()
    {
        $sql = "SELECT COUNT(*) as total FROM lingxing_overseas_inbound ";
        $result = $this->erpDb->query($sql);
        return $result ? (int)$result['total'] : 0;
    }

    /**
     * 拆分单个备货单为SKU明细
     * @param array $order 备货单数据
     * @return array 拆分后的SKU明细数组
     */
    public function splitOrderBySku($order)
    {
        $details = [];
        
        // 解析products JSON
        $products = $this->parseProductsJson($order['products']);
        if (empty($products)) {
            return $details;
        }

        // 解析logistics JSON获取物流信息
        $logistics = $this->parseLogisticsJson($order['logistics']);

        foreach ($products as $product) {
            $detail = $this->mapOrderToSkuDetail($order, $product, $logistics);
            if ($detail) {
                $details[] = $detail;
            }
        }

        return $details;
    }

    /**
     * 解析products JSON字段
     * @param string $productsJson
     * @return array
     */
    private function parseProductsJson($productsJson)
    {
        if (empty($productsJson)) {
            return [];
        }

        $products = json_decode($productsJson, true);
        if (!is_array($products)) {
            return [];
        }

        return $products;
    }

    /**
     * 解析logistics JSON字段
     * @param string $logisticsJson
     * @return array
     */
    private function parseLogisticsJson($logisticsJson)
    {
        if (empty($logisticsJson)) {
            return [];
        }

        $logistics = json_decode($logisticsJson, true);
        if (!is_array($logistics)) {
            return [];
        }

        return $logistics;
    }

    /**
     * 将备货单和SKU信息映射为明细记录
     * @param array $order 备货单信息
     * @param array $product SKU产品信息
     * @param array $logistics 物流信息
     * @return array|null
     */
    private function mapOrderToSkuDetail($order, $product, $logistics)
    {
        // 基础字段验证
        if (empty($product['sku'])) {
            return null;
        }

        // 获取批次信息
        $batchInfo = $this->getBatchInfo($product);

        return [
            'overseas_order_no' => $order['overseas_order_no'] ?? '',
            'sku' => $product['sku'] ?? '',
            'product_name' => $product['product_name'] ?? '',
            'shop_code' => $order['shop_code'] ?? '',
            'fnsku' => $product['fnsku'] ?? '',
            'quantity' => (int)($product['stock_num'] ?? 0),
            'box_count' => (int)($batchInfo['good_num'] ?? 0),
            'warehouse_code' => $batchInfo['batch_order_sn'] ?? '',
            'transparent_label' => '',
            'logistics_method' => $logistics['method'] ?? '',
            'target_warehouse' => $product['s_wname'] ?? '',
            'plan_time' => $order['plan_time'] ?? null,
            'ship_time' => $order['ship_time'] ?? null,
            'shipping_status' => '',
            'warehouse_arrival' => $order['warehouse_arrival'] ?? null,
            'receive_difference' => (int)($product['stock_num'] ?? 0) - (int)($product['receive_num'] ?? 0),
            'remaining_available' => (int)($product['receive_num'] ?? 0),
            'shipping_remark' => $order['shipping_remark'] ?? '',
            'other_remark' => $order['other_remark'] ?? '',
            'original_order_id' => $order['id'],
            'product_id' => (int)($product['product_id'] ?? 0),
            'batch_no' => $batchInfo['batch_no'] ?? '',
            'unit_cost' => (float)($batchInfo['unit_cost'] ?? 0),
            'total_cost' => (float)($batchInfo['unit_cost'] ?? 0) * (int)($product['stock_num'] ?? 0),
            'sync_date' => date('Y-m-d'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'is_deleted' => 0
        ];
    }

    /**
     * 获取批次信息
     * @param array $product 产品信息
     * @return array
     */
    private function getBatchInfo($product)
    {
        $batchRecordList = $product['batch_record_list'] ?? [];
        if (empty($batchRecordList) || !is_array($batchRecordList)) {
            return [];
        }

        // 返回第一个批次记录
        return $batchRecordList[0] ?? [];
    }

    /**
     * 批量插入SKU明细数据
     * @param array $details SKU明细数组
     * @return bool
     */
    public function batchInsertDetails($details)
    {
        if (empty($details)) {
            return true;
        }

        try {
            $this->db->beginTransaction();

            foreach ($details as $detail) {
                // 检查是否已存在
                $existing = $this->checkExistingDetail($detail['overseas_order_no'], $detail['sku']);
                
                if ($existing) {
                    // 更新现有记录
                    $this->updateDetail($existing['id'], $detail);
                } else {
                    // 插入新记录
                    $this->insertDetail($detail);
                }
            }

            $this->db->commit();
            return true;

        } catch (\Exception $e) {
            $this->db->rollBack();
            error_log("批量插入SKU明细失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 检查是否存在相同的明细记录
     * @param string $orderNo 备货单号
     * @param string $sku SKU
     * @return array|false
     */
    private function checkExistingDetail($orderNo, $sku)
    {

        return $this->db->table('overseas_inbound_detail')
            ->where('overseas_order_no = :order_no AND sku = :sku AND is_deleted = 0', [
                'order_no' => $orderNo,
                'sku' => $sku
            ])
            ->one();
    }

    /**
     * 插入新的明细记录
     * @param array $detail 明细数据
     * @return bool
     */
    private function insertDetail($detail)
    {
        return $this->db->table('overseas_inbound_detail')->insert($detail);
    }

    /**
     * 更新现有明细记录
     * @param int $id 记录ID
     * @param array $detail 明细数据
     * @return bool
     */
    private function updateDetail($id, $detail)
    {
        unset($detail['created_at']); // 移除创建时间，保持原值
        $detail['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->table('overseas_inbound_detail')
            ->where('id = :id', ['id' => $id])
            ->update($detail);
    }

    /**
     * 清理指定日期的明细数据
     * @param string $syncDate 同步日期
     * @return bool
     */
    public function clearDetailsByDate($syncDate)
    {
        return $this->db->table('overseas_inbound_detail')
            ->where('sync_date = :sync_date', ['sync_date' => $syncDate])
            ->update(['is_deleted' => 1]);
    }

    /**
     * 获取明细数据统计
     * @param string $syncDate 同步日期
     * @return array
     */
    public function getDetailStats($syncDate)
    {
        return $this->db->table('overseas_inbound_detail')
            ->feild('COUNT(*) as total_count,
                    COUNT(DISTINCT overseas_order_no) as order_count,
                    COUNT(DISTINCT sku) as sku_count,
                    SUM(quantity) as total_quantity,
                    SUM(total_cost) as total_cost')
            ->where('sync_date = :sync_date AND is_deleted = 0', ['sync_date' => $syncDate])
            ->list();
    }

    /**
     * 获取明细数据用于导出
     * @param string $syncDate 同步日期
     * @param string $orderNo 备货单号（可选）
     * @param string $sku SKU（可选）
     * @return array
     */
    public function getDetailForExport($syncDate, $orderNo = '', $sku = '')
    {

        $this->db->table('overseas_inbound_detail')
        ->field('overseas_order_no, sku, product_name, shop_code, fnsku,
                        quantity, box_count, warehouse_code, transparent_label,
                        logistics_method, target_warehouse, plan_time, ship_time,
                        shipping_status, warehouse_arrival, receive_difference,
                        remaining_available, shipping_remark, other_remark,
                        created_at, updated_at')
                        ->where('sync_date = :sync_date AND is_deleted = 0', ['sync_date' => $syncDate])
                        ->order('overseas_order_no, sku');
        if (!empty($orderNo)) {
            $this->db->andWhere('overseas_order_no = :order_no', ['order_no' => $orderNo]);
        }
        if (!empty($sku)) {
            $this->db->andWhere('sku = :sku', ['sku' => $sku]);
        }
        return $this->db->list();
    }

    /**
     * 批量新增明细数据
     * @param array $importData 导入数据
     * @return array 处理结果
     */
    public function batchInsertNewDetails($importData)
    {
        $result = [
            'success_count' => 0,
            'error_count' => 0,
            'errors' => []
        ];

        try {
            $this->db->beginTransaction();

            foreach ($importData as $index => $data) {
                try {
                    // 检查是否已存在
                    $existing = $this->checkExistingDetail($data['overseas_order_no'], $data['sku']);

                    if ($existing) {
                        $result['error_count']++;
                        $result['errors'][] = "第" . ($index + 1) . "行：备货单号{$data['overseas_order_no']}的SKU{$data['sku']}已存在";
                        continue;
                    }

                    // 添加系统字段
                    $data['sync_date'] = date('Y-m-d');
                    $data['created_at'] = date('Y-m-d H:i:s');
                    $data['updated_at'] = date('Y-m-d H:i:s');
                    $data['is_deleted'] = 0;

                    $this->insertDetail($data);
                    $result['success_count']++;

                } catch (\Exception $e) {
                    $result['error_count']++;
                    $result['errors'][] = "第" . ($index + 1) . "行：" . $e->getMessage();
                }
            }

            $this->db->commit();

        } catch (\Exception $e) {
            $this->db->rollBack();
            throw $e;
        }

        return $result;
    }

    /**
     * 批量更新明细数据
     * @param array $importData 导入数据
     * @return array 处理结果
     */
    public function batchUpdateDetails($importData)
    {
        $result = [
            'success_count' => 0,
            'error_count' => 0,
            'errors' => []
        ];

        try {
            $this->db->beginTransaction();

            foreach ($importData as $index => $data) {
                try {
                    // 检查记录是否存在
                    $existing = $this->checkExistingDetail($data['overseas_order_no'], $data['sku']);

                    if (!$existing) {
                        $result['error_count']++;
                        $result['errors'][] = "第" . ($index + 1) . "行：备货单号{$data['overseas_order_no']}的SKU{$data['sku']}不存在";
                        continue;
                    }

                    // 更新记录
                    $this->updateDetail($existing['id'], $data);
                    $result['success_count']++;

                } catch (\Exception $e) {
                    $result['error_count']++;
                    $result['errors'][] = "第" . ($index + 1) . "行：" . $e->getMessage();
                }
            }

            $this->db->commit();

        } catch (\Exception $e) {
            $this->db->rollBack();
            throw $e;
        }

        return $result;
    }

    /**
     * 获取明细数据列表（分页）
     * @param array $params 查询参数
     * @return array
     */
    public function getDetailList($params = [])
    {
        $page = (int)($params['page'] ?? 1);
        $pageSize = (int)($params['page_size'] ?? 20);

        $this->db->table('overseas_inbound_detail')
            ->field('*')
            ->where('1=1')
            ->order('created_at DESC');

        if (!empty($params['sync_date'])) {
            $this->db->andWhere('sync_date = :sync_date', ['sync_date' => $params['sync_date']]);
        }

        if (!empty($params['overseas_order_no'])) {
            $this->db->andWhere('overseas_order_no LIKE :order_no', ['order_no' => '%' . $params['overseas_order_no'] . '%']);
        }

        if (!empty($params['sku'])) {
            $this->db->andWhere('sku LIKE :sku', ['sku' => '%' . $params['sku'] . '%']);
        }

        if (!empty($params['shop_code'])) {
            $this->db->andWhere('shop_code = :shop_code', ['shop_code' => $params['shop_code']]);
        }

        return $this->db->pages($page, $pageSize);

    }

    /**
     * 删除明细记录（软删除）
     * @param int $id 记录ID
     * @return bool
     */
    public function deleteDetail($id)
    {
        return $this->db->table('overseas_inbound_detail')
            ->where('id = :id', ['id' => $id])
            ->update(['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
    }

    /**
     * 批量删除明细记录（软删除）
     * @param array $ids 记录ID数组
     * @return bool
     */
    public function batchDeleteDetails($ids)
    {
        if (empty($ids)) {
            return true;
        }

        $idList = implode(',', array_map('intval', $ids));
        return $this->db->table('overseas_inbound_detail')
            ->whereIn('id', $idList)
            ->update(['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
    }
}
