<?php

/**
 * @author: zhangguoming
 * @Time: 2025/4/10 11:15
 */
namespace core\sdk\qw_app;

class qwApiRequest extends baseMothed
{
    //建群
    public static function createAppchat(string $chat_name,string $owner,array $user_list) {
        $user_list = array_values(array_unique($user_list));
        if (count($user_list) < 2 || count($user_list) > 2000) {
            self::$error_msg = '群成员至少2人，至多2000人';
            return false;
        }
        $token = self::getToken();
        if (!$token) {
            return false;
        }
        //数据准备
        $data = [
            'name'=>$chat_name,
            'owner'=>$owner,
            'userlist'=> $user_list,
        ];
        //发送消息
        $url = self::$appchat_create.'?access_token='.$token;
        $data = self::getRequsestData('post',$data,$url);
        if ($data) {
            return $data['chatid'];
        } else {
            return false;
        }
    }
    //改群
    public static function editAppchat(string $chatid,string $chat_name,string $owner,array $add_user_list,array $del_user_list) {
        if (!count($add_user_list)  && !count($del_user_list)) {
            return false;
        }
        $add_user_list = array_values(array_unique($add_user_list));
        $del_user_list = array_values(array_unique($del_user_list));
        $token = self::getToken();
        if (!$token) {
            return false;
        }
        //数据准备
        $data = [
            'chatid'=>$chatid,
            'name'=>$chat_name,
            'owner'=>$owner,
            'add_user_list'=>$add_user_list,
            'del_user_list'=>$del_user_list
        ];
        //发送消息
        $url = self::$appchat_update.'?access_token='.$token;
        $data = self::getRequsestData('post',$data,$url);
        return $data;
    }
    //群消息推送
    public static function sendAppchat(string $chatid,string $content) {
        if (empty($content)) {
            return true;
        }
        $token = self::getToken();
        if (!$token) {
            return false;
        }
        //数据准备
        $data = [
            'chatid'=>$chatid,
            'msgtype'=>'text',
            'text'=>[
                'content'=>$content
            ]
        ];
        //发送消息
        $url = self::$appchat_send.'?access_token='.$token;
        $data = self::getRequsestData('post',$data,$url);
        return $data;
    }
}