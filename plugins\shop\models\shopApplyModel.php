<?php

namespace plugins\shop\models;

use admin\models\qwdepartmentModel;
use core\lib\db\dbAMysql;
use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use Exception;
use plugins\shop\form\messagesFrom;

class shopApplyModel extends baseModel
{
    public string $table = 'shop_apply';

    public static array $paras_list = [
        'dep_id'          => '部门id',
        'country_site'    => '站点',
        'shop_type'       => '店铺类型',
        'expect_date'     => '期望下达日期',
        'remind_interval' => '间隔提醒',
        'apply_reason'    => '申请原因',
        'remark'          => '备注',
    ];

    // 申请状态常量
    const STATUS_PENDING = 0;    // 待审核
    const STATUS_REJECTED = 1;   // 审核未通过
    const STATUS_WAIT_RECEIVE = 2;   // 待接收
    const STATUS_WAIT_ASSIGN = 3;   // 待分配
    const STATUS_SUCCESS = 4;   // 已完成
    const STATUS_CANCEL = 5;   // 已取消


    public static function getMaps()
    {
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');
        $deps = redisCached::getDepartment();
        $deps = array_column($deps, 'name', 'id');
        $shops = redisCached::getShop();
        $shops = array_column($shops, 'shop_num', 'id');
        return ['users' => $users, 'deps' => $deps, 'shops' => $shops];
    }
    /**
     * 格式化输出项
     */
    public function formatItem($item, $maps = [])
    {
        $users = $maps['users'] ?? [];
        if (empty($users)) {
            $users = redisCached::getUserInfo();
            $users = array_column($users, 'user_name', 'user_id');
        }
        $deps = $maps['deps'] ?? [];
        if (empty($deps)) {
            $deps = redisCached::getDepartment();
            $deps = array_column($deps, 'name', 'id');
        }
        $shops = $maps['shops'] ?? [];
        if (empty($shops)) {
            $shops = redisCached::getShop();
            $shops = array_column($shops, 'shop_number', 'id');
        }
        $format_rules = [
            ['name' => 'bind_user_name', 'maps' => $users, 'key' => 'bind_user_id'],
            ['name' => 'dep_name', 'maps' => $deps, 'key' => 'dep_id'],
            ['name' => 'shop_number', 'maps' => $shops, 'key' => 'shop_id']
        ];

        return parent::formatItem($item, $format_rules);
    }

    /**
     * 获取申请列表
     */
    public function getList($param, $order = 'id desc', $is_export = false)
    {
        // 权限控制
        $user_id = userModel::$qwuser_id;
        $all_department_users = qwdepartmentModel::getUserDepartment(userModel::$wid, false)['all_department_users'] ?: []; // 当前用户管理的部门及其子部门下的用户
        $user_ids = array_column($all_department_users, 'id');

        $db = $this->db->table($this->table, 'sa')
            ->field('sa.*, s.shop_number')
            ->leftJoin('shop', 's', 's.id=sa.shop_id');

        if (userModel::getUserListAuth('shopDemandAll')) {
            // 全部权限
        } elseif (userModel::getUserListAuth('shopDemandDepartment')) {
            if (!empty($all_department_users)) {
                $this->db->whereIn('sa.user_id', $user_ids);
            }
        } elseif (userModel::getUserListAuth('shopDemandRelated')) {
            $this->db->andWhere('sa.user_id = :user_id', ['user_id' => $user_id]);
        }

        if (!empty($param['shop_number'])) {
            $db->andWhere('s.shop_number = :shop_number', ['shop_number' => $param['shop_number']]);
        }
        if (!empty($param['dep_id']) && is_array($param['dep_id']) && count($param['dep_id']) > 0) {
            $db->whereIn('sa.dep_id', $param['dep_id']);
        }
        if (isset($param['country_site'])) {
            $db->andWhere('country_site = :country_site', ['country_site' => $param['country_site']]);
        }
        if (!empty($param['shop_type'])) {
            $db->andWhere('shop_type = :shop_type', ['shop_type' => $param['shop_type']]);
        }
        if (isset($param['status'])) {
            $db->andWhere('status = :status', ['status' => $param['status']]);
        }
        if (!empty($param['expect_date'])) {
            $db->andWhere('expect_date >= :expect_date_start and expect_date <= :expect_date_end', [
                'expect_date_start' => $param['expect_date'][0],
                'expect_date_end'   => $param['expect_date'][1]
            ]);
        }
        if (!empty($param['bind_time'])) {
            $db->andWhere('sa.bind_time >= :bind_date_start and sa.bind_time <= :bind_date_end', [
                'bind_date_start' => $param['bind_time'][0],
                'bind_date_end'   => $param['bind_time'][1]
            ]);
        }


        $db->order($order);

        if (!$is_export && !empty($param['page']) && !empty($param['page_size'])) {
            $list = $db->pages($param['page'], $param['page_size']);
            $maps = self::getMaps();
            foreach ($list['list'] as &$item) {
                $item = $this->formatItem($item, $maps);
            }
            return $list;
        } else {
            $list = $db->list();
            $maps = self::getMaps();

            $result = [];
            foreach ($list as $item) {
                $formatted = $this->formatItem($item, $maps);
                if ($is_export) {
                    // 导出使用中文键名
                    $result[] = self::changeToCnKey($formatted);
                } else {
                    $result[] = $formatted;
                }
            }

            return $result;
        }
    }

    /**
     * 创建申请
     */
    public function apply($data)
    {
        $data['status'] = self::STATUS_PENDING;
        $data['user_id'] = userModel::$qwuser_id;
        return parent::add($data, '申请');
    }

    // 编辑
    public function edit($data, $id, $old_data = [], $type = '编辑', $remark = '', $result = '', $other_attach = [])
    {
        $detail = $this->getById($id);
        if (!$detail) {
            throw new Exception('数据不存在');
        }

        // 审核拒绝再次编辑，状态变为待审核
        if ($detail['status'] == self::STATUS_REJECTED) {
            $data['status'] = self::STATUS_PENDING;
        }

        $this->dataValidCheck($data, self::$paras_list);
        parent::edit($data, $id, $detail, $type, $remark, $result, $other_attach);
    }

    /**
     * 取消申请
     */
    public function cancel($id)
    {
        $detail = $this->getById($id);
        if (!$detail) {
            throw new Exception('数据不存在');
        }

        if (in_array($detail['status'], [self::STATUS_SUCCESS, self::STATUS_CANCEL])) {
            throw new Exception('当前状态不能取消');
        }

        $data = [
            'status' => self::STATUS_CANCEL,
        ];
        parent::edit($data, $id, $detail, '取消申请');
    }

    /**
     * 审核申请
     */
    public function audit($id, $result, $remark)
    {
        $detail = $this->getById($id);
        if (!$detail) {
            throw new Exception('数据不存在');
        }
        if ($detail['status'] != self::STATUS_PENDING) {
            throw new Exception('只能审核待审核的申请');
        }

        $data = [
            'status' => $result == 1 ? self::STATUS_WAIT_RECEIVE : self::STATUS_REJECTED,
        ];
        if ($result == 1) {
            $users = configModel::noticeUser('shop_demand', 'receive', ['dep_id' => $detail['dep_id']]);
            if (!empty($users)) {
                messagesFrom::senMeg($users, 1 , "您有新的店铺申请任务，请及时处理", $id, '', '店铺申请待接收');
            }
        } else {
            $users = redisCached::getUserInfo();
            $users = array_column($users, 'user_wid', 'user_id');
            messagesFrom::senMeg([$users[$detail['operator']]], 1, "您提交的店铺审需求审核未通过", $id, "店铺申请审核未通过");
        }

        parent::edit($data, $id, $detail, '审核', $remark, $result);
    }

    /**
     * 接收申请
     */
    public function receive($id)
    {
        $detail = $this->getById($id);
        if (!$detail) {
            throw new Exception('数据不存在');
        }
        if ($detail['status'] != self::STATUS_WAIT_RECEIVE) {
            throw new Exception('只能接收待分配的申请');
        }

        $adb = dbAMysql::getInstance();
        // 添加定时任务
        $crontab_data = [
            'is_crontab_task' => 1,
            'link_id'         => $id,
            'link_type'       => 2,
            'link_module'       => 3,
            'runtime'         => date('Y-m-d H:i:s', time()),
        ];
        $adb->table('custom_crontab');
        $adb->insert($crontab_data);

        $data = [
            'status' => self::STATUS_WAIT_ASSIGN,
        ];
        parent::edit($data, $id, $detail, '接收');
    }

    /**
     * 分配店铺
     */
    public function assign($id, $shop_id, $remark)
    {
        $detail = $this->getById($id);
        if (!$detail) {
            throw new Exception('数据不存在');
        }
        if ($detail['status'] != self::STATUS_WAIT_ASSIGN) {
            throw new Exception('只能接收待分配的申请');
        }

        $bind_time = date('Y-m-d H:i:s', time());

        // 写入店铺表
        dbShopMysql::getInstance()->table('shop')->where('where id = :id', ['id' => $shop_id])->update([
            'dep_id' => $detail['dep_id'],
            'bind_time' => $bind_time,
        ]);

        $data = [
            'status'  => self::STATUS_SUCCESS,
            'shop_id' => $shop_id,
            'bind_time' => $bind_time,
            'bind_user_id' => userModel::$qwuser_id ?? 0,
        ];

        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_wid', 'user_id');

        $deps = redisCached::getDepartment();
        $deps = array_column($deps, 'name', 'id');

        messagesFrom::senMeg([$users[$detail['operator']]], 1, "您的【{$deps[$detail['dep_id']]}_{$detail['country_site']}_{$detail['shop_type']}】已分配店铺", $id, "店铺申请审核未通过");

        parent::edit($data, $id, $detail, '分配', $remark);
    }

    /**
     * 撤回分配
     */
    public function cancelAssign($id)
    {
        $detail = $this->getById($id);
        if (!$detail) {
            throw new Exception('数据不存在');
        }
        if ($detail['status'] != self::STATUS_SUCCESS) {
            throw new Exception('只能撤回已完成的申请');
        }

        // 写入店铺表
        dbShopMysql::getInstance()->table('shop')->where('where id = :id', ['id' => $detail['shop_id']])->update(['dep_id' => null, 'bind_time' => null]);

        $data = [
            'status'  => self::STATUS_WAIT_ASSIGN,
            'shop_id' => null,
            'bind_time' => null,
            'bind_user_id' => null,
        ];
        parent::edit($data, $id, $detail, '撤回分配');
    }

    /**
     * 获取详情
     */
    public function detail($id)
    {
        return $this->db->table($this->table)
            ->where('id', $id)
            ->one();
    }

    /**
     * 催办
     */
    public function urge($id)
    {
        $detail = $this->detail($id);
        if (!$detail) {
            return false;
        }

        // 更新催办时间和次数
        return $this->db->table($this->table)
            ->where('id', $id)
            ->update([
                'last_urge_at' => date('Y-m-d H:i:s'),
                'urge_count'   => intval($detail['urge_count']) + 1,
                'urge_by'      => $_SESSION['user_id'] ?? 0
            ]);
    }


}
