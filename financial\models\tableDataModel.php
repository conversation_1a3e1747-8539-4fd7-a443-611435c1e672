<?php
/**
 * @author: zhangguoming
 * @Time: 2024/8/14 9:57
 */

namespace financial\models;

use core\lib\db\dbFMysql;

class tableDataModel
{
    public static array $lx_keys; //要查的领星字段
    public static array $oa_keys; //要查的oa字段
    public static array $oa_column_ids; //要查的自定义字段id
    public static array $table_index; //表角标
    public static array $currency_keys; //金额字段

    public function __construct()
    {
        $db = dbFMysql::getInstance();
        //百分比字段不计算在内
        $db->table('column')
            ->where('where is_delete = 0 and table_index <> 0')
            ->andWhere('show_type <> 2')
            ->field('key_name,column_name,table_index,show_type,custom_id,data_type');
        $list = $db->list();
        $lx_keys = [];
        $table_index = [];
        $currency_keys = [];
        $oa_column_ids = [];
        $oa_keys = [];
        foreach ($list as $v) {
            if ($v['custom_id'] > 0) {
                if ($v['custom_id'] != 5) {
                    $oa_column_ids[] = $v['custom_id'];
                    $oa_keys[] = $v['key_name'];
                }
            } else {
                $lx_keys[] = $v['key_name'];
                $table_index[] = $v['table_index'];
            }
            if ($v['show_type'] == 1) {
                $currency_keys[] = $v['key_name'];
            }
        }
        self::$lx_keys = $lx_keys;
        self::$oa_keys = $oa_keys;
        self::$oa_column_ids = $oa_column_ids;
        self::$table_index = $table_index;
        self::$currency_keys = $currency_keys;
    }

    public static function creatTableMonth($year) {
        $table_name = 'table_month_count_'.$year;
        $custom_val = 'custom_val_'.$year;
        //$custom_name = 'table_month_custom_'.$year;
        $dbF = dbFMysql::getInstance();
        $dbF->query("CREATE TABLE IF NOT EXISTS `oa_f_$table_name` (
            `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
            `sid` varchar(50) NOT NULL COMMENT '店铺id',
            `localSku` varchar(50) NOT NULL,
            `msku` varchar(50) NOT NULL,
            `asin` varchar(50) NOT NULL,
            `parentAsin` varchar(50) NOT NULL,
            `countryCode` varchar(10) NOT NULL,
            `project_id` int(11) NOT NULL,
            `yunying_id` int(11) NOT NULL,
            `supplier_id` int(11) NOT NULL DEFAULT '0' COMMENT 'oa供应商id',
            `reportDateMonth` varchar(10) NOT NULL,
            `created_time` datetime DEFAULT NULL,
            `updated_time` datetime DEFAULT NULL,
            `is_delete` tinyint(1) NOT NULL DEFAULT '0',
            `totalFbaAndFbmQuantity` int(11) DEFAULT '0' COMMENT 'fba和fbm销量加总，用于计算占比',
            `totalFbaAndFbmAmount` decimal(10,2) DEFAULT '0.00' COMMENT 'fba和fbm销售额加总，用于计算占比',
            `totalSalesQuantity` int(11) DEFAULT '0' COMMENT '销量',
            `fbaSalesQuantity` int(11) DEFAULT '0' COMMENT 'FBA销量',
            `fbmSalesQuantity` int(11) DEFAULT NULL COMMENT 'FBM销量',
            `totalReshipQuantity` int(11) DEFAULT NULL COMMENT '补换货量',
            `reshipFbmProductSalesQuantity` int(11) DEFAULT '0' COMMENT 'FBM补（换）货量',
            `reshipFbmProductSaleRefundsQuantity` int(11) DEFAULT '0' COMMENT 'FBM补（换）货退回量',
            `reshipFbaProductSalesQuantity` int(11) DEFAULT '0' COMMENT 'FBA补（换）货量',
            `reshipFbaProductSaleRefundsQuantity` int(11) DEFAULT '0' COMMENT 'FBA补（换）货退回量',
            `cgAbsQuantity` decimal(12,2) DEFAULT '0.00' COMMENT '成本数量绝对值',
            `cgQuantity` int(11) DEFAULT '0' COMMENT '成本数量',
            `totalAdsSales` decimal(12,2) DEFAULT '0.00' COMMENT '广告销售额',
            `adsSdSales` decimal(12,2) DEFAULT '0.00' COMMENT 'sd广告销售额',
            `adsSpSales` decimal(12,2) DEFAULT '0.00' COMMENT 'sp广告销售额',
            `sharedAdsSbSales` decimal(12,2) DEFAULT '0.00' COMMENT 'sb广告销售额',
            `sharedAdsSbvSales` decimal(12,2) DEFAULT '0.00' COMMENT 'sbv广告销售额',
            `totalAdsSalesQuantity` int(11) DEFAULT '0' COMMENT '广告销量',
            `adsSdSalesQuantity` int(11) DEFAULT '0' COMMENT 'sd广告销量',
            `adsSpSalesQuantity` int(11) DEFAULT '0' COMMENT 'sp广告销量',
            `sharedAdsSbSalesQuantity` int(11) DEFAULT '0' COMMENT 'sb广告销量',
            `sharedAdsSbvSalesQuantity` int(11) DEFAULT '0' COMMENT 'sbv广告销量',
            `totalSalesAmount` decimal(12,2) DEFAULT '0.00' COMMENT '销售额',
            `fbaSaleAmount` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA销售额',
            `fbmSaleAmount` decimal(12,2) DEFAULT '0.00' COMMENT 'FBM销售额',
            `shippingCredits` decimal(12,2) DEFAULT '0.00' COMMENT '买家运费',
            `promotionalRebates` decimal(12,2) DEFAULT '0.00' COMMENT '促销折扣',
            `fbaInventoryCredit` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA库存赔偿',
            `cashOnDelivery` decimal(12,2) DEFAULT '0.00' COMMENT 'COD',
            `otherInAmount` decimal(12,2) DEFAULT '0.00' COMMENT '其他收入',
            `fbaLiquidationProceeds` decimal(12,2) DEFAULT '0.00' COMMENT '清算收入',
            `fbaLiquidationProceedsAdjustments` decimal(12,2) DEFAULT '0.00' COMMENT '清算调整',
            `amazonShippingReimbursement` decimal(12,2) DEFAULT '0.00' COMMENT '亚马逊运费赔偿',
            `mcFbaFulfillmentFeesQuantity` int DEFAULT '0' COMMENT '多渠道销量',
            `safeTReimbursement` decimal(12,2) DEFAULT '0.00' COMMENT 'Safe-T索赔',
            `netcoTransaction` decimal(12,2) DEFAULT '0.00' COMMENT 'Netco交易',
            `reimbursements` decimal(12,2) DEFAULT '0.00' COMMENT '赔偿收入',
            `clawbacks` decimal(12,2) DEFAULT '0.00' COMMENT '追索收入',
            `sharedComminglingVatIncome` decimal(12,2) DEFAULT '0.00' COMMENT '混合VAT收入',
            `giftWrapCredits` decimal(12,2) DEFAULT '0.00' COMMENT '包装收入',
            `guaranteeClaims` decimal(12,2) DEFAULT '0.00' COMMENT '买家交易保障索赔额',
            `costOfPoIntegersGranted` decimal(12,2) DEFAULT '0.00' COMMENT '积分抵减收入',
            `totalSalesRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '收入退款额',
            `fbaSalesRefunds` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA销售退款额',
            `fbmSalesRefunds` decimal(12,2) DEFAULT '0.00' COMMENT 'FBM销售退款额',
            `shippingCreditRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '买家运费退款额',
            `giftWrapCreditRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '买家包装退款额',
            `chargebacks` decimal(12,2) DEFAULT '0.00' COMMENT '买家拒付',
            `costOfPoIntegersReturned` decimal(12,2) DEFAULT '0.00' COMMENT '积分抵减退回',
            `promotionalRebateRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '促销折扣退款额',
            `totalFeeRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '费用退款额',
            `sellingFeeRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '平台费退款额',
            `fbaTransactionFeeRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '发货费退款额',
            `refundAdministrationFees` decimal(12,2) DEFAULT '0.00' COMMENT '交易费用退款额',
            `otherTransactionFeeRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '其他订单费退款额',
            `refundForAdvertiser` decimal(12,2) DEFAULT '0.00' COMMENT '广告退款额',
            `pointsAdjusted` decimal(12,2) DEFAULT '0.00' COMMENT '积分费用',
            `shippingLabelRefunds` decimal(12,2) DEFAULT '0.00' COMMENT '运输标签费退款',
            `refundsQuantity` int(11) DEFAULT '0' COMMENT '退款量',
            `refundsRate` decimal(12,4) DEFAULT '0.0000' COMMENT '退款率',
            `fbaReturnsQuantity` int(11) DEFAULT '0' COMMENT '退货量',
            `fbaReturnsSaleableQuantity` int(11) DEFAULT '0' COMMENT '退货量（可售）',
            `fbaReturnsUnsaleableQuantity` int(11) DEFAULT '0' COMMENT '退货量（不可售）',
            `fbaReturnsQuantityRate` decimal(12,4) DEFAULT '0.0000' COMMENT '退货率',
            `platformFee` decimal(12,2) DEFAULT '0.00' COMMENT '平台费',
            `totalFbaDeliveryFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA发货费',
            `fbaDeliveryFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA发货费(多渠道)',
            `mcFbaDeliveryFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA发货费合计',
            `otherTransactionFees` decimal(12,2) DEFAULT '0.00' COMMENT '其他订单费用',
            `totalAdsCost` decimal(10,2) DEFAULT NULL COMMENT '广告费',
            `adsSpCost` decimal(10,2) DEFAULT NULL COMMENT 'SP广告费',
            `adsSbCost` decimal(10,2) DEFAULT NULL COMMENT 'SB广告费',
            `adsSbvCost` decimal(10,2) DEFAULT NULL COMMENT 'SBV广告费',
            `adsSdCost` decimal(10,2) DEFAULT NULL COMMENT 'SD广告费',
            `sharedCostOfAdvertising` decimal(12,2) DEFAULT '0.00' COMMENT '差异分摊',
            `promotionFee` decimal(12,2) DEFAULT '0.00' COMMENT '推广费',
            `sharedSubscriptionFee` decimal(12,2) DEFAULT '0.00' COMMENT '订阅费',
            `sharedLdFee` decimal(12,2) DEFAULT '0.00' COMMENT '秒杀费',
            `sharedCouponFee` decimal(12,2) DEFAULT '0.00' COMMENT '优惠卷',
            `sharedEarlyReviewerProgramFee` decimal(12,2) DEFAULT '0.00' COMMENT '早期评论人计划',
            `sharedVineFee` decimal(12,2) DEFAULT '0.00' COMMENT 'vine',
            `totalStorageFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA仓储费',
            `fbaStorageFee` decimal(12,2) DEFAULT '0.00' COMMENT '月度仓库费',
            `sharedFbaStorageFee` decimal(12,2) DEFAULT '0.00' COMMENT '月度仓储费差异',
            `longTermStorageFee` decimal(12,2) DEFAULT '0.00' COMMENT '长期仓储费',
            `sharedLongTermStorageFee` decimal(12,2) DEFAULT '0.00' COMMENT '长期仓储费差异',
            `sharedStorageRenewalBilling` decimal(12,2) DEFAULT '0.00' COMMENT '库存续订费用',
            `sharedFbaDisposalFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA销毁费',
            `sharedFbaRemovalFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA移除费',
            `sharedFbaInboundTransportationProgramFee` decimal(12,2) DEFAULT '0.00' COMMENT '入仓手续费',
            `sharedLabelingFee` decimal(12,2) DEFAULT '0.00' COMMENT '标签费',
            `sharedPolybaggingFee` decimal(12,2) DEFAULT '0.00' COMMENT '塑料包装费',
            `sharedBubblewrapFee` decimal(12,2) DEFAULT '0.00' COMMENT '泡沫包装费',
            `sharedTapingFee` decimal(12,2) DEFAULT '0.00' COMMENT '胶带费',
            `sharedFbaCustomerReturnFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA卖家退回费',
            `sharedFbaInboundDefectFee` decimal(12,2) DEFAULT '0.00' COMMENT '计划外服务费',
            `sharedFbaOverageFee` decimal(12,2) DEFAULT '0.00' COMMENT '超量仓储费',
            `sharedAmazonPartneredCarrierShipmentFee` decimal(12,2) DEFAULT '0.00' COMMENT '合作承运费',
            `sharedFbaInboundConvenienceFee` decimal(12,2) DEFAULT '0.00' COMMENT '合仓费',
            `sharedItemFeeAdjustment` decimal(12,2) DEFAULT '0.00' COMMENT '库存调整费用',
            `sharedOtherFbaInventoryFees` decimal(12,2) DEFAULT '0.00' COMMENT '其他仓储费',
            `fbaStorageFeeAccrual` decimal(12,2) DEFAULT '0.00' COMMENT '月仓储费-本月计提',
            `fbaStorageFeeAccrualDifference` decimal(12,2) DEFAULT '0.00' COMMENT '月仓储费-上月冲销',
            `longTermStorageFeeAccrual` decimal(12,2) DEFAULT '0.00' COMMENT '长期仓储费-本月计提',
            `longTermStorageFeeAccrualDifference` decimal(12,2) DEFAULT '0.00' COMMENT '长期仓储费-上月冲销',
            `sharedFbaIntegerernationalInboundFee` decimal(12,2) DEFAULT '0.00' COMMENT 'FBA国际物流货运费',
            `adjustments` decimal(12,2) DEFAULT '0.00' COMMENT '调整费用',
            `totalPlatformOtherFee` decimal(12,2) DEFAULT '0.00' COMMENT '平台其他费',
            `shippingLabelPurchases` decimal(12,2) DEFAULT '0.00' COMMENT '运输标签费',
            `fbaInventoryCreditQuantity` int DEFAULT '0' COMMENT '赔偿量',
            `disposalQuantity` int DEFAULT '0' COMMENT '销毁量',
            `removalQuantity` int DEFAULT '0' COMMENT '移除量',
            `others` decimal(10,2) DEFAULT NULL COMMENT '平台收入中其他收入的其他费用',
            `sharedCarrierShippingLabelAdjustments` decimal(12,2) DEFAULT '0.00' COMMENT '承运人装运标签调整费',
            `sharedLiquidationsFees` decimal(12,2) DEFAULT '0.00' COMMENT '清算费',
            `sharedManualProcessingFee` decimal(12,2) DEFAULT '0.00' COMMENT '人工处理费用',
            `sharedOtherServiceFees` decimal(12,2) DEFAULT '0.00' COMMENT '其他服务费',
            `sharedMfnPostageFee` decimal(12,2) DEFAULT '0.00' COMMENT '多渠道邮资费',
            `totalSalesTax` decimal(12,2) DEFAULT '0.00' COMMENT '销售税',
            `tcsIgstCollected` decimal(12,2) DEFAULT '0.00' COMMENT 'TCS-IGST',
            `tcsSgstCollected` decimal(12,2) DEFAULT '0.00' COMMENT 'TCS-SGST',
            `tcsCgstCollected` decimal(12,2) DEFAULT '0.00' COMMENT 'TCS-CGST',
            `sharedComminglingVatExpenses` decimal(12,2) DEFAULT '0.00' COMMENT '混合VAT',
            `taxCollected` decimal(12,2) DEFAULT '0.00' COMMENT 'VAT/GST',
            `taxCollectedProduct` decimal(12,2) DEFAULT '0.00' COMMENT '商品价格税',
            `taxCollectedDiscount` decimal(12,2) DEFAULT '0.00' COMMENT '促销折扣税',
            `taxCollectedShipping` decimal(12,2) DEFAULT '0.00' COMMENT '买家运费税',
            `taxCollectedGiftWrap` decimal(12,2) DEFAULT '0.00' COMMENT '礼品包装税',
            `sharedTaxAdjustment` decimal(12,2) DEFAULT '0.00' COMMENT '商品税调整',
            `salesTaxRefund` decimal(12,2) DEFAULT '0.00' COMMENT '销售税退款额',
            `tcsIgstRefunded` decimal(12,2) DEFAULT '0.00' COMMENT 'TCS-IGST',
            `tcsSgstRefunded` decimal(12,2) DEFAULT '0.00' COMMENT 'TCS-SGST',
            `tcsCgstRefunded` decimal(12,2) DEFAULT '0.00' COMMENT 'TCS-CGST',
            `taxRefunded` decimal(12,2) DEFAULT '0.00' COMMENT 'VAT/GST',
            `taxRefundedProduct` decimal(12,2) DEFAULT '0.00' COMMENT '商品价格税退款',
            `taxRefundedDiscount` decimal(12,2) DEFAULT '0.00' COMMENT '促销折扣税退款',
            `taxRefundedShipping` decimal(12,2) DEFAULT '0.00' COMMENT '买家运费税退款',
            `taxRefundedGiftWrap` decimal(12,2) DEFAULT '0.00' COMMENT '礼品包装税退款',
            `salesTaxWithheld` decimal(12,2) DEFAULT '0.00' COMMENT '市场税',
            `refundTaxWithheld` decimal(12,2) DEFAULT '0.00' COMMENT '市场税退款额',
            `tdsSection194ONet` decimal(12,2) DEFAULT '0.00' COMMENT '混合网路费用',
            `cgPriceTotal` decimal(12,2) DEFAULT '0.00' COMMENT '采购成本',
            `cgPriceAbsTotal` decimal(12,2) DEFAULT '0.00' COMMENT '采购成本绝对值',
            `hasCgPriceDetail` int(11) DEFAULT '0' COMMENT '是否有采购成本明细',
            `cgUnitPrice` decimal(12,2) DEFAULT '0.00' COMMENT '采购均价',
            `proportionOfCg` decimal(12,4) DEFAULT '0.0000' COMMENT '采购占比',
            `cgTransportCostsTotal` decimal(12,2) DEFAULT '0.00' COMMENT '头程成本',
            `hasCgTransportCostsDetail` int(11) DEFAULT '0' COMMENT '是否有物流（头程）成本明细',
            `cgTransportUnitCosts` decimal(12,2) DEFAULT '0.00' COMMENT '头程均价',
            `proportionOfCgTransport` decimal(12,4) DEFAULT '0.0000' COMMENT '头程占比',
            `totalCost` decimal(12,2) DEFAULT '0.00' COMMENT '合计成本',
            `proportionOfTotalCost` decimal(12,4) DEFAULT '0.0000' COMMENT '合计成本占比',
            `cgOtherCostsTotal` decimal(12,2) DEFAULT '0.00' COMMENT '其他成本',
            `cgOtherUnitCosts` decimal(12,2) DEFAULT '0.00' COMMENT '其他均价',
            `hasCgOtherCostsDetail` int(11) DEFAULT '0' COMMENT '是否有其他成本明细',
            `proportionOfCgOtherCosts` decimal(12,4) DEFAULT '0.0000' COMMENT '其他成本占比',
            `grossProfit` decimal(12,2) DEFAULT '0.00' COMMENT '毛利润',
            `grossProfitTax` decimal(12,4) DEFAULT '0.0000' COMMENT '毛利润税费',
            `roi` decimal(12,4) DEFAULT '0.0000' COMMENT 'ROI',
            `grossProfitIncome` decimal(12,4) DEFAULT '0.0000' COMMENT '毛利润收入',
            `grossRate` decimal(12,4) DEFAULT '0.0000' COMMENT '毛利率',
            `customOrderFee` decimal(12,2) DEFAULT '0.00' COMMENT '订单其他费',
            `customOrderFeePrincipal` decimal(12,2) DEFAULT '0.00' COMMENT '站外推广费-本金',
            `customOrderFeeCommission` decimal(12,2) DEFAULT '0.00' COMMENT '站外推广费-佣金',
            `key1` decimal(12,2) DEFAULT '0.00' COMMENT '（调整的采购成本）弃置清算亚马逊破损成本',
            `key2` decimal(12,2) DEFAULT '0.00' COMMENT '服务商费用',
            `key3` decimal(12,2) DEFAULT '0.00' COMMENT '店铺相关费用',
            `key4` decimal(12,2) DEFAULT '0.00' COMMENT '测评费+推广费-推广部返款',
            `key5` decimal(12,2) DEFAULT '0.00' COMMENT '测评费+推广费-服务商返款',
            `key6` decimal(12,2) DEFAULT '0.00' COMMENT '预留资金',
            `key7` decimal(12,2) DEFAULT '0.00' COMMENT '调整的头程成本）FBM多渠道独立站调整',
            `key8` decimal(12,2) DEFAULT '0.00' COMMENT '欧洲VAT税金-第二版',
            `key9` decimal(12,2) DEFAULT '0.00' COMMENT '（调整的尾程成本）站内多渠道尾程成本调整',
            `key10` decimal(12,2) DEFAULT '0.00' COMMENT '物流商赔偿收入',
            `key11` decimal(12,2) DEFAULT '0.00' COMMENT '站外广告费',
            `key12` decimal(12,2) DEFAULT '0.00' COMMENT '物流商赔偿收入',
            `key13` decimal(12,2) DEFAULT '0.00' COMMENT '福利政策-采购成本',
            `key14` decimal(12,2) DEFAULT '0.00' COMMENT '福利政策-头程成本',
            `key15` int(11) DEFAULT '0' COMMENT 'fbm_local_numFBA在库总库存数量',
            `key16` int(11) DEFAULT '0' COMMENT 'fbm_local_overseas_num（FBA在途+在库+海外仓）总库存数量',
            `key17` decimal(12,2) DEFAULT '0.00' COMMENT 'fbm_local_overseas_price（FBA在途+在库+海外仓）总库存采购成本+头程成本',
            `key18` decimal(12,2) DEFAULT '0.00' COMMENT '福利政策-其他成本',
            `key19` decimal(12,2) DEFAULT '0.00' COMMENT '海外营销费用',
            `key20` decimal(12,2) DEFAULT '0.00' COMMENT '站内数据调整',
            `key21` decimal(12,2) DEFAULT '0.00' COMMENT '调整的数量',
            PRIMARY KEY (`id`),
            UNIQUE KEY `sid` (`sid`,`asin`,`msku`,`parentAsin`,`localSku`,`countryCode`,`project_id`,`yunying_id`,`supplier_id`,`reportDateMonth`) USING BTREE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='月度汇总统计';");
        //自定义表的结算表
        //self::updateTableMonthCustom($dbF,$custom_name,$year);
        //自定义字段的值的表
        $dbF->query("CREATE TABLE IF NOT EXISTS `oa_f_$custom_val`  (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `m_date` varchar(10) NOT NULL COMMENT '月份',
            `sid` varchar(100) NOT NULL COMMENT 'sid',
            `asin` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
            `p_asin` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
            `sku` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
            `msku` varchar(255) NOT NULL COMMENT 'MSku',
            `project_id` int(11) NOT NULL,
            `yunying_id` int(11) NOT NULL,
            `supplier_id` int(11) NOT NULL DEFAULT '0' COMMENT 'oa供应商id',
            `country_code` varchar(10) NOT NULL COMMENT '国家编码',
            `custom_id` int(11) NOT NULL COMMENT '自定义字段id',
            `custom_name` varchar(255) NOT NULL COMMENT '自定义字段名',
            `custom_val` decimal(14,2) NOT NULL DEFAULT '0.00' COMMENT '字段值',
            `created_time` datetime DEFAULT NULL,
            `updated_time` datetime DEFAULT NULL,
            `val_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1值，2百分数',
            `m_data_id` int(11) NOT NULL DEFAULT '0' COMMENT '',
            PRIMARY KEY (`id`),
            KEY `m_date` (`m_date`,`sid`,`asin`,`p_asin`,`sku`,`msku`,`project_id`,`yunying_id`,`country_code`,`custom_id`),
            KEY `m_date_2` (`m_date`,`custom_id`),
            KEY `asin` (`asin`,`country_code`,`custom_id`) USING BTREE,
            KEY `p_asin` (`p_asin`,`country_code`,`custom_id`),
            KEY `sku` (`sku`,`custom_id`)
        )  ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='oa自定义字段对应的值($year)';");
    }
    //自定义表的结算表
    private static function updateTableMonthCustom($db,$custom_name,$year) {
        //可用的列
        $list = $db->table('custom_column')
            ->where("where is_delete = 0 and rules<>'[]' and show_type <> 2")
            ->field('id,show_type,column_name')
            ->list();
        $query = "SHOW TABLES LIKE 'oa_f_{$custom_name}'";
        $result = $db->queryAll($query);
        if (!count($result)) {
            // 表 b 不存在，创建表 b
            $createTableQuery = "CREATE TABLE `oa_f_{$custom_name}` (
                `id` int unsigned NOT NULL AUTO_INCREMENT,
                `table_month_id` int NOT NULL,
                `custom_id` int NOT NULL,
            ";
            // 获取 a 表的结构，找到所有以 'key_' 开头的字段
            foreach ($list as $v) {
                if ($v['show_type'] == 3) {
                    //值
                    $createTableQuery .= "`oa_key_{$v['id']}` int DEFAULT '0' COMMENT '{$v['column_name']}',";
                } else {
                    //金额
                    $createTableQuery .= "`oa_key_{$v['id']}` decimal(12,2) DEFAULT '0.00' COMMENT '{$v['column_name']}',";
                }
            }
            $createTableQuery .= "PRIMARY KEY (`id`) USING BTREE) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='oa自定义字段计算值($year)';";
            $db->query($createTableQuery);
        } else {
            //得到b表的所有字段
            $keyFieldsQuery = "SHOW COLUMNS FROM `oa_f_{$custom_name}`";
            $keyFieldsResult = $db->queryAll($keyFieldsQuery);
            $fieldNameArray = array_column($keyFieldsResult,'Field');
            $add_column = [];
            foreach ($list as $v) {
                $key_ = "oa_key_{$v['id']}";
                if (!in_array($key_,$fieldNameArray)) {
                    if ($v['show_type'] == 3) {
                        //值
                        $add_column[] = "ALTER TABLE oa_f_{$custom_name}  ADD COLUMN oa_key_{$v['id']} int DEFAULT '0' COMMENT '{$v['column_name']}'";
                    } else {
                        //金额
                        $add_column[] = "ALTER TABLE oa_f_{$custom_name}  ADD COLUMN oa_key_{$v['id']} decimal(12,2) DEFAULT '0.00' COMMENT '{$v['column_name']}'";
                    }
                }
            }
            if (count($add_column)) {
                array_unshift($add_column, "SET FOREIGN_KEY_CHECKS=0");
                $add_column[] = 'SET FOREIGN_KEY_CHECKS=1';
                $add_column_str = implode(';',$add_column).';';
                $db->query($add_column_str);
            }
        }
    }
}