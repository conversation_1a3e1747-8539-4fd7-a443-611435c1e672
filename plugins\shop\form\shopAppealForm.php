<?php
namespace plugins\shop\form;

use core\lib\Form;

class shopAppealForm extends Form {
    public function rules(): array
    {
        return [
            'shop_id' => ['required', 'integer'],
            'issue_type' => ['required', 'string'],
            'warning_screenshot' => ['string'],
            'performance_screenshot' => ['string'],
            'reason_analysis' => ['required', 'string'],
            'shop_status' => ['required', 'string'],
            'frozen_funds' => ['numeric'],
            'currency' => ['string'],
            'inventory' => ['integer'],
            'complaint_type' => ['required', 'string'],
            'reminder_interval' => ['integer'],
            'follower_id' => ['integer'],
        ];
    }

    public function messages(): array
    {
        return [
            'shop_id.required' => '店铺ID不能为空',
            'issue_type.required' => '问题类型不能为空',
            'reason_analysis.required' => '原因分析不能为空',
            'shop_status.required' => '店铺状态不能为空',
            'complaint_type.required' => '申诉类型不能为空',
        ];
    }

    public function scenes(): array
    {
        return [
            'create' => ['shop_id', 'issue_type', 'warning_screenshot', 'performance_screenshot', 'reason_analysis', 
                        'shop_status', 'frozen_funds', 'currency', 'inventory', 'complaint_type', 'reminder_interval'],
            'update' => ['reason_analysis', 'shop_status', 'frozen_funds', 'currency', 'inventory'],
            'assign' => ['follower_id'],
            'follow' => ['reason_analysis'],
            'review' => ['status', 'review_result'],
        ];
    }
}
