<?php

namespace plugins\salary\Controller;

use admin\models\qwuserModel;
use plugins\salary\models\salarySchemeModel;
use plugins\salary\models\userRolesModel;
use core\lib\db\dbMysql;
use core\lib\db\dbSMysql;
use plugins\salary\models\userModel;

class salarySchemeController
{
    // 获取算薪方案列表
    public function getList()
    {
        $paras_list = array('scheme_name', 'page', 'page_size', 'light_query');
        $param = arrangeParam($_GET, $paras_list);
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        $sdb = dbSMysql::getInstance();
        $db = dbMysql::getInstance();

        $sdb->table('salary_scheme');
        $sdb->where('where is_delete = 0');
        if ($param['scheme_name']) {
            $sdb->andWhere('scheme_name like :scheme_name', ['scheme_name' => '%' . $param['scheme_name'] . '%']);
        }
        $sdb->order('id desc');

        if ($param['light_query']) {
            $list = $sdb->list();
            $ret = [];
            foreach ($list as $item) {
                $ret[] = [
                    'id' => $item['id'],
                    'scheme_name' => $item['scheme_name']
                ];
            }
            returnSuccess($ret);
        }

        $data = $sdb->pages($page, $limit);

        if (empty($data['list'])) returnSuccess($data);

        $user_roles = userRolesModel::getRolesAndUsers();
        $department_users = qwuserModel::getDepartmentUsers();
        $users = $db->table('qwuser', 'u')
            ->field('u.id as user_id,u.wid, u.wname, ui.user_status')
            ->leftJoin('user_info', 'ui', 'u.id=ui.qwuser_id')
            ->list();
        $users = array_column($users, null, 'user_id');

        foreach ($data['list'] as &$item) {
            $item['operator_name'] = $users[$item['operator']]['wname'];
            $item['attach'] = $item['attach'] ? json_decode($item['attach'], true) : null;
            $item['config'] = $item['config'] ? json_decode($item['config'], true) : null;
            $scheme_users = salarySchemeModel::getSchemeUsers($item['attach'], $users, $user_roles, $department_users);
            $item['scheme_users'] = array_values($scheme_users);
        }
        unset($item);

        returnSuccess($data);
    }

    // 检查算薪方案下人员
    public function checkUser()
    {
        $paras_list = array('attach', 'id');
        $length_data = [];
        $request_list = ['attach' => '算薪对象'];
        $param = arrangeParam($_POST, $paras_list, $request_list, $length_data);

        $db = dbMysql::getInstance();
        // 用户基本信息
        $users = $db->table('qwuser', 'u')
            ->field('u.id as user_id,u.wid, u.wname, ui.user_status')
            ->leftJoin('user_info', 'ui', 'u.id=ui.qwuser_id')
            ->list();
        $users = array_column($users, null, 'user_id');
        $user_roles = userRolesModel::getRolesAndUsers();
        $department_users = qwuserModel::getDepartmentUsers();

        // 获取当前算薪方案的用户
        $attach = json_decode($param['attach'], true) ?: [];
        $attach_users = salarySchemeModel::getSchemeUsers($attach, $users, $user_roles, $department_users);
        $attach_user_ids = array_keys($attach_users);

        // 获取所有算新方案
        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_scheme');
        $sdb->where('where is_delete = 0');
        $list = $sdb->list();

        $error = [];
        $error_users = [];
        foreach ($list as $item) {
            if (isset($param['id']) && $param['id'] == $item['id']) {
                continue;
            }
            $attach = json_decode($item['attach'], true) ?: [];
            $item_users = salarySchemeModel::getSchemeUsers($attach, $users, $user_roles, $department_users);
            $item_user_ids = array_keys($item_users);
            // 判断是否有交集
            $intersect = array_intersect($attach_user_ids, $item_user_ids);
            if (!empty($intersect)) {
                $intersect_users = array_intersect_key($attach_users, array_flip($intersect));
                $intersect_users = array_values($intersect_users);
                foreach ($intersect_users as $user) {
                    $error_users[$user['user_id']] = $user;
                }
                $error[] = [
                    'scheme_name' => $item['scheme_name'],
                    'users' => $intersect_users
                ];
            }
        }

        returnSuccess(['error' => $error, 'error_users' => array_values($error_users)]);

    }

    // 新增/编辑算薪方案
    public function add()
    {
        $paras_list = array('id', 'scheme_name', 'scheme_type', 'attach', 'config');
        $length_data = ['scheme_name' => ['name' => '方案名称', 'length' => 20]];
        $request_list = ['scheme_name' => '方案名称', 'scheme_type' => '算薪规则', 'attach' => '范围', 'config' => '薪资项配置'];
        $param = arrangeParam($_POST, $paras_list, $request_list, $length_data);

        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_scheme');

        // 判断算薪方案是否存在
        if ($param['id']) {
            $sdb->where('where id = :id and is_delete = 0', ['id' => $param['id']]);
            $is_exist = $sdb->one();
            if (!$is_exist) {
                returnError('方案不存在');
            }
        }

        // 判断算薪方案名称是否重复
        $sdb->table('salary_scheme');
        $sdb->where('where scheme_name = :scheme_name and is_delete = 0', ['scheme_name' => $param['scheme_name']]);
        if ($param['id']) {
            $sdb->andWhere('id != :id', ['id' => $param['id']]);
        }
        $is_exist = $sdb->one();
        if ($is_exist) returnError('方案名称重复');

        salarySchemeModel::checkScheme($param);

        $data = [
            'scheme_name' => $param['scheme_name'],
            'operator'    => userModel::$qwuser_id,
            'scheme_type' => $param['scheme_type'],
            'attach'      => $param['attach'],
            'config'      => $param['config'],
        ];
        if ($param['id']) {
            $id = $param['id'];
            $sdb->where('where id = :id and is_delete = 0', ['id' => $param['id']]);
            $res = $sdb->update($data);
            returnSuccess([], '编辑成功');
        } else {
            $res = $sdb->insert($data);
            $id = $res;
            if (!$res) {
                returnError('新增失败');
            }
        }
        returnSuccess([], '保存成功');
    }

    // 删除算薪方案
    public function delete()
    {
        $paras_list = array('id');
        $param = arrangeParam($_POST, $paras_list);
        $id = $param['id'];
        if (!$id) returnError('id不能为空');
        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_scheme');
        $sdb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $is_exist = $sdb->one();
        if (!$is_exist) returnError('方案不存在');

        $data = [
            'is_delete' => 1,
            'operator'   => userModel::$qwuser_id
        ];
        $sdb->table('salary_scheme');
        $sdb->where('where id = :id', ['id' => $id]);
        $res = $sdb->update($data);
        if ($res) {
            returnSuccess([], 'success');
        } else {
            returnError('fail');
        }
    }

    // 获取算薪方案详情
    public function getDetail()
    {
        $paras_list = array('id');
        $param = arrangeParam($_GET, $paras_list);
        $id = $param['id'];
        if (!$id) returnError('id不能为空');
        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_scheme');
        $sdb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $detail = $sdb->one();
        if (!$detail) returnError('方案不存在');

        $user_id = $detail['operator'];
        $db = dbMysql::getInstance();
        $user = $db->table('qwuser')->field('id,wid,wname,avatar')->where('where id = :id', ['id' => $user_id])->one();

        $detail['attach'] = $detail['attach'] ? json_decode($detail['attach'], true) : null;
        $detail['operator_name'] = $user['wname'];
        $detail['config'] = $detail['config'] ? json_decode($detail['config'], true) : null;
        $user_ids = salarySchemeModel::getSchemeUsers($detail['attach']);

        $users = [];
        if (!empty($user_ids)) {
            $db = dbMysql::getInstance();
            $users = $db->table('qwuser')
                ->whereIn('id', array_column($user_ids, 'user_id'))
                ->field('id, wname, avatar')
                ->list();
        }
        $detail['scheme_users'] = $users;

        returnSuccess($detail);
    }

    // 获取固定薪资项
    public function getSchemeItem() {
        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_title');
        $list = $sdb->list();

        returnSuccess($list);
    }

}