<?php
namespace financial\controller;

use core\lib\db\dbFMysql;
use financial\form\goodsInformationForm;

class goodsInformationController
{
    public static function getList() {
        // 定义所需参数列表
        $paras_list = ['page', 'page_size', 'input', 'category'];
        // 组织参数
        $param = arrangeParam($_POST, $paras_list);
        // 设置默认分页参数
        $page = !empty($param['page']) ? (int)$param['page'] : 1;
        $page_size = !empty($param['page_size']) ? (int)$param['page_size'] : 10;
//        $search = !empty($param['input']) ? $param['input'] : null;

        //增加反义字符，非法字符的处理
        $search = !empty($param['input'])
            ? filter_var(addslashes($param['input']), FILTER_SANITIZE_STRING)
            : null;


        $categories = !empty($param['category']) ? json_decode($param['category'], true) : [];
        if (!empty($categories)) {
            $cid = dbFMysql::getInstance();

            // 构建占位符和参数数组
            $placeholders = implode(',', array_fill(0, count($categories), '?'));
            $params = array_values($categories);

            // 批量查询所有匹配的cid
            $cids = $cid->table('goods_category')
                ->where("where is_delete = 0 AND id IN ($placeholders)", $params)
                ->field('cid')
                ->list();

            // 提取结果中的cid值
            $results = array_column($cids, 'cid');
        }
        try {
            // 获取数据库实例
            $db = dbFMysql::getInstance();

            // 初始化返回数据
            $response_data = [
                'page' => $page,
                'page_size' => $page_size,
                'total' => 0,
                'new_time' => '',
                'list' => []
            ];
            // 构建查询条件
            if (!empty($search)){
                $db->table('goods')
                    ->where('where is_delete = 0');

            } else{
                $db->table('goods');
            }

            // 如果有搜索词
            if (!empty($search)) {
                $params = [
                    'sku' => '%' . $search . '%',
                    'product_name' => '%' . $search . '%',
                    'supplier_name' => '%' . $search . '%'
                ];
                //如果有分类

                if (!empty($param['category'])){

                    $db->where('where (sku LIKE :sku  or product_name LIKE :product_name or supplier_name LIKE :supplier_name)', $params);

                } else{

                     $db->where('where (sku LIKE :sku  or product_name LIKE :product_name or supplier_name LIKE :supplier_name)', $params);
                     $data = $db->pages($page, $page_size);
                }

            }

            //如果有分类
            if (!empty($param['category'])){

                $para = array_values($results);

                $db->whereIn('cid',$para);

                $data = $db->pages($page, $page_size);

            }

            //如果搜索词和分类都没有
            if (empty($search) && empty($param['category'])){

                $data = $db->pages($page, $page_size);

            }
            // 获取最新更新时间
            $response_data['new_time'] = $db->table('goods')
                ->field('MAX(syn_time) as new_time')
                ->one()['new_time'];

            $response_data['list'] = $data['list'];
            $response_data['total'] = $data['total'];



            // 返回成功信息和产品数据
            returnSuccess($response_data, "获取数据成功");

        } catch (\Exception $e) {
            // 错误处理
            returnError("获取数据失败: " . $e->getMessage());
        }
    }
    //更新商品数据
    public function synGoods() {
        $redis_key1 = 'oa_syn_goods';
        $redis_key2 = 'oa_syn_goods_detail';
        $redis = (new \core\lib\predisV())::$client;
//        $redis->del($redis_key1);
//        $redis->del($redis_key2);
        if ($redis->exists($redis_key1)) {
            $data = json_decode($redis->get($redis_key1),true);
            returnError($data['message']);
        }
        if ($redis->exists($redis_key2)) {
            $data = json_decode($redis->get($redis_key2),true);
            returnError($data['message']);
        }
        $targetFile = SELF_FK.'/task/shell/lingxing_goods_get.sh > /dev/null 2>&1 &';
        // 执行命令行
        shell_exec($targetFile);
        returnSuccess([],'开始更新');
    }
}
