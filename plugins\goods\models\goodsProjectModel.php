<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/21 16:06
 */

namespace  plugins\goods\models;

use plugins\goods\form\goodsProjectFrom;
use core\lib\config;
use core\lib\db\dbMysql;

class goodsProjectModel
{
    //修改需要记录的字段
    public static array $field_log_lsit = [
        //'id'=>'ID',
        'status'=>'项目状态',
        'remark'=>'备注',
        'current_index'=>'当前所在节点的角标'
    ];

    public static string $field = 'id,status,remark,current_index';

    static public function setUpdateLog($goods_id, $tpl_name, $lod_data=[],$data = []) :void
    {
        if (count($data)) {
            $mgs = '修改项目【'.$tpl_name.'】信息，';
            foreach ($data as $k=>$v) {
                if (isset($lod_data[$k]) && ($lod_data[$k] != $v) && isset(self::$field_log_lsit[$k])) {
                    if ($k == 'status') {
                        $mgs .= self::$field_log_lsit[$k].'：【'.config::getDataName('new_goods_status',$data[$k]).'】，';
                    } else if ($k == 'manage_info') {
                        $manage_info = json_decode($data[$k],true);
                        $manage_str = '无';
                        if (!empty($manage_info)) {
                            $manage_arry = array_column($manage_info,'qw_name');
                            $manage_str = implode(',', $manage_arry);
                        }
                        $mgs .= self::$field_log_lsit[$k].'：【'.$manage_str.'】，';
                    } else {
                        $mgs .= self::$field_log_lsit[$k].'：【'.$data[$k].'】，';
                    }
                    $has_chaunge = 1;
                }
            }
            if ($has_chaunge == 1) {
                $mgs = trim($mgs, '，');
                \core\lib\log::newGoodsLog($goods_id, 0,$mgs,0);
            }
        }
    }

    static public function  checkNodeStatusChange($param,$event_data){
        if (in_array($event_data['event_type'], [3,10,13,14])) {
            if (!isset($event_data['file_data'])) {
                SetReturn(-1, '请先上传文档');
            }
        } else {
            switch ($event_data['event_type']) {
                case 1:
                    //信息补全
                    if (!isset($event_data['filled_form_id'])) {
                        SetReturn(-1, '请先补全资料');
                    }
                    //生成本地pdf文件
                    $mpdf_data = goodsProjectFrom::saveFormPdf($event_data['filled_form_id'],$event_data['form_id']);
                    //保存图片
                    $attr_pdf_url_id = self::savePdfUrl($param, $mpdf_data);
                    $event_data['project_file_id'] = $attr_pdf_url_id;
                    break;
                case 5:
                    //查询已生成的硬件测试数据
                    $db = dbMysql::getInstance();
                    $db->table('project_file')->where("where is_delete = 0 and goods_id = {$param['goods_id']} and project_id = {$param['id']} and event_type = 5")->field('wid');
                    $pfd_file = $db->list();
                    $wids = array_column($pfd_file,'wid');
                    $event_manage = json_decode($event_data['manage_info'], true);
                    foreach ($event_manage as $v) {
                        if (!in_array($v['wid'], $wids)) {
                            SetReturn(-1, "【{$v['wname']}】还未完成【硬件功能测试】");
                        }
                    }
                    break;
                case 6:
                    //规格书答复函生成
                    if (!isset($event_data['project_file_id'])) {
                        SetReturn(-1, '请先生成答复函');
                    }
                    break;
                case 7;
                    $db = dbMysql::getInstance();
                    $db->table('project_file')->where("where is_delete = 0 and goods_id = {$param['goods_id']} and project_id = {$param['id']} and event_type = 7")->field('wid');
                    $pfd_file = $db->one();
                    //质检标准书生成
                    if (!$pfd_file) {
                        SetReturn(-1, '请先生成检测标准书');
                    }
                    break;
                case 8;
                    //收样存档
                    if (empty($event_data['goods_info'])) {
                        SetReturn(-1, '请先填写建档信息');
                    }
                    break;
            }
        }

        return $event_data;
    }

    //将pfd的文件路径保存到表
    static public function savePdfUrl($param,$path,$event_type) {
        //新增文件
        $db = dbMysql::getInstance();
        $db->table('project_file');
        //事件负责有多个，根据事件类型每个负责人只生成一个文件。
        $db->where('where goods_id = :goods_id and project_id = :project_id and user_id = :user_id and event_type = :event_type',['project_id'=>$param['id'],'goods_id'=>$param['goods_id'],'user_id'=>$user['id'],'event_type'=>$event_type]);
        $pdf_data = $db->one();

        if ($pdf_data) {
            $save_data = [
                'wid'=>userModel::$wid,
                'project_index'=>$param['index_id'],
                'event_index'=>$param['event_index_id'],
                'flow_path_id'=>$param['flow_path_id'],
                'src'=>$path['path'],
                'filename'=>$path['title'],
                'extension'=>'pdf',
                'updated_at'=>date('Y-m-d H:i:s')
            ];
            $id = $pdf_data['id'];
            $db->where("where id = {$id}");
            $db->update($save_data);
        } else {
            $insert_data = [
                'user_id'=>userModel::$qwuser_id,
                'wid'=>userModel::$wid,
                'goods_id'=>$param['goods_id'],
                'project_id'=>$param['id'],
                'project_index'=>$param['index_id'],
                'event_index'=>$param['event_index_id'],
                'flow_path_id'=>$param['flow_path_id'],
                'created_at'=>date('Y-m-d H:i:s'),
                'src'=>$path['path'],
                'filename'=>$path['title'],
                'extension'=>'pdf',
                'event_type'=>$event_type,
            ];
            $id = $db->insert($insert_data);
        }
        return $id;
    }

}