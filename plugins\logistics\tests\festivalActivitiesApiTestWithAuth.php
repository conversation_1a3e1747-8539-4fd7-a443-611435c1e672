<?php
/**
 * 节日活动API接口测试（带认证）
 * @author: AI Assistant
 * @Time: 2025/5/29
 */

class FestivalActivitiesApiTestWithAuth
{
    private $baseUrl = 'http://oa.ywx.com';
    private $loginUrl = 'http://oa.ywx.com/admin/login/login';
    private $apiUrl = 'http://oa.ywx.com/logistics/festivalActivity';
    private $token = '';
    private $testData = [];
    
    public function __construct()
    {
        echo "节日活动API接口测试（带认证）开始...\n";
    }

    /**
     * 运行所有API测试
     */
    public function runAllApiTests()
    {
        echo "\n=== 登录认证 ===\n";
        $this->login();
        
        if (empty($this->token)) {
            echo "❌ 登录失败，无法继续测试\n";
            return;
        }
        
        echo "\n=== 第一轮API测试 ===\n";
        $this->testGetBaseConfig();
        $this->testCreateActivity();
        $this->testGetList();
        $this->testGetDetail();
        $this->testUpdateActivity();
        $this->testUpdateStatus();
        $this->testGetActiveActivities();
        $this->testGetActivitiesBySite();
        $this->testCheckNameExists();
        $this->testDeleteActivity();
        
        echo "\n=== 第二轮API测试（边界值和异常情况）===\n";
        $this->testBoundaryValues();
        $this->testErrorHandling();
        $this->testInputValidation();
        
        echo "\n所有API测试完成！\n";
    }

    /**
     * 用户登录获取token
     */
    private function login()
    {
        echo "正在登录...\n";
        
        try {
            $response = $this->sendRequest($this->loginUrl, 'POST', [
                'account' => '魏雪韵',
                'password' => '123456'
            ], false); // 登录不需要token
            
            if ($response['code'] == 200) {
                $data = $response['data'];
                if (isset($data['data']['token'])) {
                    $this->token = $data['data']['token'];
                    echo "✓ 登录成功，Token: " . substr($this->token, 0, 20) . "...\n";
                } else {
                    echo "✗ 登录响应中未找到token\n";
                    echo "响应内容: " . $response['body'] . "\n";
                }
            } else {
                echo "✗ 登录失败，HTTP状态: {$response['code']}\n";
                echo "响应内容: " . $response['body'] . "\n";
            }
        } catch (Exception $e) {
            echo "✗ 登录异常: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 发送HTTP请求
     */
    private function sendRequest($url, $method = 'GET', $data = [], $needAuth = true)
    {
        $ch = curl_init();
        
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        // 设置请求头
        $headers = ['Content-Type: application/x-www-form-urlencoded'];
        if ($needAuth && !empty($this->token)) {
            $headers[] = 'Authorization: ' . $this->token;
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if (!empty($data)) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            }
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception("CURL Error: " . $error);
        }
        
        return [
            'code' => $httpCode,
            'body' => $response,
            'data' => json_decode($response, true)
        ];
    }

    /**
     * 测试获取基础配置
     */
    public function testGetBaseConfig()
    {
        echo "\n1. 测试获取基础配置API...\n";
        
        try {
            $response = $this->sendRequest($this->apiUrl . '/getBaseConfig');
            
            if ($response['code'] == 200) {
                $data = $response['data'];
                if (isset($data['data']['festival_types']) && 
                    isset($data['data']['sites']) && 
                    isset($data['data']['status_options'])) {
                    echo "✓ 基础配置获取成功\n";
                    echo "  - 节日类型数: " . count($data['data']['festival_types']) . "\n";
                    echo "  - 站点数: " . count($data['data']['sites']) . "\n";
                } else {
                    echo "✗ 基础配置数据不完整\n";
                }
            } else {
                echo "✗ 基础配置获取失败，HTTP状态: {$response['code']}\n";
                if (isset($response['data']['msg'])) {
                    echo "  错误信息: {$response['data']['msg']}\n";
                }
            }
        } catch (Exception $e) {
            echo "✗ 基础配置测试异常: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 测试创建活动API
     */
    public function testCreateActivity()
    {
        echo "\n2. 测试创建活动API...\n";
        
        try {
            $data = [
                'name' => 'API测试活动_' . time(),
                'festival_type' => '春节',
                'start_date' => '2025-02-01',
                'end_date' => '2025-02-15',
                'description' => 'API测试用活动',
                'sites_config' => json_encode([
                    'US' => ['discount_rate' => 0.15, 'free_shipping_threshold' => 50],
                    'UK' => ['discount_rate' => 0.12, 'free_shipping_threshold' => 40]
                ]),
                'stock_rules' => json_encode([
                    'lead_time_days' => 30,
                    'safety_stock_multiplier' => 1.5,
                    'categories' => ['electronics', 'fashion']
                ]),
                'status' => 1,
            ];

            $response = $this->sendRequest($this->apiUrl . '/create', 'POST', $data);
            
            if ($response['code'] == 200) {
                $responseData = $response['data'];
                if (isset($responseData['data']['id'])) {
                    $this->testData['created_id'] = $responseData['data']['id'];
                    echo "✓ 创建活动成功，ID: {$responseData['data']['id']}\n";
                } else {
                    echo "✗ 创建活动失败，未返回ID\n";
                    echo "  响应: " . json_encode($responseData) . "\n";
                }
            } else {
                echo "✗ 创建活动失败，HTTP状态: {$response['code']}\n";
                if (isset($response['data']['msg'])) {
                    echo "  错误信息: {$response['data']['msg']}\n";
                }
            }
        } catch (Exception $e) {
            echo "✗ 创建活动测试异常: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 测试获取列表API
     */
    public function testGetList()
    {
        echo "\n3. 测试获取列表API...\n";
        
        try {
            // 基本列表测试
            $response = $this->sendRequest($this->apiUrl . '/getList');
            
            if ($response['code'] == 200) {
                $data = $response['data'];
                if (isset($data['data']['list']) && isset($data['data']['total'])) {
                    echo "✓ 基本列表获取成功，总数: {$data['data']['total']}\n";
                } else {
                    echo "✗ 列表数据格式错误\n";
                }
            } else {
                echo "✗ 获取列表失败，HTTP状态: {$response['code']}\n";
                if (isset($response['data']['msg'])) {
                    echo "  错误信息: {$response['data']['msg']}\n";
                }
            }

            // 分页测试
            $response = $this->sendRequest($this->apiUrl . '/getList?page=1&limit=5');
            if ($response['code'] == 200) {
                $data = $response['data'];
                echo "✓ 分页测试成功，当前页记录数: " . count($data['data']['list']) . "\n";
            }

            // 筛选测试
            $response = $this->sendRequest($this->apiUrl . '/getList?festival_type=春节&status=1');
            if ($response['code'] == 200) {
                echo "✓ 筛选测试成功\n";
            }
            
        } catch (Exception $e) {
            echo "✗ 列表测试异常: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 测试获取详情API
     */
    public function testGetDetail()
    {
        echo "\n4. 测试获取详情API...\n";
        
        try {
            if (isset($this->testData['created_id'])) {
                $response = $this->sendRequest($this->apiUrl . '/getDetail?id=' . $this->testData['created_id']);
                
                if ($response['code'] == 200) {
                    $data = $response['data'];
                    if (isset($data['data']['name']) && isset($data['data']['sites_config'])) {
                        echo "✓ 获取详情成功，活动名称: {$data['data']['name']}\n";
                        
                        // 验证JSON字段解析
                        if (is_array($data['data']['sites_config']) && is_array($data['data']['stock_rules'])) {
                            echo "✓ JSON字段解析正确\n";
                        } else {
                            echo "✗ JSON字段解析失败\n";
                        }
                    } else {
                        echo "✗ 详情数据不完整\n";
                    }
                } else {
                    echo "✗ 获取详情失败，HTTP状态: {$response['code']}\n";
                    if (isset($response['data']['msg'])) {
                        echo "  错误信息: {$response['data']['msg']}\n";
                    }
                }
            } else {
                echo "✗ 没有可用的测试ID\n";
            }
        } catch (Exception $e) {
            echo "✗ 详情测试异常: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 测试更新活动API
     */
    public function testUpdateActivity()
    {
        echo "\n5. 测试更新活动API...\n";
        
        try {
            if (isset($this->testData['created_id'])) {
                $updateData = [
                    'id' => $this->testData['created_id'],
                    'description' => 'API更新后的描述_' . time(),
                    'sites_config' => json_encode([
                        'US' => ['discount_rate' => 0.20, 'free_shipping_threshold' => 45],
                        'UK' => ['discount_rate' => 0.18, 'free_shipping_threshold' => 35],
                        'DE' => ['discount_rate' => 0.15, 'free_shipping_threshold' => 30]
                    ])
                ];

                $response = $this->sendRequest($this->apiUrl . '/update', 'POST', $updateData);
                
                if ($response['code'] == 200) {
                    echo "✓ 更新活动成功\n";
                    
                    // 验证更新结果
                    $detailResponse = $this->sendRequest($this->apiUrl . '/getDetail?id=' . $this->testData['created_id']);
                    if ($detailResponse['code'] == 200) {
                        $detail = $detailResponse['data']['data'];
                        echo "✓ 更新数据验证正确\n";
                    }
                } else {
                    echo "✗ 更新活动失败，HTTP状态: {$response['code']}\n";
                    if (isset($response['data']['msg'])) {
                        echo "  错误信息: {$response['data']['msg']}\n";
                    }
                }
            } else {
                echo "✗ 没有可用的测试ID\n";
            }
        } catch (Exception $e) {
            echo "✗ 更新测试异常: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 测试状态更新API
     */
    public function testUpdateStatus()
    {
        echo "\n6. 测试状态更新API...\n";
        
        try {
            if (isset($this->testData['created_id'])) {
                $data = [
                    'id' => $this->testData['created_id'],
                    'status' => 0
                ];

                $response = $this->sendRequest($this->apiUrl . '/updateStatus', 'POST', $data);
                
                if ($response['code'] == 200) {
                    echo "✓ 状态更新成功\n";
                    
                    // 恢复状态
                    $data['status'] = 1;
                    $this->sendRequest($this->apiUrl . '/updateStatus', 'POST', $data);
                } else {
                    echo "✗ 状态更新失败，HTTP状态: {$response['code']}\n";
                    if (isset($response['data']['msg'])) {
                        echo "  错误信息: {$response['data']['msg']}\n";
                    }
                }
            }
        } catch (Exception $e) {
            echo "✗ 状态更新测试异常: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 测试获取有效活动API
     */
    public function testGetActiveActivities()
    {
        echo "\n7. 测试获取有效活动API...\n";
        
        try {
            // 当前有效活动
            $response = $this->sendRequest($this->apiUrl . '/getActiveActivities');
            
            if ($response['code'] == 200) {
                $data = $response['data'];
                if (is_array($data['data'])) {
                    echo "✓ 当前有效活动获取成功，数量: " . count($data['data']) . "\n";
                } else {
                    echo "✗ 有效活动数据格式错误\n";
                }
            } else {
                echo "✗ 获取有效活动失败，HTTP状态: {$response['code']}\n";
                if (isset($response['data']['msg'])) {
                    echo "  错误信息: {$response['data']['msg']}\n";
                }
            }

            // 指定日期查询
            $response = $this->sendRequest($this->apiUrl . '/getActiveActivities?date=2025-02-10');
            if ($response['code'] == 200) {
                echo "✓ 指定日期有效活动查询成功\n";
            }
            
        } catch (Exception $e) {
            echo "✗ 有效活动测试异常: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 测试站点特定活动API
     */
    public function testGetActivitiesBySite()
    {
        echo "\n8. 测试站点特定活动API...\n";
        
        try {
            $response = $this->sendRequest($this->apiUrl . '/getActivitiesBySite?site=US');
            
            if ($response['code'] == 200) {
                $data = $response['data'];
                if (is_array($data['data'])) {
                    echo "✓ 美国站点活动获取成功，数量: " . count($data['data']) . "\n";
                    
                    // 验证站点配置
                    if (!empty($data['data'])) {
                        $firstActivity = $data['data'][0];
                        if (isset($firstActivity['site_config']['discount_rate'])) {
                            echo "✓ 站点配置数据正确\n";
                        } else {
                            echo "✗ 站点配置数据缺失\n";
                        }
                    }
                } else {
                    echo "✗ 站点活动数据格式错误\n";
                }
            } else {
                echo "✗ 获取站点活动失败，HTTP状态: {$response['code']}\n";
                if (isset($response['data']['msg'])) {
                    echo "  错误信息: {$response['data']['msg']}\n";
                }
            }
            
        } catch (Exception $e) {
            echo "✗ 站点活动测试异常: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 测试名称唯一性检查API
     */
    public function testCheckNameExists()
    {
        echo "\n9. 测试名称唯一性检查API...\n";
        
        try {
            // 测试已存在的名称
            $response = $this->sendRequest($this->apiUrl . '/checkNameExists?name=2025春节活动');
            
            if ($response['code'] == 200) {
                $data = $response['data'];
                if (isset($data['data']['exists'])) {
                    echo "✓ 名称唯一性检查成功，存在: " . ($data['data']['exists'] ? '是' : '否') . "\n";
                } else {
                    echo "✗ 名称检查返回数据格式错误\n";
                }
            } else {
                echo "✗ 名称检查失败，HTTP状态: {$response['code']}\n";
                if (isset($response['data']['msg'])) {
                    echo "  错误信息: {$response['data']['msg']}\n";
                }
            }

            // 测试不存在的名称
            $response = $this->sendRequest($this->apiUrl . '/checkNameExists?name=不存在的活动名称_' . time());
            if ($response['code'] == 200) {
                $data = $response['data'];
                if (!$data['data']['exists']) {
                    echo "✓ 不存在名称检查正确\n";
                }
            }
            
        } catch (Exception $e) {
            echo "✗ 名称检查测试异常: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 测试删除活动API
     */
    public function testDeleteActivity()
    {
        echo "\n10. 测试删除活动API...\n";
        
        try {
            if (isset($this->testData['created_id'])) {
                $data = [
                    'id' => $this->testData['created_id']
                ];

                $response = $this->sendRequest($this->apiUrl . '/delete', 'POST', $data);
                
                if ($response['code'] == 200) {
                    echo "✓ 删除活动成功\n";
                    
                    // 验证删除结果
                    $detailResponse = $this->sendRequest($this->apiUrl . '/getDetail?id=' . $this->testData['created_id']);
                    if ($detailResponse['code'] != 200) {
                        echo "✓ 删除验证正确\n";
                    } else {
                        echo "✗ 删除验证失败，记录仍存在\n";
                    }
                } else {
                    echo "✗ 删除活动失败，HTTP状态: {$response['code']}\n";
                    if (isset($response['data']['msg'])) {
                        echo "  错误信息: {$response['data']['msg']}\n";
                    }
                }
            }
        } catch (Exception $e) {
            echo "✗ 删除测试异常: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 测试边界值API
     */
    public function testBoundaryValues()
    {
        echo "\n11. 测试边界值API...\n";
        
        try {
            // 测试长名称
            $longNameData = [
                'name' => str_repeat('长', 100),
                'festival_type' => '测试',
                'start_date' => '2025-12-01',
                'end_date' => '2025-12-05'
            ];

            $response = $this->sendRequest($this->apiUrl . '/create', 'POST', $longNameData);
            echo "✓ 长名称边界测试完成，状态: {$response['code']}\n";

            // 测试空参数
            $response = $this->sendRequest($this->apiUrl . '/getList?page=0&limit=0');
            echo "✓ 空参数边界测试完成，状态: {$response['code']}\n";

            // 测试大分页
            $response = $this->sendRequest($this->apiUrl . '/getList?page=1000&limit=1000');
            echo "✓ 大分页边界测试完成，状态: {$response['code']}\n";
            
        } catch (Exception $e) {
            echo "✗ 边界值测试异常: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 测试错误处理API
     */
    public function testErrorHandling()
    {
        echo "\n12. 测试错误处理API...\n";
        
        try {
            // 测试不存在的ID
            $response = $this->sendRequest($this->apiUrl . '/getDetail?id=99999');
            echo "✓ 不存在ID测试完成，状态: {$response['code']}\n";

            // 测试删除不存在的记录
            $response = $this->sendRequest($this->apiUrl . '/delete', 'POST', ['id' => 99999]);
            echo "✓ 删除不存在记录测试完成，状态: {$response['code']}\n";

            // 测试缺少参数
            $response = $this->sendRequest($this->apiUrl . '/create', 'POST', []);
            echo "✓ 缺少参数测试完成，状态: {$response['code']}\n";

            // 测试无效站点
            $response = $this->sendRequest($this->apiUrl . '/getActivitiesBySite?site=INVALID');
            echo "✓ 无效站点测试完成，状态: {$response['code']}\n";
            
        } catch (Exception $e) {
            echo "✗ 错误处理测试异常: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 测试输入验证API
     */
    public function testInputValidation()
    {
        echo "\n13. 测试输入验证API...\n";
        
        try {
            // 测试无效日期格式
            $invalidDateData = [
                'name' => '无效日期测试',
                'festival_type' => '测试',
                'start_date' => '2025-13-40',
                'end_date' => '2025-02-30'
            ];

            $response = $this->sendRequest($this->apiUrl . '/create', 'POST', $invalidDateData);
            echo "✓ 无效日期测试完成，状态: {$response['code']}\n";

            // 测试SQL注入
            $sqlInjectionData = [
                'name' => "'; DROP TABLE festival_activity; --",
                'festival_type' => '测试',
                'start_date' => '2025-12-01',
                'end_date' => '2025-12-05'
            ];

            $response = $this->sendRequest($this->apiUrl . '/create', 'POST', $sqlInjectionData);
            echo "✓ SQL注入防护测试完成，状态: {$response['code']}\n";

            // 测试XSS攻击
            $xssData = [
                'name' => '<script>alert("XSS")</script>',
                'festival_type' => '测试',
                'start_date' => '2025-12-01',
                'end_date' => '2025-12-05'
            ];

            $response = $this->sendRequest($this->apiUrl . '/create', 'POST', $xssData);
            echo "✓ XSS防护测试完成，状态: {$response['code']}\n";
            
        } catch (Exception $e) {
            echo "✗ 输入验证测试异常: " . $e->getMessage() . "\n";
        }
    }
}

// 运行API测试
$apiTest = new FestivalActivitiesApiTestWithAuth();
$apiTest->runAllApiTests();
