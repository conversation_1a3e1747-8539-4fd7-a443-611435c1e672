<?php

namespace plugins\salary\Controller;

use core\lib\config;
use core\lib\db\dbSMysql;
use core\lib\redisCached;
use plugins\salary\models\salaryCalculationModel;

class configController
{

    public function getConfig() {

        $config = config::all('data_salary');

        $corp = redisCached::getCorp();
        $config['corps'] = $corp;

        $sdb = dbSMysql::getInstance();

        // 固定薪资项
        $config['salary_title'] = redisCached::get('salary_title', function () {
            $sdb = dbSMysql::getInstance();
            return  $sdb->table('salary_title')->field('id, name, name_en, module') -> list();
        }, 60 * 5);

        // 浮动薪资项
        $sdb->table('salary_item')->field('id, item_name as name')->where('where is_delete = 0');
        $list = $sdb->list();
        $config['salary_item'] = $list;

        returnSuccess($config);

    }

    public function getMonth()
    {
        $type = $_GET['type']??'';
        // 默认上个月
        $month = date('Y-m',strtotime('-1 month'));
        $sdb = dbSMysql::getInstance();
        switch ($type) {
            case 'user_insurance_fund_tax':
                $social_security = $sdb->table('user_insurance_fund_tax')
                    ->where('where is_delete = 0')
                    ->order('month desc')
                    ->one();
                $config['month'] = $social_security['month'] ?? $month;
                break;
            case 'insurance':
                $social_security = $sdb->table('user_insurance_fund_tax')
                    ->where('where is_delete = 0 and type = 1')
                    ->order('month desc')
                    ->one();
                $config['month'] = $social_security['month'] ?? $month;
                break;
            case 'fund':
                $social_security = $sdb->table('user_insurance_fund_tax')
                    ->where('where is_delete = 0 and type = 2')
                    ->order('month desc')
                    ->one();
                $config['month'] = $social_security['month'] ?? $month;
                break;
            case 'tax':
                $social_security = $sdb->table('user_insurance_fund_tax')
                    ->where('where is_delete = 0 and type = 3')
                    ->order('month desc')
                    ->one();
                $config['month'] = $social_security['month'] ?? $month;
                break;
            case 'delete_comment':
                $comment = $sdb->table('user_reward')
                    ->where('where is_delete = 0')
                    ->order('month desc')
                    ->one();
                $config['month'] = $comment['month'] ?? $month;
                break;
            case 'salary':
                $calculation = $sdb->table('salary_calculation')
                    ->where('where status = :status and is_delete = 0', ['status' => salaryCalculationModel::STATUS_FINISHED])
                    ->order('month desc')
                    ->one();
                $config['month'] = $calculation['month'] ?? $month;
                break;
            case 'my_salary':
                $task = $sdb->table('salary_calculation_user_task', 't')
                    ->leftJoin('salary_calculation', 'sc', 't.calc_id = sc.id')
                    ->where('where t.is_delete = 0 and sc.is_delete = 0')
                    ->order('sc.month desc')
                    ->one();
                $config['month'] = $task['month'] ?? $month;
                break;
            default:
                $config['month'] = $month;
        }

        returnSuccess($config);
    }

    public function getList() {
        $keys = $_GET['keys']??'';
        if ($keys) {
            $keys = json_decode($keys);
        } else {
            $keys = [];
        }
        $sdb = dbSMysql::getInstance();
        $sdb->table('config');
        if (count($keys)) {
            $sdb->whereIn('key_name',$keys);
        }
        $list = $sdb->list();
        $list_array = [];
        foreach ($list as $v) {
            $v['data'] = json_decode($v['data'],true);
            $list_array[$v['key_name']] = $v['data'];
        }

        returnSuccess($list_array);
    }

    public function setConfig() {
        $paras_list = array('config');
        $param = arrangeParam($_POST, $paras_list);
        $config = $param['config'];
        if (empty($config)) {
            SetReturn(-1,'参数有误');
        }
        $config = json_decode($config,true);

        $sdb = dbSMysql::getInstance();
        $config_data = $sdb->table('config')->list();
        $config_keys = array_column($config_data, 'key_name');
        foreach ($config as $v) {
            if (in_array($v['key_name'],$config_keys)) {
                //修改
                $update_data = [
                    'data'=>is_array($v['data'])?json_encode($v['data']):$v['data'],
                ];
                $sdb->table('config')->where('where key_name =:key_name',['key_name' => $v['key_name']])->update($update_data);
            } else {
                //新增
                $insert_data = [
                    'key_name'=>$v['key_name'],
                    'data'=>is_array($v['data'])?json_encode($v['data']):$v['data'],
                ];
                $sdb->insert($insert_data);
            }
        }
        returnSuccess('','修改成功');
    }

}