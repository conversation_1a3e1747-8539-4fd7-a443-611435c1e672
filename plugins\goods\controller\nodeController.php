<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/14 11:56
 */

namespace  plugins\goods\controller;

use plugins\goods\form\nodeFrom;
use core\lib\db\dbMysql;
use plugins\goods\models\userModel;

class nodeController
{
    public function getList(){
        $paras_list = array('node_name', 'status', 'qw_name', 'page', 'page_size' ,'event_id', 'order_by');
        $param = arrangeParam($_POST, $paras_list);

        $where_string = 'where is_delete = 0';
        $where_array = [];
        if (!empty($param['node_name'])) {
            $where_string .= ' and node_name like :node_name';
            $where_array['node_name'] = '%'.$param['node_name'].'%';
        }
        if ($param['status'] > -1) {
            $where_string .= ' and status = :status';
            $where_array['status'] = (int)$param['status'];
        }
        $event_id = (int)$param['event_id'];
        if ($event_id > 0) {
            $where_string .= ' and event_id like :event_id';
            $where_array['event_id'] = '%,'.$event_id.',%';
        }

        if(!empty($param['qw_name'])) {
            $where_string .= " and manage_info->'$[*].wname' like :qw_name";
            $where_array['qw_name'] = '%'.$param['qw_name'].'%';
        }


        $db = dbMysql::getInstance();
        $db->table('node');
        $db->where($where_string, $where_array);
        $db->field('id,node_name,status,manage_info,description,event_id,created_at,expected_day,need_check,check_user_info,send_copy_user,event_detail');
        $order_str = '';
        if (!empty($param['order_by'])) {
            $order_by = json_decode($param['order_by'],true);
            foreach ($order_by as $k=>$ord){
                $order_str .= ''.$k.' '.$ord.',';
            }
        }
        if (empty($order_str)) {
            $db->order('id desc');
        } else {
            $db->order(trim($order_str,','));
        }
        $list = $db->pages($param['page'], $param['page_size']);
        returnSuccess($list);
    }

    public function editNode() {
        $paras_list = array('id', 'node_name', 'status', 'manage_info', 'description', 'event_list', 'send_copy_user', 'expected_day', 'need_check', 'check_user_info','manage_type');
        $length_data = ['node_name'=>['name'=>'节点名称','length'=>20],'description'=>['name'=>'描述','length'=>225]];
        $request_list = ['node_name'=>"节点名称",'manage_type'=>"负责人类型"];
        $param = arrangeParam($_POST, $paras_list, [] ,$length_data);
        $id = (int)$param['id'];
        $manage_type = json_decode($param['manage_type'], true);
        $is_developer = 0;
        if (in_array(2,$manage_type)) {
            $is_developer = 1;
        }
        if (!$is_developer) {
            $manage_info = json_decode($param['manage_info'], true);
            if (count($manage_info) > 1) {
                SetReturn(-1, '负责人不能超过1人');
            }
            foreach ($manage_info as $v) {
                if (empty($v['wid']) || empty($v['wname']) || empty($v['avatar'])) {
                    SetReturn(-1, '负责人参数有误');
                }
            }
        } else {
            $manage_info = [];
        }

        //获取事件详情
        $event_list = json_decode($param['event_list'],true);
        if (empty($event_list)) {
            SetReturn(-1, '事件必传选');
        }
        $event_ids = [];
        foreach ($event_list as $v1) {
            if (!count($v1)) {
                SetReturn(-1, '事件数据有误，请重新设置事件');
            }
            foreach ($v1 as $v2) {
                if (empty($v2['id'])) {
                    SetReturn(-1, '事件数据异常');
                }
                $event_ids[] = $v2['id'];
            }
        }
        if (!count($event_ids)) {
            SetReturn(-1, '请选择事件');
        }
        $event_detail = nodeFrom::getEventDetail($event_ids,$event_list);

        //保存节点信息
        $db = dbMysql::getInstance();
        $db->table('node');
        $node = $db->where('where id = :id', ['id'=>$id])->one();
        $data = [
            'node_name'=>$param['node_name'],
            'status'=>(int)$param['status'],
            'manage_info'=>json_encode($manage_info,JSON_UNESCAPED_UNICODE),
            'description'=>$param['description'],
            'event_id'=>','.implode(',',$event_ids).',',
            'send_copy_user'=>$param['send_copy_user'],
            'expected_day'=>(int)$param['expected_day'],
            'need_check'=>(int)$param['need_check'],
            'check_user_info'=>$param['check_user_info']??'',
            'event_detail'=>json_encode($event_detail),
            'is_developer'=>$is_developer,
        ];
        if (!$node) {
            //新增
            $data['user_id'] = userModel::$qwuser_id;
            $data['created_at'] = date('Y-m-d H:i:s');
            if($db->insert($data)) {
                SetReturn(0, '添加成功');
            } else {
                SetReturn(0, '添加失败');
            }
        } else {
            //修改
            $data['updated_at'] = date('Y-m-d H:i:s');
            $db->where('where id = :id', ['id'=>$node['id']]);
            if($db->update($data)) {
                SetReturn(0, '修改成功');
            } else {
                SetReturn(0, '修改失败');
            }
        }
    }

    public function getDtail() {
        $id = (int)$_GET['id'];
        if (!$id) {
            SetReturn(-1, '参数有有误');
        }
        $db = dbMysql::getInstance();
        $db->table('node');
        $db->field('id,node_name,status,manage_info,send_copy_user,status,description,event_detail,expected_day,need_check,check_user_info,is_developer');
        $node = $db->where('where id = :id', ['id'=>$id])->one();
        $event_detail = json_decode($node['event_detail'],true);
        $new_event_detail = [];
        foreach ($event_detail as $event_) {
            $event_item = [];
            foreach ($event_ as $event) {
                $event_item[] = [
                    'id'=>$event['id'],
                    'event_name'=>$event['event_name']
                ];
            }
            $new_event_detail[] = $event_item;
        }
        if ($node['is_developer']) {
            $node['manage_type'][] = '2';
        } else {
            $node['manage_type'][] = '1';
        }
        unset($node['event_detail']);
        $node['event_list'] = $new_event_detail;
        returnSuccess(['data'=>$node]);
    }

    public function setStatus() {
        $paras_list = array('ids','status');
        $param = arrangeParam($_POST, $paras_list);
        $ids = json_decode($param['ids']);
        $status = (int)$param['status'];
        if (empty($param['ids'])) {
            SetReturn(-1,'请选择要操作的数据');
        }
        //查询需要绑定的模板
        $ids = getArryForType($ids);
        $db = dbMysql::getInstance();
        $db->table('node');
        $db->whereIn('id', $ids);
        $db->update(['status'=>$status, 'updated_at'=>date('Y-m-d H:i:s')]);
        returnSuccess(0,'设置成功');
    }

}