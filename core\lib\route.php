<?php
/**
 * Created by 漠山.
 * @purpose 描述
 * @Author: 漠山
 * @Time: 2023/12/3 22:33
 */

namespace core\lib;

class route
{
    public $module;
    public $ctrl;
    public $action;
    public $request;
    public function __construct(){
        /*
         * 1、隐藏index.php
         * 2、获取URL 参数部分
         * 3、返回对应控制器和方法
         * */
        $app_config = config::all('app');
        if (isset($_SERVER['REQUEST_URI']) && ($_SERVER['REQUEST_URI'] != '/' && $_SERVER['REQUEST_URI'] != '')) {
            $path = explode('?',$_SERVER['REQUEST_URI']) ;
            $patharr = explode('/', trim($path[0], '/'));
            if (isset($patharr[0])) {
                $this->module = $patharr[0];
            } else {
                $this->module = $app_config['module'];
            }
            if (isset($patharr[1])) {
                $this->ctrl = $patharr[1];
            } else {
                $this->ctrl = $app_config['controller'];
            }
            if (isset($patharr[2])) {
                $this->action = $patharr[2];
            } else {
                $this->action = $app_config['action'];
            }
            //获取参数
        } else {
            $this->module = $app_config['module'];
            $this->ctrl = $app_config['controller'];
            $this->action = $app_config['action'];
        }
    }
}