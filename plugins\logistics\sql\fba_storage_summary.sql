-- FBA库存汇总统计表
CREATE TABLE IF NOT EXISTS `oa_l_fba_storage_summary` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `level_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '汇总层级:1店铺级,2站点级,3SKU级,4ASIN级',
  `asin` varchar(50) NOT NULL DEFAULT '' COMMENT 'ASIN(各级别都有)',
  `sku` varchar(100) NOT NULL DEFAULT '' COMMENT 'SKU(SKU级和店铺级有值)',
  `site_code` varchar(10) NOT NULL DEFAULT '' COMMENT '站点代码(站点级和店铺级有值)',
  `sid` int(11) NOT NULL DEFAULT 0 COMMENT '店铺ID(仅店铺级有值)',
  `shop_name` varchar(255) NOT NULL DEFAULT '' COMMENT '店铺名称',
  
  -- listing配置信息(从listing_data表获取)
  `product_stage` tinyint(1) NOT NULL DEFAULT 0 COMMENT '产品阶段：1成长期，2稳定期，3衰退期，4新品期，5清货',
  `stock_positioning` tinyint(1) NOT NULL DEFAULT 0 COMMENT '备货定位：1重点备货，2常规备货，3停止备货',
  `product_positioning` tinyint(1) NOT NULL DEFAULT 0 COMMENT '产品定位: 1头部产品,2腰部产品,3尾部产品,4清货产品',
  
  -- FBA库存核心数据
  `fba_sellable_qty` int(11) NOT NULL DEFAULT 0 COMMENT 'FBA可售数量',
  `fba_pending_transfer_qty` int(11) NOT NULL DEFAULT 0 COMMENT 'FBA待调仓数量',
  `fba_transferring_qty` int(11) NOT NULL DEFAULT 0 COMMENT 'FBA调仓中数量',
  `fba_inbound_qty` int(11) NOT NULL DEFAULT 0 COMMENT 'FBA在途数量',
  `inventory_plus_inbound_qty` int(11) NOT NULL DEFAULT 0 COMMENT '库存+在途数量',
  
  -- 扩展库存字段
  `overseas_warehouse_qty` int(11) NOT NULL DEFAULT 0 COMMENT '海外仓库存',
  `overseas_warehouse_inbound_qty` int(11) NOT NULL DEFAULT 0 COMMENT '海外仓在途',
  
  -- 采购计划字段(可编辑)
  `planned_purchase_qty` int(11) NOT NULL DEFAULT 0 COMMENT '计划采购数量',
  `purchase_days` int(11) NOT NULL DEFAULT 0 COMMENT '采购天数',
  `purchase_pending_qty` int(11) NOT NULL DEFAULT 0 COMMENT '采购未交数量',
  
  -- 销售周期字段
  `sellable_days` int(11) NOT NULL DEFAULT 0 COMMENT '拟售天数',
  `available_days` int(11) NOT NULL DEFAULT 0 COMMENT '可售天数',
  
  -- 销量统计字段  
  `estimated_sales_qty` int(11) NOT NULL DEFAULT 0 COMMENT '预估销量',
  `daily_avg_sales_qty` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT '日均销量',
  `sales_7days_qty` int(11) NOT NULL DEFAULT 0 COMMENT '7天销量',
  `sales_14days_qty` int(11) NOT NULL DEFAULT 0 COMMENT '14天销量',
  `sales_30days_qty` int(11) NOT NULL DEFAULT 0 COMMENT '30天销量',
  
  -- 前10天销量明细(JSON格式存储)
  `sales_10days_detail` text COMMENT '前10天销量明细JSON',
  
  -- 成本价格相关
  `fba_sellable_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT 'FBA可售成本价',
  `fba_pending_transfer_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT 'FBA待调仓成本价',
  `fba_transferring_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT 'FBA调仓中成本价',
  `fba_inbound_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT 'FBA在途成本价',
  `inventory_plus_inbound_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '库存+在途成本价',
  `overseas_warehouse_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '海外仓库存成本价',
  `overseas_warehouse_inbound_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '海外仓在途成本价',
  
  -- 汇总统计信息
  `shop_count` int(11) NOT NULL DEFAULT 0 COMMENT '包含店铺数量',
  `site_count` int(11) NOT NULL DEFAULT 0 COMMENT '包含站点数量',
  `sku_count` int(11) NOT NULL DEFAULT 0 COMMENT '包含SKU数量',
  `shop_list` text COMMENT '包含店铺列表JSON',
  `site_list` text COMMENT '包含站点列表JSON',
  `sku_list` text COMMENT '包含SKU列表JSON',
  
  -- 系统字段
  `sync_date` date NOT NULL COMMENT '同步日期',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0否，1是',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_level_dimensions` (`level_type`, `asin`, `sku`, `site_code`, `sid`, `sync_date`),
  KEY `idx_sync_date` (`sync_date`),
  KEY `idx_level_type` (`level_type`),
  KEY `idx_asin` (`asin`),
  KEY `idx_sku` (`sku`),
  KEY `idx_site_code` (`site_code`),
  KEY `idx_sid` (`sid`),
  KEY `idx_is_deleted` (`is_deleted`),
  KEY `idx_product_stage` (`product_stage`),
  KEY `idx_stock_positioning` (`stock_positioning`),
  KEY `idx_product_positioning` (`product_positioning`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='FBA库存汇总统计表';
