openapi: 3.0.0
info:
  title: 角色管理API
  version: 1.0.0
  description: 提供角色管理的相关接口
paths:
  /shop/role/getDefaultAuth:
    get:
      tags:
        - Role
      summary: 获取默认权限列表
      description: 获取系统默认权限列表
      responses:
        '200':
          description: 成功返回权限列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      auth:
                        type: array
                        description: 权限列表

  /shop/role/getRoleList:
    post:
      tags:
        - Role
      summary: 获取角色列表
      description: 根据条件筛选获取角色列表
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                role_name:
                  type: string
                  description: 角色名称
                page:
                  type: integer
                  description: 页码
                  default: 1
                page_size:
                  type: integer
                  description: 每页数量
                  default: 10
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/Role'
                      total:
                        type: integer
                        description: 总记录数
                      page:
                        type: integer
                        description: 当前页码
                      page_size:
                        type: integer
                        description: 每页条数

  /shop/role/addRole:
    post:
      tags:
        - Role
      summary: 新增/修改角色
      description: 创建或更新角色信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - role_name
              properties:
                id:
                  type: integer
                  description: 角色ID（编辑时必填）
                role_name:
                  type: string
                  description: 角色名称
                inherit_ids:
                  type: string
                  description: 继承的角色ID列表(JSON格式)
      responses:
        '200':
          description: 操作成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 操作成功

  /shop/role/delRole:
    post:
      tags:
        - Role
      summary: 删除角色
      description: 根据ID删除角色
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
              properties:
                id:
                  type: integer
                  description: 角色ID
      responses:
        '200':
          description: 删除成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 删除成功

  /shop/role/editAuth:
    post:
      tags:
        - Role
      summary: 编辑权限 
      description: 编辑角色的权限列表
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - role_id
                - auth
              properties:
                role_id:
                  type: integer
                  description: 角色ID
                auth:
                  type: string
                  description: 权限列表(JSON格式)
      responses:
        '200':
          description: 编辑成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 编辑成功

  /shop/role/editListAuth:
    post:
      tags:
        - Role
      summary: 编辑列表权限
      description: 编辑角色的列表权限
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - role_id
                - list_auth
              properties:
                role_id:
                  type: integer
                  description: 角色ID
                list_auth:
                  type: string
                  description: 列表权限(JSON格式)
      responses:
        '200':
          description: 编辑成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 编辑成功

  /shop/role/addRoleUserList:
    post:
      tags:
        - Role
      summary: 获取角色用户列表
      description: 获取指定角色的用户列表
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - role_id
              properties:
                role_id:
                  type: integer
                  description: 角色ID
                user_name:
                  type: string
                  description: 用户名
                page:
                  type: integer
                  description: 页码
                  default: 1
                page_size:
                  type: integer
                  description: 每页数量
                  default: 10
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/RoleUser'
                      total:
                        type: integer
                        description: 总记录数

  /shop/role/addRoleUser:
    post:
      tags:
        - Role
      summary: 添加角色用户
      description: 为角色添加用户
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - role_id
                - user_list
              properties:
                role_id:
                  type: integer
                  description: 角色ID
                user_list:
                  type: string
                  description: 用户ID列表(JSON格式)
      responses:
        '200':
          description: 添加成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 添加成功

  /shop/role/banRoleUser:
    post:
      tags:
        - Role
      summary: 禁用/启用角色用户
      description: 修改用户在角色中的状态
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - rid
                - uid
              properties:
                rid:
                  type: integer
                  description: 角色ID
                uid:
                  type: integer
                  description: 用户ID
      responses:
        '200':
          description: 操作成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 操作成功

  /shop/role/importRoleUser:
    post:
      tags:
        - Role
      summary: 导入角色用户
      description: 通过Excel文件批量导入角色用户
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - excel_src
              properties:
                excel_src:
                  type: string
                  description: Excel文件链接
      responses:
        '200':
          description: 导入成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 导入成功
                  data:
                    type: object
                    properties:
                      error_file:
                        type: string
                        description: 错误数据文件路径
                      error_count:
                        type: integer
                        description: 错误数量
                      success_count:
                        type: integer
                        description: 成功数量
                      data:
                        type: array
                        description: 导入结果数据

  /shop/role/exportRoleUser:
    post:
      tags:
        - Role
      summary: 导出角色用户
      description: 导出角色用户数据
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - type
              properties:
                type:
                  type: integer
                  description: 导出类型(1-选中数据,2-全部数据)
                ids:
                  type: string
                  description: 选中的数据ID(JSON格式)
      responses:
        '200':
          description: 导出成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 导出成功
                  data:
                    type: object
                    properties:
                      url:
                        type: string
                        description: 文件下载路径

  /shop/role/exportUserRolesAndPermissions:
    get:
      tags:
        - Role
      summary: 导出用户角色和权限
      description: 导出所有用户的角色和权限数据
      responses:
        '200':
          description: 导出成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 导出成功
                  data:
                    type: string
                    description: 导出文件路径

  /shop/role/removeRole:
    post:
      tags:
        - Role
      summary: 移除角色
      description: 移除用户的角色
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - rid
                - uid
              properties:
                rid:
                  type: integer
                  description: 角色ID
                uid:
                  type: integer
                  description: 用户ID
      responses:
        '200':
          description: 移除成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 移除成功

components:
  schemas:
    Role:
      type: object
      properties:
        id:
          type: integer
          description: ID
        role_name:
          type: string
          description: 角色名称
        auth:
          type: array
          description: 权限列表
        auth_name:
          type: array
          description: 权限名称列表
        list_auth:
          type: array
          description: 列表权限

    RoleUser:
      type: object
      properties:
        id:
          type: integer
          description: ID
        status:
          type: integer
          description: 状态
        role_name:
          type: string
          description: 角色名称
        wname:
          type: string
          description: 用户姓名
        created_time:
          type: string
          description: 创建时间
          format: date-time
