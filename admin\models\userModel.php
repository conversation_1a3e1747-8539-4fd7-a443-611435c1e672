<?php
/**
 * @author: zhangguoming
 * @Time: 2024/3/27 11:25
 */

namespace  admin\models;


use core\lib\db\dbMysql;

class userModel
{
    public static string $wid;
    public static string $qwuser_id;
    public static string $wname;
    public static string $is_super;
    public static string $auth;
    public static string $avatar;
    private static mixed $role_list = '';
    //用户列表获取用户角色
    public static function getUserListRole($list) {
        if(count($list)) {
            if (!empty(self::$role_list)) {
                $role_list = self::$role_list;
            } else {
                $db = dbMysql::getInstance();
                $user_ids = array_column($list,'id');
                $role_list = $db->table('sys_user_roles','a')
                    ->leftJoin('sys_role','b','b.id = a.role_id')
                    ->field('b.*,a.qwuser_id')
                    ->whereIn('a.qwuser_id',$user_ids)
                    ->list();
                self::$role_list = $role_list;
            }
            if ($role_list) {
                foreach ($list as &$v) {
                    $roles = [];
                    foreach ($role_list as $role) {
                        if ($v['id'] == $role['qwuser_id']) {
                            $roles[] = ['id'=>$role['id'],'role_name'=>$role['role_name']];
                        }
                    }
                    $v['roles'] = $roles;
                }
            }
            else {
                foreach ($list as &$v) {
                    $v['roles'] = [];
                }
            }
        }
        return $list;
    }
    //根据权限获取可查看的用户id(qwuser_id)
    public static function getIdsByAuth(string $type) {
        if ($type == 'all_wating_matters') {
            //全部待办
            $all_key = 'all_wating_matters_all';//查看全部
            $department_key = 'all_wating_matters_department';//查看部门权限
        } elseif ($type == 'completed_matters') {
            //已办
            $all_key = 'completed_matters_all';//查看全部权限
            $department_key = 'completed_matters_department';//查看部门权限
        } elseif ($type == 'process') {
            //流程
            $all_key = 'process_all';//查看全部权限
            $department_key = 'process_department';//查看部门权限
        } else {
            returnError('列表查看权限有误');
        }
        $user_ids = [];
        $auth_list = json_decode(self::$auth,true);
        //超管和有查看全部权限的成员可看全部
        if (self::$is_super == 1 || in_array($all_key,$auth_list)) {
            return $user_ids;
        }
        //部门权限
        if (in_array($department_key,$auth_list)) {
            $db = dbMysql::getInstance();
            $user_info = $db->table('qwuser')
                ->where('id = :id',['id'=>self::$qwuser_id])
                ->field('wdepartment_ids')
                ->one();
            if ($user_info) {
                //获取所有部门
                $qwdepartment = $db->table('qwdepartment')
                    ->field('qw_parentid,wp_id')
                    ->list();
                $qwdepartment_ = array_column($qwdepartment,'qw_parentid','wp_id');
                //根据自己所在的部门获取，所有下级部门
                $qwdepartment_ids = json_decode($user_info['wdepartment_ids']);
                $next_ids = $qwdepartment_ids;
                do {
                    //找下一级部门
                    $next_qwdepartment = array_intersect($qwdepartment_,$next_ids);
                    $next_ids = array_keys($next_qwdepartment);
                    $qwdepartment_ids = array_merge($qwdepartment_ids,$next_ids);
                } while (count($next_ids));
                //用户企业id获取
                $user_list = $db->table('qwuser')
                    ->where('JSON_OVERLAPS(wdepartment_ids,:wdepartment_ids)',['wdepartment_ids'=>json_encode($qwdepartment_ids)])
                    ->field('id')
                    ->list();
                $user_ids = array_column($user_list,'id');
            }
            return $user_ids;
        }
        return [self::$qwuser_id];
    }
}