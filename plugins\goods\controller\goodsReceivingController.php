<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/7 12:00
 */

namespace  plugins\goods\controller;

use plugins\goods\common\authenticationCommon;
use plugins\goods\common\publicMethod;
use plugins\goods\form\goodsReceivingFrom;
use plugins\goods\models\userModel;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;

class goodsReceivingController
{
    //入库列表
    public function getList() {
        $paras_list = array('goods_name', 'flow_path_id', 'batch', 'created_time', 'order_by', 'page_size', 'page');
        $param = arrangeParam($_POST, $paras_list);
        $db = dbMysql::getInstance();
        $db->table('goods_receiving','a')->where('where 1=1');
        $db->leftJoin('goods_new','b', 'b.id = a.goods_id');
        if (!empty($param['goods_name'])) {
            $db->andWhere('and b.goods_name like :goods_name',['goods_name'=>'%'.$param['goods_name'].'%']);
        }
        if ($param['flow_path_id'] > 0) {
            $db->andWhere('and a.flow_path_id=:flow_path_id',['flow_path_id'=>$param['flow_path_id']]);
        }
        if ($param['batch'] > 0) {
            $db->andWhere("and a.sample_batch = :sample_batch",['sample_batch'=>$param['batch']]);
        }
        if (!empty($param['created_time'])) {
            $created_time = json_decode($param['created_time']);
            $start_time = strtotime($created_time[0]);
            $end_time = strtotime($created_time[1]);
            $db->andWhere('and a.created_time >= :start_time and a.created_time <= :end_time',['start_time'=>$start_time,'end_time'=>$end_time]);
        }
        $order_str = '';
        if (!empty($param['order_by'])) {
            $order_by = json_decode($param['order_by'],true);
            foreach ($order_by as $k=>$ord){
                $order_str .= 'a.'.$k.' '.$ord.',';
            }
        }
        if (empty($order_str)) {
            $db->order('a.id desc');
        } else {
            $db->order(trim($order_str,','));
        }
        $db->field('a.*,b.goods_name');
        $list = $db->pages($param['page'],$param['page_size']);
        foreach ($list['list'] as &$v) {
            $v['created_time'] = date('Y-m-d H:i:s',(int)$v['created_time']);
            $v['color_id'] = json_decode($v['color_id']);
        }
        returnSuccess($list);
    }

    //入库登记 + 修改
    public function setReceiving() {
        $paras_list = array('id', 'goods_id', 'flow_path_id', 'sample_batch', 'goods_num', 'color_ids', 'project_id','sample_no','description', 'matter_id');
        $request_data = ['goods_id'=>'入库产品','flow_path_id'=>'送样类型','goods_num'=>'样品数量','color_ids'=>'样品颜色','sample_no'=>'样品编号'];
        $length_data = ['description'=>['name'=>'入库说明','length'=>255]];
        $param = arrangeParam($_POST, $paras_list, $request_data, $length_data);
        $color_ids = json_decode($param['color_ids']);
        if (!count($color_ids)) {
            SetReturn('-1','请选择入库的商品颜色');
        }
        if ((int)$param['goods_num'] <= 0) {
            SetReturn('-1','样品数量不正确');
        }
        $id = (int)$param['id'];
        $flow_path_id = (int)$param['flow_path_id'];
        $db = dbMysql::getInstance();
        $db->beginTransaction();
        try {
            if ($id > 0) {
                $receiving_data = $db->query('select user_id,flow_path_id,sample_batch from oa_goods_receiving where id=:id',['id'=>$id]);
                //验证流程中是否该此人负责
                $batch = $receiving_data['flow_path_id']==1?($receiving_data['sample_batch'] !=1?1:2):1;
                $template = $db->table('template')
                    ->where('where flow_path_id=:flow_path_id and batch=:batch and status=1 and is_delete=0',
                        ['flow_path_id'=>$receiving_data['flow_path_id'],'batch'=>$batch])
                    ->one();
                if (!$template) {
                    SetReturn(-1,'未找到对应的模板');
                }
                $tpl_data = json_decode($template['tpl_data'],true);
                if ($receiving_data['flow_path_id']==1) {
                    $event_data = $tpl_data[0][0]['event_detail'][0][0];
                } else {
                    $event_data = $tpl_data[1][0]['event_detail'][0][0];
                }
                $manage_info = json_decode($event_data['manage_info'],true);
                authenticationCommon::projectManageVatify($manage_info);
                //修改
                $update_data = [
                    'goods_num'=>(int)$param['goods_num'],
                    'color_id'=>$param['color_ids'],
                    'description'=>$param['description'],
                    'sample_no'=>$param['sample_no'],
                ];
                $db->table('goods_receiving');
                $db->where('where id =:id',['id'=>$id]);
                $db->update($update_data);
                $db->commit();
                returnSuccess('','修改成功');
            } else {
                if ($flow_path_id == 1) {
                    goodsReceivingFrom::saveReceiving1($param);
                } else {
                    goodsReceivingFrom::saveReceiving3($param);
                }
                $db->commit();
                returnSuccess('','保存成功');
            }
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }

    //获取已入库的批次，数量，抽货样品-出货样流程。
    public function getReceivingToToday() {
        $paras_list = array('goods_id','flow_path_id');
        $request_data = ['goods_id'=>'入库产品ID','flow_path_id'=>'送样类型'];
        $param = arrangeParam($_POST, $paras_list, $request_data);
        $goods_id = (int)$param['goods_id'];
        $flow_path_id = (int)$param['flow_path_id'];
        if (!$goods_id || !$flow_path_id) {
            SetReturn('-1','参数有误');
        }
        $db = dbMysql::getInstance();
        //获取所有入库信息
        $last_receiving = $db->queryAll('select sample_batch,color_id,created_time from oa_goods_receiving where goods_id=:goods_id and is_delete=0 and flow_path_id = :flow_path_id limit 1',['goods_id'=>$goods_id,'flow_path_id'=>$flow_path_id]);
        //获取今天已经输入库的数量
        $today_count = 0;
        $start_of_day = strtotime('today midnight');
        $end_of_day = strtotime('tomorrow midnight') - 1;
        foreach ($last_receiving as $v) {
            if ($v['created_time'] > $start_of_day && $v['created_time'] < $end_of_day) {
                $today_count++;
            }
        }
        //出货样，抽货样获待办事项
        $matter_list = [];
        if ($flow_path_id > 1) {
            //获取待办事项列表
            $db->table('goods_project')
                ->where('where goods_id=:goods_id and is_delete=0 and status <> 4 and flow_path_id = :flow_path_id',['goods_id'=>$goods_id,'flow_path_id'=>$flow_path_id])
                ->field('id');
            $goods_project = $db->list();
            if (count($goods_project)) {
                $db->table('goods_matters a')
                    ->leftJoin('goods_project','b','b.id = a.goods_project_id')
                    ->where('where a.qwuser_id=:qwuser_id and a.event_index is not null and a.status=0 and event_type=8',['qwuser_id'=>userModel::$qwuser_id])
                    ->whereIn('a.goods_project_id',array_column($goods_project,'id'));
                $db->field('a.id,a.node_index,a.event_index,a.matter_name,b.sample_batch,a.goods_project_id as project_id');
                $matter_list = $db->list();
            }
        }
        returnSuccess([
            'last_sample_batch'=>array_column($last_receiving,'sample_batch'),
            'today_count'=>$today_count,
            'matter_list'=>$matter_list
        ]);

    }
    //获取入库编码
    public function getReceivingCode() {
        $paras_list = array('goods_id', 'flow_path_id', 'sample_batch');
        $request_data = ['goods_id'=>'入库产品ID','sample_batch'=>'样品批次','flow_path_id'=>'送样类型'];
        $param = arrangeParam($_POST, $paras_list, $request_data);
        $sample_batch = (int)$param['sample_batch'];
        $flow_path_id = (int)$param['flow_path_id'];
        $db = dbMysql::getInstance();
        $goods_info = $db->table('goods_new')
            ->where('where id=:goods_id',['goods_id'=>$param['goods_id']])
            ->field('id,goods_name,goods_size')
            ->one();
        $goods_size = $goods_info['goods_size'];
        //首拼
        $code_str = publicMethod::getFirstPinyin($goods_info['goods_name']).$goods_info['id'];
        //流程
        if ($flow_path_id == 1) {
            //型号
            if ($goods_size > 0) {
                if ($goods_size == 1) {
                    $code_str .= "XH";
                } else if ($goods_size == 2) {
                    $code_str .= "ZH";
                } else {
                    $code_str .= "DH";
                }
            }
            $code_str .= "-C{$sample_batch}";
        } else {
            $start_of_day = strtotime('today midnight');
            $end_of_day = strtotime('tomorrow midnight') - 1;
            //查询当天入库的数量
            $today_count = $db->table('goods_receiving')
                ->where('where goods_id=:goods_id and flow_path_id=:flow_path_id',['goods_id'=>$param['goods_id'],'flow_path_id'=>$flow_path_id])
                ->andWhere('and created_time > :start_of_day and created_time < :end_of_day',['start_of_day'=>$start_of_day,'end_of_day'=>$end_of_day])
                ->count();
            if ($today_count > 1) {
                $code_str .= "({$today_count})";
            }
        }
        //时间
        $code_str .= "-".date('Ymd');
        $code_str = str_replace(' ','',$code_str);
        returnSuccess(['sample_no'=>$code_str]);
    }
}