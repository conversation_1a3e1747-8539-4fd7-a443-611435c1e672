<?php

namespace plugins\salary\Controller;

use core\lib\redisCached;
use Rap2hpoutre\FastExcel\FastExcel;
use plugins\salary\form\messagesFrom;
use plugins\salary\models\salaryCalculationModel;
use core\lib\db\dbMysql;
use core\lib\db\dbSMysql;
use plugins\salary\models\userModel;

class userSalaryManageController
{
    // 员工工资明细
    public function getFinishUserList()
    {
        $paras_list = array('month', 'user_id', 'dep_id', 'corp_id', 'page', 'page_size');
        $param = arrangeParam($_GET, $paras_list);
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        $sdb = dbSMysql::getInstance();

        $sdb->table('salary_calculation_user', 'scu')
            ->field('scu.*, sc.month')
            ->leftJoin('salary_calculation', 'sc', 'sc.id = scu.calc_id')
            ->where('where sc.status = :status and scu.status = :status', ['status' => salaryCalculationModel::STATUS_FINISHED]);

        if (userModel::getUserListAuth('finishUserAll')) {
            // 全部权限
        } elseif (userModel::getUserListAuth('finishUserWorkPlace')) {
            $auth_work_place_id = [];
            $gid = ["1", "2"];
            foreach ($gid as $item) {
                if (userModel::getUserListAuth('finishUserWorkPlace_'.$item)) {
                    $auth_work_place_id[] = $item;
                }
            }
            if (empty($auth_work_place_id)) {
                returnError('无权限查看');
            }
            $sdb->whereIn("JSON_EXTRACT(scu.result, '$.user_info.work_place')", $auth_work_place_id);
        }

        if ($param['month']) {
            $month = json_decode($param['month'], true);
            if (!empty($month)) {
                $sdb->andWhere('sc.month >= :start_month and sc.month <= :end_month', ['start_month' => $month[0], 'end_month' => $month[1]]);
            }

        }
        if ($param['user_id']) {
            $user_id = json_decode($param['user_id'], true);
            if (!empty($user_id)) {
                $sdb->whereIn('scu.user_id', $user_id);
            }
        }
        if (isset($param['corp_id'])) {
            $corp_id = json_decode($param['corp_id'], true);
            if (!empty($corp_id)) {
                $sdb->whereIn("JSON_EXTRACT(scu.result, '$.user_info.corp_id')", $corp_id);
            }
        }
        if ($param['dep_id']) {
            $department = json_decode($param['dep_id'], true);
            if (!empty($department)) {
                $sdb->whereIn("JSON_EXTRACT(scu.result, '$.user_info.user_wmain_department')", $department);
            }
        }
        $data = $sdb->pages($page, $limit);
        if (empty($data['list'])) returnSuccess($data);

        foreach ($data['list'] as &$item) {
            $item['attach'] = json_decode($item['attach'], true) ?? null;
            $item['result'] = json_decode($item['result'], true) ?? null;
        }

        returnSuccess($data);
    }

    // 员工工资明细
    public function getFinishUserDetail()
    {
        $paras_list = array('id');
        $param = arrangeParam($_GET, $paras_list);
        if (!$param['id']) returnError('ID不能为空');

        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_calculation_user', 'scu')
            ->field('scu.*, sc.month')
            ->leftJoin('salary_calculation', 'sc', 'sc.id = scu.calc_id')
            ->where('where scu.id = :id and sc.status = :status and scu.status = :status', ['id' => $param['id'],'status' => salaryCalculationModel::STATUS_FINISHED]);
        $detail = $sdb->one();
        if (empty($detail)) returnSuccess($detail);

        $detail['attach'] = json_decode($detail['attach'], true) ?? null;
        $detail['result'] = json_decode($detail['result'], true) ?? null;

        returnSuccess($detail);
    }

    // 员工工资明细
    public function exportFinishUserList()
    {
        $paras_list = array('month', 'user_id', 'dep_id',  'keys', 'ids');
        $param = arrangeParam($_POST, $paras_list);
        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_calculation_user', 'scu')
            ->field('scu.*, sc.month')
            ->leftJoin('salary_calculation', 'sc', 'sc.id = scu.calc_id')
            ->where('where sc.status = :status and scu.status = :status', ['status' => salaryCalculationModel::STATUS_FINISHED]);
        if ($param['month']) {
            $month = json_decode($param['month'], true);
            if (!empty($month)) {
                $sdb->andWhere('sc.month >= :start_month and sc.month <= :end_month', ['start_month' => $month[0], 'end_month' => $month[1]]);
            }

        }
        if ($param['user_id']) {
            $user_id = json_decode($param['user_id'], true);
            if (!empty($user_id)) {
                $sdb->whereIn('scu.user_id', $user_id);
            }
        }
        if ($param['dep_id']) {
            $department = json_decode($param['dep_id'], true);
            if (!empty($department)) {
                $sdb->whereIn("JSON_EXTRACT(scu.result, '$.user_info.user_wmain_department')", $department);
            }
        }
        $sdb->order('scu.id DESC');
        $list = $sdb->list();
        empty($list) && returnError('没有数据');

        foreach ($list as &$item) {
            $item['result'] = json_decode($item['result'], true) ?? null;
        }
        $keys = json_decode($param['keys'], true);
        $ids = json_decode($param['ids'], true);
        $new_data = self::filterData($list, $keys, $ids);

        //保存
        $save_path = "/public/salary/temp/user";
        $url = SELF_FK . $save_path;

        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path . "/" . date('YmdHis') . uniqid() . '.xlsx';
        $url = SELF_FK . $path;
        if (!file_exists($url)) {
            touch($url);
        }
        (new FastExcel($new_data))->export($url);
        returnSuccess(['path' => $path]);

    }

    // 获取薪资计算列表
    public function getList()
    {
        $paras_list = array('month', 'page', 'page_size');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_calculation');
        $sdb->where('where is_delete = 0 and status = :status', ['status' => salaryCalculationModel::STATUS_FINISHED]);

        if (userModel::getUserListAuth('salaryManageAll')) {
            // 全部权限
        } elseif (userModel::getUserListAuth('salaryManageRelated')) {
            $sdb->andWhere('operator = :operator', ['operator' => userModel::$qwuser_id]);
        }

        if ($param['month']) {
            $month = json_decode($param['month'], true);
            $sdb->andWhere('month >= :start and month <= :end', ['start' => $month[0], 'end' => $month[1]]);
        }
        $sdb->order('id desc');
        $data = $sdb->pages($page, $limit);

        if (empty($data['list'])) returnSuccess($data);

        $calc_ids = array_column($data['list'], 'id');
        $user_count = $sdb->table('salary_calculation_user')
            ->field('count(id) as user_count, calc_id')
            ->where('where is_delete = 0 and status = :status', ['status' => salaryCalculationModel::STATUS_FINISHED])
            ->whereIn('calc_id', $calc_ids)
            ->groupBy(['calc_id'])
            ->list();
        $user_count = array_column($user_count, 'user_count', 'calc_id');

        $users = redisCached::getUserInfo();
        $users = array_column($users, null, 'user_id');

        foreach ($data['list'] as &$item) {
            $item['attach'] = json_decode($item['attach'], true);
            $item['user_count'] = $user_count[$item['id']] ?? 0;
            $item['operator_name'] = $users[$item['operator']]['user_name'] ?? '';
        }

        returnSuccess($data);
    }

    // 发布工资条
    public function add()
    {
        $paras_list = array('id', 'setting');
        $request_list = ['id' => '算薪计算ID', 'setting' => '配置'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

        empty($param['setting']) && returnError('配置不能为空');
        $setting = json_decode($param['setting'], true);
        empty($setting['user']) && returnError('用户配置不能为空');


        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_calculation');
        $detail = $sdb->where('where id = :id and status = :status and  is_delete = 0', ['id' => $param['id'], 'status' => salaryCalculationModel::STATUS_FINISHED])->one();
        empty($detail) && returnError('薪资计算不存在');

        $sdb->table('salary_calculation_user');
        $sdb->where('where calc_id = :calc_id and status = :status', ['calc_id' => $param['id'], 'status' => salaryCalculationModel::STATUS_FINISHED]);
        $user_list = $sdb->list();
        empty($user_list) && returnError('薪资计算用户不存在');
        $user_list = array_column($user_list, null, 'id');

        // 记录配置
        $sdb->table('salary_calculation')
            ->where('where id = :id', ['id' => $param['id']])
            ->update([
                'publish_setting' => $param['setting'],
                'publish_time' => date('Y-m-d H:i:s'),
                'operator' => userModel::$qwuser_id
            ]);

        // 查询已有的工资条
        $task_list = $sdb->table('salary_calculation_user_task')
            ->field('id, user_id, department_id, type, setting')
            ->where('where calc_id = :calc_id', ['calc_id' => $param['id']])
            ->list();

        $task_map = [];
        foreach ($task_list as $task) {
            $department_id = $task['department_id'] ?? 0;
            $key = $task['type'].'-'.$task['user_id'].'-'.$department_id;
            $task_map[$key] = $task;
        }

        $user_setting = $setting['user'];
        $department_setting = $setting['department'];
        $department_task = []; // 部门任务
        $insert_data = [];
        $update_data = [];

        $msg_user_ids = [];
        foreach ($user_list as $item) {
            $user_id = $item['user_id'];
            $user_info = json_decode($item['result'], true)['user_info'];
            if (!empty($department_setting)) {
                $department_id = $user_info['user_wmain_department'];
                if (!isset($department_task[$department_id])) {
                    $department_task[$department_id] = [];
                }
                $department_task[$department_id][] = $user_id;
            }
            $msg_user_ids[] = $user_id;
            if (isset($task_map['1-'.$user_id.'-0'])) {
                $update_data[] = [
                    'id' => $task_map['1-'.$user_id.'-0']['id'],
                    'setting' => json_encode([
                        'status' => 0,
                        'remind_count' => $user_setting['times'] ?? null,
                        'minutes' => $user_setting['minutes'] ?? null,
                    ]),
                ];
            } else {
                $insert_data[] = [
                    'calc_id' => $param['id'],
                    'user_id' => $user_id,
                    'department_id' => null,
                    'type' =>1,
                    'calc_u_id' => $item['id'],
                    'setting' => json_encode([
                        'status' => 0,
                        'remind_count' => $user_setting['times'] ?? null,
                        'minutes' => $user_setting['minutes'] ?? null,
                    ]),
                    'operator' => userModel::$qwuser_id,
                ];
            }
        }

        $user_map = dbMysql::getInstance()->table('qwuser')->field('wid, id')->list();
        $user_map = array_column($user_map, 'wid', 'id');

        $wids = array_intersect_key($user_map, array_flip($msg_user_ids));
        $wids = array_values($wids);
        messagesFrom::senMeg($wids, 1, $detail['month'].'工资条已发布', $param['id'], '',"工资条");

        // 部门任务
        if (!empty($department_task)) {
            $msg_user_ids = [];
            // 部门信息
            $departments= redisCached::getDepartment();
            $departments = array_column($departments, null, 'wp_id');

            $users = dbMysql::getInstance()->table('qwuser')->field('wid, id')->list();
            $users = array_column($users, 'id', 'wid');

            foreach ($department_task as $department_id => $user_ids) {
                $department = $departments[$department_id];
                $department_leader = json_decode($department['department_leader'], true);
                if (empty($department_leader)) continue;
                foreach ($department_leader as $leader) {
                    $msg_user_ids[] = $users[$leader];
                    if (isset($task_map['2-'.$users[$leader].'-'.$department_id])) {
                        $update_data[] = [
                            'id' => $task_map['2-'.$users[$leader].'-'.$department_id]['id'],
                            'setting' => json_encode([
                                'status' => 0,
                                'remind_count' => $department_setting['times'] ?? null,
                                'minutes' => $department_setting['minutes'] ?? null,
                            ]),
                        ];
                    } else {
                        $insert_data[] = [
                            'calc_id' => $param['id'],
                            'user_id' => $users[$leader],
                            'department_id' => $department_id,
                            'type' => 2,
                            'calc_u_id' => json_encode($user_ids),
                            'setting' => json_encode([
                                'status' => 0,
                                'remind_count' => $department_setting['times'] ?? null,
                                'minutes' => $department_setting['minutes'] ?? null,
                            ]),
                            'operator' => userModel::$qwuser_id,
                        ];
                    }
                }
            }
            $wids = array_intersect_key($user_map, array_flip($msg_user_ids));
            $wids = array_values($wids);
            messagesFrom::senMeg($wids, 1, $detail['month'].'部门工资条已发布', $param['id'], '', "工资条");
        }


        // 算薪员工增加任务
        if (!empty($insert_data)) {
            $keys = array_keys($insert_data[0]);
            $list_ = array_map(function ($row) {
                return array_values($row);
            }, $insert_data);
            $sdb->table('salary_calculation_user_task')->insertBatch($keys, $list_);
        }

        // 更新算薪员工任务
        if (!empty($update_data)) {
            $sdb->table('salary_calculation_user_task')->updateBatch($update_data);
        }
        returnSuccess([], '发起成功');
    }

    // 获取算薪下员工
    public function getUserList()
    {
        $paras_list = array('id', 'user_id', 'page', 'page_size', 'status');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        if (!$param['id']) returnError('id不能为空');
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        $sdb = dbSMysql::getInstance();
        $calc = $sdb->table('salary_calculation')
            ->where('where id = :id and is_delete = 0 and status = :status', ['id' => $param['id'], 'status' => salaryCalculationModel::STATUS_FINISHED])
            ->one();
        if (!$calc) returnError('算薪计算不存在');

        $sdb->table('salary_calculation_user_task', 't')
            ->field('u.*, t.setting')
            ->leftJoin('salary_calculation_user', 'u', 'u.id = t.calc_u_id and t.type = 1')
            ->where('where u.calc_id = :calc_id and u.status = :status', ['calc_id' => $param['id'], 'status' => salaryCalculationModel::STATUS_FINISHED]);

        if (isset($param['user_id'])) {
            $user_id = json_decode($param['user_id'], true);
            if (!empty($user_id)) {
                $sdb->whereIn('u.user_id', $user_id);
            }
        }
        if (isset($param['status'])) {
            if ($param['status'] == 1) {
                $sdb->andWhere("(JSON_EXTRACT(t.setting, '$.status') = 1)");
            } else {
                $sdb->andWhere("(JSON_EXTRACT(t.setting, '$.status') = 0)");
            }
        }
        $data = $sdb->pages($page, $limit);
        if (empty($data['list'])) returnSuccess($data);

        foreach ($data['list'] as &$item) {
            $item['attach'] = json_decode($item['attach'], true) ?? null;
            $item['result'] = json_decode($item['result'], true) ?? null;
            $item['setting'] = json_decode($item['setting'], true) ?? null;
        }

        returnSuccess($data);
    }

    // 获取薪资计算详情
    public function getDetail()
    {
        $paras_list = array('id');
        $param = arrangeParam($_GET, $paras_list);
        $id = $param['id'];
        if (!$id) returnError('id不能为空');
        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_calculation');
        $sdb->where('where id = :id and is_delete = 0', ['id' => $id]);
        $detail = $sdb->one();
        if (empty($detail)) returnError('薪资计算不存在');
        $detail['attach'] = $detail['attach'] ? json_decode($detail['attach'], true) : null;

        // 统计
        $sdb->table('salary_calculation_user_task', 't')
            ->field('u.*, t.setting')
            ->leftJoin('salary_calculation_user', 'u', 'u.id = t.calc_u_id and t.type = 1')
            ->where('where u.calc_id = :calc_id and u.status = :status', ['calc_id' => $param['id'], 'status' => salaryCalculationModel::STATUS_FINISHED]);
        $user_list = $sdb->list();
        $detail['list'] = $user_list;
        $detail['user_count'] = [];
        foreach ($user_list as $item) {
            $setting = json_decode($item['setting'], true) ?? null;
            $status = $setting['status'] ?? 0;
            if (!isset($detail['user_count'][$status])) {
                $detail['user_count'][$status] = [
                    'status' => $status,
                    'count' => 0
                ];
            }
            $detail['user_count'][$status]['count']++;
        }
        $detail['user_count'] = array_values($detail['user_count']);

        returnSuccess($detail);
    }

    // 我的工资条
    public function getMySalaryList()
    {
        $paras_list = array('month', 'page', 'page_size');
        $param = arrangeParam($_GET, $paras_list);
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        $sdb = dbSMysql::getInstance();

        $sdb->table('salary_calculation_user_task', 't')
            ->field('t.*, sc.month, sc.calc_name')
            ->leftJoin('salary_calculation', 'sc', 'sc.id = t.calc_id')
            ->where('where t.user_id = :user_id', ['user_id' => userModel::$qwuser_id]);
        if ($param['month']) {
            $month = json_decode($param['month'], true);
            if (!empty($month)) {
                $sdb->andWhere('sc.month >= :start_month and sc.month <= :end_month', ['start_month' => $month[0], 'end_month' => $month[1]]);
            }

        }
        $data = $sdb->pages($page, $limit);
        if (empty($data['list'])) returnSuccess($data);

        $db = dbMysql::getInstance();
        // 用户信息
        $user = $db->table('qwuser')->field('id, wname')->list();
        $user = array_column($user, null, 'id');

        // 部门信息
        $department = $db->table('qwdepartment')->field('wp_id, name')->list();
        $department = array_column($department, 'name', 'wp_id');

        foreach ($data['list'] as &$item) {
            $item['user_name'] = $user[$item['user_id']]['wname'] ?? '';
            $item['department_name'] = $department[$item['department_id']] ?? '';
            $item['setting'] = json_decode($item['setting'], true) ?? null;
            $item['calc_u_id'] = json_decode($item['calc_u_id'], true) ?? null;
        }

        returnSuccess($data);
    }

    // 我的工资条
    public function getMySalaryDetail()
    {
        $paras_list = array('id');
        $param = arrangeParam($_GET, $paras_list);
        if (!$param['id']) returnError('ID不能为空');

        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_calculation_user_task', 't')
            ->where('where t.id = :id', ['id' => $param['id']]);
        $detail = $sdb->one();
        if (empty($detail)) returnSuccess('工资条不存在');
        $detail['calc_u_id'] = $detail['type'] == 2 ? (json_decode($detail['calc_u_id'], true) ?? null) : [$detail['calc_u_id']];
        empty($detail['calc_u_id']) && returnError('工资计算不存在');

        $calc_id = $detail['calc_id'];
        // 查询薪资计算
        $sdb->table('salary_calculation', 'sc')
            ->field('sc.attach')
            ->where('where sc.id = :id', ['id' => $calc_id]);
        $calc = $sdb->one();
        $attach = json_decode($calc['attach'], true) ?? null;
        $detail['config'] = $attach['scheme']['config'] ?? null;
        $detail['items'] = $attach['items'] ?? null;

        $setting = json_decode($detail['setting'], true) ?? null;
        $detail['setting'] = $setting;
        if (isset($setting['remind_count']) && !$setting['remind_count']) returnError('没有查看次数!');

        $sdb->table('salary_calculation_user', 'scu')
            ->field('scu.*, sc.month')
            ->leftJoin('salary_calculation', 'sc', 'sc.id = scu.calc_id')
            ->where('where sc.id = :id and sc.status = :status and scu.status = :status', ['id' => $detail['calc_id'], 'status' => salaryCalculationModel::STATUS_FINISHED])
            ->whereIn('scu.id', $detail['calc_u_id']);
        $list = $sdb->list();
        if (!empty($list)) {
            foreach ($list as &$item) {
                $item['attach'] = json_decode($item['attach'], true) ?? null;
                $item['result'] = json_decode($item['result'], true) ?? null;
                unset($item['audit_attach']);
            }
        }

        // 查看次数减1
        $setting['remind_count']--;
        $setting['status'] = 1;
        $sdb->table('salary_calculation_user_task')
            ->where('where id = :id', ['id' => $param['id']])
            ->update([
                'setting' => json_encode($setting)
            ]);

        $detail['list'] = $list;

        returnSuccess($detail);
    }

    // 过滤数据
    private static function filterData($data, $keys, $ids = [])
    {
        // 固定薪资项
        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_title')->field('id, name, name_en, module');
        $salary_title = $sdb->list();
        $salary_title = array_column($salary_title, 'name', 'name_en');

        // 浮动薪资项
        $sdb->table('salary_item')->field('id, item_name as name')->where('where is_delete = 0');
        $salary_item = $sdb->list();
        $salary_item = array_column($salary_item, 'name', 'id');


        if (empty($data) || empty($keys)) return [];
        $ret_data = [];

        foreach ($data as $item) {
            $item_data = [];
            // 进行筛选
            if (!empty($ids)) {
                if (!in_array($item['id'], $ids)) continue;
            }
            $item_result = $item['result']['item_result'] ?? [];
            $item_result = array_column($item_result, 'item_result', 'item_id');

            foreach ($keys as $key) {
                if (is_numeric($key)) { // 浮动薪资项
                    $item_data[$salary_item[$key]] = $item_result[$key] ?? '';
                } else {
                    $item_data[$salary_title[$key]] = salaryCalculationModel::getValueByPath($item['result'], $key);
                }
            }
            $ret_data[] = $item_data;
        }

        return $ret_data;


    }


}