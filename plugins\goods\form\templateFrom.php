<?php
/**
 * @author: zhangguoming
 * @Time: 2024/3/28 11:13
 */

namespace  plugins\goods\form;

use core\lib\config;
use core\lib\db\dbMysql;

class templateFrom
{
    /**
     * @param $node_list  选择节点的json格式
     * @param $node_ids   选择节点的id集合
     * @return array
     * @throws \core\lib\ExceptionError
     */
    public static function getTplData($node_list, $node_ids){
        $db = dbMysql::getInstance();
        $db->table('node');
        $db->field('id,node_name,manage_info,is_developer,description,event_detail,send_copy_user,expected_day,need_check,check_user_info');
        $db->whereIn('id',$node_ids);
        $node_data = $db->list();
        $new_node_list = [];
        foreach ($node_data as $node_) {
            $node_['begin_time'] = '';
            $node_['complete_time'] = '';
            $node_['status'] = 0;
            $node_['remark'] = '';
            if ($node_['need_check'] == 1) {
                $node_['check_info']['is_pass'] = -1;
            }
            $event_detail = json_decode($node_['event_detail'],true);
            $node_['event_detail'] = $event_detail;
            $new_node_list[$node_['id']] = $node_;
        }
        $new_node_data = [];
        foreach ($node_list as $v1) {
            $node_item = [];
            foreach ($v1 as $v2) {
                $node_item[] = $new_node_list[$v2['id']];
            }
            $new_node_data[] = $node_item;
        }
        return $new_node_data;
    }

    /**
     * @param $id 保存模板的id
     * @return void  移除其他模板的开启状态
     */
    public static function setTplStatus($id,$flow_path_id,$batch){
        if ($flow_path_id == 3){
            return;
        }
        $db = dbMysql::getInstance();
        $db->query('update oa_template set status = 0 where id <> :id and flow_path_id=:flow_path_id and batch=:batch',['id'=>$id,'flow_path_id'=>$flow_path_id,'batch'=>$batch]);
    }

    public static function getTplDataByFlowId($flow_path_id,$batch,$goods_manage) {
        $db = dbMysql::getInstance();
        $db->table('template');
        $db->where('where status = 1 and is_delete = 0 and flow_path_id = :flow_path_id',['flow_path_id'=>$flow_path_id]);
        if ($flow_path_id == 1) {
            $db->andWhere('and batch=:batch',['batch'=>$batch]);
        }
        $tpl_data = $db->one();
        $tpl_name = config::getDataName('flow_path',$flow_path_id);
        if (!$tpl_data) {
            switch ($flow_path_id) {
                case 1:
                    //大货样
                    SetReturn('-1','暂未设置【'.$tpl_name.'-'.($batch == 1?'首批':'次批').'】模板，请联系管理员');
                    break;
                case 2:
                    //出货样
                    SetReturn('-1','暂未设置【'.$tpl_name.'】模板，请联系管理员');
                    break;
                case 3:
                    SetReturn('-1','暂未设置【'.$tpl_name.'】模板，请联系管理员');
                    break;
                default:
                    SetReturn('-1','没有该类型的模板');
            }
        }
        $tpl_data_array = self::changeTplData(json_decode($tpl_data['tpl_data'],true),$goods_manage);
        $tpl_data['tpl_data'] = json_encode($tpl_data_array,JSON_UNESCAPED_UNICODE);
        return $tpl_data;
    }

    public static function changeTplData(array $tpl_data_array,$goods_manage){
        foreach ($tpl_data_array as $k1=>$node_list) {
            foreach ($node_list as $k2=>$node) {
                if (!empty($node['is_developer'])) {
                    $tpl_data_array[$k1][$k2]['manage_info'] = $goods_manage;
                }
                if (empty($tpl_data_array[$k1][$k2]['manage_info']) || $tpl_data_array[$k1][$k2]['manage_info'] == '[]') {
                    SetReturn('-1','模板对应节点【'.$node['node_name'].'】负责人存在问题');
                }
                foreach ($node['event_detail'] as $k3=>$event_list) {
                    foreach ($event_list as $k4=>$event) {
                        if (!empty($event['is_developer'])) {
                            if ($event['event_type'] != 4) {
                                $tpl_data_array[$k1][$k2]['event_detail'][$k3][$k4]['manage_info'] = $goods_manage;
                            } else {
                                $event_manage =  array_merge(json_decode($event['manage_info'],true),json_decode($goods_manage,true));
                                $event_manage = arrayUnique2($event_manage,'id');
                                $tpl_data_array[$k1][$k2]['event_detail'][$k3][$k4]['manage_info'] = json_encode($event_manage,JSON_UNESCAPED_UNICODE);
                            }
                        }
                        if (empty($tpl_data_array[$k1][$k2]['event_detail'][$k3][$k4]['manage_info']) || $tpl_data_array[$k1][$k2]['event_detail'][$k3][$k4]['manage_info'] == '[]') {
                            SetReturn('-1','模板对应事件【'.$node['node_name'].'】负责人存在问题');
                        }
                    }
                }
            }
        }
        return $tpl_data_array;
    }
}