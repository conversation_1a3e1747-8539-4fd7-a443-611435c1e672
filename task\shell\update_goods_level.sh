# !/bin/bash
# oa商品等级更新
# 获取第一个参数ye
token='576d9908a51f11ee97f19c2dcd695fd0'
m_date=$1
while true; do
    # 发送POST请求，这里假设URL为http://example.com/api，根据实际情况替换
#    response=$(curl -s -X POST -d "m_date=$m_date&token=$token" 'http://171.223.214.187:8901/task/financialTask/updateGoodsLevel')
#    response=$(curl -s -X POST -d "m_date=$m_date&token=$token" 'http://oa.com/task/financialTask/updateGoodsLevel')
    response=$(curl -s -X POST -d "m_date=$m_date&token=$token" 'http://39.101.133.112:8082/task/financialTask/updateGoodsLevel')
     echo "$response"
    # 从响应中提取code字段的值
    code=$(echo "$response" | grep -oP '"code":\K\d+')
    # 检查code字段的值
    current_time=$(date +"%Y-%m-%d %H:%M:%S")
    if [ "$code" -ne 2 ]; then
        echo "code=$code，继续请求$current_time"
    else
       break ;
    fi
done
current_time=$(date +"%Y-%m-%d %H:%M:%S")
echo "同步完成，退出。 $current_time"



