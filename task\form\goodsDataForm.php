<?php

/**
 * @author: zhangguoming
 * @Time: 2024/6/14 9:08
 */

namespace task\form;
use core\lib\db\dbFMysql;
use core\lib\db\dbSshMysql;
use financial\common\importNeedDataBase;

class goodsDataForm
{
    //增量数据重新匹配
    public static function countAginGoodsData($list,$m_date,$year) {
        //国家数据
        $country_name = array_column($list,'country');
        $country_ = importNeedDataBase::getCountryListByName($country_name);
        //运营
        $yunying_name = array_column($list,'yunying');
        $qwuser_ = importNeedDataBase::qwuserByNameList($yunying_name);
        //项目
        $project_ = importNeedDataBase::getProjectAll();
        //店铺
        $store_name = array_column($list,'store_name');
        $store_ = importNeedDataBase::getSellerByName($store_name);
        //保存数据
        $error_list = [];
        $db = dbFMysql::getInstance();
        $db->beginTransaction();
        foreach ($list as $k=>$v) {
            $row_num = $k + 1;
            //验证字段内容
            if (empty($v['country']) || empty($v['store_name']) || empty($v['project_name']) || empty($v['yunying']) || empty($v['asin']) || empty($v['p_asin']) || empty($v['sku'])) {
                $error_list[] = ['error_reason' => '运营部门|运营人员|国家|ASIN|父ASIN|仓库SKU|店铺，不能为空', 'data' => $v, 'row_num' => $row_num];
                continue;
            }
            //运营
            if (!isset($qwuser_[$v['yunying']])) {
                $error_list[] = ['error_reason'=>'未查询到该运营人员','data'=>$v,'row_num'=>$row_num];
                continue;
            } else {
                $yunying_id = $qwuser_[$v['yunying']];
            }
            //国家
            if (!isset($country_[$v['country']])) {
                $error_list[] = ['error_reason' => '未查询到国家名', 'data' => $v, 'row_num' => $row_num];
                continue;
            } else {
                $countryCode = $country_[$v['country']]['code'];
            }
            //店铺
            if (!isset($store_[$v['store_name']])) {
                $error_list[] = ['error_reason' => '未查询到店铺', 'data' => $v, 'row_num' => $row_num];
                continue;
            } else {
                $sid = $store_[$v['store_name']]['sid'];
            }
            //项目及项目中的负责人
            if (!isset($project_[$v['project_name']])) {
                $error_list[] = ['error_reason'=>'未查询到项目','data'=>$v,'row_num'=>$row_num];
                continue;
            } else {
                $project_id = $project_[$v['project_name']]['id'];
                $p_user_ids = $project_[$v['project_name']]['user_ids'];
                if (!count($p_user_ids)) {
                    $error_list[] = ['error_reason'=>'项目中未设置运营','data'=>$v,'row_num'=>$row_num];
                    continue;
                }
                if (!in_array($yunying_id,$p_user_ids)) {
                    $error_list[] = ['error_reason'=>'项目中不存在该运营','data'=>$v,'row_num'=>$row_num];
                    continue;
                }
            }
            //查msku表中是否有数据
            $msku_data = $db->table('msku_report_data_' . $year)
                ->where('where is_delete=0 and yunying_id=:yunying_id and project_id=:project_id and countryCode=:country_code and asin=:asin and parentAsin=:parentAsin and localSku=:sku and sid=:sid and reportDateMonth=:m_date', ['m_date' => $m_date, 'yunying_id' => $yunying_id, 'project_id' => $project_id, 'country_code' => $countryCode, 'asin' => $v['asin'], 'sku' => $v['sku'], 'sid' => $sid, 'parentAsin' => $v['p_asin']])
                ->field('id')
                ->order('id asc')
                ->one();
            if (!$msku_data) {
                $error_list[] = ['error_reason' => '未找到对应musk报表数据', 'data' => $v, 'row_num' => $row_num];
                continue;
            }
            $update_data = [
                'base_id' => $msku_data['id'],
                'is_error' => 0,
                'project_id' => $project_id,
                'yunying_id' => $yunying_id,
                'country_code' => $countryCode,
                'updated_time' => date('Y-m-d H:i:s'),
                'sid' => $sid,
            ];
            //增量数据修改
            $db->table('goods_data_' . $year)->where('where id=:id', ['id' => $v['id']])
                ->update($update_data);
            //全量数据修改
            $db->table('msku_report_data_' . $year)
                ->where('where id=:base_id',['base_id'=>$msku_data['id']])
                ->update([
                    'key1'=>$v['key1'],
                    'key9'=>$v['key9'],
                    'key20'=>$v['key20'],
                    'key21'=>$v['key21'],
                    'key7'=>$v['key7'],
                ]);
        }
        if (count($error_list)) {
            foreach ($error_list as $v) {
                $data_ = $v['data'];
                $db->table('goods_data_import_error_log')
                    ->insert([
                        'import_id'=>$data_['import_id'],
                        'data'=>json_encode($data_,JSON_UNESCAPED_UNICODE),
                        'error_reason'=>$v['error_reason'],
                        'row_num'=>$v['row_num']
                    ]);
            }
        }
        $db->commit();
    }
    public static function countAginGoodsStock($list,$m_date,$year) {
        //国家数据
        $country_name = array_column($list,'country');
        $country_ = importNeedDataBase::getCountryListByName($country_name);
        //运营
        $yunying_name = array_column($list,'yunying');
        $qwuser_ = importNeedDataBase::qwuserByNameList($yunying_name);
        //项目
        $project_ = importNeedDataBase::getProjectAll();
        //店铺
        $store_name = array_column($list,'store_name');
        $store_ = importNeedDataBase::getSellerByName($store_name);
        //保存数据
        $error_list = [];
        $db = dbFMysql::getInstance();
        $db->beginTransaction();
        foreach ($list as $k=>$v) {
            $row_num = $k+1;
            //验证字段内容
            if (empty($v['country']) || empty($v['store_name']) || empty($v['project_name']) || empty($v['yunying']) || empty($v['asin']) || empty($v['p_asin']) || empty($v['sku'])) {
                $error_list[] = ['error_reason'=>'运营部门|运营人员|国家|ASIN|父ASIN|仓库SKU|店铺，不能为空','data'=>$v,'row_num'=>$row_num];
                continue;
            }
            //运营
            if (!isset($qwuser_[$v['yunying']])) {
                $error_list[] = ['error_reason'=>'未查询到该运营人员','data'=>$v,'row_num'=>$row_num];
                continue;
            } else {
                $yunying_id = $qwuser_[$v['yunying']];
            }
            //国家
            if (!isset($country_[$v['country']])) {
                $error_list[] = ['error_reason'=>'未查询到国家名','data'=>$v,'row_num'=>$row_num];
                continue;
            } else {
                $countryCode = $country_[$v['country']]['code'];
            }
            //店铺
            if (!isset($store_[$v['store_name']])) {
                $error_list[] = ['error_reason'=>'未查询到店铺','data'=>$v,'row_num'=>$row_num];
                continue;
            } else {
                $sid = $store_[$v['store_name']]['sid'];
            }
            //项目及项目中的负责人
            if (!isset($project_[$v['project_name']])) {
                $error_list[] = ['error_reason'=>'未查询到项目','data'=>$v,'row_num'=>$row_num];
                continue;
            } else {
                $project_id = $project_[$v['project_name']]['id'];
                $p_user_ids = $project_[$v['project_name']]['user_ids'];
                if (!count($p_user_ids)) {
                    $error_list[] = ['error_reason'=>'项目中未设置运营','data'=>$v,'row_num'=>$row_num];
                    continue;
                }
                if (!in_array($yunying_id,$p_user_ids)) {
                    $error_list[] = ['error_reason'=>'项目中不存在该运营','data'=>$v,'row_num'=>$row_num];
                    continue;
                }
            }
            //查msku表中是否有数据
            $msku_data = $db->table('msku_report_data_'.$year)
                ->where('where is_delete=0 and yunying_id=:yunying_id and project_id=:project_id and countryCode=:country_code and asin=:asin and parentAsin=:parentAsin and localSku=:sku and sid=:sid and reportDateMonth=:m_date',['m_date'=>$m_date,'yunying_id'=>$yunying_id,'project_id'=>$project_id,'country_code'=>$countryCode,'asin'=>$v['asin'],'sku'=>$v['sku'],'sid'=>$sid,'parentAsin'=>$v['p_asin']])
                ->field('id')
                ->one();
            if(!$msku_data) {
                $error_list[] = ['error_reason'=>'未找到对应musk报表数据','data'=>$v,'row_num'=>$row_num];
                continue;
            }
            $update_data = [
                'base_id'=>$msku_data['id'],
                'is_error'=>0,
                'project_id'=>$project_id,
                'yunying_id'=>$yunying_id,
                'country_code'=>$countryCode,
                'created_time'=>date('Y-m-d H:i:s'),
                'sid'=>$sid,
            ];
            $db->table('goods_stock_'.$year)->where('where id=:id',['id'=>$v['id']])
                ->update($update_data);
            //保存数据到同步数据
            $db->table('msku_report_data_'.$year)
                ->where('where id=:base_id',['base_id'=>$msku_data['id']])
                ->update([
                    'key15'=>$v['fbm_local_num'],
                    'key16'=>$v['fbm_local_overseas_num'],
                    'key17'=>$v['fbm_local_overseas_price'],
                ]);
        }
        if (count($error_list)) {
            foreach ($error_list as $v) {
                $data_ = $v['data'];
                $db->table('goods_stock_import_error_log')
                    ->insert([
                        'import_id'=>$data_['import_id'],
                        'data'=>json_encode($v['data'],JSON_UNESCAPED_UNICODE),
                        'error_reason'=>$v['error_reason'],
                        'row_num'=>$v['row_num']
                    ]);
            }
        }
        $db->commit();
        //进度保存
    }

}
