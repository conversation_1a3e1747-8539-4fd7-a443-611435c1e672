<?php

namespace plugins\shop\controller;

use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use Exception;
use plugins\shop\models\phoneCardModel;
use plugins\shop\models\relationModel;
use plugins\shop\models\userModel;
use Rap2hpoutre\FastExcel\FastExcel;

class phoneCardController extends baseController
{
    // 列表
    public function getList()
    {
        $paras_list = array('phone_number', 'user_id', 'user_name', 'card_status', 'user_status', 'register_date', 'is_use_self',
            'phone_usage', 'relations', 'use_status', 'charge_manage', 'phone_type', 'update_time', 'page', 'page_size');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $param['page'] = $param['page'] ?? 1;
        $param['page_size'] = $param['page_size'] ?? 10;

        $model = new phoneCardModel();
        $list = $model->getList($param);

        $ids = array_column($list['list'], 'id');
        $relation =  (new relationModel())->getReverseRelation('phone_card', $ids);
        foreach ($list['list'] as &$item) {
            $item['relations'] = $relation[$item['id']] ?? [];
        }
        returnSuccess($list);
    }

    // 新增
    public static function add()
    {
        $model = new phoneCardModel();
        $param = array_intersect_key($_POST, array_flip(array_keys(phoneCardModel::$paras_list)));
        $id = $_POST['id'] ?? 0;

        try {
            $model->dataValidCheck($param);
        } catch (Exception $e) {
            returnError($e->getMessage());
        }

        // 唯一性检验
        $detail = $model->getByPhoneNumber($param['phone_number'], $id);
        if ($detail) {
            returnError('电话号已存在');
        }

        if ($id) {
            // 验证数据正确性
            $detail = $model->getById($id);
            if (!$detail) {
                returnError('数据不存在');
            }
            $model->edit($param, $id, $detail);
            returnSuccess([], '编辑成功');
        } else {
            $model->add($param);
            returnSuccess([], '添加成功');
        }
    }

    // 详情
    public static function detail()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $model = new phoneCardModel();
        $detail = $model->getById($id);
        $maps = $model::getMaps();
        $detail = $model->formatItem($detail, $maps);

        $relation =  (new relationModel())->getReverseRelation('phone_card', [$id]);
        $detail['relations'] = $relation[$item['id']] ?? [];

        returnSuccess($detail);
    }

    // 获取日志
    public static function getLog()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $db = dbShopMysql::getInstance();
        $list = $db->table('operation_log')
            ->where('where table_name = :table_name and table_id = :table_id', ['table_name' => 'phone_card', 'table_id' => $id])
            ->order('id desc')->list();

        $users =  redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');
        $model = new phoneCardModel();

        foreach ($list as &$item) {
            $item['operator_name'] = $users[$item['operator']] ?? '';
            $attach = json_decode($item['attach'], true);
            $item['before'] = $attach['before'] ?? null;
            $item['before'] = $model->formatItem($item['before'], ['users' => $users]);
            $item['after'] = $attach['after'] ?? null;
            $item['after'] = $model->formatItem($item['after'], ['users' => $users]);
            unset($item['attach']);
        }
        returnSuccess($list);
    }

    // 导入
    public function import()
    {
        $paras_list = array('excel_src');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        $excel_url = SELF_FK . $param['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格不存在');
        }

        $data = (new FastExcel())->import($excel_url, function ($row) {
            // 将每一项 trim 去空格，再判断整行是否为空
            $cleaned = collect($row)->map(function ($value) {
                if (is_string($value)) {
                    return trim((string)$value);
                }
                return $value;
            });
            // 全部为空则跳过
            if ($cleaned->filter()->isEmpty()) {
                return null;
            }
            return $cleaned->toArray(); // 返回处理后的数据
        });

        // 判断表头
        $first_user = $data[0];
        if (empty($first_user['电话号码']) || empty($first_user['登记日期']) || empty($first_user['电话卡归属类型']) ||
            empty($first_user['电话卡归属人']) || empty($first_user['电话卡类型']) || empty($first_user['套餐情况']) ||
            empty($first_user['是否自用']) || empty($first_user['电话卡保管人']) || empty($first_user['公司充值管理']) ||
            empty($first_user['使用状态']) || empty($first_user['电话卡状态']) || !isset($first_user['备注'])) {
            returnError('表头错误');
        }

        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_id', 'user_name');

        $model = new phoneCardModel();
        $paras_list = $model::$paras_list;
        unset($paras_list['phone_usage']);
        $import_data = [];
        $error_data = [];
        foreach ($data as $row) {
            $error_msg = [];
            empty($row['电话号码']) && $error_msg[] = '电话号码不能为空';
            // 唯一性检验
            $detail = $model->getByPhoneNumber($row['电话号码']);
            if ($detail) {
                $error_msg[] = '电话号已存在';
            }
            try {
                $register_date = $row['登记日期']->format('Y-m-d');
                if (empty($row['登记日期']) || strtotime($register_date) === false) {
                    $error_msg[] = '登记日期格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '登记日期格式错误';
            }
            empty($row['电话卡归属人']) && $error_msg[] = '电话卡归属人不能为空';
            if ($row['电话卡归属类型'] == '外部人员') {
                $user_name = $row['电话卡归属人'];
            } elseif ($row['电话卡归属类型'] == '公司员工') {
                if (!isset($users[$row['电话卡归属人']])) {
                    $error_msg[] = '找不到公司员工';
                } else {
                    $user_id = $users[$row['电话卡归属人']];
                }
            } else {
                $error_msg[] = '电话卡归属类型错误';
            }
            empty($row['电话卡类型']) && $error_msg[] = '电话卡类型不能为空';
            empty($row['套餐情况']) && $error_msg[] = '套餐情况不能为空';
            empty($row['是否自用']) && $error_msg[] = '是否自用不能为空';
            !in_array($row['是否自用'], ['是', '否']) && $error_msg[] = '是否自用错误';
            empty($row['电话卡保管人']) && $error_msg[] = '电话卡保管人不能为空';
            empty($row['公司充值管理']) && $error_msg[] = '公司充值管理不能为空';
            !in_array($row['公司充值管理'], ['是', '否']) && $error_msg[] = '公司充值管理错误';
            empty($row['使用状态']) && $error_msg[] = '使用状态不能为空';
            !in_array($row['使用状态'], $model::$use_status) && $error_msg[] = '使用状态错误';
            empty($row['电话卡状态']) && $error_msg[] = '电话卡状态不能为空';
            !in_array($row['电话卡状态'], $model::$card_status) && $error_msg[] = '电话卡状态错误';

            $item_data = [
                'user_id' => $user_id ?? 0,
                'user_name' => $user_name ?? '',
                'phone_number' => $row['电话号码'],
                'register_date' => $register_date ?? null,
                'phone_type' => $row['电话卡类型'],
                'phone_combo' => $row['套餐情况'],
                'is_use_self' => $row['是否自用'],
                'use_status' => $row['使用状态'],
                'phone_manager' => $row['电话卡保管人'],
                'charge_manage' => $row['公司充值管理'],
                'card_status' => $row['电话卡状态'],
                'remark' => $row['备注'],
            ];

            $check_row = $model->dataValidCheck($item_data, $paras_list, false);
            if (!empty($check_row['error'])) {
                $error_msg = array_merge($error_msg, $check_row['error']);
            }

            if (!empty($error_msg)) {
                $row['失败原因'] = implode(';', $error_msg);
                $error_data[] = $row;
                continue;
            }

            $import_data[] = $item_data;
            $id = $model->add($item_data, '导入');

        }

        if (count($error_data) > 0) {
            // 使用项目根目录动态构建导出文件路径
            $src = '/public/shop/temp/error/user_import_errors' . time() . rand(100, 1000) . '.xlsx';
            $exportPath = SELF_FK . $src;

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径

            returnSuccess(['error_file' => $src, 'error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $error_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $import_data], '导入成功');
        }

    }

    // 导出
    public function export()
    {
        $paras_list = array('phone_number', 'user_id', 'user_name', 'card_status', 'user_status', 'register_date', 'is_use_self',
            'phone_usage', 'relations', 'use_status', 'charge_manage', 'phone_type', 'update_time', 'keys', 'ids'
        );
        $param = array_intersect_key($_POST, array_flip($paras_list));

        $model = new phoneCardModel();
        $data = $model->getList($param, 'id desc', true);
        if (empty($data)) {
            returnError('没有数据可导出');
        }
        $export_data = [];
        foreach ($data as $item) {
            $keys = ['电话号码', '登记时间', '电话卡归属类型', '电话卡归属人', '电话卡类型', '套餐情况', '是否自用',
                '电话卡保管人', '公司充值管理', '电话卡状态', '用途', '备注'];
            $sortedItem = [];
            foreach ($keys as $key) {
                if (!empty($param['keys']) && !in_array($key, $param['keys'])) continue;
                if ($key == '电话卡归属类型') {
                    $sortedItem[$key] = $item['电话卡归属人id'] > 0 ? '公司员工' : '外部人员';
                } elseif ($key == '用途') {
                    $sortedItem[$key] = is_array($item['用途']) ? implode(';', $item['用途']) : $item['用途'];
                } else {
                    $sortedItem[$key] = $item[$key] ?? '';
                }
            }
            $export_data[] = $sortedItem;
        }

        $filePath = '/public/shop/temp/phone_card_' . date('YmdHis') . rand(1,1000).  '.xlsx';
        (new FastExcel($export_data))->export(SELF_FK.$filePath);

        returnSuccess(['src' => $filePath], '导出成功');
    }

    // 批量编辑
    public static function editBatch()
    {
        $paras_list = array('data');
        $request_list = ['data' => '编辑数据'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

        if (count($param['data']) > 500) {
            returnError('批量编辑数据不能超过500条');
        }

        $phone_numbers = array_column($param['data'], 'phone_number');
        $phone_numbers = array_unique($phone_numbers);
        if (count($phone_numbers) != count($param['data'])) {
            returnError('批量编辑数据不能有重复的电话号');
        }

        $model = new phoneCardModel();
        $result = $model->editBatch($param['data']);

        returnSuccess($result);
    }
}
