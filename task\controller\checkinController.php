<?php

namespace task\controller;

use admin\models\qwModel;
use core\lib\config;
use core\lib\db\dbCMysql;
use core\lib\db\dbMysql;
use core\lib\log;
use core\lib\predisV;

class checkinController extends qwController
{
    //发送打卡数据拉取
    function getCheckinOption()
    {
        $api_token = $_POST['token'] ?? '';
        $month = $_POST['month'] ?? '';
        if ($api_token != '576d9908a51f11ee97f19c2dcd695fd0') {
            echo 1111;
            die;
        }
        $model = new qwModel();
        $res = $model->getCorpCheckinOption($month);
        returnSuccess(['res' => $res]);

    }

    function getVacation()
    {
        $api_token = $_POST['token'] ?? '';
        if ($api_token != '576d9908a51f11ee97f19c2dcd695fd0') {
            echo 1111;
            die;
        }
        $model = new qwModel();
        $res = $model->getVacation();
        returnSuccess(['res' => $res]);
    }

    function getApproval()
    {
        $month = $_POST['month'] ?? '';
        $value = $_POST['value'] ?? '';
        $new_next_cursor = $_POST['next_cursor'] ?? '';
        $api_token = $_POST['token'] ?? '';
        if ($api_token != '576d9908a51f11ee97f19c2dcd695fd0') {
            echo 1111;
            die;
        }
        $model = new qwModel();
        $res = $model->getApproval($month, $value, $new_next_cursor);
        returnSuccess(['res' => $res]);
    }

    function getApprovalDetail()
    {
        $no = $_POST['sp_no'] ?? '';
        $api_token = $_POST['token'] ?? '';
        if ($api_token != '576d9908a51f11ee97f19c2dcd695fd0') {
            echo 1111;
            die;
        }
        $model = new qwModel();
        $res = $model->getApprovalDetail($no);

        returnSuccess(['res' => $res]);
    }

    function getUser()
    {
        $month = $_POST['month'] ?? '';

        // 所有的企业打卡规则
        $cdb = dbCMysql::getInstance();
        $corpRules = $cdb->table('corp_checkin_option')->field('id, groupid, spe_workdays, spe_offdays, rule, range_info, white_users')->list();
        $corpRules = array_column($corpRules, null, 'groupid');

        // 获取所有用户
        $db = dbMysql::getInstance();
        $users = $db->table('qwuser')->field('id, wname, wdepartment_ids,wid, position')->list();
        $userMap = array_column($users, null, 'wid');

        $rules = [];

        foreach ($corpRules as $corp_rule) {
            $corp_rule['rule'] = json_decode($corp_rule['rule'], true);
            $corp_rule['white_users'] = json_decode($corp_rule['white_users'], true);
            $corp_rule['range_info'] = json_decode($corp_rule['range_info'], true);
            $departments = $corp_rule['range_info']['party_id'];
            $range_users = $corp_rule['range_info']['userid'];
            foreach ($users as $user) {
                $user_departmnets = json_decode($user['wdepartment_ids'], true);
                if (in_array($user['wid'], $range_users)) {
                    $corp_rule['checkin_users'][$user['id']] = $user['wid'];
                }
                if (array_intersect($departments, $user_departmnets)) {
                    $corp_rule['checkin_users'][$user['id']] = $user['wid'];
                }
            }
            $user_ids = array_values($corp_rule['checkin_users']);
            if (empty($corp_rule['checkin_users'])) continue;

            // 取出当前规则下的用户
            $wids = array_keys(array_intersect_key($userMap, array_flip($user_ids)));
            $rules[$corp_rule['groupid']] = ['id' => $corp_rule['groupid'], 'users' => $wids];
        }

        // 更新同步时间
        $cdb->table('sync_time')->insert([
            'type' => 3,
            'status' => 1,
            'month' => $month,
            'finish_time' => date('Y-m-d H:i:s'),
        ]);

        $rules = array_values($rules);
        returnSuccess(['res' => $rules]);


    }

    function getUserCheckin()
    {
        $month = $_POST['month'] ?? '';
        $id = $_POST['id'] ?? '';
        $user_id = $_POST['user_id'] ?? '';
        $api_token = $_POST['token'] ?? '';
        if ($api_token != '576d9908a51f11ee97f19c2dcd695fd0') {
            echo 1111;
            die;
        }
        $model = new qwModel();
        $res = $model->getUserCheckin($id, $user_id, $month);

        returnSuccess(['res' => $res]);
    }

}