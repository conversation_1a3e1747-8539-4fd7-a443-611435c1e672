<?php

namespace plugins\shop\controller;

use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use Exception;
use plugins\shop\form\messagesFrom;
use plugins\shop\models\configModel;
use plugins\shop\models\domainModel;
use plugins\shop\models\relationModel;
use plugins\shop\models\trademarkModel;
use plugins\shop\models\userModel;
use Rap2hpoutre\FastExcel\FastExcel;

class domainController extends baseController
{
    // 列表
    public function getList()
    {
        $paras_list = array('domain', 'domain_platform', 'brand_name', 'user_id', 'status', 'dep_id',
            'purchase_date', 'expire_date', 'apply_date', 'update_time', 'page', 'page_size');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $param['page'] = $param['page'] ?? 1;
        $param['page_size'] = $param['page_size'] ?? 10;

        $model = new domainModel();
        $list = $model->getList($param);
        $ids = array_column($list['list'], 'id');

        if (!empty($ids)) {
            // 查询所有关联的商标
            $relationModel = new relationModel();
            $relationList = $relationModel->getListByIds('domain', $ids, 'trademark');
            $trademarks = redisCached::getTrademark();
            $trademarks = array_column($trademarks, null, 'id');

            foreach ($list['list'] as &$item) {
                $item['trademark'] = [];
                foreach ($relationList as $relation) {
                    if ($item['id'] == $relation['to_id']) {
                        if (!isset($trademarks[$relation['from_id']])) continue;
                        $item['trademark'][] = $trademarks[$relation['from_id']];
                    }
                }
            }
        }


        returnSuccess($list);

    }

    // 申请域名
    public static function apply()
    {
        $model = new domainModel();
        $param = array_intersect_key($_POST, array_flip(['dep_id', 'remark']));

        try {
            $model->dataValidCheck($param, ['dep_id' => '部门id']);
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
        $param['apply_user_id'] = userModel::$qwuser_id;
        $id = $model->add($param, '申请域名');

        // 通知
        $users = configModel::noticeUser('domain', 'apply');
        if (!empty($users)) {
            $user_name = userModel::$wname;
            $deps = redisCached::getDepartment();
            $deps = array_column($deps, 'name', 'id');
            $dep_name = $deps[$param['dep_id']];
            $content = "【{$user_name}】提交了【{$dep_name}】的域名申请";
            messagesFrom::senMeg($users, 1 , $content, $id, '', '域名申请');
        }

        returnSuccess(['id' => $id], '申请域名成功');
    }

    // 录入/编辑
    public static function edit()
    {
        $model = new domainModel();
        if (!isset($_POST['id']) || empty($_POST['id'])) {
            returnError('id不能为空');
        }
        $id = $_POST['id'];
        $param = array_intersect_key($_POST, array_flip(array_keys(domainModel::$paras_list)));

        try {
            $model->dataValidCheck($param, domainModel::$paras_list);
        } catch (Exception $e) {
            returnError($e->getMessage());
        }

        // 唯一性检验
        $detail = $model->getByDomain($param['domain'], $id);
        if ($detail) {
            returnError('域名已存在');
        }

        $type = '编辑';
        $detail = $model->getById($id);

        if ($detail['status'] == domainModel::STATUS_APPLY) {
            $param['status'] = domainModel::STATUS_NORMAL;
            $type = '录入域名';
        }

        $model->edit($param, $id, $detail, $type);

        returnSuccess(['id' => $id], '录入成功');
    }

    // 费用申请
    public static function applyRenewal()
    {
        $model = new domainModel();
        if (!isset($_POST['id']) || empty($_POST['id'])) {
            returnError('id不能为空');
        }
        $id = $_POST['id'];
        $detail = $model->getById($id);
        $model->edit([
            'status' => domainModel::STATUS_RENEWING,
            'apply_user_id' => userModel::$qwuser_id,
        ], $_POST['id'], $detail, '申请续费');

        // 通知
        $users = configModel::noticeUser('domain', 'fee');
        if (!empty($users)) {
            $user_name = userModel::$wname;
            $content = "【{$user_name}】提交了【{$detail['domain']}】的续费申请，请您及时审核【{$detail['domain']}】的费用申请";
            messagesFrom::senMeg($users, 1 , $content, $id, '', '域名续费申请');
        }

        returnSuccess(['id' => $id], '申请续费成功');
    }

    // 费用审核
    public static function auditRenewal()
    {
        $param = array_intersect_key($_POST, array_flip(['id', 'status', 'remark']));
        if (!isset($_POST['id']) || empty($_POST['id'])) {
            returnError('id不能为空');
        }
        $id = $_POST['id'];
        $model = new domainModel();
        $detail = $model->getById($id);
        if ($param['status'] == 1) {
            $status = domainModel::STATUS_WAIT_RENEWAL;
        } else {
            $status = domainModel::STATUS_NORMAL;
        }
        $model->edit([
            'status' => $status, 
            'remark' => $param['remark'],
            'operator' => userModel::$qwuser_id,
        ], $_POST['id'], $detail, '费用审核');

        if ($param['status'] != 1) {
            $users = redisCached::getUserInfo();
            $users = array_column($users, 'user_wid', 'user_id');
            $content = "您提交的【{$detail['domain']}】域名续费申请未审核通过";
            messagesFrom::senMeg([$users[$detail['apply_user_id']]], 1 , $content, $id, '', '域名费用审核不通过');
        } else {
            // 通知
            $users = configModel::noticeUser('domain', 'confirm');
            if (!empty($users)) {
                $content = "【{$detail['domain']}】的费用审核已通过，请您及时进行为该域名续费";
                messagesFrom::senMeg($users, 1 , $content, $id, '', '域名费用确认');
            }
        }

        returnSuccess(['id' => $id], '费用审核成功');
    }

    // 费用确认
    public static function confirmRenewal()
    {
        $param = array_intersect_key($_POST, array_flip(['id', 'cost', 'purchase_date', 'expire_date', 'valid_days', 'remark']));
        if (!isset($_POST['id']) || empty($_POST['id'])) {
            returnError('id不能为空');
        }
        $id = $_POST['id'];
        $model = new domainModel();
        $detail = $model->getById($id);
        $model->edit([
            'status' => domainModel::STATUS_NORMAL,
            'cost' => $param['cost'],
            'purchase_date' => $param['purchase_date'],
            'expire_date' => $param['expire_date'],
            'valid_days' => $param['valid_days'],
            'operator' => userModel::$qwuser_id,
        ], $_POST['id'], $detail, '费用确认');

        returnSuccess(['id' => $id], '费用确认成功');
    }

    // 详情
    public static function detail()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $model = new domainModel();
        $detail = $model->getById($id);
        $detail = $model->formatItem($detail);

        $relationModel = new relationModel();
        $relationList = $relationModel->getListByIds('domain', [$detail['id']], 'trademark');
        $trademarks = redisCached::getTrademark();
        $trademarks = array_column($trademarks, null, 'id');
        $detail['trademark'] = [];

        foreach ($relationList as $relation) {
            $detail['trademark'][] = $trademarks[$relation['from_id']];
        }

        returnSuccess($detail);
    }

    // 日志
    public static function getLog()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $db = dbShopMysql::getInstance();
        $list = $db->table('operation_log')->where('where table_name = :table_name and table_id = :table_id', 
        ['table_name' => 'domain', 'table_id' => $id])->order('id desc')->list();

        $users =  redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        $model = new domainModel();
        $maps = $model::getMaps();

        foreach ($list as &$item) {
            $item['operator_name'] = $users[$item['operator']] ?? '';
            $attach = json_decode($item['attach'], true);
            $item['before'] = $attach['before'] ?? null;
            $item['before'] = $model->formatItem($item['before'], $maps);
            $item['after'] = $attach['after'] ?? null;
            $item['after'] = $model->formatItem($item['after'], $maps);
            unset($item['attach']);
        }
        returnSuccess($list);
    }

    // 导入
    public function import()
    {
        $paras_list = array('excel_src');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        $excel_url = SELF_FK . $param['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格不存在');
        }

        $data = (new FastExcel())->import($excel_url, function ($row) {
            // 将每一项 trim 去空格，再判断整行是否为空
            $cleaned = collect($row)->map(function ($value) {
                if (is_string($value)) {
                    return trim((string)$value);
                }
                return $value;
            });
            // 全部为空则跳过
            if ($cleaned->filter()->isEmpty()) {
                return null;
            }
            return $cleaned->toArray(); // 返回处理后的数据
        });
        // 判断表头
        $first = $data[0];
        if (empty($first['域名']) || empty($first['域名邮箱']) || empty($first['域名平台']) ||
            empty($first['购买时间']) || empty($first['到期日期']) || empty($first['有效天数']) ||
            empty($first['使用部门']) || !isset($first['备注'])) {
            returnError($first);
        }

        $email = redisCached::getEmail();
        $emails = array_column($email, 'id', 'email_account');

        $deps = redisCached::getDepartment();
        $deps = array_column($deps, 'id', 'name');


        $model = new domainModel();
        $param_list = domainModel::$paras_list;
        $param_list['dep_id'] = '使用部门';
        $import_data = [];
        $error_data = [];
        foreach ($data as $row) {
            $error_msg = [];
            empty($row['域名']) && $error_msg[] = '域名不能为空';
            // 唯一性检验
            $detail = $model->getByDomain($row['域名']);
            if ($detail) {
                $error_msg[] = '域名已存在';
            }
            empty($row['域名邮箱']) && $error_msg[] = '域名邮箱不能为空';
            if (!isset($emails[$row['域名邮箱']])) {
                $error_msg[] = '域名邮箱不存在';
            }

            empty($row['域名平台']) && $error_msg[] = '域名平台不能为空';
            empty($row['购买时间']) && $error_msg[] = '购买时间不能为空';
            try {
                $purchase_date = $row['购买时间']->format('Y-m-d');
                if (empty($purchase_date) || strtotime($purchase_date) === false) {
                    $error_msg[] = '购买时间格式错误';
                }
            } catch (Exception $e) {
                $error_msg[] = '购买时间格式错误';
            }
            empty($row['到期日期']) && $error_msg[] = '到期时间不能为空';
            try {
                $expire_date = $row['到期日期']->format('Y-m-d');
                if (empty($expire_date) || strtotime($expire_date) === false) {
                    $error_msg[] = '到期时间格式错误';
                }
            } catch (Exception $e) {
                $error_msg[] = '到期时间格式错误';
            }
            empty($row['有效天数']) && $error_msg[] = '有效天数不能为空';
            if (!is_numeric($row['有效天数'])) {
                $error_msg[] = '有效天数必须为数字';
            }
            empty($row['使用部门']) && $error_msg[] = '使用部门不能为空';
            if (!isset($deps[$row['使用部门']])) {
                $error_msg[] = '使用部门不存在';
            }

            $item_data = [
                'domain' => $row['域名'],
                'domain_platform' => $row['域名平台'],
                'email_id' => $emails[$row['域名邮箱']] ?? null,
                'purchase_date' => $purchase_date ?? null,
                'expire_date' => $expire_date ?? null,
                'valid_days' => $row['有效天数'],
                'dep_id' => $deps[$row['使用部门']],
                'operator' => userModel::$qwuser_id,
                'status' => domainModel::STATUS_NORMAL,
                'remark' => $row['备注'] ?? '',
            ];
            $check_row = $model->dataValidCheck($item_data, $param_list, false);
            if (!empty($check_row['error'])) {
                $error_msg = array_merge($error_msg, $check_row['error']);
            }
            if (!empty($error_msg)) {
                $row['失败原因'] = implode(';', $error_msg);
                $error_data[] = $row;
                continue;
            }

            $import_data[] = $item_data;
            $id = $model->add($item_data, '导入');
        }

        if (count($error_data) > 0) {
            // 使用项目根目录动态构建导出文件路径
            $projectRoot = SELF_FK;
            $src = '/public/shop/temp/error/user_import_errors' . time() . rand(100, 1000) . '.xlsx';
            $exportPath = $projectRoot . $src;

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径

            returnSuccess(['error_file' => $src, 'error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $error_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $import_data], '导入成功');
        }

    }

    // 导出
    public function export()
    {
        $paras_list = array('domain', 'domain_platform', 'brand_name', 'user_id', 'status', 'dep_id',
            'purchase_date', 'expire_date', 'apply_date', 'update_time', 'keys', 'ids'
        );
        $param = array_intersect_key($_POST, array_flip($paras_list));

        $model = new domainModel();
        $data = $model->getList($param, 'id desc', true);
        if (empty($data)) {
            returnError('没有数据可导出');
        }

        $export_data = [];
        foreach ($data as $item) {
            $keys = ['使用部门', '域名', '域名邮箱', '域名平台', '购买时间', '到期时间', '有效天数', '备注'];
            $sortedItem = [];
            foreach ($keys as $key) {
                if (!empty($param['keys']) && !in_array($key, $param['keys'])) continue;
                $sortedItem[$key] = $item[$key] ?? '';
            }
            $export_data[] = $sortedItem;
        }

        $filePath = '/public/shop/temp/domain_' . date('YmdHis') . rand(1,1000). '.xlsx';
        (new FastExcel($export_data))->export(SELF_FK. $filePath);

        returnSuccess(['src' => $filePath], '导出成功');

    }

    // 批量编辑
    public static function editBatch()
    {
        $paras_list = array('data');
        $request_list = ['data' => '编辑数据'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

        if (count($param['data']) > 500) {
            returnError('批量编辑数据不能超过500条');
        }

        $domains = array_column($param['data'], 'domain');
        $domains = array_unique($domains);
        if (count($domains) != count($param['data'])) {
            returnError('批量编辑数据不能有重复的域名');
        }

        $model = new domainModel();
        $result = $model->editBatch($param['data']);

        returnSuccess($result);
    }

}