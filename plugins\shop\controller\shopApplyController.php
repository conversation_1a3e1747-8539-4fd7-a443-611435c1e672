<?php
namespace plugins\shop\controller;

use core\lib\db\dbAMysql;
use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use plugins\shop\form\messagesFrom;
use plugins\shop\models\configModel;
use plugins\shop\models\shopApplyModel;
use plugins\shop\models\shopRegisterModel;
use plugins\shop\models\userModel;

class shopApplyController extends baseController
{


    /**
     * 获取列表
     */
    public function getList()
    {
        $paras_list = array('shop_number', 'dep_id', 'country_site', 'shop_type', 'status',
            'expect_date', 'bind_time', 'page', 'page_size');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $param['page'] = $param['page'] ?? 1;
        $param['page_size'] = $param['page_size'] ?? 10;

        $model = new shopApplyModel();
        $list = $model->getList($param);
        returnSuccess($list);
    }

    /**
     * 店铺申请
     */
    public function apply()
    {
        $param = array_intersect_key($_POST, array_flip(array_keys(shopApplyModel::$paras_list)));

        try {
            $model = new shopApplyModel();
            $params = $model->dataValidCheck($param, shopApplyModel::$paras_list);
            $result = $model->apply($params);
            if ($result) {
                $user_name = userModel::$wname;
                $users = configModel::noticeUser('shop_demand', 'audit', ['dep_id' => $param['dep_id']]);
                if (!empty($users)) {
                    messagesFrom::senMeg($users, 1 , "请您及时到店铺管理系统审核【{$user_name}】提交的店铺申请需求", $result, '', '店铺申请待审核');
                }
                returnSuccess();
            }
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }

    }

    /**
     * 取消申请
     */
    public function cancel()
    {
        $id = $_POST['id'] ?? 0;
        if (empty($id)) {
            returnError('id不能为空');
        }

        try {
            $model = new shopApplyModel();
            $model->cancel($id);
            returnSuccess('取消申请成功');
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }

    }

    /**
     * 审核
     */
    public function audit()
    {
        $id = $_POST['id'] ?? 0;
        if (empty($id)) {
            returnError('id不能为空');
        }
        $paras_list = array('result', 'remark');
        $param = array_intersect_key($_POST, array_flip($paras_list));

        try {
            $model = new shopApplyModel();
            $params = $model->dataValidCheck($param, ['result' => '审核结果', 'remark' => '审核意见']);
            $model->audit($id, $params['result'], $params['remark']);

            returnSuccess('审核成功');
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 编辑申请
     */
    public function edit()
    {
        $id = $_POST['id'] ?? 0;
        if (empty($id)) {
            returnError('id不能为空');
        }
        $param = array_intersect_key($_POST, array_flip(array_keys(shopApplyModel::$paras_list)));
        try {
            $model = new shopApplyModel();
            $params = $model->dataValidCheck($param, shopApplyModel::$paras_list);
            $model->edit($params,$id);
            returnSuccess('编辑成功');
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 接收申请
     */
    public function receive()
    {
        $id = $_POST['id'] ?? 0;
        if (empty($id)) {
            returnError('id不能为空');
        }

        try {
            $model = new shopApplyModel();
            $model->receive($id);

            returnSuccess('接收成功');
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 分配店铺
     */
    public function assign()
    {
        $id = $_POST['id'] ?? 0;
        if (empty($id)) {
            returnError('id不能为空');
        }
        $paras_list = array('shop_id', 'remark');
        $param = array_intersect_key($_POST, array_flip($paras_list));

        try {
            $model = new shopApplyModel();
            $model->dataValidCheck($param, $paras_list);
            $model->assign($id, $param['shop_id'], $param['remark']);
            returnSuccess('分配成功');
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 撤回分配
     */
    public function cancelAssign()
    {
        $id = $_POST['id'] ?? 0;
        if (empty($id)) {
            returnError('id不能为空');
        }

        try {
            $model = new shopApplyModel();
            $model->cancelAssign($id);
            returnSuccess('撤回分配成功');
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 获取详情
     */
    public function detail()
    {
        $id = $_GET['id'] ?? 0;
        if (empty($id)) {
            returnError('id不能为空');
        }

        $model = new shopApplyModel();
        $detail = $model->getById($id);
        $detail = $model->formatItem($detail);

        // 需要关联的注册任务
        $db = dbShopMysql::getInstance();
        $register = $db->table('shop_register')->where('shop_apply_id = :id', ['id' => $detail['id']])->one();
        $detail['shop_register'] = $register ?: null;
        if ($register) {
            $list = $db->table('operation_log')->where('where table_name = :table_name and table_id = :table_id',
                ['table_name' => 'shop_register', 'table_id' => $register['id']])->order('id desc')->list();

            $model = new shopRegisterModel();
            $maps = $model::getMaps();

            $users = $maps['users'] ?? [];
            foreach ($list as &$item) {
                $item['operator_name'] = $users[$item['operator']] ?? '';
                $attach = json_decode($item['attach'], true);
                $item['before'] = $attach['before'] ?? null;
                $item['before'] = $model->formatItem($item['before'], $maps);
                $item['after'] = $attach['after'] ?? null;
                $item['after'] = $model->formatItem($item['after'], $maps);
                unset($item['attach']);
            }

            $detail['shop_register_log'] = $list ?? [];
        }

        returnSuccess($detail);
    }

    /**
     * 催办
     */
    public function urge()
    {
        $id = $_POST['id'] ?? 0;
        if (empty($id)) {
            returnError('id不能为空');
        }
        $model = new shopApplyModel();
        $detail = $model->getById($id);
        if ($detail['status'] == $model::STATUS_WAIT_ASSIGN) {
            $user_name = userModel::$wname;
            $users = configModel::noticeUser('shop_demand', 'audit', ['dep_id' => $detail['dep_id']]);
            if (!empty($users)) {
                messagesFrom::senMeg($users, 1 , "请您及时到店铺管理系统审核【{$user_name}】提交的店铺申请需求", $id, '', '店铺申请待审核');
            }
            returnSuccess();
        } elseif ($detail['status'] == $model::STATUS_WAIT_RECEIVE) {
            $users = configModel::noticeUser('shop_demand', 'receive', ['dep_id' => $detail['dep_id']]);
            if (!empty($users)) {
                messagesFrom::senMeg($users, 1 , "您有新的店铺申请任务，请及时处理", $id, '', '店铺申请待接收');
            }
        }
        returnSuccess('催办成功');
    }

    // 获取日志
    public static function getLog()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $db = dbShopMysql::getInstance();
        $list = $db->table('operation_log')->where('where table_name = :table_name and table_id = :table_id',
            ['table_name' => 'shop_apply', 'table_id' => $id])->order('id asc')->list();

        $model = new shopApplyModel();
        $maps = $model::getMaps();
        $users = $maps['users'] ?? [];

        foreach ($list as &$item) {
            $item['operator_name'] = $users[$item['operator']] ?? '';
            $attach = json_decode($item['attach'], true);
            $item['before'] = $attach['before'] ?? null;
            $item['before'] = $model->formatItem($item['before'], $maps);
            $item['after'] = $attach['after'] ?? null;
            $item['after'] = $model->formatItem($item['after'], $maps);
            unset($item['attach']);
        }
        returnSuccess($list);
    }
}
