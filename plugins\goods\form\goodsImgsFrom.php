<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/12 15:29
 */

namespace  plugins\goods\form;

use core\lib\db\dbMysql;

class goodsImgsFrom
{
    public static function getGoodsImages(int $goods_id, array $color_ids = []) {
        //把产品图片更新上去
        $db = dbMysql::getInstance();
        $db->table('imgs_request_collection')
            ->field('id,color_id,url as img_url,thumb_src,file_name as filename,created_time as created_at')
            ->where('where goods_id=:goods_id and is_delete = 0 and type = 1 and file_type = 3 and source_type= 0',['goods_id'=>$goods_id])
            ->list();
        if (count($color_ids)) {
            $db->whereIn('color_id',$color_ids);
        }
        $app_img_list = $db->list();
        if (count($app_img_list)) {
            foreach ($app_img_list as &$v) {
                if (!empty($v['thumb_src'])) {
                    $v['img_url'] = $v['thumb_src'];
                }
                unset($v['thumb_src']);
            }
        }
        return $app_img_list;
    }
    //列表获取首图
    public static function getGoodsImagesForList($list) {
        $goods_ids = array_column($list,'id');
        if (count($goods_ids)) {
            $db = dbMysql::getInstance();
            $goods_list_imgs = $db->table('imgs_request_collection')
                ->field('id,goods_id,url,thumb_src,file_name,color_id,created_time as created_at')
                ->where('where is_delete = 0 and type = 1 and file_type = 3 and source_type= 0')
                ->whereIn('goods_id',$goods_ids)
                ->list();
            if (count($goods_list_imgs)) {
                foreach ($list as &$vv) {
                    foreach ($vv['color_relation'] as &$color_relation) {
                        $color_relation['goods_img'] = empty($vv['thumb_src'])?$vv['goods_img']:$vv['thumb_src'];
                        foreach ($goods_list_imgs as $img) {
                            if ($color_relation['goods_id'] == $img['goods_id'] && $color_relation['color_id'] == $img['color_id']) {
                                $color_relation['goods_img'] = empty($img['thumb_src'])?$img['url']:$img['thumb_src'];
                                break;
                            }
                        }
                    }
                }
            }
        }
        return $list;
    }
    public static function getGoodsFunctionForList($list) {
        $function_ids = [];
        foreach ($list as &$v) {
            $v['function_list'] = [];
            $v['function_id'] = json_decode($v['function_id']);
            $function_ids = array_merge($function_ids,$v['function_id']);
        }
        $function_ids = array_unique($function_ids);
        $db = dbMysql::getInstance();
        $function_list = $db->table('goods_function')
            ->whereIn('id',$function_ids)
            ->field('id,fc_name,fc_name_en')
            ->list();
        if (count($function_list)) {
            foreach ($list as &$v) {
                foreach ($function_list as $fuc) {
                    if (in_array($fuc['id'],$v['function_id'])) {
                        $v['function_list'][] = $fuc;
                    }
                }
            }
        }
        return $list;
    }
}