<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/13 17:52
 */

namespace financial\controller;

use core\lib\config;
use core\lib\db\dbFMysql;

class commonController
{
    //字典列表
    public function patternList(){
       //dd(hexdec('FF'));
       $paras_list = array('key');
       $param = arrangeParam($_GET, $paras_list);
       $list = [];
       if (!empty($param['key'])) {
           $key_arry = explode(',',$param['key']);
           $c_data = config::all('data_financial');
           foreach ($key_arry as $v) {
               if(isset($c_data[$v])) {
                   $list[$v] = $c_data[$v];
               }
           }
       } else {
           $list = config::all('data_financial');
       }
       returnSuccess($list);
   }
    //获取导出进度
    public function getProgressData() {
        $progress_key = $_POST['progress_key'];
        $redis = (new \core\lib\predisV())::$client;
        if (!$redis->get($progress_key)) {
            returnError('未找打对应的进度，progress_key错误');
        }
        $data = json_decode($redis->get($progress_key),true);
        returnSuccess($data);
    }
    //获取库存导入进度
    public function getGoodsStockImportProgress() {
        $import_id = $_POST['import_id']??0;
        if (!$import_id) {
            returnError('缺少必传参数');
        }
        $db = dbFMysql::getInstance();
        $data = $db->table('goods_stock_import')
            ->where('where id=:import_id',['import_id'=>$import_id])
            ->field('total,success_count,fail_count')
            ->one();
        if (!$data) {
            returnError('未找到该导入记录');
        }
        returnSuccess($data);
    }
    //获取msku报表数据全量导入数据
    public function getMskuDataImportProgress() {
        $import_id = $_POST['import_id']??0;
        if (!$import_id) {
            returnError('缺少必传参数');
        }
        $db = dbFMysql::getInstance();
        $data = $db->table('msku_report_import')
            ->where('where id=:import_id',['import_id'=>$import_id])
            ->field('total,success_count,fail_count')
            ->one();
        if (!$data) {
            returnError('未找到该导入记录');
        }
        returnSuccess($data);
    }
    //获取msku报表数据全量导入数据
    public function getGoodsDataImportProgress() {
        $import_id = $_POST['import_id']??0;
        if (!$import_id) {
            returnError('缺少必传参数');
        }
        $db = dbFMysql::getInstance();
        $data = $db->table('goods_data_import')
            ->where('where id=:import_id',['import_id'=>$import_id])
            ->field('total,success_count,fail_count')
            ->one();
        if (!$data) {
            returnError('未找到该导入记录');
        }
        returnSuccess($data);
    }
}