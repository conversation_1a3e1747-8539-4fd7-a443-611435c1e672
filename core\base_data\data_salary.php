<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/14 9:20
 */
return array(
    // 条件类型
    'rule_type'      => [
        ['id' => '1', 'name' => '条件'],
        ['id' => '2', 'name' => '条件组'],
    ],

    // 多个条件的连接符
    'link_symbol'    => [
        ['id' => '1', 'name' => '或', 'value' => '||'],
        ['id' => '2', 'name' => '且', 'value' => '&&'],
    ],

    // 规则的计算符号
    'rules_symbol'   => [
        ['id' => '1', 'name' => '>', 'value' => '>'],
        ['id' => '2', 'name' => '≥', 'value' => '>='],
        ['id' => '3', 'name' => '<', 'value' => '<'],
        ['id' => '4', 'name' => '≤', 'value' => '<='],
        ['id' => '5', 'name' => '=', 'value' => '=='],
        ['id' => '6', 'name' => '≠', 'value' => '!='],
    ],

    // 取数规则
    'calc_type'      => [
        ['id' => '1', 'name' => '原始数据', 'method' => ''],
        ['id' => '2', 'name' => '四舍五入', 'method' => 'round'],
        ['id' => '3', 'name' => '向上取整', 'method' => 'ceil'],
        ['id' => '4', 'name' => '向下取整', 'method' => 'floor'],
    ],

    // 公式计算符号
    'formula_symbol' => [
        ['id' => '1', 'name' => '+'],
        ['id' => '2', 'name' => '-'],
        ['id' => '3', 'name' => '*'],
        ['id' => '4', 'name' => '/'],
    ],

    // 归属地
    'work_place'     => [
        ['id' => '1', 'name' => '成都'],
        ['id' => '2', 'name' => '深圳'],
    ],

    // 员工状态
    'user_status'    => [
        ['id' => '1', 'name' => '在职'],
        ['id' => '2', 'name' => '试用期'],
        ['id' => '3', 'name' => '已离职'],
    ],

    // 员工类型
    'user_type'      => [
        ['id' => '1', 'name' => '全职'],
        ['id' => '2', 'name' => '实习'],
        ['id' => '3', 'name' => '兼职'],
        ['id' => '4', 'name' => '劳务派遣'],
        ['id' => '5', 'name' => '劳务外包'],
        ['id' => '6', 'name' => '退休返聘'],
    ],

    // 薪资状态
    'salary_status'  => [
        ['id' => '0', 'name' => '待定薪'],
        ['id' => '1', 'name' => '正常'],
        ['id' => '2', 'name' => '待审批'],
    ],

    'salary_change_reason'    => [
        ['id' => '1', 'name' => '晋升', 'type' => 2],
        ['id' => '2', 'name' => '试用期转正', 'type' => 1],
        ['id' => '3', 'name' => '实习期转正', 'type' => 1],
        ['id' => '8', 'name' => '实习转试用期', 'type' => 1],
        ['id' => '4', 'name' => '调岗', 'type' => 2],
        ['id' => '5', 'name' => '降级', 'type' => 2],
        ['id' => '6', 'name' => '其他', 'type' => 2],
    ],

    'salary_item_module' => [
        ['id' => '1', 'name' => '人事信息'],
        ['id' => '2', 'name' => '考勤信息'],
        ['id' => '3', 'name' => '绩效信息'],
        ['id' => '4', 'name' => '固定薪资项'],
        ['id' => '5', 'name' => '浮动薪资项'],
        ['id' => '6', 'name' => '社保公积金'],
        ['id' => '7', 'name' => '个税'],
        ['id' => '8', 'name' => '统计项'],
    ],

    // 薪资项类型
    'salary_item_tag'        => [
        ['id' => '1', 'name' => '加薪项'],
        ['id' => '2', 'name' => '扣薪项'],
        ['id' => '3', 'name' => '补贴类'],
        ['id' => '4', 'name' => '其他'],
    ],

    // 薪资项类型
    'salary_item_type'        => [
        ['id' => '1', 'name' => '税前项'],
        ['id' => '2', 'name' => '税后项'],
        ['id' => '3', 'name' => '统计项'],
    ],

    // 薪资项计算类型
    'salary_item_calc_method' => [
        ['id' => '1', 'name' => '通过公式计算'],
        ['id' => '2', 'name' => '按阶梯计算'],
    ],

    // 薪资项值类型
    'salary_item_value_type'  => [
        ['id' => '1', 'name' => '考勤', 'list' => [
            ['id' => '1', 'name' => '实际出勤天数'],
            ['id' => '2', 'name' => '应出勤天数'],
            ['id' => '3', 'name' => '满勤天数'],
        ]],
        ['id' => '2', 'name' => '删差评', 'list' => [
            ['id' => '1', 'name' => '删差评数量'],
//            ['id' => '2', 'name' => '改评成功数量'],
        ]],
        ['id' => '3', 'name' => '自定义'],
    ],

    'salary_item_value_type_1' => [
        ['id' => '1', 'name' => '实际出勤天数'],
        ['id' => '2', 'name' => '应出勤天数'],
        ['id' => '3', 'name' => '满勤天数'],
    ],

    'salary_item_value_type_2'            => [
        ['id' => '1', 'name' => '删差评数量'],
//        ['id' => '2', 'name' => '改评成功数量'],
    ],

    // 薪资项阶梯值类型
    'salary_item_stage_rule_value_type'   => [
        ['id' => '1', 'name' => '工龄'],
    ],

    // 薪资项阶梯值类型
    'salary_item_stage_result_value_type' => [
        ['id' => '1', 'name' => '工龄'],
        ['id' => '11', 'name' => '实际出勤天数'],
        ['id' => '12', 'name' => '应出勤天数'],
        ['id' => '13', 'name' => '满勤天数'],
        ['id' => '21', 'name' => '基本工资'],
        ['id' => '22', 'name' => '综合工资'],
        ['id' => '3', 'name' => '自定义值'],
    ],

    'salary_item_stage_symbol' => [
        ['id' => '1', 'name' => '小于', 'value' => '<'],
        ['id' => '2', 'name' => '小于等于', 'value' => '<='],
        ['id' => '3', 'name' => '大于', 'value' => '>'],
        ['id' => '4', 'name' => '大于等于', 'value' => '>='],
        ['id' => '5', 'name' => '等于', 'value' => '='],
        ['id' => '6', 'name' => '在范围内', 'value' => 'between'],
    ],

    'salary_calculation_status' => [
        ['id' => '1', 'name' => '待核算'],
        ['id' => '2', 'name' => '核算中'],
        ['id' => '3', 'name' => '待审核'],
        ['id' => '4', 'name' => '待审批'],
        ['id' => '5', 'name' => '已完成'],
        ['id' => '6', 'name' => '已取消'],
        ['id' => '7', 'name' => '已作废'],
        ['id' => '8', 'name' => '核算失败'],
        ['id' => '9', 'name' => '审核未通过'],
        ['id' => '10', 'name' => '审批未通过'],
    ],

    'user_insurance_fund_tax_title' => [
        [
            ['id' => 'user_name', 'name' => '姓名'],
            ['id' => 'id_number', 'name' => '身份证号'],
            ['id' => 'corp_name', 'name' => '公司'],
            ['id' => 'user_department', 'name' => '部门'],
            ['id' => 'user_status', 'name' => '员工状态'],
            ['id' => 'hire_date', 'name' => '入职时间'],
            ['id' => 'leave_date', 'name' => '离职时间'],
            ['id' => 'month', 'name' => '缴纳周期'],
        ],
        [
            ['id' => 'user_social_insurance.status', 'name' => '社保缴纳状态'],
            ['id' => 'social_insurance_diff', 'name' => '社保差额'],
            ['id' => 'user_social_insurance.total', 'name' => '应收合计'],
            ['id' => 'user_social_insurance.total_personal', 'name' => '个人合计'],
            ['id' => 'user_social_insurance.total_corp', 'name' => '单位合计'],
            ['id' => 'user_social_insurance.endowment_insurance_base', 'name' => '养老缴纳基数'],
            ['id' => 'user_social_insurance.endowment_insurance_personal', 'name' => '养老个人缴纳'],
            ['id' => 'user_social_insurance.endowment_insurance_corp', 'name' => '养老单位缴纳'],
            ['id' => 'user_social_insurance.medical_insurance_base', 'name' => '医疗缴纳基数'],
            ['id' => 'user_social_insurance.medical_insurance_personal', 'name' => '医疗个人缴纳'],
            ['id' => 'user_social_insurance.medical_insurance_corp', 'name' => '医疗单位缴纳'],
            ['id' => 'user_social_insurance.employment_injury_insurance_base', 'name' => '工伤缴纳基数'],
            ['id' => 'user_social_insurance.employment_injury_insurance_personal', 'name' => '工伤个人缴纳'],
            ['id' => 'user_social_insurance.employment_injury_insurance_corp', 'name' => '工伤单位缴纳'],
            ['id' => 'user_social_insurance.unemployment_insurance_base', 'name' => '失业缴纳基数'],
            ['id' => 'user_social_insurance.unemployment_insurance_personal', 'name' => '失业个人缴纳'],
            ['id' => 'user_social_insurance.unemployment_insurance_corp', 'name' => '失业单位缴纳'],
            ['id' => 'user_social_insurance.maternity_insurance_base', 'name' => '生育缴纳基数'],
            ['id' => 'user_social_insurance.maternity_insurance_personal', 'name' => '生育个人缴纳'],
            ['id' => 'user_social_insurance.maternity_insurance_corp', 'name' => '生育单位缴纳'],
        ],
        [
            ['id' => 'user_housing_fund.status', 'name' => '公积金缴纳状态'],
            ['id' => 'user_housing_fund.base', 'name' => '存缴基数'],
            ['id' => 'user_housing_fund.corp_rate', 'name' => '单位存缴比例'],
            ['id' => 'user_housing_fund.personal_rate', 'name' => '个人存缴比例'],
            ['id' => 'user_housing_fund.total', 'name' => '金额合计'],
            ['id' => 'user_housing_fund.total_person', 'name' => '个人承担'],
            ['id' => 'housing_fund_diff', 'name' => '差额'],
        ],
        [
            ["id" => "user_tax.project", "name" => "所得项目"],
            ["id" => "user_tax.remark", "name" => "备注"],
            ["id" => "user_tax.cost", "name" => "本期费用"],
            ["id" => "user_tax.income", "name" => "本期收入"],
            ["id" => "user_tax.exempt_income", "name" => "本期免税收入"],
            ["id" => "user_tax.endowment_insurance", "name" => "本期基本养老保险费"],
            ["id" => "user_tax.medical_insurance", "name" => "本期基本医疗保险费"],
            ["id" => "user_tax.unemployment_insurance", "name" => "本期失业保险费"],
            ["id" => "user_tax.housing_fund", "name" => "本期住房公积金"],
            ["id" => "user_tax.enterprise_pension", "name" => "本期企业(职业)年金"],
            ["id" => "user_tax.commercial_health_insurance", "name" => "本期商业健康保险费"],
            ["id" => "user_tax.tax_deferred_endowment_insurance", "name" => "本期税延养老保险费"],
            ["id" => "user_tax.other_deduction", "name" => "本期其他扣除(其他)"],
            ["id" => "user_tax.total_income", "name" => "累计收入额"],
            ["id" => "user_tax.total_exempt_income", "name" => "累计免税收入"],
            ["id" => "user_tax.total_deduction", "name" => "累计减除费用"],
            ["id" => "user_tax.total_special_deduction", "name" => "累计专项扣除"],
            ["id" => "user_tax.child_education_deduction", "name" => "累计子女教育支出扣除"],
            ["id" => "user_tax.continuing_education_deduction", "name" => "累计继续教育支出扣除"],
            ["id" => "user_tax.housing_loan_interest_deduction", "name" => "累计住房贷款利息支出扣除"],
            ["id" => "user_tax.housing_rent_deduction", "name" => "累计住房租金支出扣除"],
            ["id" => "user_tax.supporting_elderly_deduction", "name" => "累计赡养老人支出扣除"],
            ["id" => "user_tax.infant_care_deduction", "name" => "累计3岁以下婴幼儿照护"],
            ["id" => "user_tax.personal_pension", "name" => "累计个人养老金"],
            ["id" => "user_tax.total_other_deduction", "name" => "累计其他扣除"],
            ["id" => "user_tax.total_donation_deduction", "name" => "累计准予扣除的捐赠"],
            ["id" => "user_tax.total_taxable_income", "name" => "累计应纳税所得额"],
            ["id" => "user_tax.tax_rate", "name" => "税率"],
            ["id" => "user_tax.quick_deduction", "name" => "速算扣除数"],
            ["id" => "user_tax.total_tax_payable", "name" => "累计应纳税额"],
            ["id" => "user_tax.total_tax_relief", "name" => "累计减免税额"],
            ["id" => "user_tax.total_tax_withheld", "name" => "累计应扣缴税额"],
            ["id" => "user_tax.total_pre_paid_tax", "name" => "累计已预缴税额"],
            ["id" => "user_tax.total_tax_refund", "name" => "累计应补(退)税额"],
            ["id" => "user_tax.special_deduction", "name" => "专项扣除"],
            ["id" => "user_tax.months", "name" => "月数"],
            ["id" => "user_tax.pre_paid_amount", "name" => "已预缴金额"],
        ]
    ],


);