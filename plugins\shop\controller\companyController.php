<?php

namespace plugins\shop\controller;

use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use Exception;
use plugins\shop\models\companyModel;
use plugins\shop\models\legalPersonModel;
use plugins\shop\models\relationModel;
use Rap2hpoutre\FastExcel\FastExcel;

class companyController extends baseController
{
    // 获取列表
    public function getList()
    {
        $param = array_intersect_key($_GET, array_flip([
            'house_number', 'tax_number', 'company_name', 'legal_person', 'register_city', 'register_address',
            'license_keeper', 'company_status', 'register_status', 'register_date',
            'register_type', 'receive_card_id', 'e_license',
            'page', 'page_size'
        ]));

        $model = new companyModel();
        $list = $model->getList($param);
        $maps = $model->getMaps();
        foreach ($list['list'] as &$item) {
            $item = $model->formatItem($item, $maps);
        }
        $ids = array_column($list['list'], 'id');
        $relation =  (new relationModel())->getReverseRelation('company', $ids);
        foreach ($list['list'] as &$item) {
            $item['relations'] = $relation[$item['id']] ?? [];
        }

        returnSuccess($list);
    }

    // 接收申请
    public static function accept()
    {
        try {
            if (empty($_POST['id'])) {
                returnError('ID不能为空');
            }

            $model = new companyModel();
            $model->accept($_POST['id']);
            returnSuccess([], '接收成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    // 注册登记
    public static function register()
    {
        try {
            if (empty($_POST['id'])) {
                returnError('ID不能为空');
            }

            $model = new companyModel();
            $param = array_intersect_key($_POST, array_flip(array_keys(companyModel::$paras_list)));

            $model->register($param, $_POST['id']);
            returnSuccess([], '注册登记成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    // 编辑
    public static function edit()
    {
        try {
            if (empty($_POST['id'])) {
                returnError('ID不能为空');
            }

            $model = new companyModel();
            $param = array_intersect_key($_POST, array_flip(array_keys(companyModel::$paras_list)));

            $model->edit($param, $_POST['id']);
            returnSuccess([], '编辑成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    // 申请注销
    public static function applyCancel()
    {
        try {
            if (empty($_POST['id'])) {
                returnError('ID不能为空');
            }

            if (empty($_POST['remark'])) {
                returnError('注销原因不能为空');
            }

            $model = new companyModel();
            $model->applyCancel($_POST['id'], $_POST['remark']);
            returnSuccess([], '申请注销成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    // 处理注销申请
    public static function handleCancel()
    {
        try {
            if (empty($_POST['id'])) {
                returnError('ID不能为空');
            }

            if (!isset($_POST['is_cancel'])) {
                returnError('是否注销不能为空');
            }

            if (empty($_POST['remark'])) {
                returnError('处理结果不能为空');
            }

            $model = new companyModel();
            $model->handleCancel(
                $_POST['id'],
                $_POST['is_cancel'],
                $_POST['remark']
            );
            returnSuccess([], '处理成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    // 变更法人
    public static function changeLegalPerson()
    {
        try {
            if (empty($_POST['id'])) {
                returnError('ID不能为空');
            }
            if (empty($_POST['legal_person_id'])) {
                returnError('法人ID不能为空');
            }

            $model = new companyModel();
            $model->changeLegalPerson($_POST['id'], $_POST['legal_person_id']);
            returnSuccess([], '变更成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    // 状态维护
    public static function updateStatus()
    {
        try {
            if (empty($_POST['id'])) {
                returnError('ID不能为空');
            }

            if (empty($_POST['company_status'])) {
                returnError('状态不能为空');
            }

            $model = new companyModel();
            $model->updateStatus(
                $_POST['id'],
                $_POST['company_status'],
                $_POST['status_change_time'] ?? date('Y-m-d'),
                $_POST['remark'] ?? ''
            );
            returnSuccess([], '状态维护成功');
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    // 详情
    public static function detail()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $model = new companyModel();
        $detail = $model->getById($id);
        if (!$detail) {
            returnError('数据不存在');
        }
        $detail = $model->formatItem($detail);
        $detail['relations'] = [];
        $relation =  (new relationModel())->getReverseRelation('company', [$id]);
        $relation = $relation[$detail['id']] ?? [];

        if (empty($relation)) returnSuccess($detail);

        $shop_ids = [];
        $trademark_ids = [];
        foreach ($relation as $item) {
            if ($item['from_table'] == 'shop') {
                $shop_ids[] = $item['from_id'];
            } elseif ($item['from_table'] == 'trademark') {
                $trademark_ids[] = $item['from_id'];
            }
        }

        $shop_db = dbShopMysql::getInstance();
        // 获取商标信息
        if (!empty($trademark_ids)) {
            $trademark = $shop_db->table('trademark')->field('id, dep_id, country')->whereIn('id', $trademark_ids)->list();
            $trademark = array_column($trademark, null, 'id');
        }
        // 获取店铺信息
        if (!empty($shop_ids)) {
            $shop = $shop_db->table('shop')->field('id, dep_id, shop_site')->whereIn('id', $shop_ids)->list();
            $shop = array_column($shop, null, 'id');
        }

        $deps = redisCached::getDepartment();
        $deps = array_column($deps, 'name', 'id');

        foreach ($relation as &$item) {
            if ($item['from_table'] == 'shop') {
                $item['country'] = $shop[$item['from_id']]['shop_site'] ?? '';
                $item['dep_id'] = $shop[$item['from_id']]['dep_id'] ?? '';
            } elseif ($item['from_table'] == 'trademark') {
                $item['country'] = $trademark[$item['from_id']]['country'] ?? '';
                $item['dep_id'] = $trademark[$item['from_id']]['dep_id'] ?? '';
            }
            $item['dep_name'] = $deps[$item['dep_id']] ?? '';
        }

        $detail['relations'] = $relation;

        returnSuccess($detail);
    }

    // 获取日志
    public static function getLog()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $db = dbShopMysql::getInstance();
        $list = $db->table('operation_log')->where('where table_name = :table_name and table_id = :table_id', 
        ['table_name' => 'company', 'table_id' => $id])->order('id desc')->list();

        $users =  redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        $model = new companyModel();
        $maps = $model::getMaps();

        foreach ($list as &$item) {
            $item['operator_name'] = $users[$item['operator']] ?? '';
            $attach = json_decode($item['attach'], true);
            $item['before'] = $attach['before'] ?? null;
            $item['before'] = $model->formatItem($item['before'], $maps);
            $item['after'] = $attach['after'] ?? null;
            $item['after'] = $model->formatItem($item['after'], $maps);
            unset($item['attach']);
        }
        returnSuccess($list);
    }

    /**
     * 批量导入
     */
    public function import()
    {
        $paras_list = array('excel_src');
        $request_list = ['excel_src' => '表格链接'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

        if (empty($param['excel_src'])) {
            returnError('表格链接不能为空');
        }

        $excel_url = SELF_FK . $param['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格文件不存在');
        }

        $data = (new FastExcel())->import($excel_url, function ($row) {
            // 将每一项 trim 去空格，再判断整行是否为空
            $cleaned = collect($row)->map(function ($value) {
                if (is_string($value)) {
                    return trim((string)$value);
                }
                return $value;
            });
            // 全部为空则跳过
            if ($cleaned->filter()->isEmpty()) {
                return null;
            }
            return $cleaned->toArray(); // 返回处理后的数据
        });
        $first = $data['0'];
        if (empty($first['公司名称']) || empty($first['公司法人']) || empty($first['法人身份证号']) || empty($first['公司状态']) ||
            empty($first['公司国家']) || empty($first['公司注册城市']) || empty($first['注册类型（法人自注册、公司注册）']) ||
            empty($first['纳税人识别号']) || empty($first['公司注册对接人']) || empty($first['公司注册日期']) || empty($first['注册公司地址']) ||
            !isset($first['房屋编码（选填）']) || empty($first['营业执照保管方']) || !isset($first['银行公户账号']) || empty($first['记账报税对接人']) ||
            !isset($first['备注']))
        {
            returnError('表头错误');
        }

        $receive_card = redisCached::getReceiveCard();
        $receive_card = array_column($receive_card, 'id', 'card_number');

        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_id', 'user_name');

        $legal_person = redisCached::getLegalPerson();
        $legal_person = array_column($legal_person, null, 'id_card');

        $model = new companyModel();
        $paras_list = $model::$paras_list;
        $import_data = [];
        $error_data = [];

        foreach ($data as $row) {
            $error_msg = [];
            empty($row['公司名称']) && $error_msg[] = '公司名称不能为空';
            empty($row['公司法人']) && $error_msg[] = '公司法人不能为空';
            empty($row['法人身份证号']) && $error_msg[] = '法人身份证号不能为空';
            $legal_person_id = null;
            if (!isset($legal_person[$row['法人身份证号']])) {
                $error_msg[] = '法人身份证号不存在';
            } else {
                $legal_person_id = $legal_person[$row['法人身份证号']]['id'];
                if ($legal_person[$row['法人身份证号']]['name'] !== $row['公司法人']) {
                    $error_msg[] = '法人姓名不一致';
                }
            }
            empty($row['公司状态']) && $error_msg[] = '公司状态不能为空';
            empty($row['公司国家']) && $error_msg[] = '公司国家不能为空';
            empty($row['公司注册城市']) && $error_msg[] = '公司注册城市不能为空';
            empty($row['注册类型（法人自注册、公司注册）']) && $error_msg[] = '注册类型不能为空';
            empty($row['纳税人识别号']) && $error_msg[] = '纳税人识别号不能为空';
            empty($row['公司注册对接人']) && $error_msg[] = '公司注册对接人不能为空';
            empty($row['公司注册日期']) && $error_msg[] = '公司注册日期不能为空';
            $register_date = null;
            try {
                $register_date = $row['公司注册日期']->format('Y-m-d');
                if (empty($row['公司注册日期']) || strtotime($register_date) === false) {
                    $error_msg[] = '公司注册日期格式错误';
                }
            } catch (\Throwable $e) {
                $error_msg[] = '公司注册日期格式错误';
            }
            empty($row['注册公司地址']) && $error_msg[] = '注册公司地址不能为空';
            empty($row['营业执照保管方']) && $error_msg[] = '营业执照保管方不能为空';
            $receive_card_id = null;
            if (!empty($row['银行公户账号'])) {
                if (!isset($receive_card[$row['银行公户账号']])) {
                    $error_msg[] = '银行公户账号不存在';
                } else {
                    $receive_card_id = $receive_card[$row['银行公户账号']];
                }
            }
            empty($row['记账报税对接人']) && $error_msg[] = '记账报税对接人不能为空';
            $accountants = [];
            if (!empty($row['记账报税对接人'])) {
                $u = explode(';', $row['记账报税对接人']);
                foreach ($u as $accountant) {
                    if (!isset($users[$accountant])) {
                        $error_msg[] = '记账报税对接人'.$accountant.'不存在';
                    } else {
                        $accountants[] = $users[$accountant];
                    }
                }
            }

            $item_data = [
                'company_name' => $row['公司名称'],
                'legal_person_id' => $legal_person_id,
                'company_status' => $row['公司状态'],
                'register_country' => $row['公司国家'],
                'register_city' => $row['公司注册城市'],
                'register_type' => $row['注册类型（法人自注册、公司注册）'],
                'tax_number' => $row['纳税人识别号'],
                'register_coordinator' => $row['公司注册对接人'],
                'register_date' => $register_date,
                'register_address' => $row['注册公司地址'],
                'house_number' => $row['房屋编码（选填）'] ?? '',
                'license_keeper' => $row['营业执照保管方'],
                'receive_card_id' => $receive_card_id,
                'accountants' => $accountants,
                'remark' => $row['备注'] ?? '',
                'register_status' => $model::REGISTER_STATUS_SUCCESS
            ];
            $check_row = $model->dataValidCheck($item_data, $paras_list, false);
            if (!empty($check_row['error'])) {
                $error_msg = array_merge($error_msg, $check_row['error']);
            }

            if (!empty($error_msg)) {
                $row['失败原因'] = implode(';', $error_msg);
                $error_data[] = $row;
                continue;
            }

            $import_data[] = $item_data;
            $id = $model->add($item_data, '导入');
        }

        if (count($error_data) > 0) {
            // 使用项目根目录动态构建导出文件路径
            $projectRoot = SELF_FK;
            $src = '/public/shop/temp/error/user_import_errors' . time() . rand(100, 1000) . '.xlsx';
            $exportPath = $projectRoot . $src;

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径

            returnSuccess(['error_file' => $src, 'error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $error_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $import_data], '导入成功');
        }

    }

    /**
     * 导出
     */
    public function export()
    {
        $param = array_intersect_key($_POST, array_flip([
            'house_number', 'tax_number', 'company_name', 'legal_person', 'register_city', 'register_address',
            'license_keeper', 'company_status', 'register_status', 'register_date',
            'register_type', 'receive_card_id', 'e_license', 'keys', 'ids'
        ]));

        $model = new companyModel();
        $data = $model->getList($param, 'id desc', true);

        if (empty($data)) {
            returnError('没有可导出的数据');
        }

        $export_data = [];
        foreach ($data as $item) {
            $keys = [
                '公司名称', '公司状态', '公司国家', '注册城市', '注册类型', '纳税人识别号',
                '注册对接人', '注册日期', '注册地址', '房屋编码', '营业执照保管方',
                '开立银行公户', '记账报税人'
            ];
            $sortedItem = [];
            foreach ($keys as $key) {
                if (!empty($param['keys']) && !in_array($key, $param['keys'])) continue;
                if ($key == '记账报税人') {
                    if (!empty($item[$key])) {
                        $sortedItem[$key] = implode(';', array_column($item[$key], 'user_name'));
                    } else {
                        $sortedItem[$key] = '';
                    }
                } else {
                    $sortedItem[$key] = $item[$key] ?? '';
                }
            }
            $export_data[] = $sortedItem;
        }

        $filePath = '/public/shop/temp/receive_account_export_' . date('YmdHis') . '.xlsx';
        (new FastExcel($export_data))->export(SELF_FK.$filePath);

        // 导出数据
        returnSuccess(['src' => $filePath], '导出成功');

    }

    public function editBatch()
    {
        $paras_list = array('data');
        $request_list = ['data' => '编辑数据'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

        if (count($param['data']) > 500) {
            returnError('批量编辑数据不能超过500条');
        }

        $model = new companyModel();
        $result = $model->editBatch($param['data']);

        returnSuccess($result);

    }
}
