<?php

namespace plugins\goods\form;

use core\lib\db\dbMysql;
use plugins\goods\models\userModel;

class imageOwnerConfigFrom
{
    //拉取图片负责人列表
    public static function getImageOwnerList($param)
    {
        $db = dbMysql::getInstance();

        // 构建查询
        $db->table('imgs_request_distributor', 'd')
            ->leftJoin('qwuser', 'u1', 'u1.id = d.user_id')
            ->leftJoin('qwuser', 'u2', 'u2.id = d.update_user_id')
            ->field('d.id,d.user_id,d.distributor_id,d.country_code,d.type,d.group_name,d.created_time,d.updated_time,d.category_id,u1.wname as creator_name,u2.wname as update_name,d.update_user_id');

        // 站点（国家code）（支持多选）
        if (!empty($param['country_code']) && $param['country_code'] != '[]') {
            $db->andWhere('JSON_OVERLAPS(d.country_code, :country_code_json)', [
                'country_code_json' => $param['country_code']
            ]);
        }

        // 需求性质（支持多选）
        if (isset($param['type']) && $param['type'] != '[]') {
            $db->andWhere('JSON_OVERLAPS(d.type, :type_json)', [
                'type_json' => $param['type']
            ]);
        }


        // 类目（支持多选）
        if (!empty($param['category_id']) && $param['category_id'] != '[]') {
            $db->andWhere('JSON_OVERLAPS(d.category_id, :category_id_json)', [
                'category_id_json' => $param['category_id']
            ]);
        }

        // 负责人
        if (!empty($param['distributor_id']) && $param['distributor_id'] != '[]') {
            $db->andWhere('JSON_OVERLAPS(d.distributor_id, :user_json)', [
                'user_json' => $param['distributor_id']
            ]);
        }

        // 未删除记录
        $db->andWhere('d.is_delete = 0');

        // 排序
        $db->order('d.id DESC');

        // 分页
        $result = $db->pages($param['page'], $param['page_size']);

        // 返回结果（若需后处理可取消注释）
        $result = self::getRelatedData($db,$result);

        returnSuccess($result);
    }
    // 处理相关数据
    public static function getRelatedData($db, &$result)
    {
        // 1. 取出列表
        $list = $result['list'] ?? [];

        // 2. 批量解码 JSON 字符串并合并去重
        // —— 类目
        $categoryJsons = array_column($list, 'category_id');
        $categoryArrays = $categoryJsons
            ? array_map(fn($s) => json_decode($s, true) ?: [], $categoryJsons)
            : [];
        $categoryIds = $categoryArrays
            ? array_unique(call_user_func_array('array_merge', $categoryArrays))
            : [];

        // —— 国家
        $countryJsons = array_column($list, 'country_code');
        $countryArrays = $countryJsons
            ? array_map(fn($s) => json_decode($s, true) ?: [], $countryJsons)
            : [];
        $countryCodes = $countryArrays
            ? array_unique(call_user_func_array('array_merge', $countryArrays))
            : [];

        // —— wq_id
        $wqJsons = array_column($list, 'wq_id');
        $wqArrays = $wqJsons
            ? array_map(fn($s) => json_decode($s, true) ?: [], $wqJsons)
            : [];
        $wqIds = $wqArrays
            ? array_unique(call_user_func_array('array_merge', $wqArrays))
            : [];
        // —— type
        $typeJsons = array_column($list, 'type');
        $typeArrays = $typeJsons
            ? array_map(fn($s) => json_decode($s, true) ?: [], $typeJsons)
            : [];
        $typeValues = $typeArrays
            ? array_unique(call_user_func_array('array_merge', $typeArrays))
            : [];

        // —— distributor_id
        $distributorJsons = array_column($list, 'distributor_id');
        $distributorArrays = $distributorJsons
            ? array_map(fn($s) => is_string($s) ? json_decode($s, true) ?: [] : (is_numeric($s) ? [(int)$s] : []), $distributorJsons)
            : [];
        $distributorIds = $distributorArrays
            ? array_unique(call_user_func_array('array_merge', $distributorArrays))
            : [];
//        dd($distributorIds);


        // 批量查询，创建映射
        // 类目
        $categoryMap = [];
        if ($categoryIds) {
            $categoryMap = $db->table('goods_cate')
                ->field('id,cate_name')
                ->whereIn('id', $categoryIds)
                ->list();
            $categoryMap = array_column($categoryMap, 'cate_name', 'id');
        }
        // 国家
        $countryMap = [];
        if ($countryCodes) {
            $countryMap = $db->table('market')
                ->field('code,country')
                ->whereIn('code', $countryCodes)
                ->list();
            $countryMap = array_column($countryMap, 'country', 'code');
        }
        $NONE = ['NONE'=>"无"];
        //合并数组
        $countryMap = array_merge($countryMap,$NONE);
        // 部门
        $wqMap = [];
        if ($wqIds) {
            $wqMap = $db->table('qwdepartment')
                ->field('wp_id,name')
                ->whereIn('wp_id', $wqIds)
                ->list();
            $wqMap = array_column($wqMap, 'name', 'wp_id');
        }

        // type
        $typeMap = [];
        if ($typeValues) {
            // 假设 type 数据来自 imgs_request_nature 表
            $typeMap = [
                ['id' => '1', 'name' => '新图制作'],
                ['id' => '2', 'name' => '图片优化'],
                ['id' => '3', 'name' => '套版任务'],
                ['id' => '4', 'name' => '其他'],
            ];
            $typeMap = array_column($typeMap, 'name', 'id');
        }
        //审核人id
        $distributorMap = [];
        if ($distributorIds) {
            $distributorMap = $db->table('qwuser')
                ->field('id,wname')
                ->whereIn('id', $distributorIds)
                ->list();
            $distributorMap = array_column($distributorMap, 'wname', 'id');
        }

        // 处理时间格式和多选字段
        foreach ($result['list'] as &$item) {
            // 处理时间格式
            if (!empty($item['created_time'])) {
                $item['created_time'] = date('Y-m-d H:i:s', strtotime($item['created_time']));
            }
            if (!empty($item['updated_time'])) {
                $item['updated_time'] = date('Y-m-d H:i:s', strtotime($item['updated_time']));
            }

            // 处理多选字段的映射
            // 类目
            if (!empty($item['category_id'])) {
                $categoryIds = json_decode($item['category_id'], true);
                if (is_array($categoryIds)) {
                    $item['category_name'] = array_map(fn($id) => $categoryMap[$id] ?? $id, $categoryIds);
                } else {
                    $item['category_name'] = $categoryMap[$categoryIds] ?? $categoryIds;
                }
            }

            // 国家
            if (!empty($item['country_code'])) {
                $countryCodes = json_decode($item['country_code'], true);
                if (is_array($countryCodes)) {
                    $item['country_name'] = array_map(fn($code) => $countryMap[$code] ?? $code, $countryCodes);
                } else {
                    $item['country_name'] = $countryMap[$countryCodes] ?? $countryCodes;
                }
            }

            // 部门
            if (!empty($item['wq_id'])) {
                $wqIds = json_decode($item['wq_id'], true);
                if (is_array($wqIds)) {
                    $item['wq_name'] = array_map(fn($id) => $wqMap[$id] ?? $id, $wqIds);
                } else {
                    $item['wq_name'] = $wqMap[$wqIds] ?? $wqIds;
                }
            }
            // 需求类型
            if (!empty($item['type'])) {
                $typeIds = json_decode($item['type'], true);
                if (is_array($typeIds)) {
                    $item['type_name'] = array_map(fn($id) => $typeMap[$id] ?? $id, $typeIds);
                } else {
                    $item['type_name'] = $typeMap[$typeIds] ?? $typeIds;
                }
            }
            //审核人
            if (!empty($item['distributor_id'])) {
                $distributorIds = json_decode($item['distributor_id'], true);
                if (is_array($distributorIds)) {
                    $item['distributor_name'] = array_map(fn($id) => $distributorMap[$id] ?? $id, $distributorIds);
                } else {
                    $item['distributor_name'] = $distributorMap[$distributorIds] ?? $distributorIds;
                }
            }
        }

        // 返回处理后的结果
        return $result;
    }

    /**
     * 删除图片负责人配置
     * @param int $id 配置ID
     * @return array 操作结果
     */
    public static function deleteImageOwner($id)
    {
        if (empty($id)) {
            returnError('参数错误，缺少ID');
        }
        
        $db = dbMysql::getInstance();
        
        try {
            // 开启事务
            $db->beginTransaction();
            
            // 查询配置是否存在
            $config = $db->table('imgs_request_distributor')
                ->where('id = :id AND is_delete = 0', ['id' => $id])
                ->one();
                
            if (!$config) {
                returnError('配置不存在或已删除');
            }
            
            // 软删除（更新is_delete字段）
            $result = $db->table('imgs_request_distributor')
                ->where('id = :id', ['id' => $id])
                ->update(['is_delete' => 1, 'updated_time' => date('Y-m-d H:i:s')]);
//            // 更新相关图片需求记录
//            $requests = $db->table('imgs_request')
//                ->where('request_distributor_id = :id', ['id' => $id])
//                ->list();
//            if (!empty($requests)) {
//                self::updateRequestAndParticipant($db, $requests);
//            }
//            foreach ($requests as $request) {
//                // 根据新的配置重新匹配审核人
//                $checkerInfo = self::matchdistributorConfig($db, $request['country_code'], $request['category_id'], $request['type']);
//
//                // 更新图片需求的审核人配置ID
//                $db->table('imgs_request')
//                    ->where('id = :id and (status = 0 or status = 1)', ['id' => $request['id']])
//                    ->update([
//                        'request_distributor_id' => $checkerInfo[1]
//                    ]);
//            }
            // 提交事务
            $db->commit();
            
            returnSuccess('删除成功');
        } catch (Exception $e) {
            // 回滚事务
            $db->rollBack();
            returnError('删除失败：' . $e->getMessage());
        }
    }
    /**
     * 新增或编辑图片负责人配置
     * @param array $param 参数
     * @return array 操作结果
     */
    public static function saveImageOwner($param)
    {
        $db = dbMysql::getInstance();
        $param['user_id'] = userModel::$qwuser_id;

        // 构建数据（支持多选字段的 JSON 存储）
        $data = [];
        $multiFields = ['country_code', 'type', 'category_id'];
        foreach ($multiFields as $field) {
            if (isset($param[$field])) {
                $data[$field] = is_array($param[$field]) ? json_encode($param[$field], JSON_UNESCAPED_UNICODE) : $param[$field];
            }
        }

        // 美工组长
        if (isset($param['distributor_id'])) {
            $data['distributor_id'] = $param['distributor_id'];
        }

        // 组名及唯一性校验
        if (!empty($param['group_name'])) {
            $data['group_name'] = $param['group_name'];

            $db->table('imgs_request_distributor')
                ->where('group_name = :group_name AND is_delete = 0', ['group_name' => $param['group_name']]);
            if (!empty($param['id'])) {
                $db->andWhere('id != :id', ['id' => (int)$param['id']]);
            }
            if ($db->one()) {
                returnError('组名已存在');
            }
        }

        if (empty($data)) {
            returnError('没有需要保存的数据');
        }

        // 检查相同需求性质和站点下的类目配置是否重复
        if (!empty($data['country_code']) && !empty($data['type']) && !empty($data['category_id'])) {
            $query = $db->table('imgs_request_distributor')
                ->where('is_delete = 0')
                ->andWhere('JSON_OVERLAPS(country_code, :country_code)', [
                    'country_code' => $data['country_code']
                ])
                ->andWhere('JSON_OVERLAPS(type, :type)', [
                    'type' => $data['type']
                ]);

            // 如果是编辑操作，排除当前记录
            if (!empty($param['id'])) {
                $query->andWhere('id != :id', ['id' => $param['id']]);
            }

            $existingConfig = $query->one();

            if ($existingConfig) {
                // 如果数据库中存在该配置
                if ($existingConfig['category_id'] === '[]') {
                    // 如果数据库中的配置是全选类目，则不允许新增
                    returnError('该需求性质和站点下已存在全选类目的配置');
                } else if ($data['category_id'] !== '[]') {
                    // 如果当前配置不是全选类目，检查类目是否有重叠
                    $overlapConfig = $query->andWhere('JSON_OVERLAPS(category_id, :category_id)', [
                        'category_id' => $data['category_id']
                    ])->one();

                    if ($overlapConfig) {
                        returnError('该需求性质和站点下已存在相同的类目配置');
                    }
                }
            }
        }
        $now = date('Y-m-d H:i:s');
        $data['updated_time'] = $now;

        try {
            $db->beginTransaction();

            if (empty($param['id'])) {
                // 新增
                $data['created_time'] = $now;
                $data['update_user_id'] = userModel::$qwuser_id;

                $data['user_id'] = (int)(userModel::$qwuser_id ?? 0);
                $id = $db->table('imgs_request_distributor')->insert($data);
                $message = '新增成功';
                //根据站点和类目去主表查询匹配是否有对应的记录,如果有的话去修改对应的request_checke_id
//                dd($id);
                $info = $db->table('imgs_request')
                    ->where('type =:type AND country_code =:country_code AND JSON_OVERLAPS(category_id, :category_id)', [
                        'type'=>$data['type'],
                        'country_code' => $data['country_code'],
                        'category_id' => $data['category_id']
                    ])->one();
                if (!empty($info)){
                    $db->table('imgs_request')
                        ->where('type =:type AND country_code =:country_code AND JSON_OVERLAPS(category_id, :category_id)', [
                            'type'=>$data['type'],
                            'country_code' => $data['country_code'],
                            'category_id' => $data['category_id']
                        ])->update(['request_distributor_id'=>$id]);
                }
            } else {
                // 编辑
                $id = (int)$param['id'];
                $data['update_user_id'] = userModel::$qwuser_id;

                $exist = $db->table('imgs_request_distributor')
                    ->where('id = :id AND is_delete = 0', ['id' => $id])
                    ->one();
                if (!$exist) {
                    $db->rollBack();
                    returnError('配置不存在或已删除');
                }

                $db->table('imgs_request_distributor')
                    ->where('id = :id', ['id' => $id])
                    ->update($data);
//                // 更新相关图片需求记录
//                $requests = $db->table('imgs_request')
//                    ->where('request_distributor_id = :id', ['id' => $id])
//                    ->list();
//                if (!empty($requests)) {
//                    self::updateRequestAndParticipant($db, $requests);
//                }
//                foreach ($requests as $request) {
//                    // 根据新的配置重新匹配审核人
//                    $checkerInfo = self::matchdistributorConfig($db, $request['country_code'], $request['category_id'], $request['type']);
//
//                    // 更新图片需求的审核人配置ID
//                    $db->table('imgs_request')
//                        ->where('id = :id and (status = 0 or status = 1)', ['id' => $request['id']])
//                        ->update([
//                            'request_distributor_id' => $checkerInfo[1]
//                        ]);
//                }

                $message = '更新成功';
            }

            $db->commit();
            returnSuccess('', $message);
        } catch (\Exception $e) {
            $db->rollBack();
            returnError('操作失败：' . $e->getMessage());
        }
    }
    /**
     * 统一封装为 JSON 数组字符串（["xxx"] 形式）
     */
    private static function toJsonArray($input): string
    {
        if (is_array($input)) {
            return json_encode($input, JSON_UNESCAPED_UNICODE);
        }
        if (is_string($input) && str_starts_with($input, '[')) {
            return $input;
        }
        return json_encode([$input], JSON_UNESCAPED_UNICODE);
    }
    /**
     * 根据国家和类目匹配负责人配置
     * @param dbMysql $db 数据库实例
     * @param string|array $country_code 国家编码
     * @param string|array $category_id 类目ID
     * @return array [check_id数组, 配置ID]
     */
    private static function matchdistributorConfig($db, $country_code, $category_id, $type): array
    {
        $category_id = self::toJsonArray($category_id);
        $country_code = self::toJsonArray($country_code);
        $type = self::toJsonArray($type);

        // 先根据国家查找通用配置（不区分类目）
        $row = $db->table('imgs_request_distributor')
            ->where(
                'country_code = :cc AND type = :type AND is_delete = 0',
                [
                    'cc'  => $country_code,
                    'type'=> $type
                ]
            )
            ->one();
        if (empty($row)) {
            // 查不到配置，从 config 表读取默认配置
            $result = $db->table('config')->where('key_name = "imgs_request_distributor"')->one();
            $row['distributor_id'] = $result['data'] ?? '[]';
        }

        // 如果 category_id 为 []，表示不限制类目，直接返回
        if (($row['category_id'] ?? '') === '[]') {
            $ids = json_decode($row['distributor_id'], true);
            return [$ids ?? [], $row['id'] ?? 0];
        }

        // 否则继续根据国家 + 类目查找更精确的配置
        $row = $db->table('imgs_request_distributor')
            ->where(
                'country_code = :cc AND type = :type AND category_id = :cid AND is_delete = 0',
                [
                    'cc'  => $country_code,
                    'type'=> $type,
                    'cid' => $category_id
                ]
            )
            ->one();

        if (empty($row)) {
            // 仍然查不到，从 config 表读取默认配置
            $result = $db->table('config')->where('key_name = "imgs_request_distributor"')->one();
            $row['distributor_id'] = $result['data'] ?? '[]';
        }

        $ids = json_decode($row['distributor_id'], true);
        return [$ids ?? [], $row['id'] ?? 0];
    }

    //终线设置
    public static function getFinalLine($param)
    {
        $db = dbMysql::getInstance();
        $db->beginTransaction();
        
        try {
            // 更新配置表
            $db->table('config')
                ->where('key_name = "imgs_request_distributor"')
                ->update([
                    'data' => $param['user_ids'],
                    'updated_time' => date('Y-m-d H:i:s'),
                    'user_id' => userModel::$qwuser_id
                ]);
            
//            // 获取所有需要更新的请求
//            $requests = $db->table('imgs_request')
//                ->where('request_distributor_id = 0')
//                ->list();
//
//            if (!empty($requests)) {
//                self::updateRequestAndParticipant($db, $requests);
//            }

            $db->commit();
            returnSuccess('', '配置成功');
        } catch (\Exception $e) {
            $db->rollBack();
            returnError('配置失败：' . $e->getMessage());
        }
    }

    /**
     * 更新请求和参与者记录
     * @param dbMysql $db 数据库实例
     * @param array $requests 需要更新的请求列表
     */
    private static function updateRequestAndParticipant($db, $requests)
    {
        // 收集所有需要更新的数据
        $updateData = [];
        $participantData = [];
        
        foreach ($requests as $request) {
            // 根据新的配置重新匹配审核人
            $checkerInfo = self::matchdistributorConfig($db, $request['country_code'], $request['category_id'], $request['type']);
            
            // 收集更新数据
            $updateData[] = [
                'id' => $request['id'],
                'request_distributor_id' => $checkerInfo[1],
                'allocation_user_id' => $checkerInfo[0][0]
            ];
            
            // 收集参与者数据
            $participantData[] = [
                'request_id' => $request['id'],
                'type' => 3,
                'user_id' => $checkerInfo[0][0]
            ];
        }

        // 批量更新imgs_request表
        if (!empty($updateData)) {
            foreach ($updateData as $data) {
                $db->table('imgs_request')
                    ->where('id = :id and (status = 0 or status = 1)', ['id' => $data['id']])
                    ->update([
                        'request_distributor_id' => $data['request_distributor_id'],
                        'allocation_user_id' => $data['allocation_user_id']
                    ]);
            }
        }

        // 批量处理参与者记录
        if (!empty($participantData)) {
            // 先删除所有相关记录
            $requestIds = array_column($participantData, 'request_id');
            $db->table('imgs_request_participant')
                ->whereIn('request_id', $requestIds)
                ->where('type = 3')
                ->delete();

            // 批量插入新记录
            foreach ($participantData as $data) {
                $db->table('imgs_request_participant')
                    ->insertIgnore([
                        'request_id' => $data['request_id'],
                        'user_id' => $data['user_id'],
                        'type' => $data['type']
                    ]);
            }
        }
    }

    //获取终线设置信息
    public static function getFinalLineCurrent()
    {
        $db = dbMysql::getInstance();
        $result = $db->table('config')->where('key_name = "imgs_request_distributor"')->one();
        returnSuccess($result,'' );
    }


}