<?php

/**
 * @author: zhangguoming
 * @Time: 2024/3/27 9:02
 */

namespace plugins\checkin\models;

use core\lib\db\dbCMysql;
class userRolesModel
{
    //根据用户id获取用户角色权限
    public static function getAuthByQuserId($user_id) {
        $cdb = dbCMysql::getInstance();
        $cdb->table('user_roles','a');
        $cdb->where('where user_id = :user_id and a.is_delete = 0 and a.status = 1',['user_id'=>$user_id]);
        $cdb->leftJoin('roles','b','b.id = a.role_id');
        $cdb->field('a.role_id,b.role_name,b.auth,b.type,b.list_auth');
        $list = $cdb->list();
        $data = [
            'auth'=>'[]',
            'list_auth'=>[],
            'role_name'=>'',
            'role_type'=>[],
        ];
        if (count($list)) {
            $role_name = [];
            $auth = [];
            $role_type=[];
            $list_auth = [];
            foreach ($list as $v) {
                if ($v['auth'] == 'null') {
                    $v['auth'] = [];
                } else {
                    $v['auth'] = json_decode($v['auth'],true);

                }
                if ($v['list_auth'] == 'null') {
                    $v['list_auth'] = [];
                } else {
                    $v['list_auth'] = json_decode($v['list_auth'],true);
                }
                $role_name[] = $v['role_name'];
                $auth = array_merge($auth,$v['auth']);
                $list_auth = array_merge($list_auth,$v['list_auth']);
                if (!in_array($v['type'],$role_type)) {
                    $role_type[] = $v['type'];
                }
            }
            $data['role_name'] = implode(',',$role_name);
            $auth = array_unique($auth);
            $auth = array_values($auth);
            $data['auth'] = json_encode($auth);
            $data['list_auth'] = json_encode($list_auth);
            $data['role_type'] = $role_type;
        }
        return $data;
    }

    // 获取所有角色，及角色下的用户
    public static function getRolesAndUsers(){
        $cdb = dbCMysql::getInstance();
        $cdb->table('roles','a');
        $cdb->where('where a.is_delete = 0');
        $cdb->field('a.id,a.role_name,a.type,a.auth,a.list_auth');
        $roles = $cdb->list();
        $roles = array_column($roles,null,'id');

        $cdb->table('user_roles');
        $cdb->field('role_id,user_id');
        $cdb->where('where is_delete = 0 and status = 1');
        $user_roles = $cdb->list();

        foreach ($user_roles as $user_role) {
            $roles[$user_role['role_id']]['users'][] = $user_role['user_id'];
        }
        return array_values($roles);
    }


}