<?php

namespace plugins\shop\models;

use admin\models\qwdepartmentModel;
use core\lib\redisCached;
use Exception;
use plugins\shop\form\messagesFrom;

class legalPersonModel extends baseModel
{
    public string $table = 'legal_person';

    // 参数列表
    public static array $paras_list = [
        'name'                      => '法人',
        'phone'                     => '法人手机号',
        'id_card'                   => '身份证号',
        'gender'                    => '性别',
        'birth_date'                => '出生日期',
        'id_card_address'           => '身份证地址',
        'id_card_expire'            => '身份证有效期',
        'city'                      => '常住城市',
        'work_status'               => '法人工作情况',
        'education'                 => '学历',
        'education_cert'            => '学历证明材料',
        'recommend_channel'         => '推荐渠道',
        'recommender'               => '推荐人',
        'relation_with_recommender' => '关系说明',
        'register_date'             => '报名日期',
        'internal_coordinator'      => '内部对接人ID',
        'cooperation_level'         => '法人配合度',
        'credit_card_status'        => '信用卡情况',
        'credit_card_id'            => '信用卡ID',
        'amazon_available'          => '手机可用于注册亚马逊',
        'user_tags'                 => '用户标签',
        'agreement'                 => '协议信息',
        'legal_fee'                 => '法人费用',
        'recommend_fee'             => '推荐费',
        'remark'                    => '备注',
        'available_materials'       => '可提供资料',
        'is_register_company'       => '是否注册公司',
    ];

    public static array $json_keys = [
        'id_card_expire',
        'education_cert',
        'user_tags',
        'agreement',
        'legal_fee',
        'recommend_fee',
        'available_materials',
    ];

    public function getTimeAuthStr($type)
    {
        $auth_detail = userModel::$auth_detail;
        if (!isset($auth_detail['legal_person'])) {
            return [];
        }
        $str = [];
        $arr = [];
        $idx = 0;
        foreach ($auth_detail['legal_person'] as $auth_item) {
            if ($auth_item['auth'] != $type) continue;
            if (isset($auth_item['detail']) && !empty($auth_item['detail'])) {
                if ($auth_item['detail']['1'] == 'INF') {
                    $str[] = 'lp.created_at >= :auth_s_date'.$idx;
                    $arr['auth_s_date'.$idx] = $auth_item['detail']['0'].' 00:00:00';
                } else {
                    $str[] = 'lp.created_at >= :auth_s_date'.$idx.' and lp.created_at <= :auth_e_date'.$idx;
                    $arr['auth_s_date'.$idx] = $auth_item['detail']['0']. ' 00:00:00';
                    $arr['auth_e_date'.$idx] = $auth_item['detail']['1']. ' 23:59:59';
                }
            }
            $idx++;
        }
        if (empty($str)) {
            return [];
        }
        $str = implode(' or ', $str);
        return [
            'str' => '(' . $str . ')',
            'arr' => $arr,
        ];
    }

    // 获取列表
    public function getList($param, $order = 'id desc', $is_export = false)
    {

        // 权限控制
        $user_id = userModel::$qwuser_id;
        $all_department_users = qwdepartmentModel::getUserDepartment(userModel::$wid, false)['all_department_users'] ?: []; // 当前用户管理的部门及其子部门下的用户
        $user_ids = array_column($all_department_users, 'id');

        $this->db->table($this->table, 'lp')
            ->leftJoinOut('db', 'qwuser', 'u', 'u.id = lp.internal_coordinator')
            ->leftJoin('relations', 'r', "r.to_table = 'legal_person' and r.to_id = lp.id")
            ->leftJoin('company', 'c', "r.from_table = 'company' and r.from_id = c.id")
            ->field('lp.*, r.created_at as bind_time, c.id as company_id, c.company_name as company_name, c.register_status as company_register_status')
            ->where('where lp.is_delete = 0');


        if (userModel::getUserListAuth('legalPersonAll')) {
            $str = $this->getTimeAuthStr('legalPersonAll');
            if (!empty($str) && !empty($str['str']) && !empty($str['arr'])) {
                $this->db->andWhere($str['str'], $str['arr']);
            }
            // 全部权限
        } elseif (userModel::getUserListAuth('legalPersonDepartment')) {
            if (!empty($all_department_users)) {
                $this->db->whereIn('lp.user_id', $user_ids);
            }
            $str = $this->getTimeAuthStr('legalPersonDepartment');
            if (!empty($str) && !empty($str['str']) && !empty($str['arr'])) {
                $this->db->andWhere($str['str'], $str['arr']);
            }
        } elseif (userModel::getUserListAuth('legalPersonRelated')) {
            $this->db->andWhere('lp.user_id = :user_id', ['user_id' => $user_id]);
            $str = $this->getTimeAuthStr('legalPersonRelated');
            if (!empty($str) && !empty($str['str']) && !empty($str['arr'])) {
                $this->db->andWhere($str['str'], $str['arr']);
            }
        }
        // 固定筛选
        if (!empty($param['ids'])) {
            $this->db->whereIn('lp.id', $param['ids']);
        }

        // 姓名
        if (!empty($param['name'])) {
            $this->db->andWhere('name like :name', ['name' => '%'. $param['name'] .'%']);
        }
        // 推荐人
        if (!empty($param['recommender'])) {
            $this->db->andWhere('recommender like :recommender', ['recommender' => '%'. $param['recommender'] .'%']);
        }
        // 身份证号码
        if (!empty($param['id_card'])) {
            $this->db->andWhere('id_card = :id_card', ['id_card' => $param['id_card']]);
        }
        // 身份证地址
        if (!empty($param['id_card_address'])) {
            $this->db->andWhere('id_card_address like :id_card_address', ['id_card_address' => '%'. $param['id_card_address'] .'%']);
        }
        // 手机号
        if (!empty($param['phone'])) {
            $this->db->andWhere('phone = :phone', ['phone' => $param['phone']]);
        }
        // 内部对接人
        if (!empty($param['internal_coordinator'])) {
            $this->db->andWhere('u.wname like :internal_coordinator', ['internal_coordinator' => '%'. $param['internal_coordinator'] .'%']);
        }
        // 学历
        if (!empty($param['education'])) {
            $this->db->andWhere('education = :education', ['education' => $param['education']]);
        }
        // 工作状态
        if (!empty($param['work_status'])) {
            $this->db->andWhere('work_status = :work_status', ['work_status' => $param['work_status']]);
        }
        // 性别
        if (!empty($param['gender'])) {
            $this->db->andWhere('gender = :gender', ['gender' => $param['gender']]);
        }
        // 是否注册亚马逊
        if (!empty($param['amazon_available'])) {
            $this->db->andWhere('amazon_available = :amazon_available', ['amazon_available' => $param['amazon_available']]);
        }
        if (isset($param['register_date']) && !empty($param['register_date']) && is_array($param['register_date'])) {
            $this->db->andWhere('lp.register_date >= :start and s.register_date <= :end', [
                'start' => $param['register_date'][0],
                'end' => $param['register_date'][1]
            ]);
        }
        // 身份证有效期
        if (!empty($param['id_card_expire'])) {
            $this->db->andWhere('JSON_EXTRACT(lp.id_card_expire, "$[1]") >= :start_time
                                and JSON_EXTRACT(lp.id_card_expire, "$[0]") <= :end_time ',
                [
                    'start_time' => $param['id_card_expire'][0],
                    'end_time' => $param['id_card_expire'][1]
                ]);
        }
        if (!empty($param['update_at'])) {
            $this->db->andWhere('lp.updated_at >= :update_time_start and lp.updated_at <= :update_time_end', [
                'update_time_start' => $param['update_at'][0],
                'update_time_end'   => $param['update_at'][1]. ' 23:59:59',
            ]);
        }


        $this->db->order($order);

        if (isset($param['page']) && isset($param['page_size'])) {
            $param['page'] = $param['page'] ?: 1;
            $param['page_size'] = $param['page_size'] ?: 10;
            $list = $this->db->pages($param['page'], $param['page_size']);
            $maps = self::getMaps();
            foreach ($list['list'] as &$item) {
                $item = $this->formatItem($item, $maps);
            }
            return $list;
        } else {
            $list = $this->db->list();
            if ($is_export) {
                $export_data = [];
                $maps = self::getMaps();
                $paras_list = static::$paras_list;
                $paras_list['internal_coordinator_name'] = '内部对接人';
                $paras_list['credit_card_number'] = '法人信用卡';
                $paras_list['credit_card_validity_period'] = '信用卡有效期';
                foreach ($list as $item) {
                    $item = $this->formatItem($item, $maps);
                    // 使用参数对应的中文作为键名返回
                    $export_data[] = self::changeToCnKey($item, $paras_list);
                }

                return $export_data;
            }
            return $list;
        }
    }

    public static function getMaps()
    {

        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        $credit_card = redisCached::getCreditCard();

        return [
            'users' => $users,
            'credit_card' => $credit_card
        ];
    }

    // 格式化单条记录
    public function formatItem($item, $maps = [])
    {
        $users = $maps['users'] ?? [];
        if (empty($maps['users'])) {
            $users = redisCached::getUserInfo();
            $users = array_column($users, 'user_name', 'user_id');
        }
        $credit_card = $maps['credit_card'] ?? [];
        if (empty($maps['credit_card'])) {
            $credit_card = redisCached::getCreditCard();
        }
        $maps = [
            ['name' => 'operator_name', 'maps' => $users, 'key' => 'operator'],
            ['name' => 'internal_coordinator_name', 'maps' => $users, 'key' => 'internal_coordinator'],
            ['name' => 'credit_card_number', 'maps' => array_column($credit_card, 'card_number', 'id'), 'key' => 'credit_card_id'],
            ['name' => 'credit_card_validity_period', 'maps' => array_column($credit_card, 'validity_period', 'id'), 'key' => 'credit_card_id'],
        ];
        $item =  parent::formatItem($item, $maps);

        // 过滤字段

        // 协议信息(含列表字段权限)
        if (!userModel::getUserListAuth('legalPersonAgreement')) {
            unset($item['agreement']);
        }
        // 法人费用(含列表字段权限)
        if (!userModel::getUserListAuth('legalPersonFee')) {
            unset($item['legal_fee']);
        }
        // 推荐费(含列表字段权限)
        if (!userModel::getUserListAuth('legalPersonReCommendFee')) {
            unset($item['recommend_fee']);
        }
        // 公司注册情况
        if (!userModel::getUserListAuth('legalPersonCompany')) {
            unset($item['company']);
        }
        return $item;
    }

    public function getByIdCard($id_card, $id = 0)
    {
        $this->db->table($this->table)->where('where id_card = :id_card', ['id_card' => $id_card]);
        if ($id) {
            $this->db->andWhere('id != :id', ['id' => $id]);
        }
        return $this->db->one();
    }

    // 新增
    public function add($data, $type = '新增')
    {
        $paras_list = self::$paras_list;
        if ($data['type'] == '定制' || $data['type'] == '现号') {
            unset($paras_list['agreement']);
            unset($paras_list['legal_fee']);
            unset($paras_list['recommend_fee']);
        }
        $this->dataValidCheck($data, $paras_list);

        $data = $this->jsonEncodeFormat($data);

        $data['user_id'] = userModel::$qwuser_id ?? 0;

        $id = parent::add($data, $type);

        // 重新生成缓存
        redisCached::del(redisCached::YWX_LEGAL_PERSON);

        return $id;
    }

    // 编辑
    public function edit($data, $id, $old_data = [], $type = '编辑', $remark = '', $result = '', $other_attach = [])
    {
        $detail = $this->getById($id);
        if (!$detail) {
            throw new Exception('数据不存在');
        }

        if ($detail['is_register_company'] == 1 && ($detail['is_register_company'] != $data['is_register_company'])) {
            throw new Exception('法人已注册公司,无法编辑');
        }
        $paras_list = self::$paras_list;
        if ($data['type'] == '现号') {
            unset($paras_list['agreement']);
            unset($paras_list['legal_fee']);
            unset($paras_list['recommend_fee']);
        }

        $this->dataValidCheck($data, $paras_list);

        parent::edit($data, $id, $detail, $type, $remark, $result, $other_attach);

        // 重新生成缓存
        redisCached::del(redisCached::YWX_LEGAL_PERSON);
    }

    // 申请公司
    public function applyCompany($id)
    {
        $detail = $this->getById($id);
        if (!$detail) {
            throw new Exception('法人不存在');
        }

        // 检查是否已有待处理的申请
        $exists = $this->db->table('company')
            ->where('legal_person_id = :legal_person_id', ['legal_person_id' => $id])
            ->one();

        if ($exists) {
            throw new Exception('已存在待处理的公司申请');
        }

        // 创建公司申请
        $data = [
            'legal_person_id' => $id,
            'register_status' => 1,
        ];

        $company_id = $this->db->table('company')->insert($data);
        // 创建关联关系
        $relationModel = new relationModel();
        $relationModel->createRelation('company', $company_id, 'legal_person', $id);

        // 通知
        $users = configModel::noticeUser('company', 'regist');
        if (!empty($users)) {
            messagesFrom::senMeg($users, 1 , "请您及时到【店铺管理系统】接收公司注册任务", $company_id, '', '公司待注册');
        }

        return true;
    }

    // 申请费用
    public function applyFee($id, $fee_data)
    {
        $detail = $this->getById($id);
        if (!$detail) {
            throw new Exception('法人不存在');
        }

        // 检查是否已有待处理的费用申请
        if (!empty($detail['fee_id'])) {
            throw new Exception('已存在待处理的费用申请');
        }

        // 分别获取法人费用和推荐费用
        $legal_fee = json_decode($detail['legal_fee'], true) ?? null;
        $recommend_fee = json_decode($detail['recommend_fee'], true) ?? null;
        $legal_fee_rule = $legal_fee['rule'] ?? [];
        $recommend_fee_rule = $recommend_fee['rule'] ?? [];

        $legal_fee_rule_year = [];
        foreach ($legal_fee_rule as $item) {
            for ($start = $item['start']; $start <= $item['end']; $start++) {
                $legal_fee_rule_year[$start] = $item['price'];
            }
        }
        $recommend_fee_rule_year = [];
        foreach ($recommend_fee_rule as $item) {
            for ($start = $item['start']; $start <= $item['end']; $start++) {
                $recommend_fee_rule_year[$start] = $item['price'];
            }
        }

        $apply_fee = [];
        $fee_msg = [];
        if ($fee_data['legal_fee_year']) {
            $apply_fee['legal_fee'][$fee_data['legal_fee_year']] = [
                'price' => $legal_fee_rule_year[$fee_data['legal_fee_year']] ?? null
            ];
            $fee_msg[] = "{$fee_data['legal_fee_year']}年的法人费";
        }

        if ($fee_data['recommend_fee_year']) {
            $apply_fee['recommend_fee'][$fee_data['recommend_fee_year']] = [
                'price' => $recommend_fee_rule_year[$fee_data['recommend_fee_year']] ?? null
            ];
            $fee_msg[] = "{$fee_data['recommend_fee_year']}年推荐费";
        }

        // 创建费用申请任务
        $feeModel = new legalPersonFeeModel();
        $feeData = [
            'legal_person_id' => $id,
            'fee_detail'      => json_encode($apply_fee, JSON_UNESCAPED_UNICODE),
            'type'            => 1,
            'remark'          => $fee_data['remark'] ?? '',
        ];

        $fee_id = $feeModel->add($feeData);

        $legal_fee['apply_year'][] = $fee_data['legal_fee_year'];
        $recommend_fee['apply_year'][] = $fee_data['recommend_fee_year'];
        $old_data = $detail;
        $new_data = [
            'fee_id' => $fee_id,
            'legal_fee' => $legal_fee,
            'recommend_fee' => $recommend_fee,
        ];

        // 通知
        $users = configModel::noticeUser('legal_person', 'fee');
        if (!empty($users)) {
            $user_name = userModel::$wname;
            $fee_msg = implode("、", $fee_msg);
            messagesFrom::senMeg($users, 1 , "【{$user_name }】提交了{$fee_msg}的申请，请您及时进行费用支付确认", $id, '', '法人费用申请');
        }

        parent::edit($new_data, $id, $old_data);
    }

    // 确认费用
    public function confirmFee($id, $fee_data)
    {
        $detail = $this->getById($id);
        $legal_fee = json_decode($detail['legal_fee'], true);
        $recommend_fee = json_decode($detail['recommend_fee'], true);
        if (!$detail) {
            throw new Exception('法人不存在');
        }

        // 检查是否已有待处理的费用申请
        if (empty($detail['fee_id'])) {
            throw new Exception('不存在待处理的费用申请');
        }

        $feeModel = new legalPersonFeeModel();
        $fee = $feeModel->getById($detail['fee_id']);
        $fee_detail = $fee_data['fee_detail'];

        // 创建费用申请任务
        $feeModel = new legalPersonFeeModel();
        $feeData = [
            'legal_person_id' => $id,
            'pid'             => $detail['fee_id'],
            'fee_detail'      => json_encode($fee_data['fee_detail'], JSON_UNESCAPED_UNICODE),
            'type'            => 2,
            'remark'          => $fee_data['remark'] ?? '',
        ];

        $feeModel->add($feeData);

        // 通知
        $fee_msg_pay = [];
        $fee_msg_unpaid = [];

        $legal_fee_year = [];
        $recommend_fee_year = [];
        foreach ($fee_detail['legal_fee'] as $year => $item) {
            if ($item['status'] == '已支付') {
                $fee_msg_pay[] = "{$year}年的法人费";
            } else {
                $fee_msg_unpaid[] = "{$year}年的法人费";
                $legal_fee_year[] = $year;
            }
        }
        foreach ($fee_detail['recommend_fee'] as $year => $item) {
            if ($item['status'] == '已支付') {
                $fee_msg_pay[] = "{$year}年的推荐费";
            } else {
                $fee_msg_unpaid[] = "{$year}年的推荐费";
                $recommend_fee_year[] = $year;
            }
        }
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_wid', 'user_id');

        $user_name = userModel::$wname;
        $text = [];
        if (!empty($fee_msg_pay)) {
            $fee_msg_pay = implode("、", $fee_msg_pay);
            $text[] = '已支付'."{$fee_msg_pay}";
        }
        if (!empty($fee_msg_unpaid)) {
            $fee_msg_unpaid = implode("、", $fee_msg_unpaid);
            $text[] = '未支付'."{$fee_msg_unpaid}";
        }
        $text = implode(",", $text);
        messagesFrom::senMeg([$users[$detail['operator']]], 1 , "【{$user_name }】{$text}", $id, '', '法人费用已确认');

        // 更新法人费用和推荐费用
        $legal_apply_year = array_values(array_diff($legal_fee['apply_year'], $legal_fee_year));
        $recommend_apply_year = array_values(array_diff($recommend_fee['apply_year'], $recommend_fee_year));
        $legal_fee['apply_year'] = $legal_apply_year ?: [];
        $recommend_fee['apply_year'] = $recommend_apply_year ?: [];

        $new_data = [
            'fee_id' => null,
            'legal_fee' => json_encode($legal_fee, JSON_UNESCAPED_UNICODE),
            'recommend_fee' => json_encode($recommend_fee, JSON_UNESCAPED_UNICODE),
        ];
        $this->db->table($this->table)->where('id = :id', ['id' => $id])->update($new_data);
    }

    // 撤销费用申请
    public function cancelFee($id)
    {
        $detail = $this->getById($id);
        $legal_fee = json_decode($detail['legal_fee'], true);
        $recommend_fee = json_decode($detail['recommend_fee'], true);
        if (!$detail) {
            throw new Exception('法人不存在');
        }

        if (!$detail['fee_id']) {
            throw new Exception('没有待处理的费用申请');
        }

        $feeModel = new legalPersonFeeModel();
        $fee = $feeModel->getById($detail['fee_id']);

        // 更新任务状态为已撤销
        $feeData = [
            'legal_person_id' => $id,
            'fee_detail'      => $fee['fee_detail'],
            'pid'             => $detail['fee_id'],
            'type'            => 3,
        ];
        $feeModel->add($feeData);

        // 通知
        $legal_fee_year = [];
        $recommend_fee_year = [];
        $fee_msg = [];
        $fee_detail = json_decode($fee['fee_detail'], true);
        foreach ($fee_detail['legal_fee'] as $year => $item) {
            $legal_fee_year[] = $year;
            $fee_msg[] = "{$year}年的法人费";
        }
        foreach ($fee_detail['recommend_fee'] as $year => $item) {
            $recommend_fee_year[] = $year;
            $fee_msg[] = "{$year}年推荐费";
        }
        $users = configModel::noticeUser('legal_person', 'fee');
        if (!empty($users)) {
            $user_name = userModel::$wname;
            $fee_msg = implode("、", $fee_msg);
            messagesFrom::senMeg($users, 1 , "【{$user_name }】已撤销{$fee_msg}的申请，您无需进行费用支付确认", $id, '', '法人费用申请已撤销');
        }

        // 更新法人费用和推荐费用
        $legal_apply_year = array_values(array_diff($legal_fee['apply_year'], $legal_fee_year));
        $recommend_apply_year = array_values(array_diff($recommend_fee['apply_year'], $recommend_fee_year));
        $legal_fee['apply_year'] = $legal_apply_year ?: [];
        $recommend_fee['apply_year'] = $recommend_apply_year ?: [];

        $new_data = [
            'fee_id' => null,
            'legal_fee' => json_encode($legal_fee, JSON_UNESCAPED_UNICODE),
            'recommend_fee' => json_encode($recommend_fee, JSON_UNESCAPED_UNICODE),
        ];
        $this->db->table($this->table)->where('id = :id', ['id' => $id])->update($new_data);
    }

    // 删除
    public function delete($id)
    {
        $detail = $this->getById($id);
        if (!$detail) {
            throw new Exception('数据不存在');
        }

        // 检查是否有关联的公司
        $hasCompany = $this->db->table('company')
            ->where('legal_person_id = :legal_person_id', ['legal_person_id' => $id])
            ->one();

        if ($hasCompany) {
            throw new Exception('该法人已关联公司,无法删除');
        }

        $old_data = $detail;
        $new_data = [
            'is_delete' => 1,
        ];

        $users = configModel::noticeUser('legal_person', 'del');
        if (!empty($users)) {
            $user_name = userModel::$wname;
            messagesFrom::senMeg($users, 1 , "【{$user_name }】在店铺管理系统中删除了{$detail['name']}法人", $id, '', '法人删除');
        }

        parent::edit($new_data, $id, $old_data, '删除');
    }

    // 批量编辑
    public function editBatch($data)
    {
        $db = $this->db->table($this->table);
        $ids = array_column($data, 'id');
        $db->whereIn('id', $ids);
        $list = $db->list();
        $list = array_column($list, null, 'id');

        $error_ids = [];
        foreach ($data as $item) {
            try {
                $item_id = $item['id'];
                unset($item['id']);
                // 唯一性校验
                $this->dataValidCheck($item, self::$paras_list);
                $detail = $this->getByIdCard($item['id_card'], $item_id);
                if ($detail) {
                    throw new Exception('法人身份证号码已存在');
                }
                self::edit($item, $item_id, $list[$item_id], '批量编辑');
            } catch (Exception $e) {
                $error_ids[] = [
                    'id' => $item_id,
                    'error' => $e->getMessage(),
                ];
            }
        }
        return $error_ids;
    }
}