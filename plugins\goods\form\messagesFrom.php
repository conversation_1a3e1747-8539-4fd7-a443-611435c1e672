<?php
/**
 * @author: zhangguoming
 * @Time: 2024/2/26 17:21
 */

namespace  plugins\goods\form;

use plugins\goods\models\userModel;
use core\lib\db\dbMysql;
use core\lib\predisV;

class messagesFrom
{
    public static array $next_node_info_data = [];//下个节点待办事项通知
    public static array $project_agent_info = [];//流程中交办事项通知
    public static array $wids_test = ["ZhangGuoMing","LiaoLiangJing","SongXiWen",'WangMingShou',"<PERSON><PERSON>unY<PERSON>","Mao<PERSON>uHang","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","Zhou<PERSON>ing<PERSON><PERSON>","HeSongLin","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON>YuYang","yuyang01","yuyang02","yuyang03"];
    /**
     * @param array $w_userids
     * @param string $text
     * @param array $other_data   $other_data = [
            'user_id'=>$user['id'],
            'model_id'=>$project_['id'],
            'node_index'=>$node_index_id,
            'event_index'=>$event_index_id,
            'msg_type'=>1
            ];
     * @return void
     * @throws \core\lib\ExceptionError
     */
    public static function senMeg(array $w_userids, string $text, array $other_data,string $remarks='') {
        $w_userids = array_unique($w_userids);
        if (empty($text)) {
            SetReturn(-1,'消息不能为空');
        }
        $db = dbMysql::getInstance();
        $msg_type = $other_data['msg_type']??0;
        $insert_data = [
            'user_id'=>$other_data['user_id'],
            'model_id'=>$other_data['model_id'],
            'node_index'=>$other_data['node_index']??null,
            'event_index'=>$other_data['event_index']??null,
            'text'=>$text,
            'created_at'=>date('Y-m-d H:i:s'),
            'type'=>$msg_type,
            'remarks'=>$remarks,
            'title'=>self::getQwMsgTitle($msg_type),
        ];
        $msg_data = [
            'system_type'=>1,//系统1产品系统，2财务系统
            'type'=>'textcard',
            'msg'=>$text,
            'title'=>self::getQwMsgTitle($msg_type),
        ];
        new predisV();
        $db->table('messages');
        foreach ($w_userids as $k=>$user_id) {
//            if (!in_array($user_id,self::$wids_test)) {
//                continue;
//            }
            $insert_data['qw_userid'] = $user_id;
            $msg_id = $db->insert($insert_data);
            $msg_data['qw_userid'] = $user_id;
            $msg_data['data'] = json_encode(['id'=>$msg_id]);
            predisV::redisQueue($msg_data);
        }
        //websocket推送消息
//        $swool_data = [
//            'qw_userid'=>$w_userids,
//            'msg'=>$text
//        ];
        //webSocket消息聊天通知
//        \core\lib\swoole\webSocketSwoole::sendMsg(json_encode($swool_data, JSON_UNESCAPED_UNICODE));
    }
    public static function senMessage(array $wids, string $text, int $user_id,int $model_id, int $msg_type = 0, $node_index = null, $event_index = null, string $remarks='') {
        $wids = array_unique($wids);
        if (empty($text)) {
            SetReturn(-1,'消息不能为空');
        }
        $db = dbMysql::getInstance();
        $insert_data = [
            'user_id'=>$user_id,
            'model_id'=>$model_id,
            'node_index'=>$node_index,
            'event_index'=>$event_index,
            'text'=>$text,
            'created_at'=>date('Y-m-d H:i:s'),
            'type'=>$msg_type,
            'remarks'=>$remarks,
            'title'=>self::getQwMsgTitle($msg_type),
        ];
        $msg_data = [
            'system_type'=>1,//系统1产品系统，2财务系统
            'type'=>'textcard',
            'msg'=>$text,
            'title'=>self::getQwMsgTitle($msg_type),
        ];
        new predisV();
        $db->table('messages');
        foreach ($wids as $k=>$user_id) {
//            if (!in_array($user_id,self::$wids_test)) {
//                continue;
//            }
            $insert_data['qw_userid'] = $user_id;
            $msg_id = $db->insert($insert_data);
            $msg_data['qw_userid'] = $user_id;
            $msg_data['data'] = json_encode(['id'=>$msg_id]);
            predisV::redisQueue($msg_data);
        }
    }

    //发送消息给下一个节点所有负责人
    public static function sendNextNodeInfo($remake='') {
        if (count(self::$next_node_info_data)) {
            //发送消息数据记录
            foreach (self::$next_node_info_data as $msg) {
                self::senMeg($msg['wids'],$msg['msg'],$msg['other_data'],$remake);
            }
        }
    }

    /**
     * @param $project_id
     * @param $current_event
     * @param $node_manage
     * @param $goods_manage
     * @param $node_index
     * @param $event_index
     * @return void 事件完成时发送系统消息给事件通知人，抄送人，产品开发，节点负责人
     */
    public static function sendMsgForSubmitEvent($project_id,$current_node,$goods_info,$node_index,$event_index,$remarks='') {
        $node_index_ = explode('-',$event_index);
        $current_event = $current_node['event_detail'][$node_index_[0]][$node_index_[1]];
        $msg_data = [];
        //节点负责人
        $node_manage = json_decode($current_node['manage_info'],true);
        $other_data = [
            'user_id'=>userModel::$qwuser_id,
            'model_id'=>$project_id,
            'node_index'=>$node_index,
            'event_index'=>$event_index,
            'msg_type'=>1
        ];
        $msg_data[] = [
            'wids'=>array_column($node_manage,'wid'),
            'msg'=>self::getMsgTxt(1,$goods_info['matter_name'],$current_node['node_name'],$current_event['event_name']),
            'other_data'=>$other_data,
        ];
        //产品负责人
        $goods_manage = json_decode($goods_info['manage_info'],true);
        $msg_data[] = [
            'wids'=>array_column($goods_manage,'wid'),
            'msg'=>self::getMsgTxt(1,$goods_info['matter_name'],$current_node['node_name'],$current_event['event_name']),
            'other_data'=>$other_data,
        ];
        //事件通知人
        $event_notify_user = !empty($current_event['notify_user'])?json_decode($current_event['notify_user'],true):[];
        $msg_data[] = [
            'wids'=>array_column($event_notify_user,'wid'),
            'msg'=>self::getMsgTxt(1,$goods_info['matter_name'],$current_node['node_name'],$current_event['event_name']),
            'other_data'=>$other_data,
        ];
        //事件抄送人
        if ($current_event['send_copy_user']) {
            $other_data['msg_type'] = 2;
            $event_coyp_user = json_decode($current_event['send_copy_user'],true);
            $msg_data[] = [
                'wids'=>array_column($event_coyp_user,'wid'),
                'msg'=>self::getMsgTxt(2,$goods_info['matter_name'],$current_node['node_name'],$current_event['event_name']),
                'other_data'=>$other_data,
            ];
        }

        //事件审核人通知
//        if ($current_event['need_check'] == 1) {
//            $check_user = json_decode($current_event['check_user_info'],true);
//            $other_data = [
//                'user_id'=>userModel::$qwuser_id,
//                'model_id'=>$project_id,
//                'node_index'=>$node_index,
//                'event_index'=>$event_index,
//                'msg_type'=>7
//            ];
//            $msg_data[] = [
//                'wids'=>array_column($check_user,'wid'),
//                'msg'=>self::getMsgTxt(8,$goods_info['matter_name'],$current_node['node_name'],$current_event['event_name']),
//                'other_data'=>$other_data,
//            ];
//        }

        foreach ($msg_data as $v) {
            if (count($v['wids'])) {
                self::senMeg($v['wids'],$v['msg'],$v['other_data'],$remarks);
            }
        }
    }


    /**
     * @param $project_id
     * @param $current_node
     * @param $goods_info
     * @param $node_index
     * @return void
     * @throws \core\lib\ExceptionError  节点通知人 抄送人，产品开发
     */
    public static function sendMsgForSubmitNode(int $project_id,array $current_node,array $goods_info,string $node_index,array $ext_copy_user=[]) {
        $other_data = [
            'user_id'=>userModel::$qwuser_id,
            'model_id'=>$project_id,
            'node_index'=>$node_index,
            'msg_type'=>1
        ];
        //产品负责人
        $goods_manage = json_decode($goods_info['manage_info'],true);
        $msg_data[] = [
            'wids'=>array_column($goods_manage,'wid'),
            'msg'=>self::getMsgTxt(1,$goods_info['matter_name'],$current_node['node_name'],''),
            'other_data'=>$other_data,
        ];
        //节点通知人
        $event_notify_user = !empty($current_node['notify_user'])?json_decode($current_node['notify_user'],true):[];
        $msg_data[] = [
            'wids'=>array_column($event_notify_user,'wid'),
            'msg'=>self::getMsgTxt(1,$goods_info['matter_name'],$current_node['node_name'],''),
            'other_data'=>$other_data,
        ];
        //事件抄送人
        $other_data['msg_type'] = 2;
        $event_coyp_user = json_decode($current_node['send_copy_user'],true);
        $copy_user = array_merge($event_coyp_user,$ext_copy_user);
        $copy_user = arrayUnique2($copy_user,'wid');
        $msg_data[] = [
            'wids'=>array_column($copy_user,'wid'),
            'msg'=>self::getMsgTxt(2,$goods_info['matter_name'],$current_node['node_name'],''),
            'other_data'=>$other_data,
        ];
        foreach ($msg_data as $v) {
            if (count($v['wids'])) {
                $wids = array_unique($v['wids']);
                self::senMeg($wids,$v['msg'],$v['other_data']);
            }
        }
    }

    //给项目流程中交办人发信息
    public static function senMsgForProjectAgent(){
        if (count(self::$project_agent_info)) {
            //发送消息数据记录
            foreach (self::$project_agent_info as $msg) {
                self::senMeg($msg['wids'],$msg['msg'],$msg['other_data']);
            }
        }
    }
    //流程中消息推送模板
    public static function getMsgTxt($type,$matter_name,$node_name,$event_name) {
        $msg = '';
        switch ($type) {
            case 1;
                //完成节点或事件通知
                $msg = '【'.$matter_name.'】流程的【'.$node_name.($event_name?'/'.$event_name:'').'】已提交。';
                break;
            case 2;
                //抄送通知
                if (empty($node_name)) {
                    // 手动
                    $msg = '用户【'.userModel::$wname.'】将【'.$matter_name.'】流程信息已抄送给您。';
                } else {
                    //手动
                    $msg = '【'.$matter_name.'】流程的【'.$node_name.($event_name?'/'.$event_name:'').'】已提交，请查看流程信息。';
                }
                break;
            case 3;
                //产品新增反馈通知
                $msg = "用户【".userModel::$wname."】在产品【{$matter_name}】中新增了问题反馈，请及时处理。";
                break;
            case 4;
                //催办提醒
                $msg = "用户【".userModel::$wname."】提醒您，请及时处理【{$matter_name}】流程的".'【'.$node_name.($event_name?'/'.$event_name:'').'】。';
                break;
            case 5;
                //待处理事项
                $msg = "【{$matter_name}】流程已到达【".$node_name.($event_name?'/'.$event_name:'')."】，请及时处理，注意待办事件的预计完成时间。";
                break;
            case 6;
                //异常通知
                if (empty($node_name)) {
                    $msg = "用户【".userModel::$wname."】在【{$matter_name}】流程中提交了异常，请及时处理。";
                } else {
                    //手动
                    $msg = "用户【".userModel::$wname."】在【{$matter_name}】流程的【".$node_name.($event_name?'/'.$event_name:'')."】中提交了异常，请及时处理。";
                }
                break;
            case 7;
                //审核通知结果, 未通过
                if (empty($node_name)) {
                    $msg = "【{$matter_name}】流程的【{$node_name}】已审核，审核未通过。审核人【".userModel::$wname."】";
                } else {
                    $msg = "【{$matter_name}】流程的【".$node_name.($event_name?'/'.$event_name:'')."】已审核，审核未通过。审核人【".userModel::$wname."】";
                }
                break;
            case 9;
                //审核,通过
                if (empty($node_name)) {
                    $msg = "【{$matter_name}】流程的【{$node_name}】已审核，审核已通过。审核人【".userModel::$wname."】";
                } else {
                    $msg = "【{$matter_name}】流程的【".$node_name.($event_name?'/'.$event_name:'')."】已审核，审核已通过。审核人【".userModel::$wname."】";
                }
                break;
            case 8;
                //发起审核
                if (empty($node_name)) {
                    $msg = "【{$matter_name}】流程的【{$node_name}】已提交，请您审核。";
                } else {
                    //手动
                    $msg = "【{$matter_name}】流程的【".$node_name.($event_name?'/'.$event_name:'')."】已提交，请您审核。";
                }
                break;
            case 10;
                //发起审批
                $msg = "【{$matter_name}】流程的【{$node_name}】存在不合格项，请您审批。";
                break;
            case 11;
                //审批未通过
                $msg = "【{$matter_name}】流程的【{$node_name}】不合格项审批，未通过。";
                break;
            case 12;
                //审批通过
                $msg = "【{$matter_name}】流程的【{$node_name}】不合格项审批，已通过。";
                break;
            case 13;
                //交办
                $msg = "用户【".userModel::$wname."】已将{$matter_name}流程的【".$node_name.($event_name?'/'.$event_name:'')."】交办给您，请及时处理。";
                break;
        }
        return $msg;
    }
    //图片需求持消息模板
    public static function getMsgTxtForImgRequest($type,$goods_name = '',$request_name = '') {
        $msg = '';
        switch ($type) {
            case 1:
                //待办通知
                $msg = "产品【{$goods_name}】的【{$request_name}】需求图的制作任务已分配给您，请及时处理。";
                break;
            case 2:
                //保存并提交了图片
                $msg = "产品【{$goods_name}】的【{$request_name}】需求任务已完成并提交，请您审核。";
                break;
            case 3:
                //图片审核通过
                $msg = "产品【{$goods_name}】的【{$request_name}】审核已通过。";
                break;
            case 4:
                //图片审核未通过
                $msg = "产品【{$goods_name}】的【{$request_name}】审核未通过。";
                break;
            case 5:
                //催办提醒
                $msg = "用户【".userModel::$wname."】提醒您，请及时上传【{$request_name}】。";
                break;
            case 6:
                //审核提示
                $msg = "产品【{$goods_name}】的【{$request_name}】需求任务已完成并审核，请您再次审核。";
                break;
            case 7:
                //运营审核提示
                $msg = "产品【{$goods_name}】的【{$request_name}】对应产品运营【".userModel::$wname."】审核已通过。";
                break;
            case 8:
                //运营审核提示
                $msg = "产品【{$goods_name}】的【{$request_name}】对应产品运营【".userModel::$wname."】审核未通过。";
                break;
            case 9:
                //运营审核提示
                $msg = "产品【{$goods_name}】的【{$request_name}】对应产品运营【".userModel::$wname."】审核未通过，已将任务打回。";
                break;
            case 10:
                //重新上传
                $msg = "产品【{$goods_name}】的【{$request_name}】已重新上传，请您悉知。";
                break;
            case 11;
                //图片拍摄
                $msg = "产品【{$goods_name}】的【{$request_name}】的拍摄任务已分配给您，请及时处理。";
                break;
            case 12:
                //图片完成通知
                $msg = "产品【{$request_name}】的拍摄已完成，请您悉知。";
                break;
            case 13:
                //催办提醒
                $msg = "用户【".userModel::$wname."】提醒您，请及时上传【{$request_name}】拍摄图片。";
                break;
            case 14:
                //待接收通知
                $msg = "您有新的作图需求，请您及时接收，开启任务。";
                break;

        }

        return $msg;
    }
    //消息标题
    public static function getQwMsgTitle($type) {
        $title = '';
        switch ($type) {
            case 0:
                //完成节点或事件通知
                $title = '系统通知';break;
            case 1:
                //完成节点或事件通知
                $title = '提交';break;
            case 2:
                //完成节点通知给抄送人   主动
                $title = "抄送通知";break;
            case 3:
                $title = '交办通知';break;
            case 4:
                //催办提醒
                $title = '催办提醒';break;
            case 5:
                //待处理事项
                $title = 'OA待处理事项新增';break;
            case 6:
                //待处理事项
                $title = '异常通知';break;
            case 7:
                //待处理事项
                $title = '审核通知';break;
            case 8:
                //待处理事项
                $title = '审批通知';break;
            case 9:
                $title = '任务超时提醒';break;
            case 10:
                $title = 'SOP待审核';break;
            case 11:
                $title = 'SOP审核不通过';break;
            case 12:
                $title = '图片任务待接收';break;
            case 14:
                $title = '任务进度提醒';break;
            case 16:
                $title = '图片任务完成';break;
            case 17:
                $title = '图片需求待配置';break;
            case 18:
                $title = 'APP图片待审核';break;
            case 19:
                $title = '图片任务暂停';break;
            case 20:
                $title = '图片任务恢复';break;
            case 21:
                $title = '图片测试结果';break;
            case 22:
                $title = '图片任务变更';break;
        }
        return $title;
    }




















}