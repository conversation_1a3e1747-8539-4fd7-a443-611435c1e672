openapi: 3.0.0
info:
  title: 店铺管理API
  version: 1.0.0
  description: 提供店铺管理相关的接口

paths:
  /shop/getList:
    get:
      tags:
        - 店铺管理
      summary: 获取店铺列表
      description: 根据条件筛选获取店铺列表
      parameters:
        - name: shop_code
          in: query
          description: 店铺编号
          required: false
          schema:
            type: string
        - name: register_type
          in: query
          description: 注册类型
          required: false
          schema:
            type: string
        - name: account_type
          in: query
          description: 账号类型
          required: false
          schema:
            type: string
        - name: shop_site
          in: query
          description: 店铺站点
          required: false
          schema:
            type: string
        - name: business_manager
          in: query
          description: 招商经理
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页条数
          required: false
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/Shop'
                      total:
                        type: integer
                        description: 总记录数

  /shop/getDetail:
    get:
      tags:
        - 店铺管理
      summary: 获取店铺详情
      description: 根据ID获取店铺详细信息
      parameters:
        - name: id
          in: query
          description: 店铺ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回详情数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    $ref: '#/components/schemas/Shop'

  /shop/add:
    post:
      tags:
        - 店铺管理
      summary: 新增店铺
      description: 添加新的店铺信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ShopCreate'
      responses:
        '200':
          description: 添加成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 添加成功
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 新增记录ID

  /shop/update:
    post:
      tags:
        - 店铺管理
      summary: 更新店铺
      description: 修改已有店铺信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ShopUpdate'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 更新成功

  /shop/delete:
    get:
      tags:
        - 店铺管理
      summary: 删除店铺
      description: 删除指定店铺
      parameters:
        - name: id
          in: query
          description: 店铺ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 删除成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 删除成功

  /shop/getDailyCheckList:
    get:
      tags:
        - 日检记录
      summary: 获取日检记录列表
      description: 获取店铺日检记录列表
      parameters:
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页条数
          required: false
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/DailyCheck'
                      total:
                        type: integer
                        description: 总记录数

  /shop/createDailyCheck:
    post:
      tags:
        - 日检记录
      summary: 创建日检记录
      description: 添加新的日检记录
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DailyCheckCreate'
      responses:
        '200':
          description: 创建成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 创建成功
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 新增记录ID

  /shop/getWeeklyCheckList:
    get:
      tags:
        - 周检记录
      summary: 获取周检记录列表
      description: 获取店铺周检记录列表
      parameters:
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页条数
          required: false
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/WeeklyCheck'
                      total:
                        type: integer
                        description: 总记录数

  /shop/createWeeklyCheck:
    post:
      tags:
        - 周检记录
      summary: 创建周检记录
      description: 添加新的周检记录
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WeeklyCheckCreate'
      responses:
        '200':
          description: 创建成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 创建成功
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 新增记录ID

  /shop/getAppealList:
    get:
      tags:
        - 申诉管理
      summary: 获取申诉列表
      description: 获取店铺申诉记录列表
      parameters:
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页条数
          required: false
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/Appeal'
                      total:
                        type: integer
                        description: 总记录数

  /shop/createAppeal:
    post:
      tags:
        - 申诉管理
      summary: 创建申诉
      description: 提交店铺申诉
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AppealCreate'
      responses:
        '200':
          description: 创建成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 创建成功
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 新增记录ID

  /shop/processAppeal:
    post:
      tags:
        - 申诉管理
      summary: 处理申诉
      description: 处理店铺申诉
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 申诉ID
                result:
                  type: string
                  description: 处理结果
              required:
                - id
                - result
      responses:
        '200':
          description: 处理成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 处理成功

  /shop/getPasswordViewList:
    get:
      tags:
        - 密码查看管理
      summary: 获取密码查看申请列表
      description: 获取密码查看申请记录列表
      parameters:
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页条数
          required: false
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/PasswordView'
                      total:
                        type: integer
                        description: 总记录数

  /shop/applyPasswordView:
    post:
      tags:
        - 密码查看管理
      summary: 申请查看密码
      description: 提交密码查看申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordViewCreate'
      responses:
        '200':
          description: 申请成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 申请成功
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 新增记录ID

  /shop/approvePasswordView:
    post:
      tags:
        - 密码查看管理
      summary: 审批密码查看申请
      description: 处理密码查看申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 申请ID
                status:
                  type: integer
                  description: 审批状态
              required:
                - id
                - status
      responses:
        '200':
          description: 审批成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 审批成功

  /shop/viewPassword:
    get:
      tags:
        - 密码查看管理
      summary: 查看店铺密码
      description: 查看指定店铺的密码信息
      parameters:
        - name: shop_id
          in: query
          description: 店铺ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 获取成功
                  data:
                    type: object
                    properties:
                      email_password:
                        type: string
                        description: 邮箱密码
                      shop_password:
                        type: string
                        description: 店铺密码

  /shop/getFeeList:
    get:
      tags:
        - 费用管理
      summary: 获取费用列表
      description: 获取店铺费用记录列表
      parameters:
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页条数
          required: false
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/Fee'
                      total:
                        type: integer
                        description: 总记录数

  /shop/applyFee:
    post:
      tags:
        - 费用管理
      summary: 申请费用
      description: 提交费用申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FeeCreate'
      responses:
        '200':
          description: 申请成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 申请成功
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 新增记录ID

  /shop/confirmFee:
    post:
      tags:
        - 费用管理
      summary: 确认费用
      description: 确认费用申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: 费用记录ID
              required:
                - id
      responses:
        '200':
          description: 确认成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 确认成功

  /shop/cancelFee:
    get:
      tags:
        - 费用管理
      summary: 撤销费用
      description: 撤销费用申请
      parameters:
        - name: id
          in: query
          description: 费用记录ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 撤销成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 撤销成功

components:
  schemas:
    Shop:
      type: object
      properties:
        id:
          type: integer
          description: 店铺ID
        register_type:
          type: string
          description: 注册类型
        shop_number:
          type: string
          description: 店铺编号
        company_id:
          type: integer
          description: 公司ID
        account_type:
          type: string
          description: 账号类型
        coordinator:
          type: string
          description: 对接人
        shop_site:
          type: string
          description: 店铺站点
        business_manager:
          type: string
          description: 招商经理
        receive_card_id:
          type: integer
          description: 收款账号ID
        contact_group:
          type: string
          description: 对接群
        register_device:
          type: string
          description: 注册设备
        phone_card_id:
          type: integer
          description: 注册手机号ID
        email_id:
          type: integer
          description: 注册邮箱ID
        credit_card_id:
          type: integer
          description: 信用卡ID
        shop_password:
          type: string
          description: 店铺密码
        vat_register_status:
          type: string
          description: VAT注册情况
        epr_register_status:
          type: string
          description: EPR注册情况
        remark:
          type: string
          description: 备注
        created_at:
          type: string
          format: date-time
          description: 创建时间
        updated_at:
          type: string
          format: date-time
          description: 更新时间

    ShopCreate:
      type: object
      properties:
        register_type:
          type: string
          description: 注册类型
        shop_number:
          type: string
          description: 店铺编号
        company_id:
          type: integer
          description: 公司ID
        account_type:
          type: string
          description: 账号类型
        coordinator:
          type: string
          description: 对接人
        shop_site:
          type: string
          description: 店铺站点
        business_manager:
          type: string
          description: 招商经理
        receive_card_id:
          type: integer
          description: 收款账号ID
        contact_group:
          type: string
          description: 对接群
        register_device:
          type: string
          description: 注册设备
        phone_card_id:
          type: integer
          description: 注册手机号ID
        email_id:
          type: integer
          description: 注册邮箱ID
        credit_card_id:
          type: integer
          description: 信用卡ID
        shop_password:
          type: string
          description: 店铺密码
        vat_register_status:
          type: string
          description: VAT注册情况
        epr_register_status:
          type: string
          description: EPR注册情况
        remark:
          type: string
          description: 备注
      required:
        - register_type
        - shop_number
        - account_type

    ShopUpdate:
      type: object
      properties:
        id:
          type: integer
          description: 店铺ID
        register_type:
          type: string
          description: 注册类型
        shop_number:
          type: string
          description: 店铺编号
        company_id:
          type: integer
          description: 公司ID
        account_type:
          type: string
          description: 账号类型
        coordinator:
          type: string
          description: 对接人
        shop_site:
          type: string
          description: 店铺站点
        business_manager:
          type: string
          description: 招商经理
        receive_card_id:
          type: integer
          description: 收款账号ID
        contact_group:
          type: string
          description: 对接群
        register_device:
          type: string
          description: 注册设备
        phone_card_id:
          type: integer
          description: 注册手机号ID
        email_id:
          type: integer
          description: 注册邮箱ID
        credit_card_id:
          type: integer
          description: 信用卡ID
        shop_password:
          type: string
          description: 店铺密码
        vat_register_status:
          type: string
          description: VAT注册情况
        epr_register_status:
          type: string
          description: EPR注册情况
        remark:
          type: string
          description: 备注
      required:
        - id

    DailyCheck:
      type: object
      properties:
        id:
          type: integer
          description: 记录ID
        shop_id:
          type: integer
          description: 店铺ID
        check_result:
          type: string
          description: 检查结果
        check_date:
          type: string
          format: date
          description: 检查日期
        check_user:
          type: integer
          description: 检查人ID
        created_at:
          type: string
          format: date-time
          description: 创建时间

    DailyCheckCreate:
      type: object
      properties:
        shop_id:
          type: integer
          description: 店铺ID
        check_result:
          type: string
          description: 检查结果
      required:
        - shop_id
        - check_result

    WeeklyCheck:
      type: object
      properties:
        id:
          type: integer
          description: 记录ID
        shop_id:
          type: integer
          description: 店铺ID
        check_result:
          type: string
          description: 检查结果
        check_date:
          type: string
          format: date
          description: 检查日期
        check_user:
          type: integer
          description: 检查人ID
        created_at:
          type: string
          format: date-time
          description: 创建时间

    WeeklyCheckCreate:
      type: object
      properties:
        shop_id:
          type: integer
          description: 店铺ID
        check_result:
          type: string
          description: 检查结果
      required:
        - shop_id
        - check_result

    Appeal:
      type: object
      properties:
        id:
          type: integer
          description: 记录ID
        shop_id:
          type: integer
          description: 店铺ID
        appeal_content:
          type: string
          description: 申诉内容
        appeal_user:
          type: integer
          description: 申诉人ID
        appeal_result:
          type: string
          description: 处理结果
        created_at:
          type: string
          format: date-time
          description: 创建时间

    AppealCreate:
      type: object
      properties:
        shop_id:
          type: integer
          description: 店铺ID
        appeal_content:
          type: string
          description: 申诉内容
      required:
        - shop_id
        - appeal_content

    PasswordView:
      type: object
      properties:
        id:
          type: integer
          description: 记录ID
        shop_id:
          type: integer
          description: 店铺ID
        apply_user:
          type: integer
          description: 申请人ID
        apply_reason:
          type: string
          description: 申请原因
        approve_status:
          type: integer
          description: 审批状态
        created_at:
          type: string
          format: date-time
          description: 创建时间

    PasswordViewCreate:
      type: object
      properties:
        shop_id:
          type: integer
          description: 店铺ID
        apply_reason:
          type: string
          description: 申请原因
      required:
        - shop_id
        - apply_reason

    Fee:
      type: object
      properties:
        id:
          type: integer
          description: 记录ID
        shop_id:
          type: integer
          description: 店铺ID
        fee_type:
          type: string
          description: 费用类型
        fee_amount:
          type: number
          format: float
          description: 费用金额
        fee_description:
          type: string
          description: 费用说明
        apply_user:
          type: integer
          description: 申请人ID
        confirm_status:
          type: integer
          description: 确认状态
        created_at:
          type: string
          format: date-time
          description: 创建时间

    FeeCreate:
      type: object
      properties:
        shop_id:
          type: integer
          description: 店铺ID
        fee_type:
          type: string
          description: 费用类型
        fee_amount:
          type: number
          format: float
          description: 费用金额
        fee_description:
          type: string
          description: 费用说明
      required:
        - shop_id
        - fee_type
        - fee_amount
        - fee_description
