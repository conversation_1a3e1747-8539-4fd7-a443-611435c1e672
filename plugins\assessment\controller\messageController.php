<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/22 15:07
 */

namespace plugins\assessment\controller;

use core\lib\db\dbAMysql;
use plugins\assessment\models\userModel;

class messageController
{
    public function getList()
    {
        $paras_list = array('is_read','page','page_size','type');
        $param = arrangeParam($_GET, $paras_list);
        $is_read = (int)$param['is_read'];
        $type = (int)$param['type'];
        $adb = dbAMysql::getInstance();
        $adb->table('messages')
            ->where('where qw_userid=:qw_userid',['qw_userid'=>userModel::$wid]);
        if ($is_read > -1) {
            $adb->andWhere('is_read=:is_read',['is_read'=>$is_read]);
        }
        if ($type == 2) {
            $adb->andWhere('type=:type',['type'=>$type]);
        }
        $adb->field('id,title,qw_userid,text,type,is_read,created_at,remarks');
        $adb->order('is_read asc,id desc');
        $list = $adb->pages($param['page'],$param['page_size']);
        returnSuccess($list);
    }
    public function getMessageCount() {
        //计数
        $adb = dbAMysql::getInstance();
        $project_msg_count = $adb->table('messages')
            ->where('where qw_userid=:qw_userid and is_read=0',['qw_userid'=>userModel::$wid])
            ->count();
        $project_copy_msg_count = $adb->table('messages')
            ->where('where qw_userid=:qw_userid and is_read=0',['qw_userid'=>userModel::$wid])
            ->andWhere('type=2')
            ->count();
        $list = [
            'project_msg_count'=>$project_msg_count,
            'project_copy_msg_count'=>$project_copy_msg_count,
        ];
        returnSuccess($list);
    }
    public function getDetail()
    {
        $id = (int)$_GET['id'];
        if (!$id) {
            SetReturn(-1, '参数有误');
        }
        $adb = dbAMysql::getInstance();
        $adb->table('messages');
        $adb->where('where id = '.$id);
        $adb->field('id,title,qw_userid,type,text,model_id,created_at,remarks');
        $data = $adb->one();
        $adb->update(['is_read'=>1]);
        returnSuccess(['data'=>$data]);
    }

    //全部标记已读
    public function setAllRead()
    {
        $type = (int)$_GET['type'];
        $adb = dbAMysql::getInstance();
        $user = json_decode(USER_INFO,true);

        $adb->table('messages');
        $adb->where('where qw_userid = :qw_userid and is_read = 0',['qw_userid'=>$user['wid']]);
        if ($type == 2) {
            $adb->andWhere('type=2');
        }
        $adb->update(['is_read'=>1]);
        SetReturn(0, '全部已读');
    }

    //获取企微通知的消息
    public function getMsgDetail() {
        $secret_code = $_POST['data']??'';
        if (empty($secret_code)) {
            SetReturn(-1,'参数有误');
        }
        $data = qwMsgDecryption($secret_code);
        $data  = json_decode($data,true);
        $id = $data['id']??0;
        if (!$id) {
            SetReturn(-1,'参数有误');
        }
        $adb = dbAMysql::getInstance();
        $adb->table('messages');
        $adb->where('where id = '.$id);
        $data = $adb->one();
        $adb->update(['is_read'=>1]);
        returnSuccess(['data'=>$data]);
    }
}