<?php

/**
 * 节日活动控制器
 * @author: AI Assistant
 * @Time: 2025/5/29
 */

namespace plugins\logistics\controller;

use core\lib\validator;
use plugins\logistics\models\festivalActivitiesModel;
use plugins\logistics\models\userModel;

class festivalActivityController extends baseController
{
    private $model;

    public function __construct()
    {
        parent::__construct();
        $this->model = new festivalActivitiesModel();
    }

    /**
     * 获取节日活动列表
     * GET /logistics/festivalActivity/getList
     */
    public function getList()
    {
        $params = [
            'festival_type' => $_GET['festival_type'] ?? '',
            'status' => $_GET['status'] ?? '',
            'start_date' => $_GET['start_date'] ?? '',
            'end_date' => $_GET['end_date'] ?? '',
            'name' => $_GET['name'] ?? '',
            'order_by' => $_GET['order_by'] ?? '',
            'page' => isset($_GET['page']) ? (int)$_GET['page'] : null,
            'page_size' => isset($_GET['page_size']) ? (int)$_GET['page_size'] : null,
        ];

        $result = $this->model->getList($params);

        // 解析JSON字段为前端显示
        foreach ($result['list'] as &$item) {
            if (!empty($item['sites_config'])) {
                $item['sites_config'] = json_decode($item['sites_config'], true);
            }
            if (!empty($item['stock_rules'])) {
                $item['stock_rules'] = json_decode($item['stock_rules'], true);
            }
        }

        returnSuccess($result);
    }

    /**
     * 获取节日活动详情
     * GET /logistics/festivalActivity/getDetail?id=1
     */
    public function getDetail()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('ID参数不能为空');
        }

        $detail = $this->model->getById($id);
        if (!$detail) {
            returnError('记录不存在');
        }

        // 解析JSON字段
        if (!empty($detail['sites_config'])) {
            $detail['sites_config'] = json_decode($detail['sites_config'], true);
        }
        if (!empty($detail['stock_rules'])) {
            $detail['stock_rules'] = json_decode($detail['stock_rules'], true);
        }

        returnSuccess($detail);
    }

    /**
     * 创建节日活动
     * POST /logistics/festivalActivity/create
     */
    public function add()
    {
        $data = [
            'name' => $_POST['name'] ?? '',
            'festival_type' => $_POST['festival_type'] ?? '',
            'start_date' => $_POST['start_date'] ?? '',
            'end_date' => $_POST['end_date'] ?? '',
            'description' => $_POST['description'] ?? '',
            'sites_config' => $_POST['sites_config'] ?? [],
            'stock_rules' => $_POST['stock_rules'] ?? [],
            'status' => $_POST['status'] ?? 1,
            'created_by' => userModel::$qwuser_id ?? 0,
        ];

        // 参数验证
        try {
            validator::validate($data, [
                'name' => '名称|required|max:100',
                'festival_type' => '类型|required|in:1,2', // 1:节日, 2:活动
                'start_date' => '开始日期|required|date',
                'end_date' => '结束日期|required|date|after_or_equal:start_date',
                'description' => '描述|max:500',
                'sites_config' => '站点|array',
                'stock_rules' => '备货规则|array',
                'status' => 'in:0,1', // 0:禁用, 1:启用
            ]);
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }

        $id = $_POST['id'] ?? 0;
        $start_date = $data['start_date'];

        // 检查名称是否重复
        if ($this->model->isNameExists($data['name'], $start_date, $id)) {
            $festival_type = $_POST['festival_type'] == 1 ? '节日' : '活动';
            returnError("该{$festival_type}名称在当前年份已存在，请修改名称后再试。");
        }

        if ($id) {
            // 更新操作
            $result = $this->model->getById($id);
            if (!$result) {
                returnError('记录不存在');
            }
            $this->model->update($id, $data);
            returnSuccess([], '更新成功');
        } else {
            // 创建操作
            $id = $this->model->create($data);
    
            returnSuccess(['id' => $id], '创建成功');
        }
    }

    /**
     * 删除节日活动
     * POST /logistics/festivalActivity/delete
     */
    public function delete()
    {
        $id = $_POST['id'] ?? 0;
        if (!$id) {
            returnError('ID参数不能为空');
        }

        // 检查记录是否存在
        $existing = $this->model->getById($id);
        if (!$existing) {
            returnError('记录不存在');
        }

        $result = $this->model->delete($id);

        if ($result) {
            returnSuccess([], '删除成功');
        } else {
            returnError('删除失败');
        }
    }

    /**
     * 更新状态
     * POST /logistics/festivalActivity/updateStatus
     */
    public function updateStatus()
    {
        $id = $_POST['id'] ?? 0;
        $status = $_POST['status'] ?? '';

        if (!$id || $status === '') {
            returnError('参数不能为空');
        }

        // 权限检查
        if (!userModel::getUserListAuth('festival_activities_update')) {
            returnError('无权限操作', 403);
        }

        // 检查记录是否存在
        $existing = $this->model->getById($id);
        if (!$existing) {
            returnError('记录不存在');
        }

        $result = $this->model->updateStatus($id, $status);

        if ($result) {
            returnSuccess([], '状态更新成功');
        } else {
            returnError('状态更新失败');
        }
    }

    /**
     * 获取当前有效的节日活动
     * GET /logistics/festivalActivity/getActiveActivities
     */
    public function getActiveActivities()
    {
        $date = $_GET['date'] ?? null;

        $activities = $this->model->getActiveActivities($date);

        // 解析JSON字段
        foreach ($activities as &$activity) {
            if (!empty($activity['sites_config'])) {
                $activity['sites_config'] = json_decode($activity['sites_config'], true);
            }
            if (!empty($activity['stock_rules'])) {
                $activity['stock_rules'] = json_decode($activity['stock_rules'], true);
            }
        }

        returnSuccess($activities);
    }

    /**
     * 根据站点获取节日活动配置
     * GET /logistics/festivalActivity/getActivitiesBySite?site=US
     */
    public function getActivitiesBySite()
    {
        $site = $_GET['site'] ?? '';
        if (empty($site)) {
            returnError('站点参数不能为空');
        }

        $date = $_GET['date'] ?? null;
        $activities = $this->model->getActivitiesBySite($site, $date);

        returnSuccess($activities);
    }
}
