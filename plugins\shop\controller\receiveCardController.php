<?php

namespace plugins\shop\controller;

use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use Exception;
use plugins\shop\models\receiveCardModel;
use Rap2hpoutre\FastExcel\FastExcel;

class receiveCardController extends baseController
{
    // 列表
    public function getList()
    {
        $paras_list = array('card_number', 'bank_name', 'receive_platform', 'card_status', 'main_receive_account_id',
            'receive_account_id', 'currency', 'use_platform', 'update_time', 'page', 'page_size');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $param['page'] = $param['page'] ?? 1;
        $param['page_size'] = $param['page_size'] ?? 10;

        $model = new receiveCardModel();
        $list = $model->getList($param);

        returnSuccess($list);
    }

    // 新增
    public static function add()
    {
        $model = new receiveCardModel();
        $paras_list = $model::$paras_list;
        $param = array_intersect_key($_POST, array_flip(array_keys($paras_list)));
        $id = $_POST['id'] ?? 0;

        try {
            $model->dataValidCheck($param, $paras_list);

            // 检查卡号唯一性
            $detail = $model->getByCardNumber($param['card_number'] ?? '', $id);
            if ($detail) {
                returnError('收款卡号已存在');
            }
            if ($id) {
                // 验证数据正确性
                $detail = $model->getById($id);
                if (!$detail) {
                    returnError('数据不存在');
                }
                $model->edit($param, $id, $detail);
                returnSuccess([], '编辑成功');
            } else {
                $model->add($param);
                returnSuccess([], '添加成功');
            }
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }


    // 详情
    public static function detail()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $model = new receiveCardModel();
        $detail = $model->getById($id);
        $detail = $model->formatItem($detail);

        if (!$detail) {
            returnError('数据不存在');
        }

        returnSuccess($detail);
    }

    // 获取日志
    public static function getLog()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $db = dbShopMysql::getInstance();
        $list = $db->table('operation_log')->where('where table_name = :table_name and table_id = :table_id',
            ['table_name' => 'receive_card', 'table_id' => $id])->order('id desc')->list();

        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        $model = new receiveCardModel();
        $maps = $model::getMaps();

        foreach ($list as &$item) {
            $item['operator_name'] = $users[$item['operator']] ?? '';
            $attach = json_decode($item['attach'], true);
            $item['before'] = $attach['before'] ?? null;
            $item['before'] = $model->formatItem($item['before'], $maps);
            $item['after'] = $attach['after'] ?? null;
            $item['after'] = $model->formatItem($item['after'], $maps);
            unset($item['attach']);
        }
        returnSuccess($list);
    }

    // 批量导入
    public function import()
    {
        $paras_list = array('excel_src');
        $request_list = ['excel_src' => '表格链接'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

        if (empty($param['excel_src'])) {
            returnError('表格链接不能为空');
        }

        $excel_url = SELF_FK . $param['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格文件不存在');
        }

        $data = (new FastExcel())->import($excel_url, function ($row) {
            // 将每一项 trim 去空格，再判断整行是否为空
            $cleaned = collect($row)->map(function ($value) {
                if (is_string($value)) {
                    return trim((string)$value);
                }
                return $value;
            });
            // 全部为空则跳过
            if ($cleaned->filter()->isEmpty()) {
                return null;
            }
            return $cleaned->toArray(); // 返回处理后的数据
        });
        $first = $data['0'];
        if (empty($first['收款卡号']) || empty($first['收款银行名称']) || empty($first['收款平台']) ||
            empty($first['主账户']) || empty($first['子账户']) || empty($first['收款币种']) ||
            empty($first['使用平台']) || !isset($first['备注'])) {
            returnError('表头错误');
        }

        $receive_account = redisCached::getReceiveAccountAll();
        $receive_account = array_column($receive_account, null, 'account_name');

        $model = new receiveCardModel();
        $paras_list = $model::$paras_list;
        $import_data = [];
        $error_data = [];

        foreach ($data as $row) {
            $error_msg = [];
            empty($row["收款卡号"]) && $error_msg[] = '收款卡号不能为空';
            // 检查卡号唯一性
            $detail = $model->getByCardNumber($row['收款卡号']);
            if ($detail) {
                $error_msg[] = '收款卡号已存在';
            }
            empty($row['收款银行名称']) && $error_msg[] = '收款银行名称不能为空';
            empty($row['收款平台']) && $error_msg[] = '收款平台不能为空';
            empty($row['主账户']) && $error_msg[] = '主账户不能为空';
            if (!isset($receive_account[$row['主账户']])) {
                $error_msg[] = '主账户不存在';
            } else {
                if ($receive_account[$row['主账户']]['pid'] != 0) {
                    $error_msg[] = $row['主账户'].'不是主账户';
                }
                $main_receive_account_id = $receive_account[$row['主账户']]['id'];
            }
            empty($row['子账户']) && $error_msg[] = '子账户不能为空';
            if (!isset($receive_account[$row['子账户']])) {
                $error_msg[] = '子账户不存在';
            } else {
                if ($receive_account[$row['子账户']]['pid'] == 0) {
                    $error_msg[] = $row['子账户'].'不是子账户';
                }
                $receive_account_id = $receive_account[$row['子账户']]['id'];
            }

            empty($row['收款币种']) && $error_msg[] = '收款币种不能为空';
            empty($row['使用平台']) && $error_msg[] = '使用平台不能为空';
            empty($row['状态']) && $error_msg[] = '状态不能为空';

            $item_data = [
                "card_number"             => $row["收款卡号"],
                "bank_name"               => $row['收款银行名称'],
                "receive_platform"        => $row['收款平台'],
                "main_receive_account_id" => $main_receive_account_id ?? null,
                "receive_account_id"      => $receive_account_id ?? null,
                "currency"                => $row['收款币种'],
                "card_status"             => $row['状态'],
                "use_platform"            => $row['使用平台'],
                "remark"                  => $row["备注"] ?? '',
            ];
            $check_row = $model->dataValidCheck($item_data, $paras_list, false);
            if (!empty($check_row['error'])) {
                $error_msg = array_merge($error_msg, $check_row['error']);
            }

            if (!empty($error_msg)) {
                $row['失败原因'] = implode(';', $error_msg);
                $error_data[] = $row;
                continue;
            }

            $import_data[] = $item_data;
            $id = $model->add($item_data, '导入');
        }

        if (count($error_data) > 0) {
            // 使用项目根目录动态构建导出文件路径
            $projectRoot = SELF_FK;
            $src = '/public/shop/temp/error/user_import_errors' . time() . rand(100, 1000) . '.xlsx';
            $exportPath = $projectRoot . $src;

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径

            returnSuccess(['error_file' => $src, 'error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $error_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $import_data], '导入成功');
        }
    }

    // 导出
    public function export()
    {
        $paras_list = array('card_number', 'bank_name', 'receive_platform', 'card_status', 'main_receive_account_id',
            'receive_account_id', 'currency', 'use_platform', 'update_time', 'ids', 'keys');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        $param['page'] = $param['page'] ?? 1;
        $param['page_size'] = $param['page_size'] ?? 10;

        $model = new receiveCardModel();
        $data = $model->getList($param, 'id desc', true);

        if (empty($data)) {
            returnError('没有可导出的数据');
        }

        $export_data = [];
        foreach ($data as $item) {
            $keys = ['收款卡号', '收款银行', '收款平台', '主账户', '子账户', '收款币种', '使用平台', '状态', '备注'];
            $sortedItem = [];
            foreach ($keys as $key) {
                if (!empty($param['keys']) && !in_array($key, $param['keys'])) continue;
                if ($key == '用途') {
                    $sortedItem[$key] = is_array($item['用途']) ? implode(';', $item['用途']) : $item['用途'];
                } else {
                    $sortedItem[$key] = $item[$key] ?? '';
                }
            }
            $export_data[] = $sortedItem;
        }

        $filePath = '/public/shop/temp/receive_card_' . date('YmdHis') . rand(1, 1000) . '.xlsx';
        (new FastExcel($export_data))->export(SELF_FK . $filePath);

        // 导出数据
        returnSuccess(['src' => $filePath], '导出成功');
    }

    // 批量编辑
    public static function editBatch()
    {
        $paras_list = array('data');
        $request_list = ['data' => '编辑数据'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

        if (count($param['data']) > 500) {
            returnError('批量编辑数据不能超过500条');
        }

        $card_numbers = array_column($param['data'], 'card_number');
        $card_numbers = array_unique($card_numbers);
        if (count($card_numbers) != count($param['data'])) {
            returnError('收款卡号不能重复');
        }

        $model = new receiveCardModel();
        $result = $model->editBatch($param['data']);

        returnSuccess($result);

    }
}
