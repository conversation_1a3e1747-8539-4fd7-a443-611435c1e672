<?php
/**
 * @author: zhangguoming
 * @Time: 2024/11/26 9:30
 */

namespace financial\form;


//等级列表查询
use core\lib\db\dbFMysql;
use financial\models\boardTableModels;
use financial\models\userModel;

class leveTableForm extends boardTableModels
{
    public static mixed $level_info = '';
    public static array $weidu_list = [];
    //清仓
    public static function clearSaleTotal($type) {
        self::getGoodsLeveLSku();
        $list = [];
        $last_list = [];
        if (!empty(self::$level_info)) {
            //数据查询
            $lx_field = ['asin','countryCode'];
            $lx_field_u = ['asin','countryCode as country_code'];
            $lx_group_by = ['asin','countryCode'];
            //领星数据
            $lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by);
            $oa_list = [];
            $asin_list = array_column($lx_list,'asin');
            if (count($lx_list)) {
                $oa_field = ['asin','country_code','custom_val','custom_id'];
                $oa_field_u = ['asin','country_code','custom_id','sum(custom_val) as total'];
                $oa_group_by = ['asin','country_code','custom_id'];
                //自定义字段数据
                $oa_list =  self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,$asin_list);
            }
            //合并oa和lx数据
            $list = self::arrangeList($lx_list,$oa_list);
            //获取上个月的信息
            if (self::$need_last_data) {
                $last_lx_list = [];
                if (count(self::$lx_last_keys)) {
                    $last_lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by,[],1);
                }
                $last_oa_list = [];
                if (count(self::$oa_last_ids)) {
                    $last_oa_list = self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,$asin_list,1);
                }
                $last_list = self::arrangeList($last_lx_list,$last_oa_list,1);
            }
        }
        //排序，分页，合并计算
        $data = self::getPageList($list,$type,$last_list);
        //转币种
        $data['list'] = self::getPriceByNewRoute($data['list'],self::$param['currency_code'],self::$all_keys,self::$currency_keys,self::$search_month);
        return ['data'=>$data];
    }
    //爆品
    public static function hotGoodsTotal($type) {
        self::getGoodsLeveLSku();
        $list = [];
        $last_list = [];
        if (!empty(self::$level_info)) {
            //数据查询
            $lx_field = ['parentAsin','countryCode'];
            $lx_field_u = ['parentAsin as p_asin','countryCode as country_code'];
            $lx_group_by = ['parentAsin','countryCode'];
            //领星数据
            $lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by);
            $oa_list = [];
            $asin_list = array_column($lx_list,'p_asin');
            if (count($lx_list)) {
                $oa_field = ['p_asin','country_code','custom_val','custom_id'];
                $oa_field_u = ['p_asin','country_code','custom_id','sum(custom_val) as total'];
                $oa_group_by = ['p_asin','country_code','custom_id'];
                //自定义字段数据
                $oa_list = self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,$asin_list);
            }
            $list = self::arrangeList($lx_list,$oa_list);
            //获取上个月的信息
            if (self::$need_last_data) {
                $last_lx_list = [];
                if (count(self::$lx_last_keys)) {
                    $last_lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by,[],1);
                }
                $last_oa_list = [];
                if (count(self::$oa_last_ids)) {
                    $last_oa_list = self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,$asin_list,1);
                }
                $last_list = self::arrangeList($last_lx_list,$last_oa_list,1);
            }
        }
        //排序，分页，合并计算
        $data = self::getPageList($list,$type,$last_list);
        //转币种
        $data['list'] = self::getPriceByNewRoute($data['list'],self::$param['currency_code'],self::$all_keys,self::$currency_keys,self::$search_month);
        return ['data'=>$data];
    }

    //获取可用的sku，产品等级相关的统计使用（如：爆款清仓）  2024-10-11 改为维度数据获取
    public static function getGoodsLeveLSku() {
        $db = dbFMysql::getInstance();
        $month = end(self::$search_month);
        if (self::$real_table == 'clear_sale') {
            $level = $db->table('goods_level_relation')
                ->where('where level_type = 2 and m_date = :m_date and is_delete = 0',['m_date'=>$month])
                ->one();
        }
        if (self::$real_table == 'hot_goods') {
            $level = $db->table('goods_level_relation')
                ->where('where level_type = 1 and m_date = :m_date and is_delete = 0',['m_date'=>$month])
                ->one();
        }
        self::$level_info = $level??'';
    }

    /** 重写sql构成 */
    //获取查询条件(领星原表,所有数据)
    public static function getSqlWhereList($field,$field_u,$group_by,$waring_array = [],$is_last_month = 0) {
        $weidu = self::$level_info;
        $group_by_1 = $field;
        if (!self::$show_all_data) {
            if (count(self::$yunying_ids) == 0 && count(self::$project_ids) == 0) {
                return [];
            }
        }
        $db = dbFMysql::getInstance();
        $param = self::$param;
        self::getGoodsSku($db);
        $sku_list = self::$sql_sku_list;
        $user_sku = self::$sql_use_sku;
        if (self::$table == 'asin') {
            self::getNewGoodsWhere($db);
        }
        //搜索的字段
        if (count(self::$lx_keys)) {
            foreach (self::$lx_keys as $w_k) {
                $field_u[] = "sum($w_k) as $w_k";
                $field[] = "sum($w_k) as $w_k";
            }
        }
        $field = array_unique($field);
        $field_u = array_unique($field_u);
        $field_u_string = implode(',',$field_u);
        $field_string = implode(',',$field);
        //获取数据
        $sql_list = [];
        //等级维度
        $weidu_key = $weidu['type'] == 1?'asin':($weidu['type'] == 2?'parentAsin':'localSku');
        foreach (self::$years as $year=>$m_date_list) {
            $m_date_list_ = array_intersect(self::$locked_month,$m_date_list);
            $db->table('goods_level_relation','a');
            if ($weidu_key != 'localSku'){
                $db->leftJoin('table_month_count_'.$year,'b',"b.countryCode = a.country_code and b.$weidu_key = a.weidu_val and b.localSku = a.sku");
            } else {
                $db->leftJoin('table_month_count_'.$year,'b',"b.countryCode = a.country_code and b.localSku = a.sku");
            }
            $db->where('a.level_type =:level_type and a.m_date=:m_date and b.is_delete = 0 and a.is_delete = 0',['level_type'=>$weidu['level_type'],'m_date'=>$weidu['m_date']])
                ->whereIn('b.reportDateMonth',$m_date_list_);
            //国家
            if (!empty($param['country_code']) && $param['country_code'] != '[]') {
                $country_codes = json_decode($param['country_code']);
                $db->whereIn('country_code',$country_codes);
            }
            //项目
            if (!empty($param['project_ids']) && $param['project_ids']!='[]') {
                $project_ids = json_decode($param['project_ids']);
                if (!self::$show_all_data) {
                    $project_ids = array_intersect(self::$project_ids,$project_ids);
                }
                $db->whereIn('project_id',$project_ids);
            }
            //权限运营+搜索
            $user_id = userModel::$qwuser_id;
            if (!empty($param['yunying_ids']) && $param['yunying_ids']!='[]') {
                if (!self::$show_all_data) {
                    $yunying_ids = array_intersect(self::$yunying_ids,json_decode($param['yunying_ids']));
                    $yunying_str = getYunyingSql($yunying_ids,self::$project_ids,$user_id);
                    $db->andWhere($yunying_str);
                } else {
                    $db->whereIn('yunying_id',json_decode($param['yunying_ids']));
                }
            } else {
                if (!self::$show_all_data) {
                    $yunying_str = getYunyingSql(self::$yunying_ids,self::$project_ids,$user_id);
                    $db->andWhere($yunying_str);
                }
            }
            //自定义搜索
            if (!empty($param['search_type']) && !empty($param['search_value'])) {
                if ($param['search_type'] == 'asin') {
                    $db->andWhere('b.asin like :asin',['asin'=>"%{$param['search_value']}%"]);
                }elseif ($param['search_type'] == 'sku') {
                    $db->andWhere('b.localSku like :sku',['sku'=>"%{$param['search_value']}%"]);
                } elseif ($param['search_type'] == 'p_asin') {
                    $db->andWhere('b.parentAsin like :parentAsin',['parentAsin'=>"%{$param['search_value']}%"]);
                }
            }
            if ($user_sku) {
                $db->whereIn('b.localSku',$sku_list);
            }
            if (self::$sql_use_new_goods) {
                $array_ = self::$sql_new_goods_data;
                if (self::$sql_use_new_goods == 2) {
                    //非新品查询
                    $db->whereInMuch(['b.asin','b.countryCode'],$array_);
                } else {
                    $db->whereInMuch(['b.asin','b.countryCode'],$array_,'not');
                }
            }
            $db->field($field_string)->groupBy($group_by_1);
            $sql_list[] = $db->getSql();
        }
        $mew_tabel = implode(' UNION ALL ',$sql_list);
        $db->tablep($mew_tabel,'un_table')
            ->field($field_u_string)
            ->groupBy($group_by);
        $report_data = $db->list();
        return $report_data;
    }
    //自定义表
    public static function getSqlWhereForOa($field,$field_u,$group_by,$table_list,$is_last_month = 0) {
        if (!self::$show_all_data) {
            if (count(self::$yunying_ids) == 0 && count(self::$project_ids) == 0) {
                return [];
            }
        }
        $param = self::$param;
        $db = dbFMysql::getInstance();
        //查询可用的sku
        $sku_list = self::$sql_sku_list;
        $user_sku = self::$sql_use_sku;
        //获取数据
        $sql_list = [];
        $field_string = implode(',',$field);
        $field_u_string = implode(',',$field_u);
        foreach (self::$years as $year=>$m_date_list) {
            $m_date_list_ = array_intersect(self::$locked_month,$m_date_list);
            //获取数据
            $db->table("custom_val_$year")
                ->whereIn('m_date',$m_date_list_)
                ->whereIn(self::$table,$table_list)
                ->whereIn('custom_id',self::$oa_column_ids);
            //国家
            if (!in_array(self::$table,self::$country_table)) {
                if (!empty($param['country_code'])) {
                    $db->andWhere('country_code=:country_code',['country_code'=>$param['country_code']]);
                }
            }
            //项目
            if (!empty($param['project_ids']) && $param['project_ids']!='[]') {
                $project_ids = json_decode($param['project_ids']);
                if (!self::$show_all_data) {
                    $project_ids = array_intersect(self::$project_ids,$project_ids);
                }
                $db->whereIn('project_id',$project_ids);
            }
            //权限运营+搜索
            $user_id = userModel::$qwuser_id;
            if (!empty($param['yunying_ids']) && $param['yunying_ids']!='[]') {
                if (!self::$show_all_data) {
                    $yunying_ids = array_intersect(self::$yunying_ids,json_decode($param['yunying_ids']));
                    $yunying_str = getYunyingSql($yunying_ids,self::$project_ids,$user_id);
                    $db->andWhere($yunying_str);
                } else {
                    $db->whereIn('yunying_id',json_decode($param['yunying_ids']));
                }
            } else {
                if (!self::$show_all_data) {
                    $yunying_str = getYunyingSql(self::$yunying_ids,self::$project_ids,$user_id);
                    $db->andWhere($yunying_str);
                }
            }
            //自定义搜索
            if (!empty($param['search_type']) && !empty($param['search_value'])) {
                if ($param['search_type'] == 'asin') {
                    $db->andWhere('asin like :asin',['asin'=>"%{$param['search_value']}%"]);
                }elseif ($param['search_type'] == 'sku') {
                    $db->andWhere('sku like :sku',['sku'=>"%{$param['search_value']}%"]);
                } elseif ($param['search_type'] == 'p_asin') {
                    $db->andWhere('p_asin like :p_asin',['p_asin'=>"%{$param['search_value']}%"]);
                }
            }
            if ($user_sku) {
                $db->whereIn('sku',$sku_list);
            }
            if (self::$sql_use_new_goods) {
                $array_ = self::$sql_new_goods_data;
                if (self::$sql_use_new_goods == 2) {
                    //非新品查询
                    $db->whereInMuch(['asin','country_code'],$array_);
                } else {
                    $db->whereInMuch(['asin','country_code'],$array_,'not');
                }
            }
            $db->field($field_string);
            $sql_list[] = $db->getSql();
        }
        $mew_tabel = implode(' UNION ALL ',$sql_list);
        $db->tablep($mew_tabel,'oa_un_table')
            ->field($field_u_string)
            ->groupBy($group_by);
        $oa_data = $db->list();
        return $oa_data;
    }
}