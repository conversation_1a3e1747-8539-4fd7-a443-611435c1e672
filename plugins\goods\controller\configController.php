<?php
/**
 * @author: zhangguoming
 * @Time: 2024/3/29 14:20
 */

namespace  plugins\goods\controller;

use plugins\goods\form\configFrom;
use plugins\goods\models\userModel;
use core\lib\db\dbMysql;

class configController
{
    public function getConfig() {
        $keys = $_GET['keys']??'';
        if ($keys) {
            $keys = json_decode($keys);
        } else {
            $keys = [];
        }
        $data = configFrom::getConfigData($keys);
        returnSuccess($data);
    }

    public function setConfig() {
        $paras_list = array('config');
        $param = arrangeParam($_POST, $paras_list);
        $config = $param['config'];
        if (empty($config)) {
            SetReturn(-1,'参数有误');
        }
        $config = json_decode($config,true);

        $db = dbMysql::getInstance();
        $config_data = $db->queryAll('select key_name,data from oa_config');
        $config_keys = array_column($config_data, 'key_name');
        $current_time = date('Y-m-d H:i:s');
        foreach ($config as $v) {
            if (in_array($v['key_name'],$config_keys)) {
                //修改
                $db->query('update oa_config set data=:data,updated_time=:updated_time where key_name = :key_name', ['key_name'=>$v['key_name'],'data'=>is_array($v['data'])?json_encode($v['data']):$v['data'],'updated_time'=>$current_time]);
            } else {
                //新增
                $insert_data = [
                    'user_id'=>userModel::$qwuser_id,
                    'key_name'=>$v['key_name'],
                    'data'=>is_array($v['data'])?json_encode($v['data']):$v['data'],
                    'created_time'=>$current_time
                ];
                $db->query('insert into oa_config (user_id,key_name,data,created_time) values (:user_id,:key_name,:data,:created_time)', $insert_data);
            }
        }
        returnSuccess('','修改成功');
    }
}