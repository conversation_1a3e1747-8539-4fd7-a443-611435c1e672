<?php
/**
 * @author: zhangguoming
 * @Time: 2024/8/5 10:42
 */

namespace financial\models;

use core\lib\db\dbFMysql;

class goodsCategoryModel
{
    public static array $goods_cate_list = [];
    //根据分类id获取产品的sku(不可循环调用）
    public static function getSkuByCateId(array $cat_ids) {
        $db = dbFMysql::getInstance();
        if (count($cat_ids)) {
            $goods_category = $db->table('goods_category')
                ->whereIn('id',$cat_ids)
                ->field('cid')
                ->list();
            $sku_list = [];
            if (count($goods_category)) {
                $goods_list = $db->table('goods')
                    ->whereIn('cid',array_column($goods_category,'cid'))
                    ->field('sku')
                    ->list();
                $sku_list = array_column($goods_list,'sku');
            }
        } else {
            $goods_list = $db->table('goods')
                ->field('sku')
                ->list();
            $sku_list = array_column($goods_list,'sku');
        }
        return $sku_list;
    }
    /**
     * @param array $cate_ids
     * @return array
     * @throws \core\lib\ExceptionError  不查表获取分类的全面
     * $type  0递归查，1不递归
     */
    public static function getGoodsCate(array $cate_ids,$type=0) {
        if (!count($cate_ids)) {
            return [];
        }
        if (count(self::$goods_cate_list) == 0) {
            $db = dbFMysql::getInstance();
            $all_cate = $db->queryAll('select * from oa_f_goods_category where is_delete = 0');
        } else {
            $all_cate = self::$goods_cate_list;
        }
        $data = [];
        foreach ($cate_ids as $v) {
            foreach ($all_cate as $v1) {
                if ($v1['cid'] == $v) {
                    $data[] = $v1;
                }
            }
        }
        $result = [];
        foreach ($data as $v) {
            //递归获取名称
            if (!$type) {
                $name_array = self::getCateAllName($all_cate,$v['parent_cid'],[$v['title']]);
            } else {
                $name_array = [$v['title']];
            }
            $result[$v['cid']] = [
                'cid'=>$v['cid'],
                'parent_cid'=>$v['parent_cid'],
                'name'=>implode('/',array_reverse($name_array))
            ];
        }
        return $result;
    }
    //获取完整名
    private static function getCateAllName($all_cate,$pid,$name_list){
        if ($pid>0) {
            foreach ($all_cate as $cate) {
                if ($cate['cid'] == $pid) {
                    $name_list[] = $cate['title'];
                    if ($cate['parent_cid'] > 0) {
                        $name_list = self::getCateAllName($all_cate,$cate['parent_cid'],$name_list);
                    }
                }
            }
        }
        return $name_list;
    }
}