<?php

namespace plugins\shop\controller;

use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use Exception;
use plugins\shop\models\trademarkModel;
use Rap2hpoutre\FastExcel\FastExcel;
use Throwable;

class trademarkController extends baseController
{
    private $model;

    public function __construct()
    {
        parent::__construct();
        $this->model = new trademarkModel();
    }

    /**
     * 获取商标列表
     */
    public function getList()
    {
        $paras_list = [
            'brand_name', 'trademark_holder', 'transfer_lawyer', 'trademark_holder_pre',
            'transfer_provider', 'service_provider', 'certificate_number', 'original_storage',
            'dep_id', 'user_id', 'trademark_type',
            'country', 'category', 'domain', 'email', 'use_status',
            'register_date', 'transfer_date',
            'certificate_date', 'earliest_declaration_date',
            'record_date', 'validity_period',
            'bind_date', 'page', 'page_size'];
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $param['page'] = $param['page'] ?? 1;
        $param['page_size'] = $param['page_size'] ?? 10;

        $result = $this->model->getList($param);
        returnSuccess($result);
    }

    /**
     * 新增商标
     */
    public function add()
    {

        $param = array_intersect_key($_POST, array_flip(array_keys($this->model::$paras_list)));
        $id = $_POST['id'] ?? 0;

        try {
            $model = new trademarkModel();
            $params = $model->dataValidCheck($param, trademarkModel::$paras_list);

            // 唯一性校验
            $brand_name = $model->getByBrandName($param['brand_name'], $id);
            if ($brand_name) {
                throw new Exception('品牌名已存在');
            }

            if ($id) {
                // 验证数据正确性
                $detail = $model->getById($id);
                if (!$detail) {
                    returnError('数据不存在');
                }
                $model->edit($param, $id, $detail);
                returnSuccess([], '编辑成功');
            } else {
                $result = $model->add($params);
                returnSuccess(['id' => $result], '新增成功');
            }
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 获取商标详情
     */
    public function getDetail()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        try {
            $detail = $this->model->getDetail($id);
            if (!$detail) {
                returnError('数据不存在');
            }
            returnSuccess($detail);
        } catch (Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 批量导入
     */
    public function import()
    {
        $paras_list = array('excel_src');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        $excel_url = SELF_FK . $param['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格不存在');
        }

        $data = (new FastExcel())->import($excel_url, function ($row) {
            // 将每一项 trim 去空格，再判断整行是否为空
            $cleaned = collect($row)->map(function ($value) {
                if (is_string($value)) {
                    return trim((string)$value);
                }
                return $value;
            });
            // 全部为空则跳过
            if ($cleaned->filter()->isEmpty()) {
                return null;
            }
            return $cleaned->toArray(); // 返回处理后的数据
        });

        // 判断表头
        $first_user = $data[0];
        if (empty($first_user['注册（购买）日期']) || empty($first_user['类型（自注册、外购）']) || empty($first_user['品牌名']) ||
            empty($first_user['国家']) || empty($first_user['类目']) || !isset($first_user['转让律师']) ||
            empty($first_user['商标持有人']) || !isset($first_user['商标原持有人']) || !isset($first_user['商标转让日期']) ||
            !isset($first_user['商标转让服务商']) || empty($first_user['域名（多个域名的,隔开）']) || !isset($first_user['价格']) ||
            !isset($first_user['币种']) || empty($first_user['服务商']) || empty($first_user['下证日期']) ||
            empty($first_user['证书号/注册号']) || empty($first_user['原件保管地']) || empty($first_user['商标有效开始日期']) ||
            empty($first_user['商标有效结束日期']) || empty($first_user['最早宣誓日期']) || !isset($first_user['备案店铺']) ||
            !isset($first_user['备案日期']) || !isset($first_user['备注'])) {
            returnError('表头不正确');
        }

        $model = new trademarkModel();
        $paras_list = $model::$paras_list;
        unset($paras_list['email_id']);
        unset($paras_list['transfer_lawyer']);
        unset($paras_list['trademark_holder_pre']);
        unset($paras_list['transfer_date']);
        unset($paras_list['transfer_provider']);
        unset($paras_list['price']);
        unset($paras_list['currency']);
        unset($paras_list['record_date']);
        unset($paras_list['use_status']);

        $import_data = [];
        $error_data = [];

        $domain_map = redisCached::getDomain();
        $domain_map = array_column($domain_map, 'id', 'domain');

        $shop_map = redisCached::getShop();
        $shop_map = array_column($shop_map, 'id', 'shop_number');

        foreach ($data as $row) {
            $error_msg = [];

            // 必填字段验证
            empty($row['注册（购买）日期']) && $error_msg[] = '注册（购买）日期不能为空';
            empty($row['类型（自注册、外购）']) && $error_msg[] = '类型不能为空';
            empty($row['品牌名']) && $error_msg[] = '品牌名不能为空';
            empty($row['国家']) && $error_msg[] = '国家不能为空';
            empty($row['类目']) && $error_msg[] = '类目不能为空';
            empty($row['商标持有人']) && $error_msg[] = '商标持有人不能为空';
            empty($row['域名（多个域名的,隔开）']) && $error_msg[] = '域名不能为空';
            empty($row['服务商']) && $error_msg[] = '服务商不能为空';
            empty($row['下证日期']) && $error_msg[] = '下证日期不能为空';
            empty($row['证书号/注册号']) && $error_msg[] = '证书号/注册号不能为空';
            empty($row['原件保管地']) && $error_msg[] = '原件保管地不能为空';
            empty($row['商标有效开始日期']) && $error_msg[] = '商标有效开始日期不能为空';
            empty($row['商标有效结束日期']) && $error_msg[] = '商标有效结束日期不能为空';
            empty($row['最早宣誓日期']) && $error_msg[] = '最早宣誓日期不能为空';

            // 日期格式验证
            $register_date = null;
            $transfer_date = null;
            $certificate_date = null;
            $validity_start_date = null;
            $validity_end_date = null;
            $earliest_declaration_date = null;
            $record_date = null;

            try {
                $register_date = $row['注册（购买）日期']->format('Y-m-d');
                if (empty($row['注册（购买）日期']) || strtotime($register_date) === false) {
                    $error_msg[] = '注册（购买）日期格式错误';
                }
            } catch (Throwable $e) {
                $error_msg[] = '注册（购买）日期格式错误';
            }


            try {
                if (!empty($row['商标转让日期'])) {
                    $transfer_date = $row['商标转让日期']->format('Y-m-d');
                    if (strtotime($transfer_date) === false) {
                        $error_msg[] = '商标转让日期格式错误';
                    }
                }
            } catch (Throwable $e) {
                $error_msg[] = '商标转让日期格式错误';
            }

            try {
                if (!empty($row['下证日期'])) {
                    $certificate_date = $row['下证日期']->format('Y-m-d');
                    if (strtotime($certificate_date) === false) {
                        $error_msg[] = '下证日期格式错误';
                    }
                } else {
                    $error_msg[] = '下证日期不能为空';
                }
            } catch (Throwable $e) {
                $error_msg[] = '下证日期格式错误';
            }

            try {
                if (!empty($row['商标有效开始日期'])) {
                    $validity_start_date = $row['商标有效开始日期']->format('Y-m-d');
                    if (strtotime($validity_start_date) === false) {
                        $error_msg[] = '商标有效开始日期格式错误';
                    }
                } else {
                    $error_msg[] = '商标有效开始日期不能为空';
                }
            } catch (Throwable $e) {
                $error_msg[] = '商标有效开始日期格式错误';
            }

            try {
                if (!empty($row['商标有效结束日期'])) {
                    $validity_end_date = $row['商标有效结束日期']->format('Y-m-d');
                    if (strtotime($validity_end_date) === false) {
                        $error_msg[] = '商标有效结束日期格式错误';
                    }
                } else {
                    $error_msg[] = '商标有效结束日期不能为空';
                }
            } catch (Throwable $e) {
                $error_msg[] = '商标有效结束日期格式错误';
            }

            try {
                if (!empty($row['最早宣誓日期'])) {
                    $earliest_declaration_date = $row['最早宣誓日期']->format('Y-m-d');
                    if (strtotime($earliest_declaration_date) === false) {
                        $error_msg[] = '最早宣誓日期格式错误';
                    }
                } else {
                    $error_msg[] = '最早宣誓日期不能为空';
                }
            } catch (Throwable $e) {
                $error_msg[] = '最早宣誓日期格式错误';
            }

            try {
                if (!empty($row['备案日期'])) {
                    $record_date = $row['备案日期']->format('Y-m-d');
                    if (strtotime($record_date) === false) {
                        $error_msg[] = '备案日期格式错误';
                    }
                }
            } catch (Throwable $e) {
                $error_msg[] = '备案日期格式错误';
            }

            // 处理域名
            $domains = [];
            if (!empty($row['域名（多个域名的,隔开）'])) {
                $domain_list = explode(';', $row['域名（多个域名的,隔开）']);
                foreach ($domain_list as $domain) {
                    $domain = trim($domain);
                    if (!empty($domain) && isset($domain_map[$domain])) {
                        $domains[] = $domain_map[$domain];
                    }
                }
            }

            // 处理店铺
            $shop_id = [];
            if (!empty($row['备案店铺'])) {
                $shop_list = explode(';', $row['备案店铺']);
                foreach ($shop_list as $shop) {
                    $shop = trim($shop);
                    if (!empty($shop) && isset($shop_map[$shop])) {
                        $shop_id[] = $shop_map[$shop];
                    }
                }
            }

            // 构造商标数据
            $item_data = [
                'brand_name'                => $row['品牌名'],
                'country'                   => $row['国家'],
                'category'                  => $row['类目'],
                'transfer_lawyer'           => $row['转让律师'] ?? '',
                'trademark_holder'          => $row['商标持有人'],
                'trademark_holder_pre'      => $row['商标原持有人'] ?? '',
                'transfer_date'             => $transfer_date,
                'transfer_provider'         => $row['商标转让服务商'] ?? '',
                'domain'                    => $domains ?? null,
                'price'                     => $row['价格'] ?? 0,
                'currency'                  => $row['币种'] ?? '',
                'service_provider'          => $row['服务商'],
                'certificate_date'          => $certificate_date,
                'certificate_number'        => $row['证书号/注册号'],
                'original_storage'          => $row['原件保管地'],
                'validity_period'           => ($validity_end_date && $validity_start_date) ? [$validity_start_date, $validity_end_date] : null,
                'earliest_declaration_date' => $earliest_declaration_date,
                'record_shop'               => $shop_id ?? null,
                'record_date'               => $record_date,
                'remark'                    => $row['备注'] ?? '',
                'trademark_type'            => $row['类型（自注册、外购）'],
                'register_date'             => $register_date,
            ];

            // 验证数据正确性
            try {
                $check_row = $model->dataValidCheck($item_data, $paras_list, false);
                if (!empty($check_row['error'])) {
                    $error_msg = array_merge($error_msg, $check_row['error']);
                }

                // 检查品牌名唯一性
                $exists = $model->getByBrandName($row['品牌名']);
                if ($exists) {
                    $error_msg[] = '品牌名已存在';
                }
            } catch (Exception $e) {
                $error_msg[] = $e->getMessage();
            }

            if (!empty($error_msg)) {
                $row['失败原因'] = implode(';', $error_msg);
                $error_data[] = $row;
                continue;
            }

            // 数据正确，添加到导入数据中
            $import_data[] = $item_data;
            $id = $model->add($item_data, '导入');
        }


        if (count($error_data) > 0) {
            // 使用项目根目录动态构建导出文件路径
            $src = '/public/shop/temp/error/user_import_errors' . time() . rand(100, 1000) . '.xlsx';
            $exportPath = SELF_FK . $src;

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径

            returnSuccess(['error_file' => $src, 'error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $error_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => count($error_data), 'success_count' => count($import_data), 'data' => $import_data], '导入成功');
        }

    }

    /**
     * 导出
     */
    public function export()
    {
        $paras_list = ['brand_name', 'trademark_holder', 'transfer_lawyer', 'trademark_holder_pre',
            'transfer_provider', 'service_provider', 'certificate_number', 'original_storage',
            'dep_id', 'user_id', 'trademark_type',
            'country', 'category', 'domain', 'email', 'use_status',
            'register_date', 'transfer_date',
            'certificate_date', 'earliest_declaration_date',
            'record_date', 'validity_period',
            'bind_date', 'ids', 'keys'];
        $param = array_intersect_key($_POST, array_flip($paras_list));

        $data = $this->model->getList($param, 'id desc', true);

        if (empty($data)) {
            returnError('没有可导出的数据');
        }

        $export_data = [];
        foreach ($data as $item) {
            $keys = [
                '注册 / 购买日期', '品牌名', '国家', '类目', '商标转让律师', '商标持有人', '商标转让前持有人',
                '商标转让日期', '商标转让服务商', '域名', '价格', '币种', '服务商', '下证日期',
                '证书号 / 注册号', '原件保管地', '商标有效期(开始日期)', '商标有效期(结束日期)',
                '最早宣誓日期', '备案店铺', '备案日期', '备注'
            ];
            $sortedItem = [];
            foreach ($keys as $key) {
                if (!empty($param['keys']) && !in_array($key, $param['keys'])) continue;
                if ($key == '域名') {
                    if (!isset($item['域名'])) {
                        $sortedItem[$key] = '';
                    } else {
                        $sortedItem[$key] = implode(';', array_column($item['域名'], 'domain'));
                    }
                } elseif ($key == '备案店铺') {
                    if (!isset($item['备案店铺'])) {
                        $sortedItem[$key] = '';
                    } else {
                        $sortedItem[$key] = implode(';', array_column($item['备案店铺'], 'shop_number'));
                    }
                } elseif ($key == '商标有效期(开始日期)') {
                    $sortedItem[$key] = $item['商标有效期'][0] ?? '';
                } elseif ($key == '商标有效期(结束日期)') {
                    $sortedItem[$key] = $item['商标有效期'][1] ?? '';
                } else {
                    $sortedItem[$key] = $item[$key] ?? '';
                }
            }
            $export_data[] = $sortedItem;
        }

        $filePath = '/public/shop/temp/trademark_export_' . date('YmdHis') . rand(1, 1000) . '.xlsx';
        (new FastExcel($export_data))->export(SELF_FK . $filePath);

        // 导出数据
        returnSuccess(['src' => $filePath], '导出成功');
    }

    public function editBatch()
    {
        $paras_list = array('data');
        $request_list = ['data' => '编辑数据'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

        if (count($param['data']) > 500) {
            returnError('批量编辑数据不能超过500条');
        }

        $brand_names = array_column($param['data'], 'brand_name');
        $brand_names = array_unique($brand_names);
        if (count($brand_names) != count($param['data'])) {
            returnError('批量编辑数据不能有重复的品牌名');
        }

        $model = new trademarkModel();
        $result = $model->editBatch($param['data']);

        returnSuccess($result);
    }


    // 获取日志
    public static function getLog()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $db = dbShopMysql::getInstance();
        $list = $db->table('operation_log')->where('where table_name = :table_name and table_id = :table_id',
            ['table_name' => 'trademark', 'table_id' => $id])->order('id desc')->list();

        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        $model = new trademarkModel();

        foreach ($list as &$item) {
            $item['operator_name'] = $users[$item['operator']] ?? '';
            $attach = json_decode($item['attach'], true);
            $item['before'] = $attach['before'] ?? null;
            $item['before'] = $model->formatItem($item['before']);
            $item['after'] = $attach['after'] ?? null;
            $item['after'] = $model->formatItem($item['after']);
            unset($item['attach']);
        }
        returnSuccess($list);
    }
}
