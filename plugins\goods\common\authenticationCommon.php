<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/22 9:28
 */

namespace plugins\goods\common;

use plugins\goods\form\configFrom;
use plugins\goods\models\userModel;
use core\lib\db\dbMysql;

class  authenticationCommon
{
    public static function projectManageVatify($manage_info)
    {
        $user_wid = array_column($manage_info,'wid');
        $user_name = implode(',',array_column($manage_info,'wname'));
        if (!in_array(userModel::$wid, $user_wid)) {
            SetReturn(-1, '非负责人无权操作，负责人【'.$user_name.'】');
        }
    }

    public static function authEditGoodsvarify($manage_info) {
        $user_wid = array_column($manage_info,'wid');
        if (!(userModel::$is_super || userModel::isManage() || in_array(userModel::$wid, $user_wid))) {
            SetReturn(-1, '您无权修改产品信息');
        }
    }

    /**
     * @return void 验证待办人信息
     * @throws \core\lib\ExceptionError
     */
    public static function authGoodsMattersVatify($goods_id, $type)
    {
        $db = dbMysql::getInstance();
        $matters = $db->query('select id,qwuser_id from oa_goods_matters where goods_id=:goods_id and type=:type',['goods_id'=>$goods_id,'type'=>$type]);
        if (!$matters) {
            SetReturn(-1, '未获取到待办事项');
        }
        $msg = '';
        switch ($type) {
            case 1:
                $msg = '您无权录入该产品英文名';break;
            case 2:
                $msg = '您无权录入该产品中文名';break;
            default:
                $msg = '您无权操作改步骤';break;
        }
        if (userModel::$qwuser_id != $matters['qwuser_id']) {
            SetReturn(-1, $msg);
        }
    }


    /**
     * @param $event_data 当前的事件
     * @param $event_type 处理的事件类型
     * @return void 验证当前事件是否可以处理
     */
    public static function verifyEventAuth($event_data, $event_type) {
        //验证事件类型
        if ($event_type != $event_data['event_type']) {
            SetReturn(-1,'处理的事件类型不匹配');
        }
        //验证负责人
        $manage_info = json_decode($event_data['manage_info'],true);
        self::projectManageVatify($manage_info);
    }


    public static function authImgRequrest($type) {
        //生成待办
        if ($type == 1) {
            $check_info = configFrom::getConfigByName('images_check_user');
            $check_info = json_decode($check_info,true);
            $user_wid = array_column($check_info,'wid');
            if (!in_array(userModel::$wid, $user_wid)) {
                SetReturn(-1, '您无权审核该图片');
            }
        }

    }
    //说明书审核验证
    public static function authFsRequrest() {
        //生成待办
        $check_info = configFrom::getConfigByName('fs_check_user');
        $check_info = json_decode($check_info,true);
        $user_wid = array_column($check_info,'wid');
        if (!in_array(userModel::$wid, $user_wid)) {
            SetReturn(-1, '您无权审核说明书内容');
        }
    }
    public static function authFsOperatorRequrest(string $operator,string $operator_pass_wid) {
        //生成待办
        $wids = array_column(json_decode($operator,true),'wid');
        if (!in_array(userModel::$wid,$wids)) {
            SetReturn(-1, '非该产品运营人员不可审核说明书');
        }
        $pass_wid = empty($operator_pass_wid)?[]:json_decode($operator_pass_wid);
        if (in_array(userModel::$wid,$pass_wid)) {
            SetReturn(-1, '您已审核，切勿重复操作');
        }
    }
}