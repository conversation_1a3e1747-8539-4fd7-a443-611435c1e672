<?php

namespace financial\models;

use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Rap2hpoutre\FastExcel\FastExcel;

class originaldataModel
{
    public static array $column_list;//要显示的列
    public static array $ColumnList;//规则列表
    public static array $yprojects = [];//运营名
    public static array $yunyings = [];//运营
    public static array $suppliers = [];//供应商名
    public static array $seller = [];//店铺名
    public static array $countryCode = [];//国家列表
    public static array $pm = [];//品名列表
    //获取国家列表
    public static function countryCode(array $country_codes)
    {
        if (!count($country_codes)) {
            return;
        }
        // 获取数据库实例
        $db = dbFMysql::getInstance();
        // 初始化结果数组
        // 执行查询
        $countries = $db->table('market')
            ->whereIn('code', $country_codes)
            ->field('code, country')
            ->list();
        $country_ = [];
        foreach ($countries as $v) {
            $country_[$v['code']] = $v;
        }
        self::$countryCode = $country_;
    }
    //店铺处理
    public static function seller(array $sids)
    {
        if (!count($sids)) {
            return;
        }
        // 获取数据库实例
        $db = dbFMysql::getInstance();
        // 初始化结果数组
        // 执行查询
        $sellers = $db->table('seller')
            ->whereIn('sid', $sids)
            ->field('sid, real_name')
            ->list();
        $seller_ = [];
        foreach ($sellers as $v) {
            $seller_[$v['sid']] = $v;
        }
        self::$seller = $seller_;
    }
    //供应商处理
    public static function supplier(array $supplier_ids)
    {
        if (!count($supplier_ids)) {
            return;
        }
        // 获取数据库实例
        $db = dbFMysql::getInstance();
        // 初始化结果数组
        $suppliers = $db->table('supplier')
            ->whereIn('id', $supplier_ids)
            ->field('id, supplier_name')
            ->list();
        $supplier_ = [];
        foreach ($suppliers as $v) {
            $supplier_[$v['id']] = $v;
        }
        self::$suppliers = $supplier_;
    }
    //品名处理
    public static function getProductName(array $skus)
    {
        if (!count($skus)) {
            return;
        }
        // 获取数据库实例
        $db = dbFMysql::getInstance();
        // 执行查询
        $products = $db->table('goods','a')
            ->leftJoin('goods_detail','b','b.sku = a.sku')
            ->whereIn('a.sku', $skus)
            ->field('a.sku,a.product_name,b.model')
            ->list();
        $products_ = [];
        foreach ($products as $p) {
            $products_[$p['sku']] = $p;
        }
        self::$pm = $products_;
    }
    //运营部门处理
    public static function yunying(array $project_ids)
    {
        if (!count($project_ids)) {
            return;
        }
        // 获取数据库实例
        $db = dbFMysql::getInstance();
        //运营组
        $projects3 = $db->table('project')
            ->whereIn('id', $project_ids)
            ->field('id,p_id,project_name')
            ->list();
        //项目二级
        $projects2 = $db->table('project')
            ->whereIn('id',array_unique(array_column($projects3,'p_id')))
            ->field('id,p_id,project_name')
            ->list();
        //项目一级
        $projects1 = $db->table('project')
            ->whereIn('id',array_unique(array_column($projects2,'p_id')))
            ->field('id,p_id,project_name')
            ->list();
        $res_data = [];
        //找到23级的关系
        foreach ($projects2 as $v) {
            foreach ($projects1 as $v1) {
                if ($v['p_id'] == $v1['id']) {
                    $res_data[$v['id']] = $v1['project_name'].'/'.$v['project_name'];
                    break;
                }
            }
        }
        $res_project = [];
        //招一二级的概关系
        foreach ($projects3 as $v) {
            foreach ($res_data as $pid => $v1) {
                if ($v['p_id'] == $pid) {
                    $res_project[$v['id']] = $v1.'/'.$v['project_name'];
                    break;
                }
            }
        }
        self::$yprojects = $res_project;
    }
    // 运营人员处理
    public static function yunyingid(array $yunying_ids)
    {
        if (!count($yunying_ids)) {
            return;
        }
        // 获取数据库实例
        $db = dbMysql::getInstance();
        $yunyings = $db->table('qwuser')
            ->whereIn('id', $yunying_ids)
            ->field('id, wname')
            ->list();
        $yunying_ = [];
        foreach ($yunyings as $v) {
            $yunying_[$v['id']] = $v;
        }
        self::$yunyings = $yunying_;
    }
    //获取规则列表
    public static function getColumnList()
    {
        // 获取字段列表
        $db = dbFMysql::getInstance();

        self::$ColumnList = $db->table('column')
            ->where('where custom_id = 0 and show_type > 0')
            ->field('key_name,column_name')
            ->list();
    }
}