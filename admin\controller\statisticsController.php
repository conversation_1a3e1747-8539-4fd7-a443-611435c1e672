<?php
/**
 * @author: zhangguoming
 * @Time: 2025/3/24 16:33
 */

namespace admin\controller;

use admin\form\statisticsForm;
use core\lib\db\dbMysql;
use plugins\goods\models\userModel;

class statisticsController
{
    //事件统计
    public function getCountList(){
        $data = [
            //待办
            'wait_agent_count'=>array_sum(statisticsForm::getWaitAgentNum()),
            //已办
            'had_agent_count'=>array_sum(statisticsForm::getHadAgentNum()),
            //我的申请
            'applay_all_count'=>array_sum(statisticsForm::getApplayNum()),
            'applay_incomplete_count'=>array_sum(statisticsForm::getApplaycompleteNum()),
        ];
        returnSuccess($data);
    }
}