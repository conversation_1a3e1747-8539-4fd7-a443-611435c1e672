<?php

namespace plugins\shop\models;

use core\lib\redisCached;
use Exception;

class shopAppealModel extends baseModel
{
    //表名
    public string $table = 'shop_appeal';

    // 状态常量
    const STATUS_PENDING = '1';
    const STATUS_PROCESSING = '2';
    const STATUS_SUCCESS = '3';
    const STATUS_CANCELLED = '4';
    const STATUS_FAILED = '5';
    const STATUS_FINISH = '6';

    const COMPLAINT_TYPE_SELF = '自申诉';
    const COMPLAINT_TYPE_SUPPLIER = '服务商申诉';

    public static array $paras_list = [
        'shop_id'                => '店铺',
        'issue_type'             => '问题类型',
        'warning_screenshot'     => '警告截图',
        'performance_screenshot' => '业绩截图',
        'reason_analysis'        => '原因分析',
        'shop_status'            => '店铺状态',
        'frozen_funds'           => '冻结资金',
        'currency'               => '冻结资金币种',
        'inventory'              => '库存',
        'complaint_type'         => '申诉类型',
        'reminder_interval'      => '间隔提醒',
    ];

    public static array $json_keys = [
        'warning_screenshot',
        'performance_screenshot',
        'appeal_file'
    ];

    // 数据校验
    public function dataValidCheck(array $data, array $param_list = [], $is_throw = true, array $error = []): array
    {
        return parent::dataValidCheck($data, $param_list, $is_throw, $error);
    }

    public static function getMaps() {
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');
        $deps = redisCached::getDepartment();
        $deps = array_column($deps, 'dep_name', 'id');
        return [
            'users' => $users,
            'deps' => $deps,
        ];
    }

    // 格式化单条记录
    public function formatItem($item, $maps = [])
    {
        $users = $maps['users'] ?? [];
        if (empty($maps['users'])) {
            $users = redisCached::getUserInfo();
            $users = array_column($users, 'user_name', 'user_id');
        }
        $deps = $maps['deps'] ?? [];
        if (empty($deps)) {
            $deps = redisCached::getDepartment();
            $deps = array_column($deps, 'name', 'id');
        }
        $maps = [
            ['name' => 'user_name', 'maps' => $users, 'key' => 'user_id'],
            ['name' => 'follower_name', 'maps' => $users, 'key' => 'follower'],
            ['name' => 'operator_name', 'maps' => $users, 'key' => 'operator'],
            ['name' => 'dep_name', 'maps' => $deps, 'key' => 'dep_id'],

        ];
        return parent::formatItem($item, $maps);
    }

    /**
     * 获取申诉列表
     */
    public function getList($param, $order = 'id desc', $is_export = false)
    {
        $db = $this->db->table($this->table, 'sa')
            ->field('sa.*, s.shop_number, s.company_id, s.dep_id, c.company_name, u.wname as follower_name')
            ->leftJoin('shop','s', 's.id = sa.shop_id')
            ->leftJoin('company', 'c', 's.company_id = c.id')
            ->leftJoinOut('db', 'qwuser', 'u', 'sa.follower = u.id')
            ->where('1=1');

        // 添加查询条件
        if (isset($param['shop_id']) && !empty($param['shop_id'])) {
            $db->andWhere('s.id = :shop_id', ['shop_id' => $param['shop_id']]);
        }
        if (isset($param['shop_number']) && !empty($param['shop_number'])) {
            $db->andWhere('s.shop_number = :shop_number', ['shop_number' => $param['shop_number']]);
        }

        if (isset($param['dep_id']) && !empty($param['dep_id']) && is_array($param['dep_id'])) {
            $db->whereIn('s.dep_id', $param['dep_id']);
        }
        if (isset($param['company_name']) && !empty($param['company_name'])) {
            $db->andWhere('c.company_name like :company_name', ['company_name' => '%' . $param['company_name'] . '%']);
        }
        if (isset($param['issue_type']) && !empty($param['issue_type']) && is_array($param['issue_type'])) {
            $db->whereIn('sa.issue_type', $param['issue_type']);
        }
        if (isset($param['status'])) {
            $db->andWhere('sa.status = :status', ['status' => $param['status']]);
        }
        if (isset($param['shop_status'])) {
            $db->andWhere('sa.shop_status = :shop_status', ['shop_status' => $param['shop_status']]);
        }
        if (isset($param['complaint_type']) && !empty($param['complaint_type']) && is_array($param['complaint_type'])) {
            $db->whereIn('sa.complaint_type', $param['complaint_type']);
        }
        if (isset($param['follower_name']) && !empty($param['follower_name'])) {
            $db->andWhere('u.wname like :follower_name', ['follower_name' => '%' . $param['follower_name'] . '%']);
        }

        $db->order($order);

        if (!$is_export && !empty($param['page']) && !empty($param['page_size'])) {
            $list = $this->db->pages($param['page'], $param['page_size']);
            $maps = self::getMaps();
            foreach ($list['list'] as &$item) {
                $item = $this->formatItem($item, $maps);
            }
            return $list;
        } else {
            $list = $db->list();
            $maps = self::getMaps();
            if ($is_export) {
                $result = [];
                foreach ($list as $item) {
                    $item = $this->formatItem($item, $maps);
                    $result[] = self::changeToCnKey($item);
                }
                return $result;
            } else {
                foreach ($list as &$item) {
                    $item = $this->formatItem($item, $maps);
                }
            }
            return $list;
        }
    }

    // 新增
    public function add($data, $type = '新增')
    {
        if ($data['complaint_type'] == self::COMPLAINT_TYPE_SELF) {
            $data['status'] = self::STATUS_PROCESSING;
            $data['follower'] = userModel::$qwuser_id ?? 0;
        }
        // 申诉人
        $data['user_id'] = userModel::$qwuser_id ?? 0;

        return parent::add($data, $type);
    }

    /**
     * 撤销申请
     */
    public function cancel($id)
    {
        $appeal = $this->getById($id);
        if (!$appeal) {
            throw new Exception('申诉记录不存在');
        }
        if (!in_array($appeal['status'] , [self::STATUS_PENDING, self::STATUS_PROCESSING])) {
            throw new Exception('该状态下不能撤销申请');
        }
        parent::edit(['status' => self::STATUS_CANCELLED], $id, $appeal, '撤销申请');

    }

    /**
     * 配置跟进人
     */
    public function assignFollower($id, $follower_id, $remark)
    {
        $appeal = $this->getDetail($id);
        if (!$appeal) {
            throw new Exception('申诉记录不存在');
        }

        $type = '配置跟进人';
        if ($appeal['follower'] == $follower_id) {
            throw new Exception('跟进人不能和当前跟进人相同');
        }
        if (!empty($appeal['follower'])) {
            $type = '重新配置跟进人';
        }

        if (!in_array($appeal['status'], [self::STATUS_PENDING, self::STATUS_PROCESSING])) {
            throw new Exception('该状态下不能配置跟进人');
        }
        if ($appeal['complaint_type'] != self::COMPLAINT_TYPE_SUPPLIER) {
            throw new Exception('非服务商申诉，不能配置跟进人');
        }

       parent::edit([
           'status' => self::STATUS_PROCESSING,
           'follower' => $follower_id
       ], $id, $appeal, $type, $remark);

    }

    /**
     * 跟进
     */
    public function follow($id, $param)
    {
        $old_data = $this->getById($id);
        if (!$old_data) {
            throw new Exception('数据不存在');
        }

        if ($old_data['status'] != self::STATUS_PROCESSING) {
            throw new Exception('该状态下不能跟进');
        }

        $old_data['appeal_file'] = json_decode($old_data['appeal_file'], true) ?? [];
        $updateData = [];
        $log = [];
        if (isset($param['appeal_file']) && !empty($param['appeal_file'])) {
            $appeal_file = array_merge($old_data['appeal_file'], $param['appeal_file']);
            $updateData['appeal_file'] = $appeal_file;
            $log['appeal_file'] = $param['appeal_file'];
        }

        if ($param['is_end']) {
            $status = $param['appeal_result'] == 1 ? self::STATUS_SUCCESS : self::STATUS_FAILED;
            $updateData['status'] = $status;
        }



        if (!empty($updateData)) {
            parent::edit($updateData, $id, $old_data, '跟进', $param['remark'], $param['appeal_result'] ?? '', $log);
        } else {
            // 仅记录日志
            $this->saveDataLog($this->table, $id, [], [], "跟进",$param['remark'], $param['appeal_result'] ?? '', $log);
        }


    }

    /**
     * 处理申诉
     */
    public function process($id, $param)
    {
        $old_data = $this->getById($id);
        if (!$old_data) {
            throw new Exception('数据不存在');
        }

        if ($old_data['status'] != self::STATUS_SUCCESS) {
            throw new Exception('该状态下不能处理');
        }

        $updateData = [
            'appeal_supplier' => $param['appeal_supplier'],
            'appeal_fee' => $param['appeal_fee'],
            'pay_date' => $param['pay_date'] ?? null,
            'phone_number' => $param['phone_number'] ?? null,
            'status' => self::STATUS_FINISH,
        ];
        $log = [];
        $old_data['appeal_file'] = json_decode($old_data['appeal_file'], true) ?? [];
        if (isset($param['appeal_file']) && !empty($param['appeal_file'])) {
            $appeal_file = array_merge($old_data['appeal_file'], $param['appeal_file']);
            $updateData['appeal_file'] = $appeal_file;
            $log['appeal_file'] = $param['appeal_file'];
        }

        parent::edit($updateData, $id, $old_data, '处理', $param['remark'] ?? '', '', $log);
    }


    /**
     * 获取申诉详情
     */
    public function getDetail($id)
    {
        $db = $this->db->table($this->table, 'sa')
            ->field('sa.*, s.shop_number, s.company_id, s.dep_id, c.company_name')
            ->leftJoin('shop','s', 's.id = sa.shop_id')
            ->leftJoin('company', 'c', 's.company_id = c.id')
            ->where('sa.id = :id', ['id' => $id]);
        $detail = $db->one();
        $detail = $this->formatItem($detail);

        return $detail;
    }
}
