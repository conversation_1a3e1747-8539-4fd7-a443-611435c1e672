<?php

namespace plugins\salary\Controller;

use core\lib\db\dbMysql;
use core\lib\db\dbSMysql;
use plugins\salary\models\salaryItemModel;
use plugins\salary\models\userModel;
use plugins\salary\models\userSalaryModel;

class salaryItemController
{
    // 获取薪资项列表
    public function getList()
    {
        $paras_list = array('page', 'page_size', 'item_name', 'item_type', 'item_tag', 'light_query');
        $param = array_intersect_key($_GET, array_flip($paras_list)) ?: [];
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;

        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_item');
        $sdb->where('where is_delete = 0');

        if ($param['light_query']) {
            $list = $sdb->list();
            $ret = [];
            foreach ($list as $item) {
                $ret[] = [
                    'id' => $item['id'],
                    'item_name' => $item['item_name']
                ];
            }
            returnSuccess($ret);
        }

        if (isset($param['item_name'])) {
            $sdb->andWhere('item_name like :item_name', ['item_name' => '%' . $param['item_name'] . '%']);
        }
        if (isset($param['item_type'])) {
            $sdb->andWhere('item_type = :item_type', ['item_type' => $param['item_type']]);
        }
        if (isset($param['item_tag'])) {
            $sdb->andWhere('item_tag = :item_tag', ['item_tag' => $param['item_tag']]);
        }

        $data = $sdb->pages($page, $limit);
        if (empty($data['list'])) returnSuccess($data);

        // 部门信息
        $departments= $db->table('qwdepartment')->field('wp_id, name')->list();
        $departments = array_column($departments, null, 'wp_id');

        // 用户信息
        $user = $db->table('qwuser')->field('id, wname')->list();
        $user = array_column($user, null, 'id');

        foreach ($data['list'] as &$item) {
            $item['item_detail'] = json_decode($item['item_detail'], true);
            $item['operator_name'] = $user[$item['operator']]['wname'] ?? '';
            $item['text'] = salaryItemModel::getText($item['item_detail'], $departments);
        }

        returnSuccess($data);
    }

    // 获取薪资项详情
    public function getDetail()
    {
        $id = $_GET['id'] ?? 0;
        if (empty($id)) returnError('薪资项ID不能为空');

        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_item');
        $data = $sdb->where('where id =:id and is_delete = 0', ['id' => $id])->one();
        if (empty($data)) returnError('薪资项不存在');

        $data['item_detail'] = json_decode($data['item_detail'], true);
        returnSuccess($data);
    }

    // 添加薪资项
    public function add()
    {
        $paras_list = array('id', 'item_name', 'item_type','item_tag', 'item_detail', 'remark');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        empty($param['item_name']) && returnError('薪资项名称不能为空');
        empty($param['item_tag']) && returnError('薪资项自定义标记不能为空');
        empty($param['item_type']) && returnError('薪资项类型不能为空');
        empty($param['item_detail']) && returnError('薪资项详情不能为空');

        salaryItemModel::check(json_decode($param['item_detail'], true));

        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_item');

        $data = [
            'item_name' => $param['item_name'],
            'item_type' => $param['item_type'],
            'item_tag' => $param['item_tag'],
            'item_detail' => $param['item_detail'],
            'remark' => $param['remark'] ?? '',
            'operator' => userModel::$qwuser_id,
        ];
        if ($param['id']) {
            $one = $sdb->where('where id =:id and is_delete = 0', ['id' => $param['id']])->one();
            if (empty($one)) returnError('薪资项不存在');
            $res = $sdb->where('where id =:id and is_delete = 0', ['id' => $param['id']])->update($data);
        } else {
            $res = $sdb->insert($data);
        }

        returnSuccess('添加薪资项成功');
    }

    // 删除
    public function delete()
    {
        $id = $_POST['id'] ?? 0;
        if (empty($id)) returnError('薪资项ID不能为空');

        $sdb = dbSMysql::getInstance();
        $sdb->table('salary_item');
        $one = $sdb->where('where id =:id and is_delete = 0', ['id' => $id])->one();
        if (empty($one)) returnError('薪资项不存在');

        $sdb->where('where id =:id and is_delete = 0', ['id' => $id])->update([
            'is_delete' => 1,
        ]);

        returnSuccess('删除薪资项成功');
    }




}