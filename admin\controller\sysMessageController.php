<?php

namespace admin\controller;

use admin\form\sysMessageForm;


class sysMessageController
{
    //获取未读消息列表
    public function getUnreadList()
    {
        $list = sysMessageForm::getUnreadList();
        returnSuccess($list, '');
    }
    // 获取消息列表
    public function getList()
    {
        $paras_list = array('is_read', 'page', 'page_size','model_id');
        $param = arrangeParam($_POST, $paras_list);
        $list = sysMessageForm::getList($param);
        returnSuccess($list, '');
    }

    //获取企微通知的消息
    public function getMsgDetail() {
        $secret_code = $_POST['data']??'';
        if (empty($secret_code)) {
            SetReturn(-1,'参数有误');
        }
        sysMessageForm::getMsgDetail($secret_code);
    }

    //消息详情
    public function getDetail()
    {
        $id = (int)$_POST['id'];
        $model_id = (int)$_POST['model_id'];
        if (!$id || !$model_id) {
            SetReturn(-1, '参数有误');
        }
        $data = sysMessageForm::getDetail($id, $model_id);
        returnSuccess($data, '');
    }
    //标记全部已读
    public function setAllRead()
    {
        $type = (int)$_POST['type'];
        $model_id = (int)$_POST['model_id'];
        if (!$model_id) {
            SetReturn(-1, '参数有误');
        }
        sysMessageForm::setAllRead($type,$model_id);
        SetReturn(0, '');
    }
    //统计未读消息数量
    public function getMessageCount()
    {
        sysMessageForm::getMessageCount();
    }

}