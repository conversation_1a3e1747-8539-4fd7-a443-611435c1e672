# !/bin/bash
# 领星msku报告数据同步脚本
# 获取第一个参数
token='01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0'
qwUrl='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=03a8032d-65c0-4390-a588-9a6b2cf11336'
fsUrl='https://open.feishu.cn/open-apis/bot/v2/hook/2b63c602-bb10-465f-bc9a-af4dc1f02233'
while true; do
    # 发送POST请求，这里假设URL为http://example.com/api，根据实际情况替换
#    response=$(curl -s -X POST -d "token=$token" 'http://171.223.214.187:8901/task/task/lingXingApi/synStorageFbaDetail')
    response=$(curl -s -X POST -d "token=$token" 'http://oa.ywx.com//task/lingXingApi/synStorageFbaDetail')
     echo "$response"
    # 从响应中提取code字段的值
    code=$(echo "$response" | grep -oP '"code":\K-?\d+')
    # 从响应中提取msg字段的值
    msg=$(echo "$response" | grep -oP '"message"\s*:\s*"\K[^"]+')
    # 检查code字段的值
    current_time=$(date +"%Y-%m-%d %H:%M:%S")
    if [ "$code" -ne 2 ]; then
        echo "code=$code，继续请求$current_time"
    else
       break ;
    fi
done
echo "FBA库存明细同步完成，$current_time"

# 执行FBA库存数据汇总
echo "开始执行FBA库存数据汇总..."
summary_start_time=$(date +"%Y-%m-%d %H:%M:%S")

while true; do
    # 发送POST请求，这里假设URL为http://example.com/api，根据实际情况替换
    summary_response=$(curl -s -X POST -d "token=$token" 'http://oa.ywx.com/task/logistics/getFbaStorageSummary')
    echo "$summary_response"

    # 从汇总响应中提取code字段的值
    summary_code=$(echo "$summary_response" | grep -oP '"code":\K-?\d+')
    echo "summary_code=$summary_code"
    # 从汇总响应中提取msg字段的值
    summary_msg=$(echo "$summary_response" | grep -oP '"message"\s*:\s*"\K[^"]+')

    summary_end_time=$(date +"%Y-%m-%d %H:%M:%S")
    if [ "$summary_code" -ne 2 ]; then
        echo "code=$summary_code，继续请求$current_time"
    else
       break ;
    fi
done

# 检查汇总结果
if [ "$summary_code" -eq 2 ]; then
    echo "FBA库存数据汇总完成，$summary_end_time"
    # 发送企微机器人 - 成功消息
    # curl "$qwUrl" -H "Content-Type: application/json" -d "{\"msgtype\":\"text\",\"text\":{\"content\":\"✅ 领星FBA库存处理完成\\n📊 明细同步: code=$code, msg=$msg\\n📈 数据汇总: code=$summary_code, msg=$summary_msg\\n⏰ 完成时间: $summary_end_time\"}}"
    # curl "$fsUrl" -H "Content-Type: application/json" -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"✅ 领星FBA库存处理完成\\n📊 明细同步: code=$code, msg=$msg\\n📈 数据汇总: code=$summary_code, msg=$summary_msg\\n⏰ 完成时间: $summary_end_time\"}}"
else
    echo "FBA库存数据汇总失败，code=$summary_code，msg=$summary_msg"
    # 发送企微机器人 - 失败消息
    # curl "$qwUrl" -H "Content-Type: application/json" -d "{\"msgtype\":\"text\",\"text\":{\"content\":\"❌ 领星FBA库存处理异常\\n📊 明细同步: code=$code, msg=$msg\\n📈 数据汇总: code=$summary_code, msg=$summary_msg\\n⏰ 失败时间: $summary_end_time\"}}"
    # curl "$fsUrl" -H "Content-Type: application/json" -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"❌ 领星FBA库存处理异常\\n📊 明细同步: code=$code, msg=$msg\\n📈 数据汇总: code=$summary_code, msg=$summary_msg\\n⏰ 失败时间: $summary_end_time\"}}"
fi