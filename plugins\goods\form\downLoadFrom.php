<?php
/**
 * @author: zhangguoming
 * @Time: 2024/5/31 17:27
 */

namespace  plugins\goods\form;


use plugins\goods\common\publicMethod;

class downLoadFrom
{
    public static function getdownLoadBase64Encode(string $url, int $goods_id = 0,int $file_type = 0) {
        $url_split = explode('.',$url);
        $ext_suffix = end($url_split);
        $img_dir = SELF_FK.$url;
//        if (in_array($ext_suffix, ['jpg', 'png', 'gif','jpeg'])) {
//            //加内容
//            $new_img = publicMethod::addTxtToPic($img_dir, $ext_suffix, $goods_id, $file_type);
//            if ($new_img) {
//                $img_dir = $new_img;
//            }
//        }
        $data = file_get_contents($img_dir);
        $base64Data = base64_encode($data);
        return $base64Data;
    }
}