<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/13 13:37
 */

namespace core\lib\app_auth\plugins;

use admin\models\userModel as baseUserModel;
use plugins\logistics\models\userModel;
use plugins\logistics\models\userRolesModel;

class authLogistics
{
    //公用方法（不用验证权限的接口）
    //验证用户请求权限

    static array $common_auth = [
        'logistics/index/index',
        //用户
        'logistics/user/updateUserPwd', //当前登录人修改自己密码
        'logistics/user/getUserInfo',   //当前登录人的基本信息
        'logistics/user/getOftenUsedUser', //获取常用用户
        'logistics/user/setOftenUsedUser', //获取常用用户
        'logistics/user/getList', //查看成员列表
        //角色
        'logistics/role/getList',//角色列表
        'logistics/role/getDefaultAuth', //获取权限列表
        //消息
        'logistics/message/getMsgDetail',//系统外获取消息
        'logistics/message/getList',   //消息列表
        'logistics/message/getDetail', //消息详情
        'logistics/message/setAllRead',//全部已读
        'logistics/message/getMessageCount', //获取数量
        'logistics/message/getMsgDetail',//系统外获取消息
        //节日活动
        'logistics/festivalActivities/getBaseConfig', //获取基础配置
        'logistics/festivalActivities/getList', //活动列表
        'logistics/festivalActivities/getDetail', //活动详情
        'logistics/festivalActivities/getActiveActivities', //获取有效活动
        'logistics/festivalActivities/getActivitiesBySite', //获取站点活动
        'logistics/festivalActivities/getFestivalTypeStats', //节日类型统计
        'logistics/festivalActivities/checkNameExists', //检查名称唯一性
        //全局配置
        'logistics/config/getBaseConfig',//获取配置
        'logistics/config/getUserConfig',
        'logistics/config/setUserConfig',
    ];
    //验证用户请求权限
    public static function checkAuth($url, $token_perfix){

        //先重写userModel中数据
        $auth_ = userRolesModel::getAuthByQuserId(baseUserModel::$qwuser_id);
        $this_auth = [];
        if ($auth_) {
            $this_auth = json_decode($auth_['auth'], true);
            $model = new userModel();
            userModel::$auth = $auth_['auth'] ?? '[]';
            userModel::$role_type = $auth_['role_type'];
        }
        //权限验证
        if (baseUserModel::$is_super == 1) {
            return true;
        }
        if (in_array($url, self::$common_auth)) {
            return true;
        }
        if (!in_array($url,$this_auth)) {
            SetReturn(-1,'暂无权限');
        } else {
            return true;
        }
        return false;
    }

}
