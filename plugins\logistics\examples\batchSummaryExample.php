<?php
/**
 * FBA库存数据汇总功能使用示例
 * @purpose 演示如何使用批量数据汇总功能
 * @Author: System
 * @Time: 2025/06/26
 */

// 引入必要的文件
define('SELF_FK', realpath('')); //当前框架所在的根目录
//define('SELF_FK', $_SERVER['DOCUMENT_ROOT']); //当前框架所在的根目录
define('CORE', SELF_FK.'/core'); //核心文件目录
define('DEBUG', true); //调试模式设置
define('LOG_PATH', SELF_FK.'/log'); //调试模式设置
define('SYS_PUBLISH',false); //系统发版中

define('ROOT', SELF_FK."/");
require_once "environment.php"; //系统环境
require_once ROOT."/vendor/autoload.php";
include  CORE.'/common/function.php';
include  CORE.'/common/return.php';
include  CORE.'/common/phperror.php';
//方法请求
include CORE.'/fk.php';
//自动加载类
spl_autoload_register('\core\fk::load');

use plugins\logistics\models\fbaStorageSummaryModel;

/**
 * 示例1：基本使用方法
 */
function basicUsageExample()
{
    echo "=== 示例1：基本使用方法 ===\n";
    
    try {
        $model = new fbaStorageSummaryModel();
        
        // 执行当天数据的汇总
        $result = $model->batchSummarizeFromDetail();
        
        if ($result['success']) {
            echo "✅ 汇总成功！\n";
            echo "处理记录数: {$result['total_processed']}\n";
            echo "新增记录数: {$result['total_inserted']}\n";
            echo "更新记录数: {$result['total_updated']}\n";
            echo "处理时间: {$result['processing_time']}秒\n";
        } else {
            echo "❌ 汇总失败: {$result['error_message']}\n";
        }
        
    } catch (Exception $e) {
        echo "❌ 执行异常: " . $e->getMessage() . "\n";
    }
}

/**
 * 示例2：指定日期和批量大小
 */
function customParametersExample()
{
    echo "\n=== 示例2：指定日期和批量大小 ===\n";
    
    try {
        $model = new fbaStorageSummaryModel();
        
        // 指定同步日期和批量大小
        $syncDate = '2025-06-25';
        $batchSize = 500;
        
        $result = $model->batchSummarizeFromDetail($syncDate, $batchSize);
        
        if ($result['success']) {
            echo "✅ 指定参数汇总成功！\n";
            echo "同步日期: $syncDate\n";
            echo "批量大小: $batchSize\n";
            echo "处理记录数: {$result['total_processed']}\n";
            echo "处理时间: {$result['processing_time']}秒\n";
        } else {
            echo "❌ 汇总失败: {$result['error_message']}\n";
        }
        
    } catch (Exception $e) {
        echo "❌ 执行异常: " . $e->getMessage() . "\n";
    }
}

/**
 * 示例3：带进度回调的汇总
 */
function progressCallbackExample()
{
    echo "\n=== 示例3：带进度回调的汇总 ===\n";
    
    try {
        $model = new fbaStorageSummaryModel();
        
        // 定义进度回调函数
        $progressCallback = function($progress, $processed, $total) {
            echo "进度: {$progress}% ({$processed}/{$total})\n";
        };
        
        $result = $model->batchSummarizeFromDetail(null, 100, $progressCallback);
        
        if ($result['success']) {
            echo "✅ 带进度回调汇总成功！\n";
            echo "最终处理记录数: {$result['total_processed']}\n";
        } else {
            echo "❌ 汇总失败: {$result['error_message']}\n";
        }
        
    } catch (Exception $e) {
        echo "❌ 执行异常: " . $e->getMessage() . "\n";
    }
}

/**
 * 示例4：批量处理多个日期
 */
function batchMultipleDatesExample()
{
    echo "\n=== 示例4：批量处理多个日期 ===\n";
    
    $model = new fbaStorageSummaryModel();
    $dates = ['2025-06-23', '2025-06-24', '2025-06-25'];
    $totalResults = [];
    
    foreach ($dates as $date) {
        echo "处理日期: $date\n";
        
        try {
            $result = $model->batchSummarizeFromDetail($date, 500);
            
            if ($result['success']) {
                echo "  ✅ 成功 - 处理: {$result['total_processed']}, 新增: {$result['total_inserted']}, 更新: {$result['total_updated']}\n";
                $totalResults[] = $result;
            } else {
                echo "  ❌ 失败: {$result['error_message']}\n";
            }
            
        } catch (Exception $e) {
            echo "  ❌ 异常: " . $e->getMessage() . "\n";
        }
    }
    
    // 汇总统计
    if (!empty($totalResults)) {
        $totalProcessed = array_sum(array_column($totalResults, 'total_processed'));
        $totalInserted = array_sum(array_column($totalResults, 'total_inserted'));
        $totalUpdated = array_sum(array_column($totalResults, 'total_updated'));
        $totalTime = array_sum(array_column($totalResults, 'processing_time'));
        
        echo "\n📊 批量处理汇总:\n";
        echo "总处理记录数: $totalProcessed\n";
        echo "总新增记录数: $totalInserted\n";
        echo "总更新记录数: $totalUpdated\n";
        echo "总处理时间: {$totalTime}秒\n";
    }
}

/**
 * 示例5：错误处理和重试机制演示
 */
function errorHandlingExample()
{
    echo "\n=== 示例5：错误处理演示 ===\n";
    
    try {
        $model = new fbaStorageSummaryModel();
        
        // 故意使用无效的日期格式来触发错误
        $result = $model->batchSummarizeFromDetail('invalid-date');
        
    } catch (Exception $e) {
        echo "✅ 成功捕获预期错误: " . $e->getMessage() . "\n";
    }
    
    try {
        // 故意使用无效的批量大小来触发错误
        $result = $model->batchSummarizeFromDetail(null, -1);
        
    } catch (Exception $e) {
        echo "✅ 成功捕获预期错误: " . $e->getMessage() . "\n";
    }
}

/**
 * 示例6：性能监控
 */
function performanceMonitoringExample()
{
    echo "\n=== 示例6：性能监控 ===\n";
    
    $model = new fbaStorageSummaryModel();
    
    // 记录开始时间和内存
    $startTime = microtime(true);
    $startMemory = memory_get_usage();
    
    try {
        $result = $model->batchSummarizeFromDetail(null, 200);
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        $peakMemory = memory_get_peak_usage();
        
        echo "📈 性能统计:\n";
        echo "执行时间: " . round($endTime - $startTime, 2) . "秒\n";
        echo "内存使用: " . round(($endMemory - $startMemory) / 1024 / 1024, 2) . "MB\n";
        echo "峰值内存: " . round($peakMemory / 1024 / 1024, 2) . "MB\n";
        
        if ($result['success']) {
            echo "处理效率: " . round($result['total_processed'] / $result['processing_time'], 2) . " 记录/秒\n";
        }
        
    } catch (Exception $e) {
        echo "❌ 性能测试异常: " . $e->getMessage() . "\n";
    }
}

/**
 * 主函数 - 运行所有示例
 */
function runAllExamples()
{
    echo "🚀 FBA库存数据汇总功能使用示例\n";
    echo "=====================================\n";
    
    // 运行所有示例
    basicUsageExample();
    customParametersExample();
    progressCallbackExample();
    batchMultipleDatesExample();
    errorHandlingExample();
    performanceMonitoringExample();
    
    echo "\n✨ 所有示例运行完成！\n";
}

// 如果直接运行此文件，执行所有示例
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    runAllExamples();
}
