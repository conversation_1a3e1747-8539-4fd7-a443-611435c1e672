-- 审批实例表
CREATE TABLE IF NOT EXISTS `approval_instances` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `business_id` int(11) NOT NULL COMMENT '业务ID',
  `title` varchar(100) NOT NULL COMMENT '审批标题',
  `initiator_id` int(11) NOT NULL COMMENT '发起人ID',
  `initiator_name` varchar(50) NOT NULL COMMENT '发起人姓名',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态(0-审批中,1-通过,2-拒绝,3-撤销)',
  `config_snapshot` text COMMENT '审批配置快照',
  `business_data` text COMMENT '业务数据',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `completed_time` datetime DEFAULT NULL COMMENT '完成时间',
  PRIMARY KEY (`id`),
  KEY `idx_business` (`business_type`,`business_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审批实例表';

-- 审批流程表
CREATE TABLE IF NOT EXISTS `approval_flows` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `instance_id` int(11) NOT NULL COMMENT '审批实例ID',
  `node_index` int(11) NOT NULL COMMENT '节点序号',
  `node_type` varchar(20) NOT NULL COMMENT '节点类型(approver-审批人,cc-抄送人)',
  `node_name` varchar(50) NOT NULL COMMENT '节点名称',
  `approver_id` int(11) NOT NULL COMMENT '审批人ID',
  `approver_name` varchar(50) NOT NULL COMMENT '审批人姓名',
  `is_active` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否活动节点',
  `action` tinyint(1) DEFAULT NULL COMMENT '操作(1-同意,2-拒绝)',
  `comment` varchar(500) DEFAULT NULL COMMENT '审批意见',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `action_time` datetime DEFAULT NULL COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_instance` (`instance_id`),
  KEY `idx_approver` (`approver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审批流程表';

-- 备货审核表
CREATE TABLE IF NOT EXISTS `prepare_audit` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(100) NOT NULL COMMENT '标题',
  `content` text NOT NULL COMMENT '内容',
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '金额',
  `creator_id` int(11) NOT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) NOT NULL COMMENT '创建人姓名',
  `department_id` int(11) NOT NULL COMMENT '部门ID',
  `approval_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '审批状态(0-审批中,1-通过,2-拒绝)',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_creator` (`creator_id`),
  KEY `idx_department` (`department_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='备货审核表';

-- 初始化审批配置
INSERT INTO `config` (`key_name`, `data`) VALUES
('perpare_audit', '{"rule":[
  {
    "condition":{"num":100,"money":1000},
    "notification":{"user":[],"role":[],"leader":"0","subordinate":"0","sender":"0", "type":[]},
    "audit_list":[{"user":[],"role":[],"leader":"0","type":["user"],"audit_type":"1"}]
  },
  {
    "condition":{"num":100,"money":1000},
    "notification":{"user":[],"role":[],"leader":"0","subordinate":"0","sender":"0"},
    "audit_list":[{"user":[],"role":[],"leader":"0","type":"user","audit_type":"1"}]
  }
],
 "status":1}');
