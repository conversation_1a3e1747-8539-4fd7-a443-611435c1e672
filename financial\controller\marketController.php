<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/19 16:55
 */

namespace financial\controller;

use core\lib\db\dbFMysql;

class marketController
{
    public function getList() {
        $db = dbFMysql::getInstance();
        $data = $db->table('market')
            ->where('where is_delete = 0')
            ->field('id,mid,region,aws_region,country,code')
            ->list();
        returnSuccess($data);
    }
}