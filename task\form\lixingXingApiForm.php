<?php

/**
 * @author: zhangguoming
 * @Time: 2024/6/14 9:08
 */

namespace task\form;
use core\lib\db\dbAfMysql;
use core\lib\db\dbErpMysql;
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use core\lib\db\dbSshMysql;
use core\lib\ExceptionError;
use core\lib\log;
use financial\form\messagesFrom;
use financial\models\mskuReportModel;
use financial\models\projectListingModel;

class lixingXingApiForm
{
    public static $m_date_routing = [];//本月的汇率（同步报表数据用得到）
    public static $currency_keys = [];//要转换金额的字段
    //汇率同步信息修改
    public static function editRouting($data,$date) {
        //获取当月币种
        $db = dbFMysql::getInstance();
        $rout_list = $db->table('routing')
            ->where('where date=:date',['date'=>$date])
            ->list();
        foreach ($data as $rout) {
            $has_data = 0;
            foreach ($rout_list as $v) {
                if ($v['code'] == $rout['code']) {
                    $has_data = 1;
                    $db->table('routing')
                        ->where('where id = :id',['id'=>$v['id']])
                        ->update([
                            'rate_org'=>$rout['rate_org'],
                            'my_rate'=>$rout['my_rate'],
                            'updated_time'=>date('Y-m-d H:i:s'),
                            'syn_time'=>date('Y-m-d H:i:s'),
                        ]);
                    break;
                }
            }
            if (!$has_data) {
                $db->table('routing')
                    ->insert([
                        'date'=>$rout['date'],
                        'code'=>$rout['code'],
                        'icon'=>$rout['icon'],
                        'name'=>$rout['name'],
                        'rate_org'=>$rout['rate_org'],
                        'my_rate'=>$rout['my_rate'],
                        'created_time'=>date('Y-m-d H:i:s'),
                        'syn_time'=>date('Y-m-d H:i:s'),
                    ]);
            }
        }
    }
    //市场同步信息修改
    public static function editMarket($data) {
        $db = dbFMysql::getInstance();
        $market = $db->table('market')
            ->where('where is_delete=0')->list();
        foreach ($data as $v) {
            $has_market = 0;
            foreach ($market as $v1) {
                if ($v['code'] == $v1['code'] && $v['mid'] == $v1['mid']) {
                    //修改
                    $has_market = 1;
                    $db->table('market')
                        ->where('where id =:id',['id'=>$v1['id']])
                        ->update([
                            'mid'=>$v['mid'],
                            'region'=>$v['region'],
                            'aws_region'=>$v['aws_region'],
                            'country'=>$v['country'],
                            'marketplace_id'=>$v['marketplace_id'],
                            'syn_time'=>date('Y-m-d H:i:s'),
                        ]);
                    break;
                }
            }
            if (!$has_market) {
                $db->table('market')
                    ->insert([
                        'mid'=>$v['mid'],
                        'code'=>$v['code'],
                        'region'=>$v['region'],
                        'aws_region'=>$v['aws_region'],
                        'country'=>$v['country'],
                        'marketplace_id'=>$v['marketplace_id'],
                        'syn_time'=>date('Y-m-d H:i:s'),
                    ]);
            }
        }
    }
    //项目同步，listing同步
    public static function synProject(){
        $m_date = empty($_POST['date'])?date('Y-m', strtotime('-1 month')):$_POST['date'];
        if (empty($m_date)) {
            SetReturn(2,'月份异常');
        }
        $year = date('Y',strtotime($m_date));
        projectListingModel::creatProjectListingTable($year);
        $redis = (new \core\lib\predisV())::$client;
//        $redis->del('listing_syn_page');
//        return SetReturn(2,'同步完成');
        if ($redis->get('listing_syn_page')) {
            $page = ($redis->get('listing_syn_page')) + 1;
        } else {
            $page = 1;
        }
        $redis->set('listing_syn_page',$page);
        $redis->expire('listing_syn_page',0.5*60*60);
        $dbErp = dbErpMysql::getInstance();
        $page_size = 5000;
        $data = $dbErp->table('lingxing_listing')
            ->where("where principal is not null and principal <> '' and marketplace <> ''")
            ->field('id,sid,seller_sku,principal,status,is_delete,marketplace,fnsku,asin,parent_asin,local_sku')
            ->order('id asc')
            ->pages($page,$page_size);
        $dbF = dbFMysql::getInstance();
        if ($page == 1) {
            //截断表
            $dbF->query("TRUNCATE TABLE oa_f_project_syn_log");
        }
        $log_table = "project_syn_log";
        $list = $data['list'];
        if (!count($list)) {
            //处理异常
            $redis->del('listing_syn_page');
            SetReturn(2,'同步完成');
        }
        //精铺店铺查询

        $sids = array_unique(array_column($list,'sid'));
        $jing_list = $dbF->table('seller')
            ->where('where is_jing = 1')
            ->whereIn('sid',$sids)
            ->field('sid')
            ->list();
        $store_jing_sids = array_column($jing_list,'sid');
        //查询同步的msku（即seller_sku）
        $user_name = [];
        $useful_data = [];
        $save_p_list = [];//要保存的项目
        $save_e_list = [];//要保存日志
        foreach ($list as $v) {
            if (empty($v['marketplace'])) {
                $save_e_list[] = [
                    'sid'=>$v['sid'],
                    'seller_sku'=>$v['seller_sku'],
                    'syn_status'=>0,
                    'syn_time'=>date('Y-m-d H:i:s'),
                    'principal'=>json_encode($v['principal'],JSON_UNESCAPED_UNICODE),
                    'status'=>$v['status'],
                    'is_delete'=>$v['is_delete'],
                    'reson_txt'=>'linsting国家为空',
                    'project_name'=>'',
                    'country'=>$v['marketplace'],
                    'yunying_name'=>'',
                ];
            }
            //负责人
            if (!empty($v['principal'])) {
                $v['principal'] = json_decode($v['principal'],true);
                if (count($v['principal']) > 2) {
                    $useful_data[] = $v;
                    $user_name = array_merge($user_name,array_column($v['principal'],'principal_name'));
                } else {
                    $save_e_list[] = [
                        'sid'=>$v['sid'],
                        'seller_sku'=>$v['seller_sku'],
                        'syn_status'=>0,
                        'syn_time'=>date('Y-m-d H:i:s'),
                        'principal'=>json_encode($v['principal'],JSON_UNESCAPED_UNICODE),
                        'status'=>$v['status'],
                        'is_delete'=>$v['is_delete'],
                        'reson_txt'=>'负责人数据异常',
                        'project_name'=>'',
                        'country'=>$v['marketplace'],
                        'yunying_name'=>'',
                    ];
                }
            } else {
                $save_e_list[] = [
                    'sid'=>$v['sid'],
                    'seller_sku'=>$v['seller_sku'],
                    'syn_status'=>0,
                    'syn_time'=>date('Y-m-d H:i:s'),
                    'principal'=>json_encode($v['principal'],JSON_UNESCAPED_UNICODE),
                    'status'=>$v['status'],
                    'is_delete'=>$v['is_delete'],
                    'reson_txt'=>'负责人数据异常',
                    'project_name'=>'',
                    'country'=>$v['marketplace'],
                    'yunying_name'=>'',
                ];
            }
        }
        $user_name = array_unique($user_name);
        //国家
        $country_list = $dbF->table('market')
            ->whereIn('ct_id',[3,4])
            ->field('ct_id,country')
            ->list();
        $continents = [];
        foreach ($country_list as $v) {
            $continents[$v['country']] = $v['ct_id'];
        }
        if (count($useful_data)) {
            $db = dbMysql::getInstance();
            //查询用户
            $user_list = $db->table('qwuser')
                ->whereIn('wname',$user_name)
                ->field('id,wname')
                ->list();
            if (count($user_list)) {
                $user_array = [];
                foreach ($user_list as $v) {
                    $user_array[$v['wname']] = $v;
                }
                //项目
                $project_ = self::getProjectAll($dbF);
                $oa_listing = [];//listing
                if (count($project_)) {
                    //获取用户数组
                    foreach ($useful_data as $v) {
                        //精铺项目
                        if (in_array($v['sid'],$store_jing_sids)) {
                            continue;
                        }
                        $yunying_zu = $v['principal'][1]['principal_name']??'';
                        //普货
                        if ($v['principal'][0]['principal_name'] == '普货') {
                            //项目匹配 普货（按第二层，运营组名匹配），其余按运营组国家匹配
                            $key_l = '普货/普货/'.$yunying_zu;
                            $project = $project_[$key_l]??'';
                        } else {
                            //其他(精品)
                            $key_l = '精品/'.$v['marketplace'].'/'.$yunying_zu;
                            $project = $project_[$key_l]??'';
                            //项目匹配不成功，按洲来匹配
                            if (!$project && isset($continents[$v['marketplace']])) {
                                if ($continents[$v['marketplace']] == 4) {
                                    //北美洲
                                    $key_l = '精品/美国/'.$yunying_zu;
                                }
                                if ($continents[$v['marketplace']] == 3) {
                                    //欧洲
                                    $key_l = '精品/德国/'.$yunying_zu;
                                }
                                $project = $project_[$key_l]??'';
                            }
                        }
                        if ($project) {
                            $update_data = [
                                'syn_time'=>date('Y-m-d H:i:s'),
                                'seller_sku'=> $v['seller_sku'],
                                'id'=>$project['id'],
                            ];
                            $yuny_name = $v['principal'][2]['principal_name'];
                            $user_id = 0;
                            //运营匹配
                            if (strpos($yuny_name,'-无') !== false) {
                                //无标签直接归与主管
                                $user_id = $project['user_id'];
                            } else {
                                if (!empty($v['principal'][2]) && isset($user_array[$v['principal'][2]['principal_name']])) {
                                    $user_id = $user_array[$v['principal'][2]['principal_name']]['id'];
                                }
                            }
                            if ($user_id) {
                                $yunying_ids = $project['user_ids'];
                                if (!in_array($user_id,$yunying_ids)) {
                                    $yunying_ids[] = $user_id;
                                    $update_data['user_ids']  = $yunying_ids;
                                    $save_p_list[$key_l] = $update_data;
                                }
                                $save_e_list[] =[
                                    'sid'=>$v['sid'],
                                    'seller_sku'=>$v['seller_sku'],
                                    'syn_status'=>1,
                                    'syn_time'=>date('Y-m-d H:i:s'),
                                    'principal'=>json_encode($v['principal'],JSON_UNESCAPED_UNICODE),
                                    'status'=>$v['status'],
                                    'is_delete'=>$v['is_delete'],
                                    'project_name'=>$v['principal'][1]['principal_name'],
                                    'yunying_name'=>$v['principal'][2]['principal_name'],
                                    'country'=>$v['marketplace'],
                                ];
                                $oa_listing[] = [
                                    'project_id'=>$project['id'],
                                    'yunying_id'=>$user_id,
                                    'sid'=>$v['sid'],
                                    'marketplace'=>$v['marketplace'],
                                    'seller_sku'=>$v['seller_sku'],
                                    'fnsku'=>$v['fnsku'],
                                    'asin'=>$v['asin'],
                                    'parent_asin'=>$v['parent_asin'],
                                    'local_sku'=>$v['local_sku'],
                                ];
                            } else {
                                $save_e_list[] =[
                                    'sid'=>$v['sid'],
                                    'seller_sku'=>$v['seller_sku'],
                                    'syn_status'=>0,
                                    'syn_time'=>date('Y-m-d H:i:s'),
                                    'principal'=>json_encode($v['principal'],JSON_UNESCAPED_UNICODE),
                                    'status'=>$v['status'],
                                    'is_delete'=>$v['is_delete'],
                                    'reson_txt'=>'运营匹配失败',
                                    'project_name'=>$v['principal'][1]['principal_name'],
                                    'yunying_name'=>isset($v['principal'][2])?$v['principal'][2]['principal_name']:'',
                                    'country'=>$v['marketplace'],
                                ];
                            }
                        }
                        else {
                            $save_e_list[] = [
                                'sid'=>$v['sid'],
                                'seller_sku'=>$v['seller_sku'],
                                'syn_status'=>0,
                                'syn_time'=>date('Y-m-d H:i:s'),
                                'principal'=>json_encode($v['principal'],JSON_UNESCAPED_UNICODE),
                                'status'=>$v['status'],
                                'is_delete'=>$v['is_delete'],
                                'reson_txt'=>'项目+国家匹配失败',
                                'project_name'=>$v['principal'][1]['principal_name'],
                                'country'=>$v['marketplace'],
                                'yunying_name'=>isset($v['principal'][2])?$v['principal'][2]['principal_name']:'',
                            ];
                        }

                    }
                }
                $db->beginTransaction();
                if (count($save_p_list)) {
                    foreach ($save_p_list as $v) {
                        $id = $v['id'];
                        $update_data = $v;
                        unset($update_data['id']);
                        $update_data['user_ids'] = ','.implode(',',$update_data['user_ids']).',';
                        $dbF->table('project')
                            ->where('where id=:id',['id'=>$id])
                            ->update($update_data);
                    }
                }
                if (count($save_e_list)) {
                    foreach ($save_e_list as $v) {
                        $dbF->table("$log_table")
                            ->insert($v);
                    }
                }
                if (count($oa_listing)) {
                    foreach ($oa_listing as $v) {
                        $v['m_date'] = $m_date;
                        $dbF->table('project_listing_'.$year)
                            ->insertIgnore($v);
                    }
                }
                $db->commit();
            }
        }
        $all_page = ceil($data['total']/$page_size);
        if ($page >= $all_page) {
            //处理异常
            $redis->del('listing_syn_page');
            //消息发送
            $error_one = $dbF->table("$log_table")
                ->where("where reson_txt = '未找到项目'")
                ->one();
            if ($error_one) {
                messagesFrom::senMsgByNoticeType(9,'领星项目同步数据中存在未匹配的项目，请及时处理。',0);
            }
            return SetReturn(2,'同步完成');
        }
        return '当前同步页码'.$page.',一共'.$all_page.'页';
    }
    //分类同步
    public static function synCategory(){
        $db = dbErpMysql::getInstance();
        $list = $db->table('lingxing_category')->list();
        //
        $dbf = dbFMysql::getInstance();
        $old_list = $dbf->table('goods_category')->list();
        $old_cids = array_column($old_list,'cid');
        $dbf->table('goods_category')
            ->where('where is_delete=0')
            ->update(['is_delete'=>1]);
        //查询用户信息
        foreach ($list as $v) {
            if (in_array($v['cid'],$old_cids)) {
                $dbf->table('goods_category')
                    ->where('where cid=:cid',['cid'=>$v['cid']])
                    ->update(['title'=>$v['title'],'is_delete'=>0,'syn_time'=>date('Y-m-d H:i:s')]);
            } else {
                $dbf->table('goods_category')->insert([
                    'cid'=>$v['cid'],
                    'title'=>$v['title'],
                    'parent_cid'=>$v['parent_cid'],
                    'syn_time'=>date('Y-m-d H:i:s'),
                ]);
            }
        }
        log::lingXingApi('Category')->info('同步领星分类'.json_encode($list,JSON_UNESCAPED_UNICODE));
    }
    //店铺 $type=1表示从管理系统中点击同步的
    public static function synSeller($type = 0){
        $db = dbErpMysql::getInstance();
        $list = $db->table('lingxing_shop_list')->list();
        $dbf = dbFMysql::getInstance();
        $old_list = $dbf->table('seller')->list();
        $old_seller = [];
        foreach ($old_list as $vv) {
            $old_seller[$vv['sid']] = $vv;
        }
        $dbAf = dbAfMysql::getInstance();
        $old_af_list = $dbAf->table('shop_list')->list();
        $old_ad_seller = [];
        foreach ($old_af_list as $vv) {
            $old_ad_seller[$vv['sid']] = $vv;
        }
        $has_error = 0;//全部数据是否有异常
        $has_new = 0;//新品
        foreach ($list as $v) {
            //已挂、本土、精铺
            $is_ban = 0;
            $is_local = 0;
            $is_jing = 0;
            if (strpos($v['name'],'已挂') !== false){
                $is_ban = 1;
            }
            if (strpos($v['name'],'本土') !== false){
                $is_local = 1;
            }
            if (strpos($v['name'],'精铺') !== false){
                $is_jing = 1;
            }
            $name_array = explode('*',$v['name']);
            //财务更新
            if (isset($old_seller[$v['sid']])) {
                $old_data = $old_seller[$v['sid']];
                $is_error = end($name_array)==$old_data['real_name']?0:1;
                $update_data = [
                    'mid'=>$v['mid'],
                    'name'=>end($name_array),
                    'lx_name'=>$v['name'],
                    'seller_id'=>$v['seller_id'],
                    'account_name'=>$v['account_name'],
                    'seller_account_id'=>$v['seller_account_id'],
                    'region'=>$v['region'],
                    'country'=>$v['country'],
                    'status'=>(int)$v['status'],
                    'syn_time'=>date('Y-m-d H:i:s'),
                    'is_error'=>$is_error,
                    'is_ban'=>$is_ban,
                    'is_local'=>$is_local,
                    'is_jing'=>$is_jing,
                ];
                if ($old_seller[$v['sid']]['updated_time'] != 'null') {
                    $update_data['real_name'] = end($name_array);
                }
                $dbf->table('seller')
                    ->where('where sid=:sid',['sid'=>$v['sid']])
                    ->update($update_data);
                if ($is_error) {
                    $has_error = 1;
                }
            }
            else {
                $dbf->table('seller')
                    ->insert([
                        'sid'=>$v['sid'],
                        'mid'=>$v['mid'],
                        'lx_name'=>$v['name'],
                        'name'=>end($name_array),
                        'real_name'=>end($name_array),
                        'seller_id'=>$v['seller_id'],
                        'account_name'=>$v['account_name'],
                        'seller_account_id'=>$v['seller_account_id'],
                        'region'=>$v['region'],
                        'country'=>$v['country'],
                        'status'=>(int)$v['status'],
                        'syn_time'=>date('Y-m-d H:i:s'),
                        'is_new'=>1,
                        'is_ban'=>$is_ban,
                        'is_local'=>$is_local,
                        'is_jing'=>$is_jing,
                    ]);
                $has_new = 1;
            }
            //售后更新
            if (isset($old_ad_seller[$v['sid']])) {
                $update_data = [
                    'mid'=>$v['mid'],
                    'lx_name'=>$v['name'],
                    'seller_id'=>$v['seller_id'],
                    'account_name'=>$v['account_name'],
                    'seller_account_id'=>$v['seller_account_id'],
                    'region'=>$v['region'],
                    'country'=>$v['country'],
                    'status'=>(int)$v['status'],
                    'syn_time'=>date('Y-m-d H:i:s'),
                ];
                $dbAf->table('shop_list')
                    ->where('sid=:sid',['sid'=>$v['sid']])
                    ->update($update_data);
            }
            else {
                $dbAf->table('shop_list')
                    ->insert([
                        'sid'=>$v['sid'],
                        'mid'=>$v['mid'],
                        'lx_name'=>$v['name'],
                        'seller_id'=>$v['seller_id'],
                        'account_name'=>$v['account_name'],
                        'seller_account_id'=>$v['seller_account_id'],
                        'region'=>$v['region'],
                        'country'=>$v['country'],
                        'status'=>(int)$v['status'],
                        'syn_time'=>date('Y-m-d H:i:s'),
                    ]);
            }
        }
        if (!$type) {
            log::lingXingApi('seller')->info('同步领星店铺'.json_encode($list,JSON_UNESCAPED_UNICODE));
        }
        //消息推送
        if ($has_new || $has_error) {
            messagesFrom::senMsgByNoticeType(6,'有店铺信息需要完善，请及时处理。',0);
        }
    }
    //msku报告数据储存
    public static function saveMskuReport($list,$date) {
        //查看表单是否存在
        $year = date('Y',strtotime($date));
        mskuReportModel::creatMskuReportTable($year);
        $table_log = 'msku_report_syn_log_'.$year;
        $table = 'msku_report_'.$year;
        $table1 = 'msku_report1_'.$year;
        $table2 = 'msku_report2_'.$year;
        $table3 = 'msku_report3_'.$year;
        $table4 = 'msku_report4_'.$year;
        $dbF = dbFMysql::getInstance();
        //已同步的数据查询
        $last_table_log = $dbF->table($table_log)
            ->where('where reportDateMonth=:reportDateMonth',['reportDateMonth'=>$date])
            ->field('lingxing_id')
            ->list();
        $lingxing_ids = array_column($last_table_log,'lingxing_id');
        $success_count = count($lingxing_ids);
        //分类数据
        $category = $dbF->table('goods_category')->where('where is_delete=0')->list();
        $category_list = [];
        foreach ($category as $vv) {
            $category_list[$vv['title']] = $vv;
        }
        //店铺
        $sid_ = array_column($list,'sid');
        $db = dbFMysql::getInstance();
        $seller_list = $db->table('seller')->field('sid,real_name')
            ->where('where is_delete = 0')
            ->whereIn('sid',$sid_)->list();
        $seller_ = [];
        foreach ($seller_list as $v) {
            $seller_[$v['sid']] = $v;
        }
        //成员
        $db_ = dbMysql::getInstance();
        $qwuser_list = $db_->table('qwuser')->field('wname,id')
            ->where('where is_delete=0')
            ->list();
        if (!count($qwuser_list)) {
            returnError('未找到任何表格内相关的运营');
        }
        $qwuser_ = [];
        foreach ($qwuser_list as $v) {
            $qwuser_[$v['id']] = $v;
        }
        //项目
        $project_list = $db->table('project')->field('project_name,id,user_ids')
            ->where('where level=3')
            ->list();
        $project_ = [];
        foreach ($project_list as $v) {
            $user_ids = trim($v['user_ids'],',');
            $v['user_ids'] = explode(',',$user_ids);
            $project_yunying = [];
            foreach ($v['user_ids'] as $id) {
                if (isset($qwuser_[$id])) {
                    $project_yunying[$qwuser_[$id]['wname']] = $qwuser_[$id];
                }
            }
            $v['yunying_list'] = $project_yunying;
            $project_[$v['project_name']] = $v;
        }
        //商品
        $goods_list = $db->table('goods')->field('sku,supplier_id')
            ->list();
        $sku_supplier = [];
        foreach ($goods_list as $v) {
            $sku_supplier[$v['sku']] = $v['supplier_id'];
        }
        //获取金额字段和汇率字段
        self::getHuilv($date);
        //异常记录
        $error_list = [];
        foreach ($list as $vv) {
            //整理数据，根据汇率计算值规则修改
            $v = self::getMskuRow($vv);
            //有同步了就不更新了
            if (in_array($v['id'],$lingxing_ids)) {
                continue;
            }
            //店铺验证
            if (!isset($seller_[$v['sid']])) {
                $error_list[] = ['error_reason'=>'未查询到店铺名','data'=>$v];
                continue;
            } else {
                $sid = $seller_[$v['sid']]['sid'];
                $storeName = $seller_[$v['sid']]['real_name'];
            }
            //部门验证
            $project_array = empty($v['principalRealname'])?[]:explode(',',$v['principalRealname']);
            if (count($project_array) >= 3) {
                if (!isset($project_[$project_array[1]])) {
                    $error_list[] = ['error_reason'=>'未查询到项目','data'=>$v];
                    continue;
                } else {
                    $project_id = $project_[$project_array[1]]['id'];
                    $item_yunying_list = $project_[$project_array[1]]['yunying_list'];
                    if (!count($item_yunying_list)) {
                        $error_list[] = ['error_reason'=>'项目中未设置运营','data'=>$v];
                        continue;
                    }
                    if (!isset($item_yunying_list[$project_array[2]])) {
                        $error_list[] = ['error_reason'=>'项目中不存在该运营','data'=>$v];
                        continue;
                    }
                    $yunying_id = $item_yunying_list[$project_array[2]]['id'];
                }
            } else {
                $error_list[] = ['error_reason'=>'没有项目信息','data'=>$v];
                continue;
            }
            $supplier_id = isset($sku_supplier[$v['localSku']])?$sku_supplier[$v['localSku']]:0;
            $dbF->beginTransaction();
            try {
                if (!empty($v['categoryName'])) {
                    $categoryName = str_replace('\\','/',$v['categoryName']);
                    $split_category = explode('/',$categoryName);
                    $category_id = isset($category_list[end($split_category)])?$category_list[end($split_category)]['id']:0;
                } else {
                    $category_id = 0;
                }
                //基础数据保存
                $base_id = $dbF->table($table)
                    ->insert([
                        'lingxing_id'=>$v['id'],'project_id'=>$project_id,'yunying_id'=>$yunying_id,'category_id'=>$category_id,'sid'=>$sid,'reportDateMonth'=>$v['reportDateMonth'],'postedDateLocale'=>$v['postedDateLocale'],'msku'=>$v['msku'],'asin'=>$v['asin'],'parentAsin'=>$v['parentAsin'],'storeName'=>$storeName,'countryCode'=>$v['countryCode'],'localName'=>$v['localName'],'localSku'=>$v['localSku'],'itemName'=>$v['itemName'],'principalRealname'=>$v['principalRealname'],'productDeveloperRealname'=>$v['productDeveloperRealname'],'categoryName'=>$v['categoryName'],'brandName'=>$v['brandName'],'currencyCode'=>$v['currencyCode'],'currencyIcon'=>$v['currencyIcon'],'listingTagIds'=>$v['listingTagIds'],'country'=>$v['country'],
                        'supplier_id'=>$supplier_id
                    ]);
                //报告数据1
                $dbF->table($table1)
                    ->insert([
                        'base_id'=>$base_id,
                        'totalFbaAndFbmQuantity'=>$v['totalFbaAndFbmQuantity'],'totalFbaAndFbmAmount'=>$v['totalFbaAndFbmAmount'],'totalSalesQuantity'=>$v['totalSalesQuantity'],'fbaSalesQuantity'=>$v['fbaSalesQuantity'],'fbmSalesQuantity'=>$v['fbmSalesQuantity'],'totalReshipQuantity'=>$v['totalReshipQuantity'],'reshipFbmProductSalesQuantity'=>$v['reshipFbmProductSalesQuantity'],'reshipFbmProductSaleRefundsQuantity'=>$v['reshipFbmProductSaleRefundsQuantity'],'reshipFbaProductSalesQuantity'=>$v['reshipFbaProductSalesQuantity'],'reshipFbaProductSaleRefundsQuantity'=>$v['reshipFbaProductSaleRefundsQuantity'],'cgAbsQuantity'=>$v['cgAbsQuantity'],'cgQuantity'=>$v['cgQuantity'],'totalAdsSales'=>$v['totalAdsSales'],'adsSdSales'=>$v['adsSdSales'],'adsSpSales'=>$v['adsSpSales'],'sharedAdsSbSales'=>$v['sharedAdsSbSales'],'sharedAdsSbvSales'=>$v['sharedAdsSbvSales'],'totalAdsSalesQuantity'=>$v['totalAdsSalesQuantity'],'adsSdSalesQuantity'=>$v['adsSdSalesQuantity'],'adsSpSalesQuantity'=>$v['adsSpSalesQuantity'],'sharedAdsSbSalesQuantity'=>$v['sharedAdsSbSalesQuantity'],'sharedAdsSbvSalesQuantity'=>$v['sharedAdsSbvSalesQuantity'],'totalSalesAmount'=>$v['totalSalesAmount'],'fbaSaleAmount'=>$v['fbaSaleAmount'],'fbmSaleAmount'=>$v['fbmSaleAmount'],'shippingCredits'=>$v['shippingCredits'],'promotionalRebates'=>$v['promotionalRebates'],'fbaInventoryCredit'=>$v['fbaInventoryCredit'],'cashOnDelivery'=>$v['cashOnDelivery'],'otherInAmount'=>$v['otherInAmount'],'fbaLiquidationProceeds'=>$v['fbaLiquidationProceeds'],'fbaLiquidationProceedsAdjustments'=>$v['fbaLiquidationProceedsAdjustments'],'amazonShippingReimbursement'=>$v['amazonShippingReimbursement'],'mcFbaFulfillmentFeesQuantity'=>$v['mcFbaFulfillmentFeesQuantity'],'safeTReimbursement'=>$v['safeTReimbursement'],'netcoTransaction'=>$v['netcoTransaction'],'reimbursements'=>$v['reimbursements'],'clawbacks'=>$v['clawbacks'],'sharedComminglingVatIncome'=>$v['sharedComminglingVatIncome'],'giftWrapCredits'=>$v['giftWrapCredits'],'guaranteeClaims'=>$v['guaranteeClaims'],'costOfPoIntegersGranted'=>$v['costOfPoIntegersGranted'],'totalSalesRefunds'=>$v['totalSalesRefunds'],'fbaSalesRefunds'=>$v['fbaSalesRefunds'],'fbmSalesRefunds'=>$v['fbmSalesRefunds'],'shippingCreditRefunds'=>$v['shippingCreditRefunds'],'giftWrapCreditRefunds'=>$v['giftWrapCreditRefunds'],'chargebacks'=>$v['chargebacks'],'costOfPoIntegersReturned'=>$v['costOfPoIntegersReturned'],'promotionalRebateRefunds'=>$v['promotionalRebateRefunds'],'totalFeeRefunds'=>$v['totalFeeRefunds'],'sellingFeeRefunds'=>$v['sellingFeeRefunds'],'fbaTransactionFeeRefunds'=>$v['fbaTransactionFeeRefunds'],'refundAdministrationFees'=>$v['refundAdministrationFees'],'otherTransactionFeeRefunds'=>$v['otherTransactionFeeRefunds'],
                    ]);
                //报告数据2
                $dbF->table($table2)
                    ->insert([
                        'base_id'=>$base_id,'refundForAdvertiser'=>$v['refundForAdvertiser'],'pointsAdjusted'=>$v['pointsAdjusted'],'shippingLabelRefunds'=>$v['shippingLabelRefunds'],'refundsQuantity'=>$v['refundsQuantity'],'refundsRate'=>$v['refundsRate'],'fbaReturnsQuantity'=>$v['fbaReturnsQuantity'],'fbaReturnsSaleableQuantity'=>$v['fbaReturnsSaleableQuantity'],'fbaReturnsUnsaleableQuantity'=>$v['fbaReturnsUnsaleableQuantity'],'fbaReturnsQuantityRate'=>$v['fbaReturnsQuantityRate'],'platformFee'=>$v['platformFee'],'totalFbaDeliveryFee'=>$v['totalFbaDeliveryFee'],'fbaDeliveryFee'=>$v['fbaDeliveryFee'],'mcFbaDeliveryFee'=>$v['mcFbaDeliveryFee'],'otherTransactionFees'=>$v['otherTransactionFees'],'totalAdsCost'=>$v['totalAdsCost'],'adsSpCost'=>$v['adsSpCost'],'adsSbCost'=>$v['adsSbCost'],'adsSbvCost'=>$v['adsSbvCost'],'adsSdCost'=>$v['adsSdCost'],'sharedCostOfAdvertising'=>$v['sharedCostOfAdvertising'],'promotionFee'=>$v['promotionFee'],'sharedSubscriptionFee'=>$v['sharedSubscriptionFee'],'sharedLdFee'=>$v['sharedLdFee'],'sharedCouponFee'=>$v['sharedCouponFee'],'sharedEarlyReviewerProgramFee'=>$v['sharedEarlyReviewerProgramFee'],'sharedVineFee'=>$v['sharedVineFee'],'totalStorageFee'=>$v['totalStorageFee'],'fbaStorageFee'=>$v['fbaStorageFee'],'sharedFbaStorageFee'=>$v['sharedFbaStorageFee'],'longTermStorageFee'=>$v['longTermStorageFee'],'sharedLongTermStorageFee'=>$v['sharedLongTermStorageFee'],'sharedStorageRenewalBilling'=>$v['sharedStorageRenewalBilling'],'sharedFbaDisposalFee'=>$v['sharedFbaDisposalFee'],'sharedFbaRemovalFee'=>$v['sharedFbaRemovalFee'],'sharedFbaInboundTransportationProgramFee'=>$v['sharedFbaInboundTransportationProgramFee'],'sharedLabelingFee'=>$v['sharedLabelingFee'],'sharedPolybaggingFee'=>$v['sharedPolybaggingFee'],'sharedBubblewrapFee'=>$v['sharedBubblewrapFee'],'sharedTapingFee'=>$v['sharedTapingFee'],'sharedFbaCustomerReturnFee'=>$v['sharedFbaCustomerReturnFee'],'sharedFbaInboundDefectFee'=>$v['sharedFbaInboundDefectFee'],'sharedFbaOverageFee'=>$v['sharedFbaOverageFee'],'sharedAmazonPartneredCarrierShipmentFee'=>$v['sharedAmazonPartneredCarrierShipmentFee'],'sharedFbaInboundConvenienceFee'=>$v['sharedFbaInboundConvenienceFee'],'sharedItemFeeAdjustment'=>$v['sharedItemFeeAdjustment'],'sharedOtherFbaInventoryFees'=>$v['sharedOtherFbaInventoryFees'],'fbaStorageFeeAccrual'=>$v['fbaStorageFeeAccrual'],'fbaStorageFeeAccrualDifference'=>$v['fbaStorageFeeAccrualDifference'],'longTermStorageFeeAccrual'=>$v['longTermStorageFeeAccrual'],'longTermStorageFeeAccrualDifference'=>$v['longTermStorageFeeAccrualDifference'],'sharedFbaIntegerernationalInboundFee'=>$v['sharedFbaIntegerernationalInboundFee'],'adjustments'=>$v['adjustments'],'totalPlatformOtherFee'=>$v['totalPlatformOtherFee'],'shippingLabelPurchases'=>$v['shippingLabelPurchases'],'fbaInventoryCreditQuantity'=>$v['fbaInventoryCreditQuantity'],'disposalQuantity'=>$v['disposalQuantity'],'removalQuantity'=>$v['removalQuantity'],'others'=>$v['others'],
                    ]);
                //报告数据3
                $dbF->table($table3)
                    ->insert([
                        'base_id'=>$base_id,'sharedCarrierShippingLabelAdjustments'=>$v['sharedCarrierShippingLabelAdjustments'],'sharedLiquidationsFees'=>$v['sharedLiquidationsFees'],'sharedManualProcessingFee'=>$v['sharedManualProcessingFee'],'sharedOtherServiceFees'=>$v['sharedOtherServiceFees'],'sharedMfnPostageFee'=>$v['sharedMfnPostageFee'],'totalSalesTax'=>$v['totalSalesTax'],'tcsIgstCollected'=>$v['tcsIgstCollected'],'tcsSgstCollected'=>$v['tcsSgstCollected'],'tcsCgstCollected'=>$v['tcsCgstCollected'],'sharedComminglingVatExpenses'=>$v['sharedComminglingVatExpenses'],'taxCollected'=>$v['taxCollected'],'taxCollectedProduct'=>$v['taxCollectedProduct'],'taxCollectedDiscount'=>$v['taxCollectedDiscount'],'taxCollectedShipping'=>$v['taxCollectedShipping'],'taxCollectedGiftWrap'=>$v['taxCollectedGiftWrap'],'sharedTaxAdjustment'=>$v['sharedTaxAdjustment'],'salesTaxRefund'=>$v['salesTaxRefund'],'tcsIgstRefunded'=>$v['tcsIgstRefunded'],'tcsSgstRefunded'=>$v['tcsSgstRefunded'],'tcsCgstRefunded'=>$v['tcsCgstRefunded'],'taxRefunded'=>$v['taxRefunded'],'taxRefundedProduct'=>$v['taxRefundedProduct'],'taxRefundedDiscount'=>$v['taxRefundedDiscount'],'taxRefundedShipping'=>$v['taxRefundedShipping'],'taxRefundedGiftWrap'=>$v['taxRefundedGiftWrap'],'salesTaxWithheld'=>$v['salesTaxWithheld'],'refundTaxWithheld'=>$v['refundTaxWithheld'],'tdsSection194ONet'=>$v['tdsSection194ONet'],'cgPriceTotal'=>$v['cgPriceTotal'],'hasCgPriceDetail'=>$v['hasCgPriceDetail'],'cgUnitPrice'=>$v['cgUnitPrice'],'proportionOfCg'=>$v['proportionOfCg'],'cgTransportCostsTotal'=>$v['cgTransportCostsTotal'],'hasCgTransportCostsDetail'=>$v['hasCgTransportCostsDetail'],'cgTransportUnitCosts'=>$v['cgTransportUnitCosts'],'proportionOfCgTransport'=>$v['proportionOfCgTransport'],'totalCost'=>$v['totalCost'],'proportionOfTotalCost'=>$v['proportionOfTotalCost'],'cgOtherCostsTotal'=>$v['cgOtherCostsTotal'],'cgOtherUnitCosts'=>$v['cgOtherUnitCosts'],'hasCgOtherCostsDetail'=>$v['hasCgOtherCostsDetail'],'proportionOfCgOtherCosts'=>$v['proportionOfCgOtherCosts'],'grossProfit'=>$v['grossProfit'],'grossProfitTax'=>$v['grossProfitTax'],'grossProfitIncome'=>$v['grossProfitIncome'],'grossRate'=>$v['grossRate'],'customOrderFee'=>$v['customOrderFee'],'customOrderFeePrincipal'=>$v['customOrderFeePrincipal'],'customOrderFeeCommission'=>$v['customOrderFeeCommission'],'roi'=>$v['roi'],
                    ]);
                $data_4['base_id'] = $base_id;
                if (!empty($v['otherFeeStr'])) {
                    $otherFeeStr = $v['otherFeeStr'];
                    $data_4['base_id'] = $base_id;
                    foreach ($otherFeeStr as $fee) {
                        switch ($fee['otherFeeName']) {
                            case "普货弃置+清算+亚马逊破损成本调整":
                                $data_4['key1'] = $fee['feeAllocation'];break;
                            case "服务商费用":
                                $data_4['key2'] = $fee['feeAllocation'];break;
                            case "店铺相关费用":
                                $data_4['key3'] = $fee['feeAllocation'];break;
                            case "测评费+推广费-推广部返款":
                                $data_4['key4'] = $fee['feeAllocation'];break;
                            case "测评费+推广费-服务商返款":
                                $data_4['key5'] = $fee['feeAllocation'];break;
                            case "预留资金":
                                $data_4['key6'] = $fee['feeAllocation'];break;
                            case "FBM+多渠道+独立站调整费用+直发费用":
                                $data_4['key7'] = $fee['feeAllocation'];break;
                            case "欧洲VAT税金-第二版":
                                $data_4['key8'] = $fee['feeAllocation'];break;
                            case "站内多渠道尾程成本调整":
                                $data_4['key9'] = $fee['feeAllocation'];break;
                            case "物流其他费用":
                                $data_4['key10'] = $fee['feeAllocation'];break;
                            case "站外广告费":
                                $data_4['key11'] = $fee['feeAllocation'];break;
                            case "站外推广费":
                                $data_4['key12'] = $fee['feeAllocation'];break;
                        }
                    }
                    if (count($data_4)) {
                        $dbF->table($table4)->insert($data_4);
                    }
                } else {
                    $dbF->table($table4)->insert($data_4);
                }
                $dbF->commit();
                $success_count++;
                //原数据记录
                $dbF->table($table_log)
                    ->insert([
                        'lingxing_id'=>$v['id'],
                        'reportDateMonth'=>$v['reportDateMonth'],
                        'msku'=>$v['msku'],
                        'json_data'=>json_encode($vv),
                        'syn_time'=>date('Y-m-d H:i:s'),
                        'syn_status'=>1
                    ]);
            } catch (ExceptionError $error) {
                $dbF->rollBack();
            }
        }
        if (count($error_list)) {
            //保存错误记录
            foreach ($error_list as $v) {
                $dbF->table($table_log)
                    ->insert([
                        'lingxing_id'=>$v['data']['id'],
                        'reportDateMonth'=>$v['data']['reportDateMonth'],
                        'msku'=>$v['data']['msku'],
                        'json_data'=>json_encode($v['data'],JSON_UNESCAPED_UNICODE),
                        'syn_time'=>date('Y-m-d H:i:s'),
                        'syn_status'=>0,
                        'error_msg'=>$v['error_reason']
                    ]);
            }
        }
        return $success_count;
    }
    //商品数据储存
    public static function saveGoods($list){
        $db = dbFMysql::getInstance();
        $goods_list = $db->table('goods')
            ->field('sku')
            ->list();
        $supplier_list = $db->table('supplier')
            ->where('where lx_supplier_id > 0')
            ->field('id,lx_supplier_id')
            ->list();
        $supplier_ = [];
        foreach ($supplier_list as $v) {
            $supplier_[$v['lx_supplier_id']] = $v['id'];
        }
        $sku_list = array_column($goods_list,'sku');
        foreach ($list as $v) {
            $lx_supplier_id = 0;
            $supplier_id = 0;
            $supplier_name = '';
            if (count($v['supplier_quote'])) {
                foreach ($v['supplier_quote'] as $supplier) {
                    if ($supplier['is_primary'] == 1) {
                        $lx_supplier_id = $supplier['supplier_id'];
                        $supplier_name = $supplier['supplier_name'];
                        break;
                    }
                }
            }
            if ($lx_supplier_id) {
                if (isset($supplier_[$lx_supplier_id])) {
                    $supplier_id = $supplier_[$lx_supplier_id];
                }
            }
            if (in_array($v['sku'],$sku_list)) {
                $db->table('goods')
                    ->where('where sku = :sku',['sku'=>$v['sku']])
                    ->update([
                        'lingxing_id'=>$v['id'],
                        'cid'=>$v['cid'],
                        'category_name'=>$v['category_name'],
                        'bid'=>$v['bid'],
                        'brand_name'=>$v['brand_name'],
                        'product_name'=>$v['product_name'],
                        'status'=>$v['status'],
                        'supplier_id'=>$supplier_id,
                        'lx_supplier_id'=>$lx_supplier_id,
                        'supplier_name'=>$supplier_name,
                        'cg_price' => $v['cg_price'],
                        'syn_time'=>date('Y-m-d H:i:s')
                    ]);
            } else {
                $db->table('goods')
                    ->insert([
                        'sku'=>$v['sku'],
                        'lingxing_id'=>$v['id'],
                        'cid'=>$v['cid'],
                        'category_name'=>$v['category_name'],
                        'bid'=>$v['bid'],
                        'brand_name'=>$v['brand_name'],
                        'product_name'=>$v['product_name'],
                        'status'=>$v['status'],
                        'supplier_id'=>$supplier_id,
                        'lx_supplier_id'=>$lx_supplier_id,
                        'supplier_name'=>$supplier_name,
                        'cg_price' => $v['cg_price'],
                        'syn_time'=>date('Y-m-d H:i:s')
                    ]);
            }
        }
        return count($list);
    }
    //保存商品详情(批量)
    public static function saveGoodsDetail($list){
        $db = dbFMysql::getInstance();
        $goods_list = $db->table('goods_detail')
            ->field('sku')
            ->list();
        $sku_list = array_column($goods_list,'sku');
        foreach ($list as $v) {
            $db->table('goods')
                ->where('where sku = :sku',['sku'=>$v['sku']])
                ->update([
                    'pic_url'=>$v['pic_url'],
                    'product_developer'=>$v['product_developer'],
                    'model'=>$v['model'],
                ]);
            if (in_array($v['sku'],$sku_list)) {
                $db->table('goods_detail')
                    ->where('where sku = :sku',['sku'=>$v['sku']])
                    ->update([
                        'pic_url'=>$v['pic_url'],
                        'syn_time'=>date('Y-m-d H:i:s'),
                        'product_developer'=>$v['product_developer'],
                        'model'=>$v['model'],
                    ]);
            } else {
                $db->table('goods_detail')
                    ->insert([
                        'sku'=>$v['sku'],
                        'pic_url'=>$v['pic_url'],
                        'syn_time'=>date('Y-m-d H:i:s'),
                        'product_developer'=>$v['product_developer'],
                        'model'=>$v['model'],
                    ]);
            }
        }
        return count($list);
    }
    public static function saveGoodsDetailOne($v){
        $db = dbFMysql::getInstance();
        $goods = $db->table('goods_detail')
            ->where('where sku=:sku',['sku'=>$v['sku']])
            ->one();
        if ($goods) {
            $db->table('goods_detail')
                ->where('where sku = :sku',['sku'=>$v['sku']])
                ->update([
                    'pic_url'=>$v['pic_url'],
                    'product_developer'=>$v['product_developer'],
                    'syn_time'=>date('Y-m-d H:i:s')
                ]);
        } else {
            $db->table('goods_detail')
                ->insert([
                    'sku'=>$v['sku'],
                    'pic_url'=>$v['pic_url'],
                    'product_developer'=>$v['product_developer'],
                    'syn_time'=>date('Y-m-d H:i:s')
                ]);
        }
    }
    public static function get_msku_report_key() {
        $dbF = dbFMysql::getInstance();
        $msku_report1 = $dbF->table('msku_report3')->one();
        $string = '';
        foreach ($msku_report1 as $k=>$vv) {
            $string.= "'$k'".'=>$v[\''.$k.'\'],';
        }
        dd($string);
    }
    //获取汇率和字段
    public static function getHuilv($date) {
        $db = dbFMysql::getInstance();
        //字段
        $column_list = $db->table('column')
            ->where('where is_delete=0 and show_type=1')
            ->whereIn('table_index',[1,2,3,4])
            ->list();
        $column_ = array_column($column_list,'key_name');
        //汇率
        $routing_list = $db->table('routing')
            ->where('where date=:date',['date'=>$date])
            ->field('code,my_rate')
            ->list();
        if (!count($routing_list)) {
            SetReturn(2,'汇率未同步');
        }
        $routing_ = [];
        foreach ($routing_list as $v) {
            $routing_[$v['code']] = $v['my_rate'];
        }
        self::$currency_keys = $column_;
        self::$m_date_routing = $routing_;
    }
    //更具汇率计算值
    public static function getMskuRow($row) {
        $countryCode = $row['currencyCode'];
        $route = self::$m_date_routing[$countryCode];
        foreach ($row as $k_=>&$v_) {
            if (in_array($k_,self::$currency_keys)) {
                $v_ = round($v_*$route,'2');
            }
        }
        return $row;
    }
    //获取供应商保存
    public static function saveSupplier($list) {
        $db = dbFMysql::getInstance();
        $supplier_data = $db->table('supplier')
            ->field('lx_supplier_id')
            ->list();
        $ids = array_column($supplier_data,'lx_supplier_id');
        foreach ($list as $v) {
            if (in_array($v['supplier_id'],$ids)) {
                $db->table('supplier')
                    ->where('where lx_supplier_id=:lx_supplier_id',['lx_supplier_id'=>$v['supplier_id']])
                    ->update([
                        'supplier_name'=>$v['supplier_name'],
                        'supplier_code'=>$v['supplier_code'],
                        'level_text'=>$v['level_text'],
                        'syn_time'=>date('Y-m-d h:i:s')
                    ]);
            } else {
                $db->table('supplier')->insert([
                    'lx_supplier_id'=>$v['supplier_id'],
                    'supplier_name'=>$v['supplier_name'],
                    'supplier_code'=>$v['supplier_code'],
                    'level_text'=>$v['level_text'],
                    'syn_time'=>date('Y-m-d h:i:s')
                ]);
            }
        }
    }
    //产品新品状态更新(2025-01-07废除)
    public static  function updateNewTag() {
        $redis = (new \core\lib\predisV())::$client;
//        $redis->del('oa_updateNewTag');die;
        $dbF = dbFMysql::getInstance();
        if ($redis->get('oa_updateNewTag')) {
            $page = ($redis->get('oa_updateNewTag')) + 1;
        } else {
            $page = 1;
            $dbF->table('goods')
                ->update(['is_new'=>1]);
        }
        $page_size = 1000;
        $redis->set('oa_updateNewTag',$page);
        $goods_list = $dbF->table('goods')
            ->order('id')
            ->field('sku')
            ->pages($page,$page_size);
        $list = $goods_list['list'];
        if (!count($list)) {
            //处理异常
            $redis->del('oa_updateNewTag');
            SetReturn(2,'同步完成');
        }
        if (count($list)) {
            if ($page == 1) {
                $dbF->table('goods')
                    ->update(['is_new'=>1]);
            }
            //查询当前sku的等级绑定关系
            $skus = array_column($list,'sku');
            lixingXingApiForm::setGoodsNewTag($dbF,$skus);
        }
        $all_page = ceil($goods_list['total']/$page_size);
        if ($page >= $all_page) {
            //处理异常
            $redis->del('oa_updateNewTag');
            SetReturn(2,'同步完成');
        }
        returnSuccess([],'当前同步页码'.$page.',一共'.$all_page.'页');
    }
    //单个新品状态更新(2025-01-07废除)
    public static function setGoodsNewTag($dbF,$skus) {
        //当前时间过去两月的时间戳
        $twoMonthsAgoTime = strtotime('-2 months');
        $dbE = dbErpMysql::getInstance();
        $list = $dbE->table('lingxing_listing')
            ->where("where first_order_time is not null and first_order_time <> ''")
            ->whereIn('local_sku',$skus)
            ->order('first_order_time')
            ->field('local_sku,first_order_time')
            ->list();
        $used_skus = [];
        if (count($list)) {
            foreach ($list as $v) {
                if (!in_array($v['local_sku'],$used_skus)) {
                    $first_order_time = strtotime($v['first_order_time']);
                    if ($first_order_time < $twoMonthsAgoTime) {
                        $dbF->table('goods')
                            ->where('where sku=:sku',['sku'=>$v['local_sku']])
                            ->update(['is_new'=>0]);
                        $used_skus[] = $v['local_sku'];
                        continue;
                    }
                }
                $used_skus[] = $v['local_sku'];
            }
        }
    }
    //更新listing中下首单时间-用于判断产品是否为新新品
    public static function getListingFirstOrderTime() {
        $dbE = dbErpMysql::getInstance();
        $list = $dbE->table('lingxing_listing')
            ->where("where asin <> '' and marketplace<> '' and first_order_time <> '' and first_order_time is not null")
            ->field('seller_sku,sid,asin,marketplace,first_order_time,parent_asin,local_sku')
            ->list();
        $db = dbFMysql::getInstance();
        //国家数据
        $country_list = $db->table('market')
            ->where('where is_delete = 0')
            ->field('country,code,ct_id')
            ->list();
        $country_ = array_column($country_list,'code','country');
        foreach ($list as $v) {
            $first_time = strtotime($v['first_order_time']);
            $db->table('listing_first_time')
                ->insertIgnore([
                    'asin'=>$v['asin'],
                    'p_asin'=>$v['parent_asin'],
                    'sku'=>$v['local_sku'],
                    'msku'=>$v['seller_sku'],
                    'sid'=>$v['sid'],
                    'marketplace'=>$v['marketplace'],
                    'country_code'=>$country_[$v['marketplace']]??'',
                    'first_order_time'=>$v['first_order_time'],
                    'first_time'=>$first_time,
                    'syn_time'=>date('Y-m-d H:i:s'),
                ]);
        }
        return '更新成功';
    }
    //获取所有项目
    public static function getProjectAll($dbF) {
        $project_list = $dbF->table('project')
            ->where('where is_delete = 0')
            ->field('id,user_id,p_id,level,project_name,user_ids')
            ->list();
        $project_1 = array_filter($project_list,function ($row){
            return $row['level'] = 1;
        });
        $project_2 = array_filter($project_list,function ($row){
            return $row['level'] = 2;
        });
        $project_3 = array_filter($project_list,function ($row){
            return $row['level'] = 3;
        });
        $project_ = [];
        foreach ($project_1 as $k1=>$v1) {
            foreach ($project_2 as $k2=>$v2) {
                if ($v1['id'] == $v2['p_id']) {
                    foreach ($project_3 as $k3=>$v3) {
                        if ($v2['id'] == $v3['p_id']) {
                            $project_name = implode('/',[$v1['project_name'],$v2['project_name'],$v3['project_name']]);
                            $project_[$project_name] = [
                                'id'=>$v3['id'],
                                'user_id'=>$v3['user_id'],
                                'name'=>$project_name,
                                'user_ids'=>empty($v3['user_ids'])?[]:explode(',',trim($v3['user_ids'],','))
                            ];
                            unset($project_3[$k3]);
                        }
                    }
                    unset($project_2[$k2]);
                }
            }
        }
        return $project_;
    }

    /**
     * 保存FBA库存明细数据
     * @param array $data
     * @return int
     */
    public static function saveFbaStorageDetail($data)
    {
        $erp_db = dbErpMysql::getInstance();
        $success_count = 0;
        $sync_date = date('Y-m-d');
        
        try {
            $erp_db->beginTransaction();
            
            foreach ($data as $item) {
                // 准备数据，过滤和映射字段
                $save_data = [
                    'name' => $item['name'] ?? '',
                    'sid' => intval($item['sid'] ?? 0),
                    'asin' => $item['asin'] ?? '',
                    'product_name' => $item['product_name'] ?? '',
                    'small_image_url' => $item['small_image_url'] ?? '',
                    'seller_sku' => $item['seller_sku'] ?? '',
                    'fnsku' => $item['fnsku'] ?? '',
                    'sku' => $item['sku'] ?? '',
                    'category_text' => $item['category_text'] ?? '',
                    'cid' => intval($item['cid'] ?? 0),
                    'product_brand_text' => $item['product_brand_text'] ?? '',
                    'bid' => intval($item['bid'] ?? 0),
                    'share_type' => intval($item['share_type'] ?? 0),
                    
                    // 库存数量相关字段
                    'total' => intval($item['total'] ?? 0),
                    'total_price' => floatval($item['total_price'] ?? 0),
                    'available_total' => intval($item['available_total'] ?? 0),
                    'available_total_price' => floatval($item['available_total_price'] ?? 0),
                    'afn_fulfillable_quantity' => intval($item['afn_fulfillable_quantity'] ?? 0),
                    'afn_fulfillable_quantity_price' => floatval($item['afn_fulfillable_quantity_price'] ?? 0),
                    'reserved_fc_transfers' => intval($item['reserved_fc_transfers'] ?? 0),
                    'reserved_fc_transfers_price' => floatval($item['reserved_fc_transfers_price'] ?? 0),
                    'reserved_fc_processing' => intval($item['reserved_fc_processing'] ?? 0),
                    'reserved_fc_processing_price' => floatval($item['reserved_fc_processing_price'] ?? 0),
                    'reserved_customerorders' => intval($item['reserved_customerorders'] ?? 0),
                    'reserved_customerorders_price' => floatval($item['reserved_customerorders_price'] ?? 0),
                    'quantity' => intval($item['quantity'] ?? 0),
                    'quantity_price' => floatval($item['quantity_price'] ?? 0),
                    'afn_unsellable_quantity' => intval($item['afn_unsellable_quantity'] ?? 0),
                    'afn_unsellable_quantity_price' => floatval($item['afn_unsellable_quantity_price'] ?? 0),
                    'afn_inbound_working_quantity' => intval($item['afn_inbound_working_quantity'] ?? 0),
                    'afn_inbound_working_quantity_price' => floatval($item['afn_inbound_working_quantity_price'] ?? 0),
                    'afn_inbound_shipped_quantity' => intval($item['afn_inbound_shipped_quantity'] ?? 0),
                    'afn_inbound_shipped_quantity_price' => floatval($item['afn_inbound_shipped_quantity_price'] ?? 0),
                    'afn_inbound_receiving_quantity' => intval($item['afn_inbound_receiving_quantity'] ?? 0),
                    'afn_inbound_receiving_quantity_price' => floatval($item['afn_inbound_receiving_quantity_price'] ?? 0),
                    'stock_up_num' => intval($item['stock_up_num'] ?? 0),
                    'stock_up_num_price' => floatval($item['stock_up_num_price'] ?? 0),
                    'afn_researching_quantity' => intval($item['afn_researching_quantity'] ?? 0),
                    'afn_researching_quantity_price' => floatval($item['afn_researching_quantity_price'] ?? 0),
                    'total_fulfillable_quantity' => intval($item['total_fulfillable_quantity'] ?? 0),
                    
                    // 库龄统计字段
                    'inv_age_0_to_30_days' => intval($item['inv_age_0_to_30_days'] ?? 0),
                    'inv_age_0_to_30_price' => floatval($item['inv_age_0_to_30_price'] ?? 0),
                    'inv_age_31_to_60_days' => intval($item['inv_age_31_to_60_days'] ?? 0),
                    'inv_age_31_to_60_price' => floatval($item['inv_age_31_to_60_price'] ?? 0),
                    'inv_age_61_to_90_days' => intval($item['inv_age_61_to_90_days'] ?? 0),
                    'inv_age_61_to_90_price' => floatval($item['inv_age_61_to_90_price'] ?? 0),
                    'inv_age_0_to_90_days' => intval($item['inv_age_0_to_90_days'] ?? 0),
                    'inv_age_0_to_90_price' => floatval($item['inv_age_0_to_90_price'] ?? 0),
                    'inv_age_91_to_180_days' => intval($item['inv_age_91_to_180_days'] ?? 0),
                    'inv_age_91_to_180_price' => floatval($item['inv_age_91_to_180_price'] ?? 0),
                    'inv_age_181_to_270_days' => intval($item['inv_age_181_to_270_days'] ?? 0),
                    'inv_age_181_to_270_price' => floatval($item['inv_age_181_to_270_price'] ?? 0),
                    'inv_age_271_to_330_days' => intval($item['inv_age_271_to_330_days'] ?? 0),
                    'inv_age_271_to_330_price' => floatval($item['inv_age_271_to_330_price'] ?? 0),
                    'inv_age_271_to_365_days' => intval($item['inv_age_271_to_365_days'] ?? 0),
                    'inv_age_271_to_365_price' => floatval($item['inv_age_271_to_365_price'] ?? 0),
                    'inv_age_331_to_365_days' => intval($item['inv_age_331_to_365_days'] ?? 0),
                    'inv_age_331_to_365_price' => floatval($item['inv_age_331_to_365_price'] ?? 0),
                    'inv_age_365_plus_days' => intval($item['inv_age_365_plus_days'] ?? 0),
                    'inv_age_365_plus_price' => floatval($item['inv_age_365_plus_price'] ?? 0),
                    
                    // 其他业务字段
                    'recommended_action' => $item['recommended_action'] ?? '',
                    'sell_through' => floatval($item['sell_through'] ?? 0),
                    'estimated_excess_quantity' => floatval($item['estimated_excess_quantity'] ?? 0),
                    'estimated_storage_cost_next_month' => floatval($item['estimated_storage_cost_next_month'] ?? 0),
                    'fba_minimum_inventory_level' => floatval($item['fba_minimum_inventory_level'] ?? 0),
                    'fba_inventory_level_health_status' => $item['fba_inventory_level_health_status'] ?? '',
                    'historical_days_of_supply' => floatval($item['historical_days_of_supply'] ?? 0),
                    'historical_days_of_supply_price' => floatval($item['historical_days_of_supply_price'] ?? 0),
                    'low_inventory_level_fee_applied' => $item['low_inventory_level_fee_applied'] ?? '',
                    'fulfillment_channel' => $item['fulfillment_channel'] ?? '',
                    'fba_storage_quantity_list' => json_encode($item['fba_storage_quantity_list'] ?? [], JSON_UNESCAPED_UNICODE),
                    
                    // 系统字段
                    'sync_date' => $sync_date,
                ];
                
                // 检查是否已存在相同记录（根据关键字段判断：asin + seller_sku）
                $where = "where name =:name and sid = :sid and asin = :asin and fnsku = :fnsku and seller_sku = :seller_sku and sku = :sku";
                $where_params = [
                    'name' => $save_data['name'],
                    'sid' => $save_data['sid'],
                    'asin' => $save_data['asin'],
                    'fnsku' => $save_data['fnsku'],
                    'seller_sku' => $save_data['seller_sku'],
                    'sku' => $save_data['sku'],
                ];
                $exists = $erp_db->table('lingxing_fba_storage_detail')
                    ->where($where, $where_params)
                    ->count();
                
                if ($exists > 0) {
                    // 更新已存在的记录
                    $erp_db->table('lingxing_fba_storage_detail')
                        ->where($where, $where_params)->update($save_data);
                } else {
                    // 插入新记录
                    $erp_db->table('lingxing_fba_storage_detail')->insert($save_data);
                }
                
                $success_count++;
            }
            
            $erp_db->commit();
            return $success_count;
            
        } catch (\Exception $e) {
            $erp_db->rollBack();
            log::lingXingApi('FbaStorageDetail')->error('保存FBA库存明细数据失败：' . $e->getMessage());
            throw new \Exception('保存数据失败：' . $e->getMessage());
        }
    }

    /**
     * 保存利润统计MSKU数据
     * @param array $dataList API返回的数据列表
     * @param string $dataDate 数据日期
     * @return array 保存结果
     */
    public static function saveProfitMskuData($dataList, $dataDate)
    {
        if (empty($dataList)) {
            return ['success_count' => 0, 'error_list' => []];
        }

        try {
            // 引入模型
            require_once __DIR__ . '/../../plugins/logistics/models/profitStatisticsMskuModel.php';
            $model = new \plugins\logistics\models\profitStatisticsMskuModel();

            // 调用模型保存数据
            $result = $model->saveProfitData($dataList, $dataDate);

            // 记录日志
            log::lingXingApi('ProfitMsku')->info("利润统计MSKU数据保存完成，日期: {$dataDate}，成功: {$result['success_count']}条，失败: " . count($result['error_list']) . "条");

            // 如果有错误，记录详细错误信息
            if (!empty($result['error_list'])) {
                foreach ($result['error_list'] as $error) {
                    log::lingXingApi('ProfitMsku')->error("数据保存失败，索引: {$error['index']}，错误: {$error['error']}", $error['data']);
                }
            }

            return $result;

        } catch (\Exception $e) {
            log::lingXingApi('ProfitMsku')->error('保存利润统计MSKU数据异常：' . $e->getMessage());
            return [
                'success_count' => 0,
                'error_list' => [
                    [
                        'index' => 0,
                        'data' => $dataList,
                        'error' => '系统异常：' . $e->getMessage()
                    ]
                ]
            ];
        }
    }

}
