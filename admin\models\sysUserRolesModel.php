<?php
namespace admin\models;
use core\lib\db\dbMysql;

/**
 * @author: zhangguoming
 * @Time: 2025/3/24 14:28
 */
class sysUserRolesModel
{
    //根据用户id获取用户角色权限
    public static function getAuthByQuserId($qwuser_id) {
        $db = dbMysql::getInstance();
        $list = $db->table('sys_user_roles','a')
            ->where('qwuser_id = :qwuser_id and a.status = 1 and a.is_delete = 0',['qwuser_id'=>$qwuser_id])
            ->leftJoin('sys_role','b','b.id = a.role_id')
            ->field('a.role_id,b.role_name,b.auth,b.type')
            ->list();
        $data = [
            'auth'=>'',
            'role_name'=>'',
            'role_type'=>[],
        ];
        if (count($list)) {
            $role_name = [];
            $auth = [];
            $role_type = [];
            foreach ($list as $v) {
                $role_name[] = $v['role_name'];
                //如果$v['auth']为'设为空'，则$v['auth']为空数组
                $v['auth'] = empty($v['auth'])?[]:$v['auth'];
                $auth = array_merge($auth,json_decode($v['auth']));
                if (!in_array($v['type'],$role_type)) {
                    $role_type[] = $v['type'];
                }
            }
            $data['role_name'] = implode(',',$role_name);
            $auth = array_unique($auth);
            $auth = array_values($auth);
            $data['auth'] = json_encode($auth);
            $data['role_type'] = $role_type;
        }
        return $data;
    }
}