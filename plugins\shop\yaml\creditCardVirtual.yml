openapi: 3.0.0
info:
  title: 虚拟信用卡管理API
  version: 1.0.0
  description: 提供虚拟信用卡管理的相关接口
paths:
  /shop/creditCardVirtual/getList:
    get:
      tags:
        - CreditCardVirtual
      summary: 获取虚拟信用卡列表
      description: 根据条件筛选获取虚拟信用卡列表
      parameters:
        - name: card_number
          in: query
          description: 卡号
          required: false
          schema:
            type: string
        - name: activation_platform
          in: query
          description: 开卡平台
          required: false
          schema:
            type: string
        - name: main_account
          in: query
          description: 主账号
          required: false
          schema:
            type: string
        - name: shop_name
          in: query
          description: 店铺名称
          required: false
          schema:
            type: string
        - name: service_provider
          in: query
          description: 对接服务商
          required: false
          schema:
            type: string
        - name: use_status
          in: query
          description: 使用状态
          required: false
          schema:
            type: string
        - name: currency
          in: query
          description: 币种
          required: false
          schema:
            type: string
        - name: shop_status
          in: query
          description: 店铺状态
          required: false
          schema:
            type: string
        - name: validity_period
          in: query
          description: 有效期
          required: false
          schema:
            type: string
        - name: activation_date
          in: query
          description: 开卡日期
          required: false
          schema:
            type: string
            format: date
        - name: bind_shop_date
          in: query
          description: 绑定店铺日期
          required: false
          schema:
            type: string
            format: date
        - name: update_time
          in: query
          description: 更新时间
          required: false
          schema:
            type: string
            format: date-time
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/CreditCardVirtual'
                      total:
                        type: integer
                        description: 总记录数
                      page:
                        type: integer
                        description: 当前页码
                      page_size:
                        type: integer
                        description: 每页条数

  /shop/creditCardVirtual/add:
    post:
      tags:
        - CreditCardVirtual
      summary: 添加虚拟信用卡
      description: 新增虚拟信用卡信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreditCardVirtualCreate'
      responses:
        '200':
          description: 添加成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 添加成功
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 新增记录ID

  /shop/creditCardVirtual/edit:
    post:
      tags:
        - CreditCardVirtual
      summary: 编辑虚拟信用卡
      description: 修改虚拟信用卡信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreditCardVirtualEdit'
      responses:
        '200':
          description: 编辑成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 编辑成功

  /shop/creditCardVirtual/detail:
    get:
      tags:
        - CreditCardVirtual
      summary: 获取虚拟信用卡详情
      description: 根据ID获取虚拟信用卡详细信息
      parameters:
        - name: id
          in: query
          description: 虚拟信用卡ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回详情数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    $ref: '#/components/schemas/CreditCardVirtual'

  /shop/creditCardVirtual/import:
    post:
      tags:
        - CreditCardVirtual
      summary: 批量导入虚拟信用卡
      description: 通过Excel文件批量导入虚拟信用卡数据
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                excel_src:
                  type: string
                  description: Excel文件链接
              required:
                - excel_src
      responses:
        '200':
          description: 导入成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 导入成功
                  data:
                    type: object
                    properties:
                      error_file:
                        type: string
                        description: 错误数据文件路径
                      error_count:
                        type: integer
                        description: 错误数量
                      success_count:
                        type: integer
                        description: 成功数量
                      data:
                        type: array
                        description: 导入结果数据

  /shop/creditCardVirtual/export:
    post:
      tags:
        - CreditCardVirtual
      summary: 导出虚拟信用卡数据
      description: 根据筛选条件导出虚拟信用卡数据
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                card_number:
                  type: string
                  description: 卡号
                activation_platform:
                  type: string
                  description: 开卡平台
                main_account:
                  type: string
                  description: 主账号
                service_provider:
                  type: string
                  description: 对接服务商
      responses:
        '200':
          description: 导出成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 导出成功
                  data:
                    type: object
                    properties:
                      file:
                        type: string
                        description: 导出文件路径

  /shop/creditCardVirtual/getLog:
    get:
      tags:
        - CreditCardVirtual
      summary: 获取操作日志
      description: 获取虚拟信用卡的操作日志记录
      parameters:
        - name: id
          in: query
          description: 虚拟信用卡ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回日志数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          description: 日志ID
                        table_name:
                          type: string
                          description: 表名
                        table_id:
                          type: integer
                          description: 记录ID
                        before_data:
                          type: object
                          description: 修改前数据
                        after_data:
                          type: object
                          description: 修改后数据
                        operator:
                          type: integer
                          description: 操作人ID
                        created_at:
                          type: string
                          format: date-time
                          description: 创建时间
                        update_time:
                          type: string
                          format: date-time
                          description: 更新时间

  /shop/creditCardVirtual/editBatch:
    post:
      tags:
        - CreditCardVirtual
      summary: 批量编辑虚拟信用卡
      description: 批量修改虚拟信用卡信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: array
                  items:
                    $ref: '#/components/schemas/CreditCardVirtualEdit'
                  description: 编辑数据数组
              required:
                - data
      responses:
        '200':
          description: 批量编辑成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 批量编辑成功

components:
  schemas:
    CreditCardVirtual:
      type: object
      properties:
        id:
          type: integer
          description: ID
        card_number:
          type: string
          description: 卡号
        activation_platform:
          type: string
          description: 开卡平台
        main_account:
          type: string
          description: 主账号
        use_platform:
          type: string
          description: 使用平台
        validity_period:
          type: string
          description: 有效期
        security_code:
          type: string
          description: 安全码
        currency:
          type: string
          description: 币种
        use_status:
          type: string
          description: 使用状态
        service_provider:
          type: string
          description: 对接服务商
        remark:
          type: string
          description: 备注

    CreditCardVirtualCreate:
      type: object
      required:
        - card_number
        - activation_date
        - activation_platform
        - main_account
        - use_platform
        - validity_period
        - currency
        - use_status
        - service_provider
      properties:
        card_number:
          type: string
          description: 卡号
        activation_date:
          type: string
          description: 开卡日期
          format: date
        activation_platform:
          type: string
          description: 开卡平台
        main_account:
          type: string
          description: 主账号
        use_platform:
          type: string
          description: 使用平台
        validity_period:
          type: string
          description: 有效期
        security_code:
          type: string
          description: 安全码
        currency:
          type: string
          description: 币种
        use_status:
          type: string
          description: 使用状态
        service_provider:
          type: string
          description: 对接服务商
        remark:
          type: string
          description: 备注

    CreditCardVirtualEdit:
      type: object
      required:
        - id
      properties:
        id:
          type: integer
          description: 虚拟信用卡ID
        card_number:
          type: string
          description: 卡号
        use_status:
          type: string
          description: 使用状态
        activation_platform:
          type: string
          description: 开卡平台
