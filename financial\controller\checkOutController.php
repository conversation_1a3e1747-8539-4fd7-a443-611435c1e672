<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/3 14:56
 */

namespace financial\controller;

use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use core\lib\rediskeys;
use financial\form\checkoutForm;
use financial\form\messagesFrom;
use financial\form\runShellTaskForm;
use financial\models\userModel;

class checkOutController
{
    //获取为结算列表
    public function getNotLockListMdate() {
        $paras_list = ['year'];
        $request_list = ['year' => '年份id'];
        // 组织参数
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $year = (int)$param['year'];
        $db = dbFMysql::getInstance();
        $list  = $db->table('checkout')
            ->where('where m_date LIKE :year', ['year' => "$year%"])
            ->list();
        returnSuccess($list);
    }
    //获取结账日志
    public function getLogList() {
        // 定义所需参数列表
        $paras_list = ['id'];
        $request_list = ['id' => 'ID'];
        // 组织参数
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $db = dbFMysql::getInstance();
        $mounth_id = $param['id'];

        // 从数据库中获取 log_data 字段的数据
        $list = $db->table('checkout')
            ->field('log_data')
            ->where('where id = :id', ['id' => $mounth_id])
            ->list();

        // 解析 log_data 字段
        $log_data = [];
        if (!empty($list) && isset($list[0]['log_data'])) {
            $log_data = json_decode($list[0]['log_data'], true);
        }
//        dd($log_data);
        // 直接返回 log_data 的内容
        returnSuccess($log_data, '获取日志成功！');
    }
    //反结账
    public function unLock() {
        // 定义所需参数列表
        $paras_list = ['id'];
        $request_list = ['id' => 'ID'];
        // 组织参数
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $month = $param['id'];
        $db = dbFMysql::getInstance();
        // 获取修改人姓名
        $user_id = userModel::$qwuser_id;
        $dby = dbMysql::getInstance();
        $checkout_info = $db->table('checkout')
            ->where('where id = :id', ['id' => $month])
            ->one();
        if ($checkout_info['is_lock'] == 0) {
            returnError('该月已反结账，切勿重复操作');
        }
        $yh = $dby->table('qwuser')
            ->field('id, wname')
            ->where('where id = :id', ['id' => $user_id])
            ->list();
        $user_name = $yh[0]['wname'] ?? '未知用户';
        // 获取当前时间
        $current_time = date('Y-m-d H:i:s');
        // 解析现有日志数据
        $existing_logs = json_decode($checkout_info['log_data'], true) ?? [];
        // 新的日志数据
        $new_log = [
            'time' => $current_time,
            'name' => $user_name,
            'operate' => '反结账'
        ];
        // 添加新的日志记录
        $existing_logs[] = $new_log;
        $new_log_data = json_encode($existing_logs, JSON_UNESCAPED_UNICODE);
        $db->table('checkout')
            ->where('where id = :id', ['id' => $month])
            ->update(['is_lock' => 0, 'log_data' => $new_log_data]);
        //删除队列
        $redis = (new \core\lib\predisV())::$client;
        $redis_key = rediskeys::$oa_count_msku_month;;
        $redis->del($redis_key);
        //消息通知
        $msg_text = $checkout_info['m_date'].'的数据已反结账，可对当前月数据进行修改。';
        messagesFrom::senMsgByNoticeType(4,$msg_text,$month);
        returnSuccess('', '反结账成功！');
    }
    //结账
    public function lock() {
        // 定义所需参数列表
        $paras_list = ['id'];
        $request_list = ['id' => '月份'];
        // 组织参数
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $month = $param['id'];
        //查看是否在结帐中
        $redis = (new \core\lib\predisV())::$client;
        $redis_key = rediskeys::$oa_count_msku_month;
        if ($redis->exists($redis_key)) {
            $data = json_decode($redis->get($redis_key),true);
            returnError($data['message']);
        }
        //验证其他操作是否都已经完成
        checkoutForm::verifyQueue($redis,$month);
        $db = dbFMysql::getInstance();
        $checkout_info = $db->table('checkout')
            ->where('where id = :id', ['id' => $month])
            ->field('id,m_date,is_lock,log_data')
            ->one();
        if ($checkout_info['is_lock'] == 1) {
            returnError('该月已结账，切勿重复操作');
        }
        // 获取修改人姓名
        $user_id = userModel::$qwuser_id;
        $dby = dbMysql::getInstance();
        $yh = $dby->table('qwuser')
            ->field('id, wname')
            ->where('where id = :id', ['id' => $user_id])
            ->list();
        $user_name = $yh[0]['wname'] ?? '未知用户';
        // 获取当前时间
        $current_time = date('Y-m-d H:i:s');
        // 解析现有日志数据
        $existing_logs = json_decode($checkout_info['log_data'], true) ?? [];
        // 新的日志数据
        $new_log = [
            'time' => $current_time,
            'name' => $user_name,
            'operate' => '结账'
        ];
        // 添加新的日志记录
        $existing_logs[] = $new_log;
        $new_log_data = json_encode($existing_logs, JSON_UNESCAPED_UNICODE);

        $db->table('checkout')
            ->where('where id = :id', ['id' => $month])
            ->update(['is_lock' => 2, 'log_data' => $new_log_data]);
        //结账通知消息通知
        $msg_text = $checkout_info['m_date'].'的数据已结账，不可再对当前月数据进行修改。';
        messagesFrom::senMsgByNoticeType(4,$msg_text,$month);
        $r_data = [
            'month'=>$checkout_info['m_date'],
            'message'=>$month.'月数据结账中，当前进度统计msku报表数据'
        ];
        $redis->set($redis_key,json_encode($r_data));
        $redis->expire($redis_key,4*60*60);
        //已最新维度生成本月数据，在以生成的数据来计算自定义字段
        runShellTaskForm::countMskuMonth();
        returnSuccess('', '结账成功！');
    }
}
