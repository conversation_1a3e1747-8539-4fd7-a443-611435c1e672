<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/24 9:57
 */

namespace core\jobs;

use core\lib\config;
use core\lib\db\dbFMysql;
use financial\form\mskuReportForm;

class exportMskuReportImportErrorDataJobs //暂未使用
{
    public string $unqueid = '';
    public string $key = '';//到表得redis_key
    public string $import_id = '';//到表得导入记录id
    public string $page;//到表得redis_key
    public function __construct($key,$import_id,$page){
        $this->key = $key;
        $this->import_id = $import_id;
        $this->page = $page;
        $this->unqueid = uniqid();
    }
    public function task(){
        $import_id = $this->import_id;
        $redis_key = $this->key;
        $page = $this->page;
        $redis = (new \core\lib\predisV())::$client;
        $export_data = json_decode($redis->get($redis_key),true);
        $db = dbFMysql::getInstance();
        $data = $db->table('msku_report_import_error_log')
            ->where('where import_id=:import_id',['import_id'=>$import_id])
            ->pages($page,10000);
        if (count($data['list'])) {
            $url = mskuReportForm::exportErrorData($data['list']);
            $export_data['success_count'] = $export_data['success_count']+count($data['list']);
            $export_data['excel_url'][] = $url;
            if ($export_data['success_count'] < $export_data['total']) {
                $queue_key = config::get('delay_queue_key', 'app');
                $page++;
                $task = new exportMskuReportImportErrorDataJobs($redis_key,$import_id,$page); // 创建任务类实例
                $redis->zAdd($queue_key, [], 0, serialize($task));
            } else {
                //保存
                //生成压缩包
                $save_path = '/public_financial/downLoad/msku_report/import_error';
                if (!file_exists(SELF_FK.$save_path)) {
                    mkdir(SELF_FK.$save_path, 0777, true);
                }
                $zip_url = $save_path ."/".date('YmdHis').'.zip';
                //生成压缩包
                if (setZipByUrl($export_data['excel_url'],$zip_url)) {
                    //保存压缩包地址
                    $db->table('msku_report_import')
                        ->where('where id=:id',['id'=>$import_id])
                        ->update(['error_zip_url'=>$zip_url]);
                }
                $export_data['zip_url'] = $zip_url;
            }
            $redis->set($redis_key,json_encode($export_data));
            $redis->expire($redis_key,60*60);
        }
    }
}