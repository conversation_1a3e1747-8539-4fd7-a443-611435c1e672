openapi: 3.0.0
info:
  title: 域名管理API
  version: 1.0.0
  description: 提供域名管理的相关接口
paths:
  /shop/domain/getList:
    get:
      tags:
        - Domain
      summary: 获取域名列表
      description: 根据条件筛选获取域名列表
      parameters:
        - name: domain
          in: query
          description: 域名
          required: false
          schema:
            type: string
        - name: domain_platform
          in: query
          description: 域名平台
          required: false
          schema:
            type: string
        - name: brand_name
          in: query
          description: 品牌名称
          required: false
          schema:
            type: string
        - name: user_id
          in: query
          description: 用户ID
          required: false
          schema:
            type: string
        - name: status
          in: query
          description: 状态
          required: false
          schema:
            type: integer
        - name: dep_id
          in: query
          description: 部门ID
          required: false
          schema:
            type: integer
        - name: purchase_date
          in: query
          description: 购买时间
          required: false
          schema:
            type: string
            format: date
        - name: expire_date
          in: query
          description: 到期时间
          required: false
          schema:
            type: string
            format: date
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/Domain'
                      total:
                        type: integer
                        description: 总记录数
                      page:
                        type: integer
                        description: 当前页码
                      page_size:
                        type: integer
                        description: 每页条数

  /shop/domain/apply:
    post:
      tags:
        - Domain
      summary: 申请域名
      description: 提交域名申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - dep_id
              properties:
                dep_id:
                  type: integer
                  description: 部门ID
                remark:
                  type: string
                  description: 备注
      responses:
        '200':
          description: 申请成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 申请成功
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 新增记录ID

  /shop/domain/edit:
    post:
      tags:
        - Domain
      summary: 录入/编辑域名信息
      description: 修改域名信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DomainEdit'
      responses:
        '200':
          description: 编辑成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 编辑成功
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 域名ID

  /shop/domain/apply_renewal:
    post:
      tags:
        - Domain
      summary: 申请域名续费
      description: 提交域名续费申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
              properties:
                id:
                  type: integer
                  description: 域名ID
      responses:
        '200':
          description: 申请成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 申请成功
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 域名ID

  /shop/domain/audit_renewal:
    post:
      tags:
        - Domain
      summary: 审核域名续费
      description: 处理域名续费申请
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - status
              properties:
                id:
                  type: integer
                  description: 域名ID
                status:
                  type: integer
                  description: 审核状态(1-通过，其他-拒绝)
                remark:
                  type: string
                  description: 审核备注
      responses:
        '200':
          description: 审核成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 审核成功
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 域名ID

  /shop/domain/confirm_renewal:
    post:
      tags:
        - Domain
      summary: 确认域名续费
      description: 确认域名续费信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DomainRenewal'
      responses:
        '200':
          description: 确认成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 确认成功
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 域名ID

  /shop/domain/detail:
    get:
      tags:
        - Domain
      summary: 获取域名详情
      description: 根据ID获取域名详细信息
      parameters:
        - name: id
          in: query
          description: 域名ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回详情数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    $ref: '#/components/schemas/Domain'

  /shop/domain/import:
    post:
      tags:
        - Domain
      summary: 批量导入域名
      description: 通过Excel文件批量导入域名数据
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                excel_src:
                  type: string
                  description: Excel文件链接
              required:
                - excel_src
      responses:
        '200':
          description: 导入成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 导入成功
                  data:
                    type: object
                    properties:
                      error_file:
                        type: string
                        description: 错误数据文件路径
                      error_count:
                        type: integer
                        description: 错误数量
                      success_count:
                        type: integer
                        description: 成功数量
                      data:
                        type: array
                        description: 导入结果数据

  /shop/domain/export:
    post:
      tags:
        - Domain
      summary: 导出域名数据
      description: 根据筛选条件导出域名数据
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                domain:
                  type: string
                  description: 域名
                domain_platform:
                  type: string
                  description: 域名平台
                status:
                  type: integer
                  description: 状态
                dep_id:
                  type: integer
                  description: 部门ID
      responses:
        '200':
          description: 导出成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 导出成功
                  data:
                    type: object
                    properties:
                      file:
                        type: string
                        description: 导出文件路径

  /shop/domain/getLog:
    get:
      tags:
        - Domain
      summary: 获取操作日志
      description: 获取域名的操作日志记录
      parameters:
        - name: id
          in: query
          description: 域名ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回日志数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          description: 日志ID
                        table_name:
                          type: string
                          description: 表名
                        table_id:
                          type: integer
                          description: 记录ID
                        before_data:
                          type: object
                          description: 修改前数据
                        after_data:
                          type: object
                          description: 修改后数据
                        operator:
                          type: integer
                          description: 操作人ID
                        created_at:
                          type: string
                          format: date-time
                          description: 创建时间
                        update_time:
                          type: string
                          format: date-time
                          description: 更新时间

  /shop/domain/editBatch:
    post:
      tags:
        - Domain
      summary: 批量编辑域名
      description: 批量修改域名信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: array
                  items:
                    $ref: '#/components/schemas/DomainBatchEdit'
                  description: 编辑数据数组
              required:
                - data
      responses:
        '200':
          description: 批量编辑成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 批量编辑成功

components:
  schemas:
    Domain:
      type: object
      properties:
        id:
          type: integer
          description: ID
        domain:
          type: string
          description: 域名
        domain_platform:
          type: string
          description: 域名平台
        brand_name:
          type: string
          description: 品牌名称
        status:
          type: integer
          description: 状态
        purchase_date:
          type: string
          description: 购买时间
          format: date
        expire_date:
          type: string
          description: 到期时间
          format: date
        valid_days:
          type: integer
          description: 有效天数
        cost:
          type: number
          description: 费用
        remark:
          type: string
          description: 备注

    DomainEdit:
      type: object
      required:
        - id
        - domain
        - domain_platform
        - purchase_date
        - expire_date
      properties:
        id:
          type: integer
          description: 域名ID
        domain:
          type: string
          description: 域名
        domain_platform:
          type: string
          description: 域名平台
        purchase_date:
          type: string
          description: 购买时间
          format: date
        expire_date:
          type: string
          description: 到期时间
          format: date

    DomainRenewal:
      type: object
      required:
        - id
        - cost
        - purchase_date
        - expire_date
        - valid_days
      properties:
        id:
          type: integer
          description: 域名ID
        cost:
          type: number
          description: 续费费用
        purchase_date:
          type: string
          description: 购买时间
          format: date
        expire_date:
          type: string
          description: 到期时间
          format: date
        valid_days:
          type: integer
          description: 有效天数

    DomainBatchEdit:
      type: object
      required:
        - id
      properties:
        id:
          type: integer
          description: 域名ID
        domain:
          type: string
          description: 域名
        domain_platform:
          type: string
          description: 域名平台
        status:
          type: integer
          description: 状态
