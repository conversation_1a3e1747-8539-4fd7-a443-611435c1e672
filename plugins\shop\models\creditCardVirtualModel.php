<?php

namespace plugins\shop\models;

class creditCardVirtualModel extends creditCardBaseModel
{
    // 虚拟卡特有字段
    public static array $paras_list = [
        // 基础字段
        "card_number"         => "信用卡号|required",
        "validity_period"     => "有效期|required",
        "security_code"       => "安全码(CVC)",
        "remark"              => "备注",
        // 虚拟卡特有字段
        "activation_platform" => "开卡平台|required",
        "receive_account_id"  => "收款账户id|required",
        "use_platform"        => "使用平台",
        "currency"            => "币种|required",
        "use_status"          => "使用状态|required",
        "service_provider"    => "对接服务商|required",
        "activation_date"     => "开卡日期|required",
    ];

    // 获取卡类型 - 虚拟卡
    protected function getCardType(): int
    {
        return 2;
    }

    // 获取特定类型的查询条件
    protected function getSpecificListCondition($param)
    {
        // 开卡平台
        if (!empty($param['activation_platform'])) {
            $this->db->andWhere('activation_platform = :activation_platform', ['activation_platform' => $param['activation_platform']]);
        }
        // 主账户（收款账户id）
        if (!empty($param['main_account'])) {
            $this->db->andWhere('ra.account_name = :main_account', ['main_account' => $param['main_account']]);
        }
        // 使用平台
        if (!empty($param['use_platform'])) {
            $or_arr = [];
            $or_data_arr = [];
            $k = 0;
            foreach ($param['use_platform'] as $use_platform) {
                $or_arr[] = "JSON_CONTAINS(c.use_platform, :use_platform_{$k})";
                $or_data_arr["use_platform_{$k}"] = json_encode([$use_platform]);
                $k++;
            }
            $this->db->andWhere('('.implode(' or ', $or_arr).')', $or_data_arr);
        }
        // 币种
        if (!empty($param['currency'])) {
            $this->db->andWhere('c.currency = :currency', ['currency' => $param['currency']]);
        }
        // 使用状态
        if (!empty($param['use_status'])) {
            $this->db->andWhere('c.use_status = :use_status', ['use_status' => $param['use_status']]);
        }
        // 对接服务商
        if (!empty($param['service_provider'])) {
            $this->db->andWhere('service_provider = :service_provider', ['service_provider' => $param['service_provider']]);
        }
        // 开卡日期
        if (!empty($param['activation_date'])) {
            $this->db->andWhere('activation_date >= :activation_date_start AND activation_date <= :activation_date_end', [
                'activation_date_start' => $param['activation_date'][0],
                'activation_date_end'   => $param['activation_date'][1],
            ]);
        }
    }
}
