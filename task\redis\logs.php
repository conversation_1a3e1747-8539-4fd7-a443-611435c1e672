<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/26 15:08
 */
class logs
{
    public static function sendMessageToQwLog($data,$type)
    {
        $path = dirname(__FILE__).'/../../log/sendMessage/';
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $file = $path . date('Ymd').'.log';

        $str = date('Y-m-d H:i:s').'【'.$type.'】: '.$data;
        if (!file_exists($file)) {
            file_put_contents($file, $str.PHP_EOL);
        } else {
            file_put_contents($file, $str.PHP_EOL, FILE_APPEND);
        }
    }
}


