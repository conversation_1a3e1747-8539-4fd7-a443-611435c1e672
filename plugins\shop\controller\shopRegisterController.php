<?php
namespace plugins\shop\controller;

use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use plugins\shop\form\messagesFrom;
use plugins\shop\models\companyModel;
use plugins\shop\models\legalPersonModel;
use plugins\shop\models\shopApplyModel;
use plugins\shop\models\shopRegisterModel;

class shopRegisterController extends baseController
{

    public function getStatistic()
    {
        $shopDb = dbShopMysql::getInstance();
        // 未分配店铺的需求
        $no_shop_apply = $shopDb->table('shop_apply')
            ->where(' where shop_id = 0 or shop_id is null')->count();

        // 未注册店铺的公司
        $shop_company = $shopDb->table('shop')->field('company_id')->list();
        $shop_company = array_column($shop_company, 'company_id');

        $shopDb->table('company');
        if (!empty($shop_company)) {
            $shopDb->andWhere('id not in ('.implode(',', $shop_company).')');
        }

        $no_shop_company = $shopDb->count();


        returnSuccess([
            'no_shop_apply' => $no_shop_apply ?: 0,
            'no_shop_company' => $no_shop_company ?: 0
        ]);
    }
    /**
     * 获取列表
     */
    public function getList()
    {
        $paras_list = array('shop_number', 'dep_id', 'country_site', 'register_type', 'register_status',
            'company_name', 'company_status', 'created_at', 'finished_at', 'page', 'page_size');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $param['page'] = $param['page'] ?? 1;
        $param['page_size'] = $param['page_size'] ?? 10;

        $model = new shopRegisterModel();
        $list = $model->getList($param);
        returnSuccess($list);
    }

    /**
     * 新增注册任务
     */
    public function add()
    {
        $param = $_POST;
        $id = $_POST['id'] ?? 0;
        $model = new shopRegisterModel();

        try {
            if ($param['register_device'] == '手机') {
                $param['email_id'] = null;
            } elseif ($param['register_device'] == '邮箱') {
                $param['phone_card_id'] = null;
            }
            $model->dataValidCheck($param);
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }

        // 唯一性校验
        $detail = $model->getByShopNumber($param['shop_number'], $id);
        if ($detail) {
            returnError('店铺编号已存在');
        }

        if ($id) {
            // 验证数据正确性
            $detail = $model->getById($id);
            if (!$detail) {
                returnError('数据不存在');
            }
            $model->edit($param, $id, $detail);
            returnSuccess([], '编辑成功');
        } else {
            $model->add($param);
            returnSuccess([], '添加成功');
        }
    }

    /**
     * 跟进注册任务
     */
    public function follow()
    {
        $paras_list = ['id', 'is_end', 'register_result', 'activation_date', 'register_date', 'remark', 'shop_apply_id'];
        $param = array_intersect_key($_POST, array_flip($paras_list));

        try {
            $model = new shopRegisterModel();
            $model->follow($param['id'], $param);
            returnSuccess([], '跟进成功');
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 重新注册
     */
    public function reRegister()
    {
        $param = $_POST;

        try {
            $model = new shopRegisterModel();
            $params = $model->dataValidCheck($param);
            $model->reRegister($params['id'], $params);
            returnSuccess([], '跟进成功');
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 暂停注册任务
     */
    public function pause()
    {
        $id = $_POST['id'] ?? 0;
        if (empty($id)) {
            returnError('id不能为空');
        }
        try {
            $model = new shopRegisterModel();
            $model->pause($id);
            returnSuccess([], '暂停成功');
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 取消暂停
     */
    public function cancelPause()
    {
        $id = $_POST['id'] ?? 0;
        if (empty($id)) {
            returnError('id不能为空');
        }
        try {
            $model = new shopRegisterModel();
            $model->cancelPause($id);
            returnSuccess([], '取消暂停成功');
        } catch (\Exception $e) {
            returnError($e->getMessage());
        }
    }

    /**
     * 催办
     */
    public function urge()
    {
        $id = $_POST['id'] ?? 0;
        if (empty($id)) {
            returnError('id不能为空');
        }
        $model = new shopRegisterModel();
        $detail = $model->getById($id);


        $shop_db = dbShopMysql::getInstance();
        $one = $shop_db->table('operation_log')
            ->where('table_name = :table_name and table_id = :table_id',
                ['table_name' => 'shop_register', 'table_id' => $id])
            ->order('id desc')
            ->one();
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_wid', 'user_id');
        $content = "请及时跟进【{$detail['shop_number']}】的店铺注册任务";
        messagesFrom::senMeg([$users[$one['operator']]], 1 , $content, $id, '', '店铺注册任务提醒');


        returnSuccess('催办成功');
    }


    /**
     * 获取详情
     */
    public function detail()
    {
        $id = $_GET['id'] ?? 0;
        if (empty($id)) {
            returnError('id不能为空');
        }

        $model = new shopRegisterModel();
        $maps = $model::getMaps();

        $detail = $model->getById($id);
        $detail = $model->formatItem($detail,$maps);

        $companyModel = new companyModel();
        $company = $companyModel->getById($detail['company_id']);
        $company = $companyModel->formatItem($company);
        $detail['company'] = $company;

        $legalPersonModel = new legalPersonModel();
        $legalPerson = $legalPersonModel->getById($detail['legal_person_id']);
        $legalPerson = $legalPersonModel->formatItem($legalPerson, $maps);
        $detail['legal_person'] = $legalPerson;

        // 拼接公司、法人
        returnSuccess($detail);
    }

    // 获取日志
    public static function getLog()
    {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            returnError('id不能为空');
        }

        $db = dbShopMysql::getInstance();
        $list = $db->table('operation_log')->where('where table_name = :table_name and table_id = :table_id',
            ['table_name' => 'shop_register', 'table_id' => $id])->order('id desc')->list();

        $model = new shopRegisterModel();
        $maps = $model::getMaps();
        $users = $maps['users'] ?? [];
        $shop_apply = $db->table('shop_apply')
            ->list();
        $shop_apply = array_column($shop_apply, null, 'id');

        foreach ($list as &$item) {
            $item['operator_name'] = $users[$item['operator']] ?? '';
            $attach = json_decode($item['attach'], true);
            $item['before'] = $attach['before'] ?? null;
            $item['before'] = $model->formatItem($item['before'], $maps);
            $item['after'] = $attach['after'] ?? null;
            $item['after'] = $model->formatItem($item['after'], $maps);
            if (isset($item['after']['shop_apply_id'])) {
                $item['after']['shop_apply'] = $shop_apply[$item['after']['shop_apply_id']] ?? null;
                $item['after']['shop_apply']['dep_name'] = $maps['deps'][$item['after']['shop_apply']['dep_id']] ?? '';
            }
            unset($item['attach']);
        }
        returnSuccess($list);
    }
}
