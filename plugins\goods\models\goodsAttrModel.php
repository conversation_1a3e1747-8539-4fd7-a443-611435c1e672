<?php
/**
 * @author: zhangguoming
 * @Time: 2024/2/5 11:05
 * TODO 1.0版本暂时未使用填写形式上传产品规格，而是通过上传excel文档形式上传规格书，所以暂时未使用该功能。
 */

namespace  plugins\goods\models;

use core\lib\config;
use core\lib\db\dbMysql;
use core\lib\log;

class goodsAttrModel
{
    private static array $old_data=[];
    private static array $new_data;
    //表格字段
    public static array $field_lsit = [
        'material'=>'材质',
        'color'=>'颜色',
        'size'=>'尺寸（L*W*H）',
        'weight'=>'重量',
        'waterproof_level'=>'防水等级',
        'rcb_type'=>'遥控器电池类型',
        'rcip'=>'遥控器绝缘保护',
        'rcs_time'=>'遥控器待机时间',
        'battery_type'=>'产品电池类型',
        'battery_capacity'=>'产品电池容量',
        'battery_lift'=>'产品电池寿命',
        'battery_protect'=>'电池保护',
        'charging_cs'=>'充电线规格',
        'battery_rv'=>'电池额定电压',
        'external_cv'=>'外接充电电压',
        'charging_i'=>'充电电流',
        'quiescent_i'=>'静态电流',
        'working_i'=>'工作电流',
        'motor_ms'=>'马达型号规格',
        'motor_data'=>'马达参数',
        'motor_lift'=>'马达寿命',
        'dental_box_type'=>'牙箱类型',
        'dental_box_lift'=>'牙箱寿命',
        'endurance'=>'产品续航时间',
        'charging_time'=>'充电时间',
        'auto_shutdowm_v'=>'自动关机电压',
        'noise_standards'=>'噪音标准',
        'working_heat'=>'工作温度',
        'stored_environment'=>'储藏温度/湿度',
        'silicone_hardness'=>'硅胶硬度',
        'key_lift'=>'按键寿命',
        'charging_plugging'=>'充电插拔',
        'authentication'=>'产品认证'
    ];
    private static string $field_str = '';
    //验证attr参数必传
    public static function verifyFormData($form_data){
        $update_date = [];
        foreach (self::$field_lsit as $k=>$field) {
            if (!isset($form_data[$k])) {
                SetReturn(-1,self::$field_lsit[$k].'必填');
            }
            $update_date[$k] = $form_data[$k];
            self::$field_str .= $k.',';
        }
        self::$field_str = trim(self::$field_str,',');
        return $update_date;
    }
    //填写表格
    public static function setGoodsAttr($goods_id, $update_data) {
        //验证表单必传信息
        self::$new_data = $update_data;
        //检查该事件的表格是否填写
        $db = dbMysql::getInstance();
        $db->table('goods_attr');
        $attr_info = $db->query('select id,'.self::$field_str.' from oa_goods_attr where goods_id = '.$goods_id.' limit 1');
        $attr_id = 0;
        if ($attr_info) {
            $attr_id = $attr_info['id'];
            self::$old_data = $attr_info;
            $update_data['updated_at'] = date('Y-m-d H:i:s');
            $db->table('goods_attr');
            $db->where('where id='.$attr_id);
            if (!$db->update($update_data)) {
                $db->rollBack();
                SetReturn(-1,'修改产品规格书失败');
            }
            self::setAttrlog($goods_id);
        } else {
            $base_data = [
                'goods_id'=>$goods_id,
            ];
            $update_data['created_at'] = date('Y-m-d H:i:s');
            $update_data = array_merge($update_data,$base_data);
            $attr_id = $db->insert($update_data);
            if (!$attr_id) {
                $db->rollBack();
                SetReturn(-1,'保存产品规格书失败');
            }

        }
        return $attr_id;
    }

    //修改attr日志记录
    static private function setAttrlog($goods_id)
    {
        if (count(self::$old_data)) {
            $has_chaunge = 0;
            $mgs = '修改新品规格：';
            foreach (self::$new_data as $k=>$v) {
                if (isset(self::$old_data[$k]) && (self::$old_data[$k] != $v) && isset(self::$field_lsit[$k])) {
                    $mgs .= self::$field_lsit[$k].'：【'.$v.'】，';
                    $has_chaunge = 1;
                }
            }
            if ($has_chaunge == 1) {
                $mgs = trim($mgs, '，');
            } else {
                $mgs = '';
            }
            if (!empty($mgs)) {
                log::newGoodsLog($goods_id,0, $mgs,0);
            }
        }
    }

    /**
     * @param $goods_id 商品id
     * @param $hardware_test 产品硬件测试结果数据
     * @param $pdf_type  //pdf类型
     * @return string[]|void
     * @throws \core\lib\ExceptionError
     */
    static public function geGoodsAttrPdfHtml($goods_id, $hardware_test, $pdf_type)
    {
        $db = dbMysql::getInstance();
        $goods_attr = $db->query("select * from oa_goods_attr where goods_id = {$goods_id}");
        unset($goods_attr['id']);
        if (!$goods_attr) {
            SetReturn(-1, '未查询到商品规格书');
        } else {
            //内容
            $goods = $db->query('select * from oa_goods_new where id = ' . $goods_id);
            //流程和新品名称
            $title = $goods['goods_name'].'规格书';
            //填写的内容
            $html = '';
            $key = formGoodsAttrModel::$field_lsit;
            if ($pdf_type > 0) {
                $achieve_key = !empty($hardware_test['achieve_key'])?json_decode($hardware_test['achieve_key'],true):'';
            }
            foreach ($goods_attr as $k => $v) {
                if (isset($key[$k])) {
                    $html .= '<tr>';
                    $html .= "<td>$key[$k]</td>";
                    $html .= "<td>$v</td>";
                    if ($pdf_type > 0) {
                        if ($achieve_key) {
                            $html .= '<td class="gou">'.(isset($k,$achieve_key)?'通过':'').'</td>';
                        } else {
                            $html .= '<td class="gou"></td>';
                        }
                    }
                    $html .= '</tr>';
                }
            }
            return ['title' => $title, 'html' => $html];
        }
    }

    //规格书硬件测试(检查是否完成所有字段的测试)
    static public function getAchieveStatus($hardware_test){
        $achieve_key = !empty($hardware_test['achieve_key'])?json_decode($hardware_test['achieve_key'], true):[];
        if (count($achieve_key) < count(self::$field_lsit)) {
            return 0;
        } else {
            foreach (self::$field_lsit as $k=>$v){
                if (!in_array($k,$achieve_key)) {
                    return 0;
                }
            }
        }
        return 1;
    }
}