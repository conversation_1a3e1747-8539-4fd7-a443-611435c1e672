<?php

namespace plugins\goods\form;

use core\lib\db\dbMysql;
use plugins\goods\models\userModel;

class sopReviewerFrom
{
    //拉取列表
    public static function getList($param)
    {
        $db = dbMysql::getInstance();

        // 构建查询
        $db->table('imgs_request_checker', 'a')
            ->leftJoin('qwuser', 'q1', 'q1.id = a.user_id')
            ->left<PERSON>oin('qwuser', 'q2', 'q2.id = a.update_user_id')
            ->field('a.id,a.user_id,a.check_id,a.category_id,a.created_time,a.updated_time,a.country_code,a.type,a.group_name,a.wq_id,q1.wname as creator_name,q2.wname as update_name,a.update_user_id');

        // —— 类目（支持多选，JSON字段）——
        if (!empty($param['category_id']) && $param['category_id'] != '[]') {
            $db->andWhere('JSON_OVERLAPS(a.category_id, :category_id_json)', [
                'category_id_json' => $param['category_id']
            ]);
        }

        // —— 站点（支持多选，JSON字段）——
        if (!empty($param['country_code']) && $param['country_code'] != '[]') {
            $db->andWhere('JSON_OVERLAPS(a.country_code, :country_code_json)', [
                'country_code_json' => $param['country_code']
            ]);
        }


        // —— 负责人
        if (!empty($param['user_id']) && $param['user_id'] != '[]') {
            $db->andWhere('JSON_OVERLAPS(a.check_id, :user_json)', [
                'user_json' => $param['user_id']
            ]);
        }

        // —— 部门ID（wq_id，维持原样，非JSON字段）——
        if (!empty($param['wq_id']) && $param['wq_id'] != '[]') {
            $db->andWhere('JSON_OVERLAPS(a.wq_id, :wq_json)', [
                'wq_json' => $param['wq_id']
            ]);
        }

        // 未删除
        $db->andWhere('a.is_delete = 0');

        // 排序
        $db->order('a.id DESC');

        // 分页
        $result = $db->pages($param['page'], $param['page_size']);

        // 处理相关数据
        $result = self::getRelatedData($db, $result);

        returnSuccess($result);
    }


    // 处理相关数据
    public static function getRelatedData($db, &$result)
    {
        // 1. 取出列表
        $list = $result['list'] ?? [];

        // 2. 批量解码 JSON 字符串并合并去重
        // —— 类目
        $categoryJsons = array_column($list, 'category_id');
        $categoryArrays = $categoryJsons
            ? array_map(fn($s) => json_decode($s, true) ?: [], $categoryJsons)
            : [];
        $categoryIds = $categoryArrays
            ? array_unique(call_user_func_array('array_merge', $categoryArrays))
            : [];

        // —— 国家
        $countryJsons = array_column($list, 'country_code');
        $countryArrays = $countryJsons
            ? array_map(fn($s) => json_decode($s, true) ?: [], $countryJsons)
            : [];
        $countryCodes = $countryArrays
            ? array_unique(call_user_func_array('array_merge', $countryArrays))
            : [];

        // —— wq_id（如果你后续也需要）
        $wqJsons = array_column($list, 'wq_id');
        $wqArrays = $wqJsons
            ? array_map(fn($s) => json_decode($s, true) ?: [], $wqJsons)
            : [];
        $wqIds = $wqArrays
            ? array_unique(call_user_func_array('array_merge', $wqArrays))
            : [];

        // —— checker_id
        $checkerJsons = array_column($list, 'check_id');
        $checkerArrays = $checkerJsons
            ? array_map(fn($s) => json_decode($s, true) ?: (is_numeric($s) ? [(int)$s] : []), $checkerJsons)
            : [];
        $checkerIds = $checkerArrays
            ? array_unique(call_user_func_array('array_merge', $checkerArrays))
            : [];


        // 批量查询，创建映射
        // 类目
        $categoryMap = [];
        if ($categoryIds) {
            $categoryMap = $db->table('goods_cate')
                ->field('id,cate_name')
                ->whereIn('id', $categoryIds)
                ->list();
            $categoryMap = array_column($categoryMap, 'cate_name', 'id');
        }

        // 国家
        $countryMap = [];
        if ($countryCodes) {
            $countryMap = $db->table('market')
                ->field('code,country')
                ->whereIn('code', $countryCodes)
                ->list();
            $countryMap = array_column($countryMap, 'country', 'code');
        }
        $NONE = ['NONE'=>"无"];
        //合并数组
        $countryMap = array_merge($countryMap,$NONE);
        // 部门
        $wqMap = [];
        if ($wqIds) {
            $wqMap = $db->table('qwdepartment')
                ->field('wp_id,name')
                ->whereIn('wp_id', $wqIds)
                ->list();
            $wqMap = array_column($wqMap, 'name', 'wp_id');
        }

        //负责人
        $checkerMap = [];
        if ($checkerIds) {
            $checkerMap = $db->table('qwuser')
                ->field('id,wname')
                ->whereIn('id', $checkerIds)
                ->list();
            $checkerMap = array_column($checkerMap, 'wname', 'id');
        }
        // 处理时间格式和多选字段
        foreach ($result['list'] as &$item) {
            // 处理时间格式
            if (!empty($item['created_time'])) {
                $item['created_time'] = date('Y-m-d H:i:s', strtotime($item['created_time']));
            }
            if (!empty($item['updated_time'])) {
                $item['updated_time'] = date('Y-m-d H:i:s', strtotime($item['updated_time']));
            }

            // 处理多选字段的映射
            // 类目
            if (!empty($item['category_id'])) {
                $categoryIds = json_decode($item['category_id'], true);
                if (is_array($categoryIds)) {
                    $item['category_name'] = array_map(fn($id) => $categoryMap[$id] ?? $id, $categoryIds);
                } else {
                    $item['category_name'] = $categoryMap[$categoryIds] ?? $categoryIds;
                }
            }

            // 国家
            if (!empty($item['country_code'])) {
                $countryCodes = json_decode($item['country_code'], true);
                if (is_array($countryCodes)) {
                    $item['country_name'] = array_map(fn($code) => $countryMap[$code] ?? $code, $countryCodes);
                } else {
                    $item['country_name'] = $countryMap[$countryCodes] ?? $countryCodes;
                }
            }

            // 部门
            if (!empty($item['wq_id'])) {
                $wqIds = json_decode($item['wq_id'], true);
                if (is_array($wqIds)) {
                    $item['wq_name'] = array_map(fn($id) => $wqMap[$id] ?? $id, $wqIds);
                } else {
                    $item['wq_name'] = $wqMap[$wqIds] ?? $wqIds;
                }
            }

            //负责人
            if (!empty($item['check_id'])) {
                $checkerIds = json_decode($item['check_id'], true);
                if (is_array($checkerIds)) {
                    $item['checker_name'] = array_map(fn($id) => $checkerMap[$id] ?? $id, $checkerIds);
                } else {
                    $item['checker_name'] = $checkerMap[$checkerIds] ?? $checkerIds;
                }
            }
        }

        // 返回处理后的结果
        return $result;
    }
    /**
     * 删除SOP审核人配置
     * @param int $id 配置ID
     * @return array 操作结果
     */
    public static function deleteSopReviewer($id)
    {
        if (empty($id)) {
            returnError('参数错误，缺少ID');
        }

        $db = dbMysql::getInstance();

        try {
            // 开启事务
            $db->beginTransaction();

            // 查询配置是否存在
            $config = $db->table('imgs_request_checker')
                ->where('id = :id AND is_delete = 0', ['id' => $id])
                ->one();

            if (!$config) {
                returnError('配置不存在或已删除');
            }

            // 软删除（更新is_delete字段）
            $result = $db->table('imgs_request_checker')
                ->where('id = :id', ['id' => $id])
                ->update(['is_delete' => 1, 'updated_time' => date('Y-m-d H:i:s')]);

//            // 更新相关图片需求记录
//            $requests = $db->table('imgs_request')
//                ->where('request_checke_id = :id', ['id' => $id])
//                ->list();
//            if (!empty($requests)) {
//                self::updateSopRequestAndParticipant($db, $requests);
//            }
            // 提交事务
            $db->commit();

            returnSuccess('删除成功');
        } catch (Exception $e) {
            // 回滚事务
            $db->rollBack();
            returnError('删除失败：' . $e->getMessage());
        }
    }
    /**
     * 统一封装为 JSON 数组字符串（["xxx"] 形式）
     */
    private static function toJsonArray($input): string
    {
        if (is_array($input)) {
            return json_encode($input, JSON_UNESCAPED_UNICODE);
        }
        if (is_string($input) && str_starts_with($input, '[')) {
            return $input;
        }
        return json_encode([$input], JSON_UNESCAPED_UNICODE);
    }
    /**
     * 根据国家和类目匹配审核人配置
     * @param dbMysql $db 数据库实例
     * @param string $country_code 国家编码
     * @param string $category_id 类目ID
     * @return array [check_id数组, 配置ID]
     */
    private static function matchCheckerConfig($db, $country_code, $category_id): array
    {
        $country_code = self::toJsonArray($country_code);

        // 先查询国家对应的配置
        $row = $db->table('imgs_request_checker')
            ->where(
                'JSON_OVERLAPS(country_code, :country_code) AND is_delete = 0',
                ['country_code' => $country_code]
            )
            ->one();

        if (empty($row)) {
            $result = $db->table('config')->where('key_name = "imgs_request_sop_checker"')->one();
            $row['check_id'] = $result['data'] ?? '[]';
            return [json_decode($row['check_id'], true) ?? [], 0];
        }

        // 如果category_id为[]，表示适用于所有类目，直接返回
        if ($row['category_id'] === '[]') {
            $ids = json_decode($row['check_id'], true);
            return [$ids ?? [], $row['id'] ?? 0];
        }

        // 否则需要检查类目是否匹配
        $row = $db->table('imgs_request_checker')
            ->where(
                'JSON_OVERLAPS(category_id, :category_id)
                 AND JSON_OVERLAPS(country_code, :country_code)
                 AND is_delete = 0',
                [
                    'category_id'  => $category_id, 
                    'country_code' => $country_code,
                ]
            )
            ->one();

        if (empty($row)) {
            $result = $db->table('config')->where('key_name = "imgs_request_sop_checker"')->one();
            $row['check_id'] = $result['data'] ?? '[]';
            return [json_decode($row['check_id'], true) ?? [], 0];
        }

        $ids = json_decode($row['check_id'], true);
        return [$ids ?? [], $row['id'] ?? 0];
    }

    /**
     * 新增或编辑SOP审核人配置
     * @param array $param 参数
     * @return array 操作结果
     */
    public static function saveSopReviewer($param)
    {
        $db = dbMysql::getInstance();

        // 构建数据
        $data = [];

        // 站点（支持多选）
        if (isset($param['country_code'])) {
            $data['country_code'] = is_array($param['country_code']) ? json_encode($param['country_code']) : $param['country_code'];
        }

        // 类目（支持多选）
        if (isset($param['category_id'])) {
            $data['category_id'] = is_array($param['category_id']) ? json_encode($param['category_id']) : $param['category_id'];
        }

        // 申请人部门（支持多选）
        if (isset($param['wq_id'])) {
            $data['wq_id'] = is_array($param['wq_id']) ? json_encode($param['wq_id']) : $param['wq_id'];
        }

        // 负责人（单选）
        if (isset($param['check_id'])) {
            $data['check_id'] = is_array($param['check_id']) ? json_encode($param['check_id']) : $param['check_id'];
        }
        // 如果没有需要保存的字段
        if (empty($data)) {
            returnError('没有需要保存的数据');
        }

        // 添加更新时间
        $data['updated_time'] = date('Y-m-d H:i:s');

        // 检查是否存在重复的类目配置
        if (!empty($data['country_code']) && !empty($data['category_id'])) {
            // 先检查数据库中是否存在该国家的配置
            $query = $db->table('imgs_request_checker')
                ->where('is_delete = 0')
                ->andWhere('JSON_OVERLAPS(country_code, :country_code)', [
                    'country_code' => $data['country_code']
                ]);

            // 如果是编辑操作，排除当前记录
            if (!empty($param['id'])) {
                $query->andWhere('id != :id', ['id' => $param['id']]);
            }

            $existingConfig = $query->one();

            if ($existingConfig) {
                // 如果数据库中存在该国家的配置
                if ($existingConfig['category_id'] === '[]') {
                    // 如果数据库中的配置是全选类目，则不允许新增
                    returnError('该站点下已存在全选类目的配置');
                } else if ($data['category_id'] !== '[]') {
                    // 如果当前配置不是全选类目，检查类目是否有重叠
                    $overlapConfig = $query->andWhere('JSON_OVERLAPS(category_id, :category_id)', [
                        'category_id' => $data['category_id']
                    ])->one();

                    if ($overlapConfig) {
                        returnError('该站点下已存在相同的类目配置');
                    }
                }
            }
        }

        try {
            // 开启事务
            $db->beginTransaction();

            // 判断是新增还是编辑
            if (empty($param['id'])) {
                // 新增
                // 添加创建时间
                $data['created_time'] = date('Y-m-d H:i:s');
                $data['user_id'] = userModel::$qwuser_id;
                $data['update_user_id'] = userModel::$qwuser_id;
                // 插入数据
                $id = $db->table('imgs_request_checker')
                    ->insert($data);
                $message = '新增成功';
                //根据站点和类目去主表查询匹配是否有对应的记录,如果有的话去修改对应的request_checke_id
                $info = $db->table('imgs_request')
                    ->where('country_code =:country_code AND JSON_OVERLAPS(category_id, :category_id)', [
                        'country_code' => $data['country_code'],
                        'category_id' => $data['category_id']
                    ])
                    ->one();
                if (!empty($info)){
                    $db->table('imgs_request')
                        ->where('country_code =:country_code AND JSON_OVERLAPS(category_id, :category_id)', [
                            'country_code' => $data['country_code'],
                            'category_id' => $data['category_id']
                        ])
                        ->update(['request_distributor_id'=>$id]);
                }
            } else {
                // 编辑
                $id = (int)$param['id'];
                $data['update_user_id'] = userModel::$qwuser_id;

                // 查询配置是否存在
                $config = $db->table('imgs_request_checker')
                    ->where('id = :id AND is_delete = 0', ['id' => $id])
                    ->one();

                if (!$config) {
                    returnError('配置不存在或已删除');
                }

                // 更新数据
                $db->table('imgs_request_checker')
                    ->where('id = :id', ['id' => $id])
                    ->update($data);
                $message = '更新成功';
//
//                // 更新相关图片需求记录
//                $requests = $db->table('imgs_request')
//                    ->where('request_checke_id =:id',['id' => $id])
//                    ->list();
//
//                if (!empty($requests)) {
//                    self::updateSopRequestAndParticipant($db, $requests);
//                }
            }

            // 提交事务
            $db->commit();

            returnSuccess('',$message);
        } catch (\Exception $e) {
            // 回滚事务
            $db->rollBack();
            returnError('操作失败：' . $e->getMessage());
        }
    }
    /**
     * 更新 SOP 审核请求和参与者记录
     * @param dbMysql $db 数据库实例
     * @param array $requests 需要更新的请求列表
     */
    private static function updateSopRequestAndParticipant($db, $requests)
    {
        // 收集所有需要更新的数据
        $updateData = [];
        $participantData = [];
        
        foreach ($requests as $request) {
            // 根据新的配置重新匹配审核人
            $checkerInfo = self::matchCheckerConfig($db, $request['country_code'], $request['category_id']);
            
            // 收集更新数据
            $updateData[] = [
                'id' => $request['id'],
                'request_checke_id' => $checkerInfo[1]
            ];
            
            // 收集参与者数据
            $participantData[] = [
                'request_id' => $request['id'],
                'type' => 'sop审核人',
                'user_id' => $checkerInfo[0][0]
            ];
        }

        // 批量更新imgs_request表
        if (!empty($updateData)) {
            foreach ($updateData as $data) {
                $db->table('imgs_request')
                    ->where('id = :id and (status = 4 or status = 9)', ['id' => $data['id']])
                    ->update([
                        'request_checke_id' => $data['request_checke_id']
                    ]);
            }
        }

        // 批量处理参与者记录
        if (!empty($participantData)) {
            // 先删除所有相关记录
            $requestIds = array_column($participantData, 'request_id');
            $db->table('imgs_request_participant')
                ->whereIn('request_id', $requestIds)
                ->where('type = 2')
                ->delete();

            // 批量插入新记录
            foreach ($participantData as $data) {
                $db->table('imgs_request_participant')
                    ->insertIgnore([
                        'request_id' => $data['request_id'],
                        'user_id' => $data['user_id'],
                        'type' => $data['type']
                    ]);
            }
        }
    }

    //终线设置
    public static function getFinalLine($param)
    {
        $db = dbMysql::getInstance();
        $db->beginTransaction();
        
        try {
            // 更新配置表
            $db->table('config')
                ->where('key_name = "imgs_request_sop_checker"')
                ->update([
                    'data' => $param['user_ids'],
                    'updated_time' => date('Y-m-d H:i:s'),
                    'user_id' => userModel::$qwuser_id
                ]);
            
//            // 获取所有需要更新的请求
//            $requests = $db->table('imgs_request')
//                ->where('request_checke_id = 0')
//                ->list();
//
//            if (!empty($requests)) {
//                self::updateSopRequestAndParticipant($db, $requests);
//            }

            $db->commit();
            returnSuccess('', '配置成功');
        } catch (\Exception $e) {
            $db->rollBack();
            returnError('配置失败：' . $e->getMessage());
        }
    }
    //获取终线设置信息
    public static function getFinalLineCurrent()
    {
        $db = dbMysql::getInstance();
        $result = $db->table('config')->where('key_name = "imgs_request_sop_checker"')->one();
        returnSuccess($result,'' );
    }

}