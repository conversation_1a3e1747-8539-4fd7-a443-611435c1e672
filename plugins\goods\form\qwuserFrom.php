<?php
/**
 * @author: zhangguoming
 * @Time: 2024/3/27 9:44
 */

namespace  plugins\goods\form;

use core\lib\db\dbMysql;
use plugins\goods\models\userModel;

class qwuserFrom
{
    //获取用户权限信息
    public static function getUserAuth() {
        $db = dbMysql::getInstance();
        $list = $db->table('user_roles','a')
            ->leftJoin('role','b','b.id = a.role_id')
            ->where('where qwuser_id = :qwuser_id',['qwuser_id'=>userModel::$qwuser_id])
            ->field('a.role_id,b.role_name,b.auth,b.type')
            ->list();
        $data = [
            'auth'=>[],
            'role_name'=>'',
            'role_type'=>[],
        ];
        if (count($list)) {
            $role_name = [];
            $auth = [];
            $role_type = [];
            foreach ($list as $v) {
                $role_name[] = $v['role_name'];
                //如果$v['auth']为'设为空'，则$v['auth']为空数组
                $v['auth'] = empty($v['auth'])?[]:$v['auth'];
                $auth = array_merge($auth,json_decode($v['auth']));
                if (!in_array($v['type'],$role_type)) {
                    $role_type[] = $v['type'];
                }
            }
            $data['role_name'] = implode(',',$role_name);
            $auth = array_unique($auth);
            $auth = array_values($auth);
            $data['auth'] = $auth;
            $data['role_type'] = $role_type;
        }
        return $data;
    }
}