<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/8 16:29
 */

namespace  plugins\goods\form;

use plugins\goods\models\userModel;
use core\lib\db\dbMysql;

class goodsAbnormalFrom
{
    public static function setGoodsAbnormal($goods_id,$project_id,$level,$abnormal_type,$title,$manage_info,$project_abnormal_id=0) {
        $insert_data = [
            'user_id'=>userModel::$qwuser_id,
            'manage_info'=>$manage_info,
            'goods_id'=>$goods_id,
            'project_id'=>$project_id,
            'level'=>$level,
            'title'=>$title,
            'abnormal_type'=>$abnormal_type,
            'created_at'=>date('Y-m-d H:i:s'),
            'project_abnormal_id'=>$project_abnormal_id
        ];
        $db = dbMysql::getInstance();
        $db->table('goods_abnormal');
        $id = $db->insert($insert_data);
        return $id;
    }
    //修改问题状态
    public static function setStatus($id,$is_handled) {
        $db = dbMysql::getInstance();
        $db->table('goods_abnormal')->where('where id =:id',['id'=>$id]);
        $db->update([
            'is_handled'=>$is_handled,
            'updated_at'=>date('Y-m-d H:i:s')
        ]);
    }
}