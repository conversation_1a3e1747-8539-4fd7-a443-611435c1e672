<?php
/**
 * @author: fbaStorageDetail
 * @Time: 2025/6/20 09:10
 */

namespace plugins\logistics\form;

use core\lib\db\dbErpMysql;
use core\lib\log;

class fbaStorageDetailForm
{
    /**
     * 获取FBA库存明细列表
     * @param array $param
     * @return array
     */
    public function getFbaStorageDetailList($param)
    {

        $page = ($param['page'] ?? 1) ? intval($param['page']) : 1;
        $page_size = ($param['page_size'] ?? 20) ? intval($param['page_size']) : 20;

        $db = dbErpMysql::getInstance();
        
        // 构建查询
        $query = $db->table('lingxing_fba_storage_detail')->where('1=1');
        
        // 日期筛选
        if (!empty($param['sync_date'])) {
            $query->andWhere('and sync_date = :sync_date', ['sync_date' => $param['sync_date']]);
        } else {
            // 默认查询最近一天的数据
            $query->andWhere('and sync_date = :sync_date', ['sync_date' => date('Y-m-d')]);
        }
        
        // 搜索条件
        if (!empty($param['search_field']) && !empty($param['search_value'])) {
            switch ($param['search_field']) {
                case 'sku':
                    $query->andWhere('and sku LIKE :search_value', ['search_value' => '%' . $param['search_value'] . '%']);
                    break;
                case 'product_name':
                    $query->andWhere('and product_name LIKE :search_value', ['search_value' => '%' . $param['search_value'] . '%']);
                    break;
                case 'seller_sku':
                    $query->andWhere('and seller_sku LIKE :search_value', ['search_value' => '%' . $param['search_value'] . '%']);
                    break;
                case 'fnsku':
                    $query->andWhere('and fnsku LIKE :search_value', ['search_value' => '%' . $param['search_value'] . '%']);
                    break;
                case 'asin':
                    $query->andWhere('and asin LIKE :search_value', ['search_value' => '%' . $param['search_value'] . '%']);
                    break;
                default:
                    break;
            }
        }
        
        // 其他筛选条件
        if (!empty($param['cid'])) {
            $query->andWhere('and cid = :cid', ['cid' => $param['cid']]);
        }
        
        if (!empty($param['bid'])) {
            $query->andWhere('and bid = :bid', ['bid' => $param['bid']]);
        }
        
        if (!empty($param['sid'])) {
            $query->andWhere('and sid = :sid', ['sid' => $param['sid']]);
        }
        
        if (isset($param['share_type']) && $param['share_type'] !== '') {
            $query->andWhere('and share_type = :share_type', ['share_type' => $param['share_type']]);
        }
        
        // 是否隐藏零库存
        if (!empty($param['is_hide_zero_stock']) && $param['is_hide_zero_stock'] == '1') {
            $query->andWhere('and (total > 0 OR available_total > 0 OR afn_fulfillable_quantity > 0)');
        }
        
        // 配送方式筛选
        if (!empty($param['fulfillment_channel_type'])) {
            if ($param['fulfillment_channel_type'] == 'FBA') {
                $query->andWhere('and afn_fulfillable_quantity > 0');
            } elseif ($param['fulfillment_channel_type'] == 'FBM') {
                $query->andWhere('and quantity > 0');
            }
        }
        
        // 排序
        $sort_field = $param['sort_field'] ?? 'id';
        $sort_order = $param['sort_order'] ?? 'desc';
        
        // 验证排序字段
        $allowed_sort_fields = [
            'id', 'asin', 'product_name', 'seller_sku', 'fnsku', 'sku',
            'total', 'available_total', 'afn_fulfillable_quantity',
            'total_price', 'available_total_price', 'afn_fulfillable_quantity_price',
            'sync_date', 'created_at'
        ];
        
        if (!in_array($sort_field, $allowed_sort_fields)) {
            $sort_field = 'id';
        }
        
        if (!in_array(strtolower($sort_order), ['asc', 'desc'])) {
            $sort_order = 'desc';
        }
        
        $query->order($sort_field . ' ' . $sort_order);

        // 分页查询
        return $query->pages($page, $page_size);
    }
    
    /**
     * 获取FBA库存明细统计信息
     * @param array $param
     * @return array
     */
    public function getFbaStorageDetailStatistics($param)
    {
        $db = dbErpMysql::getInstance();
        
        // 构建查询
        $query = $db->table('lingxing_fba_storage_detail')->where('1=1');
        
        // 日期筛选
        if (!empty($param['sync_date'])) {
            $query->andWhere('and sync_date = :sync_date', ['sync_date' => $param['sync_date']]);
        } else {
            $query->andWhere('and sync_date = :sync_date', ['sync_date' => date('Y-m-d')]);
        }
        
        // 应用相同的筛选条件
        $this->applyFilters($query, $param);
        
        // 统计查询
        $statistics = $query->field(
            'COUNT(*) as total_count, ' .
            'SUM(total) as total_quantity, ' .
            'SUM(total_price) as total_amount, ' .
            'SUM(available_total) as available_quantity, ' .
            'SUM(available_total_price) as available_amount, ' .
            'SUM(afn_fulfillable_quantity) as fba_quantity, ' .
            'SUM(afn_fulfillable_quantity_price) as fba_amount, ' .
            'SUM(quantity) as fbm_quantity, ' .
            'SUM(quantity_price) as fbm_amount'
        )->one();
        
        return [
            'total_count' => intval($statistics['total_count'] ?? 0),
            'total_quantity' => intval($statistics['total_quantity'] ?? 0),
            'total_amount' => floatval($statistics['total_amount'] ?? 0),
            'available_quantity' => intval($statistics['available_quantity'] ?? 0),
            'available_amount' => floatval($statistics['available_amount'] ?? 0),
            'fba_quantity' => intval($statistics['fba_quantity'] ?? 0),
            'fba_amount' => floatval($statistics['fba_amount'] ?? 0),
            'fbm_quantity' => intval($statistics['fbm_quantity'] ?? 0),
            'fbm_amount' => floatval($statistics['fbm_amount'] ?? 0)
        ];
    }
    
    /**
     * 应用筛选条件
     * @param $query
     * @param array $param
     */
    private function applyFilters($query, $param)
    {
        // 搜索条件
        if (!empty($param['search_field']) && !empty($param['search_value'])) {
            switch ($param['search_field']) {
                case 'sku':
                    $query->andWhere('and sku LIKE :search_value', ['search_value' => '%' . $param['search_value'] . '%']);
                    break;
                case 'product_name':
                    $query->andWhere('and product_name LIKE :search_value', ['search_value' => '%' . $param['search_value'] . '%']);
                    break;
                case 'seller_sku':
                    $query->andWhere('and seller_sku LIKE :search_value', ['search_value' => '%' . $param['search_value'] . '%']);
                    break;
                case 'fnsku':
                    $query->andWhere('and fnsku LIKE :search_value', ['search_value' => '%' . $param['search_value'] . '%']);
                    break;
                case 'asin':
                    $query->andWhere('and asin LIKE :search_value', ['search_value' => '%' . $param['search_value'] . '%']);
                    break;
            }
        }
        
        // 其他筛选条件
        if (!empty($param['cid'])) {
            $query->andWhere('and cid = :cid', ['cid' => $param['cid']]);
        }
        
        if (!empty($param['bid'])) {
            $query->andWhere('and bid = :bid', ['bid' => $param['bid']]);
        }
        
        if (!empty($param['sid'])) {
            $query->andWhere('and sid = :sid', ['sid' => $param['sid']]);
        }
        
        if (isset($param['share_type']) && $param['share_type'] !== '') {
            $query->andWhere('and share_type = :share_type', ['share_type' => $param['share_type']]);
        }
        
        // 是否隐藏零库存
        if (!empty($param['is_hide_zero_stock']) && $param['is_hide_zero_stock'] == '1') {
            $query->andWhere('and (total > 0 OR available_total > 0 OR afn_fulfillable_quantity > 0)');
        }
        
        // 配送方式筛选
        if (!empty($param['fulfillment_channel_type'])) {
            if ($param['fulfillment_channel_type'] == 'FBA') {
                $query->andWhere('and afn_fulfillable_quantity > 0');
            } elseif ($param['fulfillment_channel_type'] == 'FBM') {
                $query->andWhere('and quantity > 0');
            }
        }
    }
    
    /**
     * 获取FBA库存明细详情
     * @param int $id
     * @return array|null
     */
    public function getFbaStorageDetailById($id)
    {
        $db = dbErpMysql::getInstance();
        
        $detail = $db->table('lingxing_fba_storage_detail')
            ->where('id = :id', ['id' => $id])
            ->one();
        
        if ($detail && !empty($detail['fba_storage_quantity_list'])) {
            $detail['fba_storage_quantity_list'] = json_decode($detail['fba_storage_quantity_list'], true);
        }
        
        return $detail;
    }
    
    /**
     * 导出FBA库存明细数据
     * @param array $param
     * @return array
     */
    public function exportFbaStorageDetail($param)
    {
        $db = dbErpMysql::getInstance();
        
        // 构建查询
        $query = $db->table('lingxing_fba_storage_detail')->where('1=1');
        
        // 日期筛选
        if (!empty($param['sync_date'])) {
            $query->andWhere('and sync_date = :sync_date', ['sync_date' => $param['sync_date']]);
        } else {
            $query->andWhere('and sync_date = :sync_date', ['sync_date' => date('Y-m-d')]);
        }
        
        // 应用筛选条件
        $this->applyFilters($query, $param);
        
        // 导出字段定义
        $export_fields = [
            'name' => '仓库名',
            'sid' => '店铺ID',
            'asin' => 'ASIN',
            'product_name' => '品名',
            'seller_sku' => 'MSKU',
            'fnsku' => 'FNSKU',
            'sku' => 'SKU',
            'category_text' => '分类文本',
            'product_brand_text' => '品牌文本',
            'share_type' => '共享类型',
            'total' => '总数',
            'total_price' => '总价',
            'available_total' => '可用总数',
            'available_total_price' => '可用总数成本价',
            'afn_fulfillable_quantity' => 'FBA可售',
            'afn_fulfillable_quantity_price' => 'FBA可售成本价',
            'reserved_fc_transfers' => '待调仓',
            'reserved_fc_transfers_price' => '待调仓成本价',
            'reserved_fc_processing' => '调仓中',
            'reserved_fc_processing_price' => '调仓中成本价',
            'reserved_customerorders' => '待发货',
            'reserved_customerorders_price' => '待发货成本价',
            'quantity' => 'FBM可售',
            'quantity_price' => 'FBM可售成本价',
            'afn_unsellable_quantity' => '不可售',
            'afn_unsellable_quantity_price' => '不可售成本价',
            'afn_inbound_working_quantity' => '计划入库',
            'afn_inbound_working_quantity_price' => '计划入库成本价',
            'afn_inbound_shipped_quantity' => '在途',
            'afn_inbound_shipped_quantity_price' => '在途成本价',
            'afn_inbound_receiving_quantity' => '入库中',
            'afn_inbound_receiving_quantity_price' => '入库中成本价',
            'stock_up_num' => '实际在途',
            'stock_up_num_price' => '实际在途成本价',
            'afn_researching_quantity' => '调查中数量',
            'afn_researching_quantity_price' => '调查中数量成本价',
            'total_fulfillable_quantity' => '总可用库存',
            'inv_age_0_to_30_days' => '0-1个月库龄',
            'inv_age_0_to_30_price' => '0-1个月库龄成本价',
            'inv_age_31_to_60_days' => '1-2个月库龄',
            'inv_age_31_to_60_price' => '1-2个月库龄成本价',
            'inv_age_61_to_90_days' => '2-3个月库龄',
            'inv_age_61_to_90_price' => '2-3个月库龄成本价',
            'inv_age_0_to_90_days' => '0-3个月库龄',
            'inv_age_0_to_90_price' => '0-3个月库龄成本价',
            'inv_age_91_to_180_days' => '3-6个月库龄',
            'inv_age_91_to_180_price' => '3-6个月库龄成本价',
            'inv_age_181_to_270_days' => '6-9个月库龄',
            'inv_age_181_to_270_price' => '6-9个月库龄成本价',
            'inv_age_271_to_330_days' => '9-11个月库龄',
            'inv_age_271_to_330_price' => '9-11个月库龄成本价',
            'inv_age_271_to_365_days' => '9-12个月库龄',
            'inv_age_271_to_365_price' => '9-12个月库龄成本价',
            'inv_age_331_to_365_days' => '11-12个月库龄',
            'inv_age_331_to_365_price' => '11-12个月库龄成本价',
            'inv_age_365_plus_days' => '12个月以上库龄',
            'inv_age_365_plus_price' => '12个月以上库龄成本价',
            'recommended_action' => '推荐操作',
            'sell_through' => '售出率',
            'estimated_excess_quantity' => '预计冗余数量',
            'estimated_storage_cost_next_month' => '预计30天仓储费用',
            'fba_minimum_inventory_level' => '最低库存水平',
            'fba_inventory_level_health_status' => '库存水平健康度',
            'historical_days_of_supply' => '历史供货天数',
            'historical_days_of_supply_price' => '历史供货天数成本价',
            'low_inventory_level_fee_applied' => '低库存水平费收取情况',
            'fulfillment_channel' => '配送方式',
            'sync_date' => '同步日期'
        ];
        
        // 获取数据
        $data_list = $query->order('id DESC')->list();
        
        // 处理导出数据
        $export_data = [];
        foreach ($data_list as $item) {
            $row = [];
            foreach ($export_fields as $field => $header) {
                if ($field == 'share_type') {
                    // 转换共享类型
                    switch ($item[$field]) {
                        case 0:
                            $row[$header] = '非共享';
                            break;
                        case 1:
                            $row[$header] = '北美共享';
                            break;
                        case 2:
                            $row[$header] = '欧洲共享';
                            break;
                        default:
                            $row[$header] = $item[$field] ?? '';
                    }
                } else {
                    $row[$header] = $item[$field] ?? '';
                }
            }
            $export_data[] = $row;
        }
        
        return [
            'headers' => array_values($export_fields),
            'data' => $export_data
        ];
    }
    
    /**
     * 删除过期的FBA库存明细数据
     * @param int $days 保留天数，默认30天
     * @return int 删除的记录数
     */
    public function cleanExpiredData($days = 30)
    {
        $db = dbErpMysql::getInstance();
        
        try {
            $expireDate = date('Y-m-d', strtotime("-{$days} days"));
            
            $count = $db->table('lingxing_fba_storage_detail')
                ->where('sync_date < :expire_date', ['expire_date' => $expireDate])
                ->delete();
            
            log::lingXingApi('FbaStorageDetail')->info("清理过期FBA库存明细数据，删除{$count}条记录");
            
            return $count;
            
        } catch (\Exception $e) {
            log::lingXingApi('FbaStorageDetail')->error('清理过期数据失败：' . $e->getMessage());
            throw new \Exception('清理过期数据失败：' . $e->getMessage());
        }
    }
}
