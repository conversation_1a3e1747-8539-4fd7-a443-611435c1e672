<?php

namespace plugins\checkin\models;

use core\lib\config;
use core\lib\db\dbCMysql;

class ruleModel
{
    public static int $id;
    public static int $user_id;
    public static string $rule_name;
    public static int $rule_type;
    public static int $status;
    public static string $remark;


    /**
     * @param $rule
     * @return void
     * @throws \Exception
     */
    public static function checkRule($rule)
    {
        if (!in_array($rule['rule_type'], [1, 2, 3, 4, 5, 6])) throw new \Exception('规则类型错误');
        if (!in_array($rule['status'], [0, 1])) throw new \Exception('是否启用错误');
        $attach = json_decode($rule['attach'], true);
        switch ($rule['rule_type']) {
            case 1: // 请假扣款
                self::checkVacation($attach);
                break;
            case 2: // 旷工扣款
                self::checkAbsenteeism($attach);
                break;
            case 3: // 缺卡扣款
                self::checkNotCheckin($attach);
                break;
            case 4: // 迟到早退扣款
                self::checkLate($attach);
                break;
            case 5: // 加班计薪
                self::checkOvertime($attach);
                break;
            case 6: // 全勤奖
                self::checkFullAttendance($attach);
                break;

        }

        return;

    }

    // 校验请假扣款
    private static function checkVacation($attach) :void
    {
        empty($attach['casual_leave']) && throw new \Exception('事假规则错误');
        $casual_leave = $attach['casual_leave'];
        if (!isset($casual_leave['status']) || !in_array($casual_leave['status'], [0, 1])) throw new \Exception('事假是否启用错误');
        if ($casual_leave['status']) {
            if (!isset($casual_leave['safe_limit'])) throw new \Exception('不扣款事假最小时间错误');
            if (!$casual_leave['calc_type'] || !in_array($casual_leave['calc_type'], [1, 2, 3, 4])) throw new \Exception('事假取数规则错误');
            if (in_array($casual_leave['calc_type'], [1,2]) && empty($casual_leave['after_point'])) throw new \Exception('事假保留小数错误');
            if (!is_array($casual_leave['formula']) || empty($casual_leave['formula'])) throw new \Exception('事假扣款公式错误');
            $idx = 0;
            foreach ($casual_leave['formula'] as $item) {
                if (!in_array($item['type'], [1, 2])) throw new \Exception("事假扣款公式条件/条件组{$idx}错误");
                if ($idx && !in_array($item['symbol'], [1, 2, 3, 4])) throw new \Exception("事假扣款公式条件/条件组{$idx}计算符号错误");
                if ($item['type'] == 1) {
                    if (!is_numeric($item['value'])) throw new \Exception("条件{$idx}值错误");
                    if (!is_numeric($item['value_type'])) throw new \Exception("条件{$idx}值类型错误");
                } elseif ($item['type'] == 2) {
                    $sub_idx = 0;
                    if (!is_array($item['list']) || empty($item['list'])) throw new \Exception("事假扣款公式条件组{$idx}规则错误");
                    foreach ($item['list'] as $list_item) {
                        if ($sub_idx && !in_array($list_item['symbol'], [1, 2, 3, 4])) throw new \Exception("事假扣款公式条件组{$idx}子条件{$sub_idx}计算符号错误");
                        if (!is_numeric($list_item['value'])) throw new \Exception("事假扣款公式条件组{$idx}子条件{$sub_idx}值错误");
                        if (!is_numeric($list_item['value_type'])) throw new \Exception("事假扣款公式条件组{$idx}子条件{$sub_idx}值类型错误");
                        $sub_idx++;
                    }
                }
                $idx++;
            }
        }

        empty($attach['sick_leave']) && throw new \Exception('病假规则错误');
        $sick_leave = $attach['sick_leave'];
        if (!isset($sick_leave['status']) || !in_array($sick_leave['status'], [0, 1])) throw new \Exception('病假是否启用错误');
        if ($sick_leave['status']) {
            if (!isset($sick_leave['safe_limit'])) throw new \Exception('不扣款病假最小时间错误');
//            if (!isset($sick_leave['safe_times'])) throw new \Exception('不扣款病假最小次数错误');
            if (!isset($sick_leave['lowest_limit'])) throw new \Exception('最低薪资标准错误');
            if (!$sick_leave['calc_type'] || !in_array($sick_leave['calc_type'], [1, 2, 3, 4])) throw new \Exception('病假取数规则错误');
            if (in_array($sick_leave['calc_type'], [1,2]) && empty($sick_leave['after_point'])) throw new \Exception('病假保留小数错误');
            if (!is_array($sick_leave['formula']) || empty($sick_leave['formula'])) throw new \Exception('病假扣款公式错误');
            $idx = 0;
            foreach ($sick_leave['formula'] as $item) {
                if ($idx && !in_array($item['symbol'], [1, 2, 3, 4])) throw new \Exception("病假扣款公式条件/条件组{$idx}计算符号错误");
                if (!in_array($item['type'], [1, 2])) throw new \Exception("病假扣款公式条件/条件组{$idx}错误");
                if ($item['type'] == 1) { // 条件
                    if (!is_numeric($item['value'])) throw new \Exception("病假扣款公式条件{$idx}值错误");
                    if (!is_numeric($item['value_type'])) throw new \Exception("病假扣款公式条件{$idx}值类型错误");
                } elseif ($item['type'] == 2) { // 条件组
                    $sub_idx = 0;
                    if (!is_array($item['list']) || empty($item['list'])) throw new \Exception("病假扣款公式条件组{$idx}规则错误");
                    foreach ($item['list'] as $list_item) {
                        if ($sub_idx && !in_array($list_item['symbol'], [1, 2, 3, 4])) throw new \Exception("病假扣款公式条件组{$idx}子条件{$sub_idx}计算符号错误");
                        if (!is_numeric($list_item['value'])) throw new \Exception("病假扣款公式条件组{$idx}子条件{$sub_idx}值错误");
                        if (!is_numeric($list_item['value_type'])) throw new \Exception("病假扣款公式条件组{$idx}子条件{$sub_idx}值类型错误");
                        $sub_idx++;
                    }
                }
                $idx++;
            }
        }

//        empty($attach['year_leave']) && throw new \Exception('年假规则错误');
//        $year_leave = $attach['year_leave'];
//        if (!isset($year_leave['status']) || !in_array($year_leave['status'], [0, 1])) throw new \Exception('年假是否启用错误');
//        if (!isset($year_leave['year_lowest'])) throw new \Exception('年假最低起算时间错误');
//        if (!isset($year_leave['year_leave'])) throw new \Exception('年假长度错误');
//
//        empty($attach['examination_leave']) && throw new \Exception('体检假规则错误');
//        $examination_leave = $attach['examination_leave'];
//        if (!isset($examination_leave['status']) || !in_array($examination_leave['status'], [0, 1])) throw new \Exception('体检假是否启用错误');
//        if ($examination_leave['status'] && !isset($examination_leave['examination_leave'])) throw new \Exception('体检假长度错误');
    }

    // 校验旷工扣款
    private static function checkAbsenteeism($attach)
    {
        if (!$attach['calc_method'] || !in_array($attach['calc_method'], [1, 2])) throw new \Exception('旷工扣款计算规则错误');
        if ($attach['calc_method'] == 1) { // 固定金额扣款
            if (!is_numeric($attach['value'])) throw new \Exception('扣款金额错误');
        } elseif ($attach['calc_method'] == 2) { // 自定义公式
            if (!$attach['calc_type'] || !in_array($attach['calc_type'], [1, 2, 3, 4])) throw new \Exception('旷工扣款取数规则错误');
            if (in_array($attach['calc_type'], [1,2]) && empty($attach['after_point'])) throw new \Exception('旷工扣款保留小数错误');
            if (!is_array($attach['formula']) || empty($attach['formula'])) throw new \Exception('扣款公式错误');
            $idx = 0;
            foreach ($attach['formula'] as $item) {
                if (!in_array($item['type'], [1, 2])) throw new \Exception("条件/条件组条件类型{$idx}错误");
                if ($idx && !in_array($item['symbol'], [1, 2, 3, 4])) throw new \Exception("条件/条件组{$idx}计算符号错误");
                if ($item['type'] == 1) { // 条件
                    if (!is_numeric($item['value_type'])) throw new \Exception("条件{$idx}值类型错误");
                    if (!is_numeric($item['value'])) throw new \Exception("条件{$idx}值错误");
                } elseif ($item['type'] == 2) { // 条件组
                    $sub_idx = 0;
                    if (!is_array($item['list']) || empty($item['list'])) throw new \Exception("条件组{$idx}规则错误");
                    foreach ($item['list'] as $list_item) {
                        if ($sub_idx && !in_array($list_item['symbol'], [1, 2, 3, 4])) throw new \Exception("条件组{$idx}子条件{$sub_idx}计算符号错误");
                        if (!is_numeric($list_item['value'])) throw new \Exception("条件组{$idx}子条件{$sub_idx}值错误");
                        if (!is_numeric($list_item['value_type'])) throw new \Exception("条件组{$idx}子条件{$sub_idx}值类型错误");
                        $sub_idx++;
                    }
                }
                $idx++;
            }
        }
    }

    // 校验缺卡扣款
    private static function checkNotCheckin($attach)
    {
        empty($attach['start_or_end']) && throw new \Exception('月累计上班/下班缺卡错误');
//        empty($attach['start_and_end']) && throw new \Exception('月累计上班和下班都缺卡错误');
        foreach ($attach['start_or_end'] as $item) {
            if (empty($item['range_times']) || !isset($item['range_times'][0]) || !isset($item['range_times'][1])) throw new \Exception('次数范围错误');
            if (!is_numeric($item['value_type'])) throw new \Exception('缺卡值类型错误');
            if (!is_numeric($item['value'])) throw new \Exception('缺卡值错误');
        }
//        foreach ($attach['start_and_end'] as $item) {
//            if (empty($item['range_times']) || !isset($item['range_times'][0]) || !isset($item['range_times'][1])) throw new \Exception('缺卡次数范围错误');
//            if (!is_numeric($item['value_type'])) throw new \Exception('缺卡值类型错误');
//            if (!is_numeric($item['value'])) throw new \Exception('缺卡值错误');
//        }
    }

    // 校验迟到早退扣款
    private static function checkLate($attach)
    {
        empty($attach['come_late']) && throw new \Exception('迟到规则错误');
        empty($attach['come_late']['setting']) && throw new \Exception('迟到规则设置错误');
        empty($attach['come_late']['rule']) && throw new \Exception('迟到规则错误');
        !isset($attach['come_late']['setting']['times']) && throw new \Exception('迟到规则设置每月次数错误');
        !isset($attach['come_late']['setting']['minutes']) && throw new \Exception('迟到规则设置单次分钟错误');
        foreach ($attach['come_late']['rule'] as $item) {
            if (empty($item['range_minute']) || !isset($item['range_minute'][0]) || !isset($item['range_minute'][1])) throw new \Exception('迟到分钟范围错误');
            if (!isset($item['times'])) throw new \Exception('迟到次数错误');
            if (empty($item['over_times_setting'])) throw new \Exception('超过迟到次数配置错误');
            if (!is_numeric($item['over_times_setting']['value'])) throw new \Exception('超过迟到次数规则值错误');
            if (!is_numeric($item['over_times_setting']['value_type'])) throw new \Exception('超过迟到次数规则值类型错误');
            if (empty($item['not_over_times_setting'])) throw new \Exception('不超过迟到次数配置错误');
            if (!is_numeric($item['not_over_times_setting']['value'])) throw new \Exception('不超过迟到次数规则值错误');
            if (!is_numeric($item['not_over_times_setting']['value_type'])) throw new \Exception('不超过迟到次数规则值类型错误');
        }

        empty($attach['leave_early']) && throw new \Exception('早退规则错误');
        empty($attach['leave_early']['setting']) && throw new \Exception('早退规则设置错误');
        empty($attach['leave_early']['rule']) && throw new \Exception('早退规则错误');
        !isset($attach['leave_early']['setting']['times']) && throw new \Exception('早退规则设置每月次数错误');
        !isset($attach['leave_early']['setting']['minutes']) && throw new \Exception('早退规则设置单次分钟错误');
        foreach ($attach['leave_early']['rule'] as $item) {
            if (empty($item['range_minute']) || !isset($item['range_minute'][0]) || !isset($item['range_minute'][1])) throw new \Exception('早退分钟范围错误');
            if (!is_numeric($item['value'])) throw new \Exception('早退规则值错误');
            if (!is_numeric($item['value_type'])) throw new \Exception('早退规则值类型错误');
        }
    }

    // 校验加班计薪
    private static function checkOvertime($attach)
    {
        empty($attach['workday']) && throw new \Exception('工作日加班规则错误');
        !isset($attach['workday']['hour']) || !isset($attach['workday']['minute']) && throw new \Exception('工作日加班判定错误');
        !isset($attach['workday']['value']) && throw new \Exception('工作日加班补贴错误');

        empty($attach['nonworkingday']) && throw new \Exception('休息日加班规则错误');
        foreach ($attach['nonworkingday'] as $item) {
            !isset($item['hour']) || !isset($item['minute']) && throw new \Exception('休息日加班下班时间判定错误');
            if (empty($item['department'])) throw new \Exception('休息日适用范围错误');
            if (!is_numeric($item['value_type'])) throw new \Exception('休息日计薪规则错误');
            if ($item['value_type'] == 1 && !is_numeric($item['value'])) throw new \Exception('休息日加班补贴错误');
        }

        empty($attach['holiday']) && throw new \Exception('节假日加班规则错误');
        foreach ($attach['holiday'] as $item) {
            !isset($item['hour']) || !isset($item['minute']) && throw new \Exception('节假日加班下班时间判定错误');
            if (empty($item['department'])) throw new \Exception('节假日适用范围错误');
            if (!is_numeric($item['value_type'])) throw new \Exception('节假日计薪规则错误');
            if ($item['value_type'] == 1 && !is_numeric($item['value'])) throw new \Exception('节假日加班补贴错误');
        }


    }

    // 校验全勤奖
    private static function checkFullAttendance($attach)
    {
        !isset($attach['rule']) && throw new \Exception('全勤奖扣款规则错误');
        empty($attach['value']) && throw new \Exception('全勤奖金额错误');
    }

    // 事假扣款
    public static function calcPersonalLeave($rule, &$personal_leave_hour, $user, $is_safe = 1, &$total_deduction = 0)
    {
        $attach = $rule['attach'];
        $casual_leave = $attach['casual_leave'];
        // 公式符号(+-*/)
        $formula_symbol = config::get('formula_symbol', 'data_checkin');
        $formula_symbol_map = array_column($formula_symbol, 'name', 'id');

        $personal_deduction = 0;
        if ($casual_leave['status']) {
            if ($is_safe && $personal_leave_hour <= $casual_leave['safe_limit']) {
                return $personal_deduction;
            } else {
                if ($is_safe) { // 安全事假
                    $personal_leave_hour -= $casual_leave['safe_limit'];
                }
                $expression = '';
                $idx = 0;
                foreach ($casual_leave['formula'] as $item) {
                    $idx && $expression .= $formula_symbol_map[$item['symbol']];
                    if ($item['type'] == 1) {
                        $expression .= self::getFormulaValue($item, $user, $personal_leave_hour / 8);
                    } elseif ($item['type'] == 2) {
                        $item_expression = '';
                        $item_idx = 0;
                        foreach ($item['list'] as $subItem) {
                            $item_idx && $item_expression .= $formula_symbol_map[$subItem['symbol']];
                            $item_expression .= self::getFormulaValue($subItem, $user, $personal_leave_hour / 8);
                            $item_idx++;
                        }
                        $expression .= "($item_expression)";
                    }
                    $idx++;
                }
                eval("\$personal_deduction = $expression;");
                $total_deduction += $personal_deduction;
                // 取数规则
                switch ($casual_leave['calc_type']) {
                    case 1: // 原始数据
                        for ($i = 0; $i < $casual_leave['after_point']; $i++) {
                            $personal_deduction = $personal_deduction * 10;
                            $total_deduction = $total_deduction * 10;
                        }
                        $personal_deduction = floor($personal_deduction);
                        for ($i = 0; $i < $casual_leave['after_point']; $i++) {
                            $personal_deduction = $personal_deduction / 10;
                            $total_deduction = $total_deduction / 10;
                        }
                        $personal_deduction = number_format($personal_deduction, $casual_leave['after_point'],'.','');
                        $total_deduction = number_format($total_deduction, $casual_leave['after_point'],'.','');
                        break;
                    case 2: // 四舍五入
                        $personal_deduction = round($personal_deduction, $casual_leave['after_point']);
                        $total_deduction = round($total_deduction, $casual_leave['after_point']);
                        break;
                    case 3: // 向上取整
                        $personal_deduction = ceil($personal_deduction);
                        $total_deduction = ceil($total_deduction);
                        break;
                    case 4: // 向下取整
                        $personal_deduction = floor($personal_deduction);
                        $total_deduction = floor($total_deduction);
                        break;
                }
            }
        }

        return $personal_deduction;
    }

    // 病假扣款
    public static function calcSickLeave($rule, $sick_low_salary, $user, &$total_deduction = 0)
    {
        $attach = $rule['attach'];
        $sick_leave = $attach['sick_leave'];
        // 公式符号(+-*/)
        $formula_symbol = config::get('formula_symbol', 'data_checkin');
        $formula_symbol_map = array_column($formula_symbol, 'name', 'id');

        $sick_deduction = 0;
        if ($sick_leave['status']) {
            $expression = '';
            $idx = 0;
            foreach ($sick_leave['formula'] as $item) {
                $idx && $expression .= $formula_symbol_map[$item['symbol']];
                if ($item['type'] == 1) {
                    $expression .= self::getFormulaValue($item, $user, $sick_low_salary, $sick_leave['lowest_limit']);
                } elseif ($item['type'] == 2) {
                    $item_expression = '';
                    $item_idx = 0;
                    foreach ($item['list'] as $subItem) {
                        $item_idx && $item_expression .= $formula_symbol_map[$subItem['symbol']];
                        $item_expression .= self::getFormulaValue($subItem, $user, $sick_low_salary, $sick_leave['lowest_limit']);
                        $item_idx++;
                    }
                    $expression .= "($item_expression)";
                }
                $idx++;
            }
            eval("\$sick_deduction = $expression;");
            $total_deduction += $sick_deduction;

            // 取数规则
            switch ($sick_leave['calc_type']) {
                case 1: // 原始数据
                    for ($i = 0; $i < $sick_leave['after_point']; $i++) {
                        $sick_deduction = $sick_deduction * 10;
                        $total_deduction = $total_deduction * 10;
                    }
                    $sick_deduction = floor($sick_deduction);
                    for ($i = 0; $i < $sick_leave['after_point']; $i++) {
                        $sick_deduction = $sick_deduction / 10;
                        $total_deduction = $total_deduction / 10;
                    }
                    $sick_deduction = number_format($sick_deduction, $sick_leave['after_point'],'.','');
                    $total_deduction = number_format($total_deduction, $sick_leave['after_point'],'.','');
                    break;
                case 2: // 四舍五入
                    $sick_deduction = round($sick_deduction, $sick_leave['after_point']);
                    $total_deduction = round($total_deduction, $sick_leave['after_point']);
                    break;
                case 3: // 向上取整
                    $sick_deduction = ceil($sick_deduction);
                    $total_deduction = ceil($total_deduction);
                    break;
                case 4: // 向下取整
                    $sick_deduction = floor($sick_deduction);
                    $total_deduction = floor($total_deduction);
                    break;
            }
        }
        return $sick_deduction;
    }

    // 旷工扣款
    public static function calcAbsenteeism($rule, $absenteeism_day, $user, &$total_deduction = 0)
    {
        $attach = $rule['attach'];
        // 公式符号(+-*/)
        $formula_symbol = config::get('formula_symbol', 'data_checkin');
        $formula_symbol_map = array_column($formula_symbol, 'name', 'id');

        $absenteeism_deduction = 0;
        if ($attach['calc_method'] == 1) { // 固定金额
            $absenteeism_deduction = $attach['value'] * $absenteeism_day;
            $total_deduction += $absenteeism_deduction;
        } elseif ($attach['calc_method'] == 2) {
            $expression = '';
            $idx = 0;
            foreach ($attach['formula'] as $item) {
                $idx && $expression .= $formula_symbol_map[$item['symbol']];
                if ($item['type'] == 1) {
                    $expression .= self::getFormulaValue($item, $user, $absenteeism_day);
                } elseif ($item['type'] == 2) {
                    $item_expression = '';
                    $item_idx = 0;
                    foreach ($item['list'] as $subItem) {
                        $item_idx && $item_expression .= $formula_symbol_map[$subItem['symbol']];
                        $item_expression .= self::getFormulaValue($subItem, $user, $absenteeism_day);
                        $item_idx++;
                    }
                    $expression .= "($item_expression)";
                }
                $idx++;
            }
            eval("\$absenteeism_deduction = $expression;");
            $total_deduction += $absenteeism_deduction;
            // 取数规则
            switch ($attach['calc_type']) {
                case 1: // 原始数据
                    for ($i = 0; $i < $attach['after_point']; $i++) {
                        $absenteeism_deduction = $absenteeism_deduction * 10;
                        $total_deduction = $total_deduction * 10;
                    }
                    $absenteeism_deduction = floor($absenteeism_deduction);
                    for ($i = 0; $i < $attach['after_point']; $i++) {
                        $absenteeism_deduction = $absenteeism_deduction / 10;
                        $total_deduction = $total_deduction / 10;
                    }
                    $absenteeism_deduction = number_format($absenteeism_deduction, $attach['after_point'],'.','');
                    $total_deduction = number_format($total_deduction, $attach['after_point'],'.','');
                    break;
                case 2: // 四舍五入
                    $absenteeism_deduction = round($absenteeism_deduction, $attach['after_point']);
                    $total_deduction = round($total_deduction, $attach['after_point']);
                    break;
                case 3: // 向上取整
                    $absenteeism_deduction = ceil($absenteeism_deduction);
                    $total_deduction = ceil($total_deduction);
                    break;
                case 4: // 向下取整
                    $absenteeism_deduction = floor($absenteeism_deduction);
                    $total_deduction = floor($total_deduction);
                    break;
            }
        }
        return $absenteeism_deduction;
    }

    // 缺卡扣款
    public static function calcNotCheckin($rule, $not_checkin_times)
    {
        $attach = $rule['attach'];
        if (empty($attach['start_or_end'])) return 0;
        $not_checkin_deduction = 0;
        foreach ($attach['start_or_end'] as $item) {
            if ($not_checkin_times > $item['range_times'][0] && $not_checkin_times <= $item['range_times'][1]) {
                if ($item['value_type'] == 1) {// 按次数扣款
                    $not_checkin_deduction = $item['value'];
                }
                break;
            }
        }
        return $not_checkin_deduction;
    }

    // 迟到扣款
    public static function calcComeLate($rule, $late_duration, &$safe_late, &$user_late_times) :array
    {
        $attach = $rule['attach'];
        if (empty($attach['come_late'])) return ['late_deduction' => 0, 'late_type' => 0]; // 未设置迟到规则
        $late_deduction = 0; $late_type = 1;
        // 先判断是否在免除次数之内
        if ($safe_late < $attach['come_late']['setting']['times'] && $late_duration < ($attach['come_late']['setting']['minutes'] * 60)) {
            $safe_late++;
            return ['late_deduction' => $late_deduction, 'late_type' => 0];
        }

        foreach ($attach['come_late']['rule'] as $key => $item) {
            if ($late_duration >= $item['range_minute'][0] * 60 && $late_duration < $item['range_minute'][1] * 60) {
                // 当前阶梯的迟到次数
                if (!isset($user_late_times[$key])) $user_late_times[$key] = 0;
                $user_late_times[$key]++;

                if ($user_late_times[$key] <= $item['times']) { // 不超过次数扣款
                    $deduction_setting = $item['not_over_times_setting'];
                } else {
                    $deduction_setting = $item['over_times_setting'];
                }
                $late_type = $deduction_setting['value_type'];
                $late_deduction = $deduction_setting['value'];
                break;
            }
        }
        return ['late_deduction' => $late_deduction, 'late_type' => $late_type];
    }

    // 早退扣款
    public static function calcLeaveEarly($rule, $early_duration, &$safe_early) :array
    {
        $attach = $rule['attach'];
        if (empty($attach['leave_early'])) return ['early_deduction' => 0, 'early_type' => 0]; // 未设置迟到规则
        $early_deduction = 0;$early_type = 1;
        // 先判断是否在免除次数之内
        if ($safe_early < $attach['leave_early']['setting']['times'] && $early_duration < ($attach['leave_early']['setting']['minutes'] * 60)) {
            $safe_early++;
            return ['early_deduction' => $early_deduction, 'early_type' => 0];
        }

        foreach ($attach['leave_early']['rule'] as $item) {
            if ($early_duration >= $item['range_minute'][0] * 60 && $early_duration < $item['range_minute'][1] * 60) {
                $early_type = $item['value_type'];
                $early_deduction = $item['value'];
                break;
            }
        }
        return ['early_deduction' => $early_deduction, 'early_type' => $early_type];
    }

    // 加班计薪
    public static function calcOvertime($rule, $overtime_type, $user, $duration, $is_work = 1, &$rest_leave_time = null) :array
    {
        $attach = $rule['attach'];
        $user_department = json_decode($user['wdepartment_ids'], true);
        $overtime_addition = 0; // 加班补贴
        $overtime_addition_type = 0; // 加班补贴类型 1补贴 2调休
        $is_overtime = false;
        if ($overtime_type == 1) { // 工作日加班
            $attach['workday']['day_type'][] = 1; // 工作日默认发放餐补
            if (!in_array($is_work, $attach['workday']['day_type'])) {
                return ['overtime_addition' => 0, 'overtime_addition_type' => 0, 'is_overtime' => 0];
            }

            if ($duration > $attach['workday']['hour'] * 3600 + $attach['workday']['minute'] * 60) {
                $overtime_addition = $attach['workday']['value'];
                $is_overtime = true;

            }
        }
        elseif ($overtime_type == 2) { // 休息日加班
            $duration_day = $duration / (8 * 3600);
            foreach ($attach['nonworkingday'] as $item) {
                // tips 这里可能存在用户多个部门的配置不同，按顺序取第一个
                if (!empty(array_intersect($item['department'], $user_department))) {
                    $rest_leave_time = $item['hour'] * 3600 + $item['minute'] * 60;
                    if ($item['value_type'] == 1) { // 固定金额
                        $overtime_addition = round($item['value'] * $duration / (8 * 3600), 2);
                        $overtime_addition_type = 1;
                    } elseif ($item['value_type'] == 2) { // 调休
                        $overtime_addition = 1;
                        $overtime_addition_type = 2;
                    }
                    break;
                }
            }
            return ['overtime_addition' => $overtime_addition, 'overtime_addition_type' => $overtime_addition_type ];

        }
        elseif ($overtime_type == 3) { // 节假日加班
            foreach ($attach['holiday'] as $item) {
                if (!empty(array_intersect($item['department'], $user_department))) {
                    $rest_leave_time = $item['hour'] * 3600 + $item['minute'] * 60;
                    if ($item['value_type'] == 1) { // 固定金额
                        $overtime_addition = round($item['value'] * $duration / (8 * 3600), 2);
                        $overtime_addition_type = 1;
                    } elseif ($item['value_type'] == 2) { // 调休
                        $overtime_addition = 1;
                        $overtime_addition_type = 2;
                    }
                    break;
                }
            }

        }
        return ['overtime_addition' => $overtime_addition, 'overtime_addition_type' => $overtime_addition_type, 'is_overtime' => $is_overtime];
    }

    // 全勤奖
    public static function calcFullAttendance($rule, $user_summary, $vacation) :int
    {
        $attach = $rule['attach'];
        $full_attendance_reward = $attach['value'];
        // 迟到早退
        if (!in_array('come_late_leave_early', $attach['rule']) && ($user_summary['late'] > 0 || $user_summary['early'] > 0 || $user_summary['safe_late'] > 0 || $user_summary['safe_early'] > 0)) {
            return 0;
        }
        // 旷工
        if (!in_array('absenteeism', $attach['rule']) && $user_summary['absenteeism_day'] > 0) {
            return 0;
        }
        // 上班缺卡
        // if (!in_array('miss_start', $attach['rule']) && $user_summary['lack_start_card'] > 0) {
        //     return 0;
        // }
        // 下班缺卡
        // if (!in_array('miss_end', $attach['rule']) && $user_summary['lack_end_card'] > 0) {
        //     return 0;
        // }
        // 缺卡
        if (!in_array('miss_card', $attach['rule']) && $user_summary['lack_card'] > 0) {
            return 0;
        }
        // 上班补卡
        if (!in_array('mend_start', $attach['rule']) && $user_summary['work_card'] > 0) {
            return 0;
        }
        // 下班补卡
        if (!in_array('mend_start', $attach['rule']) && $user_summary['off_card'] > 0) {
            return 0;
        }
        // 所有假期类型
        foreach ($vacation as $item) {
            $vacation_name = $item['name'];
            if (!in_array('vacation-'.$item['id'], $attach['rule'])) {
                if (strpos($vacation_name, '事假') !== false &&
                    strpos($vacation_name, '小时') !== false &&
                    $user_summary['personal_leave_hour'] > 0) { // 事假/小时
                    return 0;
                }
                if (strpos($vacation_name, '事假') !== false &&
                    strpos($vacation_name, '天') !== false &&
                    $user_summary['personal_leave_day'] > 0) { // 事假/天
                    return 0;
                }
                if (strpos($vacation_name, '病假') !== false &&
                    ($user_summary['sick_low_salary'] > 0  || $user_summary['sick_full_salary'] > 0 )) { // 病假
                    // 20240527 全薪病假也没有全勤奖
                    return 0;
                }
                if (strpos($vacation_name, '年假') !== false &&
                    $user_summary['annual_leave_day'] > 0) { // 年假
                    return 0;
                }
                if (strpos($vacation_name, '调休假') !== false &&
                    $user_summary['adjustable_day'] > 0) { // 调休假
                    return 0;
                }
                if (strpos($vacation_name, '婚假') !== false &&
                    $user_summary['marriage_leave_day'] > 0) { // 婚假
                    return 0;
                }
                if (strpos($vacation_name, '产假') !== false &&
                    $user_summary['maternity_leave_day'] > 0) { // 产假
                    return 0;
                }
                if (strpos($vacation_name, '丧假') !== false &&
                    $user_summary['funeral_leave_day'] > 0) { // 丧假
                    return 0;
                }
                if (strpos($vacation_name, '产检假') !== false &&
                    $user_summary['prenatal_check_leave_day'] > 0) { // 产检假
                    return 0;
                }
                if (strpos($vacation_name, '体检假') !== false &&
                    $user_summary['physical_examination_leave_day'] > 0) { // 体检假
                    return 0;
                }
                if (strpos($vacation_name, '其他') !== false &&
                    $user_summary['other_leave_day'] > 0) { // 其他
                    return 0;
                }

            }
        }

        return $full_attendance_reward;
    }

    private static function getFormulaValue($item, $user, $day = 0, $lowest_limit = 0)
    {
        $value = 0;
        if ($item['value_type'] == 1) { // 个人
            switch ($item['value']) {
                case 1: // 基本工资
                    $value = $user['base_salary'];
                    break;
                case 2: // 综合工资
                    $value = $user['total_salary'];
                    break;
                case 3: // 满勤天数
                    $value = $user['full_attendance_day'];
                    break;
                case 4: // 实际出勤天数
                    $value = $user['actual_attendance_day'];
                    break;
                case 5: // 应出勤天数
                    $value = $user['should_attendance_day'];
                    break;
                case 6: // 事假/病假/旷工天数
                    $value = $day;
                    break;
                case 7: // 最低薪资标准
                    $value = $lowest_limit;
                    break;
            }
        } elseif ($item['value_type'] == 3) { // 自定义值
            $value = $item['value'];
        }
        if (isset($item['is_abs']) && $item['is_abs']) {
            $value = abs($value);
        }
        return $value;
    }


}