<?php

namespace plugins\salary\models;

use admin\models\qwuserModel;
use plugins\assessment\models\customCrontabModel;
use core\lib\db\dbAMysql;
use core\lib\db\dbCMysql;
use core\lib\db\dbMysql;
use core\lib\db\dbSMysql;
use DateTime;
use plugins\salary\form\auditForm;
use plugins\salary\form\messagesFrom;

class salaryCalculationModel
{
    //1待核算 2 核算中 3待审核 4 待审批 5已完成 6取消  7作废 8 核算失败 9 审核未通过 10 审批未通过',
    const STATUS_WAIT_CALC = 1;
    const STATUS_CALCULATING = 2;
    const STATUS_WAIT_CHECK = 3;
    const STATUS_WAIT_APPROVE = 4;
    const STATUS_FINISHED = 5;
    const STATUS_CANCEL = 6;
    const STATUS_INVALID = 7;
    const STATUS_CALC_FAIL = 8;
    const STATUS_CHECK_FAIL = 9;
    const STATUS_APPROVE_FAIL = 10;

    public static function getCalculationByMonth($month)
    {
        $sdb = dbSMysql::getInstance();
        return $sdb->table('salary_calculation')
            ->where('where month = :month and is_delete = 0', ['month' => $month])
            ->whereIn('status', [self::STATUS_WAIT_CALC, self::STATUS_CALCULATING, self::STATUS_WAIT_CHECK, self::STATUS_WAIT_APPROVE, self::STATUS_FINISHED])
            ->list();
    }

    public static function getCalculationUserByMonth($month, $user_ids)
    {
        $sdb = dbSMysql::getInstance();
        return $sdb->table('salary_calculation_user', 'scu')
            ->leftJoin('salary_calculation', 'sc', 'scu.calc_id=sc.id')
            ->where('where sc.month = :month and scu.is_delete = 0 and scu.status = 5', ['month' => $month])
            ->whereIn('scu.user_id', $user_ids)
            ->list();
    }

    // 自动生成
    public static function autoCalc($crontab)
    {

        $month = date('Y-m', strtotime(date('Y-m-d') . ' -1 month'));
        $last_month = date('Y-m', strtotime($month . ' -1 month'));

        $runtime = $crontab['runtime'];
        $next_runtime = date('Y-m-d H:i:s', strtotime($runtime . ' +1 month'));

        $adb = dbAMysql::getInstance();

        // 查询上个月的薪资计算
        $sdb = dbSMysql::getInstance();
        $list = $sdb->table('salary_calculation')
            ->where('where month = :month and status =:status', ['month' => $last_month, 'status' => self::STATUS_FINISHED])
            ->list();
        if (empty($list)) {
            $adb->table('custom_crontab')
                ->where('where id = :id', ['id' => $crontab['id']])
                ->update(['status' => -1, 'runtime' => $next_runtime]);
            return false;
        }

        // 考勤数据验证
        $cdb = dbCMysql::getInstance();
        $detail = $cdb->table('user_checkin_month')
            ->where('where month = :month', ['month' => $month])
            ->one();
        if (!$detail) {
            customCrontabModel::finishCrontab($crontab['id']);
            $adb->table('custom_crontab')
                ->where('where id = :id', ['id' => $crontab['id']])
                ->update(['status' => -1, 'runtime' => $next_runtime]);
            return false;
        }

        $scheme_ids = array_column($list, 'scheme_id');
        $scheme_ids = array_values(array_unique($scheme_ids));

        $schemes = $sdb->table('salary_scheme')
            ->whereIn('id', $scheme_ids)
            ->list();
        $schemes = array_column($schemes, null, 'id');

        $db = dbMysql::getInstance();
        // 用户基本信息
        $users = $db->table('qwuser', 'u')
            ->field('u.id as user_id,u.wid, u.wname, ui.user_status')
            ->leftJoin('user_info', 'ui', 'u.id=ui.qwuser_id')
            ->list();
        $users = array_column($users, null, 'user_id');
        $user_roles = userRolesModel::getRolesAndUsers();
        $department_users = qwuserModel::getDepartmentUsers();

        foreach ($scheme_ids as $scheme_id) {
            $scheme = $schemes[$scheme_id];
            $scheme['attach'] = json_decode($scheme['attach'], true);
            $scheme['config'] = json_decode($scheme['config'], true);
            $scheme_users = salarySchemeModel::getSchemeUsers($scheme['attach'], $users, $user_roles, $department_users);
            if (empty($scheme_users)) {
                continue;
            }

            $data = [
                'calc_name' => $scheme['scheme_name'].'_'.$month,
                'scheme_id' => $scheme_id,
                'month'     => $month,
                'attach'    => json_encode([
                    'scheme' => $scheme,
                ], JSON_UNESCAPED_UNICODE),
                'operator'  => 0,
                'status'    => salaryCalculationModel::STATUS_WAIT_CALC,
            ];
            $id = $sdb->table('salary_calculation')->insert($data);
            $data['id'] = $id;
            if (!$id) continue;

            // 生成薪资计算用户
            salaryCalculationModel::createCalcUser($data, $scheme_users);
        }

        $adb->table('custom_crontab')
            ->where('where id = :id', ['id' => $crontab['id']])
            ->update(['status' => -1, 'runtime' => $next_runtime]);

        return true;

    }

    public static function createCalcUser($data, $users)
    {
        // 获取用户信息
        $user_ids = array_column($users, 'user_id');
        $user_wids = array_column($users, 'wid');
        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();
        $cdb = dbCMysql::getInstance();
        $adb = dbAMysql::getInstance();

        // 方案信息
        $scheme = json_decode($data['attach'], true)['scheme'];
        // 计薪方式
        $scheme_type = $scheme['scheme_type'];
        $start_date = date('Y-m-01', strtotime($data['month']));
        $end_date = date('Y-m-t', strtotime($data['month']));

        // 这里需要构造按月的数据作为临时月份表
        $months = " (select '" . $data['month'] . "' as month) ";

        // 获取审核/审批配置
        $check_config = auditForm::getAuditConfig('calculation_check');
        $approve_config = auditForm::getAuditConfig('calculation_audit');

        // 部门信息
        $department = $db->table('qwdepartment')->field('wp_id, name')->list();
        $department = array_column($department, 'name', 'wp_id');
        // 公司信息
        $corps = $sdb->table('corp')->list();
        $corps = array_column($corps, null, 'id');

        $db->table('qwuser', 'u')
            ->field('u.id as user_id,u.wid as user_wid, u.wname as user_name, u.wphone as user_wphone, u.wdepartment_ids as user_wdepartment_ids, u.wmain_department as user_wmain_department, u.position as user_position, ui.*')
            ->leftJoin('user_info', 'ui', 'u.id=ui.qwuser_id');
        $db->where('1=1');
        $db->whereIn('u.id', $user_ids);
        $user_list = $db->list();

        // 用户薪资信息
        $user_salary = $sdb->table('user_salary')
            ->where('where is_delete = 0 and effective_date <= :effective_date', ['effective_date' => date('Y-m-t', strtotime($data['month']))])
            ->whereIn('qwuser_id', $user_ids)
            ->order('effective_date DESC')
            ->list();
        $user_salary_map = [];
        $user_salary_status_map = []; // 混合薪资是否还需要比开始日期小的数据
        $trial_period_salary = []; // 试用期薪资
        $regularization_salary = []; // 转正薪资

        // 如果员工没有转正，使用定薪时转正后工资
        $salary_change_record = $sdb->table('user_salary_change_record')
            ->where('where is_delete = 0 and type = 1')
            ->whereIn('qwuser_id', $user_ids)
            ->order('effective_date DESC')
            ->list();
        foreach ($salary_change_record as $item) {
            $detail = json_decode($item['detail'], true);
            $salary = $detail['salary'];
            $total_salary = 0;
            foreach ($salary as $key => $value) {
                $total_salary += $value;
            }
            $salary['total_salary'] = $total_salary;
            if (isset($regularization_salary[$item['qwuser_id']])) continue;
            $regularization_salary[$item['qwuser_id']] = $salary;
        }
        // 如果员工已经转正，使用转正类型的薪资
        foreach ($user_salary as $item) {
            $salary = json_decode($item['salary'], true);
            $total_salary = 0;
            foreach ($salary as $key => $value) {
                $total_salary += $value;
            }
            $salary['total_salary'] = $total_salary;
            if ($item['type'] == 1) { // 试用期定薪
                if (!isset($trial_period_salary[$item['qwuser_id']])) {
                    $trial_period_salary[$item['qwuser_id']] = $salary;
                }
            } elseif ($item['type'] == 2) { // 转正
                if (!isset($regularization_salary[$item['qwuser_id']])) {
                    $regularization_salary[$item['qwuser_id']] = $salary;
                }
            }
            if ($scheme_type != 3) {
                if (isset($user_salary_map[$item['qwuser_id']])) continue;
                if ($scheme_type == 2 && strtotime($item['effective_date']) > strtotime($start_date)) {
                    continue;
                }
                $user_salary_map[$item['qwuser_id']] = $salary;
            } else {
                // 至少需要一条比开始日期少的数据
                if (strtotime($item['effective_date']) < strtotime($start_date)) {
                    if ($user_salary_status_map[$item['qwuser_id']]) {
                        continue;
                    } else {
                        $user_salary_status_map[$item['qwuser_id']] = true;
                    }
                }
                $salary['effective_date'] = $item['effective_date'];
                $user_salary_map[$item['qwuser_id']][] = $salary;
            }
        }

        foreach ($user_list as &$item) {
            $item['social_insurance'] = json_decode($item['social_insurance'], true);
            $item['housing_fund'] = json_decode($item['housing_fund'], true);

            $item['user_salary'] = $user_salary[$item['user_id']] ?? null;
            $item['corp_name'] = $corps[$item['corp_id']]['name'] ?? '';
            $start = new DateTime($item['hire_date']);
            $end = new DateTime(date('Y-m-d'));
            $diff = $start->diff($end);
            $item['corp_in_age'] = [
                'year'  => $diff->y,
                'month' => $diff->m,
                'day'   => $diff->d,
            ];
            // 主部门
            $item['user_main_department_name'] = $department[$item['user_wmain_department']] ?? '';
            $item['trial_period_salary'] = $trial_period_salary[$item['user_id']] ?? [];
            $item['regularization_salary'] = $regularization_salary[$item['user_id']] ?? [];
        }
        unset($item);

        $user_map = array_column($user_list, null, 'user_id');

        // 考勤
        $user_summary_data = $cdb->table('user_checkin_summary')
            ->field('user_id,summary_month,base_info,summary_info,checkin_day')
            ->where('where summary_month = :summary_month', ['summary_month' => $data['month']])
            ->whereIn('user_id', $user_wids)
            ->list();
        foreach ($user_summary_data as &$item) {
            $item['base_info'] = json_decode($item['base_info'], true);
            $item['summary_info'] = json_decode($item['summary_info'], true);
            $item['checkin_day'] = json_decode($item['checkin_day'], true);
        }
        unset($item);
        $user_summary = array_column($user_summary_data, null, 'user_id');

        // 绩效
        $user_assessment_map = [];
        $user_assessment = $adb->table('assessment_users', 'au')
            ->field('au.id,au.a_id,au.user_id,au.result,a.attach')
            ->leftJoin('assessment', 'a', 'au.a_id=a.id')
            ->where('where au.is_delete = 0 and au.status = 1 and a.is_delete = 0 and a.status = 1')
            ->andWhere('JSON_EXTRACT(a.attach, "$.scheme.assessment_type") in ("1", "2")')
            ->andWhere('au.salary_month = :salary_month', ['salary_month' => $data['month']])
            ->whereIn('au.user_id', $user_ids)
            ->list();
        foreach ($user_assessment as $item) {
            !isset($user_assessment_map[$item['user_id']]) && $user_assessment_map[$item['user_id']] = [
                'prize'       => 0,
                'performance' => 0,
                'level'       => []
            ];
            // 考核方案对应的考核类型
            $attach = json_decode($item['attach'], true);
            $item['assessment_type'] = $attach['scheme']['assessment_type'] ?? 0;
            $item['result'] = json_decode($item['result'], true);
            $item['result'] = array_intersect_key($item['result'], array_flip(['level', 'performance', 'final_performance', 'prize']));
            $user_assessment_map[$item['user_id']]['prize'] += $item['result']['prize'] ?? 0;
            $performance = $item['result']['final_performance'] ?? $item['result']['performance'] ?? 0;
            $user_assessment_map[$item['user_id']]['performance'] += $performance;
            if (isset($item['result']['level'])) {
                $user_assessment_map[$item['user_id']]['level'][] = $item['result']['level'];
            }
        }


        // 五险一金个税信息
        $db->table('qwuser', 'u')
            ->field('m.month, u.id as user_id,u.wid as user_wid, u.wname as user_name, u.wphone as user_wphone, u.wdepartment_ids as user_wdepartment_ids, u.wmain_department as user_wmain_department, u.position as user_position, ui.*, a1.attach as user_social_insurance ,a2.attach as user_housing_fund ,a3.attach as user_tax')
            ->leftJoin('user_info', 'ui', 'u.id=ui.qwuser_id')
            ->crossJoin($months, 'm')
            ->leftJoinOut('salary', 'user_insurance_fund_tax', 'a1', 'u.id=a1.qwuser_id and a1.is_delete=0 and a1.type=1 and a1.month = m.month')
            ->leftJoinOut('salary', 'user_insurance_fund_tax', 'a2', 'u.id=a2.qwuser_id and a2.is_delete=0 and a2.type=2 and a1.month = m.month')
            ->leftJoinOut('salary', 'user_insurance_fund_tax', 'a3', 'u.id=a3.qwuser_id and a3.is_delete=0 and a3.type=3 and a1.month = m.month');
        $db->whereIn('u.id', $user_ids);
        $user_insurance_fund_tax = $db->list();

        // 获取默认社保公积金缴纳设置
        $default_setting = $sdb->table('config')
            ->where('where 1=1')
            ->whereIn('key_name', ['social_security', 'housing_fund'])
            ->list();
        foreach ($default_setting as &$item) {
            $item['data'] = json_decode($item['data'], true);
        }
        unset($item);
        $default_setting = array_column($default_setting, 'data', 'key_name');
        $social_security = array_column($default_setting['social_security'], 'num', 'work_place');
        $housing_fund = array_column($default_setting['housing_fund'], 'num', 'work_place');

        $user_insurance_fund_tax_data = [];
        foreach ($user_insurance_fund_tax as $item) {
            $item['social_insurance'] = json_decode($item['social_insurance'], true);
            $item['housing_fund'] = json_decode($item['housing_fund'], true);

            $item['user_social_insurance'] = json_decode($item['user_social_insurance'], true);
            $item['user_housing_fund'] = json_decode($item['user_housing_fund'], true);
            $item['user_tax'] = json_decode($item['user_tax'], true);

            // 社保差额， 导入的缴纳金额大于默认值
            if (!empty($item['user_social_insurance']) && !empty($item['user_social_insurance']['total_corp']) && $item['user_social_insurance']['total_corp'] > $social_security[$item['work_place']]) {
                $item['social_insurance_diff'] = $item['user_social_insurance']['total_corp'] - $social_security[$item['work_place']];
            }
            // 公积金差额， 导入的缴纳金额大于默认值
            if (!empty($item['user_housing_fund']) && !empty($item['user_housing_fund']['total']) && floatval($item['user_housing_fund']['total'] / 2) > $housing_fund[$item['work_place']]) {
                $item['housing_fund_diff'] = floatval($item['user_housing_fund']['total'] / 2) - $housing_fund[$item['work_place']];
            }

            // 过滤字段
            $key = [
                'user_id',
                'social_insurance',
                'housing_fund',
                'user_social_insurance',
                'user_housing_fund',
                'user_tax',
                'social_insurance_diff',
                'housing_fund_diff'
            ];
            $user_insurance_fund_tax_data[$item['user_id']] = array_intersect_key($item, array_flip($key));
        }
        $user_insurance_fund_tax_data = array_column($user_insurance_fund_tax_data, null, 'user_id');

        // 删差评
        $user_comment_reward = [];
        $user_comment_sum = [];
        $sdb = dbSMysql::getInstance();
        $user_reward = $sdb->table('user_reward', 'a')
            ->where('where a.is_delete = 0 and a.month = :month', ['month' => $data['month']])
            ->list();
        foreach ($user_reward as $reward) {
            $reward['attach'] = json_decode($reward['attach'], true);
            if (!isset($user_comment_reward[$reward['qwuser_id']])) $user_comment_reward[$reward['qwuser_id']] = 0;
            if (!isset($user_comment_sum[$reward['qwuser_id']])) $user_comment_sum[$reward['qwuser_id']] = 0;
            $user_comment_reward[$reward['qwuser_id']] += $reward['amount'];
            $user_comment_sum[$reward['qwuser_id']] += $reward['attach']['num'];
        }

        // 生成薪资计算用户
        $insert_data = [];
        foreach ($users as $user) {

            if (!empty($user_assessment_map[$user['user_id']]['level'])) {
                $user_assessment_map[$user['user_id']]['level'] = implode('、', $user_assessment_map[$user['user_id']]['level']);
            }
            $insert_data[] = [
                'calc_id'      => $data['id'],
                'user_id'      => $user['user_id'],
                'attach'       => json_encode([
                    'user_info'          => $user_map[$user['user_id']] ?? [],
                    'salary'             => $user_salary_map[$user['user_id']] ?? [],
                    'checkin'            => $user_summary[$user['wid']] ?? [],
                    'assessment'         => $user_assessment_map[$user['user_id']] ?? [],
                    'insurance_fund_tax' => $user_insurance_fund_tax_data[$user['user_id']] ?? [],
                    'comment_reward'     => $user_comment_reward[$user['user_id']] ?? 0,
                    'comment_num'        => $user_comment_sum[$user['user_id']] ?? 0
                ], JSON_UNESCAPED_UNICODE),
                'audit_attach' => json_encode([
                    'check'   => $check_config,
                    'approve' => $approve_config
                ], JSON_UNESCAPED_UNICODE),
                'status'       => salaryCalculationModel::STATUS_WAIT_CALC,
            ];
        }
        if (!empty($insert_data)) {
            $keys = array_keys($insert_data[0]);
            $list_ = array_map(function ($row) {
                return array_values($row);
            }, $insert_data);
            $sdb->table('salary_calculation_user')->insertBatch($keys, $list_);
        }

    }

    // 计算结果
    public static function calcResult($crontab)
    {
        $calc_id = $crontab['link_id'];
        $sdb = dbSMysql::getInstance();

        $calculation = $sdb->table('salary_calculation')->where('where id = :id', ['id' => $calc_id])->one();
        if (empty($calculation)) {
            // 将定时任务状态改为已完成
            customCrontabModel::finishCrontab($crontab['id']);
            return false;
        }
        if ($calculation['status'] != salaryCalculationModel::STATUS_CALCULATING) {
            // 将定时任务状态改为已完成
            customCrontabModel::finishCrontab($crontab['id']);
            return false;
        }

        // 获取默认社保公积金缴纳设置
        $default_setting = $sdb->table('config')
            ->where('where 1=1')
            ->whereIn('key_name', ['social_security', 'housing_fund'])
            ->list();
        foreach ($default_setting as &$item) {
            $item['data'] = json_decode($item['data'], true);
        }
        unset($item);
        $default_setting = array_column($default_setting, 'data', 'key_name');
        $social_security = array_column($default_setting['social_security'], 'num', 'work_place');
        $housing_fund = array_column($default_setting['housing_fund'], 'num', 'work_place');

        // 当前算薪月份
        $month = $calculation['month'];
        $attach = json_decode($calculation['attach'], true);
        $scheme = $attach['scheme'];
        $scheme_config = $scheme['config'];
        $item_ids = []; // 需要计算的浮动薪资项
        foreach ($scheme_config as $item) {
            if ($item['module'] == 5) { // 浮动薪资项
                $item_ids = $item['list'];
            }
        }

        // 查询当前算薪计算下的用户
        $users = $sdb->table('salary_calculation_user')->where('where calc_id = :calc_id', ['calc_id' => $calc_id])->list();
        if (empty($users)) {
            // 将定时任务状态改为已完成
            customCrontabModel::finishCrontab($crontab['id']);
            return false;
        }
        $total = count($users);
        $error_count = 0;

        // 取出所有的固定薪资项 和 浮动薪资项
        $titles = $sdb->table('salary_title')->list();
        $items = $sdb->table('salary_item')->where('where is_delete = 0')->list();
        foreach ($items as &$item) {
            $item['item_detail'] = json_decode($item['item_detail'], true);
        }
        unset($item);
        $titles = array_column($titles, null, 'id');
        $items = array_column($items, null, 'id');

        // 已完成
        $status = salaryCalculationModel::STATUS_FINISHED;
        $update_data = [];
        foreach ($users as $user) {
            $user_attach = json_decode($user['attach'], true);
            $user_audit = json_decode($user['audit_attach'], true);
            // 审批节点
            $audit_user = [];
            if (isset($user_audit['check'])) {
                $status = salaryCalculationModel::STATUS_WAIT_CHECK;
                $next_node = auditForm::getNextNode($user_audit['check']['rule']);
                // 下一个节点
                if (!empty($next_node)) {
                    $audit_user = is_array($next_node['audit_user']) ? $next_node['audit_user'] : intval($next_node['audit_user']);
                    $user_audit['check']['node'] = $next_node['node'];
                    $user_audit['check']['audit_user'] = $audit_user;
                }
            } elseif (isset($user_audit['approve'])) {
                $status = salaryCalculationModel::STATUS_WAIT_APPROVE;
                $next_node = auditForm::getNextNode($user_audit['approve']['rule']);
                // 下一个节点
                if (!empty($next_node)) {
                    $audit_user = is_array($next_node['audit_user']) ? $next_node['audit_user'] : intval($next_node['audit_user']);
                    $user_audit['approve']['node'] = $next_node['node'];
                    $user_audit['approve']['audit_user'] = $audit_user;
                }
            }

            // step 1 先算当月的工资（混合算薪）
            $salary = $user_attach['salary'] ?? [];
            if ($scheme['scheme_type'] == 3) {
                $salary = self::getSalary($user_attach);
            } else {
                if ($salary['day_salary']) { // 员工日薪
                    $salary['base_salary'] = $salary['day_salary'] * $user_attach['checkin']['summary_info']['should_attendance_day'];
                    $salary['total_salary'] = $salary['base_salary'];
                }
            }

            $user_result = [
                'user_info'          => $user_attach['user_info'] ?? [],
                'salary'             => $salary ?: [],
                'checkin'            => $user_attach['checkin'] ?? [],
                'assessment'         => $user_attach['assessment'] ?? [],
                'insurance_fund_tax' => $user_attach['insurance_fund_tax'] ?? [],
                'comment_reward'     => $user_attach['comment_reward'] ?? 0
            ];

            // 补多缴个税 (上月预缴个税 - 本月导入个税)
            $user_result['pre_paid_diff'] = 0;
            // 个税 需要增加预缴个税
            $prepay_tax = 0;
            if (!isset($user_attach['insurance_fund_tax']['user_tax']['total_tax_refund'])) {
                $user_result['insurance_fund_tax']['user_tax']['total_tax_refund'] = 0;
            }
            $user_result['insurance_fund_tax']['user_tax']['total_tax_refund'] += $prepay_tax;

            // step 2 计算浮动薪资项 （补贴类需要单独统计）
            $item_result_list = [];
            $item_result_map = [
                'before'    => [
                    'add' => [],
                    'sub' => []
                ],
                'after'     => [
                    'add' => [],
                    'sub' => []
                ],
                'allowance' => []
            ];
            foreach ($item_ids as $item_id) {
                $item = $items[$item_id];
                $range_month = [];
                if ($item['item_detail']['range_type'] == 2 && !empty($item['item_detail']['range_month'])) {
                    $range_month = $item['item_detail']['range_month'];
                }
                try {
                    if ($item['item_detail']['range_type'] == 2 && !in_array($month, $range_month)) {
                        $item_result = 0;
                    } else {
                        $item_result = salaryItemModel::getResult($item['item_detail'], $user_result);
                    }
                } catch (\Throwable $e) {
                    $sdb->table('salary_calculation_user')
                        ->where('where id = :id', ['id' => $user['id']])
                        ->update(['status' => salaryCalculationModel::STATUS_CALC_FAIL]);
                    $error_count++;
                    // 该条数据核算失败
                    continue;
                }
                $item_result_list[] = [
                    'item_id'     => $item_id,
                    'item_result' => $item_result
                ];
                if ($item['item_tag'] == 1) { // 加薪项
                    if ($item['item_type'] == 1) { // 税前
                        $item_result_map['before']['add'][] = $item_result;
                    } elseif ($item['item_type'] == 2) { // 税后
                        $item_result_map['after']['add'][] = $item_result;
                    }
                } elseif ($item['item_tag'] == 2) { // 减薪项
                    if ($item['item_type'] == 1) { // 税前
                        $item_result_map['before']['sub'][] = $item_result;
                    } elseif ($item['item_type'] == 2) { // 税后
                        $item_result_map['after']['sub'][] = $item_result;
                    }
                } elseif ($item['item_tag'] == 3) { // 补贴项
                    $item_result_map['allowance'][] = $item_result;
                }

            }
            $user_result['item_result'] = $item_result_list;

            // 统计
            // 补贴 公式 = 加班餐补 + 浮动薪资项中的所有补贴类薪资项 allowance
            $user_result['allowance'] = $user_attach['checkin']['summary_info']['overtime_dinner_addition'] ?? 0;
            // 不买社保， 需要补贴
            if ($user_attach['insurance_fund_tax']['social_insurance']['status'] == 0 && $user_attach['insurance_fund_tax']['social_insurance']['is_allowance'] == 1) {
                $user_result['allowance'] += $social_security[$user_attach['user_info']['work_place']];
            }
            // 不买公积金， 需要补贴
            if ($user_attach['insurance_fund_tax']['housing_fund']['status'] == 0 && $user_attach['insurance_fund_tax']['housing_fund']['is_allowance'] == 1) {
                $user_result['allowance'] += $housing_fund[$user_attach['user_info']['work_place']];

            }
            foreach ($item_result_map['allowance'] as $item) {
                $user_result['allowance'] += $item;
            }

            // 离职补贴 dimission_allowance user_info的数据

            // step 3 计算固定薪资项 （特别关注补贴）
            $before_add = self::getTitleByTagAndType($user_result, $titles, 1, 1);
            $after_add = self::getTitleByTagAndType($user_result, $titles, 1, 2);
            $before_sub = self::getTitleByTagAndType($user_result, $titles, 2, 1);
            $after_sub = self::getTitleByTagAndType($user_result, $titles, 2, 2);

            // step 4 工资合计 salary_total 是所有税前加薪项之和 （综合工资+全勤奖+工龄+删差评+绩效奖金+加班费+补贴+补多缴个税+离职补偿）
            $salary_total = 0;
            foreach ($before_add as $item) {
                $salary_total += $item['value'];
            }
            foreach ($item_result_map['before']['add'] as $item) {
                $salary_total += $item;
            }
            $salary_total = round($salary_total, 2);
            $user_result['salary_total'] = $salary_total;

            // step 5 应发工资 should_salary 是所有税前项之和 (综合工资+全勤奖+工龄+删差评+绩效奖金+加班费+补贴+补多缴个税+离职补偿+考勤扣款+乐捐扣款+社保差额+公积金差额)
            foreach ($before_sub as $item) {
                $salary_total -= $item['value'];
            }
            foreach ($item_result_map['before']['sub'] as $item) {
                $salary_total -= $item;
            }
            $salary_total = round($salary_total, 2);
            $user_result['should_salary'] = $salary_total;

            // step 6 实发工资合计 real_salary_total 实发合计 = 应发-个人社保-个人公积金-个人个税
            foreach ($after_add as $item) {
                $salary_total += $item['value'];
            }
            foreach ($item_result_map['after']['add'] as $item) {
                $salary_total += $item;
            }
            foreach ($after_sub as $item) {
                $salary_total -= $item['value'];
            }
            foreach ($item_result_map['after']['sub'] as $item) {
                $salary_total -= $item;
            }
            $salary_total = round($salary_total, 2);
            $user_result['real_salary_total'] = $salary_total;

            // step 7 实发1 real_salary1 实发1 = 实发工资合计
            $user_result['real_salary1'] = $user_result['real_salary_total'];
            // step 8 实发2 real_salary2 实发2 = 实发合计 - 实发1
            $user_result['real_salary2'] = round($user_result['real_salary_total'] - $user_result['real_salary1'], 2);

            // step 9 报税工资 tax_salary 报税工资 = 应发工资
            $user_result['tax_salary'] = $user_result['should_salary'];

            // 当前算薪月内离职，计算个税预缴工资
            if ($user_attach['user_info']['leave_date']) {
                $leave_time = strtotime($user_attach['user_info']['leave_date']);
                if ($leave_time >= strtotime(date('Y-m-01', strtotime($month))) && $leave_time <= strtotime(date('Y-m-t', strtotime($month)))) {
                    // 实际报税工资 = 本月【报税工资】- 本月【个人社保部分】 - 本月【个人公积金部分】
                    $user_tax_salary = $user_result['tax_salary']
                        - $user_attach['insurance_fund_tax']['user_social_insurance']['total_personal']
                        - $user_attach['insurance_fund_tax']['user_housing_fund']['total_personal'];
                    $user_tax_salary = round($user_tax_salary, 2);
                    $user_prepay_tax = self::getTaxPrepay($user_tax_salary, $user_attach['insurance_fund_tax']['user_tax'], $month);
                    $user_prepay_tax = round($user_prepay_tax, 2);
                    $sdb->table('user_tax_prepay')->insert([
                            'qwuser_id' => $user['user_id'],
                            'month'     => $month,
                            'attach'    => json_encode([
                                'hire_date'             => $user_attach['user_info']['hire_date'],
                                'leave_date'            => $user_attach['user_info']['leave_date'],
                                'tax_salary'            => $user_result['tax_salary'],
                                'dimission_allowance'   => $user_result['user_info']['dimission_allowance'],
                                'user_housing_fund'     => $user_attach['insurance_fund_tax']['user_housing_fund']['total_personal'],
                                'user_social_insurance' => $user_attach['insurance_fund_tax']['user_social_insurance']['total_personal'],
                                'sys_num'               => $user_prepay_tax,

                            ], JSON_UNESCAPED_UNICODE)]
                    );
                }
            }

            // 公司承担社保公积金 total_corp 公式 = 公司承担社保 + 公司承担公积金
            $user_result['total_corp'] = 0;
            $user_result['total_corp'] += $user_attach['insurance_fund_tax']['user_social_insurance']['total_corp'] ?? 0;
            $user_result['total_corp'] += $user_attach['insurance_fund_tax']['user_housing_fund']['total_corp'] ?? 0;
            $user_result['total_corp'] = round($user_result['total_corp'], 2);

            $update_data[] = [
                'id'           => $user['id'],
                'status'       => $status,
                'audit_user'   => $audit_user ? json_encode($audit_user) : null,
                'audit_attach' => json_encode($user_audit, JSON_UNESCAPED_UNICODE),
                'result'       => json_encode($user_result, JSON_UNESCAPED_UNICODE)
            ];
        }
        if (!empty($update_data)) {
            $sdb->table('salary_calculation_user')->updateBatch($update_data);
        }

        if ($error_count == $total) {
            $status = salaryCalculationModel::STATUS_CALC_FAIL;
        }

        // 更新算薪状态
        $update_data = [
            'status' => $status
        ];
        $sdb->table('salary_calculation')->where('where id = :id', ['id' => $calc_id])->update($update_data);

        // 消息通知
        if ($audit_user && in_array($status, [salaryCalculationModel::STATUS_WAIT_CHECK, salaryCalculationModel::STATUS_WAIT_APPROVE])) {
            $action = $status == salaryCalculationModel::STATUS_WAIT_CHECK ? '审核' : '审批';
            $audit_user = is_array($audit_user) ? $audit_user : [$audit_user];
            $user_map = dbMysql::getInstance()->table('qwuser')->field('wid')->list();
            $user_map = array_column($user_map, 'wid', 'id');

            $wids = array_intersect_key($user_map, array_flip($audit_user));
            $wids = array_values($wids);
            messagesFrom::senMeg($wids, 1, "请及时{$action}【{$calculation['calc_name']}_{$calculation['month']}】", $calculation['id'],  "","薪资计算");
        }

        // 将定时任务状态改为已完成
        customCrontabModel::finishCrontab($crontab['id']);

        return true;
    }

    // 算薪失败
    public static function calcFail($crontab)
    {
        $calc_id = $crontab['link_id'];
        $sdb = dbSMysql::getInstance();
        $adb = dbAMysql::getInstance();
        $calculation = $sdb->table('salary_calculation')->where('where id = :id', ['id' => $calc_id])->one();
        if (empty($calculation)) {
            // 将定时任务状态改为已完成
            customCrontabModel::finishCrontab($crontab['id']);
            return false;
        }
        if ($calculation['status'] != salaryCalculationModel::STATUS_CALCULATING) {
            // 将定时任务状态改为已完成
            customCrontabModel::finishCrontab($crontab['id']);
            return false;
        }

        // 查询当前算薪计算下的用户
        $users = $sdb->table('salary_calculation_user')->where('where calc_id = :calc_id', ['calc_id' => $calc_id])->list();
        if (empty($users)) {
            // 将定时任务状态改为已完成
            customCrontabModel::finishCrontab($crontab['id']);
            return false;
        }

        $update_data = [
            'status' => salaryCalculationModel::STATUS_CALC_FAIL
        ];
        $sdb->table('salary_calculation')->where('where id = :id', ['id' => $calc_id])->update($update_data);
        $sdb->table('salary_calculation_user')->where('where calc_id = :calc_id', ['calc_id' => $calc_id])->update($update_data);

        $user_map = dbMysql::getInstance()->table('qwuser')->field('wid')->list();
        $user_map = array_column($user_map, 'wid', 'id');
        $msg_user_ids = $calculation['operator'] ? [$user_map[$calculation['operator']]] : [];
        if (empty($msg_user_ids)) {
            // 通知给系统管理员
            $users = $sdb->table('user_roles')
                ->field('user_id')
                ->where('where role_id = 1 and is_delete = 0 and status = 1')->list();
            $msg_user_ids = array_column($users, 'user_id');
        }
        $user_map = dbMysql::getInstance()->table('qwuser')->field('wid')->list();
        $user_map = array_column($user_map, 'wid', 'id');

        $wids = array_intersect_key($user_map, array_flip($msg_user_ids));
        $wids = array_values($wids);
        messagesFrom::senMeg($wids, 1, "【{$calculation['calc_name']}_{$calculation['month']}】核算失败", $calculation['id'], "","薪资计算");

        // 将定时任务状态改为已完成
        customCrontabModel::finishCrontab($crontab['id']);
        return true;
    }

    public static function changeCalculationStatus($id)
    {
        $sdb = dbSMysql::getInstance();
        $calculation = $sdb->table('salary_calculation')->where('where id = :id', ['id' => $id])->one();

        $user_map = dbMysql::getInstance()->table('qwuser')->field('wid')->list();
        $user_map = array_column($user_map, 'wid', 'id');
        $msg_user_ids = $calculation['operator'] ? [$user_map[$calculation['operator']]] : [];
        if (empty($msg_user_ids)) {
            // 通知给系统管理员
            $users = $sdb->table('user_roles')
                ->field('user_id')
                ->where('where role_id = 1 and is_delete = 0 and status = 1')->list();
            $msg_user_ids = array_column($users, 'user_id');
        }
        $user_map = dbMysql::getInstance()->table('qwuser')->field('wid')->list();
        $user_map = array_column($user_map, 'wid', 'id');

        $wids = array_intersect_key($user_map, array_flip($msg_user_ids));

        $users = $sdb->table('salary_calculation_user')
            ->field('id, status')
            ->where('where calc_id = :calc_id and is_delete = 0', ['calc_id' => $id])->list();
        $status_map = [
            salaryCalculationModel::STATUS_WAIT_CALC    => 0,
            salaryCalculationModel::STATUS_CALCULATING  => 0,
            salaryCalculationModel::STATUS_WAIT_CHECK   => 0,
            salaryCalculationModel::STATUS_WAIT_APPROVE => 0,
            salaryCalculationModel::STATUS_FINISHED     => 0,
            salaryCalculationModel::STATUS_CANCEL       => 0,
            salaryCalculationModel::STATUS_INVALID      => 0,
            salaryCalculationModel::STATUS_CALC_FAIL    => 0,
            salaryCalculationModel::STATUS_CHECK_FAIL   => 0,
            salaryCalculationModel::STATUS_APPROVE_FAIL => 0
        ];

        $total = 0;
        foreach ($users as $user) {
            $status_map[$user['status']] += 1;
            $total++;
        }

        $calc_status = '';
        // 所有用户核算失败，算薪状态为核算失败
        if ($status_map[salaryCalculationModel::STATUS_CALC_FAIL] == $total) {
            $calc_status = salaryCalculationModel::STATUS_CALC_FAIL;
        }
        // 有一个是待审核, 算薪状态为待审核
        if ($status_map[salaryCalculationModel::STATUS_WAIT_CHECK]) {
            $calc_status = salaryCalculationModel::STATUS_WAIT_CHECK;
        }
        // 所有用户都是待审批，算薪状态为待审批
        if ($status_map[salaryCalculationModel::STATUS_WAIT_APPROVE] == $total) {
            $calc_status = salaryCalculationModel::STATUS_WAIT_APPROVE;
        }
        // 有一个用户已完成，算薪状态为已完成
        if ($status_map[salaryCalculationModel::STATUS_FINISHED]) {
            $calc_status = salaryCalculationModel::STATUS_FINISHED;
        }
        // 所有用户审批不通过，算薪状态为审批不通过
        if ($status_map[salaryCalculationModel::STATUS_APPROVE_FAIL] == $total) {
            $calc_status = salaryCalculationModel::STATUS_APPROVE_FAIL;
            messagesFrom::senMeg($wids, 1, "【{$calculation['calc_name']}_{$calculation['month']}】审批未通过", $calculation['id'], "", "薪资计算");
        }
        // 所有用户审核不通过，算薪状态为审核不通过
        if ($status_map[salaryCalculationModel::STATUS_CHECK_FAIL] == $total) {
            $calc_status = salaryCalculationModel::STATUS_CHECK_FAIL;
            messagesFrom::senMeg($wids, 1, "【{$calculation['calc_name']}_{$calculation['month']}】审核未通过", $calculation['id'], "", "薪资计算");
        }

        // 更新算薪状态
        if ($calc_status) {
            $update_data = [
                'status' => $calc_status
            ];
            dbSMysql::getInstance()->table('salary_calculation')->where('where id = :id', ['id' => $id])->update($update_data);
        }
        return;
    }


    public static function getTitleByTagAndType($data, $titles, $tag, $type)
    {
        $ret = [];

        foreach ($titles as $title) {
            if ($title['tag'] != $tag || $title['type'] != $type) {
                continue;
            }
            $name_en = $title['name_en'];
            $value = self::getValueByPath($data, $name_en);

            // 只添加存在的值，防止空数据
            $ret[] = [
                'name_en' => $name_en,
                'value'   => $value === null ? 0 : $value
            ];
        }

        return $ret;
    }

    /**
     * 递归解析 `.` 分隔的键名路径，获取 `$data` 对应值
     */
    public static function getValueByPath($data, $path, $delimiter = '.')
    {
        $keys = explode($delimiter, $path);

        foreach ($keys as $key) {
            if (!is_array($data) || !isset($data[$key])) {
                return null; // 防止 Undefined index
            }
            $data = $data[$key]; // 逐层深入
        }

        return $data;
    }

    private static function getTaxPrepay($tax_salary, $user_tax, $month)
    {
        // 离职月【累计应纳税所得额】 = 本月导入的【累计应纳税所得额】 + 【实际报税工资】
        $total_taxable_income = $tax_salary + $user_tax['total_tax_refund'];
        $rate = self::getTaxRate($total_taxable_income);
        $deduction = self::getTaxRate($total_taxable_income, 'deduction');
        $prepay = ($total_taxable_income - 5000) * $rate - $deduction / 12 * intval(date('m', strtotime($month))) + 20;

        return round($prepay, 2);

    }

    private static function getTaxRate($salary, $type = 'rate')
    {
        $tax_setting = [
            ['rate' => 0.03, 'deduction' => 0],
            ['rate' => 0.10, 'deduction' => 2520],
            ['rate' => 0.20, 'deduction' => 16920],
            ['rate' => 0.25, 'deduction' => 31920],
            ['rate' => 0.30, 'deduction' => 52920],
            ['rate' => 0.35, 'deduction' => 85920],
            ['rate' => 0.45, 'deduction' => 181920],
        ];
        $map = array_column($tax_setting, $type);
        if ($salary <= 36000) {
            return $map[0];
        } elseif ($salary <= 144000) {
            return $map[1];
        } elseif ($salary <= 300000) {
            return $map[2];
        } elseif ($salary <= 420000) {
            return $map[3];
        } elseif ($salary <= 660000) {
            return $map[4];
        } elseif ($salary <= 960000) {
            return $map[5];
        } else {
            return $map[6];
        }
    }

    public static function getSalary($data)
    {
        $value = [
            'base_salary'        => 0,
            'position_salary'    => 0,
            'secret_salary'      => 0,
            'welfare_salary'     => 0,
            'performance_salary' => 0,
            'total_salary'       => 0,
        ];
        $should_attendance_day = $data['checkin']['summary_info']['should_attendance_day'];
        $days = $data['checkin']['checkin_day'];
        $salaries = $data['salary'];
        array_multisort(array_column($salaries, 'effective_date'), SORT_ASC, $salaries);

        foreach ($days as $day => $day_detail) {
            $current_salary = null;
            foreach ($salaries as $salary) {
                if (strtotime($day) >= strtotime($salary['effective_date'])) {
                    $current_salary = $salary;
                }
                if (strtotime($day) < strtotime($salary['effective_date'])) {
                    break;
                }
            }
            if ($current_salary && isset($day_detail['actual_attendance_day']) && $day_detail['actual_attendance_day'] > 0) {
                if ($current_salary['day_salary']) {
                    $value['base_salary'] += $current_salary['day_salary'] * $day_detail['actual_attendance_day'];
                    $value['total_salary'] += $current_salary['day_salary'] * $day_detail['actual_attendance_day'];
                } else {
                    $value['base_salary'] += $current_salary['base_salary'] / $should_attendance_day * $day_detail['actual_attendance_day'];
                    $value['position_salary'] += $current_salary['position_salary'] / $should_attendance_day * $day_detail['actual_attendance_day'];
                    $value['secret_salary'] += $current_salary['secret_salary'] / $should_attendance_day * $day_detail['actual_attendance_day'];
                    $value['welfare_salary'] += $current_salary['welfare_salary'] / $should_attendance_day * $day_detail['actual_attendance_day'];
                    $value['performance_salary'] += $current_salary['performance_salary'] / $should_attendance_day * $day_detail['actual_attendance_day'];
                    $value['total_salary'] += $current_salary['total_salary'] / $should_attendance_day * $day_detail['actual_attendance_day'];
                }
            }
        }

        return array_map(function ($item) {
            return round($item, 2);
        }, $value);

    }


}