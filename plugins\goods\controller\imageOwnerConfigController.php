<?php

namespace plugins\goods\controller;

use plugins\goods\form\imageOwnerConfigFrom;
//图片审核配置
class imageOwnerConfigController
    {
    //拉取图片负责人列表
    public function getImageOwnerList()
    {
        $paras_list = [
            'page',              // 页码
            'page_size',          // 每页数量
            'country_code',      // 站点（国家code）
            'type',              // 需求性质
            'distributor_id',    // 组长（任务分配者）
            'category_id',     // 类目
        ];

        $param = arrangeParam($_POST, $paras_list);
        
        imageOwnerConfigFrom::getImageOwnerList($param);
    }
    /**
     * 删除图片负责人配置
     */
    public function deleteImageOwner()
    {
        $id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
        imageOwnerConfigFrom::deleteImageOwner($id);
    }

    /**
     * 新增或编辑图片负责人配置
     */
    public function saveImageOwner()
    {
        $paras_list = [
            'id',                // 配置ID，不传为新增，传为编辑
            'country_code',      // 站点
            'type',              // 需求性质
            'category_id',     // 类目
            'distributor_id',    // 美工组长
            'group_name',        // 组名
        ];
        
        $param = arrangeParam($_POST, $paras_list);
        imageOwnerConfigFrom::saveImageOwner($param);
    }
    //终线设置
    public function getFinalLine()
    {
        $paras_list = [
            'user_ids'
        ];

        $param = arrangeParam($_POST, $paras_list);

        imageOwnerConfigFrom::getFinalLine($param);
    }
    //获取终线设置当前信息
    public function getFinalLineCurrent()
    {
        imageOwnerConfigFrom::getFinalLineCurrent();
    }

}