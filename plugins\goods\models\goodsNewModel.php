<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/21 16:06
 */

namespace  plugins\goods\models;

use plugins\goods\common\authenticationCommon;
use plugins\goods\form\goodsNewFrom;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;

class goodsNewModel
{
    public static array $goods_info = [];
    //验证新品模板是否还可以操作
    static public function varifyAllowOperate($goods_id) {
        $db = dbMysql::getInstance();
        $goods_data = $db->query('select id,manage_info,status from oa_goods_new where id=:id',['id'=>$goods_id]);
        if (in_array($goods_data['status'],[2,4])) {
            switch ($goods_data['status']) {
                case 2:
                    SetReturn(-1,'该项目对应的新品已出货，不可操作');
                case 4:
                    SetReturn(-1,'该项目对应的新品已暂停，不可操作');
            }
        }
        return $goods_data;
    }

    //保存新品基础信息
    static public function setGoodsInfo($param) {
        $goods_id = (int)$param['id']??0;
        $goods_name = trim($param['goods_name']);
        $data_ = [
            'alias_name'=>$param['alias_name'],
            'handset_types'=>$param['handset_types'],
            'goods_size'=>(int)$param['goods_size'],
            'is_app'=>$param['is_app'],
            'is_electriferous'=>(int)$param['is_electriferous'],
            'goods_img'=>$param['goods_img'],
            'thumb_src'=>$param['thumb_src'],
            'function_id'=>$param['function_id'],
            'cat_id'=>$param['cat_id'],
            'operator_info'=>$param['operator_info'],
            'description'=>$param['description'],
            'supplier_id'=>(int)$param['supplier_id'],
        ];
        //判断商品名称是否重复
        $db = dbMysql::getInstance();
        try {
            //修改商品信息
            if ($goods_id) {
                $goods = $db->query('select * from oa_goods_new where id = :id and is_delete = 0',['id'=>$goods_id]);
                if (!$goods) {
                    SetReturn(-1, '产品不存在');
                }
                if (!empty($goods['goods_name']) && empty($goods_name)) {
                    SetReturn(-1, '产品中文名不能为空');
                }
                if (!$goods) {
                    SetReturn(-1, '新品不存在');
                }
                if ($goods['is_app'] != $param['is_app']) {
                    $project_count = $db->table('goods_project')
                        ->where('where goods_id=:goods_id',['goods_id'=>$goods_id])
                        ->count();
                    if ($project_count) {
                        SetReturn(-1, '新品已存在流程不可修改是否【APP产品】');
                    }
                }
                $db->table('goods_new');
                $data_['manage_info'] = $param['manage_info'];
                //验证绑定权限
                authenticationCommon::authEditGoodsvarify(json_decode($goods['manage_info'], true));
                //判断产品是否可操作
                goodsNewFrom::varifyAllowOperateGoods($goods);
                //查看商品是否名称唯一
                if ($param['is_app'] == 0 || (!empty($goods['goods_name']))) {
                    $goods_other = $db->query('select id from oa_goods_new where is_delete = 0 and goods_name = :goods_name and id <> :id',['id'=>$goods_id,'goods_name'=>$goods_name]);
                    if ($goods_other) {
                        SetReturn(-1, '该产品名称已存在');
                    }
                    $data_['goods_name'] = $goods_name;
                }
                //修改
                $data_['updated_at'] = date('Y-m-d H:i:s');
                $db->where('where id = :id', ['id'=>$goods_id]);
                if (!$db->update($data_)) {
                    $db->rollBack();
                    SetReturn(-1, '修改商品信息失败');
                }
                self::$goods_info = $db->query('select * from oa_goods_new where id = :id',['id'=>$goods_id]);;
                goodsNewFrom::setUpdateLog($goods,$data_);
            } else {
                $db->table('goods_new');
                $data_['user_id'] = userModel::$qwuser_id;
                $data_['manage_info'] = $param['manage_info'];
                $data_['created_at'] = date('Y-m-d H:i:s');
                if ($data_['is_app'] == 0) {
                    $data_['goods_name'] = $goods_name;
                }
                $db->where('',[]);
                //验证重复名称
                if (!$param['is_app']) {
                    $goods = $db->query('select id from oa_goods_new where is_delete = 0 and goods_name = :goods_name',['goods_name'=>$goods_name]);
                    if ($goods) {
                        SetReturn(-1, '该新品名称已存在');
                    }
                }
                //绑定goods_code
                $code_data = $db->query('select * from oa_goods_code where goods_id = 0 order by id asc limit 1');
                if (!$code_data) {
                    SetReturn(-1, 'app内置编码已用完，请联系开发人员处理');
                }
                //保存
                $goods_id = $db->insert($data_);
                self::$goods_info = $data_;
                $code_ = $db->table('goods_code')
                    ->where('where goods_id=:goods_id',['goods_id'=>$goods_id])
                    ->one();
                if ($code_) {
                    SetReturn(-1, '该产品已经有内置app，请联系开发人员处理');
                }
                $db->query('update oa_goods_code set goods_id=:goods_id where id=:code_id ',['code_id'=>$code_data['id'],'goods_id'=>$goods_id]);
                if (!$goods_id) {
                    $db->rollBack();
                    SetReturn(-1, '新增商品信息失败');
                }
            }
            return $goods_id;
        } catch (ExceptionError $e){
            $db->rollBack();
            throw new ExceptionError($e->getMessage());
        }
    }
}