<?php

namespace plugins\checkin\controller;

use admin\models\qwdepartmentModel;
use plugins\checkin\form\checkinForm;
use plugins\checkin\form\messagesFrom;
use plugins\checkin\models\holidayModel;
use plugins\checkin\models\userModel;
use core\lib\db\dbCMysql;
use core\lib\db\dbMysql;
use core\lib\db\dbSMysql;
use Rap2hpoutre\FastExcel\FastExcel;
use plugins\salary\models\salaryCalculationModel;

class userCheckinSummaryController
{
    // 获取汇总列表
    public function getList()
    {
        $paras_list = array('corp_id', 'corp_group_id', 'month', 'users', 'page', 'page_size');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        if (!$param['month']) returnError('考勤月份必填');
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        $cdb = dbCMysql::getInstance();
        $cdb->table('user_checkin_summary');
        $cdb->where('where summary_month = :month', ['month' => $param['month']]);
        $cdb->order('id desc');

        // 权限控制
        $user_id = userModel::$qwuser_id;
        $all_department_users = qwdepartmentModel::getUserDepartment(userModel::$wid)['all_department_users'] ?: []; // 当前用户管理的部门及其子部门下的用户
        $user_ids = array_column($all_department_users, 'wid');

        if (userModel::getUserListAuth('summaryAll')) {
            // 全部权限
        } elseif (userModel::getUserListAuth('summaryDepartment')) {
            if (!empty($all_department_users)) {
                $cdb->whereIn('user_id', $user_ids);
            }
        } elseif (userModel::getUserListAuth('summaryRelated')) {
            $cdb->andWhere('user_id = :user_id', ['user_id' => $user_id]);
        } elseif (userModel::getUserListAuth('summaryCorpRule')) {
            $auth_corp_group_id = [];
            $gid = [1, 2, 3, 4, 10, 11];
            foreach ($gid as $item) {
                if (userModel::getUserListAuth('summary_'.$item)) {
                    $auth_corp_group_id[] = $item;
                }
            }
            if (empty($auth_corp_group_id)) {
                returnError('无权限查看');
            }
            $auth_corp_group_id = implode(',', $auth_corp_group_id);
            $cdb->andWhere("JSON_EXTRACT(base_info, '$.rule_groupid') in ($auth_corp_group_id)");
        }

        if (!empty($param['users'])) {
            $users = json_decode($param['users'], true);
            if (!empty($users)) {
                $cdb->whereIn('user_id', $users);
            }
        }

        if (!empty($param['corp_group_id'])) {
            $corp_group_id = json_decode($param['corp_group_id'], true);
            if (!empty($corp_group_id)) {
                $corp_group_id = array_map('intval', $corp_group_id);
                $corp_group_id = implode(',', $corp_group_id);
                $cdb->andWhere("JSON_EXTRACT(base_info, '$.rule_groupid') in ($corp_group_id)");
            }
        }

        $list = $cdb->pages($page, $limit);

        $user_ids = array_column($list['list'], 'user_id');
        $db = dbMysql::getInstance();
        $users = $db->table('qwuser')->field('id,wid,wname,position')->whereIn('wid', $user_ids)->list();
        $userMap = array_column($users, null, 'wid');

        // 格式化数据
        $ret_list = [];

        foreach ($list['list'] as $item) {
            $base_info = json_decode($item['base_info'], true);
            $ret_list[] = [
                'id'           => $item['id'],
                'user_id'      => $item['user_id'],
                'user_name'    => $base_info['wname'] ?? '',
                'departs_name' => $base_info['departs_name'],
                'position'     => $userMap[$item['user_id']]['position'] ?? '',
                'base_info'    => $base_info,
                'checkin_day'  => json_decode($item['checkin_day'], true),
                'summary_info' => json_decode($item['summary_info'], true),
                'is_confirm'   => $item['is_confirm'],
            ];
        }
        $list['list'] = $ret_list;
        
        returnSuccess($list);
    }

    // 导出
    public function export()
    {
        $paras_list = array('corp_id', 'month', 'users', 'corp_group_id');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        if (!$param['month']) returnError('考勤月份必填');
        $cdb = dbCMysql::getInstance();
        $cdb->table('user_checkin_summary');
        $cdb->where('where summary_month = :month', ['month' => $param['month']]);
        $cdb->order('id desc');

         // 权限控制
         $user_id = userModel::$qwuser_id;
         $all_department_users = qwdepartmentModel::getUserDepartment(userModel::$wid)['all_department_users'] ?: []; // 当前用户管理的部门及其子部门下的用户
         $user_ids = array_column($all_department_users, 'wid');
 
         if (userModel::getUserListAuth('summaryAll')) {
             // 全部权限
         } elseif (userModel::getUserListAuth('summaryDepartment')) {
             if (!empty($all_department_users)) {
                 $cdb->whereIn('user_id', $user_ids);
             }
         } elseif (userModel::getUserListAuth('summaryRelated')) {
             $cdb->andWhere('user_id = :user_id', ['user_id' => $user_id]);
         } elseif (userModel::getUserListAuth('summaryCorpRule')) {
             $auth_corp_group_id = [];
             $gid = [1, 2, 3, 4, 10, 11];
             foreach ($gid as $item) {
                 if (userModel::getUserListAuth('summary_'.$item)) {
                     $auth_corp_group_id[] = $item;
                 }
             }
             if (empty($auth_corp_group_id)) {
                 returnError('无权限查看');
             }
             $auth_corp_group_id = implode(',', $auth_corp_group_id);
             $cdb->andWhere("JSON_EXTRACT(base_info, '$.rule_groupid') in ($auth_corp_group_id)");
         }

        if (!empty($param['users'])) {
            $users = json_decode($param['users'], true);
            if (!empty($users)) {
                $cdb->whereIn('user_id', $users);
            }
        }
        if (!empty($param['corp_group_id'])) {
            $corp_group_id = json_decode($param['corp_group_id'], true);
            if (!empty($corp_group_id)) {
                $corp_group_id = array_map('intval', $corp_group_id);
                $corp_group_id = implode(',', $corp_group_id);
                $cdb->andWhere("JSON_EXTRACT(base_info, '$.rule_groupid') in ($corp_group_id)");
            }
        }

        $list = $cdb->list();

        $user_ids = array_column($list, 'user_id');
        $db = dbMysql::getInstance();
        $users = $db->table('qwuser')->field('id,wid,wname,position')->whereIn('wid', $user_ids)->list();
        $userMap = array_column($users, null, 'wid');

        // 格式化数据
        $excel_data = [];
        $week_map = ['1' => '周一', '周二', '周三', '周四', '周五', '周六', '周日'];

        foreach ($list as $item) {
            $base_info = json_decode($item['base_info'], true);
            $checkin_day = json_decode($item['checkin_day'], true);
            $summary_info = json_decode($item['summary_info'], true);
            $excel_item = [
                '姓名' => $userMap[$item['user_id']]['wname'] ?? '',
                '部门' => $base_info['departs_name'],
                '岗位' => $userMap[$item['user_id']]['position'] ?? '',
            ];

            for ($i = 1; $i <= count($checkin_day); $i++) {
                $day = date('Y-m-'.sprintf('%02d', $i), strtotime($param['month']));
                $day_info = $checkin_day[$day] ?? [];
                $week = $week_map[date('N', strtotime($day))];
                $key = date('m-d', strtotime($day)) . '(' . $week . ')';
                if ($day_info['is_work']) {
                    if (empty($day_info['detail'])) {
                        $excel_item[$key] = '正常';
                    } else {
                        $excel_item[$key] = implode(',', array_column($day_info['detail'], 'text'));
                    }
                } else {
                    if (empty($day_info['detail'])) {
                        $excel_item[$key] = '';
                    } else {
                        $excel_item[$key] = implode(',', array_column($day_info['detail'], 'text'));
                    }
                }
            }
            $excel_item['病假全薪/天'] = $summary_info['sick_full_salary'];
            $excel_item['病假最低/天'] = $summary_info['sick_low_salary'];
            $excel_item['事假/天'] = $summary_info['personal_leave_day'];
            $excel_item['事假/小时'] = $summary_info['personal_leave_hour'];
            $excel_item['旷工/天'] = $summary_info['absenteeism_day'];
            $excel_item['年假/天'] = $summary_info['annual_leave_day'];
            $excel_item['调休/天'] = $summary_info['adjustable_day'];
            $excel_item['婚假/天'] = $summary_info['marriage_leave_day'];
            $excel_item['产假/天'] = $summary_info['maternity_leave_day'];
            $excel_item['丧假/天'] = $summary_info['funeral_leave_day'];
            $excel_item['产检假/天'] = $summary_info['prenatal_check_leave_day'];
            $excel_item['体检假/天'] = $summary_info['physical_examination_leave_day'];
            $excel_item['其他/天'] = $summary_info['other_leave_day'];
            $excel_item['缺卡/次'] = $summary_info['lack_card'];
            $excel_item['出差/小时'] = $summary_info['business_trip_hour'];
            $excel_item['迟到/次'] = $summary_info['late'];
            $excel_item['早退/次'] = $summary_info['early'];
            $excel_item['迟到不扣款/次'] = $summary_info['safe_late'];
            $excel_item['早退不扣款/次'] = $summary_info['safe_early'];
            $excel_item['工作日加班/次'] = $summary_info['workday_overtime'];
            $excel_item['休息日加班/次'] = $summary_info['restday_overtime'];
            $excel_item['节假日加班/次'] = $summary_info['holiday_overtime'];
            $excel_item['上班补卡/次'] = $summary_info['work_card'];
            $excel_item['下班补卡/次'] = $summary_info['off_card'];
            $excel_item['病假扣款'] = $summary_info['sick_deduction'];
            $excel_item['事假扣款'] = $summary_info['personal_deduction'];
            $excel_item['旷工扣款'] = $summary_info['absenteeism_deduction'];
            $excel_item['缺卡扣款'] = $summary_info['lack_card_deduction'];
            $excel_item['迟到扣款'] = $summary_info['late_deduction'];
            $excel_item['早退扣款'] = $summary_info['early_deduction'];
            $excel_item['考勤扣款'] = $summary_info['attendance_deduction'];
            $excel_item['乐捐扣款'] = $summary_info['donation_deduction'];
            $excel_item['加班餐补'] = $summary_info['overtime_dinner_addition'];
            $excel_item['加班费'] = $summary_info['overtime_addition'];
            $excel_item['加班调休/天'] = $summary_info['overtime_rest'];
            $excel_item['应出勤天数'] = $summary_info['should_attendance_day'];
            $excel_item['实际出勤天数'] = $summary_info['actual_attendance_day'];
            $excel_item['满勤天数'] = $summary_info['full_attendance_day'];
            $excel_item['全勤奖励'] = $summary_info['full_attendance_reward'];

            $excel_data[] = $excel_item;
        }

        // 准备导出数据
        // 使用项目根目录动态构建导出文件路径
        $exportPath = SELF_FK . '/public/checkin/temp/user_checkin_data' . $param['month'] . '.xlsx';

        // 检查并创建文件夹
        $exportDir = dirname($exportPath);
        if (!file_exists($exportDir)) {
            mkdir($exportDir, 0777, true);
        }

        // 使用 FastExcel 导出数据
        (new FastExcel($excel_data))->export($exportPath);

        // 返回导出文件路径
        $exportPathqd = '/public/checkin/temp/user_checkin_data' . $param['month'] . '.xlsx';
        returnSuccess($exportPathqd);

    }

    // 发起确认
    public function confirm()
    {
        $paras_list = array('ids');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        empty($param['ids']) && returnError('ids必填');
        $ids = json_decode($param['ids'], true);
        $cdb = dbCMysql::getInstance();
        $cdb->table('user_checkin_summary');
        $cdb->whereIn('id', $ids);
        $list = $cdb->list();
        if (empty($list)) returnError('数据不存在');

        $need_confirm_ids = [];
        foreach ($list as $item) { 
            if ($item['is_confirm'] == 2 || $item['is_confirm'] == null) {
                $need_confirm_ids[] = $item['id'];
            }
        }
        if (empty($need_confirm_ids)) returnError('没有可以发起确认的数据');

        $cdb->table('user_checkin_summary');
        $cdb->whereIn('id', $need_confirm_ids)->update(['is_confirm' => 0, 'confirm_create_user_id' => userModel::$wid]);


        $company_name = '深圳易威行科技创新有限公司';
        $user_name = userModel::$wname;
        foreach ($list as $item) {
            if (!in_array($item['id'], $need_confirm_ids)) continue;
            $month = $item['summary_month'];
            $user_id = $item['user_id'];
            messagesFrom::senMeg([$user_id], 1, "{$user_name}已发布【{$company_name}_{$month}考勤】，请尽快确认", $item['id']);
        }

        returnSuccess([], '发起确认成功');
    }

    // 重新汇总
    public function reSummary()
    {
        $paras_list = array('id');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        empty($param['id']) && returnError('id必填');
        $id = $param['id'];
        $cdb = dbCMysql::getInstance();
        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();

        $cdb->table('user_checkin_summary');
        $cdb->where('where id = :id', ['id' => $id]);
        $item = $cdb->one();
        if (empty($item)) returnError('数据不存在');

        $wid = $item['user_id'];
        $month = $item['summary_month'];
        $start_time = date('Y-m-01', strtotime($month));
        $end_time = date('Y-m-t', strtotime($month));

        // 查询是否有当月的算薪
        $calculation = salaryCalculationModel::getCalculationByMonth($month);
        if ($calculation) returnError('已进行算薪的不可汇总');

        // 获取所有用户
        $users = $db->table('qwuser')->field('id, wname, wdepartment_ids,wid, position')->list();
        $userMap = array_column($users, null, 'wid');

        // 获取节假日配置
        $holidayOption = holidayModel::getHolidayByTimeRange($start_time, $end_time);

        
        // 获取假期数据
        $vacation = $cdb->table('vacation')->field('id,name,time_attr,duration_type,perday_duration')->list();
        $vacation = array_column($vacation, null, 'id');
        
        // 所有的企业打卡规则
        $corpRules = $cdb->table('corp_checkin_option')->field('id, groupid,groupname, spe_workdays, spe_offdays, rule, range_info, white_users')->list();
        $corpRules = array_column($corpRules, null, 'groupid');

        $rule_id = [];
        foreach ($corpRules as &$corp_rule) {
            $corp_rule['rule'] = json_decode($corp_rule['rule'], true);
            $corp_rule['full_days'] = 0;
            // 构造当月的日期数组
            $days = [];
            for ($i = 1; $i <= date('t', strtotime($month)); $i++) {
                $checkin_date = date('Y-m-'.sprintf('%02d', $i), strtotime($month));
                $is_work = checkinForm::isWorkDay($checkin_date, $corp_rule, $holidayOption); // 0 休息 1 工作日 2 节假日
                $days[$checkin_date] = ['is_work' => $is_work];
                if ($is_work == 1) {
                    $corp_rule['full_days']++;
                }
            }
            $corp_rule['days'] = $days;

            // 获取扣款规则
            if (isset($corp_rule['rule']['rule_1'])) $rule_id[] = $corp_rule['rule']['rule_1'];
            if (isset($corp_rule['rule']['rule_2'])) $rule_id[] = $corp_rule['rule']['rule_2'];
            if (isset($corp_rule['rule']['rule_3'])) $rule_id[] = $corp_rule['rule']['rule_3'];
            if (isset($corp_rule['rule']['rule_4'])) $rule_id[] = $corp_rule['rule']['rule_4'];
            if (isset($corp_rule['rule']['rule_5'])) $rule_id[] = $corp_rule['rule']['rule_5'];
            if (isset($corp_rule['rule']['rule_6'])) $rule_id[] = $corp_rule['rule']['rule_6'];

        }
        $ruleMap = $cdb->table('rule')->field('id,rule_name, rule_type,attach')->whereIn('id', $rule_id)->where('where is_delete = 0 and status = 1')->list();
        foreach ($ruleMap as &$r) {
            $r['attach'] = json_decode($r['attach'], true);
        }
        $ruleMap = array_column($ruleMap, null, 'id');

        // 取出当月的所有用户
        $user_list = [];
        $users = $cdb->table('checkin_user')->where('where month = :month', ['month' => $month])->list();
        foreach ($users as $user) {
            $user['users'] = json_decode($user['users'], true);
            $user_list = array_merge($user_list, $user['users']);
        }
        $user_list = array_column($user_list, null, 'wid');

        $wids = [$wid];
        // 用户基本信息
        $user_info_data = $db->table('user_info')->whereIn('wid', $wids)->list();
        $user_info = array_column($user_info_data, null, 'wid');

        $user_id = $userMap[$wid]['id'];
        // 用户薪资
        $salary = $sdb->table('user_salary')
            ->where('where qwuser_id = :user_id and  is_delete = 0 and effective_date < :effective_date', ['user_id' => $user_id, 'effective_date' => $month.'-25'])
            ->order('effective_date DESC, id desc')
            ->one();
        $salary = $salary ? json_decode($salary['salary'], true) : [];
        $total_salary = 0;
        foreach ($salary as $key => $value) {
            $total_salary += $value;
        }
        $salary['total_salary'] = $total_salary;

        $user = $user_list[$wid];
        $user['wname'] = $user_list[$wid]['name'];
        $user['wdepartment_ids'] = $userMap[$wid]['wdepartment_ids'] ?? '';
        $user['position'] = $user_list[$wid]['position'];
        $user['base_salary'] = $salary['base_salary'] ?? 0;
        $user['total_salary'] = $salary['total_salary'] ?? 0;
        $user['day_salary'] = $salary['day_salary'] ?? 0; // 这个可能会有，但也有可能会没有
        $user['hire_date'] = $user_info[$wid]['hire_date'] ?? '';

        $userSummary = checkinForm::userCheckSummary($user, $start_time, $end_time, $corpRules, $ruleMap, $vacation, $userMap);
        $userSummary['summary_month'] = $month;
        // 将已确认、已拒绝的数据更新为未确认
        if ($item['is_confirm'] == 1 || $item['is_confirm'] == 2) {
            $userSummary['is_confirm'] = null;
            $userSummary['confirm_create_user_id'] = userModel::$wid;
            $userSummary['confirm_time'] = null;
            $company_name = '深圳易威行科技创新有限公司';
            $user_name = userModel::$wname;
            messagesFrom::senMeg([$user['wid']], 1, "{$user_name}已将{$company_name}_{$month}考勤】重新汇总，请您重新确认考勤", $param['id']);
        }

        $cdb->table('user_checkin_summary')->where('id = :id', ['id' => $param['id']])->update($userSummary);
        returnSuccess([], '重新汇总成功');
    }

    // 获取提交信息
    public function submitDetail() {
        $paras_list = array('month');
        $param = arrangeParam($_GET, $paras_list);
        empty($param['month']) && returnError('月份不能为空');
        $cdb = dbCMysql::getInstance();
        $detail = $cdb->table('user_checkin_month')
            ->where('where month = :month', ['month' => $param['month']])
            ->one();
        // 查询是否有当月的算薪
        $calculation = salaryCalculationModel::getCalculationByMonth($param['month']);
        $detail['is_calculated'] = $calculation ? 1 : 0;

        returnSuccess($detail);
    }

    // 提交到算薪
    public function submitSalary()
    {
        $paras_list = array('month');
        $param = arrangeParam($_POST, $paras_list);
        empty($param['month']) && returnError('月份不能为空');

        // 查询是否有当月的算薪
        $calculation = salaryCalculationModel::getCalculationByMonth($param['month']);
        if ($calculation) returnError('已进行算薪的不可提交');

        $cdb = dbCMysql::getInstance();
        $detail = $cdb->table('user_checkin_month')
            ->where('where month = :month', ['month' => $param['month']])
            ->one();

        if ($detail) {
            $cdb->table('user_checkin_month')
                ->where('where month = :month', ['month' => $param['month']])
                ->update(['operator' => userModel::$qwuser_id]);
        } else {
            $cdb->table('user_checkin_month')
                ->insert([
                    'month' => $param['month'],
                    'operator' => userModel::$qwuser_id
                ]);
        }

        returnSuccess([], '提交成功');
    }

}