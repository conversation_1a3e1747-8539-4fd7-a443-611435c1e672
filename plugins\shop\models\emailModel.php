<?php

namespace plugins\shop\models;

use core\lib\redisCached;
use Exception;
use plugins\shop\models\relationModel;

class emailModel extends baseModel
{
    public string $table = 'email';

    public static array $paras_list = [
        'register_date'         => '注册时间|required',
        'email_account'         => '邮箱账号|required',
        'email_password'        => '邮箱密码|required',
        'email_assistant_email' => '邮箱辅助邮箱|required',
        'email_safe_phone_id'   => '邮箱安全手机|required',
        'email_user'            => '邮箱使用人',
        'email_usage'           => '用途',
        'use_platform'          => '使用平台',
        'use_status'            => '使用状态|required',
        'remark'                => '备注'
    ];

    public static array $json_keys = [
        'email_usage',
        'use_platform'
    ];

    public function __construct()
    {
        parent::__construct();
    }

    // 数据校验
    public function dataValidCheck(array $data, array $param_list = [], $is_throw = true, $error = []): array
    {
        if (empty($data)) {
            throw new Exception('邮箱数据不能为空');
        }
        return parent::dataValidCheck($data, $param_list, $is_throw, $error);
    }

    public function getList($param, $order = 'id desc', $is_export = false)
    {
        if (isset($param['relations']) && !empty($param['relations'])) {
            $ids = (new relationModel)->getIdsByRelation('email', $param['relations']);
        }
        $this->db->table($this->table, 'e')
            ->leftJoin('phone_card', 'pc', 'pc.id = e.email_safe_phone_id')
            ->field('e.*, pc.phone_number as email_safe_phone, pc.phone_manager')
            ->where('where 1=1');

        // 固定筛选
        if (!empty($param['ids'])) {
            $this->db->whereIn('e.id', $param['ids']);
        }
        if (!empty($param['email_account'])) {
            $this->db->andWhere('email_account like :email_account', ['email_account' => '%' . $param['email_account'] . '%']);
        }
        if (!empty($param['email_assistant_email'])) {
            $this->db->andWhere('email_assistant_email like :email_assistant_email', ['email_assistant_email' => '%' . $param['email_assistant_email'] . '%']);
        }
        if (!empty($param['email_safe_phone'])) {
            $this->db->andWhere('pc.phone_number = :email_safe_phone', ['email_safe_phone' => $param['email_safe_phone']]);
        }
        // 手机号保管人
        if (!empty($param['phone_manager'])) {
            $this->db->andWhere('pc.phone_manager like :phone_manager', ['phone_manager' => '%' . $param['phone_manager'] . '%']);
        }
        if (!empty($param['email_user'])) {
            $this->db->andWhere('email_user like :email_user', ['email_user' => '%' . $param['email_user'] . '%']);
        }
        if (!empty($param['use_status']) && is_array($param['use_status'])) {
            $this->db->whereIn('use_status', $param['use_status']);
        }
        // 根据用途筛选（JSON数组格式，支持数组中任一值匹配）
        if (!empty($param['email_usage']) || !empty($ids)) {
            $conditions = [];
            $param_arr = [];
            if (!empty($param['email_usage'])){
                $conditions[] = 'JSON_OVERLAPS(email_usage, :email_usage)';
                $param_arr['email_usage'] = json_encode($param['email_usage']);
            }
            $ids && $conditions[] = 'e.id in (' . implode(',', $ids) . ')';
            $this->db->andWhere('(' . implode(' OR ', $conditions) . ')', $param_arr);
        }
        if (!empty($param['use_platform']) && is_array($param['use_platform'])) {
            $this->db->andWhere('JSON_OVERLAPS(use_platform, :use_platform)', ['use_platform' => json_encode($param['use_platform'])]);
        }
        if (!empty($param['register_date'])) {
            $this->db->andWhere('e.register_date >= :register_date_start and e.register_date <= :register_date_end', [
                'register_date_start' => $param['register_date'][0],
                'register_date_end'   => $param['register_date'][1],
            ]);
        }
        if (!empty($param['update_time'])) {
            $this->db->andWhere('e.updated_at >= :updated_at_start and e.updated_at <= :updated_at_end', [
                'updated_at_start' => $param['update_time'][0],
                'updated_at_end'   => $param['update_time'][1].' 23:59:59',
            ]);
        }
        $this->db->order($order);

        if (isset($param['page']) && isset($param['page_size'])) {
            $param['page'] = $param['page'] ?: 1;
            $param['page_size'] = $param['page_size'] ?: 10;
            $list = $this->db->pages($param['page'], $param['page_size']);
            $maps = self::getMaps();
            foreach ($list['list'] as &$item) {
                $item = $this->formatItem($item, $maps);
            }
            return $list;
        } else {
            $list = $this->db->list();
            if ($is_export) {
                $export_data = [];
                $maps = self::getMaps();
                foreach ($list as $item) {
                    $item = $this->formatItem($item, $maps);
                    $paras_list = self::$paras_list;
                    $paras_list['email_safe_phone'] = '邮箱安全手机';
                    $paras_list['phone_manager'] = '手机号保管人';
                    // 使用参数对应的中文作为键名返回
                    $export_data[] = self::changeToCnKey($item, $paras_list);
                }
                return $export_data;
            }
            return $list;
        }
    }
    public function add($data, $type = '新增')
    {

        $safePhoneId = $data['email_safe_phone_id'] ?? null;
        $id = parent::add($data, $type);

        // 如果设置了安全手机,创建关联关系
        if($safePhoneId) {
            $relationModel = new relationModel();
            $relationModel->updateRelation($this->table, $id, 'phone_card', $safePhoneId);
        }

        return $id;
    }

    public function edit($data, $id, $old_data = [], $type = '编辑', $remark = '', $result = '', $other_attach = [])
    {
        $safePhoneId = $data['email_safe_phone_id'] ?? null;
        if ($safePhoneId) {
            // 验证安全手机ID是否存在
            $relationModel = new relationModel();
            $relationModel->updateRelation($this->table, $id, 'phone_card', $safePhoneId);
        }


        parent::edit($data, $id, $old_data, $type, $remark, $result, $other_attach);
    }

    public static function getMaps () {
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        $phone_card = redisCached::getPhoneCard();
        $phone_card = array_column($phone_card, null, 'id');

        return [
            'users' => $users,
            'phone_card'       => $phone_card,
        ];
    }

    public function formatItem($item, $maps = [])
    {
        $users = $maps['users'] ?? [];
        if (empty($maps['users'])) {
            $users = redisCached::getUserInfo();
            $users = array_column($users, 'user_name', 'user_id');
        }

        $phone_card = $maps['phone_card'] ?? [];
        if (empty($maps['phone_card'])) {
            $phone_card = redisCached::getPhoneCard();
            $phone_card = array_column($phone_card, null, 'id');
        }

        $maps = [
            ['name' => 'operator_name', 'maps' => $users, 'key' => 'operator'],
            ['name' => 'phone_number', 'maps' => array_column($phone_card, 'phone_number', 'id'), 'key' => 'email_safe_phone_id'],
            ['name' => 'phone_manager', 'maps' => array_column($phone_card, 'phone_manager', 'id'), 'key' => 'email_safe_phone_id'],
        ];
        return parent::formatItem($item, $maps);
    }

    // 通过手机号获取信息
    public function getByEmailAccount($email_account, $id = null)
    {
        $this->db->table($this->table)->where('where email_account = :email_account', ['email_account' => $email_account]);
        if ($id) {
            $this->db->andWhere('id != :id', ['id' => $id]);
        }
        return $this->db->one();
    }

    // 批量编辑
    public function editBatch($data)
    {
        $db = $this->db->table($this->table);
        $ids = array_column($data, 'id');
        $db->whereIn('id', $ids);
        $list = $db->list();
        $list = array_column($list, null, 'id');

        $db->beginTransaction();
        try {
            foreach ($data as $item) {
                $item_id = $item['id'];
                unset($item['id']);
                $this->dataValidCheck($item, self::$paras_list);
                // 唯一性校验
                $phone_number = $this->getByEmailAccount($item['email_account'], $item_id);
                if ($phone_number) {
                    throw new Exception('邮箱已存在');
                }
                self::edit($item, $item_id, $list[$item_id], '批量编辑', '', '批量编辑');
            }
            $db->commit();
        } catch (Exception $e) {
            $db->rollBack();
            throw new Exception($e->getMessage());
        }
    }
}
