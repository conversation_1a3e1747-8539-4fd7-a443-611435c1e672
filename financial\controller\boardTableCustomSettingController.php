<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/19 16:23
 */

namespace financial\controller;

//
use admin\controller\formController;
use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use financial\form\boardTableCustomSettingForm;
use financial\models\authListModel;
use financial\models\userModel;

class boardTableCustomSettingController
{
    public function getList() {
        $paras_list = array('table');
        $request_list = ['table' => '板块'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $db = dbFMysql::getInstance();
        $list = $db->table('table_custom_setting')
            ->where('where user_id = :user_id and table_type=:table_type and is_delete = 0',['user_id'=>userModel::$qwuser_id,'table_type'=>$param['table']])
            ->field('id,f_name,is_default')
            ->list();
        returnSuccess($list, '拉取成功');
    }
    public function detail() {
        $id = (int)$_POST['id'];
        if (!$id) {
           returnError('ID必传');
        }
        $db = dbFMysql::getInstance();
        $data = $db->table('table_custom_setting')
            ->where('where id=:id and is_delete = 0',['id'=>$id])
            ->one();
        $custom_keys = json_decode($data['custom_keys']);
        $custom_list = $db->table('column')
            ->whereIn('key_name',$custom_keys)
            ->field('key_name,column_name,data_type')
            ->list();
        $fanan_data = [];
        //先排序
        $custom_keys_f = array_flip($custom_keys);
        // 使用 uasort 函数和一个自定义的比较函数来排序数组
        uasort($custom_list, function($item1, $item2) use ($custom_keys_f) {
            // 获取两个值在排序顺序中的位置
            $order1 = $custom_keys_f[$item1['key_name']] ?? PHP_INT_MAX;
            $order2 = $custom_keys_f[$item2['key_name']] ?? PHP_INT_MAX;
            // 如果在排序顺序中找不到某个值，则将其视为最大的顺序值
            // 然后按照顺序值进行比较
            return $order1 - $order2;
        });
        foreach ($custom_list as $v) {
            if (!isset($fanan_data[$v['data_type']])) {
                $fanan_data[$v['data_type']] = [
                    'type'=>$v['data_type'],
                    'type_name'=>config::getDataName1('data_financial','column_type',$v['data_type'])
                ];
            }
            $fanan_data[$v['data_type']]['custom_list'][] = $v;
        }
        returnSuccess(['custom_list'=>$custom_keys,'fanan_data'=>$fanan_data]);
    }
    public function edit() {
        $paras_list = array('id','custom_keys','f_name','table','is_default');
        $request_list = ['custom_keys' => '方案','f_name'=>'方案名称','table' => '板块'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        if ($param['custom_keys'] == '[]') {
            returnError('方案不能为空');
        }
        $custom_data = json_decode($param['custom_keys']);
        $id = (int)$param['id'];
        $db = dbFMysql::getInstance();
        if ($id) {
            $data = $db->table('table_custom_setting')
                ->where('where id=:id and is_delete = 0',['id'=>$id])
                ->one();
            if (!$data) {
                returnError('未查询到数据');
            }
            $db->table('table_custom_setting')
                ->where('where id=:id and is_delete = 0',['id'=>$id]);
            if ($param['is_default'] == 1){
                $db->update([
                    'f_name'=>$param['f_name'],
                    'custom_keys'=>json_encode($custom_data,JSON_UNESCAPED_UNICODE),
                    'updated_time'=>date('Y-m-d H:i:s'),
                    'is_default'=>$param['is_default'],
                ]);
                //将其他的设为0
                $db->table('table_custom_setting')
                    ->where('where id<>:id and user_id=:user_id and table_type=:table_type and is_delete = 0',
                        ['id'=>$id,'user_id'=>userModel::$qwuser_id,'table_type'=>$param['table']])
                    ->update(['is_default'=>0]);
            }elseif ($param['is_default'] == 2){
                $db->update([
                    'f_name'=>$param['f_name'],
                    'custom_keys'=>json_encode($custom_data,JSON_UNESCAPED_UNICODE),
                    'updated_time'=>date('Y-m-d H:i:s')
                ]);
            }
            returnSuccess($id,'修改成功');
        } else {
            if ($param['is_default'] == 1){
                $id = $db->table('table_custom_setting')
                            ->insert([
                                'user_id'=>userModel::$qwuser_id,
                                'f_name'=>$param['f_name'],
                                'custom_keys'=>json_encode($custom_data,JSON_UNESCAPED_UNICODE),
                                'updated_time'=>date('Y-m-d H:i:s'),
                                'created_time'=>date('Y-m-d H:i:s'),
                                'table_type'=>$param['table'],
                                'is_delete'=>'0',
                                'is_default'=>1
                            ]);
                //将其他的设为0
                $db->table('table_custom_setting')
                    ->where('where id<>:id and user_id=:user_id and table_type=:table_type and is_delete = 0',
                        ['id'=>$id,'user_id'=>userModel::$qwuser_id,'table_type'=>$param['table']])
                    ->update(['is_default'=>0]);
            }elseif ($param['is_default'] == 2){
                $id = $db->table('table_custom_setting')
                        ->insert([
                            'user_id'=>userModel::$qwuser_id,
                            'f_name'=>$param['f_name'],
                            'custom_keys'=>json_encode($custom_data,JSON_UNESCAPED_UNICODE),
                            'updated_time'=>date('Y-m-d H:i:s'),
                            'created_time'=>date('Y-m-d H:i:s'),
                            'table_type'=>$param['table'],
                            'is_delete'=>'0',
                            'is_default'=>0
                        ]);
            }
            returnSuccess($id,'新增成功');
        }
    }

    //拉取用户对应板块方案列表
    public function getUserTableAuth()
    {
        $paras_list = array('table');
        $request_list = ['table' => '板块'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $db = dbFMysql::getInstance();
        $auth_list = [];
        //拿到角色权限列表
        $user_roles = $db->table('user_roles')
            ->where('where user_id = :user_id and is_delete = 0 and status = 1', ['user_id' => userModel::$qwuser_id])
            ->list();
        if (count($user_roles)) {
            $ids = array_column($user_roles, 'role_id');
            $roles = $db->table('roles')
                ->field('list_auth')
                ->whereIn('id', $ids)
                ->list();
            $list_auth_list = [];
            foreach ($roles as $v) {
                $list_auth = json_decode($v['list_auth'], true);
                foreach ($list_auth as $auth) {
                    if ($auth['name'] == $param['table']) {
                        $list_auth_list = array_merge($list_auth_list, $auth['key']);
                    }
                }
            }
            if (count($list_auth_list)) {
                $auth_list = array_unique($list_auth_list);
            }
        }
        $data_type = config::get('column_type','data_financial');
        //更具类型分组
        $column = $db->table('column')
            ->where('where table_index<>0 and is_delete = 0')
            ->whereIn('key_name',$auth_list)
            ->field('key_name as `key`,column_name as name,data_type,show_type')
            ->list();
        $new_list = [];
        foreach ($data_type as $v) {
            $item['key'] = 'type_'.$v['id'];
            $item['name'] = $v['name'];
            $item['child'] = [];
            foreach ($column as $k2=>$v1) {
                if ($v1['data_type'] == $v['id']) {
                    $item['child'][] = $v1;
                    unset($column[$k2]);
                }
            }
            $new_list[] = $item;
        }
        //基础信息
        $base_list = ['key'=>'base_info','name'=>'基础信息','child'=>[]];
        $base_auth = authListModel::$base_auth;
        foreach ($base_auth as $v) {
            if(in_array($v['key'],$auth_list)) {
                $base_list['child'][] = $v;
            }
        }
        $res_list = array_merge([$base_list],$new_list);
        returnSuccess($res_list);
    }

    //删除方案
    public function del() {
        $paras_list = array('id');
        $request_list = ['id' => '方案ID'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

//        dd($param);

        $db = dbFMysql::getInstance();

        $db->table('table_custom_setting')
            ->where('where id=:id',['id'=>$param['id']])
            ->update([
                'is_delete'=>1,
                'updated_time'=>date('Y-m-d H:i:s')
            ]);
        returnSuccess([],'删除成功');
    }
    public function getDefault() {
        $paras_list = array('table');
        $request_list = ['table' => '板块'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $db = dbFMysql::getInstance();

        // 查询用户自定义的配置
        $data = $db->table('table_custom_setting')
            ->where('where user_id=:user_id and table_type=:table_type and is_default=1 and is_delete=0',
                ['user_id' => userModel::$qwuser_id, 'table_type' => $param['table']])
            ->one();

        if (empty($data)) {
            // 处理角色权限
            $user_roles = $db->table('user_roles')
                ->where('where user_id = :user_id and is_delete = 0 and status = 1', ['user_id' => userModel::$qwuser_id])
                ->list();

            $auth_list = [];
            if (!empty($user_roles)) {
                $ids = array_column($user_roles, 'role_id');
                $roles = $db->table('roles')
                    ->field('list_auth')
                    ->whereIn('id', $ids)
                    ->list();

                foreach ($roles as $role) {
                    $list_auth = json_decode($role['list_auth'], true);
                    foreach ($list_auth as $auth) {
                        if ($auth['name'] == $param['table']) {
                            $auth_list = array_merge($auth_list, $auth['key']);
                        }
                    }
                }
                $auth_list = array_unique($auth_list);
            }

            // 获取分组列
            $data_type = config::get('column_type', 'data_financial');
            $columns = $db->table('column')
                ->where('where table_index<>0 and is_delete = 0')
                ->whereIn('key_name', $auth_list)
                ->field('key_name as `key`, column_name as name, data_type, show_type')
                ->list();

            $grouped_columns = [];
            foreach ($data_type as $type) {
                $item = [
                    'key' => 'type_' . $type['id'],
                    'name' => $type['name'],
                    'child' => []
                ];
                foreach ($columns as $index => $column) {
                    if ($column['data_type'] == $type['id']) {
                        $item['child'][] = $column;
                        unset($columns[$index]);
                    }
                }
                $grouped_columns[] = $item;
            }

            // 处理基础信息
            $base_list = ['key' => 'base_info', 'name' => '基础信息', 'child' => []];
            foreach (authListModel::$base_auth as $auth) {
                if (in_array($auth['key'], $auth_list)) {
                    $base_list['child'][] = $auth;
                }
            }

            $final_list = array_merge([$base_list], $grouped_columns);

            // 提取所有字段
            $all_keys = [];
            function extractKeys($items, &$keys) {
                foreach ($items as $item) {
                    if (!empty($item['child'])) {
                        extractKeys($item['child'], $keys);
                    }
                    if (!empty($item['key'])) {
                        $keys[] = $item['key'];
                    }
                }
            }
            extractKeys($final_list, $all_keys);

            // 排序：优先字段
            $priority_fields = ['asin', 'p_asin', 'supplier_name', 'sku', 'product_name', 'store_name', 'project1', 'project', 'yunying', 'product_developer', 'level_name', 'is_new', 'category_name'];
            $sorted_keys = array_unique(array_merge($priority_fields, $all_keys));

            // 移除数字键，只保留字段名
            $sorted_keys = array_values($sorted_keys);

            returnSuccess(json_encode($sorted_keys, JSON_UNESCAPED_UNICODE));
        } else {
            returnSuccess($data['custom_keys']);
        }
    }

}