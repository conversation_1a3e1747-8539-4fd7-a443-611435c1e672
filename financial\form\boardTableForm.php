<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/16 15:56
 */

namespace financial\form;

use core\lib\db\dbFMysql;
use financial\models\boardTableModels;
use financial\models\userModel;

class boardTableForm extends boardTableModels
{
    //asin数据获取
    public static function getAsinList($type) {
        //数据查询
        $lx_field = ['asin','countryCode'];
        $lx_field_u = ['asin','countryCode as country_code'];
        $lx_group_by = ['asin','countryCode'];
        //领星数据
        $lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by);
        $oa_list = [];
        $asin_list = array_column($lx_list,'asin');
        $oa_field = ['asin','country_code','sum(custom_val) as custom_val','custom_id'];
        $oa_field_u = ['asin','country_code','custom_id','sum(custom_val) as total'];
        $oa_group_by = ['asin','country_code','custom_id'];
        if (count($lx_list)) {
            //自定义字段数据
            $oa_list =  self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,$asin_list);
        }
        //合并oa和lx数
        $list = self::arrangeList($lx_list,$oa_list);
        //获取上个月的信息
        $last_list = [];
        if (self::$need_last_data) {
            $last_lx_list = [];
            if (count(self::$lx_last_keys)) {
                $last_lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by,[],1);
            }
            $last_oa_list = [];
            if (count(self::$oa_last_ids)) {
                $last_oa_list = self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,$asin_list,1);
            }
            $last_list = self::arrangeList($last_lx_list,$last_oa_list,1);
        }
        //将数据存入记录
        //分页，合并计算
        $data = self::getPageList($list,$type,$last_list);
        //转币种
        $data['list'] = self::getPriceByNewRoute($data['list'],self::$param['currency_code'],self::$all_keys,self::$currency_keys,self::$search_month);
        return ['data'=>$data];
    }
    //父asin数据获取
    public static function getPasinList($type) {
        //数据查询
        $lx_field = ['parentAsin','countryCode'];
        $lx_field_u = ['parentAsin as p_asin','countryCode as country_code'];
        $lx_group_by = ['parentAsin','countryCode'];
        //领星数据
        $lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by);
        $oa_list = [];
        $p_asin_list = array_column($lx_list,'p_asin');
        if (count($lx_list)) {
            $oa_field = ['p_asin','country_code','sum(custom_val) as custom_val','custom_id'];
            $oa_field_u = ['p_asin','country_code','custom_id','sum(custom_val) as total'];
            $oa_group_by = ['p_asin','country_code','custom_id'];
            //自定义字段数据
            $oa_list =  self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,$p_asin_list);
        }
        //合并oa和lx数据
        $list = self::arrangeList($lx_list,$oa_list);
        //获取上个月的信息
        $last_list = [];
        if (self::$need_last_data) {
            $last_lx_list = [];
            if (count(self::$lx_last_keys)) {
                $last_lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by,[],1);
            }

            $last_oa_list = [];
            if (count(self::$oa_last_ids)) {
                $last_oa_list = self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,$p_asin_list,1);
            }
            $last_list = self::arrangeList($last_lx_list,$last_oa_list,1);
        }
        //分页，合并计算
        $data = self::getPageList($list,$type,$last_list);
        //转币种
        $data['list'] = self::getPriceByNewRoute($data['list'],self::$param['currency_code'],self::$all_keys,self::$currency_keys,self::$search_month);
        return ['data'=>$data];
    }
    //SKU
    public static function getSkuList($type) {
        //数据查询
        $lx_field = ['localSku'];
        $lx_field_u = ['localSku as sku'];
        $lx_group_by = ['localSku'];
        //领星数据
        $lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by);
        $oa_list = [];
        $sku_list = array_column($lx_list,'sku');
        if (count($lx_list)) {
            $oa_field = ['sku','sum(custom_val) as custom_val','custom_id'];
            $oa_field_u = ['sku','custom_id','sum(custom_val) as total'];
            $oa_group_by = ['sku','custom_id'];
            //自定义字段数据
            $oa_list =  self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,$sku_list);
        }
        //合并oa和lx数据
        $list = self::arrangeList($lx_list,$oa_list);
        //获取上个月的信息
        $last_list = [];
        if (self::$need_last_data) {
            $last_lx_list = [];
            if (count(self::$lx_last_keys)) {
                $last_lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by,[],1);
            }

            $last_oa_list = [];
            if (count(self::$oa_last_ids)) {
                $last_oa_list = self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,$sku_list,1);
            }
            $last_list = self::arrangeList($last_lx_list,$last_oa_list,1);
        }
        //排序，分页，合并计算
        $data = self::getPageList($list,$type,$last_list);
        //转币种
        $data['list'] = self::getPriceByNewRoute($data['list'],self::$param['currency_code'],self::$all_keys,self::$currency_keys,self::$search_month);
        return ['data'=>$data];
    }
    //运营榜单
    public static function getYunyingList($type) {
        //数据查询
        $lx_field = ['yunying_id'];
        $lx_field_u = ['yunying_id'];
        $lx_group_by = ['yunying_id'];
        //领星数据
        $lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by);
        $oa_list = [];
        $yunying_ids = array_column($lx_list,'yunying_id');
        if (count($lx_list)) {
            $oa_field = ['yunying_id','custom_id','sum(custom_val) as custom_val'];
            $oa_field_u = ['yunying_id','custom_id','sum(custom_val) as total'];
            $oa_group_by = ['yunying_id','custom_id'];
            //自定义字段数据
            $oa_list =  self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,$yunying_ids);
        }
        //合并oa和lx数据
        $list = self::arrangeList($lx_list,$oa_list);
        //获取上个月的信息
        $last_list = [];
        if (self::$need_last_data) {
            $last_lx_list = [];
            if (count(self::$lx_last_keys)) {
                $last_lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by,[],1);
            }

            $last_oa_list = [];
            if (count(self::$oa_last_ids)) {
                $last_oa_list = self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,$yunying_ids,1);
            }
            $last_list = self::arrangeList($last_lx_list,$last_oa_list,1);
        }
        //排序，分页，合并计算
        $data = self::getPageList($list,$type,$last_list);
        //转币种
        $data['list'] = self::getPriceByNewRoute($data['list'],self::$param['currency_code'],self::$all_keys,self::$currency_keys,self::$search_month);
        return ['data'=>$data];
    }
    //店铺
    public static function getSidList($type) {
        //数据查询
        $lx_field = ['sid','countryCode'];
        $lx_field_u = ['sid','countryCode as country_code'];
        $lx_group_by = ['sid','countryCode'];
        //领星数据
        $lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by);
        $oa_list = [];
        $yunying_ids = array_column($lx_list,'sid');
        if (count($lx_list)) {
            $oa_field = ['sid','country_code','custom_id','sum(custom_val) as custom_val'];
            $oa_field_u = ['sid','country_code','custom_id','sum(custom_val) as total'];
            $oa_group_by = ['sid','country_code','custom_id'];
            //自定义字段数据
            $oa_list =  self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,$yunying_ids);
        }
        //合并oa和lx数据
        $list = self::arrangeList($lx_list,$oa_list);
        //获取上个月的信息
        $last_list = [];
        if (self::$need_last_data) {
            $last_lx_list = [];
            if (count(self::$lx_last_keys)) {
                $last_lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by,[],1);
            }

            $last_oa_list = [];
            if (count(self::$oa_last_ids)) {
                $last_oa_list = self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,$yunying_ids,1);
            }
            $last_list = self::arrangeList($last_lx_list,$last_oa_list,1);
        }
        //排序，分页，合并计算
        $data = self::getPageList($list,$type,$last_list);
        //转币种
        $data['list'] = self::getPriceByNewRoute($data['list'],self::$param['currency_code'],self::$all_keys,self::$currency_keys,self::$search_month);
        return ['data'=>$data];
    }
    //月度汇总
    public static function getMonthTotal() {
        //不需要占比的字段：FBA销量\FBM销量\FBM补（换）货量\FBM补（换）货退回量\ FBA补（换）货量\ FBA补（换）货退回量\多渠道销量\赔偿量\退款量\退款率\ 退货量（可售）\退货量（不可售）\退货率\销毁量\移除量\销量(领星)\销售额(领星)\销量1\客单价1\赔偿量\发货量1 \退款量1\单个仓储费1\采购单价1  头程单价1
        //库存周转天数1
        $no_need_rate = ['fbaSalesQuantity','fbmSalesQuantity','reshipFbmProductSalesQuantity','reshipFbmProductSaleRefundsQuantity','reshipFbaProductSalesQuantity','reshipFbaProductSalesQuantity','mcFbaFulfillmentFeesQuantity','fbaInventoryCreditQuantity','refundsQuantity','oa_key_49','refundsRate','oa_key_64','fbaReturnsSaleableQuantity','fbaReturnsUnsaleableQuantity','fbaReturnsQuantityRate','disposalQuantity','removalQuantity','totalSalesQuantity','totalSalesAmount','oa_key_1','oa_key_5','fbaInventoryCreditQuantity','oa_key_79','oa_key_49','oa_key_68','oa_key_50','oa_key_51'];

        //国家筛选，排序，分页，合并计算
        if (self::$param['hidden_detail']) {
            //数据查询
            $lx_field = ['reportDateMonth'];
            $lx_field_u = ['reportDateMonth as m_date'];
            $lx_group_by = ['reportDateMonth'];
            //领星数据
            $lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by);
            $oa_list = [];
            if (count($lx_list)) {
                $oa_field = ['m_date','sum(custom_val) as custom_val','custom_id'];
                $oa_field_u = ['m_date','custom_id','sum(custom_val) as total'];
                $oa_group_by = ['m_date','custom_id'];
                //自定义字段数据
                $oa_list =  self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,false);
            }
            //月汇总
            $data = self::getMonthAmount($lx_list,$oa_list,$no_need_rate);
        } else {
            //数据查询
            $lx_field = ['asin','countryCode','reportDateMonth'];
            $lx_field_u = ['asin','countryCode as country_code','reportDateMonth as m_date'];
            $lx_group_by = ['asin','countryCode','reportDateMonth'];
            //领星数据
            $lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by);
            $oa_list = [];
            $asin_list = array_column($lx_list,'asin');
            if (count($lx_list)) {
                $oa_field = ['asin','country_code','m_date','sum(custom_val) as custom_val','custom_id'];
                $oa_field_u = ['asin','country_code','m_date','custom_id','sum(custom_val) as total'];
                $oa_group_by = ['asin','country_code','m_date','custom_id'];
                //自定义字段数据
                $oa_list =  self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,$asin_list);
                $new_ia_list = [];
                foreach ($oa_list as $v) {
                    $key = $v['asin'].'_'.$v['country_code'].'_'.$v['m_date'];
                    $new_ia_list[$key] = ['key'=>'oa_key_'.$v['custom_id'],'total'=>$v['total']];
                }
            }
            $list = self::arrangeList($lx_list,$oa_list);
            //月+asin+国家汇总
            $data = self::getMonthAsinAmount($list,$no_need_rate);
        }
        //转币种
        return $data;
    }
    //预警
    public static function waringGoodsTotal($type) {
        $waring_array = self::getWaringGoodsSku();
        $sku_array = $waring_array['sku'];
        $country_code_array = $waring_array['country_code'];
        $waring_list = $waring_array['list'];
        $list = [];
        if (count($sku_array)) {
            //数据查询
            $lx_field = ['parentAsin','asin','countryCode','localSku','yunying_id','project_id','sid'];
            $lx_field_u = ['asin','countryCode as country_code','localSku as sku','parentAsin as p_asin','yunying_id','project_id','sid'];
            $lx_group_by = ['parentAsin','asin','countryCode','localSku','yunying_id','project_id','sid'];
            //领星数据
            $lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by,['sku'=>$sku_array,'country_code'=>$country_code_array]);
            //整理数据
            $list = self::arrangeWaringList($lx_list);
        }
        //国家筛选，排序，分页，合并计算;
        $data = self::getWaringPageList($list,$waring_list,$type);
        return ['data'=>$data];
    }
    //供应商利润贡献
    public static function supplierGoodsTotal($type) {
        $lx_field = ['localSku','supplier_id','reportDateMonth'];
        $lx_field_u = ['supplier_id','localSku as sku','reportDateMonth as m_date'];
        $lx_group_by = ['localSku','supplier_id','reportDateMonth'];
        //领星数据
        $lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by);
        $oa_list = [];
        $sku_list = array_unique(array_column($lx_list,'sku'));
        if (count($lx_list)) {
            $oa_field = ['sku','supplier_id','sum(custom_val) as custom_val','m_date'];
            $oa_field_u = ['sku','supplier_id','sum(custom_val) as total','m_date'];
            $oa_group_by = ['sku','supplier_id','m_date'];
            //自定义字段数据
            $oa_list =  self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,$sku_list);
        }
        //合并数据
        $list = self::arrangeSupplierList($lx_list,$oa_list);
        //国家筛选，排序，分页，合并计算
        if (self::$param['hidden_detail']) {
            //汇总
            $data = self::getSupplierPageList($list,$type);
        } else {
            //详情
            $data = self::getSupplierGoodsPageList($list,$type);
        }
        return ['data'=>$data];
    }

    //产品利润贡献
    public static function goodsTotal($type) {
        $lx_field = ['localSku','reportDateMonth'];
        $lx_field_u = ['localSku as sku','reportDateMonth as m_date'];
        $lx_group_by = ['localSku','reportDateMonth'];
        //领星数据
        $lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by);
        $oa_list = [];
        $sku_list = array_column($lx_list,'sku');
        if (count($lx_list)) {
            $oa_field = ['sku','sum(custom_val) as custom_val','m_date'];
            $oa_field_u = ['sku','sum(custom_val) as total','m_date'];
            $oa_group_by = ['sku','m_date'];
            //自定义字段数据
            $oa_list =  self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,$sku_list);
        }
        //合并数据
        $list = self::arrangeGoodsList($lx_list,$oa_list);
        if ($type == 2){
            $data = self::getGoodsPageLists($list);
        }else{
            $data = self::getGoodsPageList($list);
        }
        return ['data'=>$data];
    }
    public static function goodsGrossTotal($type) {
        $db = dbFMysql::getInstance();
        $param = self::$param;
        $db->table('goods','a')
            ->leftJoin('goods_category','b','b.cid = a.cid');
        if (!empty($param['category_ids']) && $param['category_ids'] != '[]') {
            $category_ids = json_decode($param['category_ids']);
            $db->whereIn('a.cid',$category_ids);
        }
        $all_sku = array_column($db->field('sku')->list(),'sku');
        if (!empty($param['search_type']) && !empty($param['search_value'])) {
            if ($param['search_type'] == 'sku') {
                $db->andWhere('a.sku like :sku',['sku'=>"%{$param['search_value']}%"]);
            } elseif ($param['search_type'] == 'goods_name') {
                $db->andWhere('a.product_name like :product_name',['product_name'=>"%{$param['search_value']}%"]);
            }
        }
        if ($type == 2){
            $goods_list['list'] =  $db->field('a.id,a.category_name,a.cid,a.sku,a.product_name')
                ->order('id asc')
                ->list();
        }else{
            $goods_list =  $db->field('a.id,a.category_name,a.cid,a.sku,a.product_name')
                ->order('id asc')
                ->pages(self::$param['page'],self::$param['page_size']);
        }
        $s_list = $goods_list['list'];
        $category_list = goodsInformationForm::getGoodsCate(array_column($s_list,'cid'));
        self::$sql_sku_list = array_column($s_list,'sku');
        $not_match = 0;
        if (in_array('无',self::$sql_sku_list)) {
            $not_match = 1;
            self::$sql_sku_list = array_filter(self::$sql_sku_list, function($value) {
                return $value !== '无';
            });
        }
        self::$sql_use_sku = 1;
        //领星数据查询
        $lx_field = ['localSku','reportDateMonth'];
        $lx_field_u = ['localSku as sku','reportDateMonth as m_date'];
        $lx_group_by = ['localSku','reportDateMonth'];
        $lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by);
        $oa_list = [];
        $sku_list = array_column($lx_list,'sku');
        if (count($lx_list)) {
            $oa_field = ['sku','sum(custom_val) as custom_val','m_date'];
            $oa_field_u = ['sku','sum(custom_val) as total','m_date'];
            $oa_group_by = ['sku','m_date'];
            //自定义字段数据
            $oa_list =  self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,$sku_list);
        }
        //获取匹配不到的数据
        if ($not_match) {
            self::$sql_use_sku = 2;
            self::$sql_sku_list = $all_sku;
            $lx_field = ['reportDateMonth'];
            $lx_field_u = ['reportDateMonth as m_date'];
            $lx_group_by = ['reportDateMonth'];
            $lx_list_wu = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by);
            $lx_list_wu = array_map(function ($row){
                $row['sku'] = '无';
                return $row;
            },$lx_list_wu);
            $oa_list_wu = [];
            if (count($lx_list_wu)) {
                $oa_field = ['sum(custom_val) as custom_val','m_date'];
                $oa_field_u = ['sum(custom_val) as total','m_date'];
                $oa_group_by = ['m_date'];
                //自定义字段数据
                $oa_list_wu =  self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,false);
                $oa_list_wu = array_map(function ($row){
                    $row['sku'] = '无';
                    return $row;
                },$oa_list_wu);
            }
            $lx_list = array_merge($lx_list,$lx_list_wu);
            $oa_list = array_merge($oa_list,$oa_list_wu);
        }
        //合并数据
        $list = self::arrangeGoodsList($lx_list,$oa_list);
        //整理数据
        $data = self::goodsGrossTotalPageList($s_list,$list,$category_list);
        $result = [
            'count'=>$goods_list['total'],
            'page_count'=>count($data),
            'page'=>self::$param['page'],
            'page_size'=>self::$param['page_size'],
            'list'=>$data,
        ];
        return ['data'=>$result];

    }

    //产品利润贡献底部统计
    public static function goodsTotalCount() {
        $db = dbFMysql::getInstance();
        $param = self::$param;
        $db->table('goods','a');
        if (!empty($param['category_ids']) && $param['category_ids'] != '[]') {
            $category_ids = json_decode($param['category_ids']);
            $db->whereIn('a.cid',$category_ids);
        }
        $all_sku = array_column($db->field('sku')->list(),'sku');
        if (!empty($param['search_type']) && !empty($param['search_value'])) {
            if ($param['search_type'] == 'sku') {
                $db->andWhere('a.sku like :sku',['sku'=>"%{$param['search_value']}%"]);
            } elseif ($param['search_type'] == 'goods_name') {
                $db->andWhere('a.product_name like :product_name',['product_name'=>"%{$param['search_value']}%"]);
            }
        }
        $goods_list =  $db->field('a.id,a.category_name,a.cid,a.sku,a.product_name')
            ->list();
        self::$sql_sku_list = array_column($goods_list,'sku');
        self::$sql_use_sku = 1;
        $not_match = 0;
        if (in_array('无',self::$sql_sku_list)) {
            $not_match = 1;
            unset(self::$sql_sku_list['无']);
        }
        //数据查询
        $lx_field = ['reportDateMonth'];
        $lx_field_u = ['reportDateMonth as m_date'];
        $lx_group_by = ['reportDateMonth'];
        //领星数据
        $lx_list = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by);
        $oa_list = [];
        if (count($lx_list)) {
            $oa_field = ['sum(custom_val) as custom_val','m_date'];
            $oa_field_u = ['sum(custom_val) as total','m_date'];
            $oa_group_by = ['m_date'];
            //自定义字段数据
            $oa_list =  self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,false);
        }
        //合并数据
        $list = [];
        foreach ($lx_list as $v) {
            $item = $v;
            $item['year'] = date('Y',strtotime($v['m_date']));
            $item['oa_key_4'] = 0;
            foreach ($oa_list as $k1=>$v1) {
                if ($v1['m_date'] == $v['m_date']) {
                    $item['oa_key_4'] = $v1['total'];
                    unset($oa_list[$k1]);
                }
            }
            $list[] = $item;
        }
        //获取匹配不到的数据
        if ($not_match) {
            self::$sql_use_sku = 2;
            self::$sql_sku_list = $all_sku;
            $lx_field = ['reportDateMonth'];
            $lx_field_u = ['reportDateMonth as m_date'];
            $lx_group_by = ['reportDateMonth'];
            $lx_list_wu = self::getSqlWhereList($lx_field,$lx_field_u,$lx_group_by);
            $oa_list_wu = [];
            if (count($lx_list_wu)) {
                $oa_field = ['sum(custom_val) as custom_val','m_date'];
                $oa_field_u = ['sum(custom_val) as total','m_date'];
                $oa_group_by = ['m_date'];
                //自定义字段数据
                $oa_list_wu =  self::getSqlWhereForOa($oa_field,$oa_field_u,$oa_group_by,false);
            }
            $list_wu = [];
            foreach ($lx_list_wu as $v) {
                $item = $v;
                $item['year'] = date('Y',strtotime($v['m_date']));
                $item['oa_key_4'] = 0;
                foreach ($oa_list_wu as $k1=>$v1) {
                    if ($v1['m_date'] == $v['m_date']) {
                        $item['oa_key_4'] = $v1['total'];
                        unset($oa_list[$k1]);
                    }
                }
                $list_wu[] = $item;
            }
            $list = array_merge($list,$list_wu);
        }
        $data = self::getGoodsCount($list);
        return ['data'=>$data];
    }




}