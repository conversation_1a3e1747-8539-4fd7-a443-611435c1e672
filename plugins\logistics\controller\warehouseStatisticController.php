<?php
/**
 * @author: warehouseStatistic
 * @Time: 2024/8/10 10:00
 */

namespace plugins\logistics\controller;

use plugins\logistics\form\warehouseStatisticForm;
use plugins\shop\controller\baseController;

class warehouseStatisticController extends baseController
{
    // 执行统计
    public function executeStatistic()
    {
        $year_month = $_POST['year_month'] ?? date('Y-m');
        
        $form = new warehouseStatisticForm($year_month);
        $result = $form->executeStatistic($year_month);
        
        if ($result['code'] === 0) {
            SetReturn(0, $result['msg']);
        } else {
            SetReturn(-1, $result['msg']);
        }
    }
    
    // 收集仓库数据
    public function collectWarehouseData()
    {
        $year_month = $_POST['year_month'] ?? date('Y-m');
        
        try {
            $form = new warehouseStatisticForm($year_month);
            $result = $form->executeStatistic($year_month);
            
            if ($result['code'] === 0) {
                SetReturn(0, '仓库数据收集成功', [
                    'year_month' => $year_month,
                    'collect_time' => date('Y-m-d H:i:s'),
                    'message' => $result['msg']
                ]);
            } else {
                SetReturn(-1, $result['msg']);
            }
        } catch (\Exception $e) {
            SetReturn(-1, '数据收集失败: ' . $e->getMessage());
        }
    }
    
    // 获取统计数据列表
    public function getStatisticList()
    {
        $param = [
            'year_month' => $_GET['year_month'] ?? date('Y-m'),
            'page' => $_GET['page'] ?? 1,
            'page_size' => $_GET['page_size'] ?? 20,
            'warehouse_type' => $_GET['warehouse_type'] ?? '',
            'product_name' => $_GET['product_name'] ?? '',
            'sku' => $_GET['sku'] ?? '',
            'warehouse_name' => $_GET['warehouse_name'] ?? '',
            'sort_field' => $_GET['sort_field'] ?? 'total_day_end_cost',
            'sort_order' => $_GET['sort_order'] ?? 'desc',
        ];

        $form = new warehouseStatisticForm($param['year_month']);
        $result = $form->getStatisticList($param);

        returnSuccess($result);
    }
    
}
