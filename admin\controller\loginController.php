<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/7 17:27
 */
namespace admin\controller;

use admin\form\loginForm;
use admin\models\sysUserRolesModel;
use admin\models\userModel;
use core\lib\config;
use core\lib\db\dbMysql;

class loginController
{
    public function login(){
        // 检查参数
        $param = $_POST;
        $paraList = array('account', 'password');
        $missField = checkParaments($param, $paraList);
        if ($missField != null) {
            SetReturn(-1,'缺少必传参数');
        }
        $user_name = trim($param["account"]);  // 用户名
        $password = trim($param["password"]);  // 密码
        if (!$user_name || !$password) {
            SetReturn(-1, "用户名或密码不能为空");
        }
        $db = dbMysql::getInstance();
        $sql = 'select id,wname,wid,avatar from oa_qwuser where wstatus = 1 and is_delete = 0 and (wname=:account or wphone=:account) limit 1';
        $w_user = $db->query($sql, ['account'=>$user_name]);
        if ($w_user) {
            //密码解析：
            $user = $db->query("select id,pwd,uniqueid,super_admin,status from oa_user where qw_id = :qw_id and is_delete = 0 limit 1",['qw_id'=>$w_user['id']]);
            if (!$user) {
                SetReturn(-1, "非管理员不可登录");
            }
            if ($user['status'] == 1) {
                SetReturn(-1, "账号未激活");
            }
            if ($user['status'] == 3) {
                SetReturn(-1, "账号被禁用");
            }
            $pwd = $this->getPwdMd5($password, $user['uniqueid']);
            if ($pwd != $user['pwd']) {
                SetReturn(-1, "用户名或密码不正确");
            } else {
                $result = $this->getTokenRedis($w_user, $user);
                SetReturn(0,'',$result);
            }
        } else {
            SetReturn(-1, "用户名或密码不正确");
        }

    }
    public function codeLogin(){
        // 检查参数
        $param = $_POST;
        $paraList = array('code');
        $missField = checkParaments($param, $paraList);
        if ($missField != null) {
            SetReturn(-1,'缺少必传参数');
        }
        $db = dbMysql::getInstance();
        $code_data = $db->table('login_code')
            ->where('code = :code',['code'=>$param['code']])
            ->one();
        if (!$code_data) {
            returnError('CODE已失效');
        }
        $db->table('login_code')
            ->where('code = :code',['code'=>$param['code']])
            ->update([
                'status'=>0,
            ]);
        $created_time = $code_data['created_time'];
        if ((time() - $created_time) > 60 || $code_data['status'] == 0) {
            returnError('CODE已过期');
        }
        $w_user = $db->table('qwuser')
            ->where('id=:id',['id'=>$code_data['user_id']])
            ->field('id,wname,wid,avatar')
            ->one();
        if ($w_user) {
            $user = $db->table('user')
                ->where('qw_userid=:qw_userid and is_delete = 0',['qw_userid'=>$w_user['wid']])
                ->field('id,pwd,uniqueid,super_admin,status')
                ->one();
            if (!$user) {
                SetReturn(-1, "非管理员不可登录");
            }
            if ($user['status'] == 1) {
                SetReturn(-1, "账号未激活");
            }
            if ($user['status'] == 3) {
                SetReturn(-1, "账号被禁用");
            }
            $result = $this->getTokenRedis($w_user, $user, $code_data['token1']);
            SetReturn(0,'',$result);
        } else {
            SetReturn(-1, "用户名或密码不正确");
        }

    }
    /**
     * @param $user  企微用户信息
     * @param $user_ 后台用户信息
     * @return array
     * @throws \RedisException
     * @throws \core\lib\ExceptionError
     */
    public function getTokenRedis($user, $user_, $code_token='')
    {
        //不要轻易改动字段，系统很多功能要用
        $redis = (new \core\lib\predisV())::$client;
        $prefix = config::get('token_key_prefix', 'app');
        if (!empty($code_token) && $redis->exists($prefix.$code_token)) {
            $token = $code_token;
        } else {
            $token = $this->getPwdMd5($user['id'].$user['wname'], getuniqId());
        }
        $key = $prefix.$token;
        $data = [
            'id'=>$user['id'],
            'avatar'=>$user['avatar'],
            'wid'=>$user['wid'],
            'wname'=>$user['wname'],
            'is_super'=>$user_['super_admin'],
            'auth'=>'',
            'role_name'=>'',
            'role_type'=>[]
        ];
        $auth_ = sysUserRolesModel::getAuthByQuserId($user['id']);
        if ($auth_) {
            $data['auth'] = $auth_['auth'];
            $data['role_name'] = $auth_['role_name'];
            $data['role_type'] = $auth_['role_type'];
        }
        $data['role_type'] = json_encode($data['role_type']);
        $redis->hmset($key, $data);
        $redis->expire($key, 4*60*60);
        return [
            'token'=> $token,
            'data'=>$data
        ];
    }
    public function getPwdMd5($password, $uni_code){
        return substr(md5($password.$uni_code), 0,20);
    }
    public function logOut(){
        $prefix = config::get('token_key_prefix', 'app');
        $key = $prefix.USER_TOKEN;
        $redis = (new \core\lib\predisV())::$client;
        $user_info = $redis->hmget($key,['id'])??'';
        if (!empty($user_info)) {
            $user_id = $user_info['id']??0;
            if ($user_id) {
                loginForm::cleanUpToken($user_id);
            }
            $redis->del($key);
        }
        returnSuccess([],'成功登出');
    }
}