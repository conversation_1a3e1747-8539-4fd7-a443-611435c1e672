<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/10 17:51
 */

namespace financial\form;

use core\lib\db\dbFMysql;
use financial\models\boardModel;
use financial\models\userModel;
use phpseclib3\Math\BigInteger\Engines\PHP;

class boardRateForm extends boardModel //跟合并计算相关的字段,和比例字段
{
    public static int $custom_id;
    public static array $rules;
    public static array $all_year;//本时间段要查询的年
    public static int $show_type;//数据显示格式，1金额，2百分数, 3值
    public function __construct($custom_id)
    {
        parent::__construct();
        self::$custom_id = $custom_id;
        //找规则
        $db = dbFMysql::getInstance();
        $custom_column = $db->table('custom_column')
            ->where('where id=:id',['id'=>$custom_id])
            ->one();
        if (!$custom_column) {
            returnError('未找到自定义字段');
        }
        self::$show_type = $custom_column['show_type'];
        //规则整理
        $rules_ = self::getResRules($custom_column);
        //根据规则分包分分出来，便于后后边儿查询
        self::$rules = $rules_['rules'];
        $last_relation_column = $rules_['last_relation_column'];
        $relation_column = $rules_['relation_column'];
        coustomColumnJobForm::$last_relation_column = $last_relation_column;
        coustomColumnJobForm::$relation_column = $relation_column;
        coustomColumnJobForm::getKeys();
        //规则跟毛利润相关，且存在合并字段，规则信息
        if ((in_array(4,coustomColumnJobForm::$all_custom_ids) || self::$custom_id == 4) && count(self::$aggregation_keys) || self::$custom_id == 4) {
            if (count(self::$aggregation_keys)) {
                self::$need_add_aggregation_val = 1;
                coustomColumnJobForm::$all_keys[] = 'aggregation_val';
            }
        }
        //获取数据
        $years_list = self::$years;
        if (count($last_relation_column)) {
            //有去年的字段就多查一个月
            $mix_month = self::$search_month[0];
            $last_month = date('Y-m',strtotime($mix_month.'-01 -1 month'));
            $last_m_year = date('Y',strtotime($last_month));
            $years_list[$last_m_year][] = $last_month;
        }
        self::$all_year = $years_list;
    }

    //获取月份数据
    public function totalCustom() {
        //获取所有数据
        $list = self::getListData(self::$all_year);
        //根据规则计算每个月对应的数据
        $res_data = self::getMdataRateList($list['lx_data'],$list['oa_data']);
        if (self::$show_type == 1) {
            //按月汇率处理数据
            $res_data = self::getPriceByRoute1($res_data,self::$search_month);
        }
        return $res_data;
    }
    //获取环比和同比
    public static function getQoqAndYoy() {
        //查询上个时间同步时间段数据
        $this_data = self::getTimeRegionData(self::$years);
        //获取上一个时间段数据
        $qoq_data = self::getTimeRegionData(self::$qoq_years);

        //获取去年的数据
        $yoy_data = self::getTimeRegionData(self::$yoy_years);

        if (count(coustomColumnJobForm::$last_relation_column)) {
            $yoy_last_data = self::getTimeRegionData(self::$yoy_qoq_years);
            $qoq_last_data = self::getTimeRegionData(self::$qoq_last_years);
        } else {
            $qoq_last_data = [];
            $yoy_last_data = [];
        }
        //按规则计算每个时间段数据
        $this_val = self::getRangeData($this_data,$qoq_data);
        $qoq_val = self::getRangeData($qoq_data,$qoq_last_data);
        $yoy_val = self::getRangeData($yoy_data,$yoy_last_data);
        $currency_code = self::$param['currency_code']??'';
        if (!empty($currency_code) && $currency_code!='CNY') {
            if (self::$show_type == 1) {
                $db = dbFMysql::getInstance();
                $routing_ = $db->table('routing')
                    ->where('where code=:code',['code'=>$currency_code])
                    ->whereIn('date',self::$search_month)
                    ->field('code,date,code,my_rate')
                    ->order('id desc')
                    ->one();
                if ($routing_) {
                    $this_val = roundToString($this_val/$routing_['my_rate']);
                    $qoq_val = roundToString($qoq_val/$routing_['my_rate']);
                    $yoy_val = roundToString($yoy_val/$routing_['my_rate']);
                }
            }
        }
        $qoq_rate = $qoq_val==0?'-':roundToString(($this_val*100 - $qoq_val*100)/$qoq_val);
        $yoy_rate = $yoy_val==0?'-':roundToString(($this_val*100 - $yoy_val*100)/$yoy_val);
        return [
            'total'=>roundToString($this_val),
            'qoq'=>[
                'mouth'=>self::$qoq_month[0].'~'.end(self::$qoq_month),
                'total'=>roundToString($qoq_val),
                'rate'=>$qoq_rate
            ],
            'yoy'=>[
                'mouth'=>self::$yoy_month[0].'~'.end(self::$yoy_month),
                'total'=>roundToString($yoy_val),
                'rate'=>$yoy_rate
            ],
        ];
    }

    //国家贡献
    public function countryContribution() {
        //固定显示的国家 ：美国、加拿大、德国、法国、英国、日本
        $must_show_country = ['US'=>'美国','CA'=>'加拿大','DE'=>'德国','FR'=>'法国','UK'=>'英国','JP'=>'日本'];
        //获取所有数据
        $list = self::getListDataC(self::$all_year);
        //先按国家和月份整理好数据,（上面的数据已经是国家+月份为维度整理好的）
        $new_list = [];
        foreach ($list['lx_data'] as $v) {
            $key_ = $v['country_code'];
            $new_list[$key_]['lx_data'][$v['m_date']] = $v;
            $new_list[$key_]['oa_data'] = [];
        }

        foreach ($list['oa_data'] as $v) {
            $key_ = $v['country_code'];
            if (!isset($new_list[$key_]['lx_data'])) {
                $new_list[$key_]['lx_data'] = [];
            }
            $new_list[$key_]['oa_data'][$v['m_date']] = $v;
        }
        $db = dbFMysql::getInstance();
        $country_list = $db->table('market')
            ->field('id,country,code')
            ->list();
        $country_ = [];
        foreach ($country_list as $contry) {
            $country_[$contry['code']] = $contry;
        }
        //国家汇总数据
        $country_data = [];
        //所有数据
        $all_data = [];
        foreach ($new_list as $k=>$v) {
            //根据规则计算每个月对应的数据,以国家为单位
            $res_data = self::getMdataRateList($v['lx_data'],$v['oa_data']);
            $res_data = self::getPriceByRoute1($res_data,self::$search_month);
            $country_data[] = [
                'country_code'=>$k,
                'country'=>$country_[$k]['country'],
                'total'=>array_sum(array_column($res_data,'total')),
                'rate'=>'0.00',
                'list'=>$res_data
            ];
            $all_data = array_merge($all_data,$res_data);
        }
        //整理全部利润数据（月份）
        $new_all_data = [];
        foreach ($all_data as $v) {
            if (!isset($new_all_data[$v['month']])) {
                $new_all_data[$v['month']] = $v;
            } else {
                $new_all_data[$v['month']]['total'] += $v['total'];
            }
        }
        $new_all_data = array_values($new_all_data);
        //整理国家利润占比
        $total_count = array_sum(array_column($new_all_data,'total'));
        $new_list = [];
        $all_rate = 0;
        $other_list = [];
        foreach ($country_data as $v) {
            if (isset($must_show_country[$v['country_code']])) {
                $rate = $v['total']*100/$total_count;
                $v['rate'] = $rate;
                $new_list[] = $v;
            } else {
                if ($v['total'] > 0) {
                    $rate = $v['total']*100/$total_count;
                    $v['rate'] = $rate;
                }
                if ($v['rate'] <= 5) {
                    $other_list = array_merge($other_list,$v['list']);
                } else {
                    $all_rate += $v['rate'];
                    $new_list[] = $v;
                }
            }
        }
        if (count($other_list)) {
            $gross_profit = [];
            foreach ($other_list as $v) {
                if (!isset($gross_profit[$v['month']])) {
                    $gross_profit[$v['month']] = $v;
                } else {
                    $gross_profit[$v['month']]['total'] += $v['total'];
                }
            }
            $new_list[] = [
                'country_code'=>'',
                'country'=>'其他',
                'total'=>array_sum(array_column($gross_profit,'total')),
                'rate'=>(10000-$all_rate*100)/100,
                'list'=>array_values($gross_profit)
            ];
        }
        //整理数据
        foreach ($new_list as $k=>$v) {
            $v['rate'] = roundToString($v['rate']);
            $v['total'] = roundToString($v['total']);
            foreach ($v['list'] as &$v1) {
                $v1['total'] = roundToString($v1['total']);
            }
            $new_list[$k] = $v;
        }
        //月数据中没有固定要显示的国家时
        if (!empty($param['country_code'])) {
            $country_codes = array_column($new_list,'country_code');
            $not_exist_code =  array_diff(array_keys($must_show_country),$country_codes);

            if (count($not_exist_code)) {
                foreach ($not_exist_code as $v) {
                    $item = [
                        'country'=>$must_show_country[$v],
                        'country_code'=>$v,
                        'list'=>[],
                        'rate'=>'0',
                        'total'=>'0'
                    ];
                    $new_list[] = $item;
                }
            }
        }
        foreach ($new_all_data as $k=>$v) {
            $new_all_data[$k]['total'] = roundToString($v['total']);
        }
        return ['country_list'=>$new_list,'all_data'=>$new_all_data];
    }

    //top10
    public function topTen() {
        //查询时间段数据
        $this_data = self::getTimeRegionDataByProject(self::$years);
        //获取上一个时间段数据
        if (count(coustomColumnJobForm::$last_relation_column)) {
            $last_data = self::getTimeRegionDataByProject(self::$qoq_years);
        } else {
            $last_data = [];
        }
        $list = [];
        foreach ($this_data as $k=>$v) {
            $list[$k]['total'] = roundToString(self::getRangeData($v,$last_data[$k]??[]));
        }
        $list = self::getPriceByRoute1($list);
        return $list;
    }

    /**单月数据处理**/
    //查询需要月份的数据并整理为人民币(以币种为维度)
    private static function getListData($years_list) {
        //获取所有数据
        $res_list = [];
        $oa_list = [];
        $db = dbFMysql::getInstance();
        $month_array = [];
        foreach ($years_list as $year=>$m_date_list) {
            //获取本年份的值
            //领星数据
            if (count(coustomColumnJobForm::$all_keys)) {
                $fields = 'reportDateMonth as m_date';
                $group_by = ['reportDateMonth'];
                $list = boardModel::getSqlWhereList($db,$year,$m_date_list,$group_by,$fields,coustomColumnJobForm::$all_keys);
            } else {
                $list = [];
            }
            if (count(coustomColumnJobForm::$all_custom_ids)) {
                //自定义数据
                $dbo = boardModel::getSqlWhereForOa($db,$year,$m_date_list);
                $list_ = $dbo->whereIn('custom_id',coustomColumnJobForm::$all_custom_ids)
                    ->field('CONCAT(\'oa_key_\',custom_id) as val_key,m_date,custom_id,sum(custom_val) as total')
                    ->groupBy(['m_date,custom_id'])
                    ->list();
            } else {
                $list_ = [];
            }
            $month_array = array_merge($month_array,$m_date_list);
            $res_list = array_merge($res_list,$list);
            $oa_list = array_merge($oa_list,$list_);
        }
        //整理月数据，将每个月的数据统计为人民币
        $report_data = self::mergeMdataLx($res_list,$month_array,self::$param['currency_code'],coustomColumnJobForm::$all_keys,coustomColumnJobForm::$currency_keys);
        $oa_data = self::mergeMdataOa($oa_list,$month_array,coustomColumnJobForm::$all_custom_ids);
        //如果需要加上合并计算字段，整理oa数据给加上
        if (self::$need_add_aggregation_val && in_array(4,coustomColumnJobForm::$all_custom_ids)) {
            foreach ($oa_data as $key=>&$v) {
                $v['oa_key_4'] += $report_data[$key]['aggregation_val'];
            }
        }
        return ['lx_data'=>$report_data,'oa_data'=>$oa_data];
    }
    //查询需要月份的数据并整理为人民币(以币种为维度，国家)
    private static function getListDataC($years_list) {
        //获取所有数据
        $res_list = [];
        $oa_list = [];
        $db = dbFMysql::getInstance();
        $month_array = [];
        foreach ($years_list as $year=>$m_date_list) {
            //获取本年份的值
            //领星数据
            if (count(coustomColumnJobForm::$all_keys)) {
                $fields = 'reportDateMonth as m_date,countryCode as country_code';
                $group_by = ['reportDateMonth,countryCode'];
                $list = boardModel::getSqlWhereList($db,$year,$m_date_list,$group_by,$fields,coustomColumnJobForm::$all_keys);
            } else {
                $list = [];
            }
            if (count(coustomColumnJobForm::$all_custom_ids)) {
                //自定义数据
                $dbo = boardModel::getSqlWhereForOa($db,$year,$m_date_list);
                $list_ = $dbo->whereIn('custom_id',coustomColumnJobForm::$all_custom_ids)
                    ->field('CONCAT(\'oa_key_\',custom_id) as val_key,m_date,custom_id,country_code,sum(custom_val) as total')
                    ->groupBy(['m_date,country_code,custom_id'])
                    ->list();
            } else {
                $list_ = [];
            }
            $month_array = array_merge($month_array,$m_date_list);
            $res_list = array_merge($res_list,$list);
            $oa_list = array_merge($oa_list,$list_);
        }
        //整理月数据，将每个月的数据统计为人民币
        $report_data = self::mergeMdataLx($res_list,$month_array,self::$param['currency_code'],coustomColumnJobForm::$all_keys,coustomColumnJobForm::$currency_keys,-1);
        $oa_data = self::mergeMdataOa($oa_list,$month_array,coustomColumnJobForm::$all_custom_ids,-1);
        return ['lx_data'=>$report_data,'oa_data'=>$oa_data];
    }
    /**
     * @param $lx_list
     * @param $oa_list
     * @param int $type 0按月整
     * @return array 根据规则计算每个月对应的数据(前面的数据都是按月整理好了的)
     */
    private function getMdataRateList($lx_list,$oa_list,int $type = 0) {
        $new_list = [];
        switch ($type) {
            case 0:
                foreach (self::$search_month as $month) {
                    $item_ = [
                        'month'=>$month,
                        'total'=>0
                    ];
                    //获取数据值
                    $rule_data = self::$rules[0]['rules']??[];
                    foreach ($rule_data as $rk1=>$rule_) {
                        if ($rule_['group_type'] == 1) {
                            $rule_['val'] = self::getValRow($month,$rule_,$lx_list,$oa_list);
                        } else {
                            foreach ($rule_['list'] as $rk2=>$rule_l) {
                                $rule_['list'][$rk2]['val'] = self::getValRow($month,$rule_l,$lx_list,$oa_list);
                            }
                        }
                        $rule_data[$rk1] = $rule_;
                    }
                    //跟毛利润相关的字段要加上合并字段
                    if (self::$custom_id == 4 && self::$need_add_aggregation_val) {
                        //毛利润字段，且有合并字段
                        $rule_data[] = [
                            'group_type'=>1,
                            'symbol'=>1,
                            'val'=>$lx_list[$month]["aggregation_val"]??0,
                            'is_absolute'=>0,
                        ];
                    }
                    if (self::$show_type == 2){
                        $item_['total'] = roundToString(coustomColumnJobForm::getValue($rule_data)*100);
                    } else {
                        $item_['total'] = roundToString(coustomColumnJobForm::getValue($rule_data));
                    }
                    $new_list[] = $item_;
                }
                break;
        }
        return $new_list;
    }
    //计算某列的值 (每个小运算规则中的值 月分为维度)
    public static function getValRow($month,$rule_,$lx_list,$oa_list) {
        $row_val = 0;
        if ($rule_['type'] == 1) {
            if (in_array($rule_['coulmn_key'],coustomColumnJobForm::$all_keys)) {
                if (count($lx_list)) {
                    foreach ($lx_list as $gvr_v) {
                        if ($gvr_v['m_date'] == $month) {
                            $row_val = $gvr_v[$rule_['coulmn_key']];
                        }
                    }
                }
            } else {
                if (count($oa_list)) {
                    foreach ($oa_list as $gvr_v) {
                        if ($gvr_v['m_date'] == $month) {
                            $row_val = $gvr_v[$rule_['coulmn_key']];
                        }
                    }
                }
            }
        } elseif ($rule_['type'] == 2){
            $last_month = date('Y-m',strtotime($month.'-01 -1 month'));
            if (in_array($rule_['coulmn_key'],coustomColumnJobForm::$all_keys)) {
                if (count($lx_list)) {
                    foreach ($lx_list as $gvr_v) {
                        if ($gvr_v['m_date'] == $last_month) {
                            $row_val = $gvr_v[$rule_['coulmn_key']];
                        }
                    }
                }
            } else {
                if (count($oa_list)) {
                    foreach ($oa_list as $gvr_v) {
                        if ($gvr_v['m_date'] == $last_month) {
                            $row_val = $gvr_v[$rule_['coulmn_key']];
                        }
                    }
                }
            }
        } else if ($rule_['type'] == 3){
            $row_val = round($rule_['val'],4);
        }
        return $row_val;
    }
    /**多月数据处理**/
    //获取整该时间区域的数据
    private static function getTimeRegionData($mouth_list) {
        //获取月数据(如果规则有上月数据，对应时间区间的上月区间)
        $list = self::getListData($mouth_list);
        $lx_data_list = $list['lx_data'];
        $oa_data_list = $list['oa_data'];
        //领星数据
        $lx_data = [];
        foreach ($lx_data_list as $v) {
            foreach (coustomColumnJobForm::$all_keys as $key_) {
                if (isset($lx_data[$key_])) {
                    $lx_data[$key_] += $v[$key_];
                } else {
                    $lx_data[$key_] = $v[$key_];
                }
            }
        }
        //自定义数据
        $oa_data = [];
        foreach ($oa_data_list as $v) {
            foreach (coustomColumnJobForm::$all_custom_ids as $id_) {
                $key_c = 'oa_key_'.$id_;
                $oa_data[$key_c] = 0;
                if (isset($oa_data[$key_c])) {
                    $oa_data[$key_c] = $v[$key_c];
                } else {
                    $oa_data[$key_c] += $v[$key_c];
                }
            }
        }
        return array_merge($lx_data,$oa_data);
    }
    //按规则计算每个时间段数据（以时间段作为维度）
    private static function getRangeData($this_range_data,$last_range_data) {
        $rule_data = self::$rules[0]['rules'];
        foreach ($rule_data as &$rule_) {
            if ($rule_['group_type'] == 1) {
                $rule_['val'] = self::getValRange($rule_,$this_range_data,$last_range_data);
            } else {
                foreach ($rule_['list'] as &$rule_l) {
                    $rule_l['val'] = self::getValRange($rule_l,$this_range_data,$last_range_data);
                }
            }
        }
        if (self::$custom_id == 4 && self::$need_add_aggregation_val) {
            //毛利润字段，且有合并字段
            $rule_data[] = [
                'group_type'=>1,
                'symbol'=>1,
                'type'=>1,
                'coulmn_key'=>"aggregation_val",
                'val'=>$this_range_data["aggregation_val"],
                'is_absolute'=>0,
            ];
        }
        if (self::$show_type == 2){
            $total = coustomColumnJobForm::getValue($rule_data) * 100;
        } else {
            $total = coustomColumnJobForm::getValue($rule_data);
        }
        return $total;
    }
    //获取区域时间的值
    public static function getValRange($rule_,$this_range_data,$last_range_data) {
        $row_val = 0;
        if ($rule_['type'] == 1) {
            $row_val = $this_range_data[$rule_['coulmn_key']]??0;
        } elseif ($rule_['type'] == 2){
            $row_val = $last_range_data[$rule_['coulmn_key']]??0;
        } else if ($rule_['type'] == 3){
            $row_val = round($rule_['val'],4);
        }
        return $row_val;
    }
    //获取整该时间区域的数据（以项目组为维度）
    private static function getTimeRegionDataByProject($mouth_list) {
        $db = dbFMysql::getInstance();
        $month_array = [];
        $res_list = [];
        $oa_list = [];
        foreach ($mouth_list as $year=>$m_date_list) {
            //获取本年份的值
            //领星数据
            if (count(coustomColumnJobForm::$all_keys)) {
                $fields = 'reportDateMonth as m_date,project_id';
                $group_by = ['reportDateMonth,project_id'];
                $list = self::getSqlWhereList($db,$year,$m_date_list,$group_by,$fields,coustomColumnJobForm::$all_keys);
            } else {
                $list = [];
            }
            if (count(coustomColumnJobForm::$all_custom_ids)) {
                //自定义数据
                $dbo = self::getSqlWhereForOa($db,$year,$m_date_list);
                $list_ = $dbo->whereIn('custom_id',coustomColumnJobForm::$all_custom_ids)
                    ->field('CONCAT(\'oa_key_\',custom_id) as val_key,m_date,custom_id,sum(custom_val) as total,project_id')
                    ->groupBy(['m_date,custom_id,project_id'])
                    ->list();
            } else {
                $list_ = [];
            }
            $month_array = array_merge($month_array,$m_date_list);
            $res_list = array_merge($res_list,$list);
            $oa_list = array_merge($oa_list,$list_);
        }
        //整理月数据，将每个月的数据统计为人民币
        $lx_data_list = self::mergeMdataLx($res_list,$month_array,self::$param['currency_code'],coustomColumnJobForm::$all_keys,coustomColumnJobForm::$currency_keys,1);
        $oa_data_list = self::mergeMdataOa($oa_list,$month_array,coustomColumnJobForm::$all_custom_ids,1);
        $new_list = [];
        foreach ($lx_data_list as $k=>$v) {
            if (!isset($new_list[$k])) {
                $new_list[$k] = $v;
            }
        }
        foreach ($oa_data_list as $k1=>$v1) {
            if (!isset($new_list[$k1])) {
                $new_list[$k1] = $v1;
            } else {
                $new_list[$k1] = array_merge($new_list[$k1],$v1);
            }
        }
        return $new_list;
    }

    //整理规则(更具是否有自定义字段整理为最终的自定义规则，单个规则)
    public static function getResRules($custom_column) {
        $rules = json_decode($custom_column['rules'],true);
        if (!count($rules)) {
            returnError('该自定义字段未设置规则');
        }
        $relation_column = json_decode($custom_column['relation_column']);
        $last_relation_column = json_decode($custom_column['last_relation_column']);
        return [
            'rules'=>$rules,
            'relation_column'=>$relation_column,
            'last_relation_column'=>$last_relation_column
        ];
    }












}