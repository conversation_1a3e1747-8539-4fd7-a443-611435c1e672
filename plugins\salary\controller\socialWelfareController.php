<?php

namespace plugins\salary\Controller;

use core\lib\config;
use core\lib\db\dbMysql;
use core\lib\db\dbSMysql;
use core\lib\redisCached;
use DateTime;
use Rap2hpoutre\FastExcel\FastExcel;
use plugins\salary\models\userModel;

class socialWelfareController
{

    public function getStatistic()
    {
        $paras_list = ['month'];
        $param = array_intersect_key($_GET, array_flip($paras_list));
        if (empty($param['month'])) returnError('缴纳周期必填');
        $month = json_decode($param['month'], true);
        // 校验格式
        if (count($month) != 2) returnError('缴纳周期格式错误');
        if (!strtotime($month[0]) || !strtotime($month[1])) returnError('缴纳周期格式错误');
        $start_month = date('Y-m', strtotime($month[0]));
        $months = [];

        // 这里需要构造按月的数据作为临时月份表
        while ($start_month <= date('Y-m', strtotime($month[1]))) {
            if (empty($months)) {
                $months[] = "select '" . $start_month . "' as month";
            } else {
                $months[] = "union all select '$start_month'";
            }
            $start_month = date('Y-m', strtotime($start_month . '-01 +1 month'));
        }
        $months = implode(' ', $months);
        $months = " ($months) ";

        $db = dbMysql::getInstance();

        $db->table('qwuser', 'u')
            ->field('m.month, u.id as user_id,u.wid as user_wid, u.wname as user_name, u.wphone as user_wphone, u.wdepartment_ids as user_wdepartment_ids, u.wmain_department as user_wmain_department, u.position as user_position, ui.*, a1.attach as user_social_insurance ,a2.attach as user_housing_fund ,a3.attach as user_tax')
            ->leftJoin('user_info', 'ui', 'u.id=ui.qwuser_id')
            ->crossJoin($months, 'm')
            ->leftJoinOut('salary', 'user_insurance_fund_tax', 'a1', 'u.id=a1.qwuser_id and a1.is_delete=0 and a1.type=1 and a1.month = m.month')
            ->leftJoinOut('salary', 'user_insurance_fund_tax', 'a2', 'u.id=a2.qwuser_id and a2.is_delete=0 and a2.type=2 and a1.month = m.month')
            ->leftJoinOut('salary', 'user_insurance_fund_tax', 'a3', 'u.id=a3.qwuser_id and a3.is_delete=0 and a3.type=3 and a1.month = m.month');
        $list = $db->list();
        $ret = [
            'work_without_insurance'    => 0, // 在职未缴纳社保
            'work_without_housing_fund' => 0, // 在职未缴纳公积金
            'leave_with_insurance'      => 0, // 离职已缴纳社保
            'leave_with_housing_fund'   => 0, // 离职已缴纳公积金
        ];
        foreach ($list as $item) {
            if (!isset($item['user_status']) || empty($item['user_status'])) continue;
            if ($item['user_status'] == 3) {
                if (!empty($item['user_social_insurance'])) { // 离职已缴纳社保
                    $ret['leave_with_insurance']++;
                }
                if (!empty($item['user_housing_fund'])) { // 离职已缴纳公积金
                    $ret['leave_with_housing_fund']++;
                }
            } elseif ($item['user_status']) {
                if (empty($item['user_social_insurance'])) { // 在职未缴纳社保
                    $ret['work_without_insurance']++;
                }
                if (empty($item['user_housing_fund'])) { // 在职未缴纳公积金
                    $ret['work_without_housing_fund']++;
                }
            }

        }
        returnSuccess($ret);

    }

    // 五险一金列表
    public function getUserSocialWelfareList()
    {
        $paras_list = ['month', 'user_id', 'corp_id', 'dep_id', 'status', 'page', 'page_size', 'user_status'];
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        if (empty($param['month'])) returnError('缴纳周期必填');
        $month = json_decode($param['month'], true);
        // 校验格式
        if (count($month) != 2) returnError('缴纳周期格式错误');
        if (!strtotime($month[0]) || !strtotime($month[1])) returnError('缴纳周期格式错误');
        $start_month = date('Y-m', strtotime($month[0]));
        $months = [];

        // 这里需要构造按月的数据作为临时月份表
        while ($start_month <= date('Y-m', strtotime($month[1]))) {
            if (empty($months)) {
                $months[] = "select '" . $start_month . "' as month";
            } else {
                $months[] = "union all select '$start_month'";
            }
            $start_month = date('Y-m', strtotime($start_month . '-01 +1 month'));
        }
        $months = implode(' ', $months);
        $months = " ($months) ";

        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();

        $db->table('qwuser', 'u')
            ->field('m.month, u.id as user_id,u.wid as user_wid, u.wname as user_name, u.wphone as user_wphone, u.wdepartment_ids as user_wdepartment_ids, u.wmain_department as user_wmain_department, u.position as user_position, ui.*, a1.attach as user_social_insurance ,a2.attach as user_housing_fund ,a3.attach as user_tax')
            ->leftJoin('user_info', 'ui', 'u.id=ui.qwuser_id')
            ->crossJoin($months, 'm')
            ->leftJoinOut('salary', 'user_insurance_fund_tax', 'a1', 'u.id=a1.qwuser_id and a1.is_delete=0 and a1.type=1 and a1.month = m.month')
            ->leftJoinOut('salary', 'user_insurance_fund_tax', 'a2', 'u.id=a2.qwuser_id and a2.is_delete=0 and a2.type=2 and a1.month = m.month')
            ->leftJoinOut('salary', 'user_insurance_fund_tax', 'a3', 'u.id=a3.qwuser_id and a3.is_delete=0 and a3.type=3 and a1.month = m.month');
        $db->where('where 1=1');
        $db->order('u.id asc, m.month DESC');

        if (userModel::getUserListAuth('userSocialWelfareListAll')) {
            // 全部权限
        } elseif (userModel::getUserListAuth('userSocialWelfareListWorkPlace')) {
            $auth_work_place_id = [];
            $gid = ["1", "2"];
            foreach ($gid as $item) {
                if (userModel::getUserListAuth('userSocialWelfareListWorkPlace_'.$item)) {
                    $auth_work_place_id[] = $item;
                }
            }
            if (empty($auth_work_place_id)) {
                returnError('无权限查看');
            }
            $sdb->whereIn('ui.work_place', $auth_work_place_id);
        }

        // 构建查询条件
        if (!empty($param['user_id'])) {
            $user_ids = json_decode($param['user_id'], true);
            if (!empty($user_ids)) {
                $db->whereIn('u.id', $user_ids);
            }
        }
        // 公司
        if (!empty($param['corp_id'])) {
            $corps = json_decode($param['corp_id'], true);
            if (!empty($corps)) {
                $db->whereIn('ui.corp_id', $corps);
            }
        }
        // 部门
        if (!empty($param['dep_id'])) {
            $departments = json_decode($param['dep_id'], true);
            if (!empty($departments)) {
                $db->whereIn('u.wmain_department', $departments);
            }
        }
        // 员工状态
        if (!empty($param['user_status'])) {
            $user_status = json_decode($param['user_status'], true);
            if (!empty($user_status)) {
                $db->whereIn('ui.user_status', $user_status);
            }
        }

        // 缴纳情况
        if (isset($param['status']) && in_array($param['status'], [0, 1])) {
            if ($param['status'] == 1) { // 正常缴纳：需要社保、公积金都正常缴纳
                $db->andWhere('and a1.attach is not null and a2.attach is not null');
            } else { // 未缴：只要有社保公积金任一未缴
                $db->andWhere('and (a1.attach is null or a2.attach is null)');
            }
        }

        $list = $db->pages($page, $limit);
        empty($list['list']) && returnSuccess($list);

        // 获取默认社保公积金缴纳设置
        $default_setting = $sdb->table('config')
            ->where('where 1=1')
            ->whereIn('key_name', ['social_security', 'housing_fund'])
            ->list();
        foreach ($default_setting as &$item) {
            $item['data'] = json_decode($item['data'], true);
        }
        unset($item);
        $default_setting = array_column($default_setting, 'data', 'key_name');
        $social_security = array_column($default_setting['social_security'], 'num', 'work_place');
        $housing_fund = array_column($default_setting['housing_fund'], 'num', 'work_place');

        // 部门信息
        $department = redisCached::getDepartment();
        $department = array_column($department, 'name', 'wp_id');

        // 公司信息
        $corps = redisCached::getCorp();
        $corps = array_column($corps, null, 'id');

        foreach ($list['list'] as &$item) {
            $item['corp_name'] = $corps[$item['corp_id']]['name'] ?? '';
            $item['user_main_department_name'] = $department[$item['user_wmain_department']] ?? '';
            $item['social_insurance'] = json_decode($item['social_insurance'], true);
            $item['housing_fund'] = json_decode($item['housing_fund'], true);

            $item['user_social_insurance'] = json_decode($item['user_social_insurance'], true);
            $item['user_housing_fund'] = json_decode($item['user_housing_fund'], true);
            $item['user_tax'] = json_decode($item['user_tax'], true);

            // 社保差额， 导入的缴纳金额大于默认值
            if (isset($item['user_social_insurance']['total_corp'])) {
                $item['social_insurance_diff'] = $item['user_social_insurance']['total_corp'] - $social_security[$item['work_place']];
            } else {
                $item['social_insurance_diff'] = 0;
            }
            // 公积金差额， 导入的缴纳金额大于默认值
            if (isset($item['user_housing_fund']['total'])) {
                $item['housing_fund_diff'] = floatval($item['user_housing_fund']['total'] / 2) - $housing_fund[$item['work_place']];
            } else {
                $item['housing_fund_diff'] = 0;
            }
        }

        returnSuccess($list);
    }

    // 员工五险一金明细
    public function getUserSocialWelfareDetail()
    {
        $paras_list = ['month', 'user_id'];
        $param = array_intersect_key($_GET, array_flip($paras_list));
        if (empty($param['month'])) returnError('缴纳周期必填');
        if (empty($param['user_id'])) returnError('用户ID必填');
        $month = $param['month'] == 'null' ? null : $param['month'];

        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();

        $db->table('qwuser', 'u')
            ->field('u.id as user_id,u.wid as user_wid, u.wname as user_name, u.wphone as user_wphone, u.wdepartment_ids as user_wdepartment_ids, u.wmain_department as user_wmain_department, u.position as user_position, ui.*')
            ->leftJoin('user_info', 'ui', 'u.id=ui.qwuser_id');
        $db->where('where u.id = :user_id', ['user_id' => $param['user_id']]);

        $detail = $db->one();

        if (!$detail) returnError('记录不存在');

        $user_insurance_fund_tax = [];
        if ($month) {
            $user_insurance_fund_tax = $sdb->table('user_insurance_fund_tax')
                ->where('where qwuser_id = :user_id and month = :month', ['user_id' => $param['user_id'], 'month' => $month])
                ->list();
            $user_insurance_fund_tax = array_column($user_insurance_fund_tax, null, 'type');
        }


        // 获取默认社保公积金缴纳设置
        $default_setting = $sdb->table('config')
            ->where('where 1=1')
            ->whereIn('key_name', ['social_security', 'housing_fund'])
            ->list();
        foreach ($default_setting as &$item) {
            $item['data'] = json_decode($item['data'], true);
        }
        unset($item);
        $default_setting = array_column($default_setting, 'data', 'key_name');
        $social_security = array_column($default_setting['social_security'], 'num', 'work_place');
        $housing_fund = array_column($default_setting['housing_fund'], 'num', 'work_place');

        // 部门信息
        $department = $db->table('qwdepartment')->field('wp_id, name')->list();
        $department = array_column($department, 'name', 'wp_id');

        // 公司信息
        $corps = $sdb->table('corp')->list();
        $corps = array_column($corps, null, 'id');


        $detail['corp_name'] = $corps[$detail['corp_id']]['name'] ?? '';
        $detail['user_main_department_name'] = $department[$detail['user_wmain_department']] ?? '';
        $detail['social_insurance'] = json_decode($detail['social_insurance'], true);
        $detail['housing_fund'] = json_decode($detail['housing_fund'], true);

        $detail['month'] = $month;
        $detail['user_social_insurance'] = json_decode($user_insurance_fund_tax['1']['attach'], true) ?? null;
        $detail['user_housing_fund'] = json_decode($user_insurance_fund_tax['2']['attach'], true) ?? null;
        $detail['user_tax'] = json_decode($user_insurance_fund_tax['3']['attach'], true) ?? null;

        // 社保差额， 导入的缴纳金额大于默认值
        $detail['social_insurance_diff'] = $detail['user_social_insurance']['total_corp'] - $social_security[$detail['work_place']];
        // 公积金差额， 导入的缴纳金额大于默认值
        $detail['housing_fund_diff'] = floatval($detail['user_housing_fund']['total'] / 2) - $housing_fund[$detail['work_place']];

        returnSuccess($detail);
    }

    // 导出
    public function exportUserSocialWelfare()
    {
        $paras_list = ['month', 'user_id', 'corp_id', 'dep_id', 'status', 'user_status', 'keys', 'rows'];
        $param = array_intersect_key($_POST, array_flip($paras_list));
        if (empty($param['month'])) returnError('缴纳周期必填');
        if (empty($param['keys'])) returnError('导出字段必填');
        $month = json_decode($param['month'], true);

        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();

        $db->table('qwuser', 'u')
            ->field('u.id as user_id,u.wid as user_wid, u.wname as user_name, u.wphone as user_wphone, u.wdepartment_ids as user_wdepartment_ids, u.wmain_department as user_wmain_department, u.position as user_position, ui.*,a1.month as month1,a2.month as month2,a3.month as month3, a1.attach as user_social_insurance ,a2.attach as user_housing_fund ,a3.attach as user_tax')
            ->leftJoin('user_info', 'ui', 'u.id=ui.qwuser_id')
            ->leftJoinOut('salary', 'user_insurance_fund_tax', 'a1', 'u.id=a1.qwuser_id and a1.type=1')
            ->leftJoinOut('salary', 'user_insurance_fund_tax', 'a2', 'u.id=a2.qwuser_id and a2.type=2 and a2.month = a1.month')
            ->leftJoinOut('salary', 'user_insurance_fund_tax', 'a3', 'u.id=a3.qwuser_id and a3.type=3 and a3.month = a1.month');
        $db->where('where ((a1.month >= :s_month and a1.month <= :e_month) or a1.month is null)', ['s_month' => $month[0], 'e_month' => $month[1]]);
        $db->andwhere('and ((a2.month >= :s_month and a2.month <= :e_month) or a2.month is null)', ['s_month' => $month[0], 'e_month' => $month[1]]);
        $db->andwhere('and ((a3.month >= :s_month and a3.month <= :e_month) or a3.month is null)', ['s_month' => $month[0], 'e_month' => $month[1]]);


        // 构建查询条件
        if (!empty($param['user_id'])) {
            $user_ids = json_decode($param['user_id'], true);
            if (!empty($user_ids)) {
                $db->whereIn('u.id', $user_ids);
            }
        }
        // 公司
        if (!empty($param['corp_id'])) {
            $corps = json_decode($param['corp_id'], true);
            if (!empty($corps)) {
                $db->whereIn('ui.corp_id', $corps);
            }
        }
        // 部门
        if (!empty($param['dep_id'])) {
            $departments = json_decode($param['dep_id'], true);
            if (!empty($departments)) {
                $db->whereIn('u.wmain_department', $departments);
            }
        }
        // 员工状态
        if (!empty($param['user_status'])) {
            $user_status = json_decode($param['user_status'], true);
            if (!empty($user_status)) {
                $db->whereIn('ui.user_status', $user_status);
            }
        }

        // 缴纳情况
        if (isset($param['status']) && in_array($param['status'], [0, 1])) {
            if ($param['status'] == 1) { // 正常缴纳：需要社保、公积金都正常缴纳
                $db->andWhere('and a1.attach is not null and a2.attach is not null');
            } else { // 未缴：只要有社保公积金任一未缴
                $db->andWhere('and (a1.attach is null or a2.attach is null)');
            }
        }

        $db->order('u.id DESC');
        $list = $db->list();
        empty($list) && returnError('没有数据');

        // 获取默认社保公积金缴纳设置
        $default_setting = $sdb->table('config')
            ->where('where 1=1')
            ->whereIn('key_name', ['social_security', 'housing_fund'])
            ->list();
        foreach ($default_setting as &$item) {
            $item['data'] = json_decode($item['data'], true);
        }
        unset($item);
        $default_setting = array_column($default_setting, 'data', 'key_name');
        $social_security = array_column($default_setting['social_security'], 'num', 'work_place');
        $housing_fund = array_column($default_setting['housing_fund'], 'num', 'work_place');

        // 部门信息
        $department = $db->table('qwdepartment')->field('wp_id, name')->list();
        $department = array_column($department, 'name', 'wp_id');

        // 公司信息
        $corps = $sdb->table('corp')->list();
        $corps = array_column($corps, null, 'id');

        $new_data = [];

        foreach ($list as &$item) {
            $item['month'] = null;
            $item['month1'] && $item['month'] = $item['month1'];
            $item['month2'] && $item['month'] = $item['month2'];
            $item['month3'] && $item['month'] = $item['month3'];
            $item['corp_name'] = $corps[$item['corp_id']]['name'] ?? '';
            $item['user_main_department_name'] = '';
            if (isset($item['wmain_department']) && $item['wmain_department'] && isset($department[$item['wmain_department']])) {
                $item['user_main_department_name'] = $department[$item['wmain_department']];
            }
            $item['social_insurance'] = json_decode($item['social_insurance'], true);
            $item['housing_fund'] = json_decode($item['housing_fund'], true);

            $item['user_social_insurance'] = json_decode($item['user_social_insurance'], true);
            $item['user_housing_fund'] = json_decode($item['user_housing_fund'], true);
            $item['user_tax'] = json_decode($item['user_tax'], true);

            // 社保差额， 导入的缴纳金额大于默认值
            $item['user_social_insurance']['total_corp'] = $item['user_social_insurance']['total_corp'] ?? 0;
            $social_security[$item['work_place']] = $social_security[$item['work_place']] ?? 0;
            $item['social_insurance_diff'] = $item['user_social_insurance']['total_corp'] - $social_security[$item['work_place']];
            // 公积金差额， 导入的缴纳金额大于默认值
            $item['user_housing_fund']['total'] = $item['user_housing_fund']['total'] ?? 0;
            $housing_fund[$item['work_place']] = $housing_fund[$item['work_place']] ?? 0;
            $item['housing_fund_diff'] = floatval($item['user_housing_fund']['total'] / 2) - $housing_fund[$item['work_place']];
        }

        $keys = json_decode($param['keys'], true);
        $rows = [];
        if (!empty($param['rows'])) {
            $rows = json_decode($param['rows'], true);
        }
        $new_data = self::filterData($list, $keys, $rows);

        //保存
        $save_path = "/public/salary/temp/user";
        $url = SELF_FK . $save_path;

        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path . "/" . date('YmdHis') . uniqid() . '.xlsx';
        $url = SELF_FK . $path;
        if (!file_exists($url)) {
            touch($url);
        }
        (new FastExcel($new_data))->export($url);
        returnSuccess(['path' => $path]);

    }

    // 社保列表
    public function getSocialInsuranceList()
    {
        $list = self::getList(1, $_GET);

        returnSuccess($list);
    }

    // 社保明细
    public function getSocialWelfareDetail()
    {
        $paras_list = ['id'];
        $param = array_intersect_key($_GET, array_flip($paras_list));
        if (empty($param['id'])) returnError('id不能为空');

        $detail = self::getDetail(1, $param['id']);

        returnSuccess($detail);

    }

    // 社保导入
    public function importSocialWelfare()
    {

        $paras_list = array('excel_src');
        $request_list = ['excel_src' => '表格链接'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $excel_url = SELF_FK . $param['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格不存在');
        }

        $excel_data = (new FastExcel)->import($excel_url);
        $data = $excel_data->toArray();
        if (!count($data)) {
            returnError('表格内不存在数据');
        }

        // 表头
        //缴费时间,单位名称,姓名,身份证号,
        //应交合计,个人应交合计,单位应交合计,
        //养老缴纳基数,养老个人缴纳,养老单位缴纳,
        //医疗缴纳基数,医疗个人缴纳,医疗单位缴纳,
        //工伤缴纳基数,工伤单位缴纳,工伤个人缴纳,
        //失业缴纳基数,失业单位缴纳,失业个人缴纳,
        //生育缴纳基数,生育单位缴纳,生育个人缴纳,
        //是否缴纳一档
        $first_user = $data[0];
        if (!isset($first_user['缴费时间']) || !isset($first_user['单位名称']) || !isset($first_user['姓名']) || !isset($first_user['身份证号']) ||
            !isset($first_user['应交合计']) || !isset($first_user['个人应交合计']) || !isset($first_user['单位应交合计']) ||
            !isset($first_user['养老缴纳基数']) || !isset($first_user['养老个人缴纳']) || !isset($first_user['养老单位缴纳']) ||
            !isset($first_user['医疗缴纳基数']) || !isset($first_user['医疗个人缴纳']) || !isset($first_user['医疗单位缴纳']) ||
            !isset($first_user['工伤缴纳基数']) || !isset($first_user['工伤单位缴纳']) || !isset($first_user['工伤个人缴纳']) ||
            !isset($first_user['失业缴纳基数']) || !isset($first_user['失业单位缴纳']) || !isset($first_user['失业个人缴纳']) ||
            !isset($first_user['生育缴纳基数']) || !isset($first_user['生育单位缴纳']) || !isset($first_user['生育个人缴纳'])) {
            returnError('表格格式错误');
        }

        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();
        // 用户基本信息
        $users = $db->table('user_info')->list();
        $users = array_column($users, null, 'id_number');

        // 公司信息
        $corps = $sdb->table('corp')->list();
        $corps = array_column($corps, null, 'name');

        // 已存在的缴纳信息
        $months = array_column($data, '缴费时间');
        $list = $sdb->table('user_insurance_fund_tax')
            ->where('where is_delete = 0 and type = 1')
            ->whereIn('month', $months)
            ->list();
        $user_list = [];
        foreach ($list as $item) {
            $user_list[$item['month'] . '-' . $item['qwuser_id']] = $item;
        }

        $error_data = [];
        $res_data = [];
        foreach ($data as $item) {
            $error_msg = [];
            if (empty($item['姓名'])) continue;
            $dt = DateTime::createFromFormat('Y-m', $item['缴费时间']);
            if (!($dt && $dt->format('Y-m') === $item['缴费时间'])) {
                $error_msg[] = '缴费时间格式错误';
            }
            $month = $item['缴费时间'];
            if (!array_key_exists($item['身份证号'], $users)) {
                $error_msg[] = '用户不存在';
            }
            $cur_user = $users[$item['身份证号']] ?? [];
            if (empty($cur_user)) {
                $error_msg[] = '证件号码未匹配到用户';
            } else {
                if (empty($item['姓名']) || $item['姓名'] != $cur_user['name']) {
                    $error_msg[] = '用户姓名错误';
                }
            }
            if (empty($item['单位名称']) || !array_key_exists($item['单位名称'], $corps)) {
                $error_msg[] = '公司不存在';
            } else {
                $corp_id = $corps[$item['单位名称']]['id'];
            }

            if (!empty($error_msg)) {
                $error_data[] = array_merge($item, ['失败原因' => implode('，', $error_msg)]);
                continue;
            }

            $attach = [
                'total'                                => $item['应交合计'] ?? 0,
                'total_personal'                       => $item['个人应交合计'] ?? 0,
                'total_corp'                           => $item['单位应交合计'] ?? 0,
                'endowment_insurance_base'             => $item['养老缴纳基数'] ?? 0,
                'endowment_insurance_personal'         => $item['养老个人缴纳'] ?? 0,
                'endowment_insurance_corp'             => $item['养老单位缴纳'] ?? 0,
                'medical_insurance_base'               => $item['医疗缴纳基数'] ?? 0,
                'medical_insurance_personal'           => $item['医疗个人缴纳'] ?? 0,
                'medical_insurance_corp'               => $item['医疗单位缴纳'] ?? 0,
                'unemployment_insurance_base'          => $item['失业缴纳基数'] ?? 0,
                'unemployment_insurance_personal'      => $item['失业个人缴纳'] ?? 0,
                'unemployment_insurance_corp'          => $item['失业单位缴纳'] ?? 0,
                'employment_injury_insurance_base'     => $item['工伤缴纳基数'] ?? 0,
                'employment_injury_insurance_personal' => $item['工伤个人缴纳'] ?? 0,
                'employment_injury_insurance_corp'     => $item['工伤单位缴纳'] ?? 0,
                'maternity_insurance_base'             => $item['生育缴纳基数'] ?? 0,
                'maternity_insurance_personal'         => $item['生育个人缴纳'] ?? 0,
                'maternity_insurance_corp'             => $item['生育单位缴纳'] ?? 0,
            ];

            $user_info_data = [
                'month'     => $month,
                'qwuser_id' => $cur_user['qwuser_id'],
                'corp_id'   => $corp_id,
                'attach'    => json_encode($attach),
                'type'      => 1,
                'operator'  => userModel::$qwuser_id
            ];
            if (array_key_exists($month . '-' . $cur_user['qwuser_id'], $user_list)) {
                $id = $user_list[$month . '-' . $cur_user['qwuser_id']]['id'];
                $sdb->table('user_insurance_fund_tax')->where('id = :id', ['id' => $id])->update($user_info_data);
            } else {
                $sdb->table('user_insurance_fund_tax')->insert($user_info_data);
            }
            $res_data[] = $item;
        }

        if (count($error_data) > 0) {
            // 使用项目根目录动态构建导出文件路径
            $src = '/public/salary/temp/error/user_import_errors' . time() . rand(100, 1000) . '.xlsx';
            $exportPath = SELF_FK . $src;

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径

            returnSuccess(['error_file' => $src, 'error_count' => count($error_data), 'success_count' => count($res_data), 'data' => $error_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => count($error_data), 'success_count' => count($res_data), 'data' => $res_data], '导入成功');
        }

    }

    // 删除社保
    public function deleteSocialWelfare()
    {
        $paras_list = ['id'];
        $param = array_intersect_key($_POST, array_flip($paras_list));
        if (empty($param['id'])) returnError('id不能为空');

        $sdb = dbSMysql::getInstance();
        $detail = $sdb->table('user_insurance_fund_tax')
            ->where('where id = :id AND is_delete = 0 and type = 1', ['id' => $param['id']])
            ->one();

        if (!$detail) returnError('记录不存在');

        $sdb->table('user_insurance_fund_tax')
            ->where('id = :id', ['id' => $param['id']])
            ->update(['is_delete' => 1]);

        returnSuccess([], '删除成功');

    }

    // 公积金列表
    public function getHousingFundList()
    {
        $list = self::getList(2, $_GET);

        returnSuccess($list);

    }

    // 公积金明细
    public function getHousingFundDetail()
    {
        $paras_list = ['id'];
        $param = array_intersect_key($_GET, array_flip($paras_list));
        if (empty($param['id'])) returnError('id不能为空');

        $detail = self::getDetail(2, $param['id']);

        returnSuccess($detail);

    }

    // 公积金导入
    public function importHousingFund()
    {
        $paras_list = array('excel_src');
        $request_list = ['excel_src' => '表格链接'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $excel_url = SELF_FK . $param['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格不存在');
        }

        $excel_data = (new FastExcel)->import($excel_url);
        $data = $excel_data->toArray();
        if (!count($data)) {
            returnError('表格内不存在数据');
        }

        // 表头
        // 姓名,证件号码,个人账号,缴存时间,缴存基数（元）,单位缴存比例,个人缴存比例
        // 金额合计（元）,个人承担, 差额, 公司名称
        $first_user = $data[0];
        if (empty($first_user['姓名']) || empty($first_user['证件号码']) || !isset($first_user['个人账号']) || empty($first_user['缴存时间']) ||
            !isset($first_user['缴存基数（元）']) || !isset($first_user['单位缴存比例']) || !isset($first_user['个人缴存比例']) ||
            !isset($first_user['金额合计（元）']) || !isset($first_user['公司名称'])) {
            returnError('表格格式错误');
        }

        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();
        // 用户基本信息
        $users = $db->table('user_info')->list();
        $users = array_column($users, null, 'id_number');

        // 公司信息
        $corps = $sdb->table('corp')->list();
        $corps = array_column($corps, null, 'name');

        // 已存在的缴纳信息
        $months = array_column($data, '缴存时间');
        $list = $sdb->table('user_insurance_fund_tax')
            ->where('where is_delete = 0 and type = 2')
            ->whereIn('month', $months)
            ->list();
        $user_list = [];
        foreach ($list as $item) {
            $user_list[$item['month'] . '-' . $item['qwuser_id']] = $item;
        }

        $error_data = [];
        $res_data = [];
        foreach ($data as $item) {
            $error_msg = [];
            $dt = DateTime::createFromFormat('Y-m', $item['缴存时间']);
            if (!($dt && $dt->format('Y-m') === $item['缴存时间'])) {
                $error_msg[] = '缴费时间格式错误';
            }
            $month = $item['缴存时间'];
            if (!array_key_exists($item['证件号码'], $users)) {
                $error_msg[] = '用户不存在';
            }
            $cur_user = $users[$item['证件号码']] ?? [];
            if (empty($cur_user)) {
                $error_msg[] = '证件号码未匹配到用户';
            } else {
                if (empty($item['姓名']) || $item['姓名'] != $cur_user['name']) {
                    $error_msg[] = '用户姓名错误';
                }
            }
            if (empty($item['公司名称']) || !array_key_exists($item['公司名称'], $corps)) {
                $error_msg[] = '公司不存在';
            } else {
                $corp_id = $corps[$item['公司名称']]['id'];
            }

            if (!empty($error_msg)) {
                $error_data[] = array_merge($item, ['失败原因' => implode('，', $error_msg)]);
                continue;
            }


            $attach = [
                'account'        => $item['个人账号'] ?? '',
                'base'           => $item['缴存基数（元）'] ?? 0,
                'corp_rate'      => $item['单位缴存比例'] ?? 0,
                'personal_rate'  => $item['个人缴存比例'] ?? 0,
                'total'          => $item['金额合计（元）'] ?? 0,
                'total_corp'     => $item['金额合计（元）'] / 2 ?? 0,
                'total_personal' => $item['金额合计（元）'] / 2 ?? 0,
            ];

            $user_info_data = [
                'month'     => $month,
                'qwuser_id' => $cur_user['qwuser_id'],
                'corp_id'   => $corp_id,
                'attach'    => json_encode($attach),
                'type'      => 2,
                'operator'  => userModel::$qwuser_id
            ];
            if (array_key_exists($month . '-' . $cur_user['qwuser_id'], $user_list)) {
                $id = $user_list[$month . '-' . $cur_user['qwuser_id']]['id'];
                $sdb->table('user_insurance_fund_tax')->where('id = :id', ['id' => $id])->update($user_info_data);
            } else {
                $sdb->table('user_insurance_fund_tax')->insert($user_info_data);
            }

            $res_data[] = $item;
        }

        if (count($error_data) > 0) {
            // 使用项目根目录动态构建导出文件路径
            $src = '/public/salary/temp/error/user_import_errors' . time() . rand(100, 1000) . '.xlsx';
            $exportPath = SELF_FK . $src;

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径

            returnSuccess(['error_file' => $src, 'error_count' => count($error_data), 'success_count' => count($res_data), 'data' => $error_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => count($error_data), 'success_count' => count($res_data), 'data' => $res_data], '导入成功');
        }


    }

    // 公积金删除
    public function deleteHousingFund()
    {
        $paras_list = ['id'];
        $param = array_intersect_key($_POST, array_flip($paras_list));
        if (empty($param['id'])) returnError('id不能为空');

        $sdb = dbSMysql::getInstance();
        $detail = $sdb->table('user_insurance_fund_tax')
            ->where('where id = :id AND is_delete = 0 and type = 2', ['id' => $param['id']])
            ->one();

        if (!$detail) returnError('记录不存在');

        $sdb->table('user_insurance_fund_tax')
            ->where('id = :id', ['id' => $param['id']])
            ->update(['is_delete' => 1]);

        returnSuccess([], '删除成功');

    }

    // 个人所得税列表
    public function getPersonalIncomeTaxList()
    {
        $list = self::getList(3, $_GET);
        returnSuccess($list);
    }

    // 个人所得税明细
    public function getPersonalIncomeTaxDetail()
    {
        $paras_list = ['id'];
        $param = array_intersect_key($_GET, array_flip($paras_list));
        if (empty($param['id'])) returnError('id不能为空');

        $detail = self::getDetail(3, $param['id']);

        returnSuccess($detail);

    }

    // 个人所得税导入
    public function importPersonalIncomeTax()
    {
        $paras_list = array('excel_src');
        $request_list = ['excel_src' => '表格链接'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $excel_url = SELF_FK . $param['excel_src'];
        if (!file_exists($excel_url)) {
            returnError('表格不存在');
        }

        $excel_data = (new FastExcel)->import($excel_url);
        $data = $excel_data->toArray();
        if (!count($data)) {
            returnError('表格内不存在数据');
        }

        // 表头
        // 姓名,证件号码,税款所属期,所得项目,本期收入,本期费用,本期免税收入,
        // 本期基本养老保险费,本期基本医疗保险费,本期失业保险费,本期住房公积金,本期企业(职业)年金,本期商业健康保险费,本期税延养老保险费,本期其他扣除(其他)
        // 累计收入额,累计免税收入,累计减除费用,
        // 累计专项扣除,累计子女教育支出扣除,累计继续教育支出扣除,累计住房贷款利息支出扣除,累计住房租金支出扣除,累计赡养老人支出扣除,累计3岁以下婴幼儿照护,
        // 累计个人养老金,累计其他扣除,累计准予扣除的捐赠,累计应纳税所得额,税率,速算扣除数,
        // 累计应纳税额,累计减免税额,累计应扣缴税额,累计已预缴税额,累计应补(退)税额,
        // [非必填] 备注,专项扣除,月数,已预缴金额
        $first_user = $data[0];
        if (empty($first_user['姓名']) || empty($first_user['证件号码']) || empty($first_user['税款所属期']) || empty($first_user['所得项目']) ||
            empty($first_user['本期收入']) || !isset($first_user['本期费用']) || !isset($first_user['本期免税收入']) ||
            !isset($first_user['本期基本养老保险费']) || !isset($first_user['本期基本医疗保险费']) || !isset($first_user['本期失业保险费']) ||
            !isset($first_user['本期住房公积金']) || !isset($first_user['本期企业(职业)年金']) || !isset($first_user['本期商业健康保险费']) ||
            !isset($first_user['本期税延养老保险费']) || !isset($first_user['本期其他扣除(其他)']) ||
            !isset($first_user['累计收入额']) || !isset($first_user['累计免税收入']) || !isset($first_user['累计减除费用']) ||
            !isset($first_user['累计专项扣除']) || !isset($first_user['累计子女教育支出扣除']) || !isset($first_user['累计继续教育支出扣除']) ||
            !isset($first_user['累计住房贷款利息支出扣除']) || !isset($first_user['累计住房租金支出扣除']) || !isset($first_user['累计赡养老人支出扣除']) ||
            !isset($first_user['累计3岁以下婴幼儿照护']) || !isset($first_user['累计个人养老金']) || !isset($first_user['累计其他扣除']) ||
            !isset($first_user['累计准予扣除的捐赠']) || !isset($first_user['累计应纳税所得额']) || !isset($first_user['税率']) ||
            !isset($first_user['速算扣除数']) || !isset($first_user['累计应纳税额']) || !isset($first_user['累计减免税额']) ||
            !isset($first_user['累计应扣缴税额']) || !isset($first_user['累计已预缴税额']) || !isset($first_user['累计应补(退)税额'])) {
            returnError('表格格式错误');
        }

        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();
        // 用户基本信息
        $users = $db->table('user_info')->list();
        $users = array_column($users, null, 'id_number');

        // 公司信息
        $corps = $sdb->table('corp')->list();
        $corps = array_column($corps, null, 'name');

        // 已存在的缴纳信息
        $months = array_column($data, '缴费时间');
        $list = $sdb->table('user_insurance_fund_tax')
            ->where('where is_delete = 0 and type = 3')
            ->whereIn('month', $months)
            ->list();
        $user_list = [];
        foreach ($list as $item) {
            $user_list[$item['month'] . '-' . $item['qwuser_id']] = $item;
        }

        $error_data = [];
        $res_data = [];
        foreach ($data as $item) {
            $error_msg = [];
            $dt = DateTime::createFromFormat('Y-m', $item['税款所属期']);
            if (!($dt && $dt->format('Y-m') === $item['税款所属期'])) {
                $error_msg[] = '缴费时间格式错误';
            }
            $month = $item['税款所属期'];
            if (!array_key_exists($item['证件号码'], $users)) {
                $error_msg[] = '用户不存在';
            }
            $cur_user = $users[$item['证件号码']] ?? [];
            if (empty($cur_user)) {
                $error_msg[] = '证件号码未匹配到用户';
            } else {
                if (empty($item['姓名']) || $item['姓名'] != $cur_user['name']) {
                    $error_msg[] = '用户姓名错误';
                }
            }
            if (empty($item['公司名称']) || !array_key_exists($item['公司名称'], $corps)) {
                $error_msg[] = '公司不存在';
            } else {
                $corp_id = $corps[$item['公司名称']]['id'];
            }

            if (!empty($error_msg)) {
                $error_data[] = array_merge($item, ['失败原因' => implode('，', $error_msg)]);
                continue;
            }


            $attach = [
                'project'                          => $item['所得项目'] ?? '',
                'income'                           => $item['本期收入'] ?? 0,
                'cost'                             => $item['本期费用'] ?? 0,
                'exempt_income'                    => $item['本期免税收入'] ?? 0,
                'endowment_insurance'              => $item['本期基本养老保险费'] ?? 0,
                'medical_insurance'                => $item['本期基本医疗保险费'] ?? 0,
                'unemployment_insurance'           => $item['本期失业保险费'] ?? 0,
                'housing_fund'                     => $item['本期住房公积金'] ?? 0,
                'enterprise_pension'               => $item['本期企业(职业)年金'] ?? 0,
                'commercial_health_insurance'      => $item['本期商业健康保险费'] ?? 0,
                'tax_deferred_endowment_insurance' => $item['本期税延养老保险费'] ?? 0,
                'other_deduction'                  => $item['本期其他扣除(其他)'] ?? 0,
                'total_income'                     => $item['累计收入额'] ?? 0,
                'total_exempt_income'              => $item['累计免税收入'] ?? 0,
                'total_deduction'                  => $item['累计减除费用'] ?? 0,
                'total_special_deduction'          => $item['累计专项扣除'] ?? 0,
                'child_education_deduction'        => $item['累计子女教育支出扣除'] ?? 0,
                'continuing_education_deduction'   => $item['累计继续教育支出扣除'] ?? 0,
                'housing_loan_interest_deduction'  => $item['累计住房贷款利息支出扣除'] ?? 0,
                'housing_rent_deduction'           => $item['累计住房租金支出扣除'] ?? 0,
                'supporting_elderly_deduction'     => $item['累计赡养老人支出扣除'] ?? 0,
                'infant_care_deduction'            => $item['累计3岁以下婴幼儿照护'] ?? 0,
                'personal_pension'                 => $item['累计个人养老金'] ?? 0,
                'total_other_deduction'            => $item['累计其他扣除'] ?? 0,
                'total_donation_deduction'         => $item['累计准予扣除的捐赠'] ?? 0,
                'total_taxable_income'             => $item['累计应纳税所得额'] ?? 0,
                'tax_rate'                         => $item['税率'] ?? 0,
                'quick_deduction'                  => $item['速算扣除数'] ?? 0,
                'total_tax_payable'                => $item['累计应纳税额'] ?? 0,
                'total_tax_relief'                 => $item['累计减免税额'] ?? 0,
                'total_tax_withheld'               => $item['累计应扣缴税额'] ?? 0,
                'total_pre_paid_tax'               => $item['累计已预缴税额'] ?? 0,
                'total_tax_refund'                 => $item['累计应补(退)税额'] ?? 0,
                'remark'                           => $item['备注'] ?? '',
                'special_deduction'                => $item['专项扣除'] ?? 0,
                'months'                           => $item['月数'] ?? '',
                'pre_paid_amount'                  => $item['已预缴金额'] ?? 0,
            ];
            $user_info_data = [
                'month'     => $month,
                'qwuser_id' => $cur_user['qwuser_id'],
                'corp_id'   => $corp_id,
                'attach'    => json_encode($attach),
                'type'      => 3,
                'operator'  => userModel::$qwuser_id
            ];
            if (array_key_exists($month . '-' . $cur_user['qwuser_id'], $user_list)) {
                $id = $user_list[$month . '-' . $cur_user['qwuser_id']]['id'];
                $sdb->table('user_insurance_fund_tax')->where('id = :id', ['id' => $id])->update($user_info_data);
            } else {
                $sdb->table('user_insurance_fund_tax')->insert($user_info_data);
            }
            $res_data[] = $item;
        }

        if (count($error_data) > 0) {
            // 使用项目根目录动态构建导出文件路径
            $src = '/public/salary/temp/error/user_import_errors' . time() . rand(100, 1000) . '.xlsx';
            $exportPath = SELF_FK . $src;

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 使用 FastExcel 导出错误数据
            (new FastExcel($error_data))->export($exportPath);

            // 返回导出文件路径

            returnSuccess(['error_file' => $src, 'error_count' => count($error_data), 'success_count' => count($res_data), 'data' => $error_data], '导入完成，部分数据存在错误');
        } else {
            returnSuccess(['error_count' => count($error_data), 'success_count' => count($res_data), 'data' => $res_data], '导入成功');
        }

    }

    // 删除个人所得税
    public function deletePersonalIncomeTax()
    {
        $paras_list = ['id'];
        $param = array_intersect_key($_POST, array_flip($paras_list));
        if (empty($param['id'])) returnError('id不能为空');

        $sdb = dbSMysql::getInstance();
        $detail = $sdb->table('user_insurance_fund_tax')
            ->where('where id = :id AND is_delete = 0 and type = 3', ['id' => $param['id']])
            ->one();

        if (!$detail) returnError('记录不存在');

        $sdb->table('user_insurance_fund_tax')
            ->where('id = :id', ['id' => $param['id']])
            ->update(['is_delete' => 1]);

        returnSuccess([], '删除成功');
    }

    // 五险一金列表
    private static function getList($type, $data)
    {
        $paras_list = ['month', 'corp_id', 'dep_id', 'user_id', 'page', 'page_size'];
        $param = array_intersect_key($data, array_flip($paras_list));
        if (empty($param['month'])) returnError('月份不能为空');
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;

        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();
        $sdb->table('user_insurance_fund_tax', 'a')
            ->field('a.*,c.wname as user_name, c.wdepartment_ids as user_wdepartment_ids, c.wmain_department as user_wmain_department, c.wmain_department as user_wmain_department, c.position,b.social_insurance, b.housing_fund, b.id_number,b.corp_id,b.work_place')
            ->leftJoinOut('db', 'qwuser', 'c', 'c.id=a.qwuser_id')
            ->leftJoinOut('db', 'user_info', 'b', 'b.qwuser_id=a.qwuser_id')
            ->where('where a.type = :type and a.is_delete = 0', ['type' => $type]);

        if (userModel::getUserListAuth('socialInsuranceListAll')) {
            // 全部权限
        } elseif (userModel::getUserListAuth('socialInsuranceListWorkPlace')) {
            $auth_work_place_id = [];
            $gid = ["1", "2"];
            foreach ($gid as $item) {
                if (userModel::getUserListAuth('socialInsuranceListWorkPlace_'.$item)) {
                    $auth_work_place_id[] = $item;
                }
            }
            if (empty($auth_work_place_id)) {
                returnError('无权限查看');
            }
            $sdb->whereIn('b.work_place', $auth_work_place_id);
        }

        // 构建查询条件
        if (!empty($param['month'])) {
            $month = json_decode($param['month'], true);
            $sdb->andWhere('a.month >= :start_month and a.month <= :end_month', ['start_month' => $month[0], 'end_month' => $month[1]]);
        }
        if (!empty($param['corp_id'])) {
            $sdb->whereIn('a.corp_id', json_decode($param['corp_id'], true));
        }

        if (!empty($param['user_id'])) {
            $sdb->whereIn('a.qwuser_id', json_decode($param['user_id'], true));
        }
        if (!empty($param['dep_id'])) {
            $qw_partment_ids = json_decode($param['dep_id'], true);
            $sdb->whereIn('c.wmain_department', $qw_partment_ids);
        }

        $sdb->order('a.create_time DESC, a.month desc, a.corp_id desc');
        $list = $sdb->pages($page, $limit);
        if (empty($list['list'])) returnSuccess($list);

        // 获取默认社保公积金缴纳设置
        $default_setting = $sdb->table('config')
            ->where('where 1=1')
            ->whereIn('key_name', ['social_security', 'housing_fund'])
            ->list();
        foreach ($default_setting as &$item) {
            $item['data'] = json_decode($item['data'], true);
        }
        unset($item);
        $default_setting = array_column($default_setting, 'data', 'key_name');
        $social_security = array_column($default_setting['social_security'], 'num', 'work_place');
        $housing_fund = array_column($default_setting['housing_fund'], 'num', 'work_place');

        // 部门信息
        $department = redisCached::getDepartment();
        $department = array_column($department, 'name', 'wp_id');

        // 公司信息
        $corps = redisCached::getCorp();
        $corps = array_column($corps, null, 'id');

        // 关联用户信息
        $user_ids = array_column($list['list'], 'operator');
        $db = dbMysql::getInstance();
        $users = $db->table('qwuser')
            ->whereIn('id', $user_ids)
            ->field('id, wname')
            ->list();
        $userMap = array_column($users, 'wname', 'id');

        // 处理JSON字段
        foreach ($list['list'] as &$item) {
            $item['attach'] = json_decode($item['attach'], true);
            $item['operator_name'] = $userMap[$item['operator']] ?? '';
            $item['social_insurance'] = json_decode($item['social_insurance'], true);
            $item['housing_fund'] = json_decode($item['housing_fund'], true);

            $item['corp_name'] = $corps[$item['corp_id']]['name'] ?? '';
            $item['user_main_department_name'] = $department[$item['user_wmain_department']] ?? '';
            switch ($type) {
                case 1: // 社保
                    $item['social_insurance_diff'] = $item['attach']['total_corp'] - $social_security[$item['work_place']];
                    // 导入大于默认值
                case 2: // 公积金
                    $item['housing_fund_diff'] = floatval($item['attach']['total'] / 2) - $housing_fund[$item['work_place']];

            }
        }
        return $list;
    }

    private static function getDetail($type, $id)
    {
        $db = dbMysql::getInstance();
        $sdb = dbSMysql::getInstance();

        $sdb = dbSMysql::getInstance();
        $detail = $sdb->table('user_insurance_fund_tax', 'a')
            ->field('a.*,c.wname as user_name, c.wdepartment_ids as user_wdepartment_ids, c.wmain_department as user_wmain_department,
             c.wmain_department as user_wmain_department, c.position,b.social_insurance, b.housing_fund, b.id_number,b.corp_id,b.work_place')
            ->where('where a.id = :id AND a.type = :type AND a.is_delete = 0', ['id' => $id, 'type' => $type])
            ->leftJoinOut('db', 'qwuser', 'c', 'c.id=a.qwuser_id')
            ->leftJoinOut('db', 'user_info', 'b', 'b.qwuser_id=a.qwuser_id')
            ->one();

        if (!$detail) returnError('记录不存在');
        $detail['attach'] = json_decode($detail['attach'], true);

        // 获取默认社保公积金缴纳设置
        $default_setting = $sdb->table('config')
            ->where('where 1=1')
            ->whereIn('key_name', ['social_security', 'housing_fund'])
            ->list();
        foreach ($default_setting as &$item) {
            $item['data'] = json_decode($item['data'], true);
        }
        unset($item);
        $default_setting = array_column($default_setting, 'data', 'key_name');
        $social_security = array_column($default_setting['social_security'], 'num', 'work_place');
        $housing_fund = array_column($default_setting['housing_fund'], 'num', 'work_place');

        $department = redisCached::getDepartment();
        $department = array_column($department, 'name', 'wp_id');

        $corp = redisCached::getCorp();
        $corp = array_column($corp, 'name', 'id');

        $detail['social_insurance'] = json_decode($detail['social_insurance'], true);
        $detail['housing_fund'] = json_decode($detail['housing_fund'], true);

        $detail['user_main_department_name'] = $department[$detail['user_wmain_department']] ?? '';
        $detail['corp_name'] = $corp[$detail['corp_id']] ?? '';

        switch ($type) {
            case 1: // 社保
                $detail['social_insurance_diff'] = $detail['attach']['total_corp'] - $social_security[$detail['work_place']];
            case 2: // 公积金
                $detail['housing_fund_diff'] = floatval($detail['attach']['total'] / 2) - $housing_fund[$detail['work_place']];
        }


        return $detail;
    }

    // 过滤字段
    private static function filterData($data, $keys, $filter = [])
    {
        if (empty($data) || empty($keys)) return [];

        // 字段映射
        $user_insurance_fund_tax_title = config::get('user_insurance_fund_tax_title', 'data_salary');
        $keys_map = [];
        foreach ($user_insurance_fund_tax_title as $item) {
            foreach ($item as $value) {
                $keys_map[$value['id']] = $value['name'];
            }
        }
        // 用户状态
        $user_status_map = config::get('user_status', 'data_salary');
        $user_status_map = array_column($user_status_map, 'name', 'id');

        $user_filter = [];
        if (!empty($filter)) {
            foreach ($filter as $item) {
                $user_filter[] = $item['user_id'] . '-' . $item['month'];
            }
        }

        $ret_data = [];

        foreach ($data as $item) {
            $item_data = [];
            // 进行筛选
            if (!empty($user_filter)) {
                if (!in_array($item['user_id'] . '-' . $item['month'], $user_filter)) continue;
            }
            foreach ($keys_map as $key => $value) {
                if (!in_array($key, $keys)) continue;
                // id含. 需要拆分
                if (strpos($key, '.') !== false) {
                    $key_arr = explode('.', $key);
                    if ($key_arr['1'] == 'status') { // 社保、公积金状态
                        $item_data[$value] = empty($item[$key_arr[0]]['total']) ? '未缴纳' : '正常缴纳';
                    } else {
                        $item_data[$value] = $item[$key_arr[0]][$key_arr[1]] ?? '';
                    }
                } else {
                    if ($key == 'user_status') { // 需要转义
                        $item_data[$value] = $user_status_map[$item[$key]] ?? '';
                    } else {
                        $item_data[$value] = $item[$key] ?? '';
                    }
                }
            }
            if (!empty($item_data)) {
                $ret_data[] = $item_data;
            }
        }

        return $ret_data;


    }


}