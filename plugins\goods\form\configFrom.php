<?php
/**
 * @author: zhangguoming
 * @Time: 2024/3/29 13:59
 */

namespace  plugins\goods\form;

use core\lib\db\dbMysql;

class configFrom
{
    public static array $config_data = [];
    public static array $default_config = [
        //给产品取英文名称
        'goods_ename_manage'=>'',
        //产品软件负责人
        'goods_soft_manage'=>'',
        //问题已解决抄送人
        'project_abnormal_copy_user'=>'',
        //软件回厂通知人
        'hard_return_copy_user'=>'',
        //问题未处理通知人
        'abnormal_not_handled_copy_user'=>'',
        //美工图审核人
        'images_check_user'=>'',
        //app图审核通过通知人
        'app_images_check_copy_user'=>'[]',
        //硬件检测异常特批通过通知人
        'hardware_approval_pass'=>'',
        //硬件检测异常特批未通过通知人
        'hardware_approval_not_pass'=>'',
        //硬件检测异常特批负责人
        'hardware_approval_user'=>'',
        //硬件检测异常特批负责人
        'hardware_check_pass'=>'',
        //硬件检测未通过通知人
        'hardware_check_not_pass'=>'',
        //下首单审核人员
        'first_order_check_user'=>'',
        //说明书审核人
        'fs_check_user'=>'',
        //下首单确认通知
        'first_order_copy_user'=>'',
        //说明书运营审核抄送人
        'fs_operate_check_copy_user'=>'',
        //图片负责人
        'pic_manage'=>'[]',
        //说明书预计完成周期
        'fs_expected_day'=>5,
        //专利查看人
        'zhuanli_view_user'=>'[]',
        //质量标准上传人（根据改人盖章流程中上传的pdf）
        'quality_test_user_id'=>145,
        //图片拍摄负责人
        'take_pic_user'=>'',
        //图片拍摄预计完成时间（2*24）小时
        'take_pic_expected_hours'=>'48',
        //图片需求制作预计完成时间（2*24）小时
        'images_expect_hours'=>'2',
    ];

    /**
     * @return void
     * 获取所有信息  该方法不能抛出异常
     */
    public static function getConfigData($keys = []) {
        $db = dbMysql::getInstance();
        $db->table('config');
        if (count($keys)) {
            $db->whereIn('key_name',$keys);
        }
        $list = $db->list();
        $list_array = [];
        foreach ($list as $v) {
            $list_array[$v['key_name']] = $v['data'];
        }
        $default_config = self::$default_config;

        $result = [];
        if (count($keys)) {
            foreach ($keys as $v) {
                if (isset($list_array[$v])) {
                    $result[$v] = $list_array[$v];
                } else {
                    $result[$v] = $default_config[$v];
                }
            }
        } else {
            $result = $default_config;
            foreach ($result as $k=>$v) {
                if (isset($list_array[$k])) {
                    $result[$k] = $list_array[$k];
                }
            }
        }
        return $result;
    }

    /**
     * @param $name  //键-对应key
     * @return mixed
     * @throws \core\lib\ExceptionError
     * 根据键来获取数据
     */
    public static function getConfigByName($name) {
        if (empty(self::$config_data[$name])) {
            $db = dbMysql::getInstance();
            $config_data = $db->query('select data from oa_config where key_name = :key_name',['key_name'=>$name]);
            if (!$config_data) {
                $data_  = self::$default_config[$name];
//                if ($config_data == '') {
//                    SetReturn('-1',$name.'尚未配置，请联系管理员');
//                }
            } else {
                $data_ = $config_data['data'];
            }
            self::$config_data[$name] = $data_;
            return self::$config_data[$name];
        } else {
            return self::$config_data[$name];
        }
    }

    public static function getAbnormalCopyUser(int $abnormal_type, int $is_handled, int $is_return) {
        $key = '';
        if ($abnormal_type == 2) {
            if ($is_handled == 1) {
                $key="project_abnormal_copy_user";
            } else {
                $key="abnormal_not_handled_copy_user";
            }
        }
        if ($abnormal_type == 1) {
            if ($is_handled == 1) {
                $key="project_abnormal_copy_user";
            } else {
                if ($is_return == 1) {
                    $key="hard_return_copy_user";
                } else {
                    $key="abnormal_not_handled_copy_user";
                }
            }
        }
        if ($key == '') {
            SetReturn(-1,'未获取到异常推送的KEY');
        }
        $manage_info = configFrom::getConfigByName($key);
        return $manage_info;
    }
}