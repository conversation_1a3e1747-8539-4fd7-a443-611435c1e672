<?php

namespace core\lib;
/**
 * @author: z<PERSON><PERSON>oming
 * @Time: 2024/8/23 10:58
 */
//系统redis键
class rediskeys
{
    //产品系统
    public static string $goods_yunying_update = 'oa_goods_yunying_update';//产品运营更新
    //财务reids键
    public static string $oa_count_msku_month = 'oa_count_msku_month';//结账后按月统计数据，等级，自定义字段，预警
    public static string $oa_custom_column_count = 'oa_custom_column_count';//自定义字段
    public static string $oa_updateGoodsLeve = 'oa_updateGoodsLeve';//产品等级
    public static string $oa_syn_msku_report = 'oa_syn_msku_report';//msku报告数据同步 (不能多月同时同步）
    public static string $export_msku_import = 'export_msku_import_';//msku报告导入
    public static string $export_goods_stock = 'oa_export_goods_stock_';//库存数据导入(跟月份相关)
    public static string $oa_count_goods_stock = 'oa_count_goods_stock_';//库存数据重新匹配(跟月份相关)
    public static string $export_goods_data = 'oa_export_goods_data_';//增量数据导入(跟月份相关)
    public static string $oa_count_goods_data = 'oa_count_goods_data_';//增量数数据重新匹配(跟月份相关)
    public static string $oa_cost_sharing_import = 'oa_cost_sharing_import';//费用分摊
    public static string $oa_cost_sharing_abolish = 'oa_cost_sharing_abolish';//费用废除
    public static string $import_table_key = 'import_table_key';//报表导出键（所有报表公用一个）
}