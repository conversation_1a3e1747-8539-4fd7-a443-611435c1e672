<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/17 10:08
 */

namespace  plugins\goods\form;

use plugins\goods\models\userModel;
use core\lib\config;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;

class goodsProjectAbnormalFrom
{
    //异常提交时发送消息的数据
    public static array $abnormal_msg_data = [
        'msg'=>'',
        'other_data'=>[],
    ];
    //项目异常(未处理)
    public static array $project_abnormal = [];

    /**
     * @param $goods_id
     * @param $remark
     * @param $title
     * @param $project_id
     * @param $node_index
     * @param $event_index
     * @return void
     * @throws ExceptionError  保存项目异常数据
     */
    public static function addAbnormal($project,$param,$manage_info){
        $node_index = $param['node_index']??'';
        $event_index = $param['event_index']??'';
        $insert_data = [
            'qw_userid'=>userModel::$qwuser_id,
            'goods_id'=>$project['goods_id'],
            'project_id'=>$project['id'],
            'remark'=>$param['remark'],
            'created_at'=>date('Y-m-d H:i:s'),
            'abnormal_type'=>$param['abnormal_type'],
            'level'=>$param['level']??1,
            'images'=>$param['imgs'],
            'txt_id'=>$param['txt_id']??'[]',
            'responsible_person'=>$manage_info
        ];
        $tpl_data = json_decode($project['tpl_data'],true);
        if ($node_index == '') {
            //整个项目提交异常
            $ex_data = [
                'abnormal_name'=>'当前流程',
                'node_index'=>'',
                'event_index'=>'',
                'type'=>1,
                'title'=>$project['matter_name'],
            ];
            //消息数据
            self::$abnormal_msg_data['msg'] = messagesFrom::getMsgTxt(6,$project['matter_name'],'','');
            self::$abnormal_msg_data['other_data'] = [
                'user_id'=>userModel::$qwuser_id,
                'model_id'=>$project['id'],
                'msg_type'=>6
            ];
        } elseif ($event_index == '') {
            //节点
            $current_node = goodsProjectFrom::getCurrentNodeData($tpl_data,$node_index);
            $ex_data = [
                'abnormal_name'=>$current_node['node_name'],
                'node_index'=>$node_index,
                'event_index'=>'',
                'type'=>2,
                'title'=>$project['matter_name'].'的【'.$current_node['node_name'].'】节点',
            ];
            //消息数据
            self::$abnormal_msg_data['msg'] = messagesFrom::getMsgTxt(6,$project['matter_name'],$current_node['node_name'],'');
            self::$abnormal_msg_data['other_data'] = [
                'user_id'=>userModel::$qwuser_id,
                'model_id'=>$project['id'],
                'msg_type'=>6,
                'node_index'=>$node_index,
            ];
        } else {
            //事件
            $current_node = goodsProjectFrom::getCurrentNodeData($tpl_data,$node_index);
            $current_event = goodsProjectFrom::getCurrentEventData($current_node,$event_index);
            $ex_data = [
                'abnormal_name'=>$current_event['event_name'],
                'node_index'=>$node_index,
                'event_index'=>$event_index,
                'type'=>3,
                'title'=>$project['matter_name'].'的【'.$current_node['node_name'].'】节点的【'.$current_event['event_name'].'】事件。',
            ];
            //消息数据
            self::$abnormal_msg_data['msg'] = messagesFrom::getMsgTxt(6,$project['matter_name'],$current_node['node_name'],$current_event['event_name']);
            self::$abnormal_msg_data['other_data'] = [
                'user_id'=>userModel::$qwuser_id,
                'model_id'=>$project['id'],
                'msg_type'=>6,
                'node_index'=>$node_index,
                'event_index'=>$event_index,
            ];
        }
        $insert_data = array_merge($insert_data,$ex_data);
        $db = dbMysql::getInstance();
        $db->table('goods_project_abnormal');
        $id = $db->insert($insert_data);
        return $id;
    }

    /**
     * @param $project_id
     * @param $node_index
     * @param $event_index
     * @return void  获取项目是否有异常
     */
    public static function hasAbnormal(int $project_id,string $node_index,string $event_index) {
        $abnormal_list = self::$project_abnormal;
        if (!count($abnormal_list)) {
            $db = dbMysql::getInstance();
            $db->table('goods_project_abnormal')->where('where is_handled = 0 and project_id=:project_id',['project_id'=>$project_id]);
            $db->field('id,project_id,node_index,event_index');
            $abnormal_list = $db->list();
            self::$project_abnormal = $abnormal_list;
        }
        if (count($abnormal_list) == 0) {
            return false;
        }
        //整个流程
        if (!$node_index) {
            if(count($abnormal_list)) {
                return true;
            }
        }
        //整个节点
        if (!$event_index) {
            foreach ($abnormal_list as $abn) {
                if ($abn['node_index'] == $node_index) {
                    return true;
                }
            }
        }
        //事件
        if ($event_index) {
            foreach ($abnormal_list as $abn) {
                if ($abn['node_index'] == $node_index && $abn['event_index'] == $event_index) {
                    return true;
                }
            }
        }
        return false;
    }

    public static function getAbnormallTxt(array $ids,$abnormal_type){
        $key = $abnormal_type==1?'abnormal_soft_txt':'abnormal_hard_txt';
        $text = [];
        foreach ($ids as $v) {
            $text[] = config::getDataName($key,$v);
        }
        return implode(',',$text);
    }
}