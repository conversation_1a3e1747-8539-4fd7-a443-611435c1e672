<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/3 9:24
 */

namespace financial\form;

use core\lib\db\dbFMysql;
use core\lib\rediskeys;
use financial\models\projectListingModel;

class checkoutForm
{
    //新增月份数据
    public static function addMdate($m_date){
        $db = dbFMysql::getInstance();
        $data = $db->table('checkout')
            ->where('where m_date=:m_date',['m_date'=>$m_date])
            ->field('id,is_lock')
            ->one();
        if (!$data) {
            $db->table('checkout')
                ->insert([
                    'm_date'=>$m_date,
                    'log_data'=>'[]'
                ]);
        } else {
            if ($data['is_lock']) {
                SetReturn(2,'已锁定');
            }
        }
    }

    //结算时   数据操作时一些异步数据验证
    public static function verifyQueue($redis,$m_date){
        //验证Msku数据是否正在同步中
        $redis_key = rediskeys::$oa_syn_msku_report;
        if ($redis->exists($redis_key)) {
            returnError('Msku报表数据正在同步中，请稍后在试~');
        }
        //msku报表数据导入
        //库存数据是否再异步导入
        $redis_key = rediskeys::$export_goods_stock.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有库存数据正在导入，请稍后在试~');
        }
        //库存数据重新计算
        $redis_key = rediskeys::$oa_count_goods_stock.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有库存数据正重新匹配，请稍后在试~');
        }
        //增量数据是否再异步导入
        $redis_key = rediskeys::$export_goods_data.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有增量数据正在导入，请稍后在试~');
        }
        //增量数据重新计算
        $redis_key = rediskeys::$oa_count_goods_data.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有增量数据正重新匹配，请稍后在试~');
        }
        //费用分摊
        $redis_key = rediskeys::$oa_cost_sharing_import;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有增量数据正重新匹配，请稍后在试~');
        }
        //费用废除
        $redis_key = rediskeys::$oa_cost_sharing_abolish;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有增量数据正重新匹配，请稍后在试~');
        }
        //报表数据到入
        $redis_key = rediskeys::$export_msku_import.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有Msku报表数据正在导入，请稍后在试~');
        }
    }

    //库存数据导入时
    public static function verifyGoodsStock($redis,$m_date){
//        self::verifyListing($m_date);
        //验证Msku数据是否正在同步中
        $redis_key = rediskeys::$oa_syn_msku_report;
        if ($redis->exists($redis_key)) {
            returnError('Msku报表数据正在同步中，请稍后在试~');
        }
        //报表数据到入
        $redis_key = rediskeys::$export_msku_import.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有Msku报表数据正在导入，请稍后在试~');
        }
        //库存数据是否再异步导入
        $redis_key = rediskeys::$export_goods_stock.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有库存数据正在导入，请稍后在试~');
        }
        //库存数据重新计算
        $redis_key = rediskeys::$oa_count_goods_stock.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有库存数据正重新匹配，请稍后在试~');
        }
        //费用分摊
        $redis_key = rediskeys::$oa_cost_sharing_import;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有费用正在分摊，请稍后在试~');
        }
        //费用废除
        $redis_key = rediskeys::$oa_cost_sharing_abolish;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有费用正在废除，请稍后在试~');
        }
    }
    //增量数据导入时
    public static function verifyGoodsData($redis,$m_date){
//        self::verifyListing($m_date);
        //验证Msku数据是否正在同步中
        $redis_key = rediskeys::$oa_syn_msku_report;
        if ($redis->exists($redis_key)) {
            returnError('Msku报表数据正在同步中，请稍后在试~');
        }
        //报表数据到入
        $redis_key = rediskeys::$export_msku_import.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有Msku报表数据正在导入，请稍后在试~');
        }
        //增量数据是否再异步导入
        $redis_key = rediskeys::$export_goods_data.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有增量数据正在导入，请稍后在试~');
        }
        //增量数据重新计算
        $redis_key = rediskeys::$oa_count_goods_data.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有增量数据正重新匹配，请稍后在试~');
        }
        //费用分摊
        $redis_key = rediskeys::$oa_cost_sharing_import;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有费用正在分摊，请稍后在试~');
        }
        //费用废除
        $redis_key = rediskeys::$oa_cost_sharing_abolish;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有费用正在废除，请稍后在试~');
        }
    }
    //费用分摊时 或废除时
    public static function verifyCostSharing($redis,$m_date){
        //验证Msku数据是否正在同步中
        $redis_key = rediskeys::$oa_syn_msku_report;
        if ($redis->exists($redis_key)) {
            returnError('Msku报表数据正在同步中，请稍后在试~');
        }
        //报表数据到入
        $redis_key = rediskeys::$export_msku_import.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有Msku报表数据正在导入，请稍后在试~');
        }
        //增量数据是否再异步导入
        $redis_key = rediskeys::$export_goods_data.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有增量数据正在导入，请稍后在试~');
        }
        //增量数据重新计算
        $redis_key = rediskeys::$oa_count_goods_data.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有增量数据正重新匹配，请稍后在试~');
        }
        //费用分摊
        $redis_key = rediskeys::$oa_cost_sharing_import;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有费用正在分摊，请稍后在试~');
        }
        //费用废除
        $redis_key = rediskeys::$oa_cost_sharing_abolish;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有费用正在废除，请稍后在试~');
        }
    }

    //msku报表数据导入时
    public static function verifyImportMskuData($redis,$m_date){
//        self::verifyListing($m_date);
        //报表数据到入
        $redis_key = rediskeys::$export_msku_import.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有Msku报表数据正在导入，请稍后在试~');
        }
        //费用分摊
        $redis_key = rediskeys::$oa_cost_sharing_import;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有费用正在分摊，请稍后在试~');
        }
    }

    //数据重新同步时验证数据操作
    //结算时   数据操作时一些异步数据验证
    public static function verifySynMskuData($redis,$m_date){
        self::verifyListing($m_date);
        //库存数据是否再异步导入
        $redis_key = rediskeys::$export_goods_stock.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有库存数据正在导入，请稍后在试~');
        }
        //库存数据重新计算
        $redis_key = rediskeys::$oa_count_goods_stock.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有库存数据正重新匹配，请稍后在试~');
        }
        //增量数据是否再异步导入
        $redis_key = rediskeys::$export_goods_data.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有增量数据正在导入，请稍后在试~');
        }
        //增量数据重新计算
        $redis_key = rediskeys::$oa_count_goods_data.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有增量数据正重新匹配，请稍后在试~');
        }
        //费用分摊
        $redis_key = rediskeys::$oa_cost_sharing_import;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有费用正在分摊，请稍后在试~');
        }
        //费用废除
        $redis_key = rediskeys::$oa_cost_sharing_abolish;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有费用正在废除，请稍后在试~');
        }
        //报表数据到入
        $redis_key = rediskeys::$export_msku_import.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有Msku报表数据正在导入，请稍后在试~');
        }
    }
    //删除原始数据和删除到入数据需要验证的队列
    public static function verifyDelMsku($redis,$m_date){
        //验证Msku数据是否正在同步中
        $redis_key = rediskeys::$oa_syn_msku_report;
        if ($redis->exists($redis_key)) {
            returnError('Msku报表数据正在同步中，请稍后在试~');
        }
        //库存数据是否再异步导入
        $redis_key = rediskeys::$export_goods_stock.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有库存数据正在导入，请稍后在试~');
        }
        //库存数据重新计算
        $redis_key = rediskeys::$oa_count_goods_stock.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有库存数据正重新匹配，请稍后在试~');
        }
        //增量数据是否再异步导入
        $redis_key = rediskeys::$export_goods_data.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有增量数据正在导入，请稍后在试~');
        }
        //增量数据重新计算
        $redis_key = rediskeys::$oa_count_goods_data.$m_date;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有增量数据正重新匹配，请稍后在试~');
        }
        //费用分摊
        $redis_key = rediskeys::$oa_cost_sharing_import;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有增量数据正重新匹配，请稍后在试~');
        }
        //费用废除
        $redis_key = rediskeys::$oa_cost_sharing_abolish;
        if ($redis->exists($redis_key)) {
            returnError($m_date.'月有增量数据正重新匹配，请稍后在试~');
        }
    }

    //验证listing是否同步
    public static function verifyListing($m_date) {
        $year = date('Y',strtotime($m_date.'-01'));
        projectListingModel::creatProjectListingTable($year);
        $db = dbFMysql::getInstance();
        $listing = $db->table('project_listing_'.$year)
            ->where('where m_date=:date',['date'=>$m_date])
            ->one();
        if (!$listing) {
            returnError('该月listing数据不存在');
        }
    }










}