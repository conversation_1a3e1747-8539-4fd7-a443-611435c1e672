<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/7 15:22
 */

namespace core\lib;

use admin\form\loginForm;
use admin\models\userModel;

class checkToken
{
    public string $ip;
    public function __construct($_url, $prefix)
    {
        $headerData = getallheaders();
        $SkipToken = array(
            "admin/login/login",
            'admin/login/codeLogin',
            "admin/sysMessage/getMsgDetail",
        );
        $this->ip = GetIP();
        define('USER_TOKEN', $headerData['Authorization']??''); //调试模式设置
        if (!in_array($_url, $SkipToken)) {
            //验证是否带了token
            if (!isset($headerData['Authorization']) || $headerData['Authorization'] == '') {
                SetReturn(1, '请先登录');
            }
            if (!in_array($_url,['admin/login/logOut'])) {
                //验证登录是否过期
                $redis = (new predisV())::$client;
                $key = $prefix.$headerData['Authorization'];
                $data = $redis->hmget($key,['id','name','wid','avatar','is_super','wname','auth','role_type'])??'';
                self::publish_($data['id']);
                if (!$data['id']) {
                    SetReturn(1, '请先登录');
                } else {
                    $user_model = new userModel();
                    $user_model::$wid = $data['wid'];
                    $user_model::$qwuser_id = $data['id'];
                    $user_model::$wname = $data['wname'];
                    $user_model::$is_super = $data['is_super'];
                    $user_model::$avatar = $data['avatar']??'';
                    $user_model::$auth = empty($data['auth'])?'[]':$data['auth'];
                    //登录信息更新和时间缓存时间重置
                    $redis->expire($key,4*60*60);
                    loginForm::updateTokenTime($data['id']);
                }
            }
        }
    }

    public function publish_($user_id) {
        if (SYS_PUBLISH) {
            if (!in_array($user_id,['212','215','205'])) {
                SetReturn(10,'系统升级中');
            }
        }
    }
}