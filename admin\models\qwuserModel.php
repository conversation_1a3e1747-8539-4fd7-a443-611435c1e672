<?php

namespace  admin\models;

use core\lib\db\dbAMysql;
use core\lib\db\dbMysql;

class qwuserModel
{
    // 获取所有企微用户
    public static function getUser($data = []) {
        $paras_list = array('wname', 'qw_partment_id', 'wstatus', 'page', 'page_size', 'order_by', 'roles_id');
        $param = arrangeParam($data, $paras_list);
        //先查出角色绑定的用户id
        $adb = dbAMysql::getInstance();
        $user_ids = [];
        if (is_array($param['roles_id']) && !empty($param['roles_id']) && count($param['roles_id'])) {
            $user_ids = [];
            $user_roles = $adb->table('user_roles')
                ->where('where is_delete = 0')
                ->whereIn('role_id',$param['roles_id'])
                ->field('user_id')
                ->list();
            if (count($user_roles)) {
                $user_ids = array_column($user_roles,'user_id');
            }
        }

        $db = dbMysql::getInstance();
        $db->table('qwuser','a')
            ->field('a.id,a.wid,a.wname,a.wphone,a.wdepartment_ids,a.wstatus,a.updated_at,a.is_manage,a.avatar,a.position')
            ->where('where a.is_delete = 0');

        if (!empty($param['wname'])) {
            $db->andWhere('and a.wname like :wname',['wname'=>'%'.$param['wname'].'%']);
        }

        if (!empty($param['qw_partment_id'])) {
            $db->andWhere('and JSON_CONTAINS(a.wdepartment_ids,:qw_partment_id)',['qw_partment_id'=>json_encode($param['qw_partment_id'],JSON_NUMERIC_CHECK)]);
        }

        if (!empty($param['wstatus'])) {
            $db->andWhere('and a.wstatus = :wstatus',['wstatus'=>$param['wstatus']]);
        }

        if (count($user_ids)) {
            $db->whereIn('id',$user_ids);
        }

        $order_str = '';
        if (!empty($param['order_by'])) {
            $order_by = json_decode($param['order_by'],true);
            $order_str = '';
            foreach ($order_by as $k=>$ord){
                $order_str .= 'a.'.$k.' '.$ord.',';
            }
        }
        if(empty($order_str)) {
            $db->order('a.id desc');
        } else {
            $db->order(trim($order_str,','));
        }
        return $db->list();
    }

    // 获取所有部门下的用户
    public static function getDepartmentUsers()
    {
        $db = dbMysql::getInstance();
        $db->table('qwuser', 'a')
            ->field('a.id,a.wid,a.wname,a.wphone,a.wdepartment_ids,a.wstatus,a.updated_at,a.is_manage,a.avatar,a.position')
            ->where('where a.is_delete = 0');
        $data = $db->list();
        $departments = [];
        foreach ($data as $v) {
            $department_ids = json_decode($v['wdepartment_ids'], true);
            foreach ($department_ids as $department_id) {
                if (!isset($departments[$department_id])) {
                    $departments[$department_id] = [
                        'id' => $department_id,
                        'users' => []
                    ];
                }
                $departments[$department_id]['users'][] = $v['id'];
            }
        }
        return array_values($departments);
    }

}