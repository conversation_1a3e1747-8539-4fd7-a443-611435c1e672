<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/8 14:22
 */

namespace financial\form;

use core\lib\db\dbFMysql;
use financial\models\boardModel;
use financial\models\userModel;

class boardForm extends boardModel //自定义字段，非百分比
{
    public string $field;
    public int $custom_id;
    public static int $show_type;
    public function __construct($custom_id)
    {
        parent::__construct();
        $this->custom_id = $custom_id;
        $db = dbFMysql::getInstance();
        $column = $db->table('column')
            ->where('where custom_id = :custom_id',['custom_id'=>$this->custom_id])
            ->one();
        if (!$column) {
            returnError('没有该自定义列');
        }
        self::$show_type = $column['show_type'];
    }

    //月数据(自定义字段，跟前金额无关,跟利率无关)
    public function totalCustom() {
        $res_list = [];
        $db = dbFMysql::getInstance();
        foreach (self::$years as $year=>$m_date_list) {
            //获取本年份的值
            $dbo = boardModel::getSqlWhereForOa($db,$year,$m_date_list);
            foreach ($m_date_list as $month) {
                $dbos = $dbo;
                $list = $dbos->andWhere('custom_id = :custom_id and m_date = :month',['custom_id'=>$this->custom_id,'month'=>$month])
                    ->field('m_date,sum(custom_val) as total')
                    ->one();
                $res_list[] = $list;
            }
        }
        if (count($res_list) && $this->custom_id == 4) {
            if (count(self::$aggregation_keys)) {
                $fields_str = implode('+',self::$aggregation_keys);
                $fields = "reportDateMonth as m_date,sum($fields_str) as total";
                $group_by = ['reportDateMonth'];
                $lx_list = [];
                foreach (self::$years as $year=>$m_date_list) {
                    //领星数据
                    $list = boardModel::getSqlWhereList($db,$year,$m_date_list,$group_by,$fields,[]);
                    $lx_list = array_merge($lx_list,$list);
                }
                if (count($lx_list)) {
                    foreach ($res_list as $k=>$v) {
                        foreach ($lx_list as $k1=>$v1) {
                            if ($v1['m_date'] == $v['m_date']) {
                                $res_list[$k]['total'] += $v1['total'];
                                unset($lx_list[$k1]);
                            }
                        }
                    }
                }
            }
        }
        //按月整理数据
        $data = self::getMlist($res_list);
        if (self::$show_type == 1) {
            //按月汇率处理数据
            $data = self::getPriceByRoute1($data,self::$search_month);
        }
        return array_values($data);
    }
    //环比和同步数据(自定义字段,跟利率无关)
    public function  getQoqAndYoy($this_list) {
        $data = [
            'total'=>(string)array_sum(array_column($this_list,'total')),
            'qoq'=>$this->totalCustomQoq($this_list),
            'yoy'=>$this->totalCustomYoy($this_list),
        ];
        return $data;
    }
    //环比计算(自定义字段，跟利率无关)
    private function totalCustomQoq($this_list) {
        //查询上个时间同步时间段数据
        $res_list = [];
        $db = dbFMysql::getInstance();
        foreach (self::$qoq_years as $year=>$m_date_list) {
            //获取本年份的值
            $dbo = boardModel::getSqlWhereForOa($db,$year,$m_date_list);
            foreach ($m_date_list as $month) {
                $dbos = $dbo;
                $list = $dbos->andWhere('custom_id = :custom_id and m_date = :month',['custom_id'=>$this->custom_id,'month'=>$month])
                    ->field('m_date,sum(custom_val) as total')
                    ->one();
                $res_list[] = $list;
            }
        }
        //毛利润相关数据
        if (count($res_list) && $this->custom_id == 4) {
            if (count(self::$aggregation_keys)) {
                $fields_str = implode('+',self::$aggregation_keys);
                $fields = "reportDateMonth as m_date,sum($fields_str) as total";
                $group_by = ['reportDateMonth'];
                $lx_list = [];
                foreach (self::$qoq_years as $year=>$m_date_list) {
                    //领星数据
                    $list = boardModel::getSqlWhereList($db,$year,$m_date_list,$group_by,$fields,[]);
                    $lx_list = array_merge($lx_list,$list);
                }
                $lx_list = array_column($lx_list,'total','m_date');
                foreach ($res_list as $k=>$v) {
                    if (isset($lx_list[$v['m_date']])) {
                        $res_list[$k]['total'] += $lx_list[$v['m_date']];
                    }
                }
            }
        }
        if (self::$show_type) {
            $res_list = self::getMPricelist(self::$qoq_month,$res_list);
        }
        $rate = '-';
        $qoq_total = roundToString(array_sum(array_column($res_list,'total')));
        if (count($res_list)) {
            $this_q = array_sum(array_column($this_list,'total'));
            if ($qoq_total > 0) {
                $rate = roundToString(($this_q*100 - $qoq_total*100)/$qoq_total);
            }
        }
        return [
            'mouth'=>self::$qoq_month[0].'~'.end(self::$qoq_month),
            'total'=>$qoq_total,
            'rate'=>$rate
        ];
    }
    //同比计算(自定义字段，跟利率无关)
    public function totalCustomYoy($this_list) {
        //查询上个时间同步时间段数据
        $res_list = [];
        $db = dbFMysql::getInstance();
        foreach (self::$yoy_years as $year=>$m_date_list) {
            //获取本年份的值
            $dbo = boardModel::getSqlWhereForOa($db,$year,$m_date_list);
            $list = $dbo->andWhere('custom_id = :custom_id',['custom_id'=>$this->custom_id])
                ->field('m_date,sum(custom_val) as total')
                ->groupBy(['m_date'])
                ->list();
            $res_list = array_merge($res_list,$list);
        }
        //毛利润相关数据
        if (count($res_list) && $this->custom_id == 4) {
            if (count(self::$aggregation_keys)) {
                $fields_str = implode('+',self::$aggregation_keys);
                $fields = "reportDateMonth as m_date,sum($fields_str) as total";
                $group_by = ['reportDateMonth'];
                $lx_list = [];
                foreach (self::$yoy_years as $year=>$m_date_list) {
                    //领星数据
                    $list = boardModel::getSqlWhereList($db,$year,$m_date_list,$group_by,$fields,[]);
                    $lx_list = array_merge($lx_list,$list);
                }
                $lx_list = array_column($lx_list,'total','m_date');
                if (count($lx_list)) {
                    foreach ($res_list as $k=>$v) {
                        if (isset($lx_list[$v['m_date']])) {
                            $res_list[$k]['total'] += $lx_list[$v['m_date']];
                        }
                    }
                }
            }
        }

        if (self::$show_type) {
            $res_list = self::getMPricelist(self::$yoy_month,$res_list);
        }
        $rate = '-';
        $yoy_total = roundToString(array_sum(array_column($res_list,'total')));
        if (count($res_list)) {
            $this_q = array_sum(array_column($this_list,'total'));
            if ($yoy_total > 0) {
                $rate = roundToString(($this_q*100 - $yoy_total*100)/$yoy_total);
            }
        }
        return [
            'mouth'=>self::$yoy_month[0].'~'.end(self::$yoy_month),
            'total'=>$yoy_total,
            'rate'=>$rate
        ];
    }
    //top10
    public function topTen() {
        $res_list = [];
        $db = dbFMysql::getInstance();
        foreach (self::$years as $year=>$m_date_list) {
            $dbo = boardModel::getSqlWhereForOa($db,$year,$m_date_list);
            $list = $dbo->whereIn('custom_id',[1,3])
                ->field('m_date,project_id,custom_id,sum(custom_val) as total')
                ->groupBy(['m_date,project_id,custom_id'])
                ->list();
            $res_list = array_merge($res_list,$list);
        }
        if (count($res_list)) {
            $res_list = self::getTop10List($res_list);
        }
        return $res_list;
    }
    //成本费用
    public function costPrice() {
        $res_list = [];
        $db = dbFMysql::getInstance();
        //成本(费用)采购8、头程9、尾程10、推广费44、退款12、佣金13、其他72
        //其他有毛利润计算使用
        foreach (self::$years as $year=>$m_date_list) {
            $dbo = boardModel::getSqlWhereForOa($db,$year,$m_date_list);
            $list = $dbo->whereIn('custom_id',[8,9,10,44,12,13,72])
                ->field('m_date,custom_id,sum(custom_val) as total')
                ->groupBy(['m_date,custom_id'])
                ->list();
            $res_list = array_merge($res_list,$list);
        }
        $lx_data = [];
        if (count(self::$aggregation_keys)) {
            $string_ = 'sum('.implode('+',self::$aggregation_keys).') as aggregation_val';
            foreach (self::$years as $year=>$m_date_list) {
                //获取本年份的值
                $fields = 'reportDateMonth as m_date,'.$string_;
                $group_by = ['reportDateMonth'];
                $list = boardModel::getSqlWhereList($db,$year,$m_date_list,$group_by,$fields,[]);
                $lx_data = array_merge($lx_data,$list);
            }
            $lx_data_ = array_column($lx_data,'aggregation_val','m_date');
            foreach ($res_list as $k=>$v) {
                if ($v['custom_id'] == 72) {
                    if (isset($lx_data_[$v['m_date']])) {
                        $res_list[$k]['total'] += $lx_data_[$v['m_date']];
                    }
                }
            }
        }
        $res_list = self::getCostPriceList($res_list);
        $all_total = 0;
        $array =  ['procure'=>'采购','toucheng'=>'头程','weicheng'=>'尾程','promotion_fee'=>'推广费','refund'=>'退款','commission'=>'佣金','other'=>'其他'];
        $rate_list = [];
        foreach ($res_list as $k=>$v) {
            foreach ($v as $key=>$val) {
                if (isset($array[$key])) {
                    $all_total += $val;
                    $res_list[$k][$key] = roundToString($val);
                    if (!isset($rate_list[$key])) {
                        $rate_list[$key] = [
                            'name'=>$array[$key],
                            'total'=>$val,
                            'rate'=>0
                        ];
                    } else {
                        $rate_list[$key]['total'] += $val;
                    }

                }
            }
        }
        //占比计算
        $rate_all = 0;
        usort($rate_list, fn($a, $b) => $a['total'] <=> $b['total']);//防止total为0，确有百分比
        $i = 0;
        foreach ($array as $k=>$v) {
            $i++;
            $total = array_sum(array_column($res_list,$k));
            if ($all_total != 0) {
                $rate = round($total*100/$all_total,2);
                if ($i == 6) {
                    $rate = (10000 - $rate_all*100)/100;
                }
            } else {
                $rate = 0;
            }
            $rate_all += $rate;
            foreach ($rate_list as $k1=>$v1) {
                if ($v1['name'] == $v) {
                    $rate_list[$k1]['rate'] = roundToString($rate);
                    break;
                }
            }
        }
        $res_list = array_values($res_list);

        return ['list'=>$res_list,'rate_data'=>$rate_list];
    }

    //整理top10数据
    public static function getTop10List($res_list) {
        //销售额与毛利润
        $data_list = array_filter($res_list, function ($person) {
            return $person['custom_id'] == 3;
        });
        //销量
        $rate_list = array_filter($res_list, function ($person) {
            return $person['custom_id'] == 1;
        });
        //汇率转换
        $data_list = self::getPriceByRoute1($data_list,self::$search_month);

        $res_list = array_merge($data_list,$rate_list);
        //项目名称及组长名称获取
        $db = dbFMysql::getInstance();
        $project_ids = array_unique(array_column($res_list,'project_id'));
        $project_list = $db->table('project','a')
            ->leftJoinOut('db','qwuser','b','b.id=a.user_id')
            ->whereIn('a.id',$project_ids)
            ->field('a.id,a.project_name,a.user_id,b.wname,a.p_id')
            ->list();
        $project_ = [];
        $pids = [];
        foreach ($project_list as $v) {
            $project_[$v['id']] = $v;
            $pids[] = $v['p_id'];
        }
        $project_list2 = $db->table('project')
            ->whereIn('id',$pids)
            ->field('id,project_name')
            ->list();
        $project_2 = array_column($project_list2,'project_name','id');
        $new_list = [];
        foreach ($res_list as $v) {
            $key_ = $v['project_id'];
            $project_item = $project_[$key_]??'';
            $project_name_ = '';
            if ($project_item) {
                $project_name_ = $project_item['project_name']??'';
                $project_name_ .= isset($project_2[$project_item['p_id']])?'('.$project_2[$project_item['p_id']].')':'';
            }
            if (!isset($new_list[$key_])) {
                $new_list[$key_] = [
                    'project_id' => $v['project_id'],
                    'project_name' => $project_name_,
                    'user_name' => $project_[$key_]['wname']??'',
                    'gross_proft' => 0,
                    'sales_quantity' => 0,
                    'sales_amount' => 0,
                ];
            }
            //销量
            if ($v['custom_id'] == 1) {
                $new_list[$key_]['sales_quantity'] = roundToString($new_list[$key_]['sales_quantity'] + $v['total']);
            }
            //销售额
            if ($v['custom_id'] == 3) {
                $new_list[$key_]['sales_amount'] = roundToString($new_list[$key_]['sales_amount'] + $v['total']);
            }
        }
        return $new_list;
    }
    //整理成本费用数据
    public static function getCostPriceList($res_list) {
        //汇率转换
        $res_list = self::getPriceByRoute1($res_list,self::$search_month);
        $new_list = [];
        foreach (self::$search_month as $month) {
            if (!isset($new_list[$month])) {
                $new_list[$month] = [
                    'm_date' => $month,
                    'procure' => 0,
                    'toucheng' => 0,
                    'weicheng' => 0,
                    'promotion_fee' => 0,
                    'refund' => 0,
                    'commission' => 0,
                    'other' => 0,
                ];
            }
        }
        //成本(费用)采购8、头程9、尾程10、推广费44、退款12、佣金13、其他42
        foreach ($res_list as $v) {
            $key_ = $v['m_date'];
            if ($v['custom_id'] == 8) {
                $new_list[$key_]['procure'] += $v['total'];
            }
            if ($v['custom_id'] == 9) {
                $new_list[$key_]['toucheng'] += $v['total'];
            }
            if ($v['custom_id'] == 10) {
                $new_list[$key_]['weicheng'] += $v['total'];
            }
            if ($v['custom_id'] == 44) {
                $new_list[$key_]['promotion_fee'] += $v['total'];
            }
            if ($v['custom_id'] == 12) {
                $new_list[$key_]['refund'] += $v['total'];
            }
            if ($v['custom_id'] == 13) {
                $new_list[$key_]['commission'] += $v['total'];
            }
            if ($v['custom_id'] == 72) {
                $new_list[$key_]['other'] += $v['total'];
            }
        }
        return $new_list;
    }


    //整理数据(跟金额无关, 没根据规则计算，直接查表的数，月汇总)
    private static function getMlist($res_list) {
        $new_list = [];
        foreach (self::$search_month as $k=>$month) {
            if (!isset($new_list[$month])) {
                $new_list[$month] = [
                    'm_date'=>$month,
                    'total'=>'0.00',
                ];
            }
            foreach ($res_list as $item) {
                if ($month == $item['m_date']) {
                    $new_list[$month]['total'] = roundToString($item['total']+$new_list[$month]['total']);
                }
            }
        }
        return $new_list;
    }














}