<?php

namespace plugins\salary\models;

use admin\models\qwuserModel;
use core\lib\db\dbMysql;

class salarySchemeModel
{

    // 检验
    public static function checkScheme($scheme)
    {
        $attach = json_decode($scheme['attach'], true) ?: [];
        $config = json_decode($scheme['config'], true) ?: [];
        empty($attach['apply_range_type']) && returnError('请设置算薪对象类型');
        if (in_array('1', $attach['apply_range_type'])) {
            if (empty($attach['apply_range']['users']) && empty($attach['apply_range']['roles']) && empty($attach['apply_range']['departments'])) {
                returnError('算薪对象不能为空');
            }
        }
        if (isset($attach['no_need_type']) && in_array('1', $attach['no_need_type'])) { // 指定人员
            empty($attach['no_need_users']) && returnError('无需算薪对象列表不能为空');
        }
        empty($config) && returnError('请设置薪资项配置');
        foreach ($config as $item) {
            empty($item['module']) && returnError('薪资项模块不能为空');
            !isset($item['list']) && returnError('薪资项模块列表不能为空');
        }
    }

    // 获取方案中的用户
    public static function getSchemeUsers($attach, $users = [], $user_roles = [], $department_users = [])
    {
        if (empty($users)) {
            $db = dbMysql::getInstance();

            // 用户基本信息
            $users = $db->table('qwuser', 'u')
                ->field('u.id as user_id,u.wid, u.wname, ui.user_status')
                ->leftJoin('user_info', 'ui', 'u.id=ui.qwuser_id')
                ->list();
            $users = array_column($users, null, 'user_id');
        }
        if (empty($user_roles)) { // 获取所有角色及其用户
            $user_roles = userRolesModel::getRolesAndUsers();
        }
        if (empty($department_users)) { // 获取所有部门及其用户
            $department_users = qwuserModel::getDepartmentUsers();
        }
        $user_ids = [];
        $apply_range_type = $attach['apply_range_type'];
        if (in_array('1', $apply_range_type)) {
            $apply_range = $attach['apply_range'];
            $user_role_map = array_column($user_roles, null, 'id');
            $department_users_map = array_column($department_users, null, 'id');

            // 包含三个部分users、roles、departments，然后去除不需要考核的人员
            if (!empty($apply_range['users'])) {
                $range_users = $apply_range['users'];
                $user_ids = array_merge($user_ids, $range_users);
            }
            if (!empty($apply_range['roles'])) {
                $roles = $apply_range['roles'];
                foreach ($roles as $role) {
                    $role_users = $user_role_map[$role]['users'];
                    $user_ids = array_merge($user_ids, $role_users);
                }
            }
            if (!empty($apply_range['departments'])) {
                $departments = $apply_range['departments'];
                foreach ($departments as $department) {
                    if (empty($department_users_map[$department])) {
                        continue;
                    }
                    $user_ids = array_merge($user_ids, $department_users_map[$department]['users']);
                }
            }
        }

        foreach ($users as $u) {
            if (in_array('2', $apply_range_type) && $u['user_status'] == 2) {
                // 试用期
                $user_ids[] = $u['user_id'];
            }
            if (in_array('3', $apply_range_type) && $u['user_status'] == 1) {
                // 正式员工
                $user_ids[] = $u['user_id'];
            }
        }

        // 去除方案中不需要算薪的人员
        if (!empty($attach['no_need_type']) && in_array('1', $attach['no_need_type'])) {
            $no_need_users = $attach['no_need_users'];
            $user_ids = array_diff($user_ids, $no_need_users);
        }
        $user_ids = array_values(array_unique($user_ids));
        $after_user_ids = [];

        foreach ($user_ids as $user_id) {
            $u = $users[$user_id];
            // 试用期
            if (!empty($attach['no_need_type']) && in_array('2', $attach['no_need_type']) && $u['user_status'] == 2) {
                continue;
            }
            // 正式员工
            if (!empty($attach['no_need_type']) && in_array('3', $attach['no_need_type']) && $u['user_status'] == 1) {
                continue;
            }
            $after_user_ids[$user_id] = [
                'user_id'     => $user_id,
                'wid'         => $u['wid'],
                'wname'       => $u['wname'],
                'user_status' => $u['user_status']
            ];
        }

        return $after_user_ids;
    }

}