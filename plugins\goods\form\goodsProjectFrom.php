<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/20 9:22
 */

namespace  plugins\goods\form;

//项目的表单提交
use plugins\goods\common\authenticationCommon;
use plugins\goods\models\goodsProjectAbnormalModel;
use plugins\goods\models\goodsAttrModel;
use plugins\goods\models\userModel;
use core\lib\config;
use core\lib\db\dbMysql;
use plugins\goods\models\formGoodsAttrModel;
use core\lib\ExceptionError;
use core\lib\log;
use task\controller\qwSendMessageController;
use function Symfony\Component\Translation\t;

class goodsProjectFrom
{
    public static array $check_node_event_type = [];//审核节点时节点中包含的事件类型
    private static string $table_name;
    public static int $jump_next_node = 0;

    public static array $project_files = [];//项目详情查询时，获取的上传文件

    public static array $hardware_test_list = [];//硬件提交列表

    public static array $hardware_list = [
        'hardware_ng_list'=>[],     //ng数据
        'hardware_approval'=>false,  //审批数据
    ];// 项目详情查询时，获取的硬件检测数据


    /**
     * @param array $project_data
     * @return array
     * @throws ExceptionError  修改当前节点为系一个节点，完成当前节点 （抽货样收样）
     */
    public static function setGoodsProjectForReceiving(array $project_data,string $current_node_index,string $current_event_index) {
        $tpl_data = json_decode($project_data['tpl_data'],true);
        $current_node = self::getCurrentNodeData($tpl_data,$current_node_index);
        $current_event = self::getCurrentEventData($current_node,$current_event_index);
        authenticationCommon::verifyEventAuth($current_event, 8);
        //设置当前事件已完成
        $tpl_data = self::setEventStatus($tpl_data,$current_node_index,$current_event_index,2);
        //设置当前节点为已完成
        $tpl_data = self::setNodeStatus($tpl_data,$current_node_index,2,0,0);
        //获取下个节点简要信息
        $next_node = self::getNextNodeInfo($tpl_data,$project_data['current_index']);
        $update_data = [
            'updated_time'=>date('Y-m-d H:i:s'),
        ];
        if ($next_node) {
            //设置下个节点开始
            $update_data['current_index'] = $project_data['current_index']+1;
            $tpl_data = self::setNodeBegintime($tpl_data,$update_data['current_index']);
            $update_data['current_node_info'] = json_encode($next_node);
        } else {
            $update_data['status'] = 1;
        }
        $update_data['tpl_data'] = json_encode($tpl_data,JSON_UNESCAPED_UNICODE);
        $db = dbMysql::getInstance();
        $db->table('goods_project');
        $db->where('where id=:id',['id'=>$project_data['id']]);
        $db->update($update_data);
        return ['project_id'=>$project_data['id'],'next_node'=>$tpl_data[$update_data['current_index']],'next_node_index'=>$update_data['current_index']];
    }

    /**
     * @param int $goods_id
     * @param array $template_data 模板信息
     * @return array
     * @throws ExceptionError   新增大货样模板 （大货样收样）
     */
    public static function addGoodsProjectFor1(int $goods_id,string $matter_name,array $template_data,int $sample_batch,int $batch_num) {
        $tpl_data = json_decode($template_data['tpl_data'],true);
        $current_node = self::getCurrentNodeData($tpl_data,'0-0');
        $current_event = self::getCurrentEventData($current_node,'0-0');
        if (count($current_node['event_detail']) > 1) {
            returnError('模板有误，请联系管理员。首个节点不可包含多个事件。');
        }
        //收样权限验证
        authenticationCommon::verifyEventAuth($current_event, 8);
        //设置当前事件已完成
        $tpl_data = self::setEventStatus($tpl_data,'0-0','0-0',2);
        //设置当前节点为已完成
        $tpl_data = self::setNodeStatus($tpl_data,'0-0',2,0,0);

        //获取下个节点简要信息
        $next_node = self::getNextNodeInfo($tpl_data,0);
        $insert_data = [
            'user_id'=>userModel::$qwuser_id,
            'flow_path_id'=>$template_data['flow_path_id'],
            'goods_id'=>$goods_id,
            'tpl_name'=>$template_data['tpl_name'],
            'tpl_id'=>$template_data['id'],
            'expected_day'=>$template_data['expected_day'],
            'sample_batch'=>$sample_batch,
            'created_time'=>time(),
            'matter_name'=>$matter_name,
            'batch_num'=>$batch_num
        ];
        if ($next_node) {
            //设置下一个节点和事件的开始时间
            $tpl_data = self::setNodeBegintime($tpl_data,1);
            $insert_data['current_index'] = 1;
            $insert_data['current_node_info'] = json_encode($next_node);
        } else {
            $insert_data['current_index'] = 0;
            $insert_data['current_node_info'] = '[]';
            $insert_data['status'] = 1;
        }
        $insert_data['tpl_data'] = json_encode($tpl_data,JSON_UNESCAPED_UNICODE);
        $db = dbMysql::getInstance();
        $db->table('goods_project');
        $project_id = $db->insert($insert_data);
        //参与者添加
        goodsProjectParticipantFrom::setParticipant($tpl_data,$goods_id,$project_id);
        return ['project_id'=>$project_id,'next_node'=>$tpl_data[$insert_data['current_index']],'next_node_index'=>$insert_data['current_index']];
    }

    /**
     * @param int $goods_id
     * @param string $matter_name
     * @param array $template_data
     * @param int $sample_batch
     * @return array
     * @throws ExceptionError  新建出货样或者抽货样流程
     */
    public static function addGoodsProjectFor2or3(int $goods_id,string $matter_name,array $template_data,int $sample_batch, int $batch_num) {
        $tpl_data = json_decode($template_data['tpl_data'],true);
        $current_node = self::getCurrentNodeData($tpl_data,'0-0');
        $current_event = self::getCurrentEventData($current_node,'0-0');
        if (count($current_node['event_detail']) > 1) {
            returnError('模板有误，请联系管理员。首个节点不可包含多个事件。');
        }
        authenticationCommon::verifyEventAuth($current_event, 9);
        //设置当前事件已完成
        $tpl_data = self::setEventStatus($tpl_data,'0-0','0-0',2);
        //设置当前节点为已完成
        $tpl_data = self::setNodeStatus($tpl_data,'0-0',2,0,0);
        //获取下个节点简要信息
        $next_node = self::getNextNodeInfo($tpl_data,0);
        $insert_data = [
            'user_id'=>userModel::$qwuser_id,
            'flow_path_id'=>$template_data['flow_path_id'],
            'goods_id'=>$goods_id,
            'tpl_name'=>$template_data['tpl_name'],
            'tpl_id'=>$template_data['id'],
            'expected_day'=>$template_data['expected_day'],
            'sample_batch'=>$sample_batch,
            'created_time'=>time(),
            'matter_name'=>$matter_name,
            'batch_num'=>$batch_num
        ];
        if ($next_node) {
            //设置下一个节点和事件的开始时间
            $tpl_data = self::setNodeBegintime($tpl_data,1);
            $insert_data['current_index'] = 1;
            $insert_data['current_node_info'] = json_encode($next_node);
        } else {
            $insert_data['status'] = 1;
        }
        $insert_data['tpl_data'] = json_encode($tpl_data,JSON_UNESCAPED_UNICODE);
        $db = dbMysql::getInstance();
        $db->table('goods_project');
        $project_id = $db->insert($insert_data);
        //参与者添加
        goodsProjectParticipantFrom::setParticipant($tpl_data,$goods_id,$project_id);
        return ['project_id'=>$project_id,'next_node'=>$tpl_data[$insert_data['current_index']],'next_node_index'=>$insert_data['current_index']];
    }


    //设置当前事件已完成
    public static function setEventStatus(array $tpl_data,string $current_node_index,string $current_event_index, int $status, int $project_id=0) {
        $node_array = explode('-',$current_node_index);
        $event_array = explode('-',$current_event_index);
        $current_node = $tpl_data[$node_array[0]][$node_array[1]];
        $current_event = $current_node['event_detail'][$event_array[0]][$event_array[1]];
        //异常验证
        if (empty($current_event['begin_time'])) {
            $current_event['begin_time'] = date('Y-m-d H:i:s');
        }
        if ($status == 2) {
            //已完成
            $current_event['status'] = 2;
            $current_event['complete_time'] = date('Y-m-d H:i:s');
        }
        //设置审核人
        if ($current_event['event_type'] == 6 || $current_event['need_check'] == 1) {
            $current_event = self::setEventChecker($current_event);
        }
//        //如果是产品硬件检测
//        if ($current_event['event_type'] == 4) {
//            //审核信息
//            $current_event['need_check'] = 1;
//            $current_event['check_user_info'] = $current_node['manage_info'];
//            $current_event['check_info'] = [
//                'is_pass'=>0
//            ];
//        }
        $tpl_data[$node_array[0]][$node_array[1]]['event_detail'][$event_array[0]][$event_array[1]] = $current_event;
        return $tpl_data;
    }
    //设置当前节点已完成
    public static function setNodeStatus(array $tpl_data,string $current_node_index, int $status,int $project_id=0,int $need_auth = 1) {
        $node_array = explode('-',$current_node_index);
        $current_node = $tpl_data[$node_array[0]][$node_array[1]];
        //验证处理权限
        if ($need_auth == 1) {
            self::verifyNodeAuth($current_node,$status);
        }
        if (empty($current_node['begin_time'])) {
            $current_node['begin_time'] = date('Y-m-d H:i:s');
        }
        if ($status == 2) {
            //已完成
            $current_node['status'] = 2;
            $current_node['complete_time'] = date('Y-m-d H:i:s');
            if ($project_id > 0) {
                $count = goodsProjectAbnormalModel::getAbnormalCount(2, $project_id, $current_node_index);
                if ($count > 0) {
                    SetReturn(-1,'该节点内有异常还未处理完');
                }
            }
        }
        $tpl_data[$node_array[0]][$node_array[1]] = $current_node;
        return $tpl_data;
    }
    //判断当前节点集是否全部完成  TODO 调用必须放在$tpl_data跟新后
    public static function getNodeListStatus($node_list) {
        foreach ($node_list as $v) {
            if ($v['status'] != 2) {
                return 0;
            }
        }
        return 1;
    }

    //设置下一个节点和事件的开始时间
    public static function setNodeBegintime(array $tpl_data,int $next_node_index) {
        $node_array = $tpl_data[$next_node_index];
        foreach ($node_array as &$node) {
            $node['begin_time'] = date('Y-m-d H:i:s');
            $node['status'] = 1;
            foreach ($node['event_detail'] as &$event_list) {
                foreach ($event_list as &$event) {
                    $event['begin_time'] = date('Y-m-d H:i:s');
                    $event['status'] = 1;
                }
            }
        }
        $tpl_data[$next_node_index] = $node_array;
        return $tpl_data;
    }




    /**
     * @param $node_data 节点数据
     * @return void 验证节点下的事件是都否完成+操作权限验证
     */
    public static function verifyNodeAuth($node_data, $status) {
        //验证负责人
        $manage_info = json_decode($node_data['manage_info'],true);
        authenticationCommon::projectManageVatify($manage_info);
        //验证事件完成请况
        if ($status == 1) {
            foreach ($node_data['event_detail'] as $v1) {
                foreach ($v1 as $v2) {
                    if ($v2['status'] != 1) {
                        SetReturn(-1,'该节点内还有事件未完成，请先完成所有事件提交');
                    }
                }
            }
        }
    }

    /**
     * @param array $tpl_data //模板数据
     * @param string $current_node_index 当前节点
     * @return array 获取下一个节点信息 （节点集）
     */
    public static function getNextNodeInfo(array $tpl_data,int $current_node_index) {
        $next_node_index_p = $current_node_index+1;
        if (isset($tpl_data[$next_node_index_p])) {
            $next_node = $tpl_data[$next_node_index_p];
            $next_node_data = [];
            foreach ($next_node as $v) {
                $item = [
                    'begin_time'=>time(),
                    'node_name'=>$v['node_name'],
                    'manage_info'=>$v['manage_info']
                ];
                $next_node_data[] = $item;
            }
            return $next_node_data;
        } else {
            return false;
        }
    }


    /**
     * @param array $node_data
     * @param string $current_event_index
     * @return mixed 获取当前事件
     */
    public static function getCurrentEventData(array $node_data,string $current_event_index){
        $event_index_array = explode('-',$current_event_index);
        if (empty($node_data['event_detail'][$event_index_array[0]][$event_index_array[1]])) {
            SetReturn(-1,'事件不存在');
        }
        return $node_data['event_detail'][$event_index_array[0]][$event_index_array[1]];
    }
    /**
     * @param array $tpl_data
     * @param string $current_node_index
     * @return mixed 获取当前节点的数据
     */
    public static function getCurrentNodeData(array $tpl_data,string $current_node_index){
        $node_index_array = explode('-',$current_node_index);
        if (empty($tpl_data[$node_index_array[0]][$node_index_array[1]])) {
            SetReturn(-1,'节点不存在');
        }
        return $tpl_data[$node_index_array[0]][$node_index_array[1]];
    }

    /**
     * @param string $goods_manage
     * @param $node_nanage
     * @return void  验证当前人员是否可以催办,交办
     */
    public static function verifyNodeButtomAuth(array $node_manage_wid, array $goods_manage_wid) {
        //节点负责人
        if (in_array(userModel::$wid,$node_manage_wid)) {
            return 1;
        }
        //产品开发
        if (in_array(userModel::$wid,$goods_manage_wid)) {
            return 2;
        }
        //管理员，开发，超管 可以催办
        if (userModel::isManageDeveloper()) {
            return 3;
        }

        return false;
    }

    /**
     * @param array $current_node_list
     * @param int $is_goods_manage
     * @return array[]  获取当前节点集下面的为完成的事件和节点
     */
    public static function getIncompleteNodeEvent(array $current_node_list,int $is_goods_manage) {
        $node_list = [];
        $event_list = [];
        foreach ($current_node_list as $k=>$v) {
            if ($v['status'] != 2) {
                $node_manage_wid = array_column(json_decode($v['manage_info']),'wid');
                $is_node_manage = in_array(userModel::$wid,$node_manage_wid)?1:0;
                if ($is_goods_manage || userModel::isSupreOrManage() || $is_node_manage) {
                    $node_list[$k] = [
                        'node_name'=>$v['node_name'],
                        'manage_info'=>$v['manage_info'],
                    ];
                } else {
                    continue;
                }
                foreach ($v['event_detail'] as $k1=>$ev1) {
                    foreach ($ev1 as $k2=>$ev2) {
                        if ($ev2['status'] != 2) {
                            $event_list[$k.'-'.$k1.'-'.$k2] = [
                                'node_name'=>$v['node_name'],
                                'event_name'=>$ev2['event_name'],
                                'manage_info'=>$ev2['manage_info'],
                            ];
                        }
                    }
                }
            }
        }
        return [
            'node_list'=>$node_list,
            'event_list'=>$event_list,
        ];
    }

    /**
     * @param $project
     * @param $current_node
     * @param $param
     * @return void
     * @throws ExceptionError  验证事件是否可提交
     */
    public function verifyEventSubmit($project_id, $node_index, $event_index, $event_type,$event_data) {
        //验证事件类型
        if ($event_type != $event_data['event_type']) {
            SetReturn(-1,'处理的事件类型不匹配');
        }
        //获取下方是否有数据未处理完成
        $count = goodsProjectAbnormalModel::getAbnormalCount(3, $project_id, $node_index,$event_index);
        if ($count > 0) {
            SetReturn(-1,'该事件内有异常还未处理完');
        }
        //完成节点前需要填写内容验证
        // $event_data = goodsProjectModel::checkNodeStatusChange($param, $event_data);
        //审核验证
//        if ($event_data['need_check'] == 1) {
//            SetReturn(-1,'请将该事件提交审核');
//        }
    }

    /**
     * @param $event_type
     * @param $event_data
     * @return   //验证事件需要保存表单时的权限 如。app功能测试，产品硬件功能测试
     */
    public static function verifyEventSave(int $project_id,string $node_index,string $event_index,int $event_type) {
        $db = dbMysql::getInstance();
        $project = $db->table('goods_project')
            ->where('where id=:project_id',['project_id'=>$project_id])
            ->field('id,goods_id,matter_name,tpl_data,current_index,status,is_stop')
            ->one();
        if (!$project) {
            SetReturn(-1,'项目流程不存在');
        }
        if ($project['status'] == 4) {
            SetReturn(-1, '项目流程已被废除不可操作');
        }
        if ($project['is_stop']) {
            SetReturn(-1,'该项目流程已暂停，不可操作');
        }
        $node_index_array = explode('-',$node_index);

        //权限和类型验证
        $tpl_data = json_decode($project['tpl_data'],true);
        $current_node = self::getCurrentNodeData($tpl_data,$node_index);
        $event_data = self::getCurrentEventData($current_node,$event_index);
        if ($event_data['is_advance_submit'] == 0) {
            if ($node_index_array[0] != $project['current_index']) {
                SetReturn(-1,'只可操作当前节点');
            }
        }

        //验证事件类型
        if ($event_type != $event_data['event_type']) {
            SetReturn(-1,'处理的事件类型不匹配');
        }
        //获取下方是否有数据未处理完成
        //权限验证
        $manage_info = json_decode($event_data['manage_info'],true);
        authenticationCommon::projectManageVatify($manage_info);
        return [
            'project'=>$project
        ];
    }

    /**
     * @param $project
     * @param $current_node
     * @param $param
     * @return void
     * @throws ExceptionError 判断接节点是否可以完成
     */
    public static function verifyNodeStatus($current_node,$project_id, $node_index) {
        $event_type_array = [];
        //节点内是否还有事件未完成
        $is_finish_event = 1;
        $is_finish_event_check = 1;
        foreach ($current_node['event_detail'] as $e_list) {
            foreach ($e_list as $event) {
                if (!in_array($event['event_type'],$event_type_array)) {
                    $event_type_array[] = $event['event_type'];
                }
                if ($event['status'] != 2) {
                    $is_finish_event = 0;
                    continue;
                }
                //完成的事件才能审核
                if ($event['status'] == 2 && $event['need_check'] == 1 && $event['event_type'] == 6) {
                    //只有下首单需要审核
                    if (empty($event['check_user_info'])) {
                        $is_finish_event_check = 0;
                        continue;
                    }
                    $check_user_info = json_decode($event['check_user_info'],true);
                    foreach ($check_user_info as $check_) {
                        if ($check_['is_pass'] !=1) {
                            $is_finish_event_check = 0;
                        }
                    }
                }
            }
        }
        if ($is_finish_event == 0) {
            SetReturn(-1,'节点内有事件还未完成');
        }
        if ($is_finish_event_check == 0) {
            SetReturn(-1,'节点内有事件未审核');
        }
        //节点内是否异常事件已经处理完成
        $count = goodsProjectAbnormalModel::getAbnormalCount(2, $project_id, $node_index);
        if ($count > 0) {
            SetReturn(-1,'该节点内有异常还未处理完');
        }
        if (in_array(4,$event_type_array)) {
            $db = dbMysql::getInstance();
            $hardware_approval = $db->table('goods_hardware_approval')
                ->where('where project_id=:project_id and node_index=:node_index',['project_id'=>$project_id,'node_index'=>$node_index])
                ->one();
            if ($hardware_approval) {
                SetReturn(-1,'您已提交不合格审批，切勿重复提交');
            }
        }
        self::$check_node_event_type = $event_type_array;
    }
    /**
     * @param $param
     * @param $type  ''：一般操作，check：审核操作
     * @return array
     * @throws \core\lib\ExceptionError
     */
    public static function verifyNodeRequestData($param,$auth_check = 1){
        $param['project_id'] = (int)$param['project_id'];
        $node_index_array = explode('-',$param['node_index']);
        //修改流程信息
        $db = dbMysql::getInstance();
        $project_ =  $db->query('select id,matter_name,goods_id,tpl_name,status,tpl_data,current_index,flow_path_id,sample_batch,is_stop from oa_goods_project where id=:project_id',['project_id'=>$param['project_id']]);
        if(!$project_) {
            SetReturn(-1,'当前项目流程不存在');
        }
        if ($project_['status'] == 4) {
            SetReturn(-1, '项目流程已被废除不可操作');
        }
        if ($project_['is_stop']) {
            SetReturn(-1,'该项目流程已暂停，不可操作');
        }
        if ($node_index_array[0] != $project_['current_index']) {
            SetReturn(-1,'只能操作当前节点集');
        }
        $tpl_data = json_decode($project_['tpl_data'],true);
        $current_node = self::getCurrentNodeData($tpl_data,$param['node_index']);
        if ($current_node['status'] == 2) {
            SetReturn(-1, '该节点已完成，切勿重复操作');
        }
        //节点操作权限验证
        if ($auth_check) {
            authenticationCommon::projectManageVatify(json_decode($current_node['manage_info'],true));
        }
        return ['tpl_data'=>$tpl_data, 'project'=>$project_,'current_node'=>$current_node];
    }
    /**
     * @param $param 请求参数
     * @param $action 用这个方法的 方法名称action
     * @return array 事件操作时对事件需要的一些参数验证
     * @throws \core\lib\ExceptionError
     */
    public static function verifyEvenRequestData($param,$is_check=0){
        $param['id'] = (int)$param['project_id'];
        //查询项目
        $db = dbMysql::getInstance();
        $project = $db->query('select id,matter_name,tpl_data,goods_id,flow_path_id,tpl_name,current_index,status,is_stop from oa_goods_project where id='.$param['id']);
        if (!$project) {
            SetReturn(-1, '项目流程不存在');
        }
        if ($project['status'] == 4) {
            SetReturn(-1, '项目流程已被废除不可操作');
        }
        if ($project['is_stop']) {
            SetReturn(-1,'该项目流程已暂停，不可操作');
        }
        $param['goods_id'] = (int)$project['goods_id'];
        $param['flow_path_id'] = $project['flow_path_id'];
        $tpl_data = json_decode($project['tpl_data'],true);
        $current_node = self::getCurrentNodeData($tpl_data,$param['node_index']);
        $current_event = self::getCurrentEventData($current_node,$param['event_index']);
        if ($current_event['event_type'] != $param['event_type']) {
            SetReturn(-1,'事件类型不匹配');
        }
        //权限验证
        if (!$is_check) {
            if ($current_event['status'] == 2) {
                SetReturn(-1,'该项目流程已完成，不可操作');
            }
            authenticationCommon::projectManageVatify(json_decode($current_event['manage_info'],true));
            //串行事件判断
            $event_index_array = explode('-',$param['event_index']);
            if ($event_index_array[0]> 0) {
                $event_list = $current_node['event_detail'];
                //验证前面事件是否完成
                foreach ($event_list as $k=>$event_l) {
                    //判断全面的事件是否都完成了
                    if ($k >= $event_l) {
                        foreach ($event_l as $event) {
                            if ($event['status'] != 2) {
                                SetReturn(-1,'事件'.$event['event_name'].'还未完成');
                            }
                        }
                    }
                }
            }
        } else {
            $check_user_info = json_decode($current_event['check_user_info'],true);
            authenticationCommon::projectManageVatify($check_user_info);
            foreach ($check_user_info as $v) {
                if ($v['id'] == userModel::$qwuser_id) {
                    if ($v['is_pass'] != 0) {
                        SetReturn(-1,'您已审核，切勿重复操作');
                    }
                    break;
                }
            }
        }
        //异常添加验证
        return ['tpl_data'=>$tpl_data ,'project'=>$project,'current_node'=>$current_node,'current_event'=>$current_event];
    }

    public static function getBadEventSendData($event_list,$bad_event,$project,$node_index,$current_node,$remarks) {
        $bad_event_ = [];
        foreach ($bad_event as $v) {
            $bad_event_[$v['event_index']] = $remarks;
        }
        $other_data = [
            'user_id'=>userModel::$qwuser_id,
            'model_id'=>$project['id'],
            'node_index'=>$node_index,
            'msg_type'=>7
        ];
        $res_list = [];
        $log_txt = '';//记录string
        $new_current_node = $current_node;
        foreach ($event_list as $k=>$event_) {
            foreach ($event_ as $k1=>$event) {
                //审核未通过将未通过事件置为进行中，并清楚审核记录
                $node_index = $k.'-'.$k1;
                if (isset($bad_event_[$node_index])) {
                    $other_data['event_index'] = $node_index;
                    $manage_info = json_decode($event['manage_info'],true);
                    $item = [
                        'wids' => array_unique(array_column($manage_info,'wid')),
                        'msg' => messagesFrom::getMsgTxt(7,$project['matter_name'],$current_node['node_name'],$event['event_name']),
                        'remarks'=>$bad_event_[$node_index],
                        'other_data'=>$other_data,
                    ];
                    $res_list[] = $item;
                    $log_txt .= $event['event_name'].'，';

                    //修改节点状态
                    $new_current_node['event_detail'][$k][$k1]['status'] = 1;
                    $new_current_node['event_detail'][$k][$k1]['complete_time'] = '';
                }
            }
        }
        return [
            'qw_msg_data' => $res_list,
            'log_txt'=> $log_txt,
            'new_current_node'=>$new_current_node
        ];
    }
    //获取额外抄送人节点
    public static function getSendCopyUser(array $send_copy_user,int $project_id) {
        $event_types = self::$check_node_event_type;
        if (in_array(4,$event_types)) {
            $db = dbMysql::getInstance();
            $hardware_test_user = $db->table('goods_hardware_test','a')
                ->leftJoin('qwuser','b','b.id=a.qwuser_id')
                ->where('where a.project_id=:project_id',['project_id'=>$project_id])
                ->field('b.id,b.wid,b.wname')
                ->list();
            $send_copy_user = array_merge($send_copy_user,$hardware_test_user);
        }
        return $send_copy_user;
    }
    //获取硬件检测 审批
    public static function getHardwareApproval($project_id,$node_index,$ids) {
        $db = dbMysql::getInstance();
        $goods_hardware_test = $db->table('goods_hardware_test')
            ->where('where project_id=:project_id and node_index=:node_index and is_filished=1',['project_id'=>$project_id,'node_index'=>$node_index])
            ->whereIn('qwuser_id',$ids)
            ->field('qwuser_id')
            ->list();
        $hardware_ng_list = [];
        if (count($goods_hardware_test)) {
            $user_ids = array_column($goods_hardware_test,'qwuser_id');
            $hardware_ng_list = $db->table('goods_hardware_no','a')
                ->leftJoin('qwuser','b','b.id=a.qwuser_id')
                ->where('where a.project_id=:project_id and a.is_delete=0 and a.node_index=:node_index',['project_id'=>$project_id,'node_index'=>$node_index])
                ->whereIn('a.qwuser_id',$user_ids)
                ->field('a.id,a.qwuser_id,a.row_name,a.description,a.reason,a.suggestion,b.wname')
                ->list();
        }
        //获取审批数据
        $hardware_approval = $db->table('goods_hardware_approval')
            ->where('where project_id=:project_id and node_index=:node_index',['project_id'=>$project_id,'node_index'=>$node_index])
            ->one();
        if ($hardware_approval) {
            $reason_list = [];
            $reason_ids = json_decode($hardware_approval['reason_ids']);
            foreach ($reason_ids as $v) {
                $reason_list[] = config::getDataName('hardware_check_reason',$v);
            }
            $hardware_approval['reason_list'] = $reason_list;
        }
        self::$hardware_list = [
            'hardware_ng_list'=>$hardware_ng_list,
            'hardware_approval'=>$hardware_approval
        ];

    }

    //获取流程详情中的表单
    public function getProjectDtailTable(int $project_id,array $tpl_data,int $current_index) {
        $db = dbMysql::getInstance();
        //节点中上传的文件
        $project_file = $db->table('goods_project_file')
            ->where('where project_id=:project_id and is_delete = 0',['project_id'=>$project_id])
            ->field('id,user_id,event_type,nodex_index,event_index,src,thumb_src,filename,extension')
            ->list();
        self::$project_files = $project_file;
        $wait_agent_event = [];//待处理事件
        $wait_agent_node = [];//待审核（处理）节点
        $wait_check_event = [];//待审核事件
        $finish_detail_list = [];//完成的节点
        $has_event_type4_node_index = '';//是否有硬件检测事件+节点角标
        //流程完成和待完成表格获取
        foreach ($tpl_data as $k1=>$node_list) {
            if ($k1 <= $current_index) {
                foreach ($node_list as $k2=>$node) {
                    $node_index = $k1.'-'.$k2;
                    $finish_detail = [
                        'node_name'=>$node['node_name'],
                        'node_index'=>$node_index,
                        'submit_wname'=>json_decode($node['manage_info'],true)[0]['wname'],
                        'begin_time'=>$node['begin_time'],
                        'complete_time'=>$node['complete_time'],
                        'expected_day'=>$node['expected_day'],
                        'status'=>$node['status'],
                        'has_approval'=>0,
                    ];
                    $finish_detail_event = [];
                    foreach ($node['event_detail'] as $k3=>$event_list) {
                        foreach ($event_list as $k4=>$event) {
                            $event_index = $k3.'-'.$k4;
                            $event_manage = json_decode($event['manage_info'],true);
                            $event_item = [
                                'event_index' => $event_index,
                                'event_name'=>$event['event_name'],
                                'begin_time'=>$event['begin_time'],
                                'complete_time'=>$event['complete_time'],
                                'expected_day'=>$event['expected_day'],
                                'event_type'=>$event['event_type'],
                                'status'=>$event['status'],
                            ];
                            $event_type = $event['event_type'];
                            //硬件检测特殊处理
                            if ($event_type == 4) {
                                $ids = array_column(json_decode($event['manage_info'],true),'id');
                                //如此第二次的硬件检测 也会覆盖第一次的（第一次提交的必然时可先提交的，必然不可审核时不通过，不通过也不会到下一个节点）
                                self::getHardwareApproval($project_id,$node_index,$ids);
                                $hardware_approval = self::$hardware_list['hardware_approval'];
                                if ($hardware_approval && $hardware_approval['is_pass'] == 0) {
                                    $finish_detail['has_approval'] = 1;
                                }
                                foreach ($event_manage as $event_manage_item) {
                                    $event_item['submit_list'][] = [
                                        'has_ng'=>self::getEventNgList($event_manage_item['id']),
                                        'submit_wname'=>$event_manage_item['wname'],
                                        'files'=>self::getEventFile($event_type,$node_index,$event_index,$event_manage_item['id']),
                                    ];
                                }
                                if ($event['status'] == 2) {
                                    $wid = array_column($event_manage,'wid');
                                    //待办
                                    $is_submit_hardware = self::isSubmitHardware($project_id,$node_index,1);
                                    if (in_array(userModel::$wid,$wid) && !$is_submit_hardware) {
                                        $wait_agent_event[] = [
                                            'node_index' => $node_index,
                                            'event_index' => $event_index,
                                            'event_name' => $event['event_name'],
                                            'event_type' => $event['event_type'],
                                            'status' => $event['status'],
                                            'manage_info'=> $node['manage_info'],
                                            'send_copy_user' => $event['send_copy_user'],
                                            'begin_time'=>strtotime($node['begin_time']),
                                            'expected_day'=>$event['expected_day'],
                                            'is_submit'=>1,
                                            'files'=>self::getEventFile($event_type,$node_index,$event_index,userModel::$qwuser_id),
                                        ];
                                    }
                                }

                            } else {
                                $event_item['submit_list'][] = [
                                    'has_ng'=>0,
                                    'submit_wname'=>$event_manage[0]['wname'],
                                    'files'=>self::getEventFile($event_type,$node_index,$event_index,$event_manage[0]['id']),
                                ];
                            }
                            //异常查询
                            if (goodsProjectAbnormalFrom::hasAbnormal($project_id,$node_index,$event_index)) {
                                $event_item['status'] = 3;
                            }
                            $finish_detail_event[] = $event_item;
                        }
                    }
                    $finish_detail['event_detail'] = $finish_detail_event;
                    $finish_detail_list[] = $finish_detail;
                }
            }
        }
        $current_node = $tpl_data[$current_index];
        $has_hradware = 0;//节点集合下面是否有硬件检测事件
        foreach ($current_node as $k1=>&$node) {
            if ($node['status'] != 2) {
                $node_index = $current_index.'-'.$k1;
                //异常查询
                if (goodsProjectAbnormalFrom::hasAbnormal($project_id,$node_index,'')) {
                    $node['status'] = 3;
                }
                $is_hradware_check = 0;
                $event_all_finished = 1; //事件是否都完成，未审核也算没完成
                $has_hardware_event = 0;
                foreach ($node['event_detail'] as $k2=>&$event1) {
                    foreach ($event1 as $k3=>&$event2) {
                        $event_index = $k2.'-'.$k3;
                        if ($event2['event_type'] == 4) {
                            $has_hradware = 1;
                        }
                        if ($event2['status'] != 2) {
                            $event_type = $event2['event_type'];
                            $event_all_finished = 0;
                            //异常查询
                            if (goodsProjectAbnormalFrom::hasAbnormal($project_id,$node_index,$event_index)) {
                                $event2['status'] = 3;
                            }
                            $manage_info = json_decode($event2['manage_info'],true);
                            $wid = array_column($manage_info,'wid');
                            //延迟待办事项是否已经提交
                            $is_submit_hardware = 0;
                            if ($event_type == 4) {
                                $has_hardware_event = 1;
                                $is_submit_hardware = self::isSubmitHardware($project_id,$node_index);
                            }
                            $is_filished = 0;
                            if ($is_submit_hardware) {
                                $is_filished = self::isSubmitHardware($project_id,$node_index,1);
                            }
                            if (in_array(userModel::$wid,$wid)) {
                                if (!$is_filished || !$is_submit_hardware) {
                                    $wait_agent_event[] = [
                                        'node_index' => $node_index,
                                        'event_index' => $event_index,
                                        'event_name' => $event2['event_name'],
                                        'event_type' => $event2['event_type'],
                                        'status' => $event2['status'],
                                        'manage_info'=> $node['manage_info'],
                                        'send_copy_user' => $event2['send_copy_user'],
                                        'begin_time'=>strtotime($node['begin_time']),
                                        'is_submit'=>$is_submit_hardware,
                                        'expected_day'=>$event2['expected_day'],
                                        'files'=>self::getEventFile($event_type,$node_index,$event_index,userModel::$qwuser_id),
                                    ];
                                }

                            }
                        } else {
                            if ($event2['event_type'] == 4) {
                                $has_hardware_event = 1;
                                $is_hradware_check = 1;
                            }
                        }
                        //待审核的事件
                        if ($event2['status'] == 2 && $event2['need_check'] == 1) {
                            $check_user_info = json_decode($event2['check_user_info'],true);
                            foreach ($check_user_info as $check_user) {
                                if ($check_user['is_pass'] == 0) {
                                    $event_all_finished = 0;
                                    if (userModel::$qwuser_id == $check_user['id']) {
                                        $wait_check_event[] = [
                                            'node_index' => $node_index,
                                            'event_index' => $event_index,
                                            'event_name' => $event2['event_name'],
                                            'event_type' => $event2['event_type'],
                                            'check_user_info' => $event2['check_user_info'],
                                        ];
                                    }
                                }
                            }
                        }
                    }
                }
                $wids_e = array_column(json_decode($node['manage_info'],true),'wid');
                if (in_array(userModel::$wid,$wids_e)) {
                    //没有审批
                    if ($has_hardware_event == 1) {
                        if (!self::$hardware_list['hardware_approval']) {
                            $wait_agent_node[$k1] = [
                                'node_index' => $node_index,
                                'node_name' => $node['node_name'],
                                'send_copy_user' => $node['send_copy_user'],
                                'status' => $node['status'],
                                'manage_info'=> $node['manage_info'],
                                'is_hradware_check'=> $is_hradware_check,
                                'event_all_finished' =>$event_all_finished,
                                'begin_time'=>strtotime($node['begin_time']),
                                'expected_day'=>$node['expected_day'],
                            ];
                        }
                    } else {
                        $wait_agent_node[$k1] = [
                            'node_index' => $node_index,
                            'node_name' => $node['node_name'],
                            'send_copy_user' => $node['send_copy_user'],
                            'status' => $node['status'],
                            'manage_info'=> $node['manage_info'],
                            'is_hradware_check'=> 0,
                            'event_all_finished' =>$event_all_finished,
                            'begin_time'=>strtotime($node['begin_time']),
                            'expected_day'=>$node['expected_day'],
                        ];
                    }
                }

            }
        }
        if (!$has_hradware) {
            self::$hardware_list = [
                'hardware_ng_list'=>[],
                'hardware_approval'=>false,
            ];
        }
        return [
            'wait_agent_event'=>array_values($wait_agent_event),
            'wait_agent_node'=>array_values($wait_agent_node),
            'finish_detail_list'=>$finish_detail_list,
            'has_event_type4_node_index'=>$has_event_type4_node_index,
            'current_node'=>$current_node,
            'wait_check_event'=>$wait_check_event,
        ];
    }
    private static function getEventFile($event_type,$node_index,$event_index,$user_id=0){
        $files = self::$project_files;
        $user_files = [];
        if (count($files)) {
            foreach ($files as $file) {
                if ($event_type == 5) {
                    //检测报告
                    if ($user_id == $file['user_id'] && $file['event_type']==$event_type) {
                        $user_files[] = [
                            'user_id'=>$file['user_id'],
                            'src'=>$file['src'],
                            'thumb_src'=>$file['thumb_src'],
                            'filename'=>$file['filename'],
                            'extension'=>$file['extension'],
                        ];
                    }
                }
                elseif ($event_type == 4)
                {
                    //硬件测试报告
                    if ($user_id == $file['user_id'] && $file['event_type']==$event_type && $file['nodex_index'] == $node_index) {
                        $user_files[] = [
                            'user_id'=>$file['user_id'],
                            'src'=>$file['src'],
                            'thumb_src'=>$file['thumb_src'],
                            'filename'=>$file['filename'],
                            'extension'=>$file['extension'],
                        ];
                    }
                }
                elseif ($event_type == 7) {
                    if ($file['nodex_index'] == $node_index && $file['event_index'] == $event_index && $user_id == $file['user_id']) {
                        $user_files[] = [
                            'user_id'=>$file['user_id'],
                            'src'=>$file['src'],
                            'thumb_src'=>$file['thumb_src'],
                            'filename'=>$file['filename'],
                            'extension'=>$file['extension'],
                        ];
                    }
                }

            }
        }
        return $user_files;
    }
    public static function getEventNgList($user_id){
        $hardware_list = self::$hardware_list;
        if (count($hardware_list['hardware_ng_list'])) {
            foreach ($hardware_list['hardware_ng_list'] as $hardware) {
                if ($hardware['qwuser_id'] == $user_id) {
                    return 1;
                }
            }
        }
        return 0;
    }

    //节点完成时，修改项目数据+下个节点待办事项
    public static function updateTplDataForNodeStatus1(array $tpl_data,int $project_id,int $current_index,array $goods_info,string $node_index,int $need_auth = 1) {
        $db = dbMysql::getInstance();
        //更新当前节点为已完成
        $tpl_data = self::setNodeStatus($tpl_data,$node_index,2,$project_id,$need_auth);
        $update_data = [
            'updated_time'=>date('Y-m-d H:i:s'),
        ];
        //节点集合中是否所有节点已完成
        $is_all_finished = self::getNodeListStatus($tpl_data[$current_index]);
        if($is_all_finished) {
            if (count($tpl_data) > ($current_index+1)) {
                //下面还有节点集合
                //当前节点集合完成，更改下个节点
                self::$jump_next_node = 1;
                //获取下个节点简要信息
                $next_node_info = self::getNextNodeInfo($tpl_data,$current_index);
                $update_data['current_index'] = $current_index+1;
                $update_data['current_node_info'] = json_encode($next_node_info);
                //更新下个节点开始时间
                $tpl_data = self::setNodeBegintime($tpl_data,$update_data['current_index']);
                //生成下个节点待办事项
                $next_node = $tpl_data[$update_data['current_index']];
                goodsMattersFrom::addNextNodeMatter($project_id,$next_node,$goods_info,$update_data['current_index']);
            } else {
                //完成流程
                $update_data['status'] = 2;
                $update_data['complete_time'] = time();
                //将流程中的待办全部设置为已办
                $db->table('goods_matters')
                    ->where('where goods_project_id=:project_id and status=0',['project_id'=>$project_id])
                    ->update(['status'=>1]);
            }
        }
        $update_data['tpl_data'] = json_encode($tpl_data,JSON_UNESCAPED_UNICODE);
        $db->table('goods_project')
            ->where('where id = :project_id',['project_id'=>$project_id])
            ->update($update_data);
    }

    //验证流程是否可以操作
    public static function verifyProject($project_id) {
        $db = dbMysql::getInstance();
        $project_data =  $db->query('select id,goods_id,matter_name,tpl_name,status,tpl_data,current_index,flow_path_id,sample_batch,is_stop,status from oa_goods_project where id=:project_id',['project_id'=>$project_id]);
        if(!$project_data) {
            SetReturn(-1,'当前项目流程不存在');
        }
        if ($project_data['is_stop']) {
            SetReturn(-1,'该项目流程已暂停，不可操作');
        }
        if ($project_data['status'] == 4) {
            SetReturn(-1,'该项目流程已废除，不可操作');
        }
        return $project_data;
    }
    /**
     * @param $project
     * @param $file_data
     * @param $param
     * @return void 事件文档上传
     * @throws ExceptionError
     */
    public function saveProjectFile($project, $file_data,$node_index,$event_index){
        $db = dbMysql::getInstance();
        $where_from = [
            'goods_id'=>$project['goods_id'],
            'project_id'=>$project['id'],
            'nodex_index'=>$node_index,
            'event_index'=>$event_index,
        ];
        //删除之前的
        $db->query('update oa_goods_project_file set is_delete = 1 where goods_id=:goods_id and project_id=:project_id and nodex_index=:nodex_index and event_index=:event_index',$where_from);
        //保存现在的
        foreach ($file_data as $v) {
            $insert_data = [
                'user_id'=>userModel::$qwuser_id,
                'wid'=>userModel::$wid,
                'goods_id'=>$project['goods_id'],
                'event_type'=>7,
                'project_id'=>$project['id'],
                'nodex_index'=>$node_index,
                'event_index'=>$event_index,
                'flow_path_id'=>$project['flow_path_id'],
                'created_at'=>date('Y-m-d H:i:s'),
                'src'=>$v['src'],
                'filename'=>$v['filename'],
                'extension'=>$v['extension'],
                'thumb_src'=>$v['thumb_src'],
                'file_type'=>getFileType($v['extension']),
            ];
            $db->table('goods_project_file')
                ->insert($insert_data);
        }

    }

    //事件设置审核人
    public static function setEventChecker($event) {
        //流程申请
        $event['need_check'] = 1;
        if ($event['event_type'] == 6) {
            $check_user = configFrom::getConfigByName('first_order_check_user');
            if (!$check_user) {
                SetReturn(-1,'未设置首单下单审核人，请联系系统管理员');
            }
        } else {
            $check_user = $event['check_user_info'];
        }
        $check_user = json_decode($check_user,true);
        $check_info = [];
        $ids = [];
        foreach ($check_user as $user) {
            if (!in_array($user['id'],$ids)) {
                $check_info[] = [
                    'id'=>$user['id'],
                    'wid'=>$user['wid'],
                    'wname'=>$user['wname'],
                    'avatar'=>$user['avatar']??'',
                    'is_pass'=>0,
                    'reason'=>'',
                    'checked_time'=>'',
                ];
                $ids[] = $user['id'];
            }

        }
        $event['check_user_info'] = json_encode($check_info,true);
        return $event;
    }
    //事件 审核后的tpl信息获取+处理待办事项
    public static function setEventCheckedInfo($tpl_data,$node_index,$event_index,$is_pass,$reason,$event_type){
        $node_array = explode('-',$node_index);
        $event_array = explode('-',$event_index);
        $node = $tpl_data[$node_array[0]][$node_array[1]];
        $event = $node['event_detail'][$event_array[0]][$event_array[1]];
        $check_user_info = json_decode($event['check_user_info'],true);
        //目前是会签
        $is_all_pass = 1;
        foreach ($check_user_info as &$v) {
            if ($v['id'] == userModel::$qwuser_id) {
                $v['reason'] = $reason;
                $v['is_pass'] = ($is_pass==1?1:2);
                $v['checked_time'] = date('Y-m-d H:i:s');
            }
            if ($v['is_pass'] != 1){
                $is_all_pass = 0;
            }
        }
        $event['check_user_info'] = json_encode($check_user_info,JSON_UNESCAPED_UNICODE);
        if ($is_all_pass) {
            $event['status'] = 2;
            $event['complete_time'] = date('Y-m-d H:i:s');
        } else {
            if (!($event_type == 6 || $event_type == 4) && !$is_pass) {
                $event['status'] = 1;
                $event['complete_time'] = '';
            }
        }
        $node['event_detail'][$event_array[0]][$event_array[1]] = $event;
        $tpl_data[$node_array[0]][$node_array[1]] = $node;
        return [
            'tpl_data'=>$tpl_data,
            'is_all_pass'=>$is_all_pass
        ];
    }

    //获取额外抄送人事件
    public static function getEventSendCopyUser(int $event_type,int $project_id,array $send_copy_user) {
        //硬件检测报告
        if ($event_type == 4) {
            $db = dbMysql::getInstance();
            $hardware_test_user = $db->table('goods_hardware_test','a')
                ->leftJoin('qwuser','b','b.id=a.qwuser_id')
                ->where('where a.project_id=:project_id',['project_id'=>$project_id])
                ->field('b.id,b.wid,b.wname')
                ->list();
            $send_copy_user = array_merge($send_copy_user,$hardware_test_user);
        }
        //下首单
        if ($event_type == 6) {
            $copy_user = configFrom::getConfigByName('first_order_copy_user');
            if (!empty($copy_user)) {
                $copy_user = json_decode($copy_user,true);
                $send_copy_user = array_merge($send_copy_user,$copy_user);
            }
        }
        return array_column($send_copy_user,'wid');
    }

    //获取此人是否已经填写了产品硬件检测
    public static function isSubmitHardware($project_id,$node_index,$type=0) {
        $list = self::$hardware_test_list;
        if (!count($list)) {
            $db = dbMysql::getInstance();
            $list = $db->table('goods_hardware_test')
                ->where('where project_id=:project_id',['project_id'=>$project_id])
                ->list();
        }
        self::$hardware_test_list = $list;
        foreach ($list as $v) {
            if ($node_index == $v['node_index']) {
                if ($type == 0) {
                    if ($v['qwuser_id'] == userModel::$qwuser_id && $v['is_submit'] == 1) {
                        return 1;
                    }
                } else {
                    if ($v['qwuser_id'] == userModel::$qwuser_id && $v['is_filished'] == 1) {
                        return 1;
                    }
                }
            }
        }
        return 0;
    }
































































































































































    public function editProjectForm($table_name,$param){
        self::$table_name = $table_name;
        switch ($table_name) {
            case 'form_goods_attr':
                $form_id = formGoodsAttrModel::editGoodsAttr($param);
                break;
            default:
                SetReturn(-1, '表单名不存在');
                die;
        }
        return $form_id;
    }

    public function setFormLog($project,$current_node,$event_data){
        $prefix = '表单填写：【'.$project['tpl_name'].'】节点【'.$current_node['node_name'].'】事件【'.$event_data['event_name'].'】表单【'.self::$table_name.'】，';
        $data_msg = '';
        switch (self::$table_name) {
            case 'form_goods_attr':
                $data_msg = formGoodsAttrModel::getUpdateInfo();
                break;
            default:
                SetReturn(-1, '表单名不存在');
                die;
        }
        if ($data_msg != '') {
            //本地为文档记录
            log::newGoodsLog($project['goods_id'],$project['id'],$prefix.$data_msg,1);
        }
    }


    //项目异常提交验证
    public function verifyProjectRequestData($param){
        $param['id'] = (int)$param['id'];
        $param['index_id'] = (int)$param['index_id'];
        $param['event_index_id'] = (int)$param['event_index_id'];
        $param['abnormal_type'] = (int)$param['abnormal_type'];
        //查询项目
        $db = dbMysql::getInstance();
        $project = $db->query('select id,tpl_data,goods_id,flow_path_id,tpl_name,goods_name,current_index,status from oa_goods_project where id='.$param['id']);
        if (!$project) {
            SetReturn(-1, '项目流程不存在');
        }
        if ($project['status'] == 4) {
            SetReturn(-1, '项目流程已被废除不可操作');
        }
        $param['goods_id'] = (int)$project['goods_id'];
        $param['flow_path_id'] = $project['flow_path_id'];
        $tpl_data = json_decode($project['tpl_data'],true);
        $event_data = $param['event_index_id'] > -1 ? $tpl_data[$param['index_id']]['event_detail'][$param['event_index_id']]:'';
        return ['param'=>$param, 'tpl_data'=>$tpl_data ,'event_data'=>$event_data,'project'=>$project];
    }


//    /**
//     * @param $type  'first_goods_confirm':首单下单或签
//     * @param $project_
//     * @param $current_node
//     * @param $next_node
//     * @return void  企业微信用户推送信息-推送节点信息修改
//     * @throws \core\lib\ExceptionError
//     */
    public function sendMessage($type,$project_,$current_node,$next_node = [], $node_index_id=-1,$event_index_id = -1)
    {
        $node_name = $current_node['node_name'];
        $manage_info = json_decode($current_node['manage_info'],true);
        $w_userids = [];
        $db = dbMysql::getInstance();
        switch ($type){
            case 'status':
                //当前节点完成，给下一个流程负责人推送
                foreach ($next_node['event_detail'] as $event) {
                    $manage_info_event = json_decode($event['manage_info'],true);
                    $event_userids = array_column($manage_info_event,'wid');
                    $w_userids = array_merge($w_userids, $event_userids);
                }
                //去重
                $w_userids = array_unique($w_userids);
                //节点的状态修改为已完成
                $text = "新品【".$project_['goods_name']."】项目【".$project_['tpl_name']."】的【".$node_name."】节点已完成，请您完成下一个流程节点 【".$next_node['node_name'].'】中您负责的事件。';
                //状态修改
                break;
            case 'node_check_user':
                //节点提交审核，发消息给审核人 TODO 废除
                $w_userids = [$current_node['check_info']['wid']];
                //节点负责人将审核表单推送给审核人
                $text = "新品【".$project_['goods_name']."】项目【".$project_['tpl_name']."】的【".$node_name."】节点需要您审核";
                //状态修改
                break;
            case 'check_node':
                //节点审核结果，推送给问题事件负责人（节点负责人）
                $text = "新品【".$project_['goods_name']."】项目【".$project_['tpl_name']."】节点【".$node_name."】中有您负责的事件【未通过】，请及时查看并更正。";
                $w_userids = [];
                foreach ($current_node['not_pass_event'] as $v) {
                    $v_userids = array_column(json_decode($v['manage_info']),'wid');
                    $w_userids = array_merge($w_userids,$v_userids);
                }
                $w_userids = array_unique($w_userids);
                //状态修改
                break;
            case 'send_copy':
                //抄送人（节点）
                if (empty($current_node['send_copy_user'])) {
                    return;
                }
                $w_user = json_decode($current_node['send_copy_user']);
                $w_userids = array_column($w_user,'wid');
                if (count($w_userids) > 0) {
                    $text = "新品【".$project_['goods_name']."】项目【".$project_['tpl_name']."】的【".$node_name."】节点已完成，请知晓。";
                } else {
                    return;
                }
                break;
            case 'event_check_user':
                $current_event = $current_node['event_detail'][$event_index_id];
                $w_userids = [$current_event['check_info']['wid']];
                //节点负责人将审核表单推送给审核人
                $text = "新品【".$project_['goods_name']."】项目【".$project_['tpl_name']."】的【".$node_name."】节点的【".$current_event['event_name']."】需要您审核。";
                break;
            case 'check_event':
                //事件审核
                $current_event = $current_node['event_detail'][$event_index_id];
                $is_pass = $current_event['check_info']['is_pass'];
                $text = "新品【".$project_['goods_name']."】项目【".$project_['tpl_name']."】的【".$node_name."】节点的【".$current_event['event_name']."】事件审核【".($is_pass==1?'已通过':'未通过').'】。';
                $w_userids = [$current_event['check_info']['wid']];
                break;
            case 'event_status':
                //事件状态修改通知给节点负责人
                $current_event = $current_node['event_detail'][$event_index_id];
                $w_userids = array_column($manage_info,'wid');
                $text = "新品【".$project_['goods_name']."】项目【".$project_['tpl_name']."】的【".$node_name."】节点的【".$current_event['event_name']."】事件已完成。";
                break;
            case 'send_copy_event':
                //事件完成的状态发给抄送人
                $current_event = $current_node['event_detail'][$event_index_id];
                if (empty($current_event['send_copy_user'])) {
                    return;
                }
                $w_user = json_decode($current_event['send_copy_user']);
                $w_userids = array_column($w_user,'wid');
                if (count($w_userids) > 0) {
                    $text = "新品【".$project_['goods_name']."】项目【".$project_['tpl_name']."】的【".$node_name."】节点的【".$current_event['event_name']."】事件已完成，请知晓。";
                } else {
                    return;
                }
                break;
            case 'attr_error':
                //产品硬件测试不通过的字段提交给产品创建人。
                if (!empty($current_node['goods_info']['manage_info'])) {
                    $goods_mamage = json_decode($current_node['goods_info']['manage_info']);
                    $w_userids = array_column($goods_mamage,'wid');
                    $not_achieve_data = $current_node['goods_info']['not_achieve_data'];
                    $text = "新品【".$project_['goods_name']."】【硬件检测】失败，请您尽快处理。失败原因：";
                    $field_lsit = goodsAttrModel::$field_lsit;
                    foreach ($not_achieve_data as $k=>$error) {
                        $text .= "【{$field_lsit[$k]}】{$error},";
                    }
                    $text = trim($text,',');
                    break;
                }
            case 'responsible_person':
                //测试中有软件或硬件 问题
                $current_event = $current_node['event_detail'][$event_index_id];
                $copy_user_info = $current_node['copy_user_info'];
                $responsible_person = json_decode($copy_user_info['responsible_person']);
                $copy_person = json_decode($copy_user_info['send_copy_user']);
                $w_userids = array_merge(array_column($responsible_person,'wid'),array_column($copy_person,'wid'));
                $text = "异常：新品【".$project_['goods_name']."】项目【".$project_['tpl_name']."】的【".$node_name."】节点的【{$current_event['event_name']}】异常。异常问题：{$copy_user_info['remark']}。请及时处理。";
                break;
            case 'special_error':
                $current_event = $current_node['event_detail'][$event_index_id];
                $copy_user_info = $current_node['copy_user_info'];
                $responsible_person = json_decode($copy_user_info['responsible_person']);
                $w_userids = array_column($responsible_person,'wid');
                $text = "【异常特批】新品【".$project_['goods_name']."】项目【".$project_['tpl_name']."】的【".$node_name."】节点的【{$current_event['event_name']}】异常。异常问题：{$copy_user_info['remark']}。请及时处理。";
                break;
            case 'first_goods_confirm':
                $send_copy_user = json_decode($current_node['send_copy_user'],true);
                $w_userids = array_column($send_copy_user,'wid');
                $text = "新品【".$project_['goods_name']."】项目【".$project_['tpl_name']."】的流程已完成，请您确认是否进行【首单下单】。";
                $msg = "通知：\n".$text;
                break;
            case 'cofirm_first_order':
                $w_userids = array_column($manage_info,'wid');
                //节点的状态修改为已完成
                $text = "新品【".$project_['goods_name']."】项目【".$project_['tpl_name']."】的【首单下单】申请审核已通过。请知晓。";
                break;
            case 'send_instructions':
                $goods_info = $db->query('select operator_info from oa_goods_new where id = '.$project_['goods_id']);
                $w_userids = array_column(json_decode($goods_info['operator_info'],true),'wid');
                $text = "新品【".$project_['goods_name']."】的【产品说明书】已上传并通过。请知晓。";
                break;
            default:
                return;
        }
        $other_data = [
            'user_id'=>userModel::$qwuser_id,
            'model_id'=>$project_['id'],
            'node_index'=>$node_index_id,
            'event_index'=>$event_index_id,
            'msg_type'=>$type=='first_goods_confirm'?2:1
        ];
        messagesFrom::senMeg($w_userids, $text, $other_data);
    }

    /**
     * @param $goods_id  新品id
     * @param $hardware_test  硬件功能测试数据
     * @param $pdf_type  添加水印，0不添加(填写后的规格书，还未做任何处理)，1答复函，2硬件功能测试，3质检标准书
     * @return string[]
     * @throws ExceptionError
     * @throws \Mpdf\MpdfException
     */
    static public function saveAttrPdf($goods_id, $project_id, $hardware_test = '', $pdf_type = 0)
    {
        $html_data = goodsAttrModel::geGoodsAttrPdfHtml($goods_id, $hardware_test, $pdf_type);
        $title = $html_data['title'];
        $table_html = $html_data['html'];
        $mpdf = new \Mpdf\Mpdf();
        $mpdf->autoLangToFont = true;
        $mpdf->autoScriptToLang = true;
        $mpdf->useSubstitutions = true;
        $mpdf->SetHTMLFooter('
                <div style="text-align: center; font-weight: bold;">
                    成都易威行-前端支持
                </div>');
        // 添加水印图片
        $mpdf->showImageErrors = true;//图片错误就显示
        switch ($pdf_type) {
            case 1:
                $title.='-答复函';
                $img_url = '/public/static/api/seal/reply.png';
                $image_path = SELF_FK . $img_url;
                $mpdf->SetHTMLHeader('
                    <div style="width:160px;height: 160px;position: absolute;right: 50px; top: 0px;">
                        <img src="' . $image_path . '" style="width:100%;">
                    </div>
                ');
                break;
            case 2:
                $title.='-硬件功能测试';
                break;
            case 3:
                $title.='-检测标准书';
                $img_url = '/public/static/api/seal/quality.png';
                $image_path = SELF_FK . $img_url;
                $mpdf->SetHTMLHeader('
                    <div style="width:160px;height: 160px;position: absolute;right: 50px; top: 0px;">
                        <img src="' . $image_path . '" style="width:100%;">
                    </div>
                ');
                break;
        }
        //文件
        $save_path = "/public/upload/attr_form_pdf/{$goods_id}-{$project_id}";
        if ($hardware_test) {
            $save_path .= '/'.userModel::$wid;
        }
        $url = SELF_FK.$save_path;
        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path.'/'.$title . '.pdf';
        $url = SELF_FK.$path;
        $html = '<style>
                .attr_table table {border-collapse: collapse;border: 1px solid black;}
                .attr_table th,td {border: 1px solid black;padding: 5px;}
                .gou{text-align:center;}</style>';

        $html .= '<body style="font-size: 16px;">';
        $html .= '<h3 style="text-align: center">' . $title . '</h3>';
        //添加创建者

        $html .= '<div style="text-align: center">创建者：'.userModel::$wname.'</div>';

        $html .= '<div class="attr_table" style="margin-top: 20px;padding: 0 10px; line-height: 30px;">';

        $html .= '<table><tr style="width: 100%"><th style="width: 20%">参数名称</th><th style="width: 65%">参数数据</th>';
        if ($pdf_type > 0) {
            $html .=  '<th style="width: 15%">检测结果</th>';
        }
        $html .=  '</tr>';

        $html .= '<tbody>';
        $html .= $table_html;
        $html .= '</tbody></table>';

        $html .= '</div></body>';
        $mpdf->WriteHTML($html);

        // I生成，F保存到本地
        $mpdf->Output($url, 'F');
        return ['title'=>$title,'path'=>$path];
    }


    private function getFileSql($param,$event_data,$user,$file_data){
        if ($event_data['event_type'] == 3) {
            $insert_sql = 'insert into oa_project_file (user_id,goods_id,project_id,project_index,event_index,flow_path_id,src,filename,extension,created_at,file_type,event_type) values ';
            $insert_data = [
                'user_id'=>$user['id'],
                'wid'=>$user['wid'],
                'goods_id'=>$param['goods_id'],
                'event_type'=>$event_data['event_type'],
                'project_id'=>$param['id'],
                'project_index'=>$param['index_id'],
                'event_index'=>$param['event_index_id'],
                'flow_path_id'=>$param['flow_path_id'],
                'created_at'=>date('Y-m-d H:i:s'),
            ];
            foreach ($file_data as $k=>$file) {
                $insert_sql .= "(:user_id,:goods_id,:project_id,:project_index,:event_index,:flow_path_id,:src{$k},:filename{$k},:extension{$k},:created_at,:file_type{$k},:event_type),";
                $insert_data["src{$k}"] = $file['src'];
                $insert_data["filename{$k}"] = $file['filename'];
                $insert_data["extension{$k}"] = $file['extension'];
                $insert_data["file_type{$k}"] = getFileType($file['extension']);
            }
        } else {
            $insert_sql = 'insert into oa_project_file (user_id,wid,goods_id,src,filename,extension,created_at,file_type,event_type) values ';
            $insert_data = [
                'user_id'=>$user['id'],
                'wid'=>$user['wid'],
                'goods_id'=>$param['goods_id'],
                'event_type'=>$event_data['event_type'],
                'created_at'=>date('Y-m-d H:i:s'),
            ];
            foreach ($file_data as $k=>$file) {
                $insert_sql .= "(:user_id,:wid,:goods_id,:src{$k},:filename{$k},:extension{$k},:created_at,:file_type{$k},:event_type),";
                $insert_data["src{$k}"] = $file['src'];
                $insert_data["filename{$k}"] = $file['filename'];
                $insert_data["extension{$k}"] = $file['extension'];
                $insert_data["file_type{$k}"] = getFileType($file['extension']);
            }
        }
        $insert_sql = trim($insert_sql,',');
        return ['sql'=>$insert_sql,'insert_data'=>$insert_data];
    }
    /**
     * @param $project
     * @param $msg
     * @return void本地文档记录，记录整个流程数据
     */
    public function setLog($project,$msg):void
    {
        log::goodspProjectLog()->info('['.userModel::$wid.']将'.$project['id'].'修改为：'.$msg);
    }

    public function getGoodsNodeInfoForList(array $project) {
        $tpl_data = json_decode($project['tpl_data'],true);
        $node_list = $tpl_data[$project['current_index']];
        $result = [];
        foreach ($node_list as $v) {
            $result[] = [
                'manage_info'=>$v['manage_info'],
                'node_name'=>$v['node_name'],
                'begin_time'=>$v['begin_time']?strtotime($v['begin_time']):0,
                'expected_day'=>$v['expected_day'],
            ];
        }
        return $result;
    }
}