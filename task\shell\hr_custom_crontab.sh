#!/bin/bash
# 处理定时任务
token='private_token_steal_my_token_bad'
max_retries=3  # 设置最大重试次数
count=0  # 计数器

while true; do
    # 判断是否达到最大重试次数
    if [ $count -ge $max_retries ]; then
        echo "已达到最大重试次数 $max_retries，退出循环"
        break;
    fi
    # 发送POST请求，这里假设URL为http://example.com/api，根据实际情况替换
#    response=$(curl -s -X POST -d "token=$token" 'http://171.223.214.187:8901/task/customCrontab/getNextCrontab')
      response=$(curl -s -X POST -d "token=$token" 'http://39.101.133.112:8082/task/customCrontab/getNextCrontab')
     echo "$response"
    # 从响应中提取code字段的值
    code=$(echo "$response" | grep -oP '"code":\K-?\d+')

    if [ "$code" -ne 2 ]; then
      id=$(echo "$response" | grep -oP '"id":\K-?\d+')
      echo "调用成功： code=$code，id ：$id"
      continue;
    fi

    # 计数器 +1
    count=$((count + 1))
    echo "重试次数: $count"
done


echo "用户定时任务处理完毕，执行时间：$(date "+%Y-%m-%d %H:%M:%S")"

