<?php

namespace plugins\checkin\controller;

use plugins\checkin\models\ruleModel;
use plugins\checkin\models\userModel;
use core\lib\config;
use core\lib\db\dbCMysql;
use core\lib\db\dbMysql;

class ruleController
{

    public function getConfig()
    {
        $config = config::all('data_checkin');
        // 所有假期类型
        $vacation = dbCMysql::getInstance()->table('vacation')->field('id, name,time_attr,perday_duration')->list();
        $config['vacation'] = $vacation;
        $config['full_attendance'] = [
            ['id' => 'come_late_leave_early', 'name' => '迟到早退'],
            ['id' => 'absenteeism', 'name' => '旷工'],
            ['id' => 'miss_card', 'name' => '缺卡'],
            // ['id' => 'miss_start', 'name' => '上班缺卡'],
            // ['id' => 'miss_end', 'name' => '下班缺卡'],
            ['id' => 'mend_start', 'name' => '上班补卡'],
            ['id' => 'mend_end', 'name' => '下班补卡'],
        ];
        foreach ($vacation as $item) {
            $config['full_attendance'][] = ['id' => 'vacation-'.$item['id'], 'name' => $item['name']];
        }
        // todo 目前只有一个公司
        $config['corporation'] = dbCMysql::getInstance()->table('corporation')->field('id, name')->list();
        $cdb = dbCMysql::getInstance();
        $last_sync = $cdb->table('sync_time')->where('where type = 3 and status = 1')->order('finish_time desc')->one();
        $config['latest_checkin_month'] = $last_sync['month'] ?? '';
        returnSuccess($config);
    }

    // 获取规则列表
    public function getList()
    {
        $paras_list = array('rule_name', 'rule_type', 'page', 'page_size', 'status');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;

        $cdb = dbCMysql::getInstance();
        $cdb->table('rule');
        $cdb->where('where is_delete = 0');
        if ($param['rule_name']) {
            $cdb->andWhere('rule_name like :rule_name', ['rule_name' => '%' . $param['rule_name'] . '%']);
        }
        if ($param['rule_type']) {
            $cdb->andWhere('rule_type = :rule_type', ['rule_type' => $param['rule_type']]);
        }
        if (isset($param['status']) && in_array($param['status'], [0, 1])) {
            $cdb->andWhere('status = :status', ['status' => $param['status']]);
        }
        $cdb->order('id desc');
        $list = $cdb->pages($page, $limit);
        $user_ids = array_column($list['list'], 'user_id');
        $db = dbMysql::getInstance();
        $users = $db->table('qwuser')->whereIn('id', $user_ids)->field('id, wname')->list();
        $userMap = array_column($users, 'wname', 'id');

        foreach ($list['list'] as &$item) {
            $item['attach'] = json_decode($item['attach'], true);
            $item['user_name'] = $userMap[$item['user_id']];
        }

        returnSuccess($list);
    }

    // 新增/编辑等级规则
    public function add()
    {
        $paras_list = array('id', 'rule_type', 'rule_name', 'remark', 'attach', 'status');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        empty($param['rule_name']) && returnError('规则名称必填');
        empty($param['rule_type']) && returnError('规则类型必填');
        !isset($param['status']) && returnError('是否启用必填');

        // 校验等级规则
        try {
            ruleModel::checkRule($param);
        } catch (\Throwable $error) {
            returnError($error->getMessage());
        }

        // 判断规则名称是否重复
        $cdb = dbCMysql::getInstance();
        $cdb->table('rule');
        $cdb->where('where rule_name = :rule_name and rule_type = :rule_type and is_delete = 0', ['rule_name' => $param['rule_name'], 'rule_type' => $param['rule_type']]);
        if ($param['id']) {
            $cdb->andWhere(' id != :id', ['id' => $param['id']]);
        }
        $is_exist = $cdb->one();
        if ($is_exist) returnError('规则名称已存在');

        if ($param['id']) {
            $cdb->table('rule');
            $cdb->where('id = :id', ['id' => $param['id']]);

            $cdb->update([
                'user_id' => userModel::$qwuser_id,
                'rule_name' => $param['rule_name'],
                'rule_type' => $param['rule_type'],
                'status' => $param['status'],
                'remark' => $param['remark'],
                'attach' => $param['attach'],
            ]);
        } else {
            $cdb->table('rule');
            $cdb->insert([
                'user_id' => userModel::$qwuser_id,
                'rule_name' => $param['rule_name'],
                'rule_type' => $param['rule_type'],
                'status' => $param['status'],
                'remark' => $param['remark'],
                'attach' => $param['attach'],
            ]);
        }
        returnSuccess();
    }

    // 删除规则
    public function delete()
    {
        $paras_list = array('id');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        empty($param['id']) && returnError('规则id必填');
        $cdb = dbCMysql::getInstance();
        $cdb->table('rule');
        $cdb->where('id = :id', ['id' => $param['id']]);
        $one = $cdb->one();
        if (empty($one)) {
            returnError('规则不存在');
        }
        $cdb->update(['is_delete' => 1]);
        returnSuccess([], '删除成功');
    }

    // 获取规则详情
    public function getDetail()
    {
        $paras_list = array('id');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        empty($param['id']) && returnError('规则id必填');
        $cdb = dbCMysql::getInstance();
        $cdb->table('rule');
        $cdb->where('where id = :id', ['id' => $param['id']]);
        $detail = $cdb->one();
        if (!empty($detail)) {
            $detail['attach'] = json_decode($detail['attach'], true);
            if (!empty($detail['user_id'])) {
                $db = dbMysql::getInstance();
                $user = $db->table('qwuser')->where('id = :id', ['id' => $detail['user_id']])->field('id, wname')->one();
                $detail['user_name'] = $user['wname'];
            }
        }
        returnSuccess($detail);
    }

    // 修改状态
    public static function changeStatus()
    {
        $paras_list = array('id');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        empty($param['id']) && returnError('规则id必填');
        $cdb = dbCMysql::getInstance();
        $cdb->table('rule');
        $cdb->where('id = :id', ['id' => $param['id']]);
        $detail = $cdb->one();
        $status = $detail['status'] ? 0 : 1;

        if (empty($detail)) {
            returnError('规则不存在');
        }
        $cdb->where('id = :id', ['id' => $param['id']])->update(['status' => $status, 'user_id' => userModel::$qwuser_id]);
        returnSuccess([], '修改成功');
    }

    // 获取所有规则
    public function getAll()
    {
        $cdb = dbCMysql::getInstance();
        $cdb->table('rule');
        $cdb->where('where is_delete = 0 and status = 1');
        $cdb->field('id, rule_name, rule_type');
        $list = $cdb->list();
        $rule = [];
        foreach ($list as $item) {
            !isset($rule[$item['rule_type']]) && $rule[$item['rule_type']] = [];
            $rule[$item['rule_type']][] = $item;
        }
        returnSuccess(['rule' => $rule]);
    }

}