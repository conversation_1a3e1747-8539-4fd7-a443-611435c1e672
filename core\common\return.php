<?php
/**
 * Created by 漠山.
 * @purpose 描述
 * @Author: 漠山
 * @Time: 2023/12/3 16:19
 */

$_ResultCode = array(
    0		=> "成功",
    1       => '未登录',
    2       => '同步接口请求完成所有',
    -1      => '失败',
    10      => '系统升级中',
    9999	=> "未知错误",
    9000	=> "未定义ROUTES",
    9001	=> "没有访问权限",		// 找不到文件
    9002	=> "Token验证失败",	// 未登录或其他原因验证失败
    9003	=> "账号不存在",
    9004	=> "密码错误",
    9005	=> "API请求异常",
    8000	=> "参数缺失",
    8001	=> "参数错误",
    7000	=> "未定义API",
    7001	=> "一般错误",
    6000	=> "MYSQL 错误",
    6001	=> "REDIS 错误"
);

function showError($msg){
    if (ob_get_length()) ob_end_clean();
    header('Content-Type:application/json; charset=utf-8');
    echo $msg;
}

function returnSuccess($data = [],$msg = '',){
    if (empty($data)) {
        $data = [];
    }
    SetReturn(0, $msg, $data);
}
function returnError($msg = ''){
    SetReturn(-1, $msg);
}

// 标准服务端返回函数
function SetReturn(int $resultCode = 0,$resultMessage = "OK",$resultData = null){
    global $_ResultCode;
    // 检查编码是否存在
    if (!isset($_ResultCode[$resultCode])) {
        $resultCode = 9999;
        $resultDesc = $_ResultCode[$resultCode];
    } elseif ($resultCode >= 6000 && $resultCode < 7000) {
        $resultDesc = $_ResultCode[$resultCode];
    }

    $res = array(
        "code" => $resultCode,
        "message" => $resultMessage,	// 内部错误信息
        "status" => $resultCode > 0 ? 'warning' : 'success',		// 标准错误信息
        "data" => $resultData,

    );
    returnJson($res);
}



function returnJson(array $data = [], $header = null)
{
    if (ob_get_length()) ob_end_clean();
    header('Content-Type:application/json; charset=utf-8');
    if ($header) {
        if (is_array($header)) {
            foreach ($header as $v) {
                header($v);
            }
        } else {
            header($header);
        }
    }
    die(json_encode($data,JSON_UNESCAPED_UNICODE));
}

