<?php

namespace  admin\models;

use core\lib\config;
use core\lib\db\dbCMysql;
use core\lib\predisV;

class qwModel
{
    private array $qw_config = [];

    public function __construct()
    {
        $this->qw_config = config::all('qw');
    }

    private function getToken()
    {
        $redis = (new predisV())::$client;
        $qw_config = $this->qw_config;
        global $log;
        $redis->del('qy_token');
        $token = $redis->get('qy_token');
        if ($token) {
            return $token;
        } else {
            //内部应用
            $token_url = $qw_config['token_url_nei'];
            $data = requestHttp($token_url, 'get');
            if ($data && $data['data']) {
                $redis->set('qy_token', $data['data']);
                $redis->expire('qy_token', 5 * 60);
                return $data['data'];
            } else {
                $log::sendMessageToQwLog('企微：获取token失败', 'token获取');
                return false;
            }
            //oa系统应用
//        $token_url = $qw_config['token_url'].'?corpid='.$qw_config['corpid'].'&corpsecret='.$qw_config['secret'];
//        $data = requestHttp($token_url,'get');
//        if ($data && $data['errcode']==0) {
//            $redis->set('qy_token', $data['access_token']);
//            $redis->expire('qy_token',5*60);
//            return $data['access_token'];
//        } else {
//            $log::sendMessageToQwLog('企微：获取token失败','token获取');
//            return false;
//        }
        }
    }

    //发送应用消息
    function sendMessage()
    {
        global $qw_config;
        global $log;
        $token = $this->getToken();
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=' . $token;
        //获取跳转地址
        $msg = [
            'type'      => 'textcard',
            'qw_userid' => 'ZhangGuoMing',
            'msg'       => 'sad',
            'title'     => '6262',
            'data'      => json_encode(['sda' => 1616])
        ];
        $msg_url = 'https://www.baidu.com/';
        //获取数据
        $data = [
            'touser'  => $msg['qw_userid'],
            'msgtype' => $msg['type'] ?? 'text',
            'agentid' => 1000007,//企业应用的id
        ];
        if ($data['msgtype'] == 'text') {
            $data['text'] = ['content' => $msg['msg']];
        } elseif ($data['msgtype'] == 'textcard') {
            $data['textcard'] = [
                'title'       => $msg['title'],
                'description' => $msg['msg'],
                'url'         => $msg_url,
            ];
        }
        //发送消息
        $res = requestHttp($url, 'post', json_encode($data));
        if (!empty($res)) {
            //日志记录(不管成功与否)
            dd($res);
        } else {
            return false;
        }

    }

    //发送打卡数据拉取
    function getCorpCheckinOption($month)
    {
        $token = $this->getToken();
        $url = $this->qw_config['get_corp_checkin_option_url'] . '?access_token=' . $token;
        $data = [];
        //发送消息
        $res = requestHttp($url, 'post', json_encode($data));
        if ($res['errcode'] == 0) {
            $group = $res['group'];
            $cdb = dbCMysql::getInstance();
            $cdb->table('corp_checkin_option');
            // todo 后续需要扩展为多企微
            $group_data = $cdb->field('id,groupid')->list();
            $group_data = array_column($group_data, 'id', 'groupid');

            foreach ($group as $item) {
                if (array_key_exists($item['groupid'], $group_data)) {
                    $cdb->table('corp_checkin_option')->where('id = ' . $group_data[$item['groupid']])->update([
                        'groupid'      => $item['groupid'],
                        'groupname'    => $item['groupname'],
                        'grouptype'    => $item['grouptype'],
                        'checkindate'  => json_encode($item['checkindate'], JSON_UNESCAPED_UNICODE),
                        'spe_workdays' => json_encode($item['spe_workdays'], JSON_UNESCAPED_UNICODE),
                        'spe_offdays'  => json_encode($item['spe_offdays'], JSON_UNESCAPED_UNICODE),
                        'range_info'   => json_encode($item['range'], JSON_UNESCAPED_UNICODE), // range是关键字，不要用！！！
                        'white_users'  => json_encode($item['white_users'], JSON_UNESCAPED_UNICODE),
                        'ot_info'      => json_encode($item['ot_info'], JSON_UNESCAPED_UNICODE),
                        'ori_group'    => json_encode($item, JSON_UNESCAPED_UNICODE),
                        'update_time'  => date('Y-m-d H:i:s'), // 强制更新时间
                    ]);
                } else {
                    $cdb->insert([
                        'groupid'      => $item['groupid'],
                        'groupname'    => $item['groupname'],
                        'grouptype'    => $item['grouptype'],
                        'checkindate'  => json_encode($item['checkindate'], JSON_UNESCAPED_UNICODE),
                        'spe_workdays' => json_encode($item['spe_workdays'], JSON_UNESCAPED_UNICODE),
                        'spe_offdays'  => json_encode($item['spe_offdays'], JSON_UNESCAPED_UNICODE),
                        'range_info'   => json_encode($item['range'], JSON_UNESCAPED_UNICODE),
                        'white_users'  => json_encode($item['white_users'], JSON_UNESCAPED_UNICODE),
                        'ot_info'      => json_encode($item['ot_info'], JSON_UNESCAPED_UNICODE),
                        'ori_group'    => json_encode($item, JSON_UNESCAPED_UNICODE),
                    ]);
                }
            }
            // 更新同步时间
            $cdb->table('sync_time')->insert([
                'type'        => 1,
                'status'      => 1,
                'month'       => $month,
                'finish_time' => date('Y-m-d H:i:s'),
            ]);
        }
        return $res;
    }

    // 获取假期
    function getVacation()
    {
        $token = $this->getToken();
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/oa/vacation/getcorpconf' . '?access_token=' . $token;

        //发送消息
        $res = requestHttp($url, 'post', json_encode([]));
        if ($res['errcode'] == 0) {
            $cdb = dbCMysql::getInstance();
            // 获取所有假期
            $ori_vacation = $cdb->table('vacation')->field('id')->list();
            $ori_vacation = array_column($ori_vacation, 'id');
            $vacation = $res['lists'];

            foreach ($vacation as $item) {
                if (in_array($item['id'], $ori_vacation)) {
                    $cdb->table('vacation')->where('id = :id ', ['id' => $item['id']])->update([
                        'name'                  => $item['name'],
                        'time_attr'             => $item['time_attr'],
                        'duration_type'         => $item['duration_type'],
                        'quota_attr'            => json_encode($item['quota_attr'], JSON_UNESCAPED_UNICODE),
                        'perday_duration'       => $item['perday_duration'],
                        'is_newovertime'        => $item['is_newovertime'],
                        'enter_comp_time_limit' => $item['enter_comp_time_limit'],
                        'expire_rule'           => json_encode($item['expire_rule'], JSON_UNESCAPED_UNICODE),
                        'ori_data'              => json_encode($item, JSON_UNESCAPED_UNICODE),
                    ]);
                } else {
                    $id = $cdb->table('vacation')->insert([
                        'id'                    => $item['id'],
                        'name'                  => $item['name'],
                        'time_attr'             => $item['time_attr'],
                        'duration_type'         => $item['duration_type'],
                        'quota_attr'            => json_encode($item['quota_attr'], JSON_UNESCAPED_UNICODE),
                        'perday_duration'       => $item['perday_duration'],
                        'is_newovertime'        => $item['is_newovertime'],
                        'enter_comp_time_limit' => $item['enter_comp_time_limit'],
                        'expire_rule'           => json_encode($item['expire_rule'], JSON_UNESCAPED_UNICODE),
                        'ori_data'              => json_encode($item, JSON_UNESCAPED_UNICODE),
                    ]);
                }
            }
        }
        return $res;
    }

    function getApproval($month, $value, $new_next_cursor)
    {
        $token = $this->getToken();
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/oa/getapprovalinfo' . '?access_token=' . $token;
        $data = [
            "starttime"  => strtotime($month . '-01 00:00:00'),
            "endtime"    => strtotime(date('Y-m-t 23:59:59', strtotime($month))),
            "filters"    => [
                ["key" => "template_id", "value" => $value], // 补卡
            ],
            "new_cursor" => $new_next_cursor,
            "size"       => 100,
        ];

        return requestHttp($url, 'post', json_encode($data));
    }

    function getApprovalDetail($no)
    {
        $token = $this->getToken();
        $data = [
            "sp_no" => $no,
        ];
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/oa/getapprovaldetail' . '?access_token=' . $token;
        $res = requestHttp($url, 'post', json_encode($data));
        if ($res['errcode'] == 0) {
            $cdb = dbCMysql::getInstance();
            $detail = $cdb->table('approvel')->where('sp_no = :sp_no', ['sp_no' => $no])->one();
            if ($detail) {
                $cdb->table('approvel')->where('sp_no = :sp_no', ['sp_no' => $no])->update([
                    'sp_name'       => $res['info']['sp_name'],
                    'sp_status'     => $res['info']['sp_status'],
                    'qw_user_id'    => $res['info']['applyer']['userid'],
                    'template_id'   => $res['info']['template_id'],
                    'apply_time'    => $res['info']['apply_time'],
                    'applyer'       => json_encode($res['info']['applyer'], JSON_UNESCAPED_UNICODE),
                    'batch_applyer' => json_encode($res['info']['batch_applyer'], JSON_UNESCAPED_UNICODE),
                    'sp_record'     => json_encode($res['info']['sp_record'], JSON_UNESCAPED_UNICODE),
                    'notifyer'      => json_encode($res['info']['notifyer'], JSON_UNESCAPED_UNICODE),
                    'apply_data'    => json_encode($res['info']['apply_data'], JSON_UNESCAPED_UNICODE),
                    'comments'      => json_encode($res['info']['comments'], JSON_UNESCAPED_UNICODE),
                    'ori_data'      => json_encode($res['info'], JSON_UNESCAPED_UNICODE),
                ]);
            } else {
                $cdb->table('approvel')->insert([
                    'sp_no'         => $no,
                    'sp_name'       => $res['info']['sp_name'],
                    'sp_status'     => $res['info']['sp_status'],
                    'qw_user_id'    => $res['info']['applyer']['userid'],
                    'template_id'   => $res['info']['template_id'],
                    'apply_time'    => $res['info']['apply_time'],
                    'applyer'       => json_encode($res['info']['applyer'], JSON_UNESCAPED_UNICODE),
                    'batch_applyer' => json_encode($res['info']['batch_applyer'], JSON_UNESCAPED_UNICODE),
                    'sp_record'     => json_encode($res['info']['sp_record'], JSON_UNESCAPED_UNICODE),
                    'notifyer'      => json_encode($res['info']['notifyer'], JSON_UNESCAPED_UNICODE),
                    'apply_data'    => json_encode($res['info']['apply_data'], JSON_UNESCAPED_UNICODE),
                    'comments'      => json_encode($res['info']['comments'], JSON_UNESCAPED_UNICODE),
                    'ori_data'      => json_encode($res['info'], JSON_UNESCAPED_UNICODE),
                ]);
            }
        }
        return $res;
    }

    // 获取用户打卡数据
    function getUserCheckin($id, $wid, $month)
    {
        $start_time = strtotime($month . '-01 00:00:00');
        $end_time = strtotime(date('Y-m-t 23:59:59', strtotime($month)));

        $cdb = dbCMysql::getInstance();
        $checkin_table = 'user_checkin_data_' . date('Y', strtotime($month));
        $checkin_list = $cdb->table($checkin_table)
            ->where('where user_id = :user_id and checkin_date like :month', ['user_id' => $wid, 'month' => '%' . $month. '%'])->list();
        $checkin_list = array_column($checkin_list, 'id', 'checkin_date');

        $token = $this->getToken();
        $url = $this->qw_config['get_checkin_day_data_url'] . '?access_token=' . $token;

        $data = [
            'starttime'  => $start_time,
            'endtime'    => $end_time,
            'useridlist' => $wid,
        ];
        $res = requestHttp($url, 'post', json_encode($data));
        $check_users = [];
        if ($res['errcode'] == 0) {
            $checkindata = $res['datas'];
            $cdb = dbCMysql::getInstance();
            foreach ($checkindata as $item) {
                if (!isset($check_users[$wid])) {
                    $check_users[$wid] = [
                        'wid'          => $wid,
                        'name'         => $item['base_info']['name'],
                        'id'           => $userMap[$item['base_info']['acctid']]['id'] ?? 0,
                        'departs_name' => $item['base_info']['departs_name'],
                        'departs_ids'  => $userMap[$item['base_info']['acctid']]['wdepartment_ids'] ?? '',
                        'position'     => $userMap[$item['base_info']['acctid']]['position'] ?? '',
                    ];
                }
                $checkin_date = date('Y-m-d', $item['base_info']['date']);
                if (!array_key_exists($checkin_date, $checkin_list))  {
                    $cdb->table($checkin_table)->insert([
                        'checkin_date'    => $checkin_date,
                        'user_id'         => $item['base_info']['acctid'],
                        'base_info'       => json_encode($item['base_info'], JSON_UNESCAPED_UNICODE),
                        'summary_info'    => json_encode($item['summary_info'], JSON_UNESCAPED_UNICODE),
                        'holiday_infos'   => json_encode($item['holiday_infos'], JSON_UNESCAPED_UNICODE),
                        'exception_infos' => json_encode($item['exception_infos'], JSON_UNESCAPED_UNICODE),
                        'ot_info'         => json_encode($item['ot_info'], JSON_UNESCAPED_UNICODE),
                        'sp_items'        => json_encode($item['sp_items'], JSON_UNESCAPED_UNICODE),
                        'ori_data'        => json_encode($item, JSON_UNESCAPED_UNICODE),
                    ]);
                }
            }
            if (!empty($check_users)) {
                $check_users = array_values($check_users);
                $cdb->table('checkin_user')->insert([
                    'users'       => json_encode($check_users, JSON_UNESCAPED_UNICODE),
                    'month'       => $month,
                    'create_time' => date('Y-m-d H:i:s'),
                ]);
            }
        }
        return $res;
    }


}