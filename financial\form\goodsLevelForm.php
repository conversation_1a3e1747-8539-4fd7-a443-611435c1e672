<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/31 9:48
 */

namespace financial\form;

use core\lib\db\dbFMysql;
use financial\models\mskuReportModel;
use financial\models\tableDataModel;
use financial\models\userModel;
use setasign\Fpdi\PdfParser\Filter\Flate;

//用于产品等级的更新
class goodsLevelForm
{
    public static string $m_date;//计算的月份
    public static array $month_list = []; //规则用到的月份（本月起的倒叙）（查询用）
    public static array $year_list = []; //规则用到的时间（查询用）
    public static array $c_ids = []; //规则用到的字段id（查询用）
    public static array $market_ids = [];//规则国家id（查询用）
    public static array $country_code= []; //规则中涉及到的国家编码（查询用）
    public static array $lx_keys = [];//领星字段（查询用）
    public static array $oa_keys = [];//oa字段（查询用）
    public static array $oa_custom_ids= [];//要查询的自定义id
    public static array $oa_custom_keys = [];//需要重新计算的键加规则

    public static int $leve_type;//维度
    public static string $leve_type_key;//维度对应的key
    public static array $rules = []; //整理后的规则
    public static array $lx_list = [];
    public static array $oa_list = [];
    public static string $return_msg = '';
    //根据等级规则修改商品等级
    public static function updateGoodsLevel($id,$m_date) {
        self::$m_date = $m_date;
        $db = dbFMysql::getInstance();
        $data = $db->table('goods_level')
            ->where('where id =:id and status = 1 and is_delete = 0',['id'=>$id])
            ->one();
        if (!$data) {
            self::$return_msg = "未找到此等级";
            return false;
        }
        $start_time = strtotime($m_date);
        if ($data['start_time'] != $start_time) {
            self::$return_msg = "该等级不属于{$m_date}";
            return false;
        }
        self::getRules($data);
        //没有字段在规则中，规则就是问题规则计算
        if (count(self::$c_ids)) {
            //字段整理，规则整理
            self::getColumn();
            //数据查询
            self::getMskuData();
            //计算统计符合规则的sku数据
            $res = self::getSkuByRules();

            //设置等级数据
            $data['m_date'] = $m_date;
            self::setLevelRelation($id,$res,$data);
        }
    }
    //查询规则
    public static function getRules($data) {
        $rules = json_decode($data['rules'],true);
        //获取所有列
        $month_type = [];//月份，要用到数据的月份（上月，连续两月..）
        $market_ids = []; //国家
        $c_ids = []; //用的到字段id
        foreach ($rules as $k=>$v) {
            foreach ($v['conditions'] as $v1) {
                foreach ($v1['group'] as $v2) {
                    $c_ids[] = $v2['index'];
                    $month_type[] = $v2['compare_month'];
                    if ($v['market_id'] != ["-1"]) {
                        $market_ids = array_merge($market_ids,$v['market_id']);
                    } else {
                        $rules[$k]['market_id'] = [];
                    }
                }
            }
        }
        self::$rules = $rules;
        //月份整理
        $current_month = self::$m_date;
        $year = explode('-',$current_month)[0];
        $year_list[$year][] = $current_month;
        $month_list[] = $current_month;
        if (in_array(3,$month_type) || in_array(1,$month_type)) {
            //上月
            $month = date('Y-m',strtotime("$current_month-01 -1 month"));
            $year = explode('-',$month)[0];
            $month_list[] = $month;
            $year_list[$year][] = $month;
        }
        if (in_array(4,$month_type)) {
            //上上月
            $month =  date('Y-m', strtotime("$current_month-01 -1 month"));
            $month_list[] = $month;
            $year = explode('-',$month)[0];
            $year_list[$year][] = $month;
            $month =  date('Y-m', strtotime("$current_month-01 -2 month"));
            $month_list[] = $month;
            $year = explode('-',$month)[0];
            $year_list[$year][] = $month;
            $year_list[$year] = array_values(array_unique($year_list[$year]));
        }
        self::$month_list = array_values(array_unique($month_list));
        self::$year_list = $year_list;
        self::$c_ids = $c_ids;
        self::$leve_type = $data['type'];
        if ($data['type'] == 1) {
            self::$leve_type_key = 'asin';
        } elseif ($data['type'] == 2) {
            self::$leve_type_key = 'p_asin';
        } else {
            self::$leve_type_key = 'sku';
        }
        self::$market_ids = $market_ids;
        foreach ($year_list as $year=>$v) {
            tableDataModel::creatTableMonth($year);
        }
    }
    //查询字段整理数据，//字段整理，规则整理
    public static function getColumn() {
        //获取字段
        $db = dbFMysql::getInstance();
        $column = $db->table('column')
            ->whereIn('id',self::$c_ids)
            ->field('id,key_name,custom_id,show_type')
            ->list();
        $lx_keys = [];
        $oa_keys = [];
        $all_keys = [];//所有用到的字段
        $custom_column_ids = [];//自定义字段
        $oa_custom_ids = [];//查询的oa自定义字段id数据
        foreach ($column as $v) {
            $all_keys[$v['id']] = $v['key_name'];
            if ($v['custom_id'] > 0) {
                $custom_column_ids[] = $v['custom_id'];
                //比例字段和客单价字段(需要重新计算)
//                if ($v['show_type'] == 2 || $v['key_name'] == 'oa_key_5') {
//                    $custom_column_id[] = $v['custom_id'];
//                } else {
//                    $oa_keys[] = $v['key_name'];
//                    $c_array = explode('_',$v['key_name']);
//                    $oa_custom_ids[] = end($c_array);
//                }
            } else {
                $lx_keys[] = $v['key_name'];
            }
        }
        //自定义等级规则字段获取
        $oa_custom_keys = [];
        if (count($custom_column_ids)) {
            $custom_column_list = $db->table('custom_column')
                ->whereIn('id',$custom_column_ids)
                ->list();
            foreach ($custom_column_list as $v) {
                $relation_column = json_decode($v['relation_column']);
                if ($v['sort'] == 5 || $v['sort'] == 6 || $v['show_type'] == 2 || in_array('oa_key_5',$relation_column)) {
                    $oa_custom_keys['oa_key_'.$v['id']] = [
                        'show_type'=>$v['show_type'],
                        'rules'=>json_decode($v['rules'],true),
                    ];
                    foreach ($relation_column as $vv) {
                        if (strpos($vv,'oa_key') !== false) {
                            $oa_keys[] = $vv;
                            $array_custom_ = explode('_',$vv);
                            $oa_custom_ids[] = end($array_custom_);
                        } else {
                            $lx_keys[] = $vv;
                        }
                    }
                } else {
                    $oa_custom_ids[] = $v['id'];
                }
            }
        }
        self::$oa_custom_keys = $oa_custom_keys;
        //毛利润增加合并计算字段
        if (in_array('oa_key_4',$oa_keys)) {
            $lx_keys = array_merge($lx_keys,customColumnForm::$aggregation_keys);
        }
        $lx_keys = array_unique($lx_keys);
        $oa_keys = array_unique($oa_keys);
        self::$oa_custom_ids = $oa_custom_ids;
        self::$lx_keys = $lx_keys;
        self::$oa_keys = $oa_keys;
        if (count(self::$market_ids)) {
            $country_list  = $db->table('market')
                ->whereIn('id',self::$market_ids)
                ->list();
            foreach ($country_list as $v) {
                $country_[$v['id']] = $v['code'];
            }
            self::$country_code = array_unique($country_);
        } else {
            $country_list  = $db->table('market')
                ->list();
            foreach ($country_list as $v) {
                $country_[$v['id']] = $v['code'];
            }
            self::$country_code = array_column($country_list,'code');
        }
        //规则整理
        foreach (self::$rules as &$v) {
            $v['country_code'] = [];
            if (count($v['market_id'])) {
                foreach ($v['market_id'] as $market_id) {
                    $v['country_code'][] = $country_[$market_id];
                }
            } else {
                $v['country_code'] = self::$country_code;
            }
            foreach ($v['conditions'] as &$v1) {
                foreach ($v1['group'] as &$v3) {
                    $v3['column_key'] = $all_keys[$v3['index']];
                }
            }
        }
    }
    //数据查询
    public static function getMskuData() {
        $db = dbFMysql::getInstance();
        //领星数据
        $lx_list = [];
        $lx_c_list = [];
//        if (count(self::$lx_keys)) {
        //组别和字段整理
        if (self::$leve_type == 1) {
            $fields = 'asin,countryCode as country_code,reportDateMonth as m_date,';
            $groupby = ['asin','countryCode','reportDateMonth'];
        } elseif (self::$leve_type == 2) {
            $fields = 'parentAsin as p_asin,countryCode as country_code,reportDateMonth as m_date,';
            $groupby = ['parentAsin','countryCode','reportDateMonth'];
        } else {
            $fields = 'localSku as sku,countryCode as country_code,reportDateMonth as m_date,';
            $groupby = ['localSku','countryCode','reportDateMonth'];
        }
        $where_array = [];
        foreach (self::$lx_keys as $key_) {
            $where_array[] = "sum($key_) as $key_";
        }
        if (count($where_array)) {
            $fields .= implode(',',$where_array);
        }
        foreach (self::$year_list as $year=>$m_date_list) {
            $db->table("table_month_count_$year")
                ->where("where is_delete = 0")
                ->whereIn('reportDateMonth',self::$month_list);
            if (count(self::$country_code)) {
                $db->whereIn('countryCode', self::$country_code);
            }
            $db->field($fields)->groupBy($groupby);
            $list = $db->list();
            $lx_list = array_merge($lx_list,$list);
        }
        foreach ($lx_list as $v) {
            $key_m = $v[self::$leve_type_key].'_'.$v['m_date'];
            $country_code = $v['country_code']??'-1';
            $lx_c_list[$country_code][$key_m] = $v;
        }
//        }
        //oa数据
        $oa_list = [];
        $oa_c_list = [];
        if (count(self::$oa_custom_ids)) {
            //分组和字段
            if (!count(self::$country_code)) {
                //分组
                if (self::$leve_type == 1) {
                    $fields = 'asin,m_date,custom_id,sum(custom_val) as total';
                    $groupby = ['asin','custom_id','m_date'];
                } elseif (self::$leve_type == 2) {
                    $fields = 'p_asin,m_date,custom_id,sum(custom_val) as total';
                    $groupby = ['p_asin','custom_id','m_date'];
                } else {
                    $fields = 'sku,m_date,custom_id,sum(custom_val) as total';
                    $groupby = ['sku','custom_id','m_date'];
                }
            } else {
                //分组
                if (self::$leve_type == 1) {
                    $fields = 'asin,m_date,custom_id,country_code,sum(custom_val) as total';
                    $groupby = ['asin','country_code','custom_id','m_date'];
                } elseif (self::$leve_type == 2) {
                    $fields = 'p_asin,m_date,custom_id,country_code,sum(custom_val) as total';
                    $groupby = ['p_asin','country_code','m_date','custom_id'];
                } else {
                    $fields = 'sku,m_date,custom_id,country_code,sum(custom_val) as total';
                    $groupby = ['sku','country_code','custom_id','m_date'];
                }
            }
            foreach (self::$year_list as $year=>$m_date_list) {
                $db->table("custom_val_$year")
//                    ->where("asin = 'B0BBFSK9H2'")
                    ->whereIn('custom_id',self::$oa_custom_ids)
                    ->whereIn('m_date',self::$month_list);
                //国家
                if (count(self::$country_code)) {
                    $db->whereIn('country_code',self::$country_code);
                }
                $db->field($fields)->groupBy($groupby);
                $list = $db->list();
                $oa_list = array_merge($oa_list,$list);
            }

            foreach ($oa_list as $v) {
                $key_m = $v[self::$leve_type_key].'_'.$v['m_date'];
                $country_code = $v['country_code']??'-1';
                $oa_key_ = 'oa_key_'.$v['custom_id'];
                if (isset($oa_c_list[$country_code][$key_m])) {
                    $oa_c_list[$country_code][$key_m][$oa_key_] = $v['total'];
                } else {
                    $v[$oa_key_] = $v['total'];
                    unset($v['total']);
                    $oa_c_list[$country_code][$key_m] = $v;
                }
            }
        }
        self::$lx_list = $lx_c_list;
        self::$oa_list = $oa_c_list;
    }
    //计算统计符合规则的sku数据
    public static function getSkuByRules() {
        $sku_list = [];
        foreach (self::$rules as $v) {
            $contry_code = $v['country_code'];
            if (!count($contry_code)) {
                $contry_code = [-1];
            }
            foreach ($contry_code as $c_code) {
                $weidu_array_all = [];//最终的维度对应的数据集合
                foreach ($v['conditions'] as $k=>$c1) {
                    $weidu_array = [];
                    foreach ($c1['group'] as $k1=>$g1) {
                        $weidu_array_g = self::getValRow($c_code,$g1);
                        if ($k1 > 0) {
                            if ($g1['type'] == 1) {
                                $weidu_array = array_values(array_intersect($weidu_array,$weidu_array_g));
                            } else {
                                $weidu_array = array_values(array_merge($weidu_array,$weidu_array_g));
                            }
                        } else {
                            $weidu_array = $weidu_array_g;
                        }
                    }
                    if ($k == 0) {
                        $weidu_array_all = $weidu_array;
                    } else {
                        if ($c1['type'] == 1) {
                            $weidu_array_all = array_values(array_intersect($weidu_array_all,$weidu_array));
                        } else {
                            $weidu_array_all = array_values(array_merge($weidu_array_all,$weidu_array));
                        }
                    }
                }
                $sku_list[$c_code] = array_unique($weidu_array_all);
            }
            //全部国家就循环一次
            if ($contry_code[0] == -1) {
                break;
            }
        }
        return $sku_list;
    }
    //规则最小单位获取结果
    public static function getValRow(string $contry_code,array $rule_row) {
        $weidu_array = [];//满足该条件的维度数据
        $compare_month = $rule_row['compare_month'];//等级规则的月份选择，枚举level_rules_month
        $interval_value = (int)$rule_row['Interval_value'];//自定义值
        $reference = (int)$rule_row['symbol'];//是否绝对值）
        $symbol = (int)$rule_row['reference'];//比较符号
        $column_key = $rule_row['column_key'];
        $res2_data = [];
        if (in_array($rule_row['column_key'],self::$lx_keys)) {
            //计算字段在lx字段中
            $res_data = self::$lx_list[$contry_code]??[];
        } else {
            //计算字段在自定义字段中
            $res_data = self::$oa_list[$contry_code]??[];
            $res2_data = self::$lx_list[$contry_code]??[];
        }
        if (count($res_data)) {
            foreach ($res_data as $c_data) {
                if ($c_data['m_date'] != self::$month_list[0]) {
                    continue;
                }
                $weidu_key = $c_data[self::$leve_type_key];
                $m_val_1 = 0;//本月数据
                $m_val_2 = 0;//上月数据
                $m_val_3 = 0;//上上月数据
                if (isset(self::$month_list[0])) {
                    $key_m = $weidu_key.'_'.self::$month_list[0];
                    if (!(isset($res_data[$key_m]) && isset($res2_data[$key_m]))) {
                        continue;
                    }
                    $row_data = array_merge($res_data[$key_m],$res2_data[$key_m]);
                    $m_val_1 = $row_data[$column_key]??0;
                    if ($rule_row['column_key'] == 'oa_key_4') {
                        //毛利率
                        $aggregation_keys = customColumnForm::$aggregation_keys;
                        foreach ($aggregation_keys as $cus_key) {
                            $m_val_1 += (float)($row_data[$cus_key]??0);
                        }
                    } elseif (isset(self::$oa_custom_keys[$rule_row['column_key']])) {
                        //根据规则获取数据
                        if (isset($row_data['oa_key_4'])) {
                            $aggregation_keys = customColumnForm::$aggregation_keys;
                            foreach ($aggregation_keys as $cus_key) {
                                $row_data['oa_key_4'] += ((float)$row_data[$cus_key]??0);
                            }
                        }
                        $column_rules = self::$oa_custom_keys[$rule_row['column_key']];
                        //计算数据
                        $show_type = $column_rules['show_type'];
                        $rule_data = $column_rules['rules'][0]['rules'];
                        foreach ($rule_data as &$rule_) {
                            if ($rule_['group_type'] == 1) {
                                $rule_['val'] = rulesDataForm::getValByCustomRule($rule_,$row_data);
                            } else {
                                foreach ($rule_['list'] as &$rule_l) {
                                    $rule_l['val'] =  rulesDataForm::getValByCustomRule($rule_l,$row_data);
                                }
                            }
                        }
                        if ($show_type == 2){
                            $total = roundToString(coustomColumnJobForm::getValue($rule_data)*100);
                        } else {
                            $total = roundToString(coustomColumnJobForm::getValue($rule_data));
                        }
                        $m_val_1 = $total;
                    } else {
                        $m_val_1 = $row_data[$column_key]??0;
                    }
                }
                if (isset(self::$month_list[1])) {
                    $key_m = $weidu_key.'_'.self::$month_list[1];
                    if (!(isset($res_data[$key_m]) && isset($res2_data[$key_m]))) {
                        continue;
                    }
                    $row_data = array_merge($res_data[$key_m],$res2_data[$key_m]);
                    $m_val_2 = $res_data[$column_key]??0;
                    if ($rule_row['column_key'] == 'oa_key_4') {
                        $aggregation_keys = customColumnForm::$aggregation_keys;
                        foreach ($aggregation_keys as $cus_key) {
                            $m_val_2 += (float)($row_data[$cus_key]??0);
                        }
                    } elseif (isset(self::$oa_custom_keys[$rule_row['column_key']])) {
                        if (isset($row_data['oa_key_4'])) {
                            $aggregation_keys = customColumnForm::$aggregation_keys;
                            foreach ($aggregation_keys as $cus_key) {
                                $row_data['oa_key_4'] += ((float)$row_data[$cus_key]??0);
                            }
                        }
                        //根据规则获取数据
                        $column_rules = self::$oa_custom_keys[$rule_row['column_key']];
                        //计算数据
                        $show_type = $column_rules['show_type'];
                        $rule_data = $column_rules['rules'][0]['rules'];
                        foreach ($rule_data as &$rule_) {
                            if ($rule_['group_type'] == 1) {
                                $rule_['val'] = rulesDataForm::getValByCustomRule($rule_,$row_data);
                            } else {
                                foreach ($rule_['list'] as &$rule_l) {
                                    $rule_l['val'] =  rulesDataForm::getValByCustomRule($rule_l,$row_data);
                                }
                            }
                        }
                        if ($show_type == 2){
                            $total = roundToString(coustomColumnJobForm::getValue($rule_data)*100);
                        } else {
                            $total = roundToString(coustomColumnJobForm::getValue($rule_data));
                        }
                        $m_val_2 = $total;
                    } else {
                        $m_val_2 = $row_data[$column_key]??0;
                    }
                }
                if (isset(self::$month_list[2])) {
                    $key_m = $weidu_key.'_'.self::$month_list[2];
                    if (!(isset($res_data[$key_m]) && isset($res2_data[$key_m]))) {
                        continue;
                    }
                    $row_data = array_merge($res_data[$key_m],$res2_data[$key_m]);
                    $m_val_3 = $row_data[$column_key]??0;
                    if ($rule_row['column_key'] == 'oa_key_4') {
                        $aggregation_keys = customColumnForm::$aggregation_keys;
                        foreach ($aggregation_keys as $cus_key) {
                            $m_val_3 += (float)($row_data[$cus_key]??0);
                        }
                    } elseif (isset(self::$oa_custom_keys[$rule_row['column_key']])) {
                        //根据规则获取数据
                        if (isset($row_data['oa_key_4'])) {
                            $aggregation_keys = customColumnForm::$aggregation_keys;
                            foreach ($aggregation_keys as $cus_key) {
                                $row_data['oa_key_4'] += ((float)$row_data[$cus_key]??0);
                            }
                        }
                        $column_rules = self::$oa_custom_keys[$rule_row['column_key']];
                        //计算数据
                        $show_type = $column_rules['show_type'];
                        $rule_data = $column_rules['rules'][0]['rules'];
                        foreach ($rule_data as &$rule_) {
                            if ($rule_['group_type'] == 1) {
                                $rule_['val'] = rulesDataForm::getValByCustomRule($rule_,$row_data);
                            } else {
                                foreach ($rule_['list'] as &$rule_l) {
                                    $rule_l['val'] =  rulesDataForm::getValByCustomRule($rule_l,$row_data);
                                }
                            }
                        }
                        if ($show_type == 2){
                            $total = roundToString(coustomColumnJobForm::getValue($rule_data)*100);
                        } else {
                            $total = roundToString(coustomColumnJobForm::getValue($rule_data));
                        }
                        $m_val_3 = $total;
                    } else {
                        $m_val_3 = $row_data[$column_key]??0;
                    }

                }
                $value1 = $rule_row['value1'];
                $value2 = $rule_row['value2'];
                if ($compare_month == 1 || $compare_month == 2) {
                    if ($compare_month == 1) {
                        //与上月相比
                        $res_val = $m_val_1 - $m_val_2;
                    } else {
                        //与自定义数值相比
                        $res_val = $m_val_1 - $interval_value;
                    }
                    if ($reference == 3) {
                        $res_val = abs($res_val);
                    }
                    switch ($symbol) {
                        case 1: //大于
                            if ($res_val > $value1) {
                                $weidu_array[] = $weidu_key;
                            }
                            break;
                        case 2:
                            if ($res_val >= $value1) {
                                $weidu_array[] = $weidu_key;
                            }
                            break;
                        case 3:
                            if ($res_val < $value1) {
                                $weidu_array[] = $weidu_key;
                            }
                            break;
                        case 4:
                            if ($res_val <= $value1) {
                                $weidu_array[] = $weidu_key;
                            }
                            break;
                        case 5:
                            if ($res_val == $value1) {
                                $weidu_array[] = $weidu_key;
                            }
                            break;
                        case 6:
                            if ($res_val >= $value1 && $res_val <= $value2) {
                                $weidu_array[] = $weidu_key;
                            }
                            break;
                    }
                }
                if ($compare_month == 3 || $compare_month == 4) {
                    //3连续2月，4-连续3月
                    if ($reference == 3) {
                        $m_val_1 = abs($m_val_1);
                        $m_val_2 = abs($m_val_2);
                        $m_val_3 = abs($m_val_3);
                    }
                    switch ($symbol) {
                        case 1://大于
                            if ($compare_month == 3) {
                                if ($m_val_1 > $value1 && $m_val_2 > $value1) {
                                    $weidu_array[] = $weidu_key;
                                }
                            } else {
                                if ($m_val_1 > $value1 && $m_val_2 > $value1 && $m_val_3 > $value1) {
                                    $weidu_array[] = $weidu_key;
                                }
                            }
                            break;
                        case 2://大于等于
                            if ($compare_month == 3) {
                                if ($m_val_1 >= $value1 && $m_val_2 >= $value1) {
                                    $weidu_array[] = $weidu_key;
                                }
                            } else {
                                if ($m_val_1 >= $value1 && $m_val_2 >= $value1 && $m_val_3 >= $value1) {
                                    $weidu_array[] = $weidu_key;
                                }
                            }
                            break;
                        case 3://小于
                            if ($compare_month == 3) {
                                if ($m_val_1 < $value1 && $m_val_2 < $value1) {
                                    $weidu_array[] = $weidu_key;
                                }
                            } else {
                                if ($m_val_1 < $value1 && $m_val_2 < $value1 && $m_val_3 < $value1) {
                                    $weidu_array[] = $weidu_key;
                                }
                            }
                            break;
                        case 4://小于等于
                            if ($compare_month == 3) {
                                if ($m_val_1 <= $value1 && $m_val_2 <= $value1) {
                                    $weidu_array[] = $weidu_key;
                                }
                            } else {
                                if ($m_val_1 <= $value1 && $m_val_2 <= $value1 && $m_val_3 <= $value1) {
                                    $weidu_array[] = $weidu_key;
                                }
                            }
                            break;
                        case 5:
                            if ($compare_month == 3) {
                                if ($m_val_1 == $value1 && $m_val_2 == $value1) {
                                    $weidu_array[] = $weidu_key;
                                }
                            } else {
                                if ($m_val_1 == $value1 && $m_val_2 == $value1 && $m_val_3 == $value1) {
                                    $weidu_array[] = $weidu_key;
                                }
                            }
                            break;
                        case 6:
                            if ($compare_month == 3) {
                                if ($m_val_1 >= $value1 && $m_val_2 >= $value1 && $m_val_1 <= $value2 && $m_val_2 <= $value2) {
                                    $weidu_array[] = $weidu_key;
                                }
                            } else {
                                if ($m_val_1 >= $value1 && $m_val_2 >= $value1 && $m_val_3 >= $value1 && $m_val_1 <= $value2 && $m_val_2 <= $value2 && $m_val_3 <= $value2) {
                                    $weidu_array[] = $weidu_key;
                                }
                            }
                            break;
                    }
                }
            }
        }
//        if (count($weidu_array)) {
//            dd([$m_val_1,$m_val_2,$m_val_3,$value1,$compare_month]);
//        }
        return $weidu_array;

    }
    //保存设置等级数据
    public static function setLevelRelation($id,$res_data,$level) {
        //取国家和维度苏剧
        $db = dbFMysql::getInstance();
        //维度不是sku就去查sku
        if (self::$leve_type != 3) {
            $key_ = self::$leve_type == 1?'asin':'parentAsin';
            $msku_list = [];
            foreach ($res_data as $k=>$v) {
                $weidu_data = $v;
                foreach (self::$year_list as $year=>$m_date_list) {
                    $db->table("table_month_count_$year")
                        ->where('where is_delete = 0')
                        ->whereIn($key_,$weidu_data)
                        ->field("localSku as sku,countryCode as country_code,$key_ as weidu_val");
                    if ($k != -1) {
                        $db->andWhere('countryCode = :countryCode',['countryCode'=>$k]);
                    }
                    $group_by = ['localSku','countryCode',"$key_"];
                    $list = $db
                        ->groupBy($group_by)
                        ->list();
                    $msku_list = array_merge($msku_list,$list);
                }
            }
        } else {
            $msku_list = [];
            foreach ($res_data as $k=>$v) {
                foreach ($v as $sku) {
                    $msku_list[] = ['sku'=>$sku,'country_code'=>$k,'weidu_val'=>$sku];
                }
            }
        }
        //删除关系
        $db->table('goods_level_relation')
            ->where('where level_id = :id',['id'=>$id])
            ->delete();
        foreach ($msku_list as $sku_data) {
            $sku = $sku_data['sku'];
            $country_code = $sku_data['country_code'];
            $db->table('goods_level_relation')
                ->insert([
                    'sku'=>$sku,
                    'level_id'=>$id,
                    'country_code'=>$country_code,
                    'weidu_val'=>$sku_data['weidu_val'],
                    'type'=>$level['type'],
                    'level_type'=>$level['level_type'],
                    'm_date'=>$level['m_date']
                ]);
        }
    }
}