<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/13 10:21
 */

namespace financial\form;

use core\lib\db\dbMysql;

class noticeForm
{
    //获取列表数据的通知人
    public static function getNoticeUser($list) {
        $user_ids = [];
        foreach ($list as &$noctice) {
            $noctice['user_ids1'] = json_decode($noctice['user_ids1']);
            $noctice['user_ids2'] = json_decode($noctice['user_ids2']);
            $user_ids = array_merge($user_ids,$noctice['user_ids1'],$noctice['user_ids2']);
        }
        $user_ids = array_unique($user_ids);
        $db = dbMysql::getInstance();
        $user_list = $db->table('qwuser')
            ->whereIn('id',$user_ids)
            ->field('id,wid,wname')
            ->list();
        foreach ($list as &$noctice) {
            $noctice['user_list1'] = [];
            $noctice['user_list2'] = [];
            foreach ($user_list as $user) {
                if (in_array($user['id'],$noctice['user_ids1'])) {
                    $noctice['user_list1'][] = $user;
                }
                if (in_array($user['id'],$noctice['user_ids2'])) {
                    $noctice['user_list2'][] = $user;
                }
            }
        }
        return $list;
    }
}