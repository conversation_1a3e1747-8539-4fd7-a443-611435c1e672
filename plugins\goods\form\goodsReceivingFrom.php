<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/16 15:31
 */

namespace  plugins\goods\form;

use plugins\goods\common\publicMethod;
use plugins\goods\models\userModel;
use core\lib\db\dbMysql;
use core\lib\log;

class goodsReceivingFrom
{
    /**
     * @param $goods_id
     * @param $goods_name
     * @return void
     * @throws \core\lib\ExceptionError  商品入库编码
     */
    public function changeSampleNo($goods_id,$goods_name,$old_goods_name){
        $db = dbMysql::getInstance();
        $db->table('goods_receiving');
        $db->where('where goods_id=:goods_id and is_delete = 0',['goods_id'=>$goods_id]);
        $list = $db->list();
        $code_str = publicMethod::getFirstPinyin($goods_name).$goods_id;
        $old_code_str = publicMethod::getFirstPinyin($old_goods_name).$goods_id;
        if (count($list)) {
            foreach ($list as $v) {
                $sample_no = str_replace($old_code_str,$code_str,$v['sample_no']);
                $db->query('update oa_goods_receiving set sample_no=:sample_no where id=:id',['id'=>$v['id'],'sample_no'=>$sample_no]);
            }
        }
    }
    //大货样收货
    public static function saveReceiving1(array $param) {
        $flow_path_id = 1;
        $goods_id = (int)$param['goods_id'];
        $sample_batch = (int)$param['sample_batch'];
        if (!in_array($sample_batch,[1,2])) {
            SetReturn('-1','批次不正确');
        }
        $db = dbMysql::getInstance();

        //查看是否绑定过模板
        $goods_project_count = $db->table('goods_project')
            ->where('where is_delete = 0 and goods_id=:goods_id and flow_path_id=:flow_path_id',['goods_id'=>$goods_id,'flow_path_id'=>$flow_path_id])
            ->field('id')
            ->count();
        if ($goods_project_count == 0 && $sample_batch != 1) {
            $db->rollBack();
            SetReturn(-1,'请选择首批收样入库');
        }
        if ($goods_project_count > 0 && $sample_batch == 1) {
            $db->rollBack();
            SetReturn(-1,'该品首批已存在，请选择其次批收样入库');
        }
        if ($sample_batch == 1) {
            $batch_num = 1;
        } else {
            $batch_num = $goods_project_count;
        }
        $goods_info = $db->table('goods_new')
            ->where('where id = :goods_id',['goods_id'=>$goods_id])
            ->field('id,goods_name,manage_info')
            ->one();
        $node_index = '0-0';
        //大货样创建应该绑定的批次(有商品图就选择次批)
        $imgs_request = $db->table('imgs_request')
            ->where('where goods_id=:goods_id and type=1 and is_delete = 0',['goods_id'=>$goods_info['id']])
            ->one();
        if ($imgs_request) {
            $template_data = templateFrom::getTplDataByFlowId($flow_path_id,2,$goods_info['manage_info']);
        } else {
            $template_data = templateFrom::getTplDataByFlowId($flow_path_id,1,$goods_info['manage_info']);
        }
        $matter_name = goodsNewFrom::getMatterName1($goods_info['goods_name'],$template_data['tpl_name'],$sample_batch,$batch_num);
        $goods_info['matter_name'] = $matter_name;
        //创建大货样节点
        $project_data = goodsProjectFrom::addGoodsProjectFor1($goods_id,$goods_info['matter_name'],$template_data,$sample_batch,$batch_num);
        $project_id = $project_data['project_id'];
        $next_node = $project_data['next_node'];
        $next_node_index = $project_data['next_node_index'];
        //新增收货信息
        $insert_data = [
            'user_id'=>userModel::$qwuser_id,
            'goods_id'=>$goods_id,
            'project_id'=>$project_id,
            'flow_path_id'=>$flow_path_id,
            'sample_batch'=>$sample_batch,
            'goods_num'=>(int)$param['goods_num'],
            'color_id'=>$param['color_ids'],
            'description'=>$param['description'],
            'created_time'=>time(),
            'sample_no'=>$param['sample_no'],
        ];
        $db->table('goods_receiving');
        $db->insert($insert_data);
        //完成待办事项
        $db->table('goods_matters')
            ->where('where goods_project_id=:project_id and node_index=:node_index and status=0',['project_id'=>$project_id,'node_index'=>$node_index])
            ->update(['status'=>1,'completion_time'=>time()]);
        //生成待办事项给下个节点+消息发送
        $goods_info['sample_batch'] = $sample_batch;
        if ($next_node) {
            goodsMattersFrom::addNextNodeMatter($project_id,$next_node,$goods_info,$next_node_index);
        }
        //日志记录
        log::setGoodsProjectLog($goods_id,$project_id,"完成收样", $goods_info['matter_name'],8);
        //消息发送
        messagesFrom::sendNextNodeInfo();
    }
    //抽货收货
    public static function saveReceiving3(array $param) {
        $goods_id = (int)$param['goods_id'];
        $project_id = (int)$param['project_id'];
        $flow_path_id = 3;
        $sample_batch = (int)$param['sample_batch'];
        $matter_id = (int)$param['matter_id'];
        if ($project_id == 0 || $matter_id==0) {
            SetReturn(-1,'请选择待办流程');
        }
//        if (!in_array($sample_batch,[1,2])) {
//            SetReturn('-1','批次不正确');
//        }
        //查看是否绑定过模板
        $db = dbMysql::getInstance();
        $goods_info = $db->table('goods_new')
            ->where('where id = :goods_id',['goods_id'=>$goods_id])
            ->field('id,goods_name,manage_info')
            ->one();
        //抽货走流程
        $project_data = $db->query('select * from oa_goods_project where id=:project_id',['project_id'=>$project_id]);
        if (!$project_data) {
            SetReturn(-1,'未找到对应的项目流程');
        }
        $sample_batch = $project_data['sample_batch'];
        $goods_info['matter_name'] = $project_data['matter_name'];
        //待办信息
        $matter = $db->table('goods_matters')
            ->where('where id=:id',['id'=>$matter_id])
            ->one();
        if (!$matter) {
            SetReturn(-1,'未找到对应待办事项');
        }
        $node_index = $matter['node_index'];
        //完成收货节点+更新为当前节点
        $project_data = goodsProjectFrom::setGoodsProjectForReceiving($project_data,$matter['node_index'],$matter['event_index']);
        $project_id = $project_data['project_id'];
        $next_node = $project_data['next_node'];
        $next_node_index = $project_data['next_node_index'];
        //新增收货信息
        $insert_data = [
            'user_id'=>userModel::$qwuser_id,
            'goods_id'=>$goods_id,
            'project_id'=>$project_id,
            'flow_path_id'=>$flow_path_id,
            'sample_batch'=>$sample_batch,
            'goods_num'=>(int)$param['goods_num'],
            'color_id'=>$param['color_ids'],
            'description'=>$param['description'],
            'created_time'=>time(),
            'sample_no'=>$param['sample_no'],
        ];
        $db->table('goods_receiving');
        $db->insert($insert_data);
        //完成待办事项
        $db->table('goods_matters')
            ->where('where goods_project_id=:project_id and node_index=:node_index and status=0',['project_id'=>$project_id,'node_index'=>$node_index])
            ->update(['status'=>1,'completion_time'=>time()]);
        //生成待办事项给下个节点+消息发送
        $goods_info['sample_batch'] = $sample_batch;
        goodsMattersFrom::addNextNodeMatter($project_id,$next_node,$goods_info,$next_node_index);
        //日志记录
        log::setGoodsProjectLog($goods_id,$project_id,"完成收样", $goods_info['matter_name'],8);
        //消息发送
        messagesFrom::sendNextNodeInfo();
    }
}