<?php

/**
 * @author: zhangguoming
 * @Time: 2024/3/27 9:02
 */

namespace plugins\assessment\models;

use core\lib\config;
use core\lib\db\dbAMysql;
class levelRulesModel
{
    public static int $id;
    public static string $rule_name; // 规则名称
    public static string $level_rules; // 等级规则
    /** 实例 level_rules
     * level 绩效等级， alias 等级别名， range 分数区间，第一个闭区间，之后均为左开右闭， base 绩效基数， desc 说明
     * [
     *      {"level":"D", "alias":"差","range":[0,20], "base":"400", "desc":"差的牛马，天天休息"}
     *      {"level":"C", "alias":"一般","range":[20,60], "base":"600", "desc":"差一点的牛马，经常休息"},
     *      {"level":"B", "alias":"良好","range":[60,80], "base":"800", "desc":"一般牛马，偶尔休息"},
     *      {"level":"A", "alias":"优秀","range":[80,100], "base":"1000", "desc":"好的牛马，从不休息"},
     * ]
     * */
    public static bool $has_coefficient; // 是否包含系数, 1包含 0不包含
    public static string $coefficient_rules; // 系数规则
    /** 实例 coefficient_rules
     * coe_name 系数等级，coe_num 绩效系数
     * coe_rules 系数规则 数组，type 1条件 2条件组， symbol 条件连接符号 1或 2且
     *      rules 规则 level_rules_month 系数月份 level 等级 level_rules_symbol 等级规则符号 level_times 等级次数
     * [
     *      {"coe_name":"高级美工",
     *      "coe_num":"1.5",
     *      "coe_rules":[
     *          {"symbol":"","type":1,"rules":[
     *              {"level_rules_month":1,"level":"A","level_rules_symbol":1,"level_times":"3"}
     *          ]},
     *          {"symbol":"2","type":2,"rules":[
     *              {"level_rules_month":1,"level":"A","level_rules_symbol":1,"level_times":"3"},
     *              {"level_rules_month":2,"level":"B","level_rules_symbol":1,"level_times":"3"}
     *          ]}
     *      ]},
     *      {"coe_name":"美工",
     *      "coe_num":"1.2",
     *      "coe_rules":[
     *          {"symbol":"","type":1,"rules":[{"level_rules_month":1,"level":"B","level_rules_symbol":1,"level_times":"3"}]}
     *      ]}
     * ]
     * */
    public static string $default_coefficient; // 默认系数
    public static bool $is_delete; // 1删除 0未删除

     /*
      * 等级规则校验
      */
    public static function checkLevelRules($rules) : bool{
        empty($rules) && returnError('等级规则不能为空');
        $levels = [];
        foreach($rules as $rule) {
            empty($rule['level']) && returnError('等级不能为空');
            empty($rule['alias']) && returnError('等级别名不能为空');
            empty($rule['range']) && returnError('分数区间不能为空');
            empty($rule['base']) && returnError('绩效基数不能为空');
//            empty($rule['desc']) && returnError('说明不能为空');

            // range 至少有两个值, 且第一个值小于第二个值
            count($rule['range']) < 2 && returnError('分数区间至少有两个值');
            if ($rule['range'][1] == 'INF') $rule['range'][1] = INF;
            if ($rule['range'][0] == '-INF') $rule['range'][0] = -INF;
            if ($rule['range'][0] > $rule['range'][1]) returnError('分数区间第一个值必须小于等于第二个值');
            if (in_array($rule['level'], $levels)) returnError('等级不能重复');
            $levels[] = $rule['level'];
        }
        return true;
    }

    /*
     * 系数规则校验
     */
    public static function checkCoefficientRules($rules) : bool
    {
        empty($rules) && returnError('系数规则不能为空');
        $coe_levels = [];
        foreach ($rules as $rule) {
            if (empty($rule['coe_name']) || empty($rule['coe_num']) || empty($rule['coe_rules'])) {
                returnError('系数等级、绩效系数、系数规则不能为空');
            }
            $idx = 0;
            foreach ($rule['coe_rules'] as $r) {
                if (empty($r['type'])) {
                    returnError('系数规则类型不能为空');
                }
                // 第二条开始，symbol不能为空
                if ($idx && empty($r['symbol'])) {
                    returnError('系数规则连接符号不能为空');
                }
                // 条件组
                if ($r['type'] == 2) {
                    // 需要检验内连接符号
                    empty($r['inner_symbol']) && returnError('条件组连接符号不能为空');
                    // 条件组，rules数组中
                    foreach ($r['rules'] as $rr) {
                        empty($rr['level_rules_month']) && returnError('条件组月份不能为空');
                        empty($rr['level']) && returnError('条件组等级不能为空');
                        empty($rr['level_rules_symbol']) && returnError('条件组计算符号不能为空');
                        !isset($rr['level_times']) && returnError('条件组次数不能为空');
                    }
                } elseif ($r['type'] == 1) {
                    // 条件，rules数组中
                    foreach ($r['rules'] as $rr) {
                        empty($rr['level_rules_month']) && returnError('条件月份不能为空');
                        empty($rr['level']) && returnError('条件等级不能为空');
                        empty($rr['level_rules_symbol']) && returnError('条件计算符号不能为空');
                        !isset($rr['level_times']) && returnError('条件次数不能为空');
                    }
                }
                $idx++;
            }
            if (in_array($rule['coe_name'], $coe_levels)) returnError('系数等级不能重复');
            $coe_levels[] = $rule['coe_name'];
        }
        return true;
    }

    // 等级规则解析
    public static function getLevelRuleText($level_rules)
    {
        $rules = [];
        if (empty($level_rules) && !is_array($level_rules)) return '';
        foreach ($level_rules as $key => $value) {
            if ($value['range'][0] == '-INF' && $value['range'][1] == 'INF') {
                $rules[] = "等级均为{$value['level']}";
            }elseif ($value['range'][1] == 'INF') {
                $value['range'][1] = '无穷大';
                $rules[] = "若得分大于 {$value['range'][0]}，则等级为{$value['level']}";
            }elseif ($value['range'][0] == '-INF') {
                $value['range'][0] = '负无穷大';
                $rules[] = "若得分小于等于 {$value['range'][1]}，则等级为{$value['level']}";
            }else {
                $rules[] = "若得分在区间({$value['range'][0]}, {$value['range'][1]}]，则等级为{$value['level']}";
            }
        }
        return implode('；', $rules);
    }

    // 系数规则解析
    public static function getCoefficientRulesText($coefficient_rules)
    {

        $rules = [];
        return '';

    }

    // 获取等级和绩效
    public static function getResult($user_id, $scheme_id, $assessment_cycle, $score, $level_rules, $coefficient_rules,$default_coefficient)
    {
        $link_symbol = config::get('link_symbol', 'data_assessment');
        $link_symbol_map = array_column($link_symbol, 'value', 'id');
        $level_rules_symbol = config::get('level_rules_symbol', 'data_assessment');
        $level_rules_symbol_map = array_column($level_rules_symbol, 'value', 'id');
        $base_result = 0;
        $coe = 1; // 无系数时，默认为1
        $level = 'error';
        $level_arr = [];
        foreach ($level_rules as $rule) {
            $level_arr[$rule['level']] = 0;
            $rule['range'][1] == 'INF' && $rule['range'][1] = INF;
            $rule['range'][0] == '-INF' && $rule['range'][0] = -INF;
            if ($score > $rule['range'][0] && $score <= $rule['range'][1]) {
                $base_result = $rule['base'];
                $level = $rule['level'];
            }
        }

        // 未匹配到等级
        if ($level == 'error') return ['performance' => 'error', 'level' => 'error '];

        if (!empty($coefficient_rules)) {
            $coe = 0;
            $all= 0;
            foreach ($coefficient_rules as $rule) {
                $expression = '';
                $idx = 0;
                foreach ($rule['coe_rules'] as $r) {
                    if ($idx) $expression .= $link_symbol_map[$r['symbol']];
                    $item_expression = '';
                    $inner_idx = 0;
                    foreach ($r['rules'] as $rr) {
                        if ($inner_idx) $item_expression .= $link_symbol_map[$r['inner_symbol']];
                        $s_month = date('m', strtotime($assessment_cycle[0]));
                        $e_month = date('m', strtotime($assessment_cycle[1]));
                        $diff = intval($e_month - $s_month) + 1;
                        $new_assessment_cycle = $assessment_cycle;
                        if ($rr['level_rules_month'] == 1) { // 周期
                        } elseif ($rr['level_rules_month'] == 2) { // 上个周期
                            $new_assessment_cycle[0] = date('Y-m-d', strtotime($assessment_cycle[0] . "-$diff month"));
                            $new_assessment_cycle[1] = date('Y-m-d', strtotime($assessment_cycle[1] . "-$diff month"));
                        } elseif ($rr['level_rules_month'] == 3) {// 连续三个周期
                            $diff = $diff * 3 - 1;
                            $new_assessment_cycle[0] = date('Y-m-d', strtotime($assessment_cycle[0] . "-$diff month"));
                            $new_assessment_cycle[1] = date('Y-m-d', strtotime($assessment_cycle[1]));
                        } elseif ($rr['level_rules_month'] == 4) {// 连续六个周期
                            $diff = $diff * 6 - 1;
                            $new_assessment_cycle[0] = date('Y-m-d', strtotime($assessment_cycle[0] . "-$diff month"));
                        }
                        $level_map = assessmentUsersModel::getPerformanceLevelCount($user_id,$scheme_id, $new_assessment_cycle, $level_arr);
                        if ($rr['level_rules_month'] != 2) { // 上个周期
                            // 本次的等级也需要计算
                            $level_map[$level] = $level_map[$level] ?? 0;
                            $level_map[$level]++;
                        }

                        $item_flag = $level_map[$rr['level']] . $level_rules_symbol_map[$rr['level_rules_symbol']] . $rr['level_times'];
                        $item_expression .= eval("return $item_flag;") ? "true": "false";
                        $inner_idx++;
                    }

                    $expression .= "$item_expression";
                    $idx++;
                }
                $flag = eval("return $expression;");
                if (!$flag) continue;
                $coe = max($coe, $rule['coe_num']);
                $all ++;
            }
            if ($coe == 0) $coe = $default_coefficient;
        }



        return ['performance' => round(floatval($base_result) * floatval($coe), 2), 'level' => $level];

    }



}