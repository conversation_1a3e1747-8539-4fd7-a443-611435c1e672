-- 运输方式表
CREATE TABLE `lingxing_transport_method` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `method_id` varchar(50) NOT NULL DEFAULT '' COMMENT '运输方式id',
  `code` varchar(20) NOT NULL DEFAULT '' COMMENT '序号',
  `name` varchar(200) NOT NULL DEFAULT '' COMMENT '运输方式名称',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为系统运输方式：0否 1是',
  `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '启动状态：0停用 1启用',
  `remark` text COMMENT '备注',
  `creator_id` int(11) NOT NULL DEFAULT 0 COMMENT '创建人id',
  `creator_name` varchar(100) NOT NULL DEFAULT '' COMMENT '创建人名称',
  `updater_id` int(11) NOT NULL DEFAULT 0 COMMENT '最后编辑人id',
  `updater_name` varchar(100) NOT NULL DEFAULT '' COMMENT '最后编辑人名称',
  `created_at` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间，秒级时间戳',
  `updated_at` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间，秒级时间戳',
  `sync_date` date NOT NULL COMMENT '同步日期',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_method_id` (`method_id`),
  KEY `idx_code` (`code`),
  KEY `idx_name` (`name`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_is_system` (`is_system`),
  KEY `idx_sync_date` (`sync_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='运输方式表';
