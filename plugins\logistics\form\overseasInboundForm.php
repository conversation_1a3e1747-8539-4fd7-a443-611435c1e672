<?php
/**
 * 海外仓备货单表单验证
 * @purpose 海外仓备货单数据验证
 * @Author: System
 * @Time: 2025/06/24
 */

namespace plugins\logistics\form;

class overseasInboundForm
{
    private $errors = [];

    /**
     * 获取错误信息
     * @return array
     */
    public function getErrors()
    {
        return $this->errors;
    }

    /**
     * 添加错误信息
     * @param string $error
     */
    private function addError($error)
    {
        $this->errors[] = $error;
    }

    /**
     * 验证列表查询参数
     * @param array $params
     * @return array
     */
    public function validateListParams($params)
    {
        $this->errors = [];
        $validatedParams = [];

        // 验证状态
        if (isset($params['status']) && $params['status'] !== '') {
            $validStatus = [10, 20, 30, 40, 50, 51, 60];
            if (!in_array((int)$params['status'], $validStatus)) {
                $this->addError('无效的状态值');
            } else {
                $validatedParams['status'] = (int)$params['status'];
            }
        }

        // 验证子状态
        if (isset($params['sub_status']) && $params['sub_status'] !== '') {
            $validSubStatus = [0, 1, 2];
            if (!in_array((int)$params['sub_status'], $validSubStatus)) {
                $this->addError('无效的子状态值');
            } else {
                $validatedParams['sub_status'] = (int)$params['sub_status'];
            }
        }

        // 验证发货仓库ID
        if (!empty($params['s_wid'])) {
            if (is_array($params['s_wid'])) {
                $validatedParams['s_wid'] = array_map('intval', $params['s_wid']);
            } else {
                $validatedParams['s_wid'] = (int)$params['s_wid'];
            }
        }

        // 验证收货仓库ID
        if (!empty($params['r_wid'])) {
            if (is_array($params['r_wid'])) {
                $validatedParams['r_wid'] = array_map('intval', $params['r_wid']);
            } else {
                $validatedParams['r_wid'] = (int)$params['r_wid'];
            }
        }

        // 验证备货单号
        if (!empty($params['overseas_order_no'])) {
            $validatedParams['overseas_order_no'] = trim($params['overseas_order_no']);
        }

        // 验证开始时间
        if (!empty($params['create_time_from'])) {
            if (!$this->validateDate($params['create_time_from'])) {
                $this->addError('开始时间格式错误');
            } else {
                $validatedParams['create_time_from'] = $params['create_time_from'];
            }
        }

        // 验证结束时间
        if (!empty($params['create_time_to'])) {
            if (!$this->validateDate($params['create_time_to'])) {
                $this->addError('结束时间格式错误');
            } else {
                $validatedParams['create_time_to'] = $params['create_time_to'];
            }
        }

        // 验证时间类型
        if (!empty($params['date_type'])) {
            $validDateTypes = ['delivery_time', 'create_time', 'receive_time', 'update_time'];
            if (!in_array($params['date_type'], $validDateTypes)) {
                $this->addError('无效的时间类型');
            } else {
                $validatedParams['date_type'] = $params['date_type'];
            }
        }

        // 验证删除状态
        if (isset($params['is_delete'])) {
            $validDeletes = [0, 1, 2];
            if (!in_array((int)$params['is_delete'], $validDeletes)) {
                $this->addError('无效的删除状态值');
            } else {
                $validatedParams['is_delete'] = (int)$params['is_delete'];
            }
        }

        // 验证分页参数
        if (isset($params['page'])) {
            $page = (int)$params['page'];
            if ($page < 1) {
                $this->addError('页码必须大于0');
            } else {
                $validatedParams['page'] = $page;
            }
        }

        if (isset($params['page_size'])) {
            $pageSize = (int)$params['page_size'];
            if ($pageSize < 1) {
                $this->addError('每页数量必须大于0');
            } elseif ($pageSize > 50) {
                $this->addError('每页数量不能超过50');
            } else {
                $validatedParams['page_size'] = $pageSize;
            }
        }

        return $validatedParams;
    }

    /**
     * 验证可编辑字段
     * @param array $data
     * @return array
     */
    public function validateEditableFields($data)
    {
        $this->errors = [];
        $validatedData = [];

        // 验证透明标
        if (isset($data['transparent_label'])) {
            $transparentLabel = trim($data['transparent_label']);
            if (strlen($transparentLabel) > 200) {
                $this->addError('透明标长度不能超过200个字符');
            } else {
                $validatedData['transparent_label'] = $transparentLabel;
            }
        }

        // 验证入仓编码
        if (isset($data['warehouse_code'])) {
            $warehouseCode = trim($data['warehouse_code']);
            if (strlen($warehouseCode) > 100) {
                $this->addError('入仓编码长度不能超过100个字符');
            } else {
                $validatedData['warehouse_code'] = $warehouseCode;
            }
        }

        // 验证店铺代码
        if (isset($data['shop_code'])) {
            $shopCode = trim($data['shop_code']);
            if (strlen($shopCode) > 100) {
                $this->addError('店铺代码长度不能超过100个字符');
            } else {
                $validatedData['shop_code'] = $shopCode;
            }
        }

        // 验证FNSKU
        if (isset($data['fnsku'])) {
            $fnsku = trim($data['fnsku']);
            if (strlen($fnsku) > 200) {
                $this->addError('FNSKU长度不能超过200个字符');
            } else {
                $validatedData['fnsku'] = $fnsku;
            }
        }

        // 验证剩余可用
        if (isset($data['remaining_available'])) {
            $remainingAvailable = (int)$data['remaining_available'];
            if ($remainingAvailable < 0) {
                $this->addError('剩余可用数量不能为负数');
            } else {
                $validatedData['remaining_available'] = $remainingAvailable;
            }
        }

        // 验证发货备注
        if (isset($data['shipping_remark'])) {
            $shippingRemark = trim($data['shipping_remark']);
            if (strlen($shippingRemark) > 1000) {
                $this->addError('发货备注长度不能超过1000个字符');
            } else {
                $validatedData['shipping_remark'] = $shippingRemark;
            }
        }

        // 验证其他备注
        if (isset($data['other_remark'])) {
            $otherRemark = trim($data['other_remark']);
            if (strlen($otherRemark) > 1000) {
                $this->addError('其他备注长度不能超过1000个字符');
            } else {
                $validatedData['other_remark'] = $otherRemark;
            }
        }

        return $validatedData;
    }

    /**
     * 验证批量操作数据
     * @param array $data
     * @return array
     */
    public function validateBatchData($data)
    {
        $this->errors = [];
        $validatedData = [];

        // 验证IDs
        if (empty($data['ids']) || !is_array($data['ids'])) {
            $this->addError('请选择要操作的备货单');
            return $validatedData;
        }

        $ids = array_map('intval', $data['ids']);
        $ids = array_filter($ids, function($id) { return $id > 0; });
        
        if (empty($ids)) {
            $this->addError('请选择有效的备货单');
            return $validatedData;
        }

        $validatedData['ids'] = $ids;

        // 验证操作类型
        if (empty($data['action'])) {
            $this->addError('请选择操作类型');
            return $validatedData;
        }

        $validActions = ['update', 'delete', 'export'];
        if (!in_array($data['action'], $validActions)) {
            $this->addError('无效的操作类型');
            return $validatedData;
        }

        $validatedData['action'] = $data['action'];

        // 如果是更新操作，验证更新数据
        if ($data['action'] === 'update' && !empty($data['update_data'])) {
            $updateData = $this->validateEditableFields($data['update_data']);
            if (!empty($this->errors)) {
                return $validatedData;
            }
            $validatedData['update_data'] = $updateData;
        }

        return $validatedData;
    }

    /**
     * 验证日期格式
     * @param string $date
     * @return bool
     */
    private function validateDate($date)
    {
        // 支持 Y-m-d 和 Y-m-d H:i:s 格式
        $patterns = [
            '/^\d{4}-\d{2}-\d{2}$/',
            '/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $date)) {
                // 进一步验证日期是否有效
                $parts = preg_split('/[\s-:]/', $date);
                if (count($parts) >= 3) {
                    return checkdate((int)$parts[1], (int)$parts[2], (int)$parts[0]);
                }
            }
        }

        return false;
    }

    /**
     * 验证字符串长度
     * @param string $value
     * @param int $maxLength
     * @param string $fieldName
     * @return bool
     */
    private function validateStringLength($value, $maxLength, $fieldName)
    {
        if (strlen($value) > $maxLength) {
            $this->addError("{$fieldName}长度不能超过{$maxLength}个字符");
            return false;
        }
        return true;
    }

    /**
     * 验证整数范围
     * @param mixed $value
     * @param int $min
     * @param int $max
     * @param string $fieldName
     * @return bool
     */
    private function validateIntRange($value, $min, $max, $fieldName)
    {
        $intValue = (int)$value;
        if ($intValue < $min || $intValue > $max) {
            $this->addError("{$fieldName}必须在{$min}到{$max}之间");
            return false;
        }
        return true;
    }

    /**
     * 验证必填字段
     * @param mixed $value
     * @param string $fieldName
     * @return bool
     */
    private function validateRequired($value, $fieldName)
    {
        if (empty($value) && $value !== '0' && $value !== 0) {
            $this->addError("{$fieldName}不能为空");
            return false;
        }
        return true;
    }

    /**
     * 验证数组字段
     * @param mixed $value
     * @param array $validValues
     * @param string $fieldName
     * @return bool
     */
    private function validateInArray($value, $validValues, $fieldName)
    {
        if (!in_array($value, $validValues)) {
            $this->addError("{$fieldName}值无效");
            return false;
        }
        return true;
    }
}
