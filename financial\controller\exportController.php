<?php

namespace financial\controller;

use core\lib\predisV;
use core\lib\rediskeys;
use financial\form\exportForm;
use financial\models\exportModel;

class exportController
{
    public function __construct()
    {
        set_time_limit(0);
        if (!in_array(URL_ACTION,['exportMap'])) {
            ini_set('memory_limit', '2048G');
            $key = rediskeys::$import_table_key.'_'.URL_ACTION;
            $redis = (new \core\lib\predisV())::$client;
            if ($redis->exists($key)) {
                $data = $redis->get($key);
                $redis->expire($key,'60');
                returnError($data);
            }
        }
    }
    public function __destruct() {
        $redis = (new \core\lib\predisV())::$client;
        $redis->del(rediskeys::$import_table_key);
    }
    //导出ASIN
    public function asinExport()
    {
        // 定义所需参数列表
        $paras_list = array(
            'export_list',
            'export_type',
            'country_code',
            'currency_code',
            'project_ids',
            'yunying_ids',
            'date_time',
            'category_ids',
            'search_type',
            'search_value',
            'aggregation_keys',
            'is_new',
            'page',
            'page_size',
            'order_by_type',
            'order_by_key'
        );
        // 组织参数
        $redis = (new \core\lib\predisV())::$client;
        $redis->set(rediskeys::$import_table_key,'ASIN报表导出');
        $param = arrangeParam($_POST, $paras_list);
        $zip_path = exportForm::asin_export($param);
        returnSuccess($zip_path,'导出成功');
    }
    //导出父ASIN
    public function PasinExport()
    {
        // 定义所需参数列表
        $paras_list = array(
            'export_list',
            'export_type',
            'country_code',
            'currency_code',
            'project_ids',
            'yunying_ids',
            'date_time',
            'category_ids',
            'search_type',
            'search_value',
            'aggregation_keys',
            'is_new',
            'page',
            'page_size',
            'order_by_type',
            'order_by_key'
        );
        // 组织参数
        $redis = (new \core\lib\predisV())::$client;
        $redis->set(rediskeys::$import_table_key,'父ASIN报表导出');
        $param = arrangeParam($_POST, $paras_list);
        $zip_path = exportForm::asin_export($param);
        returnSuccess($zip_path,'导出成功');
    }
    //导出SKU
    public function SkuExport()
    {
        // 定义所需参数列表
        $paras_list = array(
            'export_list',
            'export_type',
            'country_code',
            'currency_code',
            'project_ids',
            'yunying_ids',
            'date_time',
            'category_ids',
            'search_type',
            'search_value',
            'aggregation_keys',
            'is_new',
            'page',
            'page_size',
            'order_by_type',
            'order_by_key'
        );
        // 组织参数
        $redis = (new \core\lib\predisV())::$client;
        $redis->set(rediskeys::$import_table_key,'SKU报表导出');
        $param = arrangeParam($_POST, $paras_list);
        $zip_path = exportForm::asin_export($param);
        returnSuccess($zip_path,'导出成功');
    }
    //运营榜单导出
    public function YunyingExport()
    {
        // 定义所需参数列表
        $paras_list = array(
            'export_list',
            'export_type',
            'country_code',
            'currency_code',
            'project_ids',
            'yunying_ids',
            'date_time',
            'category_ids',
            'search_type',
            'search_value',
            'aggregation_keys',
            'is_new',
            'page',
            'page_size',
            'order_by_type',
            'order_by_key'
        );
        // 组织参数
        $redis = (new \core\lib\predisV())::$client;
        $redis->set(rediskeys::$import_table_key,'运营榜单报表导出');
        $param = arrangeParam($_POST, $paras_list);
        $zip_path = exportForm::asin_export($param);
        returnSuccess($zip_path,'导出成功');
    }
    //店铺导出
    public function SidExport()
    {
        // 定义所需参数列表
        $paras_list = array(
            'export_list',
            'export_type',
            'country_code',
            'currency_code',
            'project_ids',
            'yunying_ids',
            'date_time',
            'category_ids',
            'search_type',
            'search_value',
            'aggregation_keys',
            'is_new',
            'page',
            'page_size',
            'order_by_type',
            'order_by_key'
        );
        // 组织参数
        $redis = (new \core\lib\predisV())::$client;
        $redis->set(rediskeys::$import_table_key,'店铺报表导出');
        $param = arrangeParam($_POST, $paras_list);
        $zip_path = exportForm::asin_export($param);
        returnSuccess($zip_path,'导出成功');
    }
    //爆品导出
    public function hotGoodsExport()
    {
        // 定义所需参数列表
        $paras_list = array(
            'export_list',
            'export_type',
            'country_code',
            'currency_code',
            'project_ids',
            'yunying_ids',
            'date_time',
            'category_ids',
            'search_type',
            'search_value',
            'aggregation_keys',
            'is_new',
            'page',
            'page_size',
            'order_by_type',
            'order_by_key'
        );
        // 组织参数
        $redis = (new \core\lib\predisV())::$client;
        $redis->set(rediskeys::$import_table_key,'爆款报表导出');
        $param = arrangeParam($_POST, $paras_list);
        $zip_path = exportForm::asin_export($param);
        returnSuccess($zip_path,'导出成功');
    }
    //清仓导出
    public function clearSaleExport()
    {
        // 定义所需参数列表
        $paras_list = array(
            'export_list',
            'export_type',
            'country_code',
            'currency_code',
            'project_ids',
            'yunying_ids',
            'date_time',
            'category_ids',
            'search_type',
            'search_value',
            'aggregation_keys',
            'is_new',
            'page',
            'page_size',
            'order_by_type',
            'order_by_key'
        );
        // 组织参数
        $redis = (new \core\lib\predisV())::$client;
        $redis->set(rediskeys::$import_table_key,'清仓报表导出');
        $param = arrangeParam($_POST, $paras_list);
        $zip_path = exportForm::asin_export($param);
        returnSuccess($zip_path,'导出成功');
    }
    //预警导出
    public function waringGoodsExport()
    {
        // 定义所需参数列表
        $paras_list = array(
            'export_list',
            'export_type',
            'country_code',
            'currency_code',
            'project_ids',
            'yunying_ids',
            'date_time',
            'category_ids',
            'search_type',
            'search_value',
            'aggregation_keys',
            'is_new',
            'page',
            'page_size',
            'order_by_type',
            'order_by_key'
        );
        // 组织参数
        $redis = (new \core\lib\predisV())::$client;
        $redis->set(rediskeys::$import_table_key,'预警报表正在导出');
        $param = arrangeParam($_POST, $paras_list);
        $zip_path = exportForm::asin_export($param);
        returnSuccess($zip_path,'导出成功');
    }
    //供应商利润贡献导出
    public function supplierGoodsExport()
    {
        // 定义所需参数列表
        $paras_list = array(
            'export_list',
            'export_type',
            'country_code',
            'currency_code',
            'project_ids',
            'yunying_ids',
            'date_time',
            'category_ids',
            'search_type',
            'search_value',
            'aggregation_keys',
            'is_new',
            'page',
            'page_size',
            'order_by_type',
            'order_by_key'
        );
        // 组织参数
        $redis = (new \core\lib\predisV())::$client;
        $redis->set(rediskeys::$import_table_key,'供应商利润贡献报表导出');
        $param = arrangeParam($_POST, $paras_list);
        $zip_path = exportForm::asin_export($param);
        returnSuccess($zip_path,'导出成功');
    }
    //产品利润贡献导出
    public function goodsExport()
    {
        // 定义所需参数列表
        $paras_list = array(
            'export_list',
            'export_type',
            'country_code',
            'currency_code',
            'project_ids',
            'yunying_ids',
            'date_time',
            'category_ids',
            'search_type',
            'search_value',
            'aggregation_keys',
            'is_new',
            'page',
            'page_size',
            'order_by_type',
            'order_by_key'
        );
        // 组织参数
        $redis = (new \core\lib\predisV())::$client;
        $redis->set(rediskeys::$import_table_key,'产品利润贡献报表导出');
        $param = arrangeParam($_POST, $paras_list);
        $zip_path = exportForm::asin_export($param);
        returnSuccess($zip_path,'导出成功');
    }

    //导出字段映射
    public function exportMap()
    {
        $data = exportModel::get_export_list();
        returnSuccess($data);
    }

}