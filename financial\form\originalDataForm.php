<?php
/**
 * @author: zhangguoming
 * @Time: 2024/9/3 11:41
 */

namespace financial\form;

use core\lib\db\dbFMysql;
use financial\models\mskuReportModel;
use financial\models\originaldataModel;
use Rap2hpoutre\FastExcel\FastExcel;

class originalDataForm
{
    public static function getList($param) {
        $year = substr($param['time'], 0, 4);
        mskuReportModel::creatMskuReportOriginalTable($year);
        //获取sku
        // 如果有类别条件和搜索条件
        if (!empty($param['types']) && !empty($param['Search'])) {
            if ($param['types'] === 'localName') {
                // 当 $param['types'] 为 localName 时的特定操作
                $dblocalName = dbFMysql::getInstance();
                $localName = $dblocalName->table('goods')
                    ->where('where product_name LIKE :product_name', ['product_name' => '%' . $param['Search'] . '%'])
                    ->field('sku')
                    ->one();

                // 检查查询结果
                if ($localName === false) {
                    // 如果没有查询结果，返回错误信息
                    returnSuccess([], '未找到匹配的品名');
                }
            }
        }
        //查询
        $db = dbFMysql::getInstance();
        $db->table('msku_original_data_'.$year)
            ->where('where reportDateMonth = :reportDateMonth', ['reportDateMonth' => $param['time']]);
        // 如果有类别条件和搜索条件
        if (!empty($param['types']) && !empty($param['Search'])) {
            if ($param['types'] === 'localName') {
                $db->andWhere("localSku = :localSku", ['localSku' => $localName['sku']]);
            } else {
                // 当 $param['types'] 不为 localName 时，执行原来的 LIKE 查询
                $db->andWhere("{$param['types']} LIKE :Search", ['Search' => '%' . $param['Search'] . '%']);
            }
        }
        // 删除传0，正常传1，异常传2
        if (isset($param['state'])) {
            if ($param['state'] == 0) { // 删除
                $db->andWhere('is_delete = 1');
            } elseif ($param['state'] == 1) { // 正常
                $db->andWhere('is_delete = 0 and yunying_id != 0');
            } elseif ($param['state'] == 2) { // 异常操作
                $db->andWhere('yunying_id = 0');
            }
        }
        $list =  $db->pages($param['page'], $param['page_size']);
        $supplier_ids = array_unique(array_column($list['list'],'supplier_id'));
        originaldataModel::supplier($supplier_ids);//供应商
        $project_ids = array_unique(array_column($list['list'],'project_id'));
        originaldataModel::yunying($project_ids);//运营项目名
        $yunying_ids = array_unique(array_column($list['list'],'yunying_id'));
        originaldataModel::yunyingid($yunying_ids);//运营人员名
        $sids = array_unique(array_column($list['list'],'sid'));
        originaldataModel::seller($sids);//店铺名
        $skus = array_unique(array_column($list['list'],'localSku'));
        originaldataModel::getProductName($skus);//品名
        $country_codes = array_unique(array_column($list['list'],'countryCode'));
        originaldataModel::countryCode($country_codes);//国家
        $yprojects = originaldataModel::$yprojects;
        foreach ($list['list'] as &$value) {
            $value['projectName'] = isset($yprojects[$value['project_id']])?$yprojects[$value['project_id']]:'';
            //品名
            $products_ = originaldataModel::$pm;
            if (isset($products_[$value['localSku']])) {
                $value['localName'] = $products_[$value['localSku']]['product_name'];
                $value['model'] = $products_[$value['localSku']]['model']??'';
            } else {
                $value['localName'] = '';
                $value['model'] = '';
            }
            //运营人员
            $yunying_ = originaldataModel::$yunyings;
            if (isset($yunying_[$value['yunying_id']])) {
                $value['yunying'] = $yunying_[$value['yunying_id']]['wname'];
            } else {
                $value['yunying'] = '';
            }
            //店铺名
            $seller_ = originaldataModel::$seller;
            if (isset($seller_[$value['sid']])) {
                $value['storeName'] = $seller_[$value['sid']]['real_name'];
            } else {
                $value['storeName'] = '';
            }
            //国家
            $country_ = originaldataModel::$countryCode;
            if (isset($country_[$value['countryCode']])) {
                $value['country'] = $country_[$value['countryCode']]['country'];
            } else {
                $value['country'] = '';
            }
            //供应商
            $suppliers = originaldataModel::$suppliers;
            if (isset($suppliers[$value['supplier_id']])) {
                $value['supplier_name'] = $suppliers[$value['supplier_id']]['supplier_name'];
            } else {
                $value['supplier_name'] = '';
            }
            unset($value['supplier_id']);
            unset($value['yunying_id']);
            unset($value['project_id']);
        }
        return $list;
    }

    //获取月份同步时间
    public static function getSynTime($month) {
        $db = dbFMysql::getInstance();
        $msku_report_data_syn_log = $db->table('msku_report_data_syn_log')
            ->where('where m_date=:m_date',['m_date'=>$month])
            ->order('id desc')
            ->one();
        if ($msku_report_data_syn_log) {
            return $msku_report_data_syn_log['syn_time'];
        } else {
            return '';
        }
    }

    //导出
    public static function export($list) {
        //字段
        $db = dbFMysql::getInstance();
        $column_list = $db->table('column')
            ->where('where custom_id = 0 and show_type > 0')
            ->field('key_name,column_name')
            ->list();
        $xuhao = 1;
        foreach ($list as $k=>$v) {
            $item = [
                '序号'=>$xuhao,
                '项目'=>$v['projectName'],
                '运营'=>$v['yunying'],
                '供应商名称'=>$v['supplier_name'],
                '日期'=>$v['reportDateMonth'],
                '型号'=>$v['model'],
                'MSKU'=>$v['msku'],
                'ASIN'=>$v['asin'],
                '父ASIN'=>$v['parentAsin'],
                '店铺'=>$v['storeName'],
                '国家'=>$v['country'],
                'SKU'=>$v['localSku'],
                '品名'=>$v['localName'],
                '标题'=>$v['itemName'],
                'Listing负责人'=>$v['principalRealname'],
                '开发负责人'=>$v['productDeveloperRealname'],
                '分类'=>$v['categoryName'],
                //'币种'=>$v['currencyCode'],
                '币种'=>'CNY', //同步的数据本来就是人民币
                'Listing标签'=>$v['listingTagIds'],
            ];
            foreach ($column_list as $column) {
                if (isset($v[$column['key_name']])) {
                    $item[$column['column_name']] = $v[$column['key_name']];
                }
            }
            $new_data[] = $item;
            $xuhao++;
        }
        return self::saveExcel($new_data);
    }

    private static function saveExcel($new_data) {
        $save_path = "/public_financial/temp/msku_report/original";
        $url = SELF_FK.$save_path;
        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path."/".date('YmdHis').uniqid().'.xlsx';
        $url = SELF_FK.$path;
        if (!file_exists($url)) {
            touch($url);
        }
        (new FastExcel($new_data))->export($url);
        return $path;
    }
}