<?php

namespace plugins\shop\models;

use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use Exception;

class baseModel
{
    protected object $db;
    public string $table = '';
    public static array $paras_list = [];

    public static array $json_keys = [];

    public function __construct()
    {
        $this->db = dbShopMysql::getInstance();
    }

    // 键名转换为中文
    public function changeToCnKey($data, $paras_list = [])
    {
        if (empty($paras_list) ) {
            $paras_list = self::$paras_list;
        }

        $result = [];
        foreach ($paras_list as $en => $cn) {
            $name = explode('|', $cn)[0];
            if (array_key_exists($en, $data)) {
                $result[$name] = $data[$en];
            }
        }

        return $result;
    }

    // 列表
    public function getList($param)
    {
        $page = $param['page'] ?? '';
        $page_size = $param['page_size'] ?? '';
        $db = $this->db->table($this->table);
        if (!empty($param['where'])) {
            $db->where($param['where']);
        }
        if (!empty($param['field'])) {
            $db->field($param['field']);
        }
        if (!empty($param['order'])) {
            $db->order($param['order']);
        }
        if (!empty($param['group'])) {
            $db->group($param['group']);
        }
        if (!empty($param['where_in'])) {
            foreach ($param['where_in'] as $key => $value) {
                $db->whereIn($key, $value);
            }
        }
        if ($page && $page_size) {
            return $db->pages($page, $page_size);
        }
        return $db->list();
    }

    /**
     * @param array $data
     * @param array $param_list
     * @param bool $is_throw
     * @return array
     * @throws Exception
     */
    public function dataValidCheck(array $data, array $param_list = [], bool $is_throw = true, array $error = []): array
    {
        if (empty($data)) {
            throw new Exception('数据不能为空');
        }
        // 按照参数列表检查数据
        if (!empty($param_list)) {
            foreach ($param_list as $param => $param_detail) {
                $param_detail = explode('|', $param_detail);
                $param_name = $param_detail[0];
                if (!in_array('required', $param_detail)) {
                    continue; // 如果不是必填项，跳过
                }
                if (!isset($data[$param]) || is_null($data[$param])) {
                    if (!$is_throw) {
                        $error[] = $param_name . '不能为空';
                    } else {
                        throw new Exception($param_name . '不能为空');
                    }
                }
            }
        }
        !empty($error) && $data['error'] = $error;
        return $data;
    }



    /**
     * 根据字段获取记录
     * @param string $field 字段名
     * @param mixed $value 字段值
     * @return array|null
     */
    public function getByField(string $field, $value): ?array
    {
        if (empty($field) || empty($value)) {
            return null;
        }

        return $this->db->table($this->table)
            ->where("{$field} = :{$field}", [$field => $value])
            ->find();
    }

    // 新增
    public function add($data, $type = '新增')
    {
        $data = $this->jsonEncodeFormat($data);
        $db = $this->db->table($this->table);
        $data['operator'] = userModel::$qwuser_id ?? 0;
        $data = $this->validateData($data);
        $id = $db->insert($data);
        $this->saveDataLog($this->table, $id, $data, [], $type);
        return $id;
    }

    // 批量新增
    public function addBatch($data)
    {
        $db = $this->db->table($this->table);
        $db->beginTransaction();
        try {
            foreach ($data as $item) {
                $item['operator'] = userModel::$qwuser_id ?? 0;
                $id = $db->insert($item);
                $this->saveDataLog($this->table, $id, $item, [], '批量新增');
            }
            $db->commit();
        } catch (Exception $e) {
            $db->rollBack();
            throw new Exception($e->getMessage());
        }
    }

    // 修改
    public function edit($data, $id, $old_data = [], $type = '编辑', $remark = '', $result = '', $other_attach = [])
    {
        $data = $this->jsonEncodeFormat($data);
        $data['operator'] = userModel::$qwuser_id ?? 0;
        $db = $this->db->table($this->table);
        $db->where('where id = :id', ['id' => $id]);
        $data = $this->validateData($data);
        $res = $db->update($data);
        // 编辑成功时，记录日志
        $res && $this->saveDataLog($this->table, $id, $data, $old_data, $type, $remark, $result, $other_attach);
        return;
    }

    // 详情
    public function getById($id)
    {
        $db = $this->db->table($this->table);
        return $db->where('where id =:id', ['id' => $id])->one();
    }

    /**
     * @param string $table
     * @param int $id
     * @param array $data
     * @param array $old_data
     * @param string $type
     * @param string $remark
     * @param string $result
     * @param array $other_attach
     * @return void
     */
    public function saveDataLog(string $table, int $id, array $data, array $old_data = [],
                                string $type = '', string $remark = '', string $result = '', array $other_attach = []): void
    {
        // 只记录变更的数据
        if ($old_data) {
            $before_data = [];
            $after_data = [];

            foreach ($old_data as $key => $value) {
                if (!array_key_exists($key, $data)) {
                    continue;
                }
                if ($value !== $data[$key]) { // 只记录有变更的字段
                    $before_data[$key] = $value;
                    $after_data[$key] = $data[$key];
                }
            }
        } else {
            $before_data = null;
            $after_data = $data;
        }

        $attach = [
            'after'  => $after_data ?: null,
            'before' => $before_data,
        ];

        if (!empty($other_attach)) {
            foreach ($other_attach as $key => $value) {
                if (!in_array($key, ['after', 'before'])) {
                    $attach[$key] = $value;
                }
            }
        }

        $db = $this->db;
        $db->table('operation_log');
        $db->insert([
            'table_name' => $table,
            'table_id'   => $id,
            'attach'     => json_encode($attach, JSON_UNESCAPED_UNICODE),
            'type'       => $type,
            'remark'     => $remark,
            'result'     => $result,
            'operator'   => userModel::$qwuser_id ?? 0,
        ]);
    }

    // json字段转换
    public function jsonEncodeFormat($data)
    {
        // 处理默认字段
        if (!empty(static::$json_keys)) {
            foreach (static::$json_keys as $field) {
                if (isset($data[$field]) && is_array($data[$field])) {
                    $data[$field] = $data[$field] ? json_encode($data[$field], JSON_UNESCAPED_UNICODE) : null;
                }
            }
        }
        return $data;
    }

    public function formatItem($item, $maps = [])
    {
        if (empty($item)) return $item;
        // 解码JSON字段
        if (!empty(static::$json_keys)) {
            foreach (static::$json_keys as $field) {
                if (isset($item[$field]) && is_string($item[$field])) {
                    $item[$field] = json_decode($item[$field], true);
                }
            }
        }
        /**
         * $maps = [['name' => 'operator_name', 'maps' => $users, 'key' => 'operator']]
         */

        foreach ($maps as $map) { // 字段的映射
            if (!isset($item[$map['key']])) {
//                $item[$map['name']] = '';
                continue;
            }
            if (isset($map['is_array']) && $map['is_array'] && is_array($map['keys']) && !empty($map['keys'])) {
                $map['maps'] = array_column($map['maps'], null, 'id');
                $item[$map['name']] = [];
                foreach ($item[$map['key']] as $v) {
                    $item_obj = [
                        'id' => $v,
                    ];
                    foreach ($map['keys'] as $key) {
                        $item_obj[$key] = $map['maps'][$v][$key] ?? '';
                    }
                    $item[$map['name']][] = $item_obj;
                }
            } else {
                $item[$map['name']] = $map['maps'][$item[$map['key']]] ?? '';
            }
        }
        return $item;
    }

    /**
     * 获取表的列名
     *
     * @return array 列名
     */
    public function getTableColumns()
    {
        $tableName = $this->table;
        $sql = "SHOW COLUMNS FROM oa_shop_" . $tableName;
        $result = $this->db->queryAll($sql);

        return array_column($result, 'Field');
    }

    /**
     * 验证字段是否在表的列中
     *
     * @param array $data 要验证的数据
     * @param array $excludeFields 要排除的字段（例如非数据库字段）
     * @return array 无效的字段
     */
    public function validateData($data, $excludeFields = [])
    {
        $columns = $this->getTableColumns();
        $invalidData = [];
        
        foreach ($data as $field => $value) {
            if (in_array($field, $excludeFields)) {
                continue;
            }
            
            if (in_array($field, $columns)) {
                $invalidData[$field] = $value;
            }
        }
        
        return $invalidData;
    }
}