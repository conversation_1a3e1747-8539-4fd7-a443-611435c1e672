<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/12 16:46
 */

namespace financial\controller;

use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use financial\form\routingForm;

class routingController
{
    public function getList() {
        $paras_list = array('name','page_size','page','date_time');
        $request_data = ['created_time'=>'时间区间'];
        $param = arrangeParam($_POST, $paras_list, $request_data);
        $date_time = json_decode($param['date_time']);
        $begin_time = $date_time[0];
        $end_time = $date_time[1];
        $month_list = [];
        while (1) {
            if (strtotime($begin_time) <= strtotime($end_time)) {
                $month_list[] = $begin_time;
                $begin_time = date('Y-m',strtotime($begin_time.' +1 month'));
            } else {
                break;
            }
        }
        $db = dbFMysql::getInstance();
        $db->table('routing')
            ->whereIn('date',$month_list);
        if (!empty($param['name'])) {
            $db->andWhere('name like :name',['name'=>'%'.$param['name'].'%']);
        }
        $list = $db->field('date,code,icon,name,rate_org,my_rate')
            ->pages($param['page'], $param['page_size']);
        $last_data = $db->table('routing')
            ->where('where syn_time is not null')
            ->order('syn_time desc')
            ->one();
        if ($list) {
            $list['syn_time'] = $last_data['syn_time'];
        } else {
            $list['syn_time'] = '';
        }
        returnSuccess($list);
    }
    //导出
    public function exportRouting() {
        $paras_list = array('name','page_size','page','date_time');
        $request_data = ['created_time'=>'时间区间'];
        $param = arrangeParam($_POST, $paras_list, $request_data);
        $date_time = json_decode($param['date_time']);
        $begin_time = $date_time[0];
        $end_time = $date_time[1];
        $db = dbFMysql::getInstance();
        $db->table('routing')
            ->where('where date >= :begin_time and date <= :end_time',['end_time'=>$end_time,'begin_time'=>$begin_time]);
        if (!empty($param['name'])) {
            $db->andWhere('name like :name',['name'=>'%'.$param['name'].'%']);
        }
        $list = $db->field('date,code,icon,name,rate_org,my_rate')
            ->list();
        $url = routingForm::export($list);
        returnSuccess(['url'=>$url]);
    }
    // 获取最近一个月币种获取的接口
    // 获取指定日期的币种数据
    public function getLastMonthCurrency() {
        try {
            // 获取数据库实例
            $db = dbFMysql::getInstance();

            // 获取最新一个月的日期
            $latestDate = $db->table('routing')
                ->field('MAX(date) as latest_date')
                ->one();

            if (!$latestDate ||!isset($latestDate['latest_date'])) {
                returnError('未找到最新日期的币种数据');
            }

            // 使用最新日期查询币种数据
            $list = $db->table('routing')
                ->where('where date = :date', ['date' => $latestDate['latest_date']])
                ->field('date, code, icon, name, rate_org, my_rate')
                ->list();

            // 定义指定货币的排序顺序
            $preferredOrder = [
                '人民币',
                '美元',
                '欧元',
                '英镑',
                '日元',
                '加元'
            ];

            // 自定义排序函数
            usort($list, function($a, $b) use ($preferredOrder) {
                $indexA = array_search($a['name'], $preferredOrder);
                $indexB = array_search($b['name'], $preferredOrder);

                if ($indexA === false && $indexB === false) {
                    // 如果两个货币都不在指定顺序中，按照它们在原始数据中的顺序排序
                    return 0;
                } elseif ($indexA === false) {
                    // 如果 $a 不在指定顺序中，将其排在后面
                    return 1;
                } elseif ($indexB === false) {
                    // 如果 $b 不在指定顺序中，将其排在后面
                    return -1;
                } else {
                    // 如果两个货币都在指定顺序中，按照指定顺序排序
                    return $indexA - $indexB;
                }
            });

            // 返回成功结果
            returnSuccess($list, '获取成功');
        } catch (\Exception $e) {
            // 捕获异常并返回错误信息
            returnError('查询失败：'. $e->getMessage());
        }
    }

    //同步
    public function synData() {
        $m_date = $_POST['m_date'];
        if (empty($m_date)) {
            $m_date = date('Y-m', strtotime('-1 month'));
        }
        $log_path = SELF_FK.'/log/shell';
        if (!file_exists($log_path)) {
            mkdir($log_path, 0777, true);
        }
        $log_path .= '/lingxing_routing_syn.log';
        if (!file_exists($log_path)) {
            file_put_contents($log_path, $log_path.PHP_EOL);
        } else {
            file_put_contents($log_path, $log_path.PHP_EOL, FILE_APPEND);
        }
        $targetFile = SELF_FK . '/task/shell/lingxing_routing_syn.sh ' . $m_date . ' > '.$log_path.' /dev/null 2>&1 &';
        shell_exec($targetFile);
        ///mnt/www/wwwroot/oa/api/task/shell/lingxing_routing_syn.sh > /mnt/www/wwwroot/oa/log/oa/lingxing_routing_syn.log
        returnSuccess([$targetFile],'同步成功，请稍后刷新界面');
    }


}