<?php

namespace core\jobs;
use core\lib\config;
use core\lib\db\dbMysql;
use core\lib\log;

/**
 * @author: zhangguoming
 * @Time: 2024/1/24 13:38
 */
class qwUserSynJobs
{
    public string $unqueid = '';
    public array $qw_partments = [];
    private array $qw_config = [];
    public function __construct($qw_partments){
        $this->qw_partments = $qw_partments;
        $this->unqueid = uniqid();
        $this->qw_config = config::all('qw');
    }
    //企微成员获取更新
    public function task(){
        $qw_config = $this->qw_config;
        $token = $this->getToken();
        $qw_partments = $this->qw_partments;
        if (count($this->qw_partments)) {
            foreach ($qw_partments as $partment) {
                $url = $qw_config['user_list_url'].'?access_token='.$token.'&department_id='.$partment['id'];
                $list = requestHttp($url);
                $user_list = isset($list['userlist'])?$list['userlist']:[];
                $saved_userid = [];
                $db = dbMysql::getInstance();
                $db->table('qwuser');
                if (count($user_list)) {
                    foreach ($user_list as $user) {
                        if (!in_array($user['userid'],$saved_userid)) {
                            $data_ = [
                                'wname'=>$user['name'],
                                'wdepartment_ids'=>json_encode($user['department']),
                                'wmain_department'=>$user['main_department'],
                                'wphone'=>$user['mobile'],
                                'wid'=>$user['userid'],
                                'wstatus'=>$user['status'],
                                'wsort'=>json_encode($user['order']),
                                'wdata'=>json_encode($user),
                                'position'=>$user['position'],
                            ];
                            if (isset($user['avatar'])) {
                                $data_['avatar'] = $user['avatar'];
                            }
                            if (isset($user['mobile'])) {
                                $data_['wphone'] = $user['mobile'];
                            }
                            $db->where('where wid = :userid',['userid'=>$user['userid']])->field('id');
                            $qwuser = $db->one();
                            if ($qwuser) {
                                $data_['updated_at'] = date('Y-m-d H:i:s');
                                $db->update($data_);
                            } else {
                                $data_['created_at'] = date('Y-m-d H:i:s');
                                $db->insert($data_);
                            }
                            $saved_userid[] = $user['userid'];
                        }
                    }
                    log::taskLog()->info('企微：更新部门('.$partment['name'].')成员成功');
                } else {
                    log::taskLog()->error('企微：获取部门('.$partment['name'].')成员失败');
                }
            }
        }
    }

    private function getToken(){
        $qw_config = $this->qw_config;
        $redis = (new \core\lib\predisV())::$client;
        $token = $redis->get('qy_token_');
        if ($token) {
            return $token;
        } else {
            $token_url =$qw_config['token_url_nei'];
            $data = requestHttp($token_url,'get');
            if ($data && isset($data['data'])) {
                $redis->set('qy_token_', $data['data']);
                $redis->expire('qy_token_',5*60);
                return $data['data'];
            } else {
                log::taskLog()->error('企微：获取token失败');die;
            }
        }
    }


}