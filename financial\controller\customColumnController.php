<?php

/**
 * @author: zhangguoming
 * @Time: 2024/7/3 16:04
 */

namespace financial\controller;

use core\lib\ExceptionError;
use financial\form\customColumnForm;
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use core\lib\predisV;


//$config = include 'core/config/data_financial.php';

class customColumnController
{
    //添加自定义字段
    public function edit()
    {
        $paras_list = array('id', 'column_name', 'status', 'column_type', 'rules', 'show_type', 'begin_time', 'description');
        $request_list = ['column_name' => '字段名称', 'status' => '状态', 'column_type' => '数据类别', 'rules' => '规则', 'show_type' => '数值显示格式', 'begin_time' => '生效时间'];
        $length_list = ['column_name' => ['name' => '字段名称', 'length' => 200], 'description' => ['name' => '描述', 'length' => 200]];
        $param = arrangeParam($_POST, $paras_list, $request_list, $length_list);
        customColumnForm::customColumnLimit($param);
        customColumnForm::edit($param);
    }
    //重新计算自定义字段的数据(上月)
    public function recalculateById()
    {
        $paras_list = array('ids', 'type', 'm_date');
        $request_list = ['column_name' => 'ID', 'type' => '类型'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $m_date = $param['m_date']??'';
        $ids = [];
        if ($param['type'] == 0) {
            $ids = json_decode($param['ids']);
            if (!count($ids)) {
                returnError('请选择要计算的列');
            }
        }
        if (customColumnForm::recalculate($param['type'], $ids, $m_date)) {
            returnSuccess('', '计算中...');
        } else {
            returnSuccess('', '未找到可计算的字段');
        }
    }
    //删除自定义字段
    public function del()
    {
        $paras_list = array('id');
        $request_list = ['id' => '预警ID'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $id = (int)$param['id'];
        $db = dbFMysql::getInstance();
        $db->beginTransaction();
        try {
            // 获取旧数据
            $old_data = $db->table('custom_column')
                ->where('where id=:id', ['id' => $id])
                ->one();
            if ($old_data['is_lock'] == 1) {
                returnError('不可删除');
            }
            if (!$old_data) {
                throw new ExceptionError("记录未找到");
            }
            $custom_key = ['oa_key_'.$id];
            //查询该字段有没有参与其他字段计算
            $other_data = $db->table('custom_column')
                ->where('where is_delete = 0')
                ->andWhere('(JSON_CONTAINS(relation_column,:c_key) or JSON_CONTAINS(last_relation_column,:c_key))',['c_key'=>json_encode($custom_key)])
                ->count();
            if ($other_data) {
                throw new ExceptionError("该字段有参与其他字段计算，不可删除");
            }
            // 执行逻辑删除
            $db->table('custom_column')
                ->where('where id=:id', ['id' => $id])
                ->update(['is_delete' => 1]);
            // 获取新数据（逻辑删除后的状态）
            $new_data = $db->table('custom_column')
                ->where('where id=:id', ['id' => $id])
                ->one();
            // 调用 setLog 方法传递旧数据和新数据
            customColumnForm::setLogs($id, $old_data, $new_data);
            //修改字段状态
            $db->table('column')
                ->where('where custom_id=:id', ['id' => $id])
                ->update(['is_delete'=>1]);
            $db->commit();
            returnSuccess([], '删除成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    // 导出自定义字段列表
    public function exportCustomColumnList()
    {
        try {
            // 定义所需参数列表
            $paras_list = ['name'];

            // 组织参数
            $param = arrangeParam($_POST, $paras_list);

            // 获取数据库实例
            $db = dbFMysql::getInstance();
            $dby = dbMysql::getInstance();

            // 查询 custom_column 表中的数据
            $query = $db->table('custom_column')->where('where is_delete = 0')->order('data_sort ASC');

            // 判断是否传入字段名进行模糊查询
            if (!empty($param['name'])) {
                $query->where("where column_name LIKE :name", ['name' => "%{$param['name']}%"]);
            }

            // 执行查询
            $columnsData = $query->field('id, column_name, rules, updated_time,status, description, created_time, updated_user_id, begin_time')->list();
            // 新数组，用于存储修改后的结果
            $newArray = [];
            // 计数器，用于生成新的id值
            $newId = 1;
            foreach ($columnsData as $item) {
                $newItem = $item;
                $newItem['id'] = $newId;
                $newArray[] = $newItem;
                $newId++;
            }
            $columnsData = $newArray;

            // 如果查询结果为空
            if (empty($columnsData)) {
                returnError("未找到相关数据");
            }

            // 获取所有的 updated_user_id
            $userIds = array_column($columnsData, 'updated_user_id');
            $userIds = array_unique($userIds);

            // 查询对应的用户名
            $users = $dby->table('qwuser')->whereIn('id', $userIds)->field('id, wname')->list();
            $userMap = array_column($users, 'wname', 'id');

            // 查询 column 表中的映射
            $columns = $db->table('column')->field('key_name, column_name')->list();
            $columnMap = array_column($columns, 'column_name', 'key_name');

            // 自定义映射
            $symbolMapping = [1 => "+", 2 => "-", 3 => "*", 4 => "/"];
            $typeMapping = [1 => '本月', 2 => '上月', 3 => '自定义'];

            // 用于存储最终结果的数组
            $result = [];

            foreach ($columnsData as $item) {
                // 解析 rules 字段
                $rules = json_decode($item['rules'], true);
                $expression = $this->generateExpression($rules, $columnMap, $symbolMapping, $typeMapping);

                // 查询 custom_column_log 表中最新的修改时间数据
                $log = $db->table('custom_column_log')
                    ->where("where custom_id = :id", ['id' => $item['id']])
                    ->field('user_id, created_time')
                    ->order('created_time DESC')
                    ->list();  // 获取所有记录

                // 只保留最新的一条记录
                $log = !empty($log) ? $log[0] : ['user_id' => null, 'created_time' => '未知时间'];

                $status = $item['status'] == 1 ? '启用' : '禁用';
                $expressions = $this->replaceCountryCodes($expression);
                $expressions = $this->generateExpressionOutput($expressions);

                // 确保字段计算结果为字符串
                $expressions = is_array($expressions) ? json_encode($expressions, JSON_UNESCAPED_UNICODE) : $expressions;

                $result[] = [
                    'id' => $item['id'],
                    '字段名' => $item['column_name'],
                    '字段计算' => $expressions,
                    '是否启用' => $status,
                    '生效时间' => $item['begin_time'],
                    '描述' => $item['description'],
                    '最新修改人' => $userMap[$item['updated_user_id']] ?? '未知用户',
                    '修改时间' => $item['updated_time'],
                ];
            }

            // 使用项目根目录动态构建导出文件路径
            $projectRoot = dirname(__DIR__, 2);
            $exportPath = $projectRoot . '/public_financial/temp/custom_column/data/custom_column_list.xlsx';
            $exportPathqd = '/public_financial/temp/custom_column/data/custom_column_list.xlsx';

            // 检查并创建文件夹
            $exportDir = dirname($exportPath);
            if (!file_exists($exportDir)) {
                mkdir($exportDir, 0777, true);
            }

            // 导出数据到 Excel
            $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // 设置表头
            $headers = ['id', '字段名', '字段计算', '是否启用', '生效时间', '描述', '最新修改人', '修改时间'];
            $columnIndex = 'A';
            foreach ($headers as $header) {
                $sheet->setCellValue($columnIndex . '1', $header);
                $columnIndex++;
            }

            // 填充数据
            $row = 2;
            foreach ($result as $item) {
                $columnIndex = 'A';
                foreach ($headers as $header) {
                    $sheet->setCellValue($columnIndex . $row, $item[$header]);
                    $columnIndex++;
                }
                $row++;
            }

            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
            $writer->save($exportPath);

            // 返回文件下载路径
            returnSuccess(['file_path' => $exportPathqd], '导出成功');
        } catch (\Exception $e) {
            returnError("导出数据失败: " . $e->getMessage());
        }
    }
    // 新增编辑获取所需信息
    public function getInfo()
    {
        // 定义所需参数列表
        $paras_list = array('id');
        $request_list = ['id' => '字段ID'];

        // 组织参数
        $param = arrangeParam($_POST, $paras_list, $request_list);

        // 获取数据库实例
        $db = dbFMysql::getInstance();

        // 获取ID
        $id = $param['id'];

        // 根据ID查询所需信息
        $info = $db->table('custom_column')
            ->where('where id = :id', ['id' => $id])
            ->list();



        if ($info) {
            returnSuccess($info, '获取信息成功！');
        } else {
            returnError('未找到相关信息');
        }
    }

    //获取变更记录列表
    public function getUpdatelist()
    {
        $paras_list = array('id', 'page', 'page_size');
        $request_list = ['id' => '修改记录ID'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $id = (int)$param['id'];
        $db = dbFMysql::getInstance();

        try {
            // 查询 custom_column_log 表中 custom_id 为指定 id 的数据
            $list = $db->table('custom_column_log', 'a')
                ->where('where a.custom_id=:id', ['id' => $id])
                ->leftJoinOut('db', 'qwuser', 'b', 'b.id = a.user_id')
                ->field('a.id, a.custom_id, a.log_data, a.new_rules, a.created_time, a.user_id, a.type, b.wname as update_wname')
                ->order('id asc')
                ->pages($param['page'], $param['page_size']);

            // 获取 custom_column 表中的数据
            $custom_column_data = $db->table('custom_column')
                ->where('where id=:id', ['id' => $id])
                ->field('column_name, description, status, updated_time,begin_time')
                ->list();

            if (!$custom_column_data) {
                returnError('未找到自定义列数据');
            }

            // 定义映射
            // 查询 column 表中 key_name 和 column_name 的映射
            $columns = $db->table('column')
                ->field('key_name, column_name')
                ->list();


            // 创建 key_name 到 column_name 的映射
            $column_map = [];
            foreach ($columns as $column) {
                $column_map[$column['key_name']] = $column['column_name'];
            }
            $symbolMapping = [
                1 => "+",
                2 => "-",
                3 => "*",
                4 => "/"
            ];

            // 自定义数据类型映射
            $typeMapping = [
                1 => '本月',
                2 => '上月',
                3 => '自定义'
            ];


            // 用于存储已经处理过的非重复项
            $previousItemData = null;

            foreach ($list['list'] as $key => &$item) {
                $log_data = json_decode($item['log_data'], true);
                $rules = json_decode($log_data['rules'], true);

                // 生成字段计算表达式
                $expression = $this->generateExpression($rules, $column_map, $symbolMapping, $typeMapping);

                // 国家映射
                $expression = $this->replaceCountryCodes($expression);


                // 初始化字段
                $item['description'] = '';
                $item['status'] = '';
                $item['column_name'] = '';
                $item['created_time'] = '';
                $item['modified_time'] = '';

                // 更新字段值
                $item['rules'] = $expression;
                $item['description'] = $log_data['description'] ?? '';
                $item['status'] = isset($log_data['status']) ? (int)$log_data['status'] : 0;
                $item['column_name'] = $log_data['column_name'] ?? '';
                $item['created_time'] = $log_data['begin_time'] ?? '';
                $item['modified_time'] = $log_data['updated_time'] ?? '';
                $item['update_wname'] = $item['update_wname'] ?? '';

                // 移除不需要的字段
                unset(
                    $item['new_rules'],
                    $item['log_data'],
                    $item['custom_id'],
                    $item['user_id'],
                    $item['type']
                );

                // 比较当前项与已经处理过的项
                $currentItemData = $item;

                // 清除 id 和 modified_time 字段，以避免它们影响重复性检查
                unset($currentItemData['id'], $currentItemData['modified_time']);

                // 检查当前项是否与前一项相同
                $isDuplicate = $currentItemData === $previousItemData;

                // 如果是重复的，则从列表中移除该项
                if ($isDuplicate) {
                    unset($list['list'][$key]);
                } else {
                    // 否则，将当前项设为新的前一项
                    $previousItemData = $currentItemData;
                }
            }

            unset($item); // 解引用，避免对原数组的无意修改

            // 重新索引数组
            $list['list'] = array_values($list['list']);

            returnSuccess($list, '获取数据成功');
        } catch (\Exception $e) {
            returnError('查询失败：' . $e->getMessage());
        }
    }

    // 拉取列表
    public function getCustomColumnList()
    {

        try {
            // 定义所需参数列表
            $paras_list = array('name');

            // 组织参数
            $param = arrangeParam($_POST, $paras_list);

            // 获取数据库实例
            $db = dbFMysql::getInstance();
            $dby = dbMysql::getInstance();

            // 查询需要的信息1
            $query = $db->table('custom_column')
                ->where('where is_delete = 0');

            // 判断是否传入字段名进行模糊查询
            if (!empty($param['name'])) {
                $query->where("where column_name like '%{$param['name']}%' and is_delete = 0");
            }
            // 执行查询
            $ce1 = $query->field('id, column_name, updated_time , rules, status, description, begin_time, updated_user_id,is_lock')
                ->order('data_sort asc')
                ->list();
            // 如果查询结果为空
            if (empty($ce1)) {
                returnError("未找到相关数据");
            }

            // 获取所有的 updated_user_id
            // 从数组 $ce1 中提取 'updated_user_id' 字段的值，并将其存储在 $user_ids 数组中
            $user_ids = array_column($ce1, 'updated_user_id');

            // 移除 $user_ids 数组中的重复值，确保每个用户 ID 只出现一次
            $user_ids = array_unique($user_ids);

            // 查询对应的用户名
            $users = $dby->table('qwuser')
                ->whereIn('id', $user_ids)
                ->field('id, wname')
                ->list();

            // 创建 user_id 到 name 的映射
            $user_map = [];
            foreach ($users as $user) {
                $user_map[$user['id']] = $user['wname'];
            }

            // 查询 column 表中 key_name 和 column_name 的映射
            $columns = $db->table('column')
                ->field('key_name, column_name')
                ->list();

            // 创建 key_name 到 column_name 的映射
            $column_map = [];
            foreach ($columns as $column) {
                $column_map[$column['key_name']] = $column['column_name'];
            }
            //            dd($column_map);
            // 自定义字段计算符号映射
            $symbolMapping = [
                1 => "+",
                2 => "-",
                3 => "*",
                4 => "/"
            ];

            // 自定义数据类型映射
            $typeMapping = [
                1 => '本月',
                2 => '上月',
                3 => '自定义'
            ];

            // 用于存储最终结果的数组
            $result = [];

            //            returnSuccess($ce1);
            $count = 0;
            foreach ($ce1 as $item) {
                //                dd($item);
                $count++;
                $rules = json_decode($item['rules'], true);

                if (is_array($rules)) {
                    // 生成字段计算表达式
                    $expression = $this->generateExpression($rules, $column_map, $symbolMapping, $typeMapping);
                    //国家映射
                    $expression = $this->replaceCountryCodes($expression);


                    $result[] = [
                        'id' => $item['id'],
                        '字段名' => $item['column_name'],
                        '字段计算' => $expression,
                        '是否启用' => $item['status'],
                        '生效时间' => $item['begin_time'],
                        '描述' => $item['description'],
                        '最新修改人' => $user_map[$item['updated_user_id']] ?? '',
                        '修改时间' => $item['updated_time'],
                        '是否系统内置' => $item['is_lock'] == 1 ? '是' : '否',

                    ];
                }
            }
            //            dd($count);
            // 返回最终结果
            returnSuccess($result, '获取数据成功');
        } catch (\Exception $e) {
            returnError("获取数据失败: " . $e->getMessage());
        }
    }

    //翻译规则
    private function generateExpression($data, $column_map, $symbolMapping, $typeMapping, $parentType = null, $parentCountryCodes = [])
    {
        $expressions = [];
        //        returnSuccess($data);

        foreach ($data as $item) {
            // 使用传递的父级类型和国家代码，如果当前项中没有定义
            $type = $item['type'] ?? $parentType;
            $country_codes = $item['country_code'] ?? $parentCountryCodes;
            //            returnSuccess($item['rules']);
            $rules = $item['rules'] ?? $parentCountryCodes;

            $expression = '';

            foreach ($rules as $rule) {
                if ($rule['group_type'] == 1) {
                    $column = $column_map[$rule['coulmn_key']] ?? $rule['coulmn_key'];
                    $symbol = $symbolMapping[$rule['symbol']] ?? '';
                    $typeName = $typeMapping[$rule['type']] ?? '';
                    $value = ($rule['type'] == 3) ? $rule['val'] : '';

                    if ($expression) {
                        $expression .= ' ' . $symbol . ' ' . $typeName . $column . $value;
                    } else {
                        $expression = $typeName . $column . $value;
                    }
                } elseif ($rule['group_type'] == 2) {
                    $nestedExpressionData = [['rules' => $rule['list']]];
                    $nestedExpression = $this->generateExpression($nestedExpressionData, $column_map, $symbolMapping, $typeMapping, $type, $country_codes);
                    $symbol = $symbolMapping[$rule['symbol']] ?? '';
                    if ($expression) {
                        $expression .= ' ' . $symbol . ' (' . $nestedExpression[0]['expression'] . ')';
                    } else {
                        $expression = '(' . $nestedExpression[0]['expression'] . ')';
                    }
                }
            }

            $expressions[] = [
                'type' => $type,
                'country_codes' => $country_codes,
                'expression' => $expression
            ];
        }

        return $expressions;
    }
    //组织翻译结构
    function generateExpressionOutput($data)
    {
        $output = [];

        foreach ($data as $item) {
            $type = $item['type'];
            $countryCodes = $item['country_codes'];
            $expression = $item['expression'];

            switch ($type) {
                case 1:
                    if (!empty($expression)) {
                        $output['全部'] = $expression;
                    }
                    break;
                case 2:
                    if (!empty($expression) && !empty($countryCodes)) {
                        if (!isset($output['部分'])) {
                            $output['部分'] = [];
                        }
                        foreach ($countryCodes as $countryCode) {
                            $output['部分'][$countryCode] = $expression;
                        }
                    }
                    break;
                case 3:
                    if (!empty($expression)) {
                        $output['其余'] = $expression;
                    }
                    break;
            }
        }
        //        dd($output);
        return $output;
    }

    //获取国家映射
    private function getCountryMapping()
    {
        // 获取数据库实例
        $db = dbFMysql::getInstance();
        // 查询 market 表
        $result = $db->table('market')
            ->field('code, country')
            ->where('where is_delete = 0')
            ->list();

        $mapping = [];
        // 将查询结果存储在 $mapping 数组中
        foreach ($result as $row) {
            $mapping[$row['code']] = $row['country'];
        }
        //        returnSuccess($mapping);
        return $mapping;
    }
    //国家名映射替换
    public function replaceCountryCodes($data)
    {
        // 获取国家映射
        $mapping = $this->getCountryMapping();
        //        returnSuccess($mapping);
        // 遍历数组并替换 country_codes 中的国家代码
        foreach ($data as &$item) {

            if (!empty($item['country_codes'])) {

                foreach ($item['country_codes'] as &$code) {
                    //                    returnSuccess($code);
                    if (isset($mapping[$code])) {
                        $code = $mapping[$code];
                    }
                }
            }
        }
        //        returnSuccess($data);
        return $data;
    }

    //获取计算进度
    public function getProgress()
    {
        // 验证参数
        $paras_list = array('ids');
        $request_list = ['ids' => '获取计算进度列表'];
        $param = arrangeParam($_POST, $paras_list, $request_list);

        // 解析 ids 参数，将其转换为数组
        $ids = json_decode($param['ids'], true);


        // 初始化返回数据
        $data = [];

        // 获取 Redis 客户端实例
        $redis = (new \core\lib\predisV())::$client;

        foreach ($ids as $id) {
            // 拼接出 Redis 的键名
            $key = 'custom_column_count_val_' . $id;

            // 检查键是否存在
            if ($redis->exists($key)) {
                // 获取 total 和 success 的值
                $total = $redis->hGet($key, 'total');
                $success = $redis->hGet($key, 'success');

                // 计算进度百分比
                $Progress = ($success / $total) * 100;
            } else {
                $Progress = 0; // 键不存在时，默认进度为0
            }

            // 将结果添加到返回数据中
            $data[] = [
                'id' => $id,
                'compute' => $Progress
            ];
        }

        // 返回计算进度列表
        returnSuccess($data, '获取计算进度成功');
    }
    //排序
    public function sort() {
        $paras_list = array('sort_ids');
        $request_list = ['sort_ids' => '字段排序'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        customColumnForm::setSort($param['sort_ids']);
        returnSuccess('排序成功');
    }

}
