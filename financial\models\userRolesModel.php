<?php

/**
 * @author: zhangguoming
 * @Time: 2024/3/27 9:02
 */
namespace financial\models;

use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
class userRolesModel
{
    //根据用户id获取用户角色权限
    public static function getAuthByQuserId($user_id) {
        $db = dbFMysql::getInstance();
        $db->table('user_roles','a');
        $db->where('where user_id = :user_id and a.is_delete = 0',['user_id'=>$user_id]);
        $db->leftJoin('roles','b','b.id = a.role_id');
        $db->field('a.role_id,b.role_name,b.auth,b.type,b.list_auth');
        $list = $db->list();
        $data = [
            'auth'=>[],
            'list_auth'=>[],
            'role_name'=>'',
            'role_type'=>[],
        ];
        if (count($list)) {
            $role_name = [];
            $auth = [];
            $role_type=[];
            $list_auth = [];
            foreach ($list as $v) {
                if ($v['auth'] == 'null') {
                    $v['auth'] = [];
                } else {
                    $v['auth'] = json_decode($v['auth'],true);
                }
                if ($v['list_auth'] == 'null') {
                    $v['list_auth'] = [];
                } else {
                    $v['list_auth'] = json_decode($v['list_auth'],true);
                }
                $role_name[] = $v['role_name'];
                $auth = array_merge($auth,$v['auth']);
                $list_auth = array_merge($list_auth,$v['list_auth']);
                if (!in_array($v['type'],$role_type)) {
                    $role_type[] = $v['type'];
                }
            }
            $data['role_name'] = implode(',',$role_name);
            $auth = array_unique($auth);
            $auth = array_values($auth);
            $data['auth'] = json_encode($auth);
            $data['list_auth'] = json_encode($list_auth);
            $data['role_type'] = $role_type;
        }
        return $data;
    }


}