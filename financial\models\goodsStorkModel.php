<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/26 15:58
 */

namespace financial\models;

use core\lib\db\dbFMysql;

class goodsStorkModel
{
    //导入表头
    public static $import_key_list = [
        "店铺"=>"store_name",
        "运营部门"=>"project_name",
        "运营人员"=>"yunying",
        "国家"=>"country",
        "MSKU"=>"msku",
        "ASIN"=>"asin",
        "父ASIN"=>"p_asin",
        "仓库SKU"=>"sku",
        "产品名称"=>"goods_name",
        "FBA在库总库存数量"=>"fbm_local_num",
        "（FBA在途+在库+海外仓）总库存数量"=>"fbm_local_overseas_num",
        "（FBA在途+在库+海外仓）总库存采购成本+头程成本"=>"fbm_local_overseas_price",
    ];
    //导出的表头
    public static $export_key_list = [
        "project_name"=>"运营部门",
        "yunying"=>"运营人员",
        "country"=>"国家",
        "msku"=>"MSKU",
        "asin"=>"ASIN",
        "sku"=>"仓库SKU",
        "goods_name"=>"产品名称",
        "fbm_local_num"=>"FBA在库总库存数量",
        "fbm_local_overseas_num"=>"（FBA在途+在库+海外仓）总库存数量",
        "fbm_local_overseas_price"=>"（FBA在途+在库+海外仓）总库存采购成本+头程成本",
    ];
    //导入表头判断
    public static function verifyExcelHead($row) {
        $import_key_list = self::$import_key_list;
        $diff = array_diff_key($import_key_list,$row);
        if (count($diff)) {
            $diif_name = array_keys($diff);
            returnError('模板缺少列【'.implode('，',$diif_name).'】');
        }
    }

    public static function creatGoodsStockTable($date_time) {
        $year_array = explode('-',$date_time);
        $table_name = 'goods_stock_'.$year_array[0];
        $dbF = dbFMysql::getInstance();
        //源数据表
        $dbF->query("CREATE TABLE IF NOT EXISTS `oa_f_$table_name` (
          `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
          `m_date` varchar(10) NOT NULL COMMENT '月份',
          `import_id` int(11) NOT NULL COMMENT '导入记录id',
          `base_id` int(11) NOT NULL DEFAULT '0' COMMENT '存入smku数据的id',
          `is_error` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1失败，0成功',
          `yunying_id` int(11) NOT NULL DEFAULT '0',
          `project_id` int(11) NOT NULL DEFAULT '0',
          `project_name` varchar(255) NOT NULL COMMENT '部门名',
          `yunying` varchar(50) NOT NULL COMMENT '运营名称',
          `country_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
          `country` varchar(10) NOT NULL COMMENT '国家名称',
          `asin` varchar(100) NOT NULL COMMENT 'ASIN',
          `msku` varchar(100) NOT NULL,
          `sku` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'SKU',
          `goods_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '产品名称',
          `fbm_local_num` int(11) NOT NULL DEFAULT '0' COMMENT 'FBA在库总库存数量',
          `fbm_local_overseas_num` int(11) NOT NULL DEFAULT '0' COMMENT '（FBA在途+在库+海外仓）总库存数量',
          `fbm_local_overseas_price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '（FBA在途+在库+海外仓）总库存采购成本+头程成本',
          `updated_time` datetime DEFAULT NULL,
          `created_time` datetime DEFAULT NULL,
          `is_delete` tinyint(1) NOT NULL DEFAULT '0',
          `row_num` int(11) DEFAULT NULL,
          `sid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
          `p_asin` varchar(100) NOT NULL,
          `store_name` varchar(100) NOT NULL,
          PRIMARY KEY (`id`)
        ) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='库存数据{$year_array[0]}（金额默认人民币）';");
    }
}