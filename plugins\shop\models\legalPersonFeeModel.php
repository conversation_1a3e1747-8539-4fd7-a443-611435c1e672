<?php

namespace plugins\shop\models;

use core\lib\redisCached;

class legalPersonFeeModel extends baseModel
{
    public string $table = 'legal_person_fee';


    public static array $paras_list = [
        'pid' => 'pid',
        'legal_person_id' => '信用卡ID',
        'fee_detail' => '费用年份',
        'pay_file' => '付款文件',
        'pay_date' => '付款日期',
        'type' => '类型',
        'remark' => '备注'
    ];

    const TYPE_APPLY = 1; // 申请
    const TYPE_CONFIRM = 2; // 付款

    // 获取列表
    public function getList($param)
    {
        $this->db->table($this->table, 'f')
            ->where('1=1');

        if (!empty($param['legal_person_id'])) {
            $this->db->andWhere('f.legal_person_id = :legal_person_id', ['legal_person_id' => $param['legal_person_id']]);
        }
        if (isset($param['type'])) {
            $this->db->whereIn('type', $param['type']);
        }

        $this->db->order('f.id desc');

        $list =  $this->db->list();

        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        foreach ($list as &$item) {
            $item['operator_name'] = $users[$item['operator']] ?? '';
            $item['fee_detail'] = json_decode($item['fee_detail'], true);
        }
        return $list;
    }

    // 新增费用申请
    public function add($data, $type = '新增')
    {
        $data['operator'] = userModel::$qwuser_id ?? 0;
        return $this->db->table($this->table)->insert($data);
    }

    public function edit($data, $id, $old_data = [], $type = '', $remark = '', $result = '', $other_attach = [])
    {
        $data['operator'] = userModel::$qwuser_id ?? 0;
        $this->db->table($this->table)->where('id', $id)->update($data);
    }
}
