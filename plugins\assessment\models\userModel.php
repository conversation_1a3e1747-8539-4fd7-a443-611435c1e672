<?php
/**
 * @author: zhangguoming
 * @Time: 2024/3/27 11:25
 */

namespace plugins\assessment\models;

class userModel extends \admin\models\userModel
{
    public static array $role_type;//0其他,1系统管理员


    //是否为管理员
    public static function isManage() {
        if(in_array(1,self::$role_type)) {
            return true;
        } else {
            return false;
        }
    }

    // 获取用户权限
    public static function getUserListAuth($list_auth) {
        if (self::isManage()) {
            return 1;
        }
        $user_auth = json_decode(self::$auth);
        if (in_array($list_auth,$user_auth)) {
            return 1;
        }
        return 0;
    }
}