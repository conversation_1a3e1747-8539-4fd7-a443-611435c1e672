<?php

namespace plugins\logistics\controller;

use core\lib\config;
use core\lib\db\dbAfMysql;
use core\lib\db\dbErpMysql;
use core\lib\db\dbFMysql;
use core\lib\db\dbLMysql;
use plugins\logistics\models\userModel;

class configController extends baseController
{

    public function __construct()
    {
        parent::__construct();
    }

    // 全局配置
    public function getBaseConfig() {

        $config = config::all('data_logistics');

        // 运输方式
        $erp_db = dbErpMysql::getInstance();
        $transport_method = $erp_db->table('lingxing_transport_method')
            ->field('id, name')
            ->list();
        $config['transport_method'] = $transport_method;

        // 品牌
        $brand = $erp_db->table('lingxing_product_brand')
            ->field('id, bid, title')
            ->list();
        $config['brand'] = $brand;

        // 获取数据库实例
        $db = dbFMysql::getInstance();
        // 查询 market 表
        $result = $db->table('market')
            ->field('code, country, aws_region')
            ->where('where is_delete = 0')
            ->list();
        $config['market'] = $result;


        returnSuccess($config);

    }

    // 获取配置
    public function getConfig() {
        $key_map = [
            'normal_prepare',
            'shop_config'

        ];

        $key = $_GET['key_name']??'';

        if (empty($key)) {
            returnError('参数有误');
        }

        $ldb = dbLMysql::getInstance();
        $ldb->table('config')->where('where key_name = :key_name', ['key_name' => $key]);
        $detail = $ldb->one();
        if (empty($detail)) {
            returnError('配置不存在');
        }

        $config = json_decode($detail['data'], true);

        returnSuccess([
            'config' => $config ?: null,
        ]);
    }

    // 设置配置
    public function setConfig() {
        $key = $_POST['key_name']??'';
        $data = $_POST['data']??'';

        if (empty($key) || empty($data)) {
            returnError('参数有误');
        }

        $ldb = dbLMysql::getInstance();
        $ldb->table('config')->where('where key_name = :key_name', ['key_name' => $key]);
        $detail = $ldb->one();
        if (empty($detail)) {
            returnError('配置不存在');
        }
        
        $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        $ldb->table('config')->where('where key_name = :key_name', ['key_name' => $key])->update(['data' => $data]);


        returnSuccess('','修改成功');
    }

    public function getUserConfig() {
        $user_id = userModel::$qwuser_id ?? 0;
        $config = dbLMysql::getInstance()->table('user_columns')
            ->where('user_id = :user_id', ['user_id' => $user_id])
            ->one();
        $user_columns = json_decode($config['attach'], true) ?? null;
        returnSuccess([
            'user_columns' => $user_columns ?? null,
        ]);
    }

    public function setUserConfig()
    {
        $user_id = userModel::$qwuser_id ?? 0;
        $user_columns = $_POST['user_columns'] ?? null;
        if (empty($user_columns)) {
            returnError('参数错误');
        }
        $user_columns = json_encode($user_columns);
        $db = dbLMysql::getInstance();
        $config = $db->table('user_columns')
            ->where('user_id = :user_id', ['user_id' => $user_id])
            ->one();
        if ($config) {
            $db->table('user_columns')
                ->where('user_id = :user_id', ['user_id' => $user_id])
                ->update(['attach' => $user_columns]);
        } else {
            $db->table('user_columns')->insert([
                'user_id' => $user_id,
                'attach' => $user_columns,
            ]);
        }
        returnSuccess([], '保存成功');

    }

}
