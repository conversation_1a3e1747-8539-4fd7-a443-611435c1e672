<?php
/**
 * @author: zhangguoming
 * @Time: 2024/8/6 14:45
 */

namespace financial\controller;

use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use core\lib\ExceptionError;
use financial\form\goodsWaringForm;
use financial\form\messagesFrom;
use financial\models\userModel;

class goodsWaringController
{
    //获取商品预警详情
    public function getWaringDetail() {
        $goods_waring_id = $_POST['goods_waring_id'];
        if (empty($goods_waring_id)) {
            returnError('商品预警ID必传');
        }
        $goods_waring_id = (int)$goods_waring_id;
        $goods_waring = goodsWaringForm::verifySolveAuth($goods_waring_id);
        //处理记录
        $db = dbFMysql::getInstance();
        $log_list = $db->table('goods_waring_check_log','a')
            ->leftJoinOut('db', 'qwuser', 'b', 'b.id = a.user_id')
            ->where('where a.goods_waring_id = :goods_waring_id',['goods_waring_id'=>$goods_waring_id])
            ->field('a.id,a.user_id,a.solve_txt,a.type,a.created_time,b.wname')
            ->list();
        returnSuccess(['waring_detail'=>$goods_waring,'solve_log'=>$log_list]);
    }
    //商品预警处理
    public function waringSolve() {
        $paras_list = array('goods_waring_id', 'solve_txt');
        $request_list = ['goods_waring_id'=>'商品预警ID','solve_txt'=>'预警原因分析及解决方案'];
        $length_list = ['solve_txt'=>['name'=>'预警原因分析及解决方案','length'=>1000]];
        $param = arrangeParam($_POST,$paras_list,$request_list,$length_list);
        $goods_waring_id = (int)$param['goods_waring_id'];
        if (!$goods_waring_id) {
            returnError('商品预警ID必传');
        }
        $goods_waring = goodsWaringForm::verifySolveAuth($goods_waring_id);
        //预警信息
        if ($goods_waring['status'] == 2 || $goods_waring['status'] == 4) {
            returnError('该预警已处理，切勿重复操作');
        }
        $db = dbFMysql::getInstance();
        $db->beginTransaction();
        try {
            //保存处理结果
            $db->table('goods_waring_check')
                ->where('where id=:id',['id'=>$goods_waring_id])
                ->update([
                    'user_id'=>userModel::$qwuser_id,
                    'status'=>2,
                    'processing_time'=>date('Y-m-d H:i:s')
                ]);
            //处理记录
            $db->table('goods_waring_check_log')
                ->insert([
                    'user_id'=>userModel::$qwuser_id,
                    'goods_waring_id'=>$goods_waring_id,
                    'solve_txt'=>$param['solve_txt'],
                    'type'=>1,
                    'created_time'=>date('Y-m-d H:i:s')
                ]);
            //消息发送
            goodsWaringForm::sendMsgToChecker($goods_waring['waring_name'],$goods_waring['weidu_key'],$goods_waring['zuzhang_ids'],$goods_waring_id);
            $db->commit();
            returnSuccess('','处理成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }

    }
    //商品预警审核
    public function waringCheck() {
        $paras_list = array('goods_waring_id', 'is_pass', 'reason');
        $request_list = ['goods_waring_id'=>'商品预警ID','reason'=>'审核意见','is_pass'=>'审核状态'];
        $length_list = ['reason'=>['name'=>'审核意见','length'=>100]];
        $param = arrangeParam($_POST,$paras_list,$request_list,$length_list);
        $goods_waring_id = (int)$param['goods_waring_id'];
        if (!$goods_waring_id) {
            returnError('商品预警ID必传');
        }
        $is_pass = $param['is_pass']?1:0;
        $db = dbFMysql::getInstance();
        //预警信息
        $goods_waring = goodsWaringForm::verifySolveAuth($goods_waring_id,1);
        if ($goods_waring['status'] == 1) {
            returnError('该商品预警还未解决');
        }
        if ($goods_waring['status'] == 3) {
            returnError('该商品预警已审核，切勿重复操作');
        }
        if ($goods_waring['status'] == 4) {
            returnError('该商品预警已审核，切勿重复操作');
        }
        $db->beginTransaction();
        try {
            //状态修改
            $db->table('goods_waring_check')
                ->where('where id=:id',['id'=>$goods_waring_id])
                ->update([
                    'status'=>$is_pass?4:3,
                    'check_time'=>date('Y-m-d H:i:s')
                ]);
            //处理记录
            $db->table('goods_waring_check_log')
                ->insert([
                    'user_id'=>userModel::$qwuser_id,
                    'goods_waring_id'=>$goods_waring_id,
                    'solve_txt'=>$param['reason'],
                    'is_pass'=>$is_pass,
                    'type'=>2,
                    'created_time'=>date('Y-m-d H:i:s')
                ]);
            //消息发送(最后的解决人)
            $dbOa = dbMysql::getInstance();
            $user_ = $dbOa->table('qwuser')
                ->where('id = :id',['id'=>$goods_waring['user_id']])->one();
            $asin = $goods_waring['weidu_key'];
            $waring_name =  $goods_waring['waring_name'];
            $pass_txt = $is_pass==1?'通过':'不通过';
            messagesFrom::senMeg([$user_['wid']],10,"预警【{$waring_name}】的品【asin:{$asin}】审核$pass_txt",$user_['id']);
            //发送消息给配置的人员
            messagesFrom::senMsgByNoticeType(10,"预警【{$waring_name}】的品【asin:{$asin}】已审核，审核$pass_txt",$goods_waring['id']);
            $db->commit();
            returnSuccess('审核成功');
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }

    }

}