<?php
/**
 * @author: zhangguoming
 * @Time: 2025/4/8 14:42
 */

namespace task\form\goods;

use core\lib\db\dbErpMysql;
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use core\lib\rediskeys;
use financial\models\goodsCategoryModel;

class goodsSysForm
{
    //产品运营更新
    public static function setGoodsYunying() {
        $key_ = rediskeys::$goods_yunying_update;
        $redis = (new \core\lib\predisV())::$client;
        if ($redis->exists($key_)) {
            $redis_data = json_decode($redis->get($key_),true);
            $page = $redis_data['page'] + 1;
        } else {
            $page = 1;
        }
        $page_size = 50;
        //产品获取
        $db = dbMysql::getInstance();
        $goods_list = $db->table('goods_new')
            ->where('is_delete = 0')
            ->field('id')
            ->order('id asc')
            ->pages($page,$page_size);
        if (count($goods_list['list'])) {
            $goods_ids = array_column($goods_list['list'],'id');
            //获取产品的sku
            $goods_sku_relation = $db->table('goods_color_relation')
                ->where('is_delete = 0')
                ->whereIn('goods_id',$goods_ids)
                ->field('goods_id,sku')
                ->list();
            //获取产品的领星sku
            if (count($goods_sku_relation)) {
                $dbErp = dbErpMysql::getInstance();
                $erp_sku = $dbErp->table('oa_lingxing_sku')
                    ->whereIn('oa_sku',array_column($goods_sku_relation,'sku'))
                    ->field('oa_sku,lingxing_sku')
                    ->list();
                $erp_sku_relation = array_column($erp_sku,'oa_sku','lingxing_sku');
                if ($erp_sku) {
                    $lingxing_sku = array_column($erp_sku,'lingxing_sku');
                    //查询linsting负责人
                    $lingxing_listing = $dbErp->table('lingxing_listing')
                        ->whereIn('local_sku',$lingxing_sku)
                        ->field('local_sku,principal')
                        ->list();
                    //sku对应的运营名称
                    $principal_array = [];
                    foreach ($lingxing_listing as $v) {
                        $oa_sku = $erp_sku_relation[$v['local_sku']];
                        if (!empty($v['principal'])) {
                            $principal = json_decode($v['principal'],true);
                            $principal_names = array_column($principal,'principal_name');
                            $principal_ = array_slice($principal_names,2,3);
                            if (count($principal_)) {
                                if (isset($principal_array[$oa_sku])) {
                                    $principal_array[$oa_sku] = array_unique(array_merge($principal_array[$oa_sku],$principal_));
                                } else {
                                    $principal_array[$oa_sku] = $principal_;
                                }
                            }
                        }
                    }
                    //保存结果到oa产品
                    if (count($principal_array)) {
                        $goods_yunying = [];
                        foreach ($goods_sku_relation as $v) {
                            if (isset($principal_array[$v['sku']])) {
                                if (isset($goods_yunying[$v['goods_id']])) {
                                    $goods_yunying[$v['goods_id']] = array_unique(array_merge($goods_yunying[$v['goods_id']],$principal_array[$v['sku']]));
                                } else {
                                    $goods_yunying[$v['goods_id']] = $principal_array[$v['sku']];
                                }
                            }
                        }
                        $principal_names = array_merge(...array_values($goods_yunying));
                        //用户查询
                        $user_list = $db->table('qwuser')
                            ->whereIn('wname',$principal_names)
                            ->field('id,wid,wname')
                            ->list();
                        $user_ = [];
                        foreach ($user_list as $v) {
                            $user_[$v['wname']] = $v;
                        }
                        //结果保存
                        $db->table('goods_new')
                            ->whereIn('id',$goods_ids)
                            ->update([
                                'operator_info'=>'[]',
                                'lingxing_yunying'=>'[]',
                                'yunying_syn_time'=>date('Y-m-d H:i:s')
                            ]);
                        foreach ($goods_yunying as $goods_id=>$yunying_names) {
                            $yunying_info = array_values(array_intersect_key($user_,array_flip($yunying_names)));
                            $db->table('goods_new')
                                ->where('id = :id',['id'=>$goods_id])
                                ->update([
                                    'operator_info'=>json_encode($yunying_info,JSON_UNESCAPED_UNICODE),
                                    'lingxing_yunying'=>json_encode($yunying_names,JSON_UNESCAPED_UNICODE),
                                    'yunying_syn_time'=>date('Y-m-d H:i:s')
                                ]);
                        }
                    }
                }
            }
            //redis更新
            if (count($goods_list['list']) >= $page_size) {
                $redis_data['page'] = $page;
                $redis_data['msg'] = "共{$goods_list['total']}条数据，已处理".($page*($page_size-1) + count($goods_list['list']));
                $redis->set($key_,json_encode($redis_data,JSON_UNESCAPED_UNICODE));
                $redis->expire($key_,10*60);
                returnSuccess('',$redis_data['msg']);
            }
        }
        //运行完成了
        $redis->del($key_);
        SetReturn(2,'产品运营更新完成');
    }
    //财务产品类目全名更新
    public static function updateGoodsCategoryName() {
        $db = dbFMysql::getInstance();
        $list = $db->table('goods_category')
            ->where('is_delete = 0 and parent_cid > 0')
            ->field('cid,parent_cid')
            ->list();
        //上一级
        $first_list = $db->table('goods_category')
            ->where('parent_cid = 0')
            ->field('cid')
            ->list();
        $first_cids = array_column($first_list,'cid');
        //获取分类的上一级分类
        $top_cates = array_column($list,'parent_cid','cid');
        //分了全名称
        $cids = array_column($list,'cid');
        $goods_categoty_list = goodsCategoryModel::getGoodsCate($cids);
        foreach ($goods_categoty_list as $cid=>$v) {
            $p_cid = $top_cates[$cid]??0;
            if (in_array($p_cid,$first_cids)) {
                $second_cid = $cid;
            } else {
                $second_cid = $p_cid;
            }
            $db->table('goods')
                ->where('cid = :cid',['cid'=>$cid])
                ->update([
                    'category_name_all'=>$v['name'],
                    'second_cid'=>$second_cid
                ]);
        }
    }
}