<?php
/**
 * 仓库统计功能测试
 * @author: warehouseStatistic
 * @Time: 2025/6/19
 */

define('SELF_FK', realpath('')); //当前框架所在的根目录
//define('SELF_FK', $_SERVER['DOCUMENT_ROOT']); //当前框架所在的根目录
define('CORE', SELF_FK.'/core'); //核心文件目录
define('DEBUG', true); //调试模式设置
define('LOG_PATH', SELF_FK.'/log'); //调试模式设置
define('SYS_PUBLISH',false); //系统发版中

define('ROOT', SELF_FK."/");
require_once "environment.php"; //系统环境
require_once ROOT."/vendor/autoload.php";
include  CORE.'/common/function.php';
include  CORE.'/common/return.php';
include  CORE.'/common/phperror.php';
//方法请求
include CORE.'/fk.php';
//自动加载类
spl_autoload_register('\core\fk::load');

use plugins\logistics\controller\warehouseStatisticController;
use plugins\logistics\form\warehouseStatisticForm;
use plugins\logistics\models\warehouseStatisticModel;

/**
 * 测试仓库统计功能
 */
class WarehouseStatisticTest
{
    private $year_month;
    
    public function __construct($year_month = null)
    {
        $this->year_month = $year_month ?? date('Y-m');
    }
    
    /**
     * 测试数据表创建
     */
    public function testCreateTable()
    {
        echo "📋 测试创建统计表...\n";
        
        try {
            warehouseStatisticModel::createWarehouseStatisticTable($this->year_month);
            echo "✅ 统计表创建成功\n";
            return true;
        } catch (Exception $e) {
            echo "❌ 统计表创建失败: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 测试数据收集功能
     */
    public function testCollectData()
    {
        echo "📊 测试数据收集功能...\n";
        
        try {
            $form = new warehouseStatisticForm($this->year_month);
            $result = $form->executeStatistic($this->year_month);
            
            if ($result['code'] === 0) {
                echo "✅ 数据收集成功: " . $result['msg'] . "\n";
                return true;
            } else {
                echo "❌ 数据收集失败: " . $result['msg'] . "\n";
                return false;
            }
        } catch (Exception $e) {
            echo "❌ 数据收集异常: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 测试聚合查询功能
     */
    public function testAggregateQuery()
    {
        echo "🔍 测试聚合查询功能...\n";
        
        try {
            $param = [
                'year_month' => $this->year_month,
                'page' => 1,
                'page_size' => 10
            ];
            
            $form = new warehouseStatisticForm($this->year_month);
            $result = $form->getStatisticList($param);
            
            if (isset($result['list']) && is_array($result['list'])) {
                echo "✅ 聚合查询成功，返回 " . count($result['list']) . " 条记录\n";
                echo "📈 总记录数: " . ($result['total'] ?? 0) . "\n";
                
                // 显示前几条记录的基本信息
                if (!empty($result['list'])) {
                    echo "📋 示例数据:\n";
                    foreach (array_slice($result['list'], 0, 3) as $index => $item) {
                        echo "   " . ($index + 1) . ". 仓库: " . ($item['ware_house_name'] ?? '未知') . 
                             " | SKU数量: " . ($item['sku_count'] ?? 0) . 
                             " | 期末成本: " . number_format($item['total_day_end_cost'] ?? 0, 2) . "\n";
                    }
                }
                
                return true;
            } else {
                echo "❌ 聚合查询失败，返回数据格式错误\n";
                return false;
            }
        } catch (Exception $e) {
            echo "❌ 聚合查询异常: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 测试控制器接口
     */
    public function testControllerApi()
    {
        echo "🎮 测试控制器接口...\n";
        
        try {
            // 模拟POST数据
            $_POST['year_month'] = $this->year_month;
            $_POST['page'] = 1;
            $_POST['page_size'] = 5;
            
            $controller = new warehouseStatisticController();
            
            // 捕获输出
            ob_start();
            $controller->getStatisticList();
            $output = ob_get_clean();
            
            echo "✅ 控制器接口调用成功\n";
            echo "📄 返回数据长度: " . strlen($output) . " 字符\n";
            
            return true;
        } catch (Exception $e) {
            echo "❌ 控制器接口异常: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "🚀 开始仓库统计功能测试 (月份: {$this->year_month})\n";
        echo str_repeat("=", 50) . "\n";
        
        $tests = [
            'testCreateTable' => '数据表创建',
            'testCollectData' => '数据收集',
            'testAggregateQuery' => '聚合查询',
            'testControllerApi' => '控制器接口'
        ];
        
        $results = [];
        foreach ($tests as $method => $name) {
            echo "\n";
            $results[$name] = $this->$method();
        }
        
        echo "\n" . str_repeat("=", 50) . "\n";
        echo "📊 测试结果汇总:\n";
        
        $passed = 0;
        $total = count($results);
        
        foreach ($results as $testName => $result) {
            $status = $result ? "✅ 通过" : "❌ 失败";
            echo "   {$testName}: {$status}\n";
            if ($result) $passed++;
        }
        
        echo "\n🎯 总体结果: {$passed}/{$total} 项测试通过\n";
        
        if ($passed === $total) {
            echo "🎉 所有测试通过！collectWarehouseData功能实现完成。\n";
        } else {
            echo "⚠️  部分测试失败，请检查相关功能。\n";
        }
        
        return $passed === $total;
    }
}

// 如果直接运行此文件，执行测试
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $test = new WarehouseStatisticTest();
    $test->runAllTests();
}
