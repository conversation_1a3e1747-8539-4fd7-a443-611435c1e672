<?php

namespace plugins\salary\Controller;

use core\lib\db\dbMysql;
use core\lib\db\dbSMysql;
use plugins\salary\models\userModel;

class salaryPrepayController
{

    // 列表
    public function getList() {
        $paras_list = array('user_id', 'dep_id', 'month', 'page', 'page_size');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        $month = json_decode($param['month'], true);
        if (empty($month)) returnError('月份不能为空');

        $sdb = dbSMysql::getInstance();
        $sdb->table('user_tax_prepay', 'p');
        $sdb->field('p.*, u.wname as user_name, u.wdepartment_ids');
        $sdb->leftJoinOut('db', 'qwuser', 'u', 'p.qwuser_id=u.id');
        $sdb->leftJoinOut('db', 'user_info', 'ui', 'p.qwuser_id=ui.qwuser_id');
        $sdb->where('where month >= :s_month and month <= :e_month', ['s_month' => $month[0], 'e_month' => $month[1]]);

        if (userModel::getUserListAuth('salaryPrepayAll')) {
            // 全部权限
        } elseif (userModel::getUserListAuth('salaryPrepayWorkPlace')) {
            $auth_work_place_id = [];
            $gid = ["1", "2"];
            foreach ($gid as $item) {
                if (userModel::getUserListAuth('salaryPrepayWorkPlace_'.$item)) {
                    $auth_work_place_id[] = $item;
                }
            }
            if (empty($auth_work_place_id)) {
                returnError('无权限查看');
            }
            $sdb->whereIn('ui.work_place', $auth_work_place_id);
        }

        if ($param['user_id']) {
            $user_id = json_decode($param['user_id'], true);
            if (!empty($user_id)) {
                $sdb->whereIn('p.qwuser_id', $user_id);
            }
        }
        if (!empty($param['dep_id'])) {
            $departments = json_decode($param['dep_id'], true);
            if (!empty($departments)) {
                $sdb->whereIn('u.wmain_department', $departments);
            }
        }

        $list = $sdb->pages($page, $limit);
        if (empty($list['list'])) returnSuccess($list);

        // 关联用户信息
        $user_ids = array_column($list['list'], 'operator');
        $db = dbMysql::getInstance();
        $users = $db->table('qwuser')
            ->whereIn('id', $user_ids)
            ->field('id, wname')
            ->list();
        $userMap = array_column($users, 'wname', 'id');

        // 处理JSON字段
        foreach ($list['list'] as &$item) {
            $item['attach'] = json_decode($item['attach'], true);
            $item['operator_name'] = $userMap[$item['operator']] ?? '';
        }
        returnSuccess($list);
    }


     // 调整
     public function change() {
         $paras_list = array('id', 'num');
         $param = array_intersect_key($_POST, array_flip($paras_list));
         if (empty($param['id'])) returnError('ID不能为空');
         if (empty($param['num'])) returnError('调整金额不能为空');

         $sdb = dbSMysql::getInstance();
         $sdb->table('user_tax_prepay');
         $sdb->where('where id = :id', ['id' => $param['id']]);
         $prepay = $sdb->one();
         if (empty($prepay)) returnError('预缴记录不存在');

         $prepay['attach'] = json_decode($prepay['attach'], true);
         $prepay['attach']['adjust_num'] = $param['num'];

         $sdb->table('user_tax_prepay')->where('where id = :id', ['id' => $param['id']])
             ->update(['attach' => json_encode($prepay['attach'], JSON_UNESCAPED_UNICODE)]);
         returnSuccess([], '调整成功');

     }

}