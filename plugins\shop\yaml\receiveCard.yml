openapi: 3.0.0
info:
  title: 收款卡管理API
  version: 1.0.0
  description: 提供收款卡管理的相关接口

paths:
  /shop/receiveCard/getList:
    get:
      tags:
        - 收款卡管理
      summary: 获取收款卡列表
      description: 根据条件筛选获取收款卡列表
      parameters:
        - name: card_number
          in: query
          description: 卡号
          required: false
          schema:
            type: string
        - name: bank_name
          in: query
          description: 银行名称
          required: false
          schema:
            type: string
        - name: receive_platform
          in: query
          description: 收款平台
          required: false
          schema:
            type: string
        - name: card_status
          in: query
          description: 卡片状态
          required: false
          schema:
            type: string
        - name: main_receive_account_id
          in: query
          description: 主账户
          required: false
          schema:
            type: integer
        - name: receive_account_id
          in: query
          description: 子账户
          required: false
          schema:
            type: integer
        - name: currency
          in: query
          description: 币种
          required: false
          schema:
            type: string
        - name: use_platform
          in: query
          description: 使用平台
          required: false
          schema:
            type: string
        - name: update_time
          in: query
          description: 更新时间范围
          required: false
          schema:
            type: array
            items:
              type: string
              format: date-time
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
        - name: page_size
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: 成功返回列表数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/ReceiveCard'
                      total:
                        type: integer
                        description: 总记录数
                      page:
                        type: integer
                        description: 当前页码
                      page_size:
                        type: integer
                        description: 每页条数

  /shop/receiveCard/add:
    post:
      tags:
        - 收款卡管理
      summary: 添加收款卡
      description: 新增收款卡信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReceiveCardCreate'
      responses:
        '200':
          description: 添加成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 添加成功
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 新增记录ID

  /shop/receiveCard/edit:
    post:
      tags:
        - 收款卡管理
      summary: 编辑收款卡
      description: 修改收款卡信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReceiveCardEdit'
      responses:
        '200':
          description: 编辑成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 编辑成功

  /shop/receiveCard/detail:
    get:
      tags:
        - 收款卡管理
      summary: 获取收款卡详情
      description: 根据ID获取收款卡详细信息
      parameters:
        - name: id
          in: query
          description: 收款卡ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回详情数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    $ref: '#/components/schemas/ReceiveCard'

  /shop/receiveCard/import:
    post:
      tags:
        - 收款卡管理
      summary: 批量导入收款卡
      description: 通过Excel文件批量导入收款卡数据
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                excel_src:
                  type: string
                  description: Excel文件链接
              required:
                - excel_src
      responses:
        '200':
          description: 导入成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 导入成功
                  data:
                    type: object
                    properties:
                      error_file:
                        type: string
                        description: 错误数据文件路径
                      error_count:
                        type: integer
                        description: 错误数量
                      success_count:
                        type: integer
                        description: 成功数量

  /shop/receiveCard/export:
    get:
      tags:
        - 收款卡管理
      summary: 导出收款卡数据
      description: 根据筛选条件导出收款卡数据
      responses:
        '200':
          description: 导出成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 导出成功
                  data:
                    type: object
                    properties:
                      download_url:
                        type: string
                        description: 下载链接

  /shop/receiveCard/editBatch:
    post:
      tags:
        - 收款卡管理
      summary: 批量编辑收款卡
      description: 批量修改收款卡信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: array
                  description: 收款卡数据列表
                  items:
                    $ref: '#/components/schemas/ReceiveCardEdit'
              required:
                - data
      responses:
        '200':
          description: 批量编辑成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 批量编辑成功
                  data:
                    type: object
                    properties:
                      success_count:
                        type: integer
                        description: 成功数量
                      error_count:
                        type: integer
                        description: 失败数量

  /shop/receiveCard/getLog:
    get:
      tags:
        - 收款卡管理
      summary: 获取操作日志
      description: 获取收款卡的操作日志记录
      parameters:
        - name: id
          in: query
          description: 收款卡ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功返回日志数据
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: 成功
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        operator:
                          type: string
                          description: 操作人
                        operator_name:
                          type: string
                          description: 操作人姓名
                        operation_type:
                          type: string
                          description: 操作类型
                        before:
                          type: object
                          description: 修改前数据
                        updated_at:
                          type: string
                          format: date-time
                          description: 操作时间

components:
  schemas:
    ReceiveCard:
      type: object
      properties:
        id:
          type: integer
          description: ID
        card_number:
          type: string
          description: 卡号
        bank_name:
          type: string
          description: 银行名称
        receive_platform:
          type: string
          description: 收款平台
        main_receive_account_id:
          type: integer
          description: 主账户
        receive_account_id:
          type: integer
          description: 子账户
        currency:
          type: string
          description: 币种
        use_platform:
          type: string
          description: 使用平台
        card_status:
          type: string
          description: 卡片状态
        remark:
          type: string
          description: 备注

    ReceiveCardCreate:
      allOf:
        - $ref: '#/components/schemas/ReceiveCard'
        - type: object
          required:
            - card_number
            - bank_name
            - receive_platform

    ReceiveCardEdit:
      allOf:
        - $ref: '#/components/schemas/ReceiveCardCreate'
        - type: object
          required:
            - id
