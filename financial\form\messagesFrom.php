<?php
/**
 * @author: zhangguoming
 * @Time: 2024/2/26 17:21
 */

namespace financial\form;

use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use core\lib\predisV;
use financial\models\userModel;

//消息发送
class messagesFrom
{
    public static function senMsgByNoticeType(int $type, string $msg,int $model_id) {
        $dbF = dbFMysql::getInstance();
        $notice = $dbF->table('notice')
            ->where('where type=:type and is_delete =0',['type'=>$type])
            ->one();
        if ($notice) {
            if ($notice['user_ids1'] != '[]') {
                $user_ids1 = json_decode($notice['user_ids1']);
                $dbF = dbMysql::getInstance();
                $user_list = $dbF->table('qwuser')
                    ->whereIn('id',$user_ids1)
                    ->field('wid')
                    ->list();
                $wids = array_column($user_list,'wid');
                if (count($wids)) {
                    self::senMeg($wids,$type,$msg,$model_id);
                }
            }
        }
    }

    public static function senMeg(array $w_userids,int $msg_type,string $text,int $model_id) {
        $w_userids = array_unique($w_userids);
        //$w_userids = ['ZhangYunYing'];
        $db = dbFMysql::getInstance();
        $insert_data = [
            'user_id'=>userModel::$qwuser_id??0,
            'model_id'=>$model_id,
            'text'=>$text,
            'created_at'=>date('Y-m-d H:i:s'),
            'type'=>$msg_type,
            'title'=>self::getQwMsgTitle($msg_type),
        ];
        $msg_data = [
            'system_type'=>2,//系统1产品系统，2财务系统
            'type'=>'textcard',
            'msg'=>$text,
            'title'=>self::getQwMsgTitle($msg_type),
        ];
        new predisV();
        $db->table('messages');
        foreach ($w_userids as $k=>$user_id) {
            $insert_data['qw_userid'] = $user_id;
            $msg_id = $db->insert($insert_data);
//            $msg_id = 1;
            $msg_data['qw_userid'] = $user_id;
            $msg_data['data'] = json_encode(['id'=>$msg_id]);
            predisV::redisQueue($msg_data);
        }
        //websocket推送消息
//        $swool_data = [
//            'qw_userid'=>$w_userids,
//            'msg'=>$text
//        ];
//        //webSocket消息聊天通知
//        \core\lib\swoole\webSocketSwoole::sendMsg(json_encode($swool_data, JSON_UNESCAPED_UNICODE));
    }
    public static function getQwMsgTitle($type) {
        $title = '';
        switch ($type) {
            case 1;
                $title = '清仓通知';
                break;
            case 2;
                $title = '爆款通知';
                break;
            case 3;
                $title = "费用分摊结果通知";
                break;
            case 4;
                $title = '结账通知';
                break;
            case 5;
                $title = '反结账通知';
                break;
            case 6;
                $title = '店铺信息完善通知';
                break;
            case 7;
                $title = '店铺数据导入通知';
                break;
            case 8;
                $title = '库存数据导入通知';
                break;
            case 9;
                $title = '项目管理信息维护通知';
                break;
            case 10;
                $title = '预警审核通知';
                break;
            case 1;
                $title = '产品预警通知';
                break;
        }
        return $title;
    }




















}