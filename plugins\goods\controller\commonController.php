<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/13 17:52
 */

namespace  plugins\goods\controller;

use core\lib\config;

class commonController
{
    //字典列表
   public function patternList(){
       //dd(hexdec('FF'));
       $paras_list = array('key');
       $param = arrangeParam($_GET, $paras_list);
       $list = [];
       if (!empty($param['key'])) {
           $key_arry = explode(',',$param['key']);
           $c_data = config::all('data');
           foreach ($key_arry as $v) {
               if(isset($c_data[$v])) {
                   $list[$v] = $c_data[$v];
               }
           }

       } else {
           $list = config::all('data');
       }
       returnSuccess($list);
   }
}