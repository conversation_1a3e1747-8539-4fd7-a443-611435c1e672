<?php

namespace plugins\shop\controller;

use core\lib\config;
use core\lib\db\dbAfMysql;
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use plugins\shop\models\creditCardBaseModel;
use plugins\shop\models\creditCardModel;
use plugins\shop\models\domainModel;
use plugins\shop\models\userModel;

class baseController
{
    public function __construct()
    {
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && strpos($_SERVER['CONTENT_TYPE'], 'application/json') === 0) {
            $json = file_get_contents('php://input');
            $_POST = json_decode($json, true);
        }
    }

    public function getConfig()
    {
        $config = config::all('data_shop');
        $config['country'] = redisCached::getCountry();

        $config['country_site'] = redisCached::get('ywx_lx_country', function () {
            $fdb = dbFMysql::getInstance();
            // 站点国家
            $country_list = $fdb->table('market')
                ->field('id, country, code')
                ->where('where is_delete = 0')->list();
            return $country_list;
        }, 60 * 60);

        $config['currency'] = redisCached::get('ywx_currency', function () {
            // 获取最新一个月的日期
            $fdb = dbFMysql::getInstance();
            $latestDate = $fdb->table('routing')
                ->field('MAX(date) as latest_date')
                ->one();

            if ($latestDate && isset($latestDate['latest_date'])) {
                // 使用最新日期查询币种数据
                $list = $fdb->table('routing')
                    ->where('where date = :date', ['date' => $latestDate['latest_date']])
                    ->field('code, icon, name')
                    ->list();

                // 定义指定货币的排序顺序，使用关联数组提高查找效率
                $preferredOrder = array_flip(['人民币', '美元', '欧元', '英镑', '日元', '加元']);

                usort($list, function ($a, $b) use ($preferredOrder) {
                    // 获取排序位置，不存在的货币默认为 PHP_INT_MAX
                    $posA = isset($preferredOrder[$a['name']]) ? $preferredOrder[$a['name']] : PHP_INT_MAX;
                    $posB = isset($preferredOrder[$b['name']]) ? $preferredOrder[$b['name']] : PHP_INT_MAX;

                    // 直接比较位置
                    return $posA - $posB;
                });
                return $list;
            }
            return [];
        }, 60*60);

        $config['listing_category'] = redisCached::get('ywx_lx_listing_category', function () {
            $fdb = dbFMysql::getInstance();
            // 站点国家
            $category_list = $fdb->table('goods_category')
                ->field('id,cid,parent_cid,title')
                ->where('where is_delete = 0')
                ->order('sort asc')->list();
            return buildTree($category_list, 0, 'cid', 'parent_cid');
        }, 60*60);

        $config['email_suffix'] = redisCached::get('ywx_email_suffix', function () {
            $afdb = dbAfMysql::getInstance();
            $email_suffix_list = $afdb->table('email_suffix')
                ->field('id,suffix')
                ->where('is_delete = 0')->list();
            return $email_suffix_list;
        }, 60*60);

        $config['date'] = date('Y-m-d');
        returnSuccess($config);
    }

    public function getLightQueryList() {
        $type = $_GET['type'] ?? null;
        $module = $_GET['module'] ?? null;

        switch ($type) {
            case 'phone_card':
                $data = redisCached::getPhoneCard(1);
                $list = [];
                foreach ($data as $item) {
                    $item['disable_flag'] = 0;
                    if (in_array($module, ['email', 'receive_account', 'shop'])) {
                        if ($item['card_status'] != '正常') {
                            $item['disable_flag'] = 1;
                        }
                    }
                    $list[] = $item;
                }
                break;
            case 'email':
                $data = redisCached::getEmail(1);
                $list = [];
                foreach ($data as $item) {
                    $item['disable_flag'] = 0;
                    if (in_array($module, ['domain', 'receive_account', 'trademark'])) {
                        if ($item['use_status'] == '已停用') {
                            $item['disable_flag'] = 1;
                        }
                    } elseif (in_array($module, ['shop_register', 'shop'])) {
                        if ($item['use_status'] != '未启用') {
                            $item['disable_flag'] = 1;
                        }
                    }

                    $list[] = $item;
                }
                break;
            case 'domain':
                $data = redisCached::getDomain(1);
                $list = [];
                foreach ($data as $item) {
                    $item['disable_flag'] = 0;
                    if (in_array($module, ['trademark'])) {
                        if ($item['status'] == domainModel::STATUS_EXPIRED) {
                            $item['disable_flag'] = 1;
                        }
                    }
                    $list[] = $item;
                }
                break;
            case 'trademark':
                $dep = redisCached::getDepartment();
                $dep = array_column($dep, 'wp_id', 'id');

                $data = redisCached::getTrademark(1);
                $list = [];
                $user = dbMysql::getInstance()->table('qwuser')->where('where id = :id', ['id' => userModel::$qwuser_id])->field('wdepartment_ids')->one();
                $user_department = json_decode($user['wdepartment_ids'], true);

                foreach ($data as $item) {
                    $item['disable_flag'] = 0;
                    if (in_array($module, ['shop'])) {
                        if (!in_array($dep[$item['dep_id']], $user_department)) {
                            $item['disable_flag'] = 1;
                        }
                    }
                    if (in_array($module, ['trademark_apply'])) {
                        if (!empty($item['user_id'])) { // 商标申请，已有申请人的商标不能被分配
                            $item['disable_flag'] = 1;
                        }
                    }
                    $list[] = $item;
                }
                break;
            case 'credit_card':
                $data = redisCached::getCreditCard(1);
                $list = [];
                $credit_card_id = dbShopMysql::getInstance()->table('shop')->where('where credit_card_id > 0')->field('credit_card_id')->list() ?? [];
                $credit_card_id = array_column($credit_card_id, 'credit_card_id');
                foreach ($data as $item) {
                    $item['disable_flag'] = 0;
                    if (in_array($module, ['shop'])) {
                        if ($item['credit_card_status'] != creditCardModel::STATUS_USING ||
                            $item['activation_status'] != '已激活' ||
                            in_array($item['id'], $credit_card_id)) {
                            $item['disable_flag'] = 1;
                        }
                    } elseif (in_array($module, ['shop_register'])) {
                        if ($item['credit_card_status'] != creditCardModel::STATUS_USING ||
                            $item['activation_status'] != '已激活') {
                            $item['disable_flag'] = 1;
                        }
                    }
                    $list[] = $item;
                }
                break;
            case 'receive_card':
                $data = redisCached::getReceiveCard(1);
                $receive_card_list = dbShopMysql::getInstance()->table('shop')->where('where receive_card_id is not null')->field('receive_card_id')->list() ?? [];
                $receive_card_id = [];
                foreach ($receive_card_list as $item) {
                    $receive_card_id = array_merge($receive_card_id, json_decode($item['receive_card_id'], true));
                }
                $list = [];
                foreach ($data as $item) {
                    $item['disable_flag'] = 0;
                    if (in_array($module, ['shop_register', 'shop'])) {
                        if ($item['card_status'] != '正常' ||
                         in_array($item['id'], $receive_card_id)) {
                            $item['disable_flag'] = 1;
                        }
                    }
                    $list[] = $item;
                }
                break;
            case 'receive_account':
                $data = redisCached::getReceiveAccount(1);
                $list = [];
                foreach ($data as $item) {
                    $item['disable_flag'] = 0;
                    $list[] = $item;
                }
                break;
            case 'legal_person':
                $data = redisCached::getLegalPerson(1);
                $list = [];
                // 查询绑定过店铺的公司
                $legal_person_id = dbShopMysql::getInstance()->table('company')->where('where legal_person_id > 0')->field('legal_person_id')->list() ?? [];
                $legal_person_id = array_column($legal_person_id, 'legal_person_id');
                foreach ($data as $item) {
                    $item['disable_flag'] = 0;
                    if (in_array($module, ['company'])) {
                        if (in_array($item['id'], $legal_person_id)) {
                            $item['disable_flag'] = 1;
                        }
                    }
                    $list[] = $item;
                }
                break;
            case 'company':
                $data = redisCached::getCompany(1);
                $list = [];
                // 查询绑定过店铺的公司
                $company_id = dbShopMysql::getInstance()->table('shop')->where('where company_id > 0')->field('company_id')->list() ?? [];
                $company_id = array_column($company_id, 'company_id');
                foreach ($data as $item) {
                    $item['disable_flag'] = 0;
                    if (in_array($module, ['shop_register'])) {
                        if ($item['company_status'] != '正常' || in_array($item['id'], $company_id)) {
                            $item['disable_flag'] = 1;
                        }
                    }
                    $list[] = $item;
                }
                break;
            case 'shop':
                $data = redisCached::getShop(1);
                $list = [];
                foreach ($data as $item) {
                    $item['disable_flag'] = 0;
                    if (in_array($module, ['trademark'])) {
                        if (!$item['shop_status'] != '正常') {
                            $item['disable_flag'] = 1;
                        }
                    }
                    $list[] = $item;
                }
                break;
            case 'shop_apply':
                $data = redisCached::getShopApply(1);
                $list = [];
                foreach ($data as $item) {
                    $item['disable_flag'] = 0;
                    $list[] = $item;
                }
                break;
            default:
                $list = [];
        }
        returnSuccess($list);

    }

    public function getUserConfig() {
        $user_id = userModel::$qwuser_id ?? 0;
        $config = dbShopMysql::getInstance()->table('user_columns')
            ->where('user_id = :user_id', ['user_id' => $user_id])
            ->one();
        $user_columns = json_decode($config['attach'], true) ?? null;
        returnSuccess([
            'user_columns' => $user_columns ?? null,
        ]);
    }

    public function setUserConfig()
    {
        $user_id = userModel::$qwuser_id ?? 0;
        $user_columns = $_POST['user_columns'] ?? null;
        if (empty($user_columns)) {
            returnError('参数错误');
        }
        $user_columns = json_encode($user_columns);
        $db = dbShopMysql::getInstance();
        $config = $db->table('user_columns')
            ->where('user_id = :user_id', ['user_id' => $user_id])
            ->one();
        if ($config) {
            $db->table('user_columns')
                ->where('user_id = :user_id', ['user_id' => $user_id])
                ->update(['attach' => $user_columns]);
        } else {
            $db->table('user_columns')->insert([
                'user_id' => $user_id,
                'attach' => $user_columns,
            ]);
        }
        returnSuccess([], '保存成功');

    }

}