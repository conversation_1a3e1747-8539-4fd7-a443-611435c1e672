<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/8 16:58
 */

namespace  plugins\goods\form;

use plugins\goods\models\userModel;
use core\lib\db\dbMysql;

class goodsAbnormalLogFrom
{
    public static function setData($abnormal_id,$is_handled,$remark){
        $insert_data = [
            'qwuser_id'=>userModel::$qwuser_id,
            'wname'=>userModel::$wname,
            'abnormal_id'=>$abnormal_id,
            'remark'=>$remark,
            'is_handled'=>$is_handled,
            'created_at'=>date('Y-m-d H:i:s'),
        ];
        $db = dbMysql::getInstance();
        $db->table('goods_abnormal_log');
        $id = $db->insert($insert_data);
        return $id;
    }
}