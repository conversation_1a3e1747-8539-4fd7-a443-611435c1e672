<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/14 9:20
 */


return array(
    //流程类型
    'flow_path' => [
        ['id'=>1,'name'=>'测试样'],
        ['id'=>2,'name'=>'出货样'],
        ['id'=>3,'name'=>'抽货']
    ],
    'event_type' => [
        ['id'=>1,'name'=>'确认提交'],
        ['id'=>2,'name'=>'APP图片资源库申请'],
        ['id'=>3,'name'=>'APP功能验证'],
        ['id'=>4,'name'=>'硬件检测报告'],
        ['id'=>5,'name'=>'检测标准书生成'],
        ['id'=>6,'name'=>'申请下首单'],
        ['id'=>7,'name'=>'附件上传'],
        ['id'=>8,'name'=>'收样建档'],   //流程中不能完成
        ['id'=>9,'name'=>'流程申请'],  //流程中不能完成
        ['id'=>10,'name'=>'app适配'],  //流程中不能完成
    ],
    'new_goods_status' => [
        ['id'=>0,'name'=>'未开始'],
        ['id'=>1,'name'=>'进行中'],
        ['id'=>2,'name'=>'已出货'],
        ['id'=>3,'name'=>'异常'],
        ['id'=>4,'name'=>'暂停'],
    ],
    //流程状态(事件和节点也在用)
    'goods_project_status' => [
        ['id'=>1,'name'=>'进行中'],
        ['id'=>2,'name'=>'已完成'],
        ['id'=>3,'name'=>'异常'],
        ['id'=>4,'name'=>'作废'],
    ],
    //产品附件类型
    'goods_attachment_type'=>[
        ['id'=>1,'name'=>'3D模型图'],
        ['id'=>2,'name'=>'产品规格书'],
        ['id'=>3,'name'=>'电池报告'],
        ['id'=>4,'name'=>'专利'],
        ['id'=>5,'name'=>'资质认证报告'],
        ['id'=>6,'name'=>'电机报告'],
    ],
    //长度单位
    'length_unit'=>[
        ['id'=>1,'name'=>'cm'],
        ['id'=>2,'name'=>'inch'],
    ],
    //收货批次
    'goods_sample_batch'=>[
        ['id'=>-1,'name'=>'全部'],
        ['id'=>1,'name'=>'首批'],
        ['id'=>2,'name'=>'次批'],
    ],
    //出货样，抽货样批次
    'flow_path2_batch'=>[
        ['id'=>-1,'name'=>'全部'],
        [
            'id'=>2,
            'name'=>'出货',
            'child'=>[
                ['id'=>-1,'name'=>'全部'],
                ['id'=>1,
                'name'=>'首批',
                'child'=>[
                    ['id'=>-1,'name'=>'全部'],
                    ['id'=>1,'name'=>'首批'],
                    ['id'=>2,'name'=>'首货2批次'],
                    ['id'=>3,'name'=>'首货3批次'],
                    ['id'=>4,'name'=>'首货4批次'],
                    ['id'=>5,'name'=>'首货5批次'],
                    ['id'=>6,'name'=>'首货6批次'],
                    ['id'=>7,'name'=>'首货7批次'],
                    ['id'=>8,'name'=>'首货8批次'],
                    ['id'=>9,'name'=>'首货9批次'],
                    ['id'=>10,'name'=>'首货10批次'],
                    ['id'=>11,'name'=>'首货11批次'],
                    ['id'=>12,'name'=>'首货12批次'],
                ]],
                ['id'=>2,'name'=>'第2批'],
                ['id'=>3,'name'=>'第3批'],
                ['id'=>4,'name'=>'第4批'],
                ['id'=>5,'name'=>'第5批'],
                ['id'=>6,'name'=>'第6批'],
                ['id'=>7,'name'=>'第7批'],
                ['id'=>8,'name'=>'第8批'],
                ['id'=>9,'name'=>'第9批'],
                ['id'=>10,'name'=>'第10批'],
                ['id'=>11,'name'=>'第11批'],
                ['id'=>12,'name'=>'第12批'],
            ]
        ],
        ['id'=>3,'name'=>'抽货','child'=>[
            ['id'=>-1,'name'=>'全部'],
            ['id'=>1,'name'=>'首批'],
            ['id'=>2,'name'=>'第2批'],
            ['id'=>3,'name'=>'第3批'],
            ['id'=>4,'name'=>'第4批'],
            ['id'=>5,'name'=>'第5批'],
            ['id'=>6,'name'=>'第6批'],
            ['id'=>7,'name'=>'第7批'],
            ['id'=>8,'name'=>'第8批'],
            ['id'=>9,'name'=>'第9批'],
            ['id'=>10,'name'=>'第10批'],
            ['id'=>11,'name'=>'第11批'],
            ['id'=>12,'name'=>'第12批'],
        ]],
    ],
    //大货样批次
    'flow_path1_batch'=>[
        ['sample_batch'=>-1,'name'=>'全部'],
        ['sample_batch'=>1,'name'=>'首批'],
        ['sample_batch'=>2,'name'=>'第2批'],
        ['sample_batch'=>3,'name'=>'第3批'],
        ['sample_batch'=>4,'name'=>'第4批'],
        ['sample_batch'=>5,'name'=>'第5批'],
        ['sample_batch'=>6,'name'=>'第6批'],
        ['sample_batch'=>7,'name'=>'第7批'],
        ['sample_batch'=>8,'name'=>'第8批'],
        ['sample_batch'=>9,'name'=>'第9批'],
        ['sample_batch'=>10,'name'=>'第10批'],
        ['sample_batch'=>11,'name'=>'第11批'],
        ['sample_batch'=>12,'name'=>'第12批'],
    ],
    //待办事项类型
    'matter_status'=>[
        ['id'=>-1,'name'=>'全部'],
        ['id'=>0,'name'=>'进行中'],
        ['id'=>1,'name'=>'已完成'],
        ['id'=>2,'name'=>'异常'],
        ['id'=>3,'name'=>'暂停'],
    ],
    //待办事项创建的类型
    'matter_create_type'=>[
        ['id'=>-1,'name'=>'全部'],
        ['id'=>0,'name'=>'普通类型'],
        ['id'=>1,'name'=>'审核'],
        ['id'=>2,'name'=>'抄送'],
    ],
    //异常等级
    'abnormal_level' => [
        ['id'=>-1,'name'=>'全部'],
        ['id'=>1,'name'=>'普通'],
        ['id'=>2,'name'=>'一般'],
        ['id'=>3,'name'=>'严重']
    ],
    //异常问题枚举
    'abnormal_soft_txt'=>[
        ['id'=>1,'name'=>'灯光控制出错'],
        ['id'=>2,'name'=>'功能不匹配'],
        ['id'=>3,'name'=>'功能故障'],
        ['id'=>4,'name'=>'连接问题'],
        ['id'=>5,'name'=>'按键逻辑不正确'],
        ['id'=>6,'name'=>'其他']
    ],
    'abnormal_hard_txt'=>[
        ['id'=>1,'name'=>'产品断连'],
        ['id'=>2,'name'=>'产品自动关机'],
        ['id'=>3,'name'=>'产品过热'],
        ['id'=>4,'name'=>'噪音异响'],
        ['id'=>5,'name'=>'其他']
    ],
    //产品图需求类型
    'imgs_request_type'=>[
        ['id'=>1,'name'=>'产品需求'],
        ['id'=>2,'name'=>'品牌需求'],
        ['id'=>3,'name'=>'内部需求'],
        ['id'=>4,'name'=>'其他']
    ],
    //业务平台
    'imgs_platform'=>[
        ['id'=>1,'name'=>'APP'],
        ['id'=>2,'name'=>'亚马逊'],
        ['id'=>3,'name'=>'allegro'],
        ['id'=>4,'name'=>'temu'],
        ['id'=>5,'name'=>'shein'],
        ['id'=>6,'name'=>'美客多'],
        ['id'=>7,'name'=>'沃尔玛'],
        ['id'=>8,'name'=>'emag'],
        ['id'=>9,'name'=>'wb'],
        ['id'=>10,'name'=>'zozn'],
        ['id'=>11,'name'=>'其他']
    ],
    //具体需求类别
    'imgs_second_type'=>[
        ['id'=>1,'name'=>'主图'],
    ],
    //硬件检测审核不合格项
    'hardware_check_reason'=>[
        ['id'=>1,'name'=>'使用时长问题'],
        ['id'=>2,'name'=>'电池类问题'],
        ['id'=>3,'name'=>'马达类问题'],
        ['id'=>4,'name'=>'充电类问题'],
        ['id'=>5,'name'=>'硅胶类问题'],
        ['id'=>6,'name'=>'结构装配类问题'],
        ['id'=>7,'name'=>'防水类问题'],
        ['id'=>8,'name'=>'主板类问题'],
        ['id'=>9,'name'=>'噪音问题'],
        ['id'=>10,'name'=>'其他'],
    ],

    //文档管理-文档类型
    'goods_file_type' => [
        ['id'=>0,'name'=>'全部'],
        ['id'=>1,'name'=>'产品说明书'],
//        ['id'=>2,'name'=>'产品规格书'],
        ['id'=>3,'name'=>'硬件检测报告'],
        ['id'=>4,'name'=>'检测标准书'],
        ['id'=>5,'name'=>'其他']
    ],
    //图片拍摄需求类别
    'take_picture_type' => [
        ['id'=>0,'name'=>'全部'],
        ['id'=>1,'name'=>'APP产品'],
        ['id'=>2,'name'=>'产品说明书']
    ],
    //图片需求-美工擦还是提醒
    'imgs_request_timeout_remind' => [
        ['id'=>0,'name'=>'不提醒'],
        ['id'=>5,'name'=>'每隔5天提醒一次'],
        ['id'=>7,'name'=>'每隔7天提醒一次'],
        ['id'=>15,'name'=>'每隔15天提醒一次'],
        ['id'=>30,'name'=>'每隔30天提醒一次']
    ],
    // 语言枚举
    'language_list' => [
        ['id' => 'en', 'name' => '英语'],
        ['id' => 'zh', 'name' => '中文'],
        ['id' => 'es', 'name' => '西班牙语'],
        ['id' => 'fr', 'name' => '法语'],
        ['id' => 'ar', 'name' => '阿拉伯语'],
        ['id' => 'ru', 'name' => '俄语'],
        ['id' => 'de', 'name' => '德语'],
        ['id' => 'ja', 'name' => '日语'],
        ['id' => 'pt', 'name' => '葡萄牙语'],
        ['id' => 'hi', 'name' => '印地语'],
        ['id' => 'bn', 'name' => '孟加拉语'],
        ['id' => 'tr', 'name' => '土耳其语'],
        ['id' => 'it', 'name' => '意大利语'],
        ['id' => 'ko', 'name' => '韩语'],
        ['id' => 'nl', 'name' => '荷兰语'],
        ['id' => 'pl', 'name' => '波兰语'],
        ['id' => 'th', 'name' => '泰语'],
        ['id' => 'vi', 'name' => '越南语'],
        ['id' => 'id', 'name' => '印尼语'],
    ],
    //图片测试
    'imgs_request_test_channel' => [
        ['id' => '1', 'name' => 'Banner'],
        ['id' => '2', 'name' => '社区帖子'],
//        ['id' => '3', 'name' => '邮件'],
    ],
    // 需求性质
    'imgs_request_nature' => [
        ['id' => '1', 'name' => '新图制作'],
        ['id' => '2', 'name' => '图片优化'],
        ['id' => '3', 'name' => '套版任务'],
        ['id' => '4', 'name' => '其他'],
        ['id' => '5', 'name' => 'APP产品图'],
    ],
);