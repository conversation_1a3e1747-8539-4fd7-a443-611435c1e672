<?php

namespace plugins\logistics\controller;

use plugins\logistics\models\fbaInboundShipmentDetailModel;

/**
 * FBA货件明细业务控制器
 * 处理列表查询、编辑、导入导出功能
 */
class fbaInboundShipmentController extends baseController
{
    private $model;

    public function __construct()
    {
        $this->model = new fbaInboundShipmentDetailModel();
    }

    /**
     * 获取FBA货件明细列表
     * @return void
     */
    public function getList()
    {
        try {
            // 获取查询参数
            $params = [
                'page' => (int)($_GET['page'] ?? 1),
                'page_size' => (int)($_GET['page_size'] ?? 20),
                'country' => $_GET['country'] ?? '',
                'plan_date_start' => $_GET['plan_date_start'] ?? '',
                'plan_date_end' => $_GET['plan_date_end'] ?? '',
                'warehouse_name' => $_GET['warehouse_name'] ?? '',
                'transport_method' => $_GET['transport_method'] ?? '',
                'product_name' => $_GET['product_name'] ?? '',
                'shop_code' => $_GET['shop_code'] ?? '',
                'fnsku' => $_GET['fnsku'] ?? '',
                'warehouse_code' => $_GET['warehouse_code'] ?? '',
                'remark' => $_GET['remark'] ?? ''
            ];

            // 获取列表数据
            $result = $this->model->getDetailList($params);

            returnSuccess($result);

        } catch (\Exception $e) {
            SetReturn(-1, "获取列表失败：" . $e->getMessage(), []);
        }
    }

    /**
     * 更新FBA货件明细（仅可编辑字段）
     * @return void
     */
    public function update()
    {
        try {
            // 获取参数
            $id = $_POST['id'] ?? 0;
            $shipmentCode = $_POST['shipment_code'] ?? '';
            if (empty($shipmentCode)) {
                SetReturn(-1, "货件编码不能为空", []);
                return;
            }

            // 获取可编辑字段数据
            $editableData = [
                'tracking_number' => $_POST['tracking_number'] ?? '',
                'transparent_label' => $_POST['transparent_label'] ?? '',
                'is_label_changed' => (int)($_POST['is_label_changed'] ?? 0),
                'remark' => $_POST['remark'] ?? '',
                'remark2' => $_POST['remark2'] ?? ''
            ];

            // 更新数据
            $result = $this->model->updateEditableFields($id, $shipmentCode, $editableData);

            if ($result) {
                SetReturn(2, "更新成功", ['shipment_code' => $shipmentCode]);
            } else {
                SetReturn(-1, "更新失败，请检查货件编码是否正确", []);
            }

        } catch (\Exception $e) {
            SetReturn(-1, "更新失败：" . $e->getMessage(), []);
        }
    }

    /**
     * 导出FBA货件明细
     * @return void
     */
    public function export()
    {
        try {
            // 获取筛选参数
            $params = [
                'country' => $_POST['country'] ?? '',
                'plan_date_start' => $_POST['plan_date_start'] ?? '',
                'plan_date_end' => $_POST['plan_date_end'] ?? '',
                'warehouse_name' => $_POST['warehouse_name'] ?? '',
                'transport_method' => $_POST['transport_method'] ?? '',
                'product_name' => $_POST['product_name'] ?? '',
                'shop_code' => $_POST['shop_code'] ?? '',
                'fnsku' => $_POST['fnsku'] ?? '',
                'warehouse_code' => $_POST['warehouse_code'] ?? '',
                'remark' => $_POST['remark'] ?? '',
                'page_size' => 10000 // 导出时获取大量数据
            ];

            // 获取导出数据
            $result = $this->model->getDetailList($params);
            $data = $result['list'] ?? [];

            if (empty($data)) {
                SetReturn(-1, "没有找到符合条件的数据", []);
                return;
            }

            // 生成Excel文件
            $filename = $this->generateExcelFile($data);

            SetReturn(2, "导出成功", [
                'filename' => $filename,
                'count' => count($data)
            ]);

        } catch (\Exception $e) {
            SetReturn(-1, "导出失败：" . $e->getMessage(), []);
        }
    }

    /**
     * 导入FBA货件明细（仅修改模式）
     * @return void
     */
    public function import()
    {
        try {
            if (!isset($_FILES['import_file'])) {
                SetReturn(-1, "请选择要导入的文件", []);
                return;
            }

            $file = $_FILES['import_file'];
            $importData = $this->parseImportFile($file);

            if (empty($importData)) {
                SetReturn(-1, "导入文件为空或格式错误", []);
                return;
            }

            // 批量更新数据
            $result = $this->batchUpdateFromImport($importData);

            SetReturn(2, "导入完成", [
                'total_count' => count($importData),
                'success_count' => $result['success_count'],
                'error_count' => $result['error_count'],
                'errors' => $result['errors']
            ]);

        } catch (\Exception $e) {
            SetReturn(-1, "导入失败：" . $e->getMessage(), []);
        }
    }

    /**
     * 解析导入文件
     * @param array $file 上传文件信息
     * @return array
     */
    private function parseImportFile($file)
    {
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        
        if (!in_array(strtolower($extension), ['xlsx', 'xls', 'csv'])) {
            throw new \Exception("不支持的文件格式，请使用Excel或CSV文件");
        }
        
        return $this->parseExcelFile($file['tmp_name']);
    }

    /**
     * 解析Excel文件
     * @param string $filePath 文件路径
     * @return array
     */
    private function parseExcelFile($filePath)
    {
        $data = [];
        
        if (($handle = fopen($filePath, "r")) !== FALSE) {
            $headers = fgetcsv($handle); // 读取表头
            
            while (($row = fgetcsv($handle)) !== FALSE) {
                if (count($row) >= count($headers)) {
                    $rowData = array_combine($headers, $row);
                    $validatedData = $this->validateImportRow($rowData);
                    if ($validatedData) {
                        $data[] = $validatedData;
                    }
                }
            }
            fclose($handle);
        }
        
        return $data;
    }

    /**
     * 验证导入行数据
     * @param array $rowData 行数据
     * @return array|null
     */
    private function validateImportRow($rowData)
    {
        // 货件编码为必填字段
        if (empty($rowData['货件编码'])) {
            return null;
        }
        
        return [
            'shipment_code' => trim($rowData['货件编码']),
            'tracking_number' => trim($rowData['跟踪单号'] ?? ''),
            'transparent_label' => trim($rowData['透明标签'] ?? ''),
            'is_label_changed' => $this->parseBoolean($rowData['是否换标'] ?? ''),
            'remark' => trim($rowData['备注'] ?? ''),
            'remark2' => trim($rowData['备注2'] ?? '')
        ];
    }

    /**
     * 解析布尔值
     * @param string $value 值
     * @return int
     */
    private function parseBoolean($value)
    {
        $value = strtolower(trim($value));
        return in_array($value, ['是', 'yes', '1', 'true']) ? 1 : 0;
    }

    /**
     * 批量更新导入数据
     * @param array $importData 导入数据
     * @return array
     */
    private function batchUpdateFromImport($importData)
    {
        $result = [
            'success_count' => 0,
            'error_count' => 0,
            'errors' => []
        ];

        foreach ($importData as $index => $data) {
            try {
                $shipmentCode = $data['shipment_code'];
                unset($data['shipment_code']);

                $updateResult = $this->model->updateEditableFields($shipmentCode, $data);
                
                if ($updateResult) {
                    $result['success_count']++;
                } else {
                    $result['error_count']++;
                    $result['errors'][] = "第" . ($index + 1) . "行：货件编码{$shipmentCode}不存在或更新失败";
                }

            } catch (\Exception $e) {
                $result['error_count']++;
                $result['errors'][] = "第" . ($index + 1) . "行：" . $e->getMessage();
            }
        }

        return $result;
    }

    /**
     * 生成Excel导出文件
     * @param array $data 数据
     * @return string 文件名
     */
    private function generateExcelFile($data)
    {
        $filename = "fba_inbound_shipment_detail_" . date('Ymd_His') . ".csv";
        $filepath = "/tmp/" . $filename;
        
        $fp = fopen($filepath, 'w');
        
        // 写入BOM头，解决中文乱码
        fwrite($fp, "\xEF\xBB\xBF");
        
        // 写入表头（24个字段）
        $headers = [
            '发货仓库', '计划日期', '组别', '负责人', '站点', '运输方式', '品名', 'ASIN', 'FNSKU',
            '计划数量', '箱数', '辅料', '货件编码', '收货地址', '跟踪单号', '发货日期', '货件状态',
            '送达时段', '签收数量', '差异', '透明标签', '是否换标', '备注', '备注2'
        ];
        fputcsv($fp, $headers);
        
        // 写入数据
        foreach ($data as $row) {
            $csvRow = [
                $row['warehouse_name'],
                $row['plan_date'],
                $row['group_name'],
                $row['responsible_person'],
                $row['site'],
                $row['transport_method'],
                $row['product_name'],
                $row['asin'],
                $row['fnsku'],
                $row['plan_quantity'],
                $row['box_count'],
                $row['auxiliary_materials'],
                $row['shipment_code'],
                $row['delivery_address'],
                $row['tracking_number'],
                $row['ship_date'],
                $row['shipment_status'],
                $row['delivery_time_slot'],
                $row['received_quantity'],
                $row['difference'],
                $row['transparent_label'],
                $row['is_label_changed'] ? '是' : '否',
                $row['remark'],
                $row['remark2']
            ];
            fputcsv($fp, $csvRow);
        }
        
        fclose($fp);
        return $filename;
    }
}
