-- FBA发货单主表（简化版）
CREATE TABLE IF NOT EXISTS `lingxing_inbound_shipment` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `shipment_id` int(11) NOT NULL DEFAULT 0 COMMENT '发货单ID（领星系统ID）',
  `shipment_sn` varchar(100) NOT NULL DEFAULT '' COMMENT '发货单号',
  `status` tinyint(2) NOT NULL DEFAULT 0 COMMENT '发货单状态：-1待配货，0待发货，1已发货，2已完成，3已作废',
  `shipment_time` datetime NULL COMMENT '发货时间',
  `shipment_time_second` datetime NULL COMMENT '发货时间（精确到时分秒）',
  `wid` int(11) NOT NULL DEFAULT 0 COMMENT '仓库ID',
  `wname` varchar(255) NOT NULL DEFAULT '' COMMENT '仓库名称',
  `create_user` varchar(100) NOT NULL DEFAULT '' COMMENT '创建用户',
  `logistics_provider_id` varchar(50) NOT NULL DEFAULT '' COMMENT '物流商ID',
  `logistics_provider_name` varchar(255) NOT NULL DEFAULT '' COMMENT '物流商名称',
  `logistics_channel_name` varchar(255) NOT NULL DEFAULT '' COMMENT '物流渠道名称',
  `expected_arrival_date` date NULL COMMENT '到货时间',
  `actual_shipment_time` datetime NULL COMMENT '实际发货时间（已废弃）',
  `etd_date` date NULL COMMENT '开船时间',
  `eta_date` date NULL COMMENT '预计到港时间',
  `delivery_date` date NULL COMMENT '实际妥投时间',
  `gmt_create` datetime NULL COMMENT '创建时间（精确到时分秒）',
  `is_pick` tinyint(1) NOT NULL DEFAULT 0 COMMENT '拣货状态：0未拣货，1已拣货',
  `is_print` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否打印：0否，1是',
  `pick_time` datetime NULL COMMENT '拣货时间',
  `print_num` int(11) NOT NULL DEFAULT 0 COMMENT '打印次数',
  `head_fee_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '头程费分配方式：0按计费重，1按实重，2按体积重，3按SKU数量，4自定义，5按箱子体积',
  `head_fee_type_name` varchar(100) NOT NULL DEFAULT '' COMMENT '头程分摊名称',
  `head_fee_type_name_new` varchar(100) NOT NULL DEFAULT '' COMMENT '新头程费分配名称方式',
  `file_id` varchar(255) NOT NULL DEFAULT '' COMMENT '附件文件',
  `update_time` datetime NULL COMMENT '更新时间',
  `remark` text COMMENT '备注',
  `is_return_stock` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否恢复库存：0否，1是',
  `pay_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '付款状态：0未申请，1已申请，2部分付款，3已付清，4无',
  `audit_status` int(11) NOT NULL DEFAULT 0 COMMENT '审批状态：121待审核，122驳回，123通过，124作废',
  `shipment_user` varchar(100) NOT NULL DEFAULT '' COMMENT '发货人',
  `is_exist_declaration` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否关联报关单：0否，1是',
  `is_exist_clearance` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否关联清关单：0否，1是',
  `third_party_order_mode` tinyint(1) NOT NULL DEFAULT 0 COMMENT '下单模式：0无，1系统下单，2手工下单',
  `third_party_order_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '第三方仓下单状态：1未下单，2已下单，3异常，4已发货',
  `vat_code` varchar(100) NOT NULL DEFAULT '' COMMENT '店铺VAT税号',
  `method_id` varchar(50) NOT NULL DEFAULT '' COMMENT '运输方式ID',
  `method_name` varchar(255) NOT NULL DEFAULT '' COMMENT '运输方式名称',
  `is_custom_shipment_time` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否自定义发货时间：1是，0否',
  `destination_fulfillment_center_id` varchar(255) NOT NULL DEFAULT '' COMMENT '物流中心编码',
  `status_name` varchar(50) NOT NULL DEFAULT '' COMMENT '状态名称',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除状态：0未删除，1已删除',
  
  -- Array字段直接存储为TEXT
  `logistics_data` text COMMENT '物流轨迹信息JSON格式',
  `relate_list_data` text COMMENT '关联货件列表JSON格式',
  `not_relate_list_data` text COMMENT '未关联货件列表JSON格式',
  `file_list_data` text COMMENT '文件列表JSON格式',
  
  -- 系统字段
  `sync_date` date NOT NULL COMMENT '同步日期',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0否，1是',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_shipment_id_date` (`shipment_id`, `sync_date`),
  KEY `idx_shipment_sn` (`shipment_sn`),
  KEY `idx_status` (`status`),
  KEY `idx_wid` (`wid`),
  KEY `idx_sync_date` (`sync_date`),
  KEY `idx_is_deleted` (`is_deleted`),
  KEY `idx_is_delete` (`is_delete`),
  KEY `idx_gmt_create` (`gmt_create`),
  KEY `idx_shipment_time` (`shipment_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='FBA发货单主表（简化版）';
