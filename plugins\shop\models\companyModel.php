<?php

namespace plugins\shop\models;

use admin\models\qwdepartmentModel;
use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use Exception;
use plugins\shop\form\messagesFrom;

class companyModel extends baseModel
{
    public string $table = 'company';

    const REGISTER_STATUS_WAIT = 1; // 待注册
    const REGISTER_STATUS_REGISTERING = 2; // 注册中
    const REGISTER_STATUS_SUCCESS = 3; // 注册成功
    const REGISTER_STATUS_WAIT_CANCEL = 4; // 待注销
    const REGISTER_STATUS_CANCELED = 5; // 已注销


    // 参数列表
    public static array $paras_list = [
        'legal_person_id'       => '法人ID',
        'company_status'        => '公司状态',
        'register_status'       => '注册状态',      // 1-待注册 2-注册中 3-注册成功, 4-待注销, 5-已注销
        'company_name'          => '公司名称',
        'register_country'      => '公司国家',
        'register_city'         => '注册城市',
        'register_type'         => '注册类型',      // 1-法人自注册, 2-公司代注册
        'tax_number'            => '纳税人识别号',
        'register_date'         => '注册日期',
        'register_address'      => '注册地址',
        'register_coordinator'  => '注册对接人',
        'house_number'          => '房屋编码',
        'license_keeper'        => '营业执照保管方',
        'e_license'             => '电子营业执照',
        'license_download_date' => '执照下载日期',
        'receive_card_id'       => '银行公户账号',
        'accountants'           => '记账报税人ID',     // JSON数组;
        'remark'                => '备注',
//        'status_change_time'    => '状态变化时间',
    ];

    public static array $json_keys = [
        'e_license',
        'accountants',
    ];

    public function getTimeAuthStr($type)
    {
        $auth_detail = userModel::$auth_detail;
        if (!isset($auth_detail['company'])) {
            return [];
        }
        $str = [];
        $arr = [];
        $idx = 0;
        foreach ($auth_detail['company'] as $auth_item) {
            if ($auth_item['auth'] != $type) continue;
            if (isset($auth_item['detail']) && !empty($auth_item['detail'])) {
                if ($auth_item['detail']['1'] == 'INF') {
                    $str[] = 'c.created_at >= :auth_s_date'.$idx;
                    $arr['auth_s_date'.$idx] = $auth_item['detail']['0'].' 00:00:00';
                } else {
                    $str[] = 'c.created_at >= :auth_s_date'.$idx.' and c.created_at <= :auth_e_date'.$idx;
                    $arr['auth_s_date'.$idx] = $auth_item['detail']['0']. ' 00:00:00';
                    $arr['auth_e_date'.$idx] = $auth_item['detail']['1']. ' 23:59:59';
                }
            }
            $idx++;
        }
        if (empty($str)) {
            return [];
        }
        $str = implode(' or ', $str);
        return [
            'str' => '(' . $str . ')',
            'arr' => $arr,
        ];
    }

    // 获取列表
    public function getList($param, $order = 'id desc', $is_export = false)
    {
        // 权限控制
        $user_id = userModel::$qwuser_id;
        $all_department_users = qwdepartmentModel::getUserDepartment(userModel::$wid, false)['all_department_users'] ?: []; // 当前用户管理的部门及其子部门下的用户
        $user_ids = array_column($all_department_users, 'id');

        $this->db->table($this->table, 'c')
            ->field('c.*')
            ->leftJoin('legal_person', 'lp', 'lp.id = c.legal_person_id')
            ->where('1=1');

        if (userModel::getUserListAuth('companyAll')) {
            // 全部权限
            $str = $this->getTimeAuthStr('companyAll');
            if (!empty($str) && !empty($str['str']) && !empty($str['arr'])) {
                $this->db->andWhere($str['str'], $str['arr']);
            }
        } elseif (userModel::getUserListAuth('companyDepartment')) {
            if (!empty($all_department_users)) {
                $this->db->whereIn('c.user_id', $user_ids);
            }
            $str = $this->getTimeAuthStr('companyDepartment');
            if (!empty($str) && !empty($str['str']) && !empty($str['arr'])) {
                $this->db->andWhere($str['str'], $str['arr']);
            }
        } elseif (userModel::getUserListAuth('companyRelated')) {
            $this->db->andWhere('c.user_id = :user_id', ['user_id' => $user_id]);
            $str = $this->getTimeAuthStr('companyRelated');
            if (!empty($str) && !empty($str['str']) && !empty($str['arr'])) {
                $this->db->andWhere($str['str'], $str['arr']);
            }
        }

        // 固定筛选
        if (!empty($param['ids'])) {
            $this->db->whereIn('c.id', $param['ids']);
        }

        // 房屋编号
        if (!empty($param['house_number'])) {
            $this->db->andWhere('house_number = :house_number', ['house_number' => $param['house_number']]);
        }
        // 纳税人识别号
        if (!empty($param['tax_number'])) {
            $this->db->andWhere('tax_number = :tax_number', ['tax_number' => $param['tax_number']]);
        }
        // 公司名称
        if (!empty($param['company_name'])) {
            $this->db->andWhere('company_name like :company_name', ['company_name' => '%'. $param['company_name'] .'%']);
        }
        // 法人
        if (!empty($param['legal_person'])) {
            $this->db->andWhere('lp.name like :legal_person', ['legal_person' => '%'. $param['legal_person'] .'%']);
        }
        // 注册城市
        if (!empty($param['register_city'])) {
            $this->db->andWhere('register_city like :register_city', ['register_city' => '%' . $param['register_city'] . '%']);
        }
        // 注册地址
        if (!empty($param['register_address'])) {
            $this->db->andWhere('register_address like :register_address', ['register_address' => '%' . $param['register_address'] . '%']);
        }
        // 营业执照保管方
        if (!empty($param['license_keeper'])) {
            $this->db->andWhere('license_keeper like :license_keeper', ['license_keeper' => '%' . $param['license_keeper'] . '%']);
        }
        // 公司状态
        if (!empty($param['company_status']) && is_array($param['company_status'])) {
            $this->db->whereIn('company_status', $param['company_status']);
        }
        // 注册状态
        if (!empty($param['register_status']) && is_array($param['register_status'])) {
            $this->db->whereIn('register_status', $param['register_status']);
        }
        // 注册日期
        if (!empty($param['register_date'])) {
            $this->db->andWhere('c.register_date >= :register_date_start and c.register_date <= :register_date_end', [
                'register_date_start' => $param['register_date'][0],
                'register_date_end' => $param['register_date'][1]
            ]);
        }
        // 类型
        if (!empty($param['register_type'])) {
            $this->db->andWhere('register_type = :register_type', ['register_type' => $param['register_type']]);
        }
        // 收款卡号
        if (!empty($param['receive_card_id'])) {
            $this->db->andWhere('receive_card_id = :receive_card_id', ['receive_card_id' => $param['receive_card_id']]);
        }
        // 营业执照
        if (isset($param['e_license'])) {
            if ($param['e_license'] == 1) {
                $this->db->andWhere('e_license is not null');
            } else {
                $this->db->andWhere('e_license is null');
            }
        }


        // 排序
        $this->db->order($order);

        if (!$is_export && !empty($param['page']) && !empty($param['page_size'])) {
            $list = $this->db->pages($param['page'], $param['page_size']);
            $maps = self::getMaps();
            foreach ($list['list'] as &$item) {
                $item = $this->formatItem($item, $maps);
            }
            return $list;
        } else {
            $list = $this->db->list();
            if ($is_export) {
                $export_data = [];
                $paras_list = static::$paras_list;
                $maps = self::getMaps();
                $paras_list['accountants_user'] = '记账报税人';
                $paras_list['receive_card_number'] = '开立银行公户';
                foreach ($list as $item) {
                    $item = $this->formatItem($item, $maps);
                    // 使用参数对应的中文作为键名返回
                    $export_data[] = self::changeToCnKey($item, $paras_list);
                }
                return $export_data;
            }

            return $list;
        }
    }

    // 格式化单条记录
    public function formatItem($item, $maps = [])
    {
        $legal_persons = $maps['legal_persons'] ?? [];
        if (empty($legal_persons)) {
            $legal_persons = redisCached::getLegalPerson();
            $legal_persons = array_column($legal_persons, 'name', 'id');
        }
        $receive_card = $maps['receive_card'] ?? [];
        if (empty($receive_card)) {
            $receive_card = redisCached::getReceiveCard();
            $receive_card = array_column($receive_card, 'card_number', 'id');
        }
        $users = $maps['users'] ?? [];
        if (empty($users)) {
            $ori_users = redisCached::getUserInfo();
            $users = [];
            foreach ($ori_users as $u) {
                $users[] = [
                    'id' => $u['user_id'],
                    'user_name' => $u['user_name'],
                ];
            }
        }
        $maps = [
            ['name' => 'legal_person_name', 'maps' => $legal_persons, 'key' => 'legal_person_id'],
            ['name' => 'receive_card_number', 'maps' => $receive_card, 'key' => 'receive_card_id'],
            ['name' => 'accountants_user', 'maps' => $users, 'key' => 'accountants', 'is_array' => 1, 'keys' => ['user_name']],
        ];
        return parent::formatItem($item, $maps);
    }

    public static function getMaps()
    {
        $legal_persons = redisCached::getLegalPerson();
        $legal_persons = array_column($legal_persons, 'name', 'id');
        $receive_card = redisCached::getReceiveCard();
        $receive_card = array_column($receive_card, 'card_number', 'id');
        $users = redisCached::getUserInfo();
        $users_map = [];
        foreach ($users as $u) {
            $users_map[] = [
                'id' => $u['user_id'],
                'user_name' => $u['user_name'],
            ];
        }
        return [
            'legal_persons' => $legal_persons,
            'receive_card'  => $receive_card,
            'users' => $users_map
        ];
    }

    // 新增
    public function add($data, $type = '新增')
    {
        $this->dataValidCheck($data, self::$paras_list);

        $data = $this->jsonEncodeFormat($data);

        $data['user_id'] = userModel::$qwuser_id ?? 0;

        $id = parent::add($data, $type);

        // 重新生成缓存
        redisCached::del(redisCached::YWX_COMPANY);

        return $id;
    }

    // 接收申请
    public function accept($id)
    {
        $detail = $this->getById($id);
        if (!$detail) {
            throw new Exception('公司申请不存在');
        }

        if ($detail['register_status'] != self::REGISTER_STATUS_WAIT) {
            throw new Exception('只能接收待接收状态的申请');
        }

        $old_data = $detail;
        $new_data = [
            'register_status'    => self::REGISTER_STATUS_REGISTERING,
            'status_change_time' => date('Y-m-d'),
        ];

        parent::edit($new_data, $id, $old_data);
    }

    // 注册登记
    public function register($param, $id)
    {
        $detail = $this->getById($id);
        if (!$detail) {
            throw new Exception('公司不存在');
        }

        if ($detail['register_status'] != self::REGISTER_STATUS_REGISTERING) {
            throw new Exception('只能登记注册中状态的公司');
        }

        // 基本信息验证
        $required = [
            'company_name'          => '公司名称',
            'company_status'        => '公司状态',
            'register_country'      => '注册国家',
            'register_city'         => '注册城市',
            'register_type'         => '注册类型',      // 1-法人自注册, 2-公司代注册
            'tax_number'            => '纳税人识别号',
            'register_date'         => '注册时间',
            'register_address'      => '注册地址',
            'register_coordinator'  => '注册对接人',
            'house_number'          => '房屋编号',
            'license_keeper'        => '营业执照保管方',
            'e_license'             => '电子营业执照',
            'license_download_date' => '执照下载日期',
            'receive_card_id'       => '银行公户账号',
            'accountants'           => '记账报税人',     // JSON数组;
            'remark'                => '备注',
        ];
        $this->dataValidCheck($param, $required);

        $param['register_status'] = self::REGISTER_STATUS_SUCCESS;
        $param['status_change_time'] = date('Y-m-d');
        $param['finish_time'] = date('Y-m-d H:i:s');

        parent::edit($param, $id, $detail, '注册登记');
    }

    // 编辑
    public function edit($data, $id, $old_data = [], $type = '编辑', $remark = '', $result = '', $other_attach = [])
    {
        $detail = $this->getById($id);
        if (!$detail) {
            throw new Exception('公司不存在');
        }

        // 基本信息验证
        $required = [
            'company_name'          => '公司名称',
            'register_country'      => '注册国家',
            'register_city'         => '注册城市',
            'register_type'         => '注册类型',      // 1-法人自注册, 2-公司代注册
            'tax_number'            => '纳税人识别号',
            'register_date'         => '注册时间',
            'register_address'      => '注册地址',
//            'register_coordinator'  => '注册对接人',
            'house_number'          => '房屋编号',
            'license_keeper'        => '营业执照保管方',
            'e_license'             => '电子营业执照',
            'license_download_date' => '执照下载日期',
            'receive_card_id'       => '银行公户账号',
            'accountants'           => '记账报税人',     // JSON数组;
//            'remark'                => '备注',
        ];
        $this->dataValidCheck($data, $required);

        parent::edit($data, $id, $detail, $type, $remark, $result, $other_attach);

        // 重新生成缓存
        redisCached::del(redisCached::YWX_COMPANY);
    }

    // 申请注销
    public function applyCancel($id, $remark)
    {
        $detail = $this->getById($id);
        if (!$detail) {
            throw new Exception('公司不存在');
        }

        if ($detail['register_status'] != self::REGISTER_STATUS_SUCCESS) {
            throw new Exception('只有已注册的公司可以申请注销');
        }

        if (empty($remark)) {
            throw new Exception('注销原因不能为空');
        }

        $old_data = $detail;
        $new_data = [
            'register_status' => self::REGISTER_STATUS_WAIT_CANCEL,
        ];

        // 通知
        $users = configModel::noticeUser('company', 'close');
        $user_name = userModel::$wname;
        if (!empty($users)) {
            messagesFrom::senMeg($users, 1 , "【{$user_name}】提交了【{$detail['company_name']}】的注销，请您及时处理注销该公司", $id, '', '公司注销');
        }

        parent::edit($new_data, $id, $old_data, '申请注销');
    }

    // 处理注销申请
    public function handleCancel($id, $is_cancel, $remark)
    {
        $detail = $this->getById($id);
        if (!$detail) {
            throw new Exception('公司不存在');
        }

        if ($detail['register_status'] != self::REGISTER_STATUS_WAIT_CANCEL) {
            throw new Exception('只能处理注销申请中的公司');
        }

        if (empty($remark)) {
            throw new Exception('处理结果不能为空');
        }

        $old_data = $detail;
        $new_data = [
            'register_status' => $is_cancel ? self::REGISTER_STATUS_CANCELED : self::REGISTER_STATUS_SUCCESS,
        ];

        parent::edit($new_data, $id, $old_data, '处理注销申请', $remark, $is_cancel);
    }

    // 变更法人
    public function changeLegalPerson($id, $legal_person_id)
    {
        $detail = $this->getById($id);
        if (!$detail) {
            throw new Exception('公司不存在');
        }

        // 检查新法人是否存在
        $legal_person = $this->db->table('legal_person')
            ->where('id = :id', ['id' => $legal_person_id])
            ->one();

        if (!$legal_person) {
            throw new Exception('法人不存在');
        }

        if ($detail['legal_person_id'] == $legal_person_id) {
            throw new Exception('法人没有变更');
        }

        // 更新原法人状态
        $db_shop = dbShopMysql::getInstance();
        $db_shop->table('legal_person')->where('id = :id', ['id' => $detail['legal_person_id']])->update([
            'is_register_company' => 0,
        ]);
        // 更新新法人状态
        $db_shop->table('legal_person')->where('id = :id', ['id' => $legal_person_id])->update([
            'is_register_company' => 1,
        ]);

        $old_data = $detail;
        $new_data = [
            'legal_person_id' => $legal_person_id,
        ];

        $shop = $db_shop->table('shop')->where('company_id = :company_id', ['company_id' => $id])->one();

        // 通知
        $users = configModel::noticeUser('company', 'change_legal_person', [
            'shop_id' => $shop['id'],
            'shop_coordinator' => $shop['coordinator']
        ]);
        $user_name = userModel::$wname;
        if (!empty($users)) {
            messagesFrom::senMeg($users, 1 , "【{$user_name}】变更了【{$detail['company_name']}】的法人为【{$legal_person['name']}】", $id, '', '公司法人变更');
        }

        parent::edit($new_data, $id, $old_data, '变更法人');
    }

    // 状态维护
    public function updateStatus($id, $company_status, $status_change_time, $remark)
    {
        $detail = $this->getById($id);

        $old_data = $detail;
        $new_data = [
            'company_status'     => $company_status,
            'status_change_time' => $status_change_time,
        ];

        $db_shop = dbShopMysql::getInstance();
        $shop = $db_shop->table('shop')->where('company_id = :company_id', ['company_id' => $id])->one();

        // 通知
        $users = configModel::noticeUser('company', 'change_status', [
            'shop_id' => $shop['id'],
            'shop_coordinator' => $shop['coordinator']
        ]);
        if (!empty($users)) {
            messagesFrom::senMeg($users, 1 , "【{$detail['company_name']}】的状态变更为【{$company_status}】", $id, '', '公司状态变更');
        }

        parent::edit($new_data, $id, $old_data, '状态维护', $remark);
    }

    // 批量编辑
    public function editBatch($data)
    {
        $db = $this->db->table($this->table);
        $ids = array_column($data, 'id');
        $db->whereIn('id', $ids);
        $list = $db->list();
        $list = array_column($list, null, 'id');

        $error_ids = [];
        foreach ($data as $item) {
            try {
                $item_id = $item['id'];
                unset($item['id']);
                // 唯一性校验
                $this->dataValidCheck($item, self::$paras_list);
                self::edit($item, $item_id, $list[$item_id], '批量编辑');
            } catch (Exception $e) {
                $error_ids[] = [
                    'id' => $item_id,
                    'error' => $e->getMessage(),
                ];
            }
        }
        return $error_ids;
    }
}
