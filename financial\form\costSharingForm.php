<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/28 10:47
 */

namespace financial\form;

use core\jobs\costSharingAbolishJob;
use core\jobs\costSharingJobs;
use core\lib\config;
use core\lib\db\dbFMysql;
use core\lib\ExceptionError;
use core\lib\rediskeys;
use financial\models\checkoutModel;
use financial\models\costSharingModel;
use financial\models\mskuReportModel;
use financial\models\userModel;
use Rap2hpoutre\FastExcel\FastExcel;

class costSharingForm
{
    //列表数据获取
    public static function getList($param){
        $db = dbFMysql::getInstance();
        $db->table('cost_sharing','a')->where('where 1=1')
            ->leftJoinOut('db','qwuser','b','b.id = a.user_id');
        if(!empty($param['date_array']) && $param['date_array'] != '[]'){
            $date_array = json_decode($param['date_array']);
            $db->andWhere("(a.m_date>=:begin_time and a.m_date<=:end_time)",['begin_time'=>$date_array[0],'end_time'=>$date_array[1]]);
        }
        if (!empty($param['user_id'])) {
            $db->andWhere('a.user_id=:user_id',['user_id'=>$param['user_id']]);
        }
        if ($param['status'] > 0) {
            $db->andWhere('a.status=:status',['status'=>$param['status']]);
        }
        $data = $db->order('a.id desc')
            ->field('a.id,a.sharing_no,a.user_id,a.m_date,a.created_time,a.dimension,a.status,b.wname as user_wname')
            ->pages($param['page'],$param['page_size']);
        if (count($data['list'])) {
            $data['list'] = self::getListData($data['list'],$param['data_status']);
        }
        $data['dimension_list'] = costSharingModel::$dimension_list;
        $data['export_key_list'] = costSharingModel::$export_key_list;
        return $data;
    }
    private static function getListData(array $list, int $data_status,array $import_data_ids = []) {
        $db = dbFMysql::getInstance();
        $import_ids = array_column($list,'id');
        $db->table('cost_sharing_data');
        if ($data_status > -1) {
            $db->where('where status=:status',['status'=>$data_status]);
        }
        if (count($import_data_ids)) {
            $db->whereIn('id',$import_data_ids)->list();
        }
        $list_data = $db->whereIn('import_id',$import_ids)->list();
        foreach ($list as &$v) {
            //维度
            $dimension = json_decode($v['dimension']);
            $dimension_array = array_intersect_key(costSharingModel::$dimension_list,array_flip($dimension));
            $v['dimension'] = implode(',',array_values($dimension_array));
            //导入详情
            $data_item = [];
            $total_amount = 0;
            foreach ($list_data as $k=>$v2) {
                if ($v['id'] == $v2['import_id']) {
                    $v2['data'] = json_decode($v2['data'],'true');
                    $total_amount += $v2['data']['amount'];
                    $data_item[] = $v2;
                    unset($list_data[$k]);
                }
            }
            $v['total_amount'] = (string)$total_amount;
            $v['import_data'] = $data_item;
        }
        return $list;
    }
    //下载表格
    public static function downLoadTpl($dimension){
        $dimension_list = costSharingModel::$dimension_list;
        $row = array_intersect_key($dimension_list, array_flip($dimension));
        if (!$row) {
            returnError('未匹配到系统维度');
        }
        $row_head = array_values($row);
        $row_head = array_merge($row_head,['费用名称','币种','金额','分摊规则','备注']);
        $row_head = array_flip($row_head);
        foreach ($row_head as $k=>$v) {
            $row_head[$k] = '';
        }
        //保存
        $save_path = "/public_financial/temp/cost_share_tpl";
        $url = SELF_FK.$save_path;
        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path."/".date('YmdHis').uniqid().'.xlsx';
        $url = SELF_FK.$path;
        if (!file_exists($url)) {
            touch($url);
        }
        (new FastExcel([$row_head]))->export($url);
        return $path;
    }
    //表格导导入记录
    public static function saveShareExcel(array $data,string $excel_src,string $excel_name,$m_date,$dimension) {
        $db = dbFMysql::getInstance();
        $db->beginTransaction();
        try {
            //表格保存
            $import_id = $db->table('cost_sharing')
                ->insert([
                    'user_id'=>userModel::$qwuser_id,
                    'm_date'=>$m_date,
                    'dimension'=>json_encode($dimension),
                    'excel_name'=>$excel_name,
                    'excel_path'=>$excel_src,
                    'total'=>count($data),
                    'created_time'=>date('Y-m-d H:i:s')
                ]);
            $sharing_no = getFinancialShareNo($import_id);
            $db->table('cost_sharing')
                ->where('where id=:id',['id'=>$import_id])
                ->update(['sharing_no'=>$sharing_no]);
            $import_data_ids = [];
            foreach ($data as $v) {
                $import_data_ids[] = $db->table('cost_sharing_data')
                    ->insert([
                        'import_id'=>$import_id,
                        'data'=>json_encode($v,JSON_UNESCAPED_UNICODE)
                    ]);
            }
            $db->commit();
            return ['import_data_id'=>$import_data_ids[0],'import_id'=>$import_id];
        } catch (ExceptionError $error) {
            $db->rollBack();
            throw new ExceptionError($error->getMessage());
        }
    }
    //获取单个表格导入的数据
    public static function getCostDataByImportId($import_id) {
        $db = dbFMysql::getInstance();
        $cost_sharing =  $db->table('cost_sharing')
            ->where('where id=:id',['id'=>$import_id])->one();
        $list = $db->table('cost_sharing_data')
            ->where('where import_id=:import_id',['import_id'=>$import_id])
            ->list();
        foreach ($list as &$v) {
            $v['data'] = json_decode($v['data']);
            $v['dimension'] = json_decode($cost_sharing['dimension']);
        }
        return $list;
    }

    /**
     * @param $data_ids
     * @param $type 1废除，2重新计算
     * @return mixed
     * @throws ExceptionError //操作数据时 获取可重新不同状态的数据
     */
    public static function getAllowList($data_ids,$type) {
        $db = dbFMysql::getInstance();
        $db->table('cost_sharing_data','a')
            ->leftJoin('cost_sharing','b','b.id = a.import_id');
        if ($type == 1) {
            $db->andWhere('a.status == 2');
        } else {
            $db->andWhere('(a.status = 2 or a.status = 3)');
        }
        $list = $db->where('where a.status <> 5')
            ->whereIn('a.id',$data_ids)
            ->field('a.id,b.m_date,a.import_id')
            ->list();
        if (count($list)) {
            $m_date_array = array_unique(array_column($list,'m_date'));
            //验证此月数据是否已锁定（结账）
            checkoutModel::verifyLock($m_date_array);
        }
        return $list;
    }
    //分摊任务添加
    public static function setTask(int $import_data_id,array $import_ids,$m_date = '') {
        $redis = (new \core\lib\predisV())::$client;
        //其他操作验证
        if ($m_date) {
            checkoutForm::verifyCostSharing($redis,$m_date);
        }
        $key = rediskeys::$oa_cost_sharing_import;
        $redis->set($key,json_encode(['import_data_id'=>$import_data_id,"import_ids"=>$import_ids]));
        $redis->expire($key,60*60);
        //需要验证后台是否有分摊在进行，有则不让分摊
        $task = new costSharingJobs($import_data_id);
//        $task->task();
        $queue_key = config::get('delay_queue_key', 'app');
        $redis->zAdd($queue_key, [], 0, serialize($task));
    }

    /**
     * @param $import_data_ids
     * @param $type 1作废，2作废完后计算
     * @return void
     * @throws \RedisException 作废任务添加
     */
    public static function setAbolishTask(array $import_data_ids,array $import_ids,int $type) {
        //先将这个数据设置成排队中
        $db = dbFMysql::getInstance();
        $db->table('cost_sharing_data')
            ->whereIn('id',$import_data_ids)
            ->update(['status'=>0]);
        //推向队列
        $redis = (new \core\lib\predisV())::$client;
        $key = rediskeys::$oa_cost_sharing_abolish;
        $redis->set($key,json_encode(['import_data_ids'=>$import_data_ids,'import_ids'=>$import_ids]));
        $redis->expire($key,60*60);
        //需要验证后台是否有分摊在进行，有则不让分摊
        $task = new costSharingAbolishJob(userModel::$qwuser_id, $type);
//        $task->task();
        $queue_key = config::get('delay_queue_key', 'app');
        $redis->zAdd($queue_key, [], 0, serialize($task));
    }

    //验证是否还有队列数据在处理（分摊和废除）
    public static function verifyQueue() {
        $redis = (new \core\lib\predisV())::$client;
        $key = rediskeys::$oa_cost_sharing_import;
        if ($redis->get($key)) {
            returnError('目前已有均摊在处理，请稍后再试。');
        }
        //验证是否有作废再跑
        $key = rediskeys::$oa_cost_sharing_abolish;
        if ($redis->get($key)) {
            returnError('目前已有作废数据在处理，请稍后再试。');
        }
    }
    //修改费用单状态
    public static function updateSharingStatus($ids) {
        $db = dbFMysql::getInstance();
        $sharing_list = $db->table('cost_sharing')
            ->whereIn('id',$ids)
            ->list();
        $cost_sharing_list = $db->table('cost_sharing_data')
            ->field('id,import_id,status')
            ->whereIn('import_id',$ids)
            ->list();
        foreach ($sharing_list as $v1) {
            $import_id = $v1['id'];
            $data_list = array_filter($cost_sharing_list,function ($row) use($import_id){
                return $row['import_id'] = $import_id;
            });
            $status_array = array_column($data_list,'status');
            $status_array = array_unique($status_array);
            $sharing_status = 0;
            //状态不止一个，说明有正常，也有异常
            if (count($status_array) > 1) {
                if (in_array(2,$status_array)){
                    $sharing_status = 4;
                } elseif (in_array(3,$status_array)) {
                    $sharing_status = 3;
                }
            } else {
                if ($status_array[0] == 2) {
                    $sharing_status = 2;
                } elseif ($status_array[0] == 3){
                    $sharing_status = 3;
                } else {
                    $sharing_status = 5;
                }
            }
            $db->table('cost_sharing')
                ->where('where id=:id',['id'=>$v1['id']])
                ->update(['status'=>$sharing_status]);
            //0排队中，1分摊中，2成功，3失败，4做废中，5已废除
            $msg_text = '未知状态'.$sharing_status;
            switch ($sharing_status) {
                case 0:
                    $msg_text = '排队中';break;
                case 1:
                    $msg_text = '成功';break;
                case 2:
                    $msg_text = '已分摊';break;
                case 3:
                    $msg_text = '失败';break;
                case 4:
                    $msg_text = '做废中';break;
                case 5:
                    $msg_text = '已废除';break;
            }
            $msg_text = '费用单'.$v1['sharing_no'].'分摊：'.$msg_text;
            //消息推送
            messagesFrom::senMsgByNoticeType(3,$msg_text,$v1['id']);
        }
    }

    //导出
    public static function  export($param) {
        $export_list = json_decode($param['export_list']);
        if (!count($export_list)) {
            returnError('请选择要导出的字段');
        }
        $db = dbFMysql::getInstance();
        $db->table('cost_sharing','a')->where('where 1=1')
            ->leftJoinOut('db','qwuser','b','b.id = a.user_id');
        $import_data_ids = [];
        if ($param['type'] == 0) {
            $ids = json_decode($param['import_ids']);
            $import_data_ids = json_decode($param['import_data_ids']);
            if (!count($ids) || !count($import_data_ids)) {
                returnError('请选择要导出的数据');
            }
            $db->whereIn('a.id',$ids);
        } else {
            if(!empty($param['date_array']) && $param['date_array'] != '[]'){
                $date_array = json_decode($param['date_array']);
                $db->andWhere("(a.m_date>=:begin_time and a.m_date<=:end_time)",['begin_time'=>$date_array[0],'end_time'=>$date_array[1]]);
            }
            if (!empty($param['user_id'])) {
                $db->andWhere('a.user_id=:user_id',['user_id'=>(int)$param['user_id']]);
            }
            if (!empty($param['status']) > -1) {
                $db->andWhere('a.status=:status',['status'=>$param['status']]);
            }
        }
        $list = $db->order('a.id desc')
            ->field('a.id,a.sharing_no,a.user_id,a.m_date,a.dimension,a.status,b.wname as user_wname')
            ->list();
        if (count($list)) {
            $list = self::getListData($list,$param['data_status'],$import_data_ids);
        } else {
            returnError('未查询到任何数据');
        }
        $export_key_list = costSharingModel::$export_key_list;
        $export_key_ = array_intersect_key($export_key_list,array_flip($export_list));
        $new_data = [];
        foreach ($list as $v) {
            $itme_data_list = [];
            foreach ($v['import_data'] as $v1) {
                $export_item = [];
                foreach ($export_key_ as $key=>$mm) {
                    if (in_array($key,['sharing_no','user_wname','m_date','dimension'])) {
                        $val = $v[$key];
                    } else {
                        if ($key == 'cost_name') {
                            $val = $v1['data']['cost_name'];
                        }
                        if ($key == 'amount') {
                            $val = $v1['data']['amount'];
                        }
                        if ($key == 'status') {
                            switch ($v1['status']) {
                                case 0:
                                    $val = '排队中';
                                    break;
                                case 1:
                                    $val = '分摊中';
                                    break;
                                case 2:
                                    $val = '成功';
                                    break;
                                case 3:
                                    $val = '失败';
                                    break;
                                case 4:
                                    $val = '做废中';
                                    break;
                                case 5:
                                    $val = '已废除';
                                    break;
                            }
                        }
                        if ($key == 'amount') {
                            $val = $v1['data']['amount'];
                        }
                        if ($key == 'share_rule') {
                            $val = $v1['data']['share_rule'];
                        }
                        if ($key == 'remark') {
                            $val = $v1['data']['remark'];
                        }
                        if ($key == 'error_reason') {
                            $val = $v1['error_reason'];
                        }
                    }
                    if (isset($export_key_[$key])) {
                        $export_item[$export_key_[$key]] = $val;
                    }
                }

                $itme_data_list[] = $export_item;
            }
            $new_data[$v['sharing_no']] = $itme_data_list;
        }
        $url_list = [];
        //生成excel
        foreach ($new_data as $k=>$ex) {
            //保存
            $save_path = "/public_financial/temp/sharing";
            $url = SELF_FK.$save_path;
            if (!file_exists($url)) {
                mkdir($url, 0777, true);
            }
            $path = $save_path."/".$k.'.xlsx';
            $url = SELF_FK.$path;
            if (!file_exists($url)) {
                touch($url);
            }
            (new FastExcel($ex))->export($url);
            $url_list[] = $path;
        }
        //生成压缩包
        $zip_save_path = "/public_financial/downLoad/sharing/".date('Ymd');
        $zip_url = SELF_FK.$zip_save_path;
        if (!file_exists($zip_url)) {
            mkdir($zip_url, 0777, true);
        }
        $zip_path = $zip_save_path."/".date('Ymd').uniqid().'.zip';
        setZipByUrl($url_list,$zip_path);
        return $zip_path;
    }

















}