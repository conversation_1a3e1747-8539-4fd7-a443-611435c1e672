<?php
/**
 * @author: zhangguoming
 * @Time: 2024/1/11 9:22
 */

namespace  plugins\goods\controller;

use core\lib\db\dbMysql;
use core\lib\log;
use plugins\goods\models\userModel;

class goodsColorController
{
    public function getList(){
        $paras_list = array('color_name','page','page_size');
        $param = arrangeParam($_POST, $paras_list);

        $where_str = 'where is_delete=0';
        $where_array = [];
        if (!empty($param['color_name'])) {
            $where_str .= ' and (color_name like :color_name or color_name_en like :color_name)';
            $where_array['color_name'] = '%'.$param['color_name'].'%';
        }

        $db = dbMysql::getInstance();
        $db->table('goods_color');
        $db->where($where_str, $where_array);
        $data = $db->pages($param['page'], $param['page_size']);
        returnSuccess($data);
    }

    public function editColor() {
        $paras_list = array('id','color_name','color_name_en');
        $request_list = ['color_name'=>'功能名称','color_name_en'=>'功能英文名'];
        $param = arrangeParam($_POST, $paras_list, $request_list);
        $id = (int)($param['id']);
        $up_data = [
            'color_name'=>$param['color_name'],
            'color_name_en'=>$param['color_name_en'],
        ];
        if ($id) {
            $db = dbMysql::getInstance();
            $db->table('goods_color');
            $db->where('where id = '.$id);
            $up_data['updated_at'] = date('Y-m-d H:i:s');
            if ($db->update($up_data)) {
                log::goodsColorLog()->info('用户【'.userModel::$wid.'】修改：'.json_encode($param,JSON_UNESCAPED_UNICODE));
                SetReturn(0,'修改成功');
            } else {
                SetReturn(-1,'修改失败');
            }
        } else {
            $db = dbMysql::getInstance();
            $db->table('goods_color');
            $up_data['created_at'] = date('Y-m-d H:i:s');
            if ($db->insert($up_data)) {
                log::goodsColorLog()->info('用户【'.userModel::$wid.'】新增：'.json_encode($param,JSON_UNESCAPED_UNICODE));
                SetReturn(0,'添加成功');
            } else {
                SetReturn(-1,'添加失败');
            }
        }
    }
}