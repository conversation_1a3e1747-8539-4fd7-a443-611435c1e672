#!/bin/bash
# 领星msku报告数据同步脚本
# 获取第一个参数
token='01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0'
qwUrl='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=03a8032d-65c0-4390-a588-9a6b2cf11336'
fsUrl='https://open.feishu.cn/open-apis/bot/v2/hook/2b63c602-bb10-465f-bc9a-af4dc1f02233'
# while true; do
#     # 发送POST请求，这里假设URL为http://example.com/api，根据实际情况替换
# #    response=$(curl -s -X POST -d "token=$token" 'http://171.223.214.187:8901/task/lingXingApi/synInbound')
#     response=$(curl -s -X POST -d "token=$token" 'http://oa.ywx.com//task/lingXingApi/synInbound')
#      echo "$response"
#     # 从响应中提取code字段的值
#     code=$(echo "$response" | grep -oP '"code":\K-?\d+')
#     # 从响应中提取msg字段的值
#     msg=$(echo "$response" | grep -oP '"message"\s*:\s*"\K[^"]+')
#     # 检查code字段的值
#     current_time=$(date +"%Y-%m-%d %H:%M:%S")
#     if [ "$code" -ne 2 ]; then
#         echo "code=$code，继续请求$current_time"
#     else
#        break ;
#     fi
# done
echo "海外仓备货单同步完成，$current_time"

# 执行SKU拆分处理
echo "开始执行海外仓备货单SKU拆分..."
split_start_time=$(date +"%Y-%m-%d %H:%M:%S")


while true; do
    # 发送POST请求，这里假设URL为http://example.com/api，根据实际情况替换
    split_response=$(curl -s -X POST -d "token=$token" 'http://oa.ywx.com/task/logistics/processOverseasInboundSplit')
    echo "$split_response"

    # 从拆分响应中提取code字段的值
    split_code=$(echo "$split_response" | grep -oP '"code":\K-?\d+')
    # 从拆分响应中提取msg字段的值
    split_msg=$(echo "$split_response" | grep -oP '"message"\s*:\s*"\K[^"]+')

    if [ "$split_code" -ne 2 ]; then
        echo "code=$split_code，继续请求$current_time"
    else
       break ;
    fi
done
