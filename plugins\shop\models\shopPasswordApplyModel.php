<?php
namespace plugins\shop\models;

use Exception;

class shopPasswordApplyModel extends baseModel {
    //表名
    public string $table = 'shop_password_apply';

    // 状态常量
    const STATUS_PENDING = 0;    //待审批
    const STATUS_APPROVED = 1;   //已通过
    const STATUS_REJECTED = 2;   //已拒绝

    // 申请类型
    const TYPE_SHOP = '店铺密码';
    const TYPE_EMAIL = '邮箱密码';

    //可填充字段
    public static array $paras_list = [
        'shop_id',        //店铺ID
        'apply_type',     //申请类型：店铺密码、邮箱密码
        'apply_reason',   //申请原因
        'user_id',        //申请人
        'status',         //状态：0待审批、1已通过、2已拒绝
        'view_count',     //允许查看次数
        'view_minutes',   //允许查看时长(分钟)
        'audit_user_id',  //审批人
        'audit_remark',   //审批意见
    ];

    public static array $json_keys = [
        'apply_type'
    ];

    public function formatItem($item, $maps = [])
    {
        return parent::formatItem($item, $maps);
    }

    /**
     * 获取申请列表
     */
    public function getList($param, $order = 'id desc')
    {
        $db = $this->db->table($this->table)->where('1=1');

        // 添加查询条件
        if (!empty($param['id']) && is_array($param['id'])) {
            $db->whereIn('id', $param['id']);
        }
        if (!empty($param['shop_id'])) {
            $db->andWhere('shop_id = :shop_id', ['shop_id' => $param['shop_id']]);
        }
        if (!empty($param['user_id'])) {
            $db->andWhere('user_id = :user_id', ['user_id' => $param['user_id']]);
        }
        if (isset($param['status'])) {
            $db->andWhere('status = :status', ['status' => $param['status']]);
        }

        $db->order($order);
        return $db->list();
    }

    /**
     * 创建密码查看申请
     */
    public function createApply($data)
    {
        // 数据验证
        if (empty($data['shop_id'])) {
            throw new Exception('店铺ID不能为空');
        }
        if (empty($data['apply_type'])) {
            throw new Exception('申请类型不能为空');
        }
        if (empty($data['apply_reason'])) {
            throw new Exception('申请原因不能为空');
        }

        // 检查是否有未处理的申请
        $exists = $this->db->table($this->table)
            ->where('where shop_id = :shop_id and user_id = :user_id and status = :status', [
                'shop_id' => $data['shop_id'],
                'user_id' => userModel::$qwuser_id ?? 0,
                'status' => self::STATUS_APPROVED,
            ])->list();
        foreach ($exists as $exist) {
            if ($exist['view_count'] == 0) continue;
            $exist['apply_type'] = json_decode($exist['apply_type'], true);
            if (array_intersect($data['apply_type'], $exist['apply_type']) ||
            array_intersect($exist['apply_type'], $data['apply_type'])) {
                throw new Exception('已存在申请记录');
            }
        }

        $data['apply_type'] = json_encode($data['apply_type'], JSON_UNESCAPED_UNICODE);
        $data['user_id'] = userModel::$qwuser_id ?? 0;
        $data['status'] = self::STATUS_PENDING;

        return parent::add($data, '申请');
    }

    /**
     * 批量审批申请
     */
    public function updateBatch($data)
    {
        $this->db->table($this->table)->updateBatch($data);

        return true;
    }

    /**
     * 检查密码查看权限
     */
    public function checkViewPermission($shop_id, $apply_type)
    {
        $apply = $this->db->table($this->table)
            ->where('shop_id', $shop_id)
            ->where('user_id', $_SESSION['user_id'])
            ->where('apply_type', $apply_type)
            ->where('status', self::STATUS_APPROVED)
            ->where('view_count > 0')
            ->order('id desc')
            ->one();

        if (!$apply) {
            throw new Exception('无权查看密码，请先申请');
        }

        // 检查是否在允许时间内
        $minutes = (time() - strtotime($apply['created_at'])) / 60;
        if ($minutes > $apply['view_minutes']) {
            throw new Exception('查看权限已过期，请重新申请');
        }

        // 更新查看次数
        $this->db->table($this->table)
            ->where('id', $apply['id'])
            ->update([
                'view_count' => $apply['view_count'] - 1,
                'updated_at' => date('Y-m-d H:i:s')
            ]);

        return true;
    }

    /**
     * 获取申请详情
     */
    public function getDetail($id)
    {
        return $this->db->table($this->table)
            ->where('id', $id)
            ->one();
    }
}
