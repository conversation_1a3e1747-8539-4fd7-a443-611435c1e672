<?php

namespace plugins\checkin\controller;

use admin\models\qwModel;
use plugins\checkin\models\userModel;
use core\lib\config;
use core\lib\db\dbCMysql;
use core\lib\db\dbMysql;

class corpCheckinOptionController
{
    // 获取规则列表
    public function getList()
    {
        $paras_list = array('group_name', 'users', 'page', 'page_size');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        if (isset($param['users']) && $param['users']) {
            $param['users'] = json_decode($param['users'], true);
        }

        $cdb = dbCMysql::getInstance();
        $cdb->table('corp_checkin_option');
        $cdb->field('id, corp_id, groupid, groupname, grouptype, range_info, white_users, user_id, rule, update_time');
        $cdb->order('id asc');
        $list = $cdb->list();

        $db = dbMysql::getInstance();
        $users = $db->table('qwuser')->field('id, wname, wdepartment_ids,wid')->list();
        $userMap = array_column($users, null, 'id');
        $ret = [];

        $corp = $cdb->table('corporation')->list();
        $corp = array_column($corp, null, 'id');

        foreach ($list as &$item) {
            $item['user_name'] = $userMap[$item['user_id']]['wname'] ?? '';
            $item['rule'] = json_decode($item['rule'], true);
            $item['white_users'] = json_decode($item['white_users'], true);
            $item['range_info'] = json_decode($item['range_info'], true);
            $item['checkin_users'] = [];

            $item['corp_name'] = $corp[$item['corp_id']]['name'];
            if ($param['group_name'] && strpos($item['groupname'], $param['group_name']) === false) {
                continue;
            }
            $departments = $item['range_info']['party_id'];
            $range_users = $item['range_info']['userid'];
            foreach ($users as $user) {
                $user_departmnets = json_decode($user['wdepartment_ids'], true);
                if (in_array($user['wid'], $range_users)) {
                    $item['checkin_users'][$user['id']] = $user;
                }
                if (array_intersect($departments, $user_departmnets)) {
                    $item['checkin_users'][$user['id']] = $user;
                }
            }
            if ($param['users']) {
                if (empty(array_intersect($param['users'], array_keys($item['checkin_users'])))) continue;
                if (empty(array_diff($param['users'], $item['white_users']))) continue;
            }

            $item['checkin_users'] = array_values($item['checkin_users']);
            $ret[] = $item;
        }

        if (!empty($ret)) $ret = array_slice($ret, ($page-1)*$limit, $limit);

        $last_sync = $cdb->table('sync_time')->where('where type = 1 and status = 1')->order('finish_time desc')->one();
        $last_update_time = $last_sync['finish_time'] ?? '';

        returnSuccess(['list' => $ret, 'total' => count($ret), 'page' => $page, 'page_size' => $limit, 'last_update_time' => $last_update_time]);
    }

    // 编辑考勤规则
    public function edit()
    {
        $paras_list = array('id', 'rule');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        empty($param['id']) && returnError('规则ID必填');
        empty($param['rule']) && returnError('规则必填');

        // 校验关联规则
        $rule = json_decode($param['rule'], true);
        if (empty($rule['off_type'])) returnError('考勤规则不能为空');
        if (empty($rule['rule_1'])) returnError('请假规则不能为空');
        if (empty($rule['rule_2'])) returnError('旷工规则不能为空');
        if (empty($rule['rule_3'])) returnError('缺卡规则不能为空');
        if (empty($rule['rule_4'])) returnError('迟到早退规则不能为空');
        if (empty($rule['rule_5'])) returnError('加班规则不能为空');
        if (empty($rule['rule_6'])) returnError('全勤规则不能为空');

        $rule_id = [$rule['rule_1'], $rule['rule_2'], $rule['rule_3'], $rule['rule_4'], $rule['rule_5'], $rule['rule_6']];
        $cdb = dbCMysql::getInstance();
        $ruleMap = $cdb->table('rule')->field('id,rule_name, rule_type')->where('where is_delete = 0 and status = 1')->whereIn('id', $rule_id)->list();
        $ruleMap = array_column($ruleMap, null,'id');
        if (!array_key_exists($rule['rule_1'], $ruleMap) || $ruleMap[$rule['rule_1']]['rule_type'] != 1) returnError('请假规则设置错误');
        if (!array_key_exists($rule['rule_2'], $ruleMap) || $ruleMap[$rule['rule_2']]['rule_type'] != 2) returnError('旷工规则设置错误');
        if (!array_key_exists($rule['rule_3'], $ruleMap) || $ruleMap[$rule['rule_3']]['rule_type'] != 3) returnError('缺卡规则设置错误');
        if (!array_key_exists($rule['rule_4'], $ruleMap) || $ruleMap[$rule['rule_4']]['rule_type'] != 4) returnError('迟到早退规则设置错误');
        if (!array_key_exists($rule['rule_5'], $ruleMap) || $ruleMap[$rule['rule_5']]['rule_type'] != 5) returnError('加班规则设置错误');
        if (!array_key_exists($rule['rule_6'], $ruleMap) || $ruleMap[$rule['rule_6']]['rule_type'] != 6) returnError('全勤规则设置错误');

        $cdb = dbCMysql::getInstance();
        $cdb->table('corp_checkin_option');
        $cdb->where('id = :id', ['id' => $param['id']]);
        $one = $cdb->one();
        if (!$one) returnError('规则不存在');

        $cdb->update([
            'rule' => $param['rule'],
            'user_id' => userModel::$qwuser_id,
        ]);

        returnSuccess([], '编辑成功');
    }

    public static function syncRules()
    {
        $month = date('Y-m');
        $qwModel = new qwModel();
        $qwModel->getCorpCheckinOption($month);
        returnSuccess([], '同步成功');
    }

    public static function getAllUserInRule() {
        $db = dbMysql::getInstance();
        $cdb = dbCMysql::getInstance();
        $users = $db->table('qwuser')->field('id, wname, wdepartment_ids,wid')->list();
        $cdb->table('corp_checkin_option');
        $cdb->field('id, corp_id, groupid, groupname, grouptype, range_info, white_users, user_id, rule, update_time');
        $cdb->order('id asc');
        $list = $cdb->list();
        $checkin_users = [];

        foreach ($list as $item) {
            $item['white_users'] = json_decode($item['white_users'], true);
            $item['range_info'] = json_decode($item['range_info'], true);

            $departments = $item['range_info']['party_id'];
            $range_users = $item['range_info']['userid'];
            foreach ($users as $user) {
                $user_departmnets = json_decode($user['wdepartment_ids'], true);
                if (in_array($user['wid'], $range_users)) {
                    $checkin_users[$user['id']] = $user;
                }
                if (array_intersect($departments, $user_departmnets)) {
                    $checkin_users[$user['id']] = $user;
                }
            }

        }
        return array_values($checkin_users);
    }


}