<?php

namespace plugins\shop\models;

use core\lib\redisCached;
use Exception;

class trademarkApplyModel extends baseModel
{
    public string $table = 'trademark_apply';

    // 商标类型字段定义
    const TYPE_SELF = '自注册';   // 自注册
    const TYPE_PURCHASE = '外购'; // 外购

    // 商标状态字段定义 
    const STATUS_RECEIVE_WAITING = 0;  // 待接收
    // 自注册
    const STATUS_REGISTER_WAITING = 1;  // 待注册
    const STATUS_REGISTER_IN_PROGRESS = 2;  // 注册中
    const STATUS_REGISTER_COMPLETED = 3;  // 注册完成
    // 外购
    const STATUS_TRANSFER_WAITING = 4;  // 待转让
    const STATUS_TRANSFER_IN_PROGRESS = 5;  // 转让中
    const STATUS_TRANSFER_COMPLETED = 6;  // 转让完成

    const STATUS_CANCELLED = 7;  // 已取消
    const STATUS_ASSIGNED = 8;  // 已分配

    // 使用状态字段定义
    const USE_STATUS_ACTIVE = 1;   // 使用中
    const USE_STATUS_INACTIVE = 2; // 未使用

    public static array $paras_list = [
        // 基本信息
        "dep_id"            => "部门|required",
        "country"           => "国家|required",
        "category"          => "类目|required",
        "reminder_interval" => "提醒间隔|required",
        "type"              => "商标类型",
        "status"            => "状态",
    ];

    public static array $json_keys = [
        'validity_period',
        'domain_id',
        'email_id'
    ];

    /**
     * 获取商标列表
     */
    public function getList($param, $order = 'id desc', $is_export = false)
    {
        $db = $this->db->table($this->table, 'a')
            ->field('a.*, t.shop_id, t.remark')
            ->leftJoin('trademark', 't', 'a.trademark_id = t.id');

        // 部门ID
        if (!empty($param['dep_id'])) {
            $db->andWhere('a.dep_id = :dep_id', ['dep_id' => $param['dep_id']]);
        }

        // 商标类型
        if (!empty($param['type'])) {
            $db->andWhere('a.type = :type', ['type' => $param['type']]);
        }

        // 状态
        if (isset($param['status'])) {
            $db->andWhere('a.status = :status', ['status' => $param['status']]);
        }

        // 品牌名
        if (!empty($param['brand_name'])) {
            $db->andWhere('t.brand_name LIKE :brand_name', ['brand_name' => '%' . $param['brand_name'] . '%']);
        }

        // 国家
        if (!empty($param['country'])) {
            $db->andWhere('a.country = :country', ['country' => $param['country']]);
        }

        // 类目
        if (!empty($param['category'])) {
            $db->andWhere('a.category = :category', ['category' => $param['category']]);
        }

        // 使用状态
        if (isset($param['use_status'])) {
            $db->andWhere('t.use_status = :use_status', ['use_status' => $param['use_status']]);
        }

        $db->order($order);

        if (!$is_export && !empty($param['page']) && !empty($param['page_size'])) {
            $list = $db->pages($param['page'], $param['page_size']);
            $maps = self::getMaps();
            foreach ($list['list'] as &$item) {
                $item = $this->formatItem($item, $maps);
            }
            return $list;
        } else {
            $list = $db->list();
            $maps = self::getMaps();
            $result = [];
            foreach ($list as $item) {
                $formatted = $this->formatItem($item, $maps);
                if ($is_export) {
                    // 导出使用中文键名
                    $result[] = self::changeToCnKey($formatted);
                } else {
                    $result[] = $formatted;
                }
            }

            return $result;
        }
    }

    /**
     * 获取商标详情
     */
    public function getDetail($id)
    {
        if (empty($id)) {
            return null;
        }

        $item = $this->db->table($this->table)
            ->where('id = :id', ['id' => $id])
            ->one();

        return $this->formatItem($item);
    }

    /**
     * 申请商标
     */
    public function applyTrademark($data)
    {
        $this->dataValidCheck($data, [
            'dep_id'            => '部门',
            'country'           => '国家',
            'category'          => '类目',
            'reminder_interval' => '提醒间隔'
        ]);

        $data['user_id'] = userModel::$qwuser_id;

        return parent::add($data, '申请');
    }

    /**
     * 接收申请
     */
    public function receiveApplication($data)
    {
        $old_data = $this->getById($data['id']);
        if (!$old_data) {
            throw new Exception('数据不存在');
        }

        if ($old_data['status'] != self::STATUS_RECEIVE_WAITING) {
            throw new Exception('状态不合法');
        }

        $this->dataValidCheck($data, [
            'type'   => '类型',
            'status' => '状态'
        ]);

        if ($data['type'] == self::TYPE_SELF) {
            if (!in_array($data['status'], [
                self::STATUS_REGISTER_WAITING
            ])) {
                throw new Exception('状态不合法');
            }
        } else {
            if (!in_array($data['status'], [
                    self::STATUS_TRANSFER_WAITING,
                ])) {
                throw new Exception('状态不合法');
            }
        }
        $data['operator'] = userModel::$qwuser_id;

        parent::edit($data, $data['id'], $old_data, '接收');
    }

    /**
     * 取消申请
     */
    public function cancelApplication($id)
    {
        $old_data = $this->getById($id);
        if (!$old_data) {
            throw new Exception('数据不存在');
        }

        $data = [
            'status'   => self::STATUS_CANCELLED,
            'operator' => userModel::$qwuser_id
        ];
        parent::edit($data, $id, $old_data,'取消申请');
    }

    /**
     * 更新商标状态和进展
     */
    public function updateProgress($data)
    {
        $remark = $data['remark'] ?? '';
        $old_data = $this->getById($data['id']);
        if (!$old_data) {
            throw new Exception('数据不存在');
        }

        if ($old_data['type'] == self::TYPE_SELF) {
            if (!in_array($data['status'], [
                self::STATUS_REGISTER_WAITING,
                self::STATUS_REGISTER_IN_PROGRESS,
                self::STATUS_REGISTER_COMPLETED])) {
                throw new Exception('状态不合法');
            }
        } else {
            if (!in_array($data['status'], [
                self::STATUS_TRANSFER_WAITING,
                self::STATUS_TRANSFER_IN_PROGRESS,
                self::STATUS_TRANSFER_COMPLETED])) {
                throw new Exception('状态不合法');
            }
        }

        // 由于跟进时没有变更状态，所以需要自己存日志
        $db = $this->db->table($this->table);
        $db->where('where id = :id', ['id' => $data['id']]);
        $data = $this->validateData($data);
        $res = $db->update([
            'status'   => $data['status'],
            'operator' => userModel::$qwuser_id
        ]);
        // 记录日志
        $this->saveDataLog($this->table, $data['id'], $data, $old_data, '跟进', $remark);
        return;
    }

    /**
     * 分配商标
     */
    public function assignToTrademark($id, $trademark_id)
    {
        $old_data = $this->getById($id);
        if (!$old_data) {
            throw new Exception('数据不存在');
        }

        if ($old_data['status'] != self::STATUS_RECEIVE_WAITING) {
            throw new Exception('状态不合法');
        }
        $bind_time = date('Y-m-d H:i:s', time());

        $data = [
            'trademark_id' => $trademark_id,
            'bind_time' => $bind_time,
            'status'       => self::STATUS_ASSIGNED,
        ];

        // 更新商标的分配时间
        $trademarkModel = new trademarkModel();
        $trademark = $trademarkModel->getById($trademark_id);

        if (empty($trademark)) {
            throw new Exception('商标不存在');
        }
        $trademarkModel->edit([
            'user_id' => $old_data['user_id'],
            'bind_time' => $bind_time
        ], $trademark_id, $trademark, '分配');


        parent::edit($data, $id, $old_data, '分配');
    }

    /**
     * 启动注册流程（自注册类型）
     */
    public function startRegistration($id, $data)
    {
        $old_data = $this->getById($id);
        if (!$old_data) {
            throw new Exception('数据不存在');
        }

        $data['status'] = self::STATUS_REGISTER_IN_PROGRESS;
        $data = $this->jsonEncodeFormat($data);
        parent::edit($data, $id, $old_data, '启动注册');
    }

    /**
     * 登记商标信息（外购类型）
     */
    public function registerTrademarkInfo($id, $data)
    {
        $old_data = $this->getById($id);
        if (!$old_data) {
            throw new Exception('数据不存在');
        }

        if (!in_array($old_data['status'], [self::STATUS_REGISTER_COMPLETED, self::STATUS_TRANSFER_COMPLETED])) {
            throw new Exception('状态不合法');
        }

        $bind_time = date('Y-m-d H:i:s', time());

        $updateData = [
            'status'       => self::STATUS_ASSIGNED,
            'bind_time'    => $bind_time,
            'operator'     => userModel::$qwuser_id
        ];

        if ($old_data['type'] == self::TYPE_SELF) {
            $this->dataValidCheck($data, [
                "register_date"             => "注册时间|required",
                "certificate_date"          => "下证日期|required",
                "certificate_number"        => "注册号/证书号|required",
                "original_storage"          => "原件保管地|required",
                "validity_period"           => "商标有效期|required",
                "earliest_declaration_date" => "最早宣誓日期|required",
                "shop_id"                   => "备案店铺",
                "record_date"               => "备案日期|required",
                "use_status"                => "使用状态",
                "remark"                    => "备注",
            ]);
            $data['brand_name'] = $old_data['brand_name'];
            $data['trademark_holder'] = $old_data['trademark_holder'];
            $data['domain_id'] = $old_data['domain_id'];
            $data['email_id'] = $old_data['email_id'];
            $data['service_provider'] = $old_data['service_provider'];
        } else {
            $this->dataValidCheck($data, [
                "brand_name"                => "品牌名|required",
                "transfer_lawyer"           => "转让律师|required",
                "trademark_holder"          => "商标持有人|required",
                "trademark_holder_pre"      => "商标原持有人|required",
                "transfer_date"             => "转让日期|required",
                "transfer_provider"         => "转让服务商|required",
                "price"                     => "价格|required",
                "currency"                  => "币种|required",
                "domain_id"                 => "域名|required",
                "email_id"                  => "注册邮箱|required",
                "service_provider"          => "服务商|required",
                "certificate_date"          => "下证日期|required",
                "certificate_number"        => "注册号/证书号|required",
                "original_storage"          => "原件保管地|required",
                "validity_period"           => "商标有效期|required",
                "earliest_declaration_date" => "最早宣誓日期|required",
                "shop_id"                   => "备案店铺",
                "record_date"               => "备案日期|required",
                "use_status"                => "使用状态",
                "remark"                    => "备注",
            ]);
            $updateData['brand_name'] = $data['brand_name'];
            $updateData['trademark_holder'] = $data['trademark_holder'];
            $updateData['trademark_holder_pre'] = $data['trademark_holder_pre'];
            $updateData['domain_id'] = $data['domain_id'];
            $updateData['email_id'] = $data['email_id'];
            $updateData['service_provider'] = $data['service_provider'];
            $updateData['validity_period'] = $data['validity_period'];
        }
        $data['dep_id'] = $old_data['dep_id'];
        $data['country'] = $old_data['country'];
        $data['category'] = $old_data['category'];
        unset($data['id']);
        $trademarkModel = new trademarkModel();
        $data['trademark_type'] = $old_data['type'] == self::TYPE_SELF ? $trademarkModel::TYPE_SELF : $trademarkModel::TYPE_PURCHASE;
        $data['bind_time'] = $bind_time;
        $data['user_id'] = $old_data['user_id'];
        $trademark_id = $trademarkModel->add($data);
        $updateData['trademark_id'] = $trademark_id;

        parent::edit($updateData, $id, $old_data, '商标信息登记');


    }

    public static function getMaps() {
        $users = redisCached::getUserInfo();
        $users = array_column($users, 'user_name', 'user_id');

        $deps = redisCached::getDepartment();
        $deps = array_column($deps, 'dep_name', 'id');

        $domains = redisCached::getDomain();
        $emails = redisCached::getEmail();

        $shops = redisCached::getShop();
        $shops = array_column($shops, 'shop_number', 'id');

        return [
            'users' => $users,
            'deps' => $deps,
            'domains' => $domains,
            'emails' => $emails,
            'shops' => $shops
        ];

    }

    /**
     * 格式化输出项
     */
    public function formatItem($item, $maps = [])
    {
        $users = $maps['users'] ?? [];
        if (empty($users)) {
            $users = redisCached::getUserInfo();
            $users = array_column($users, 'user_name', 'user_id');
        }
        $deps = $maps['deps'] ?? [];
        if (empty($deps)) {
            $deps = redisCached::getDepartment();
            $deps = array_column($deps, 'name', 'id');
        }
        $domains = $maps['domains'] ?? [];
        if (empty($domains)) {
            $domains = redisCached::getDomain();
        }
        $emails = $maps['emails'] ?? [];
        if (empty($emails)) {
            $emails = redisCached::getEmail();
        }
        $shops = $maps['shops'] ?? [];
        if (empty($shops)) {
            $shops = redisCached::getShop();
            $shops = array_column($shops, 'shop_number', 'id');
        }
        $maps = [
            ['name' => 'user_name', 'maps' => $users, 'key' => 'user_id'],
            ['name' => 'dep_name', 'maps' => $deps, 'key' => 'dep_id'],
            ['name' => 'domain', 'maps' => $domains, 'key' => 'domain_id', 'is_array' => 1, 'keys' => ['domain']],
            ['name' => 'email', 'maps' => $emails, 'key' => 'email_id', 'is_array' => 1, 'keys' => ['email_account']],
            ['name' => 'shop_number', 'maps' => $shops, 'key' => 'shop_id']
        ];

        return parent::formatItem($item, $maps);
    }
}
