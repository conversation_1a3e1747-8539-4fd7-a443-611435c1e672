<?php
/**
 * @author: zhangguoming
 * @Time: 2024/4/3 10:34
 */

namespace  plugins\goods\form;

use core\lib\db\dbMysql;

class goodsFunctionFrom
{
    public static function getGoodsFunction(array $function_ids) {
        if (!count($function_ids)) {
            return [];
        }
        $db = dbMysql::getInstance();
        $db->table('goods_function')->where('where is_delete = 0');
        $db->field('id,fc_name,fc_name_en');
        $data = $db->whereIn('id',$function_ids)->list();
        return $data;
    }
}