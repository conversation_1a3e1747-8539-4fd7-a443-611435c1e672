<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/19 17:51
 */

namespace financial\controller;

use core\lib\db\dbFMysql;
use financial\models\mskuReportModel;
use Rap2hpoutre\FastExcel\FastExcel;

class testController
{
    public function setColumn() {
        $data = '{"totalFbaAndFbmQuantity":0,"totalFbaAndFbmAmount":0,"totalSalesQuantity":0,"fbaSalesQuantity":0,"fbmSalesQuantity":0,"totalReshipQuantity":0,"reshipFbmProductSalesQuantity":0,"reshipFbmProductSaleRefundsQuantity":0,"reshipFbaProductSalesQuantity":0,"reshipFbaProductSaleRefundsQuantity":0,"mcFbaFulfillmentFeesQuantity":0,"cgAbsQuantity":0,"cgQuantity":0,"fbaInventoryCreditQuantity":0,"disposalQuantity":0,"removalQuantity":0,"totalAdsSales":0,"adsSdSales":0,"adsSpSales":0,"sharedAdsSbSales":0,"sharedAdsSbvSales":0,"totalAdsSalesQuantity":0,"adsSdSalesQuantity":0,"adsSpSalesQuantity":0,"sharedAdsSbSalesQuantity":0,"sharedAdsSbvSalesQuantity":0,"totalSalesAmount":0,"fbaSaleAmount":0,"fbmSaleAmount":0,"shippingCredits":0,"promotionalRebates":0,"fbaInventoryCredit":0,"cashOnDelivery":0,"otherInAmount":0,"fbaLiquidationProceeds":0,"fbaLiquidationProceedsAdjustments":0,"amazonShippingReimbursement":0,"safeTReimbursement":0,"netcoTransaction":0,"reimbursements":0,"clawbacks":0,"sharedComminglingVatIncome":0,"giftWrapCredits":0,"guaranteeClaims":0,"costOfPoIntegersGranted":0,"others":0,"totalSalesRefunds":0,"fbaSalesRefunds":0,"fbmSalesRefunds":0,"shippingCreditRefunds":0,"giftWrapCreditRefunds":0,"chargebacks":0,"costOfPoIntegersReturned":0,"promotionalRebateRefunds":0,"totalFeeRefunds":0,"sellingFeeRefunds":0,"fbaTransactionFeeRefunds":0,"refundAdministrationFees":0,"otherTransactionFeeRefunds":0,"refundForAdvertiser":0,"pointsAdjusted":0,"shippingLabelRefunds":0,"refundsQuantity":0,"refundsRate":0,"fbaReturnsQuantity":0,"fbaReturnsSaleableQuantity":0,"fbaReturnsUnsaleableQuantity":0,"fbaReturnsQuantityRate":0,"platformFee":0,"totalFbaDeliveryFee":0,"fbaDeliveryFee":0,"mcFbaDeliveryFee":0,"otherTransactionFees":0,"totalAdsCost":0,"adsSpCost":0,"adsSbCost":0,"adsSbvCost":0,"adsSdCost":0,"sharedCostOfAdvertising":0,"promotionFee":-25,"sharedSubscriptionFee":-25,"sharedLdFee":0,"sharedCouponFee":0,"sharedEarlyReviewerProgramFee":0,"sharedVineFee":0,"totalStorageFee":0,"fbaStorageFee":0,"sharedFbaStorageFee":0,"longTermStorageFee":0,"sharedLongTermStorageFee":0,"sharedStorageRenewalBilling":0,"sharedFbaDisposalFee":0,"sharedFbaRemovalFee":0,"sharedFbaInboundTransportationProgramFee":0,"sharedLabelingFee":0,"sharedPolybaggingFee":0,"sharedBubblewrapFee":0,"sharedTapingFee":0,"sharedFbaCustomerReturnFee":0,"sharedFbaInboundDefectFee":0,"sharedFbaOverageFee":0,"sharedAmazonPartneredCarrierShipmentFee":0,"sharedFbaInboundConvenienceFee":0,"sharedItemFeeAdjustment":0,"sharedOtherFbaInventoryFees":0,"fbaStorageFeeAccrual":0,"fbaStorageFeeAccrualDifference":0,"longTermStorageFeeAccrual":0,"longTermStorageFeeAccrualDifference":0,"sharedFbaIntegerernationalInboundFee":0,"adjustments":0,"totalPlatformOtherFee":0,"shippingLabelPurchases":0,"sharedCarrierShippingLabelAdjustments":0,"sharedLiquidationsFees":0,"sharedManualProcessingFee":0,"sharedOtherServiceFees":0,"sharedMfnPostageFee":0,"totalSalesTax":0,"taxCollected":0,"taxCollectedGiftWrap":0,"taxCollectedShipping":0,"taxCollectedDiscount":0,"taxCollectedProduct":0,"tcsIgstCollected":0,"tcsSgstCollected":0,"tcsCgstCollected":0,"sharedComminglingVatExpenses":0,"sharedTaxAdjustment":0,"salesTaxRefund":0,"taxRefunded":0,"taxRefundedGiftWrap":0,"taxRefundedShipping":0,"taxRefundedDiscount":0,"taxRefundedProduct":0,"tcsIgstRefunded":0,"tcsSgstRefunded":0,"tcsCgstRefunded":0,"salesTaxWithheld":0,"refundTaxWithheld":0,"tdsSection194ONet":0,"cgPriceTotal":0,"hasCgPriceDetail":0,"cgUnitPrice":0,"proportionOfCg":0,"cgTransportCostsTotal":0,"hasCgTransportCostsDetail":0,"cgTransportUnitCosts":0,"proportionOfCgTransport":0,"totalCost":0,"proportionOfTotalCost":0,"cgOtherCostsTotal":0,"cgOtherUnitCosts":0,"hasCgOtherCostsDetail":0,"proportionOfCgOtherCosts":0,"grossProfit":-25,"grossRate":0,"grossProfitIncome":0,"grossProfitTax":0,"otherFeeStr":null,"customOrderFee":0,"customOrderFeePrincipal":0,"customOrderFeeCommission":0,"roi":-1,"id":"76761091","sid":"7949","reportDateMonth":"2024-06","postedDateLocale":"2024-06-17","isDisplayDetail":null,"smallImageUrl":"","msku":"-","asin":"","parentAsin":"","storeName":"\u7cbe\u54c1\u53ef\u7528*\u5f85\u5206\u914d*AM-G372-UK","country":"\u82f1\u56fd","countryCode":"UK","localName":"","localSku":"","itemName":"","model":"","principalRealname":null,"productDeveloperRealname":null,"categoryName":"","brandName":"","currencyCode":"GBP","currencyIcon":"\uffe1","listingTagIds":""}';
        $data = json_decode($data,true);
        $db = dbFMysql::getInstance();
        $list =  $db->table('column')
            ->where('where id>0')->list();
        $columns = array_column($list,'key');
        $db->table('column');
        foreach ($data as $k=>$v) {
            if (in_array($k,['listingTagIds','id','otherFeeStr'])){
                continue;
            }
            if (!in_array($k,$columns)) {
                $db->table('key_name')->insert(['key_'=>$k,'column_name'=>$k]);
            }
        }
    }
    //更新报告数据列
    public function setMskuReportExcewl() {
        dd('44');
        $data = mskuReportModel::$export_key_list;
        $db = dbFMysql::getInstance();
        foreach ($data as $k=>$v) {
            $db->table('column')
                ->insert([
                'key_name'=>$k,
                'column_name'=>$v,
                'data_type'=>2,
            ]);
        }
    }
    //更新商品数据所在表标识
    public function setColumnTableIndex() {
        $array = [
            ['id' => 1, 'ss' => 10],
            ['id' => 2, 'ss' => 20],
            ['id' => 3, 'ss' => 30]
        ];
        $array_ss = array_column($array,'ss');
        dd(array_sum($array_ss));
        $db = dbFMysql::getInstance();
        $table0 = $db->table('msku_report_2024')->one();
        $table1 = $db->table('msku_report1_2024')->one();
        $table2 = $db->table('msku_report2_2024')->one();
        $table3 = $db->table('msku_report3_2024')->one();
        $table4 = $db->table('msku_report4_2024')->one();
        $keys_0 = [];
        foreach ($table0 as $k=>$v) {
            $keys_0[] = $k;
        }
        $keys_1 = [];
        foreach ($table1 as $k=>$v) {
            $keys_1[] = $k;
        }
        $keys_2 = [];
        foreach ($table2 as $k=>$v) {
            $keys_2[] = $k;
        }
        $keys_3 = [];
        foreach ($table3 as $k=>$v) {
            $keys_3[] = $k;
        }
        $keys_4 = [];
        foreach ($table4 as $k=>$v) {
            $keys_4[] = $k;
        }
        $column_list = $db->table('column')->list();
        $column_key = array_column($column_list,'key_name');

        $w0 = array_intersect($column_key,$keys_0);
        $w1 = array_intersect($column_key,$keys_1);
        $w2 = array_intersect($column_key,$keys_2);
        $w3 = array_intersect($column_key,$keys_3);
        $w4 = array_intersect($column_key,$keys_4);

        $db->table('column')->whereIn('key_name',$w0)
            ->update(['table_index'=>0]);
        $db->table('column')->whereIn('key_name',$w1)
            ->update(['table_index'=>1]);
        $db->table('column')->whereIn('key_name',$w2)
            ->update(['table_index'=>2]);
        $db->table('column')->whereIn('key_name',$w3)
            ->update(['table_index'=>3]);
        $db->table('column')->whereIn('key_name',$w4)
            ->update(['table_index'=>4]);
    }

    //获取msku_report统计表视图（根据字段表）；按年
    public function getmMkuReportView() {
        $ll = array_flip(mskuReportModel::$import_key_list);
        returnSuccess($ll);

        $db = dbFMysql::getInstance();
        $column_list = $db->table('column')
            ->where('where custom_id = 0')
            ->order('table_index')
            ->list();
        $new_keys = [];
        foreach ($column_list as $v) {
            if ($v['table_index'] > 0) {
                $new_keys[] = "sum({$v['key_name']}) as {$v['key_name']}";
            } else {
                $new_keys[] = $v['key_name'];
            }
        }
        $keys = ['project_id','category_id','supplier_id','seller_id'];
        $keys = array_merge($keys,$new_keys) ;
        $year = '2024';
        $view = "CREATE VIEW oa_combined_project_$year as SELECT ";
        $view .= implode(',',$keys);
        $view .= "from oa_f_msku_report_2024 as a left join oa_f_msku_report1_2024 as b on b.base_id = a.id left join oa_f_msku_report2_2024 as c on c.base_id = a.id left join oa_f_msku_report3_2024 as d on d.base_id = a.id left join oa_f_msku_report4_2024 as e on e.base_id = a.id where a.is_delete = 0 GROUP BY msku,countryCode";
        dd($view);
//         AS
//SELECT user_id,id as project_id,matter_name,tpl_name,created_time,status,is_stop,complete_time,expected_day,flow_path_id,current_node_info,1 as type  FROM oa_goods_project where is_delete = 0
    }

}