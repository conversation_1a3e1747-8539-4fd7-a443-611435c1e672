<?php

namespace plugins\checkin\controller;

use admin\models\qwModel;
use plugins\checkin\form\messagesFrom;
use plugins\checkin\models\holidayModel;
use plugins\checkin\models\userModel;
use core\lib\db\dbCMysql;
use core\lib\db\dbMysql;

class userConfirmController
{
    // 获取考勤确认列表
    public function getList()
    {
        $paras_list = array('month', 'page', 'page_size', 'users', 'is_confirm');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        $page = $param['page'] ?? 1;
        $limit = $param['page_size'] ?? 10;
        if (!$param['month']) returnError('考勤月份必填');
        if (isset($param['is_confirm']) && !in_array($param['is_confirm'], [0, 1, 2])) returnError('确认状态不正确');
        $cdb = dbCMysql::getInstance();
        $cdb->table('user_checkin_summary');
        $cdb->where('where summary_month = :month and is_confirm is not null', ['month' => $param['month']]);

        if (isset($param['is_confirm'])) {
            $cdb->andWhere('is_confirm = :is_confirm', ['is_confirm' => $param['is_confirm']]);
        }
        if (!empty($param['users'])) {
            $users = json_decode($param['users'], true);
            $cdb->whereIn('user_id', $users);
        }

        $cdb->order('id desc');
        $list = $cdb->pages($page, $limit);

        $db = dbMysql::getInstance();
        $user_wids = array_column($list['list'], 'user_id');
        $users = $db->table('qwuser')->field('id,wid,wname,position')->whereIn('wid', $user_wids)->list();
        $userMap = array_column($users, null, 'wid');

        // 格式化数据
        $ret_list = [];

        foreach ($list['list'] as $item) {
            $base_info = json_decode($item['base_info'], true);
            $ret_list[] = [
                'id' => $item['id'],
                'user_id' => $item['user_id'],
                'user_name' => $userMap[$item['user_id']]['wname'] ?? '',
                'departs_name' => $base_info['departs_name'],
                'position' => $userMap[$item['user_id']]['position'] ?? '',
                'base_info' => $base_info,
                'is_confirm' => $item['is_confirm'],
                'confirm_time' => $item['confirm_time'],
                'urge_time' => $item['urge_time'],
                'feedback' => json_decode($item['feedback'], true),

            ];
        }
        $list['list'] = $ret_list;

        // 按确认状态统计
        $confirm = [];
        $cdb->table('user_checkin_summary')->field('is_confirm, count(id) as count');
        $cdb->where('where summary_month = :month and is_confirm is not null', ['month' => $param['month']]);
        $cdb->groupBy(['is_confirm']);
        $user_confirm = $cdb->list();
        foreach ($user_confirm as $item) {
            $confirm[$item['is_confirm']] = [
                'count'  => $item['count'],
                'is_confirm' => $item['is_confirm'],
            ];
        }
        $list['user_confirm'] = $confirm;


        returnSuccess($list);
    }

    // 详情
    public function detail()
    {
        $paras_list = array('months');
        $param = array_intersect_key($_GET, array_flip($paras_list));
        if (!$param['months']) returnError('考勤月份必填');

        $cdb = dbCMysql::getInstance();
        $cdb->table('user_checkin_summary');
        $cdb->where('where user_id = :user_id AND is_confirm IS NOT NULL', ['user_id' => userModel::$wid]);
        $cdb->whereIn('summary_month', json_decode($param['months'], true));
        $data = $cdb->list();
        if (empty($data)) returnSuccess(['list' => []]);

        $db = dbMysql::getInstance();
        $users = $db->table('qwuser')->field('id,wid,wname,position')->where('where id = :user_id', ['user_id' => userModel::$qwuser_id])->list();
        $userMap = array_column($users, null, 'wid');

        // 格式化数据
        $ret_list = [];

        foreach ($data as $item) {
            $base_info = json_decode($item['base_info'], true);
            $ret_list[] = [
                'id' => $item['id'],
                'user_id' => $item['user_id'],
                'user_name' => $userMap[$item['user_id']]['wname'] ?? '',
                'departs_name' => $base_info['departs_name'],
                'position' => $userMap[$item['user_id']]['position'] ?? '',
                'base_info' => $base_info,
                'summary_month' => $item['summary_month'],
                'checkin_day' => json_decode($item['checkin_day'], true),
                'summary_info' => json_decode($item['summary_info'], true),
                'is_confirm' => $item['is_confirm'],
                'confirm_time' => $item['confirm_time'],
                'feedback' => json_decode($item['feedback'], true),
                'uge_time' => $item['uge_time'],
            ];
        }
        $list['list'] = $ret_list;

        returnSuccess($list);
    }

    // 确认
    public function confirm()
    {
        $paras_list = array('id');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        if (empty($param['id'])) returnError('id必填');

        $cdb = dbCMysql::getInstance();
        $cdb->table('user_checkin_summary');
        $cdb->where('where id = :id', ['id' => $param['id']]);
        $data = $cdb->one();
        if (empty($data)) returnError('数据不存在');
        if ($data['user_id'] != userModel::$wid) returnError('不能确认别人的考勤');
        if ($data['is_confirm'] != 0) returnError('不能进行确认');

        $update = [
            'is_confirm' => 1,
            'confirm_time' => date('Y-m-d H:i:s'),
        ];
        $cdb->where('id = :id', ['id' => $param['id']])->update($update);
        returnSuccess([], '确认成功');
    }

    // 异常反馈
    public function feedback()
    {
        $paras_list = array('id', 'feedback');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        if (empty($param['id'])) returnError('id必填');

        $cdb = dbCMysql::getInstance();
        $cdb->table('user_checkin_summary');
        $cdb->where('where id = :id', ['id' => $param['id']]);
        $data = $cdb->one();
        if (empty($data)) returnError('数据不存在');
        if ($data['user_id'] != userModel::$wid) returnError('不能异常反馈别人的考勤');
        if ($data['is_confirm'] != 0) returnError('不能进行确认');

        $feedback = json_decode($data['feedback'], true);
        $feedback[] = [
            'content' => $param['feedback'],
            'time' => date('Y-m-d H:i:s'),
        ];

        $update = [
            'is_confirm' => 2,
            'feedback' => json_encode($feedback, JSON_UNESCAPED_UNICODE),
        ];
        $cdb->where('id = :id', ['id' => $param['id']])->update($update);

        $company_name = '深圳易威行科技创新有限公司';
        $user_name = userModel::$wname;
        $month = $data['summary_month'];
        messagesFrom::senMeg([$data['confirm_create_user_id']], 1, "{$user_name}在【{$company_name}_{$month}考勤】考勤确认时提交了异常", $data['id'], $param['feedback'], '考勤确认异常');

        returnSuccess([], '反馈成功');
    }



    // 催办
    public function urge()
    {
        $paras_list = array('ids');
        $param = array_intersect_key($_POST, array_flip($paras_list));
        empty($param['ids']) && returnError('ids必填');
        $ids = json_decode($param['ids'], true);
        $ids = array_values(array_filter(array_values($ids)));
        if (empty($ids)) returnError('ids必填');

        $cdb = dbCMysql::getInstance();
        $cdb->table('user_checkin_summary');
        $cdb->whereIn('id', $ids);
        $list = $cdb->list();
        if (empty($list)) returnError('数据不存在');

        $company_name = '深圳易威行科技创新有限公司';
        $user_name = userModel::$wname;
        foreach ($list as $item) {
            $item['is_confirm'] != 0 && returnError('不能催办');
            if (strtotime($item['urge_time']) > time() - 30 * 60) {
                returnError('30分钟内只能催办一次');
            }
            $month = $item['summary_month'];
            messagesFrom::senMeg([$item['user_id']], 1, "{$user_name}提醒您尽快确认【{$company_name}_{$month}考勤】", $item['id']);
            $cdb->table('user_checkin_summary')->where('id = :id', ['id' => $item['id']])->update(['urge_time' => date('Y-m-d H:i:s')]);
        }

        returnSuccess([], '催一下成功');

    }


}