<?php
/**
 * @author: zhangguoming
 * @Time: 2025/4/18 10:30
 */

namespace plugins\goods\form;

class requestParticipantForm
{
    //修改管理人
    public static function updateUser($db,int $request_id,array $user_ids, int $type) {
        $db->table('imgs_request_participant')
            ->where('type = :type and request_id = :request_id',['type'=>$type,'request_id'=>$request_id])
            ->delete();
        $user_ids = array_unique($user_ids);
        foreach ($user_ids as $user_id) {
            $db->table('imgs_request_participant')
                ->insert([
                    'request_id'=>$request_id,
                    'user_id'=>$user_id,
                    'type'=>$type
                ]);
        }

    }
}