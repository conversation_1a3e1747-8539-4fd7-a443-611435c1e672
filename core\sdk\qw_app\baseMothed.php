<?php

/**
 * @author: zhangguoming
 * @Time: 2025/4/10 11:20
 */
namespace core\sdk\qw_app;

use core\lib\config;

class baseMothed extends qwConfig
{
    public static $error_msg = '';
    //token获取
    public static function getToken(){
        $redis = (new \core\lib\predisV())::$client;
        $qw_config = config::all('qw');
        $token = $redis->get('qy_token');
        if ($token) {
            return $token;
        } else {
            //内部应用
            if (SYS_ENV == 'develop'){
                $token_url = $qw_config['token_url_nei'];
                $data = requestHttp($token_url,'get');
                if ($data && $data['data']) {
                    $redis->set('qy_token', $data['data']);
                    $redis->expire('qy_token',5*60);
                    return $data['data'];
                } else {
                    self::$error_msg = '企微：获取token失败';
                    return false;
                }
            } else {
                $token_url = $qw_config['token_url'].'?corpid='.$qw_config['corpid'].'&corpsecret='.$qw_config['secret'];
                $data = requestHttp($token_url,'get');
                if ($data && $data['errcode']==0) {
                    $redis->set('qy_token', $data['access_token']);
                    $redis->expire('qy_token',5*60);
                    return $data['access_token'];
                } else {
                    self::$error_msg = '企微：获取token失败';
                    return false;
                }
            }
        }
    }
    //请求结果处理
    public static function getRequsestData($mothed,$data,$url){
        $res = requestHttp($url,$mothed,json_encode($data));
        if (!empty($res)) {
            if ($res['errcode'] != 0) {
                self::$error_msg = '企微请求返回错误：'.$res['errmsg'];
                return false;
            } else {
                return $res;
            }
        } else {
            self::$error_msg = '企微请求失败';
            return false;
        }
    }
}