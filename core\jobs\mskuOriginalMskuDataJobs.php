<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/24 9:57
 *
 * 导出报告数据
 */

namespace core\jobs;

use core\lib\config;
use core\lib\db\dbFMysql;
use financial\form\customColumnForm;
use financial\form\goodsDataForm;
use financial\form\mskuReportForm;
use financial\form\originalDataForm;

class mskuOriginalMskuDataJobs
{
    public string $unqueid = '';
    public string $key;
    public int $pages_size = 1000;
    public function __construct($key){
        $this->unqueid = uniqid();
        $this->key = $key;
    }
    public function task(){
        $redis = (new \core\lib\predisV())::$client;
        $export_data = json_decode($redis->get($this->key),true);
        $page = $export_data['page'];
        $m_date = $export_data['m_date'];
        $param_ = [
            'time'=>$m_date,
            'page'=>$page,
            'page_size'=>$this->pages_size,
        ];
        $data = originalDataForm::getList($param_);
        if (count($data['list'])) {
            $url = originalDataForm::export($data['list']);
            $export_data['page'] = $page+1;
            $export_data['success_count'] = $export_data['success_count']+count($data['list']);
            $export_data['excel_url'][] = $url;
            if ($export_data['success_count'] < $data['total']) {
                $queue_key = config::get('delay_queue_key', 'app');
                $task = new mskuOriginalMskuDataJobs($this->key); // 创建任务类实例
                $redis->zAdd($queue_key, [], 0, serialize($task));
                $redis->set($this->key,json_encode($export_data));
                $redis->expire($this->key,10);
            } else {
                //生成压缩包
                $save_path = '/public_financial/temp/msku_report/original/downLoad';
                if (!file_exists(SELF_FK.$save_path)) {
                    mkdir(SELF_FK.$save_path, 0777, true);
                }
                $zip_url = $save_path ."/".date('YmdHis').'.zip';
                //生成压缩包
                setZipByUrl($export_data['excel_url'],$zip_url);
                $export_data['zip_url'] = $zip_url;
                //保存导出历史
                $redis->set($this->key,json_encode($export_data));
                $redis->expire($this->key,10);
            }
        }
    }
}