<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/13 16:06
 */

namespace financial\controller;

use financial\form\goodsLevelForm;
use financial\models\goodsStorkModel;

class goodsLevelController
{
    //列表
    public function getList() {

    }
    //新增修改
    public static function edit() {

    }
    //禁用启用
    public static function ban() {

    }
    //获取详情
    public static function getDetail() {

    }
    //导出
    public static function export() {

    }

    //根据等级规则修改商品等级(单个立即更新)
    public static function updateGoodsLevel() {
        $id = $_POST['level_id'];
        $m_date = $_POST['m_date']??date('Y-m');
        goodsLevelForm::updateGoodsLevel($id,$m_date);
        if (!empty(goodsLevelForm::$return_msg)) {
            returnError(goodsLevelForm::$return_msg);
        } else {
            returnSuccess('',"更新成功");
        }

    }
}