<?php
/**
 * @author: zhangguoming
 * @Time: 2024/5/7 17:39
 */

namespace  plugins\goods\models;

use core\lib\config;
use Rap2hpoutre\FastExcel\FastExcel;

class combinedProjectModel
{
    public static array $export_key = [
        'matter_name'=>'流程名称',
        'status'=>'流程状态',
        'created_wname'=>'流程创建人',
        'created_time'=>'创建时间',
        'expected_time'=>'预计完成时间',
        'complete_time'=>'完成时间',
    ];
    //导出
    public static function export($list,$export_key)
    {
        //表头
        $new_data = [];
        //数据
        $xuhao = 1;
        foreach ($list as $v) {
            $item['序号'] = $xuhao;
            foreach ($export_key as $key) {
                $value = '';
                if (in_array($key, ['created_time', 'complete_time'])) {
                    $item[self::$export_key[$key]] = is_null($v[$key])?'':date('Y-m-d H:i:s', $v[$key]);
                } elseif ($key == 'expected_time') {
                    if ((int)$v['expected_day'] > 0) {
                        $timestampB = strtotime('+'.(int)$v['expected_day'].' days',  $v['created_time']);
                        $item[self::$export_key[$key]] = date('Y-m-d H:i:s', $timestampB);
                    } else {
                        $item[self::$export_key[$key]] = '';
                    }

                } else {
                    if ($key == 'status') {
                        if ($v['is_stop'] == 1) {
                            $item[self::$export_key[$key]] = '暂停';
                        } else {
                            $item[self::$export_key[$key]] = config::getDataName('goods_project_status',$v[$key]);
                        }
                    } else {
                        $item[self::$export_key[$key]] = $v[$key];
                    }

                }
            }

            $new_data[] = $item;
            $xuhao++;
        }
        //保存
        $save_path = "/public/downLoad/combined_project/temp/" . userModel::$wid;
        $url = SELF_FK . $save_path;
        if (!file_exists($url)) {
            mkdir($url, 0777, true);
        }
        $path = $save_path . "/" . date('YmdHis') . uniqid() . '.xlsx';
        $url = SELF_FK . $path;
        if (!file_exists($url)) {
            touch($url);
        }
        (new FastExcel($new_data))->export($url);
        return $path;

    }

}