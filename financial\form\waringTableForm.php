<?php
/**
 * @author: zhangguoming
 * @Time: 2024/11/26 9:30
 */

namespace financial\form;


//等级列表查询
use core\lib\db\dbFMysql;
use core\lib\db\dbMysql;
use financial\models\authListModel;
use financial\models\boardTableModels;
use financial\models\userModel;

class waringTableForm extends boardTableModels
{
    public static string $waring_month = '';
    public static int $list_type = 1; //2导出，1列表
    //预警
    public static function waringGoodsTotal() {
        $month = end(self::$search_month);
        self::$waring_month = $month;
        $rule_count = self::getGoodsWaring();
        if ($rule_count) {
            //数据查询
            $lx_field = ['weidu_key as asin', 'country_code', 'waring_id'];
            $lx_group_by = ['weidu_key', 'country_code', 'waring_id'];
            //领星数据
            $lx_list = self::getSqlWhereList($lx_field, [], $lx_group_by);
            if (self::$list_type == 2) {
                //导出
                $data = self::arrangeWaringList($lx_list);
            } else {
                $list = self::arrangeWaringList($lx_list['list']);
                $data =  [
                    'count'=>$lx_list['total'],
                    'page_count'=>count($list),
                    'page'=>self::$param['page'],
                    'page_size'=>self::$param['page_size'],
                    'list'=>$list,
                    'export_key'=>goodsWaringForm::$export_list,
                ];
            }
        } else {
            if (self::$list_type == 2) {
                //导出
                $data = [];
            } else {
                $data =  [
                    'count'=>0,
                    'page_count'=>0,
                    'page'=>self::$param['page'],
                    'page_size'=>self::$param['page_size'],
                    'list'=>[],
                ];
            }
        }
        return $data;
    }

    //获取本月预警规则数据
    public static function getGoodsWaring() {
        $time_ = strtotime(self::$waring_month);
        $db = dbFMysql::getInstance();
        $list_count = $db->table('waring_rules')
            ->where('status = 1 and is_delete = 0 and begin_time = :begin_time',['begin_time'=>$time_])
            ->field('id')
            ->count();
        return $list_count;
    }

    /** 重写sql构成 */
    //获取查询条件(领星原表,所有数据)
    public static function getSqlWhereList($field,$field_u,$group_by,$waring_array = [],$is_last_month = 0) {
        $month = self::$waring_month;
        //运营权限数据验证
        if (!self::$show_all_data) {
            if (count(self::$yunying_ids) == 0 && count(self::$project_ids) == 0) {
                return [];
            }
        }
        //运营月份可查看数据验证
        if (!in_array($month,self::$search_month)) {
            return [];
        }
        $db = dbFMysql::getInstance();
        $param = self::$param;
        self::getGoodsSku($db);
        $sku_list = self::$sql_sku_list;
        $user_sku = self::$sql_use_sku;
        if (self::$table == 'asin') {
            self::getNewGoodsWhere($db);
        }
        $field = array_unique($field);
        $field_string = implode(',',$field);
        //获取数据
        $year = explode('-',$month)[0];
        //等级维度，维度默认asin
        $db->table('goods_waring_check','a')
            ->leftJoin('table_month_count_'.$year,'b',"b.countryCode = a.country_code and b.asin = a.weidu_key and b.reportDateMonth = a.m_date")
            ->where('a.m_date = :m_date and b.is_delete = 0',['m_date'=>$month]);
        //预警id
        if (!empty($param['waring_id'])) {
            $db->andWhere('a.waring_id = :waring_id',['waring_id'=>(int)$param['waring_id']]);
        }
        //预警状态
        if (!empty($param['waring_status'])) {
            $db->andWhere('a.status = :status',['status'=>(int)$param['waring_status']]);
        }
        //国家
        if (!empty($param['country_code']) && $param['country_code'] != '[]') {
            $country_codes = json_decode($param['country_code']);
            $db->whereIn('a.country_code',$country_codes);
        }
        //项目
        if (!empty($param['project_ids']) && $param['project_ids']!='[]') {
            $project_ids = json_decode($param['project_ids']);
            if (!self::$show_all_data) {
                $project_ids = array_intersect(self::$project_ids,$project_ids);
            }
            $db->whereIn('b.project_id',$project_ids);
        }
        //权限运营+搜索
        $user_id = userModel::$qwuser_id;
        if (!empty($param['yunying_ids']) && $param['yunying_ids']!='[]') {
            if (!self::$show_all_data) {
                $yunying_ids = array_intersect(self::$yunying_ids,json_decode($param['yunying_ids']));
                $yunying_str = getYunyingSql($yunying_ids,self::$project_ids,$user_id,'b');
                $db->andWhere($yunying_str);
            } else {
                $db->whereIn('b.yunying_id',json_decode($param['yunying_ids']));
            }
        } else {
            if (!self::$show_all_data) {
                $yunying_str = getYunyingSql(self::$yunying_ids,self::$project_ids,$user_id,'b');
                $db->andWhere($yunying_str);
            }
        }
        //自定义搜索
        if (!empty($param['search_type']) && !empty($param['search_value'])) {
            if ($param['search_type'] == 'asin') {
                $db->andWhere('b.asin like :asin',['asin'=>"%{$param['search_value']}%"]);
            }elseif ($param['search_type'] == 'sku') {
                $db->andWhere('b.localSku like :sku',['sku'=>"%{$param['search_value']}%"]);
            } elseif ($param['search_type'] == 'p_asin') {
                $db->andWhere('b.parentAsin like :parentAsin',['parentAsin'=>"%{$param['search_value']}%"]);
            }
        }
        if ($user_sku) {
            $db->whereIn('b.localSku',$sku_list);
        }
        if (self::$sql_use_new_goods) {
            $array_ = self::$sql_new_goods_data;
            if (self::$sql_use_new_goods == 2) {
                //非新品查询
                $db->whereInMuch(['b.asin','b.countryCode'],$array_);
            } else {
                $db->whereInMuch(['b.asin','b.countryCode'],$array_,'not');
            }
        }
        $db->field($field_string)->groupBy($group_by);
        if (self::$list_type == 2) {
            $report_data = $db->list();
        } else {
            $report_data = $db->pages(self::$param['page'],self::$param['page_size']);
        }
        return $report_data;
    }
    //自定义表
    //整理预警需要的数据
    public static function arrangeWaringList($lx_list) {
        $country_data = self::getCountryData();
        //商品信息，国家信息数据（按照asin整理好需要的数据）
        $waring_goods_ = self::getGoodsDataWaring($lx_list);
        //整理数据
        $new_list = [];
        foreach ($lx_list as $v) {
            $asin_key_ = $v['asin'].'_'.$v['country_code'].'_'.$v['waring_id'];
            $waring_info = $waring_goods_[$asin_key_];
            $v = array_replace($v,$waring_info['base_info']);
            foreach (authListModel::$waring_auth as $key_list){
                $key_ = $key_list['key'];
                if (in_array($key_,['asin','country_code'])) {
                    continue;
                }
                if (in_array($key_,['p_asin','sku','level_name'])) {
                    $v[$key_] = $waring_info[$key_];
                    continue;
                }
                $goods_list = $waring_info['goods_list'];
                if (in_array($key_,['product_name','category_name','product_developer','is_new','pic_url'])){
                    $v[$key_] = array_values(array_unique(array_column($goods_list,$key_)));
                } else {
                    if ($key_ == 'country') {
                        $v[$key_] = $country_data[$v['country_code']]??'';
                    } elseif ($key_ == 'yunying') {
                        $v[$key_] = $waring_info['yunying_list'];
                    }
                }
            }
            //商品信息
            $new_list[] = $v;
        }
        return $new_list;
    }
    //获取基础信息
    private static function getGoodsDataWaring($lx_list) {
        //$lx_list 是以asin+国家+预警id为维度的
        //查询list维度下的品名和分类名称
        $month = self::$waring_month;
        $year = explode('-',$month)[0];
        $asins = array_column($lx_list,'asin');
        $waring_ids = array_column($lx_list,'waring_id');
        $dbF = dbFMysql::getInstance();
        //预警信息的查询与写入
        $waring_goods = $dbF->table('goods_waring')
            ->where('m_date = :m_date',['m_date'=>$month])
            ->whereIn('waring_id',$waring_ids)
            ->whereIn('weidu_key',$asins)
            ->field('sku,waring_id,weidu_key,country_code,yunying_id,m_date')
            ->list();
        //预警数据查询
        $goods_waring_check = $dbF->table('goods_waring_check')
            ->where('m_date = :m_date',['m_date'=>$month])
            ->whereIn('waring_id',$waring_ids)
            ->whereIn('weidu_key',$asins)
            ->field('id as goods_waring_id,status,waring_id,m_date,weidu_key,country_code,waring_name,rules,reason_txt,created_time')
            ->list();
        $waring_check_ = [];
        foreach ($goods_waring_check as $v) {
            $key_ = $v['weidu_key'].'_'.$v['country_code'].'_'.$v['waring_id'];
            $waring_check_[$key_] = $v;
        }
        $waring_goods_ = [];
        $yunying_array = [];
        foreach ($waring_goods as $v) {
            //运营和sku集合
            $key_ = $v['weidu_key'].'_'.$v['country_code'].'_'.$v['waring_id'];
            $yunying_ids = json_decode($v['yunying_id']);
            if (isset($waring_goods_[$key_])) {
                $item = $waring_goods_[$key_];
                $item['yunying_ids'] = array_unique(array_merge($item['yunying_ids'],$yunying_ids));
                $item['sku'][] =  $v['sku'];
            } else {
                $item = [
                    'asin'=>$v['weidu_key'],
                    'yunying_ids' => $yunying_ids,
                    'sku' => [$v['sku']],
                    'base_info'=>$waring_check_[$key_]
                ];
            }
            //预警基础信息写入
            $waring_goods_[$key_] = $item;
            $yunying_array = array_unique(array_merge($yunying_array,$yunying_ids));
        }
        $sku_array = array_column($waring_goods,'sku');
        //父asin查询
        $dbF->table('table_month_count_'.$year)
            ->where("is_delete = 0 and reportDateMonth = :m_date",['m_date'=>$month]);
        //国家
        if (!empty($param['country_code']) && $param['country_code'] != '[]') {
            $country_codes = json_decode($param['country_code']);
            $dbF->whereIn('countryCode',$country_codes);
        }
        //项目
        if (!empty(self::$param['project_ids']) && self::$param['project_ids']!='[]') {
            $dbF->whereIn('project_id',json_decode(self::$param['project_ids']));
        }
        //权限运营+搜索
        if (!empty($param['yunying_ids']) && $param['yunying_ids']!='[]') {
            if (count(self::$yunying_ids)) {
                $yunying_ids = array_intersect(self::$yunying_ids,json_decode($param['yunying_ids']));
                $dbF->whereIn('yunying_id',$yunying_ids)
                    ->whereIn('project_id',self::$project_ids);
            } else {
                $dbF->whereIn('yunying_id',json_decode($param['yunying_ids']));
            }
        } else {
            if (count(self::$yunying_ids)) {
                $dbF->whereIn('yunying_id',self::$yunying_ids)
                    ->whereIn('project_id',self::$project_ids);
            }
        }
//        if (!empty($param['yunying_ids']) && $param['yunying_ids']!='[]') {
//            $yunying_ids = array_merge(self::$yunying_ids,json_decode($param['yunying_ids']));
//            $dbF->whereIn('yunying_id',$yunying_ids);
//        } else {
//            if (count(self::$yunying_ids)) {
//                $dbF->whereIn('yunying_id',self::$yunying_ids);
//            }
//        }
        //自定义搜索
        if (!empty($param['search_type']) && !empty($param['search_value'])) {
            if ($param['search_type'] == 'asin') {
                $dbF->andWhere('asin like :asin',['asin'=>"%{$param['search_value']}%"]);
            }elseif ($param['search_type'] == 'sku') {
                $dbF->andWhere('localSku like :sku',['sku'=>"%{$param['search_value']}%"]);
            } elseif ($param['search_type'] == 'p_asin') {
                $dbF->andWhere('parentAsin like :parentAsin',['parentAsin'=>"%{$param['search_value']}%"]);
            }
        }
        if (self::$sql_use_sku) {
            $dbF->whereIn('localSku',self::$sql_sku_list);
        }
        $dbF->whereIn('asin',$asins)
            ->field('parentAsin,asin')
            ->groupBy(['parentAsin','asin']);
        $msku_list = $dbF->list();
        $p_asin_list = [];
        foreach ($msku_list as $v) {
            $p_asin_list[$v['asin']][] = $v['parentAsin'];
        }
        //根据sku查询商品数据
        $goods_list = [];
        if (count($sku_array)) {
            $goods_list = $dbF->table('goods','a')
                ->leftJoin('goods_detail','b','b.sku = a.sku')
                ->whereIn('a.sku',$sku_array)
                ->field('a.sku,a.product_name,a.cid,a.category_name,a.is_new,b.pic_url,b.product_developer')
                ->list();
            //查询产品的等级
            $goods_level = $dbF->table('goods_level_relation','a')
                ->leftJoin('goods_level','b','b.id = a.level_id')
                ->where('where a.is_delete = 0')
                ->whereIn('a.m_date',self::$search_month)
                ->field('a.sku,b.level_name')
                ->list();
            //获取产品上两级分类
            $category_list = [];
            if ($goods_list) {
                $cids = array_column($goods_list,'cid');
                $category_list = goodsInformationForm::getGoodsCate($cids);
            }
            foreach ($goods_list as $g_k=>$goods_) {
                $level_name_array = [];
                foreach ($goods_level as $level_) {
                    if ($goods_['sku'] == $level_['sku']) {
                        $level_name_array[] = $level_['level_name'];
                    }
                }
                $goods_list[$g_k]['level_name'] = $level_name_array;
                $goods_list[$g_k]['category_name'] = isset($category_list[$goods_['cid']])?$category_list[$goods_['cid']]['name']:'';
            }
        }
        $goods_ = [];
        foreach ($goods_list as $v) {
            $goods_[$v['sku']] = $v;
        }
        //运营
        $user_ = [];
        if (count($yunying_array)) {
            $db = dbMysql::getInstance();
            $user_list = $db->table('qwuser')
                ->whereIn('id',$yunying_array)
                ->field('id,wname')
                ->list();
            foreach ($user_list as $v) {
                $user_[$v['id']] = $v;
            }
        }
        //数据整理
        foreach ($waring_goods_ as $k=>$v) {
            $v['p_asin'] = $p_asin_list[$v['asin']]??[];
            $v['goods_list'] =  array_intersect_key($goods_,array_flip($v['sku']));
            $v['yunying_list'] = array_values(array_intersect_key($user_,array_flip($v['yunying_ids'])));
            //产品的等级
            $level_names = array_column($v['goods_list'],'level_name');
            $level_names_ = [];
            foreach ($level_names as $v_) {
                $level_names_ = array_merge($level_names_,$v_);
            }
            $v['level_name'] = array_values(array_unique($level_names_));
            unset($v['yunying_ids']);
            $waring_goods_[$k] = $v;
        }
        return $waring_goods_;
    }
}