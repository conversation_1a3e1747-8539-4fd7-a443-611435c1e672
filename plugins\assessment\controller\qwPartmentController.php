<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/8 17:27
 */

namespace plugins\assessment\controller;

use core\lib\db\dbMysql;
use plugins\assessment\models\userModel;

class qwPartmentController
{
    public function getList()
    {
        $paras_list = array('name', 'department_leader');
        $param = arrangeParam($_GET, $paras_list);
        $db = dbMysql::getInstance();
        $db->table('qwdepartment')
            ->where('where 1=1');
        if (!empty($param['name'])) {
            $db->andWhere('and name like :wp_name', ['wp_name' => '%' . $param['name'] . '%']);
        }

        if (!empty($param['department_leader'])) {
            $db->andWhere('and department_leader like :lName', ['lName' => '%' . $param['department_leader'] . '%']);
        }
        $db->field('id,wp_id,name,department_leader,qw_parentid,sort');
        $db->order('qw_parentid asc,`sort` desc');
        $partments = $db->list();
        $list = [];
        if (count($partments)) {
            $list = $this->arrangeList($partments, $partments[0]['qw_parentid']);
        }
        returnSuccess(['list' => $list, 'partments' => $partments]);
    }

    private function arrangeList($list, $pid)
    {
        $res_data = [];
        if (count($list) > 0) {
            foreach ($list as $key => $val) {
                if ($val['qw_parentid'] == $pid) {
                    $val['department_leader'] = json_decode($val['department_leader']);
                    $res_data[$key] = $val;
                    unset($list[$key]);
                    $res_data[$key]['child'] = $this->arrangeList($list, $val['wp_id']);
                }
            }
        }
        return $res_data;
    }

}