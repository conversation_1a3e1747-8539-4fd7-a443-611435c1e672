<?php

/**
 * @author: zhangguoming
 * @Time: 2024/3/27 9:02
 */

namespace plugins\assessment\models;

use core\lib\db\dbAMysql;
use core\lib\db\dbShopMysql;
use core\lib\redisCached;
use plugins\salary\models\salaryCalculationModel;
use plugins\salary\models\userInfoModel;
use plugins\shop\form\messagesFrom;
use plugins\shop\models\shopApplyModel;
use plugins\shop\models\shopRegisterModel;
use plugins\shop\models\trademarkApplyModel;

class customCrontabModel
{
    public static int $id;
    public static string $is_crontab_task; // 是否定时任务 0否 1是
    public static string $link_type; // 关联类型 1考核方案自动生成 2绩效核算 3超时通知 4超时处理
    public static string $link_id; // 关联id
    public static string $runtime; // 运行时间
    public static string $cron_expression; // 执行命令
    public static string $status; // 状态 -1未开始 0进行中 1已完成

    const MODULE_ASSESSMENT = 1; // 绩效(默认)
    const MODULE_SALARY = 2; // 薪酬

    // 获取最新一条定时任务
    public static function getNextCrontab(){
        $adb = dbAMysql::getInstance();
        $adb->table('custom_crontab');
        $adb->where('where status = -1 and runtime < :runtime',['runtime'=>date('Y-m-d H:i:s')]);
        $adb->order('runtime desc');
        $crontab = $adb->one();
        if (empty($crontab)){
            return false;
        }

        // 将当前任务设置为进行中
        $adb->table('custom_crontab');
        $adb->where('where id = :id',['id'=>$crontab['id']]);
        $adb->update(['status'=>0]);

        // 薪酬模块
        if ($crontab['link_module'] == 2) {
            switch ($crontab['link_type']) {
                case 1: // 薪酬方案自动生成
                    salaryCalculationModel::autoCalc($crontab);
                    break;
                case 2: // 薪酬核算
                    salaryCalculationModel::calcResult($crontab);
                    break;
                case 3: // 薪酬核算失败
                    salaryCalculationModel::calcFail($crontab);
                    break;
                case 4: // 改变用户状态
                    userInfoModel::changeUserStatus($crontab);
                    break;
            }
        }
        // 店铺
        else if ($crontab['link_module'] == 3) {
            switch ($crontab['link_type']) {
                case 1: // 商标需求
                    $model = new trademarkApplyModel();
                    $detail = $model->getById($crontab['link_id']);
                    if (in_array($detail['status'], [$model::STATUS_ASSIGNED, $model::STATUS_CANCELLED])) {
                        customCrontabModel::finishCrontab($crontab['id']);
                    } else {
                        // 推送消息，保存下一次执行时间
                        $shop_db = dbShopMysql::getInstance();
                        $one = $shop_db->table('operation_log')
                            ->where('table_name = :table_name and table_id = :table_id',
                                ['table_name' => 'trademark_apply', 'table_id' => $crontab['link_id']])
                            ->order('id desc')
                            ->one();
                        $users = redisCached::getUserInfo();
                        $users = array_column($users, 'user_wid', 'user_id');
                        $content = "请您及时跟进商标需求ID为{$crontab['link_id']}的【{$detail['country']}_{$detail['category']}】商标任务";
                        messagesFrom::senMeg([$users[$one['operator']]], 1 , $content, $crontab['link_id'], '', '商标任务跟进');

                        $next_runtime = strtotime($crontab['runtime']) + ($detail['reminder_interval'] * 60);
                        $adb->table('custom_crontab')
                            ->where('where id = :id', ['id' => $crontab['id']])
                            ->update(['status' => -1, 'runtime' => date('Y-m-d H:i:s', $next_runtime)]);
                    }
                    break;
                case 2: // 店铺需求
                    $model = new shopApplyModel();
                    $detail = $model->getById($crontab['link_id']);
                    if (in_array($detail['status'], [$model::STATUS_SUCCESS])) {
                        customCrontabModel::finishCrontab($crontab['id']);
                    } else {
                        // 推送消息，保存下一次执行时间
                        // 关联此需求的注册任务
                        $shop_db = dbShopMysql::getInstance();
                        $list = $shop_db->table('shop_register')->where('where shop_apply_id = :apply_id', ['apply_id' => $crontab['link_id']])->list();

                        if (!empty($list)) {
                            $deps = redisCached::getDepartment();
                            $deps = array_column($deps, 'name', 'id');

                            $users = redisCached::getUserInfo();
                            $users = array_column($users, 'user_wid', 'user_id');

                            // 找到每个注册任务最后一个操作人
                            $last_operator = [];
                            $log_list = $shop_db->table('operation_log')
                                ->where('table_name = :table_name', ['table_name' => 'shop_register'])
                                ->whereIn('table_id', array_column($list, 'id'))
                                ->order('id desc')
                                ->groupBy(['table_id'])
                                ->list();

                            foreach ($log_list as $one) {
                                $last_operator[] = $users[$one['operator']];
                            }

                            $content = "请您及时跟进【{$deps[$detail['dep_id']]}_{$detail['country_site']}_{$detail['shop_type']}】的需求";
                            messagesFrom::senMeg($last_operator, 1 , $content, $crontab['link_id'], '', '店铺需求提醒');
                        }

                        $next_runtime = strtotime($crontab['runtime']) + ($detail['remind_interval'] * 60);
                        $adb->table('custom_crontab')
                            ->where('where id = :id', ['id' => $crontab['id']])
                            ->update(['status' => -1, 'runtime' => date('Y-m-d H:i:s', $next_runtime)]);
                    }
                    break;
            }
        }
        else {
            switch ($crontab['link_type']){
                case 1: // 考核方案自动生成
                    assessmentSchemesModel::autoAssessment($crontab);
                    break;
                case 2: // 绩效核算
                    assessmentUsersModel::calcPerformance($crontab);
                    break;
                case 3: // 超时通知
                    assessmentUsersModel::overtimeMsg($crontab);
                    break;
                case 4: // 超时处理
                    assessmentUsersModel::overtimeHandle($crontab);
                    break;
                case 5: // 生成考核：
                    assessmentUsersModel::createAssessmentDetail($crontab);
                    break;
            }
        }

        return $crontab['id'];
    }

    public static function finishCrontab($id)
    {
        $adb = dbAMysql::getInstance();
        $adb->table('custom_crontab')->where('where id = :id', ['id' => $id])->update(
            ['status' => 1]
        );
        return;
    }


}