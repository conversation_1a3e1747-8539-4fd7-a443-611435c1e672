<?php

namespace financial\controller;

use core\lib\config;
use core\lib\db\dbFMysql;


class countryController
{


// 获取国家列表或根据名字查询国家
    public static function getCountry() {
        try {
            // 定义所需参数列表
            $paras_list = array('country', 'page', 'page_size');
            $request_list = ['page' => '页码', 'page_size' => '记录数'];

            // 组织参数
            $param = arrangeParam($_POST, $paras_list, $request_list);

            // 设置默认分页参数
            $page = !empty($param['page']) ? $param['page'] : 1;
            $page_size = !empty($param['page_size']) ? $param['page_size'] : 10;

            // 获取数据库实例
            $db = dbFMysql::getInstance();

            // 初始化返回数据
            $response_data = [
                'list' => [],
                'page' => $page,
                'page_size' => $page_size,
                'total' => 0
            ];

            // 判断是否传入国家名
            if (!empty($param['country'])) {
                // 根据国家名查询国家
                $country = $param['country'];
                $country_list = $db->table('market')
                    ->where('where country LIKE :country and is_delete = 0', ['country' => '%' . $country . '%'])
                    ->pages($page, $page_size); // 添加分页


                // 如果查询结果为空
                if (empty($country_list)) {
                    returnError("未找到名为 $country 的国家");
                }

                // 返回成功信息和国家数据
                returnSuccess($country_list, "获取国家成功");
            } else {
                // 获取所有国家数据
                $markets = $db->table('market')
                    ->where('where is_delete = 0')
                    ->pages($page, $page_size); // 添加分页

                // 返回成功信息和所有国家数据
                returnSuccess($markets, "获取国家列表成功");
            }
        } catch (\Exception $e) {
            returnError("获取国家失败: " . $e->getMessage());
        }
    }




    // 配置单个国家所属洲
    public static function cfgcountry() {
        try {
            // 定义所需参数列表
            $paras_list = array('id', 'ct_id');
            $request_list = ['id' => '国家ID', 'ct_id' => '所属洲ID'];

            // 组织参数
            $param = arrangeParam($_POST, $paras_list, $request_list);

            // 获取数据库实例
            $db = dbFMysql::getInstance();

            // 更新国家的所属洲
            $db->table('market')
                ->where('where id = :id and is_delete = 0', ['id' => $param['id']])
                ->update(['ct_id' => $param['ct_id'], 'syn_time' => date('Y-m-d H:i:s')]);

            returnSuccess([], "配置国家所属洲成功");
        } catch (\Exception $e) {
            returnError("配置国家所属洲失败: " . $e->getMessage());
        }
    }

    // 批量配置国家所属洲
    public static function batchCfgCountry() {
        try {
            // 获取 request_data 参数内容
            $request_data = isset($_POST['request_data']) ? $_POST['request_data'] : '';
    
            // 将 request_data 字段的 JSON 字符串转换为 PHP 数组
            $param = json_decode($request_data, true);
    
            // 检查解码是否成功
            if (json_last_error() !== JSON_ERROR_NONE) {
                returnError("request_data 字段解码错误: " . json_last_error_msg());
                return;
            }
    
            // 获取数据库实例
            $db = dbFMysql::getInstance();
    
            // 初始化计数器
            $sum = 0;
            $updated_countries = [];
    
            // 批量更新国家的所属洲
            foreach ($param as $entry) {
                if (!isset($entry['countries']) || !isset($entry['ct_id'])) {
                    continue;
                }
    
                $countries = $entry['countries'];
                $ct_id = $entry['ct_id'];
    
                foreach ($countries as $id) {
                    // 更新数据库
                    $affectedRows = $db->table('market')
                        ->where('where id = :id and is_delete = 0', ['id' => $id])
                        ->update(['ct_id' => $ct_id, 'syn_time' => date('Y-m-d H:i:s')]);
    
                    // 记录更新的国家ID
                    if ($affectedRows > 0) {
                        $updated_countries[] = $id;
                        $sum++;
                    }
                }
            }
    
            // 返回包含更新次数的成功信息
            returnSuccess(['updated_countries' => $updated_countries], "批量配置国家所属洲成功，共更新{$sum}次");
        } catch (\Exception $e) {
            returnError("批量配置国家所属洲失败: " . $e->getMessage());
        }
    }
    
    
    // 获取洲名称
    private static function getContinentNameById($ct_id) {
        $continents = config::get('continents','data_financial');

        foreach ($continents as $continent) {
            if ($continent['id'] == $ct_id) {
                return $continent['name'];
            }
        }

        return '未知洲';
    }

    //看板国家列表拉取
    public static function countryList() {
        // 获取数据库实例
        $db = dbFMysql::getInstance();

        // 获取国家列表
        $countryList = $db->table('market')
            ->where('where is_delete = 0')
            ->field('country, ct_id,id,code')
            ->list();

        $continents = config::get('continents','data_financial');
        // 初始化分组列表
        $groupedList = [];

        // 初始化其他项列表
        $otherCountries = [];

        foreach ($countryList as $country) {
            // 如果ct_id已经存在于分组列表中，直接添加到该分组
            if (isset($groupedList[$country['ct_id']])) {
                $groupedList[$country['ct_id']][] = [
                    'country' => $country['country'],
                    'id' => $country['id'],
                    'code' => $country['code']
                ];
            } else {
                // 如果ct_id还没有在分组列表中，初始化并添加到该分组
                $groupedList[$country['ct_id']] = [
                    [
                        'country' => $country['country'],
                        'id' => $country['id'],
                        'code' => $country['code']
                    ]
                ];
            }
        }

        // 遍历国家列表，将不属于特定分组的国家添加到其他项列表中
        foreach ($countryList as $country) {
            if (!isset($groupedList[$country['ct_id']])) {
                $otherCountries[] = [
                    'country' => $country['country'],
                    'id' => $country['id'],
                    'code' => $country['code']
                ];
            }
        }


        // 创建一个辅助数组，方便通过id查找name
        $idToNameMap = array_column($continents, 'name', 'id');

        foreach ($groupedList as $id => &$countries) {
            if (isset($idToNameMap[$id])) {
                $groupedList[$idToNameMap[$id]] = $countries;  // 使用name替换id作为键
                unset($groupedList[$id]);
            } else {
                $groupedList[$id] = $countries;  // 如果找不到对应的name，保持原样
            }
        }

        // 合并分组列表和其他项列表
        $list = $groupedList+$otherCountries;

        // 初始化结果数组
        $result = [];

        // 遍历数组
        foreach ($list as $key => &$values) {
            if (is_array($values) && isset($values[0]['country'])) {
                // 处理有分组的部分
                $countryArray = [];
                foreach ($values as $country) {
                    $countryArray[] = $country;
                }
                $result[] = [
                    'zhou' => $key,
                    'country' => $countryArray,

                ];
            }
        }

        // 定义一个新数组用于存储最终结果
        $reorderedResult = [];
        $groupZero = null;

        // 遍历原始数组
        foreach ($result as $key => $group) {
            if ($group['zhou'] === 0) {
                // 保存'zhou'为0的组
                $groupZero = $group;
            } else {
                // 添加其他组到新数组
                $reorderedResult[] = $group;
            }
        }

        // 将'zhou'为0的组追加到新数组的末尾
        if ($groupZero !== null) {
            $reorderedResult[] = $groupZero;
        }


        returnSuccess($reorderedResult);
    }



}
