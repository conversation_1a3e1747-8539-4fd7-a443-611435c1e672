<?php

namespace financial\form;

use financial\models\boardTableModels;
use financial\models\exportModel;
use financial\models\userModel;
use Rap2hpoutre\FastExcel\FastExcel;

class exportForm
{
    // 导出asin字段
    public static function asin_export($param)
    {
        // 获取需要导出的数据
        $export_list = json_decode($param['export_list']);
        boardTableForm::$export_keys = $export_list;
        switch ($param['export_type']) {
            case 1:
                // asin数据获取
                boardTableForm::$auth_key_ = 'asin_table';
                $form = new boardTableForm('asin');
                $data = $form::getAsinList(2);
                break;
            case 2:
                // 父asin数据获取
                boardTableForm::$auth_key_ = 'pasin_table';
                $form = new boardTableForm('p_asin');
                $data = $form::getPasinList(2);
                break;
            case 3:
                // SKU
                boardTableForm::$auth_key_ = 'sku_table';
                $form = new boardTableForm('sku');
                $data = $form::getSkuList(2);
                break;
            case 4:
                // 运营榜单
                boardTableForm::$auth_key_ = 'yunying_table';
                $form = new boardTableForm('yunying_id');
                $data = $form::getYunyingList(2);
                break;
            case 5:
                // 店铺
                boardTableForm::$auth_key_ = 'store';
                $form = new boardTableForm('sid');
                $data = $form::getSidList(2);
                break;
            case 6:
                // 爆品
                boardTableForm::$auth_key_ = 'hot';
                leveTableForm::$real_table = 'hot_goods';
                $form = new leveTableForm('p_asin');
                $data = $form::hotGoodsTotal(2);
                break;
            case 7:
                // 清仓
                boardTableForm::$auth_key_ = 'qingcang';
                leveTableForm::$real_table = 'clear_sale';
                $form = new leveTableForm('asin');
                $data = $form::clearSaleTotal(2);
                break;
            case 8:
                // 预警-----------------
                boardTableForm::$auth_key_ = 'waring';
                goodsWaringForm::exportTableData($param);
                break;
            case 9:
                // 供应商利润贡献---------------
                boardTableForm::$auth_key_ = 'supplier_gross_table';
                boardTableForm::$real_table = 'supplier_sku';
                $form = new boardTableForm('sku');
                $data = $form::supplierGoodsTotal(2);
                break;
            case 10:
                // 产品利润贡献---------------
                //获取该用能查的字段
                boardTableForm::$auth_key_ = 'goods_gross_table';
                boardTableForm::$real_table = 'goods_sku';
                $form = new boardTableForm('sku');
                $data = $form::goodsGrossTotal(2);
                break;
            default:
                returnError('Invalid type');
                break;
        }
        if (empty($data)){
            returnError('无导出数据');
        }
        //获取导出数据列表
        $list = exportModel::get_export_list();
        if ($param['export_type'] == 9 or $param['export_type'] == 10){
            $list = exportModel::get_year_month_list($data,$list);
            $list['year_amount'] = '总合计';
            $list['month_amount'] = '月度总计';
            $list['category_name'] = '产品类别';
        } elseif($param['export_type'] == 8) {
            $list['country'] = '国家';
            $list['category_name'] = '产品类别';
        }
        $filePath = '/public_financial/temp/export'; // 导出文件路径
        $filePaths = self::exportLargeDataWithAppend($data['data']['list'], $filePath,$list,$param,$param['export_type'], 5000);
        $zip_path = self::zipExcelFiles($filePath);
        return $filePath.'/'.$zip_path;

    }
    public static function exportLargeDataWithAppend(array $data, string $filePath, array $headers,$param,$type,int $chunkSize = 1000)
    {
        //获取需要转化为百分比的列表
        $p_list = exportModel::get_percent_list();
        // 定义每批数据的大小
        $batchSize = $chunkSize;
        // 计算批次数
        $numBatches = ceil(count($data) / $batchSize);
        $filePaths = [];
        // 循环处理每一批数据
        for ($i = 0; $i < $numBatches; $i++) {
            // 计算这批数据在原始数组中的起始位置和结束位置
            $startIndex = $i * $batchSize;
            $endIndex = min(($i + 1) * $batchSize, count($data));
            // 存储这一批次的所有数据
            $batchData = [];
            for ($j = $startIndex; $j < $endIndex; $j++) {
                $batchData[] = $data[$j];
            }
            if ($type == 9 or $type == 10){
                $newArray = [];
                foreach ($batchData as $item) {
                    $newItem = [
                        "supplier_id" => isset($item["supplier_id"]) ? $item["supplier_id"] : null,
                        "sku" => isset($item["sku"]) ? $item["sku"] : null,
                        "supplier_name" => isset($item["supplier_name"]) ? $item["supplier_name"] : null,
                        "product_name" => isset($item["product_name"]) ? $item["product_name"] : null,
                        "category_name" => isset($item["category_name"]) ? $item["category_name"] : null,
                        "year_amount" => isset($item["year_data"]["year_amount"]) ? $item["year_data"]["year_amount"] : null,
                        "month_amount" => isset($item["month_data"]["month_amount"]) ? $item["month_data"]["month_amount"] : null,
                    ];

                    if (isset($item["year_data"])) {
                        foreach ($item["year_data"] as $year => $value) {
                            if ($year != "year_amount") {
                                $newItem[$year] = $value;
                            }
                        }
                    }

                    if (isset($item["month_data"])) {
                        foreach ($item["month_data"] as $month => $value) {
                            if ($month != "month_amount") {
                                $newItem[$month] = $value;
                            }
                        }
                    }

                    $newArray[] = $newItem;
                }
                $path = self::export_excel($newArray,$headers,$filePath,$param,$type,$p_list);
            }else{
                $path = self::export_excel($batchData,$headers,$filePath,$param,$type,$p_list);
            }
            $filePaths[] = $path;
        }

        return $filePaths;
    }
    //导出到excel
    public static function export_excel($data,$headers,$save_path,$param,$type,$p_list)
    {
        // 验证和处理导出字段列表
        if (!isset($param['export_list'])) {
            returnError('Export field list not provided');
        }
        // 解析 export_list 参数，确保其为数组
        $export_list = json_decode($param['export_list'], true); // 以数组形式解析
        if (!is_array($export_list) || !count($export_list)) {
            returnError('Please select the fields to export');
        }
        if ($type == 8) {
            $export_list[] = 'waring_rules';
            $export_list[] = 'waring_reason_txt';
            $export_list[] = 'country';
            $export_list[] = 'category_name';
        }
        // 初始化表头和数据
        $new_data = [];
        foreach ($data as $v) {
            $item = [];
            if ($type == 8){
                $v = $v[0];
            }
            foreach ($v as $k => $v1) {
                // 如果字段不在导出字段列表中，跳过
                if (!in_array($k, $export_list)) {
                    continue;
                }
                //如果字段不在 $export_list 中定义，跳过
                if (!isset($headers[$k])) {
                    continue;
                }
                if ($k == 'is_new'){
                    if (empty($v1)){
                        $v1 = '-';
                    }else {
                        $v1 = $v1 ? '是' : '否';
                    }
                }
                if ($k == 'waring_status'){
                    switch ($v1){
                        case 1:
                            $v1 = '待处理';
                            break;
                        case 2:
                            $v1 = '待审核';
                            break;
                        case 3:
                            $v1 = '审核通过';
                            break;
                        case 4:
                            $v1 = '审核失败';
                            break;
                        default:
                            $v1 = '未知';
                    }
                }
                // 如果字段在百分比列表中，转化为百分比进行处理
                if (in_array($k, $p_list)) {
                    $v1 = $v1.'%';
                }
                if (is_array($v1)) {
                    $value = implode(', ', $v1);
                } else {
                    $value = $v1;
                }
                // 设置转换后的数据
                $item[$headers[$k]] = $value;
            }
            // 获取当前导出时间并设置到新数据行
            $exportTime = date('Y-m-d H:i:s');
            $item['导出时间'] = $exportTime;
            $new_data[] = $item;
        }
        // 保存路径
        $url = SELF_FK . $save_path;


        // 创建目录（如果不存在）
        if (!file_exists($url)) {
            if (!mkdir($url, 0777, true)) {
                returnError('Unable to create directory：' . $url);
            }
        }
        // 生成唯一的文件名
        $path = $save_path . DIRECTORY_SEPARATOR . date('YmdHis') . uniqid() . '.xlsx';
        $url = SELF_FK . $path;

        // 创建空文件（如果不存在）
        if (!file_exists($url)) {
            if (!touch($url)) {
                returnError('unable to create file：' . $url);
            }
        }
        // 使用 FastExcel 导出 Excel 文件
        try {
            (new FastExcel($new_data))->export($url);
        } catch (\Exception $e) {
            returnError('Export file failed: ' . $e->getMessage());
        }

        // 返回文件路径
        return $path;
    }

    public static function zipExcelFiles($excelFilePath)
    {
        // 生成ZIP文件名，这里以当前时间和随机数组合来确保唯一性
        $save_path = '/public_financial/temp/export';
        if (!file_exists(SELF_FK. $save_path)) {
            mkdir(SELF_FK. $save_path, 0777, true);
        }
        $return_path = date('YmdHis'). '.zip';
        $zip_url = SELF_FK. $save_path. "/". $return_path;

        $zip = new \ZipArchive();
        if ($zip->open($zip_url, \ZipArchive::CREATE) === true) {
            // 获取指定路径下的所有Excel文件
            $excelFiles = glob(SELF_FK. $excelFilePath. DIRECTORY_SEPARATOR. '*.xlsx');
            if (empty($excelFiles)) {
                // 如果没有找到Excel文件，关闭ZIP对象，删除已创建的空ZIP文件（如果有），并返回表示无数据的标识
                $zip->close();
                if (file_exists($zip_url)) {
                    unlink($zip_url);
                }
                returnError('无数据');
            }
            foreach ($excelFiles as $excelFile) {
                // 将每个Excel文件添加到ZIP压缩包中
                $zip->addFile($excelFile, basename($excelFile));
            }
            $zip->close();

            // 这里可以选择删除指定路径下的单个Excel文件，如果你希望只保留ZIP包的话
            foreach ($excelFiles as $excelFile) {
                unlink($excelFile);
            }
            return $return_path;
        } else {
            // 处理打开或创建ZIP文件失败的情况
            returnError("无法创建或打开ZIP文件：{$zip_url}");
            return false;
        }
    }

}