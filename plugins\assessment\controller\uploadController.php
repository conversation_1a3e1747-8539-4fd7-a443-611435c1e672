<?php
/**
 * @author: zhangguoming
 * @Time: 2024/6/11 17:32
 */

namespace plugins\assessment\controller;

use core\lib\db\dbMysql;
use financial\common\upLoadBase;

class uploadController extends upLoadBase
{
    //导入数据前上传文件
    public function uploadExcel()
    {
        $file = $_FILES;
        //获取文件临时路径
        $temp_name = $file['file']['tmp_name'];
        //获取文件名
        $filename = $file['file']['name'];
        if (mb_strlen($filename) > 100) {
            SetReturn(-1, '文件名称过长');
        }
        //获取文件的后缀名
        $arr = pathinfo($filename);
        $ext_suffix = $arr['extension'];
        //限制上传类型
        if (!in_array($ext_suffix, self::$excel_suffix)) {
            SetReturn(-1, 'excel格式不正确');
        }
        //获取大小
        $size = $file['file']['size'];
        self::varifyFileSize($size);
        //创建没有的文件夹（创建临时文件夹）
        $url = '/public/assessment/upload/temp/' . $ext_suffix . '/' . date('Ymd');
        $save_path = SELF_FK . $url;
        if (!file_exists($save_path)) {
            mkdir($save_path, 0777, true);
        }
        //保存图片
        $file_name = $url . '/' . date('YmdHis', time()) . rand(100, 1000) . '.' . $ext_suffix;
        $new_filename = SELF_FK . $file_name;
        if (move_uploaded_file($temp_name, $new_filename)) {
            $res = [
                'extension' => $ext_suffix,
                'filename'  => $filename,
                "src"       => $file_name,
            ];
            returnSuccess($res, '');
        } else {
            SetReturn(-1, '上传失败');
        }
    }
}