<?php
/**
 * @author: zhangguoming
 * @Time: 2024/8/5 10:41
 */

namespace financial\models;

use core\lib\config;
use core\lib\db\dbFMysql;

class goodsWaringModel
{
    //获取规则的具体说明
    public static function getRuleTxt($rules,$country,$cate_cids) {
        $db = dbFMysql::getInstance();
        $goods_category = $db->table('goods_category')
            ->whereIn('cid',$cate_cids)
            ->list();
        $category_ = [];
        foreach ($goods_category as $v) {
            $category_[$v['cid']] = $v['title'];
        }
        $txt = [];
        //规则
        foreach ($rules as $v) {
            $cate_text = [];//分类
            $country_text = [];//国家
            if (count($v['market_id'])) {
                foreach ($v['market_id'] as $market_id) {
                    $country_text[] = $country[$market_id]['country'];
                }
            } else {
                $country_text[] = '全部国家';
            }
            if (count($v['category_id'])) {
                foreach ($v['category_id'] as $category_id) {
                    $cate_text[] = $category_[$category_id];
                }
            }
            $rule_txt = [];
            foreach ($v['condition'] as $k1=>$v1) {
                $item_txt = '';
                if ($k1 > 1) {
                    if ($v1['type'] == 1) {
                        $item_txt .= '且';
                    } else {
                        $item_txt .= '或';
                    }
                }
                $item_txt .= config::getDataName1('data_financial','waring_rules_month',$v1['month']);
                $item_txt .= $v1['is_absolute_value']?'的绝对值':'的值';
                if ($v1['reference'] == 1) {
                    $item_txt .= '与自定义值'.$v1['Interval_value'].'相比';
                } else {
                    $item_txt .= config::getDataName1('data_financial','waring_rules_reference',$v1['reference']);
                }
                if ($v1['symbol'] != 6) {
                    $item_txt .= config::getDataName1('data_financial','waring_rules_symbol',$v1['symbol']).$v1['value1'];
                } else {
                    $item_txt .= ('在'.config::getDataName1('data_financial','waring_rules_symbol',$v1['symbol']).'['.$v1['value1'].','.$v1['value2'].']内');
                }
                $rule_txt[] = $item_txt;

            }
            $res_txt = '国家：'.implode('、',$country_text).'；';
            $res_txt .= '类别：'.implode('、',$cate_text).'；';
            $res_txt .= '规则：'.implode('，',$rule_txt).'。';
            $txt[] = $res_txt;
            if (count($v['market_id']) == 0) {
                break;
            }
        }
        return $txt;
    }
}