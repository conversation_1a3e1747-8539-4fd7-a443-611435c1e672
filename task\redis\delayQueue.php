<?php
/**
 * @author: zhangguoming
 * @Time: 2024/2/23 17:01
 */

use core\lib\config;

$current_path = dirname(__FILE__);
include_once $current_path.'/redisConnect.php';
include_once $current_path.'/../../environment.php';
$qw_config = require_once $current_path.'/../../core/config/qw.php';
$key = "oa_delay_queue";
$taskStatusPrefix = "delay_queue_";
global $redis;

$result = $redis->zRangeByScore($key, 0, time(),['WITHSCORES' => true, 'limit' => array(0, 5)]);
//每次只执行一个任务
if ($result > 0) {
    require_once $current_path . '/../../core/common/function.php';
    require_once $current_path . '/../../core/fk.php';
    require_once $current_path . '/../../vendor/autoload.php';
    define('LOG_PATH', $current_path . '/../../log');
    define('SELF_FK', $current_path.'/../..'); //当前框架所在的根目录
    define('CORE', SELF_FK.'/core'); //核心文件目录
    spl_autoload_register('\core\fk::load');
    $queue_key = config::get('delay_queue_key', 'app');
    //dd($result);
    foreach ($result as $k=>$v) {
        list($scroe, $class) = [$v,$k];
        $task = unserialize($class);
        $unqueid = $task->unqueid;
        // 尝试获取任务的锁
        $taskStatusKey = $taskStatusPrefix . $unqueid;
        $acquired = $redis->setnx($taskStatusKey, 1);
        $acquired = 1;
//        $redis->zRem($key, $class);
//        $redis->del($taskStatusKey);
//        continue;
        //存在就说明再处理中
        if ($acquired) {
            try {
                //消费
                $task->task();
//                dd(852);
                // 任务处理成功，更新任务状态为已完成
                $redis->zRem($key, $class);
                $redis->del($taskStatusKey);
                \core\lib\log::delayQueue()->info('【执行成功】'.json_encode($task));
                die;
            } catch (\Error $e) {
//                dd($e);
                \core\lib\log::delayQueue()->error('【执行失败条5分钟后继续执行】'.$e);
                $time = microtime(true)+5*60;
                $redis->zAdd($queue_key, [], $time, serialize($task));
                $redis->zRem($key, $class);
                $redis->del($taskStatusKey);
                die;
            } catch (\Exception $e) {
//                dd($e);
                // 任务处理失败，重置任务状态为可用
                \core\lib\log::delayQueue()->error('【执行失败条5分钟后继续执行】'.$e);
                $time = microtime(true)+5*60;
                $redis->zAdd($queue_key, [], $time, serialize($task));
                $redis->zRem($key, $class);
                $redis->del($taskStatusKey);
                die;
            }
        } else {
            continue;
        }
    }
}
die;


