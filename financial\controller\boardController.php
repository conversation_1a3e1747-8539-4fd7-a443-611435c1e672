<?php
/**
 * @author: zhangguoming
 * @Time: 2024/7/3 10:04
 */

namespace financial\controller;

use financial\form\boardForm;
use financial\form\boardRateForm;
use financial\form\boardStockForm;
use financial\models\userModel;

class boardController //看板图表数据
{
    //销量
    public function salesQuantity() {
        $form = new boardForm(1);
        $month_data = $form->totalCustom();
        $rate_data = $form->getQoqAndYoy($month_data);
        returnSuccess(['month_data'=>$month_data,'rate_data'=>$rate_data]);
    }
    //销售额
    public function totalSalesAmount() {
        $form = new boardForm(3);
        $month_data = $form->totalCustom();
        $rate_data = $form->getQoqAndYoy($month_data);
        returnSuccess(['month_data'=>$month_data,'rate_data'=>$rate_data]);
    }
    //客单价
    public function unitPrice() {
        $form = new boardRateForm(5);
        $month_data = $form->totalCustom();
        $rate_data = $form->getQoqAndYoy();
        returnSuccess(['month_data'=>$month_data,'rate_data'=>$rate_data]);
    }
    //毛利润
    public function grossProfit() {
//        $form = new boardRateForm(4);
//        $month_data = $form->totalCustom();
//        $rate_data = $form::getQoqAndYoy();
//        returnSuccess(['month_data'=>$month_data,'rate_data'=>$rate_data]);
        $form = new boardForm(4);
        $month_data = $form->totalCustom();
        $rate_data = $form->getQoqAndYoy($month_data);
        returnSuccess(['month_data'=>$month_data,'rate_data'=>$rate_data]);
    }
    //毛利率
    public function grossProfitRate() {
        $form = new boardRateForm(6);
        $month_data = $form->totalCustom();
        $rate_data = $form::getQoqAndYoy();
        returnSuccess(['month_data'=>$month_data,'rate_data'=>$rate_data]);
    }
    //roi
    public function roi() {
        $form = new boardRateForm(7);
        $month_data = $form->totalCustom();
        $rate_data = $form::getQoqAndYoy();
        returnSuccess(['month_data'=>$month_data,'rate_data'=>$rate_data]);
    }
    //国家贡献(固定显示：美国、加拿大、德国、法国、英国、日本；其他的国家有超过5%的也显示出来，没超过5%则归为其他中合并计算)
    public function countryContribution() {
        $form = new boardRateForm(4);
        $data = $form->countryContribution();
        returnSuccess($data);
    }
    //top10
    public function top10() {
        $form = new boardForm(1);
        //销量和销售额获取
        $data1 = $form->topTen();
        //毛利润
        $form2 = new boardRateForm(4);
        $gross_proft = $form2->topTen();
        //毛利率查询
        $form3 = new boardRateForm(6);
        $param = $form3::$param;
        $gross_proft_rate = $form3->topTen();
        foreach ($data1 as $k=>$v) {
            if (isset($gross_proft[$k])) {
                $data1[$k]['gross_proft'] = $gross_proft[$k]['total'];
            } else {
                $data1[$k]['gross_proft'] = '-';
            }
            if (isset($gross_proft_rate[$k])) {
                $data1[$k]['gross_proft_rate'] = $gross_proft_rate[$k]['total'];
            } else {
                $data1[$k]['gross_proft_rate'] = '-';
            }
        }
        //排序
        if (!in_array($param['order_by_key'],['gross_proft','gross_proft_rate','sales_quantity','sales_amoun'])) {
            $order_by_key = 'gross_proft';
        } else {
            $order_by_key = $param['order_by_key'];
        }
        if ($param['order_by_type'] == 1) {
            //正序
            usort($data1, fn($a, $b) => $a[$order_by_key] <=> $b[$order_by_key]);
        } else {
            usort($data1, fn($a, $b) => $b[$order_by_key] <=> $a[$order_by_key]);
        }
        returnSuccess(['list'=>$data1]);
    }
    //成本(费用)采购8、头程9、尾程10、推广费44、退款12、佣金13、其72
    public function costPrice() {
        $form = new boardForm(8);
        $data = $form->costPrice();
        returnSuccess($data);
    }
    //库存情况(库存期末情况) + 库存成本金额
    public function stockEndStatus() {
        $form = new boardStockForm();
        $data = $form->stockEndStatus();
        returnSuccess($data);
    }
    //被封资金
    public function blockedFunds() {
        $form = new boardStockForm();
        $data = $form->key6();
        returnSuccess($data);
    }













}