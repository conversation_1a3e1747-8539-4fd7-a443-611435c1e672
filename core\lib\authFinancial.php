<?php
/**
 * @author: zhangguoming
 * @Time: 2023/12/13 13:37
 */

namespace core\lib;

class authFinancial
{
    //公用方法（不用验证权限的接口）
    static array $common_auth = [
        'financial/index/index',
        'financial/login/login', //登入
        'financial/login/codeLogin',
        'financial/login/logOut', //登出
        'financial/role/getDefaultAuth', //获取权限列表
        'financial/common/patternList',//枚举获取
        'financial/upload/uploadExcel',//导入信息文件上传
        'financial/user/getUserInfo',//获取用户信息
        'financial/user/getList',//用户列表
        'financial/user/updateUserPwd',//用户修改密码
        'financial/user/getOftenUsedUser',//常用用户查询
        'financial/user/setOftenUsedUser',//保存使用的用户
        'financial/qwPartment/getList',//部门列表
        'financial/goodsCategory/getList',//产品分类获取
        'financial/market/getList',//部门列表
        'financial/column/getMonitoringList',//指标字段获取
        'financial/mskuReport/importLogList',//查看导入异常列表
        'financial/mskuReport/getProgressData',//获取导出，导入进度
        'financial/goodsStock/importErrorList',//获取导入异常列表(库存)
        'financial/customColumn/getProgress',//自定义字段获取计算进度
        'financial/mskuReport/errorListLog',//s商品数据全量-查询导出的异常列表
        'financial/goodsData/importErrorList',//商品数据增量-查询导出的异常列表
        'financial/goodsStock/importErrorList',//库存数据-查询导出的异常列表
        'financial/message/getList',//消息
        'financial/message/getMessageCount',
        'financial/message/getDetail',
        'financial/message/setAllRead',
        //表格方案
        'financial/boardTableCustomSetting/getList',
        'financial/boardTableCustomSetting/edit',
        'financial/boardTableCustomSetting/detail',
        'financial/boardTableCustomSetting/getUserTableAuth',
        'financial/boardTableCustomSetting/del',
        //币种获取
        'financial/routing/getLastMonthCurrency',
        //获取监控字段（等级和预警）
        'financial/column/getMonitoringList',
        //自己细信息获取
        'financial/customColumn/getInfo',
        //字段列获取
        'financial/column/getList',
        //原始数据列获取
        'financial/originaldata/rule',
        //下载进度获取
        'financial/common/getProgressData',
        //导入进度
        'financial/common/getGoodsDataImportProgress',//增量
        'financial/common/getMskuDataImportProgress',//全量
        'financial/common/getGoodsStockImportProgress',//库存
        //费用单模板下载
        'financial/costSharing/downLoadTeml',
        //国家
        'financial/country/countryList',
        //等级详情获取
        'financial/level/getLevelDetail',
        //用户时间权限设置
        'financial/user/getFnancialUser',
        'financial/mskuReport/getColumnCount',
        'financial/goodsData/getColumnCount',
        //获取默认方案
        'financial/boardTableCustomSetting/getDefault',
        //角色
        'financial/role/roleDetail',//角色详情
        'financial/role/getRoleAll',
        'financial/boardTable/goodsGrossProfitCount',
        'financial/goodsStock/getColumnCount',
        'financial/user/getNoLoginCode',
        //项目
        'financial/project/getList',
        //预警获取
        'financial/waringRules/getList',
    ];

    //验证用户请求权限
    public static function checkAuth($url, $token_perfix){
        if (in_array($url, self::$common_auth)) {
            return;
        }
        $headerData = getallheaders();
        $key = $token_perfix.$headerData['Authorization'];
        $redis = (new \core\lib\predisV())::$client;
        $data = $redis->hmget($key,['is_super','auth'])??'';
        if ($data['is_super'] == 1) {
            return true;
        }
        $user_auth = !$data['auth']?[]:json_decode($data['auth'],true);
        if (!in_array($url,$user_auth)) {
            SetReturn(-1,'暂无权限');
        } else {
            return true;
        }
    }

}