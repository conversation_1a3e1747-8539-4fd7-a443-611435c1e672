<?php
/**
 * @author: zhangguoming
 * @Time: 2024/8/30 13:45
 */

namespace  plugins\goods\controller;

use core\lib\db\dbMysql;

class testController
{
    //蓝牙编码+协议编码生成
        public function setCode() {
        $next_code = 'F0';
        $sql = 'insert into oa_goods_code (`code`,`protocol_code`) values ';
        while (1) {
            $p_code = '11';
            while (1) {
                $sql.=("('$next_code','$p_code'),");
                $p_code = dechex(hexdec($p_code) + 1);
                if (hexdec($p_code) > 255) {
                    break;
                }
            }
            $next_code = dechex(hexdec($next_code) + 1);
            if (hexdec($next_code) > 255) {
                break;
            }
        }
        $db = dbMysql::getInstance();
        dd($sql);
        $db->query(trim($sql,','));
    }
}